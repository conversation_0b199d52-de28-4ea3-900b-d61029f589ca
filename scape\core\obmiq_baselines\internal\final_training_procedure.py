from __future__ import annotations

from typing import Any, Dict

import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import RFECV
from sklearn.impute import SimpleImputer
from sklearn.model_selection import GroupKFold, KFold, RandomizedSearchCV
from sklearn.pipeline import Pipeline

from logwp.infra import get_logger

from ..config import ObmiqBaselinesTrainingConfig
from .ensemble_model import WeightedEnsembleModel
from .model_definitions import get_candidate_models_and_param_spaces

logger = get_logger(__name__)


def train_final_ensemble_model(
    X: pd.DataFrame,
    y: pd.Series,
    groups: pd.Series,
    nested_cv_results: Dict[str, Any],
    config: ObmiqBaselinesTrainingConfig,
    target_name: str,
) -> Dict[str, Any]:
    """
    在全量数据上训练最终的加权融合模型。

    此函数遵循开发计划，为嵌套CV选出的每个最佳模型组件，在全部数据上
    重新、独立地执行特征选择和超参数寻优，以找到全局最优配置。
    最后，将训练好的组件封装成一个可部署的融合模型。

    Args:
        X: 完整的特征DataFrame。
        y: 完整的目标Series。
        groups: 用于GroupKFold分组的Series (井号)。
        nested_cv_results: 来自嵌套CV的评估结果，包含最佳模型和权重。
        config: 训练配置对象。
        target_name: 当前预测的目标名称，用于日志记录。

    Returns:
        一个包含最终融合模型和元数据的“模型资产包”字典。
    """
    logger.info(f"开始为目标 '{target_name}' 训练最终的融合模型...")

    candidate_models = get_candidate_models_and_param_spaces(config)
    best_models_config = nested_cv_results["best_models"]
    trained_sub_models = []

    # 使用GroupKFold确保在所有优化步骤中都尊重井的边界，防止信息泄露
    # The number of splits for GroupKFold cannot be greater than the number of groups.
    n_groups = groups.nunique()
    group_cv = GroupKFold(n_splits=n_groups)
    inner_kfold_cv = KFold(n_splits=config.inner_cv_folds, shuffle=True, random_state=config.random_seed)

    for model_info in best_models_config:
        model_name = model_info["name"]
        model_details = candidate_models[model_name]
        logger.info(f"  正在为最终模型组件 '{model_name}' 进行优化...")

        # --- 防数据泄露核心: 构建完整的优化流水线 ---
        rfe_base_estimator = RandomForestRegressor(n_estimators=50, random_state=config.random_seed, n_jobs=1)
        optimization_pipeline = Pipeline([
            ("imputer", SimpleImputer(strategy="median")),
            # Critical Fix: Set n_jobs=1 for the inner RFECV to prevent nested parallelism.
            ("selector", RFECV(
                estimator=rfe_base_estimator,
                step=1, cv=inner_kfold_cv,
                scoring="neg_root_mean_squared_error",
                n_jobs=1
            )),
            ("model", model_details["estimator"]),
        ])

        # --- 在全量数据上进行最终的超参数寻优 ---
        logger.info(f"    > 步骤1 & 2: 使用GroupKFold进行最终的特征选择和超参数联合寻优...")
        final_search = RandomizedSearchCV(
            estimator=optimization_pipeline,
            param_distributions=model_details["param_space"],
            n_iter=config.n_iter_random_search,
            cv=group_cv,
            scoring="neg_root_mean_squared_error",
            random_state=config.random_seed,
            n_jobs=-1,
            refit=True,  # 关键：在找到最佳参数后，用全部数据重新训练最佳评估器
        )
        final_search.fit(X, y, groups=groups)

        final_model_component = final_search.best_estimator_
        trained_sub_models.append(final_model_component)
        selected_features_count = final_model_component.named_steps['selector'].n_features_
        logger.info(f"    > '{model_name}' 最终选定 {selected_features_count} 个特征。")
        logger.info(f"    > '{model_name}' 最终最优参数: {final_search.best_params_}")

    logger.info("所有模型组件训练完成，正在组装最终的融合模型...")
    final_ensemble_model = WeightedEnsembleModel(model_1=trained_sub_models[0], model_2=trained_sub_models[1], weight_1=best_models_config[0]["weight"], weight_2=best_models_config[1]["weight"])

    # 关键修复：在元数据中添加用于预测的、带有固定顺序的特征列表
    model_assets = {
        "ensemble_model": final_ensemble_model,
        "metadata": {"target_feature": target_name, "ensemble_config": best_models_config, "tabular_features_ordered": list(X.columns)},
    }
    return model_assets
