# Fluid Property Functions (`logwp.extras.fluidfuncs`)

一个提供用于计算油、气、水（PVT）性质的经典经验关联式函数的包。

这个包的核心设计理念是**单位安全**和**代码复用**。所有函数都与 `logwp.extras.units` 包无缝集成，并尽可能复用 `logwp.extras.petrofuncs` 中的验证工具，以确保计算的健壮性和准确性。

## 核心特性

- **单位感知**: 函数的输入（如温度、压力）应为 `units.Quantity` 对象，而输出同样是带有单位的 `Quantity` 对象。这使得单位换算无缝且安全。
- **量纲验证**: 对需要单位的输入参数，强制进行量纲检查，防止传入错误的物理量。
- **领域专用**: 专注于石油工程中的流体性质计算，提供经过验证的经典关联式实现。

## 快速入门

使用包中的函数非常直观：

```python
from logwp.extras import units
from logwp.extras import fluidfuncs

# 1. 准备输入参数
rs = 550  # 溶解气油比, scf/STB
gamma_g = 0.65
api = 35
temp = units.Q_(150, 'degF') # 温度可以是任意合法的温度单位

# 2. 调用模型函数
bubble_point = fluidfuncs.standing_pb(
    rs=rs,
    gamma_g=gamma_g,
    api_gravity=api,
    temp=temp
)

print(f"计算出的饱和压力 (Pb) 为: {bubble_point:.2f}")
# > 计算出的饱和压力 (Pb) 为: 2036.28 psi

# 3. 对结果进行单位换算
print(f"转换为兆帕 (MPa): {bubble_point.to('MPa'):.2f}")
# > 转换为兆帕 (MPa): 14.04 MPa
```

## 可用模型函数

### 原油PVT模型 (`pvt_oil.py`)

#### `standing_pb(rs, gamma_g, api_gravity, temp)`
使用 Standing 关联式计算饱和压力。
- `rs`: 溶解气油比 (scf/STB)
- `gamma_g`: 天然气相对密度 (无量纲)
- `api_gravity`: 原油API重度 (无量纲)
- `temp`: 油藏温度 (`temperature`)

### 天然气PVT模型 (`pvt_gas.py`)

#### `lee_gonzalez_gas_viscosity(temp, pressure, gas_gravity, ...)`
使用 Lee-Gonzalez-Eakin 关联式计算气体粘度。
- `temp`: 温度 (`temperature`)
- `pressure`: 压力 (`pressure`)
- `gas_gravity`: 气体相对密度 (无量纲)
- `n2_mol_frac`, `co2_mol_frac`, `h2s_mol_frac`: 非烃组分摩尔分数 (可选)
