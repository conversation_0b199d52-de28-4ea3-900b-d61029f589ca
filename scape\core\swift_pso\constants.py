"""scape.core.swift_pso.constants - SWIFT-PSO产物常量定义

为每个步骤的所有产物定义标准化的逻辑名称，避免硬编码字符串。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO常量层
设计原则: 常量枚举、分层命名、全局唯一性
性能特征: 编译时类型检查、字符串常量优化

遵循CCG规范：
- CT-1: 分层枚举组织
- CT-2: 禁止硬编码字符串
- CT-3: 类型安全枚举
- CT-4: 统一常量引用

References
----------
- 《logwp/extras/tracking/机器学习组件开发框架》§3.2 - 产物与常量规范
- 《SCAPE_CCG_编码与通用规范》§CT - 常量管理规范
"""

from __future__ import annotations

from enum import Enum


class SwiftPsoTrainingArtifacts(str, Enum):
    """定义SWIFT-PSO训练步骤的所有产物名称。

    遵循 step_name.<category>.<specific_name> 命名规则，
    其中 step_name = "swift_pso_training"。

    Architecture
    ------------
    层次/依赖: scape/core层，SWIFT-PSO训练产物常量
    设计原则: 分层命名、全局唯一性、类型安全
    性能特征: 编译时类型检查、字符串常量优化

    References
    ----------
    - 《logwp/extras/tracking/机器学习组件开发框架》§3.2 - 产物常量规范
    """

    # 模型产物
    FINAL_PARAMETERS = "swift_pso_training.models.final_parameters"

    # 配置产物
    TRAINING_CONFIG = "swift_pso_training.configs.training_config"

    # 数据集产物
    ALL_OPTIMIZED_PARAMETERS = "swift_pso_training.datasets.all_parameters_from_lowo"

    # 报告产物
    CONVERGENCE_HISTORY_FINETUNE = "swift_pso_training.reports.convergence_history_finetune"

    # 诊断性产物
    PARAMS_WARM_START = "swift_pso_training.diagnostics.params_warm_start"
    BOOTSTRAP_SUMMARY = "swift_pso_training.reports.bootstrap_summary"
    DIAGNOSTIC_FOLD_PARAMETERS = "swift_pso_training.diagnostics.fold_parameters"
    DIAGNOSTIC_FOLD_LOSS_HISTORY = "swift_pso_training.diagnostics.fold_loss_history"


class SwiftPsoPredictionArtifacts(str, Enum):
    """定义SWIFT-PSO预测步骤的所有产物名称。

    遵循 step_name.<category>.<specific_name> 命名规则，
    其中 step_name = "swift_pso_prediction"。

    Architecture
    ------------
    层次/依赖: scape/core层，SWIFT-PSO预测产物常量
    设计原则: 分层命名、全局唯一性、类型安全
    性能特征: 编译时类型检查、字符串常量优化

    References
    ----------
    - 《logwp/extras/tracking/机器学习组件开发框架》§3.2 - 产物常量规范
    """

    # 数据集产物
    PREDICTED_PERMEABILITY = "swift_pso_prediction.datasets.predicted_permeability"


class TsneVisualArtifacts(str, Enum):
    """定义t-SNE可视化步骤的所有产物名称。

    遵循 step_name.<category>.<specific_name> 命名规则，
    其中 step_name = "swift_pso_visualization"。
    遵循"数据快照"原则，确保绘图的完全可复现性。

    Architecture
    ------------
    层次/依赖: scape/core层，t-SNE可视化产物常量
    设计原则: 分层命名、数据快照、可复现性
    性能特征: 编译时类型检查、字符串常量优化

    References
    ----------
    - 《logwp/extras/tracking/机器学习组件开发框架》§4.1 - 绘图数据快照规范
    """

    # 图表产物
    TSNE_CONVERGENCE_PLOT = "swift_pso_visualization.plots.tsne_convergence_trajectory"
    TSNE_CLUSTER_PLOT = "swift_pso_visualization.plots.tsne_cluster_analysis"

    # 数据快照产物
    TSNE_CONVERGENCE_PLOT_DATA = "swift_pso_visualization.data_snapshots.tsne_convergence_trajectory"
    TSNE_CLUSTER_PLOT_DATA = "swift_pso_visualization.data_snapshots.tsne_cluster_analysis"

    # 配置产物（确保完全可复现性）
    TSNE_PLOT_PROFILE = "swift_pso_visualization.configs.tsne_plot_profile"

    # 报告产物
    TSNE_CLUSTER_ANALYSIS_REPORT = "swift_pso_visualization.reports.tsne_cluster_analysis"
    TSNE_CLUSTER_STATISTICS_REPORT = "swift_pso_visualization.reports.tsne_cluster_statistics"
    TSNE_CLUSTER_SUMMARY_TABLE = "swift_pso_visualization.reports.tsne_cluster_summary_table"

    # 兼容旧版名称 (将在未来版本移除)
    TSNE_PLOT = TSNE_CONVERGENCE_PLOT
    TSNE_PLOT_DATA = TSNE_CONVERGENCE_PLOT_DATA

# t-SNE绘图配置名称常量
class TsnePlotProfiles(str, Enum):
    """t-SNE绘图配置模板名称常量。

    定义所有t-SNE相关的PlotProfile名称，用户通过这些常量来获取特定的配置。
    """
    # 模块级基础模板
    SWIFT_PSO_BASE = "swift_pso.base"

    # 具体图表模板
    CONVERGENCE_TRAJECTORY = "swift_pso.tsne_convergence"

    # 新增：聚类分析图表模板
    CLUSTER_ANALYSIS = "swift_pso.tsne_cluster_analysis"
