"""scape.core.swift_pso.exceptions - SWIFT-PSO专属异常类

定义SWIFT-PSO包的异常层次结构，遵循SCAPE项目异常处理规范。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO异常层
设计原则: 结构化异常信息、异常链保持、分层异常树
性能特征: 轻量级异常对象、延迟格式化

遵循CCG规范：
- EH-1: Exception Groups支持（Python 3.11+）
- EH-2: 结构化异常上下文信息
- EH-3: 分层异常树设计
- EH-4: 异常链保持完整性

References
----------
- 《SCAPE_CCG_编码与通用规范》§EH - 现代化异常处理
- 《logwp.models.exceptions》- SCAPE异常基类
"""

from __future__ import annotations

from typing import Any, Dict, Optional

from logwp.infra.exceptions import WpError, ErrorContext


class SwiftPsoError(WpError):
    """SWIFT-PSO包的根异常类。

    所有SWIFT-PSO相关的异常都应该继承自此类，形成清晰的异常层次结构。

    Architecture
    ------------
    层次/依赖: scape/core层，SWIFT-PSO异常层
    设计原则: 结构化异常信息、异常链保持、分层异常树
    性能特征: 轻量级异常对象、延迟格式化

    遵循CCG规范：
    - EH-2: 结构化异常上下文信息
    - EH-3: 分层异常树设计
    - EH-4: 异常链保持完整性

    References:
        《SCAPE_CCG_编码与通用规范》§EH - 现代化异常处理
    """

    def __init__(
        self,
        message: str,
        *,
        context: Optional[ErrorContext] = None,
        step_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化SWIFT-PSO异常。

        Args:
            message: 异常消息
            context: 结构化错误上下文
            step_name: 发生异常的步骤名称
            **kwargs: 其他上下文信息
        """
        if context is None:
            context = ErrorContext(
                operation="swift_pso_operation",
                dataset_name="unknown",
                reason="swift_pso_error",
                additional_info=kwargs
            )

        if step_name:
            context.additional_info = context.additional_info or {}
            context.additional_info["step_name"] = step_name

        super().__init__(message, context=context)


class SwiftPsoTrainingError(SwiftPsoError):
    """SWIFT-PSO训练步骤专属异常。

    用于训练过程中的特定错误，如PSO优化失败、收敛问题等。
    """

    def __init__(
        self,
        message: str,
        *,
        bootstrap_iteration: Optional[int] = None,
        pso_phase: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化训练异常。

        Args:
            message: 异常消息
            bootstrap_iteration: 发生异常的Bootstrap迭代编号
            pso_phase: PSO阶段 ('bootstrap_lowo' 或 'finetune')
            **kwargs: 其他上下文信息
        """
        additional_info = kwargs.copy()
        if bootstrap_iteration is not None:
            additional_info["bootstrap_iteration"] = bootstrap_iteration
        if pso_phase is not None:
            additional_info["pso_phase"] = pso_phase

        context = ErrorContext(
            operation="swift_pso_training",
            dataset_name=kwargs.get("dataset_name", "unknown"),
            reason="training_error",
            additional_info=additional_info
        )

        super().__init__(message, context=context, step_name="swift_pso_training")


class SwiftPsoPredictionError(SwiftPsoError):
    """SWIFT-PSO预测步骤专属异常。

    用于预测过程中的特定错误，如模型参数无效、数据维度不匹配等。
    """

    def __init__(
        self,
        message: str,
        *,
        model_parameter_count: Optional[int] = None,
        prediction_rows: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """初始化预测异常。

        Args:
            message: 异常消息
            model_parameter_count: 模型参数数量
            prediction_rows: 预测数据行数
            **kwargs: 其他上下文信息
        """
        additional_info = kwargs.copy()
        if model_parameter_count is not None:
            additional_info["model_parameter_count"] = model_parameter_count
        if prediction_rows is not None:
            additional_info["prediction_rows"] = prediction_rows

        context = ErrorContext(
            operation="swift_pso_prediction",
            dataset_name=kwargs.get("dataset_name", "unknown"),
            reason="prediction_error",
            additional_info=additional_info
        )

        super().__init__(message, context=context, step_name="swift_pso_prediction")


class SwiftPsoVisualizationError(SwiftPsoError):
    """SWIFT-PSO可视化步骤专属异常。

    用于可视化过程中的特定错误，如t-SNE计算失败、绘图配置错误等。
    """

    def __init__(
        self,
        message: str,
        *,
        tsne_points: Optional[int] = None,
        plot_profile_name: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化可视化异常。

        Args:
            message: 异常消息
            tsne_points: t-SNE数据点数量
            plot_profile_name: 绘图配置名称
            **kwargs: 其他上下文信息
        """
        additional_info = kwargs.copy()
        if tsne_points is not None:
            additional_info["tsne_points"] = tsne_points
        if plot_profile_name is not None:
            additional_info["plot_profile_name"] = plot_profile_name

        context = ErrorContext(
            operation="swift_pso_visualization",
            dataset_name=kwargs.get("dataset_name", "unknown"),
            reason="visualization_error",
            additional_info=additional_info
        )

        super().__init__(message, context=context, step_name="swift_pso_visualization")
