"""scape.core.baselines.sdr.exceptions - Custom exceptions for the SDR component."""


class SdrError(Exception):
    """Base exception for all errors raised by the SDR baseline component."""
    pass


class SdrDataError(SdrError):
    """Raised for data-related errors, such as missing columns or invalid values."""
    pass


class SdrModelError(SdrError):
    """Raised for model or optimization related errors, such as optimization failure."""
    pass
