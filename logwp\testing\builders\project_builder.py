"""项目构建器 - 测试专用

提供流畅的API来构建测试用的WpWellProject对象。

Examples
--------
>>> # 使用构建器模式创建项目
>>> project = ProjectBuilder("Test_Project") \
...     .with_dataset("logs", continuous_dataset) \
...     .with_dataset("core", discrete_dataset) \
...     .with_head_attribute("version", "1.0") \
...     .with_well_mapping("C-1A", "C-1") \
...     .build()
"""

from __future__ import annotations

from typing import Any, TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.models import WpWellProject
    from logwp.models.datasets import WpDepthIndexedDatasetBase


class ProjectBuilder:
    """项目构建器 - 测试专用。
    
    提供流畅的API来构建测试用的WpWellProject对象。
    """
    
    def __init__(self, name: str):
        """初始化项目构建器。
        
        Args:
            name: 项目名称
        """
        self._name = name
        self._datasets: dict[str, Any] = {}
        self._head_attributes: dict[str, Any] = {}
        self._well_mappings: dict[str, str] = {}
        self._default_depth_unit = "m"
    
    def with_dataset(self, name: str, dataset: WpDepthIndexedDatasetBase) -> 'ProjectBuilder':
        """添加数据集。
        
        Args:
            name: 数据集名称
            dataset: 数据集对象
            
        Returns:
            ProjectBuilder: 构建器实例（支持链式调用）
        """
        self._datasets[name] = dataset
        return self
    
    def with_datasets(self, datasets: dict[str, WpDepthIndexedDatasetBase]) -> 'ProjectBuilder':
        """批量添加数据集。
        
        Args:
            datasets: 数据集字典
            
        Returns:
            ProjectBuilder: 构建器实例（支持链式调用）
        """
        self._datasets.update(datasets)
        return self
    
    def with_head_attribute(self, name: str, value: Any) -> 'ProjectBuilder':
        """添加头部属性。
        
        Args:
            name: 属性名称
            value: 属性值
            
        Returns:
            ProjectBuilder: 构建器实例（支持链式调用）
        """
        self._head_attributes[name] = value
        return self
    
    def with_head_attributes(self, attributes: dict[str, Any]) -> 'ProjectBuilder':
        """批量添加头部属性。
        
        Args:
            attributes: 属性字典
            
        Returns:
            ProjectBuilder: 构建器实例（支持链式调用）
        """
        self._head_attributes.update(attributes)
        return self
    
    def with_well_mapping(self, source_well: str, target_well: str) -> 'ProjectBuilder':
        """添加井名映射。
        
        Args:
            source_well: 源井名
            target_well: 目标井名
            
        Returns:
            ProjectBuilder: 构建器实例（支持链式调用）
        """
        self._well_mappings[source_well] = target_well
        return self
    
    def with_well_mappings(self, mappings: dict[str, str]) -> 'ProjectBuilder':
        """批量添加井名映射。
        
        Args:
            mappings: 映射字典
            
        Returns:
            ProjectBuilder: 构建器实例（支持链式调用）
        """
        self._well_mappings.update(mappings)
        return self
    
    def with_default_depth_unit(self, unit: str) -> 'ProjectBuilder':
        """设置默认深度单位。
        
        Args:
            unit: 深度单位
            
        Returns:
            ProjectBuilder: 构建器实例（支持链式调用）
        """
        self._default_depth_unit = unit
        return self
    
    def build(self) -> 'WpWellProject':
        """构建项目对象。
        
        Returns:
            WpWellProject: 构建的项目对象
            
        Note:
            这个方法需要在有完整logwp环境时才能工作。
            在测试环境中，可能需要mock相关依赖。
        """
        # 注意：这里需要导入完整的logwp.models
        # 在实际使用时需要确保环境完整
        try:
            from logwp.models import WpWellProject
            
            return WpWellProject.create_with_datasets(
                name=self._name,
                datasets=self._datasets,
                head_attributes=self._head_attributes if self._head_attributes else None,
                well_mappings=self._well_mappings if self._well_mappings else None,
                default_depth_unit=self._default_depth_unit
            )
        except ImportError:
            # 在测试环境中可能无法导入完整的logwp
            raise RuntimeError(
                "ProjectBuilder.build() requires full logwp environment. "
                "Use ProjectBuilder for testing in environments with complete dependencies."
            )
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式（用于测试和调试）。
        
        Returns:
            dict: 构建器状态字典
        """
        return {
            "name": self._name,
            "datasets": list(self._datasets.keys()),
            "head_attributes": self._head_attributes.copy(),
            "well_mappings": self._well_mappings.copy(),
            "default_depth_unit": self._default_depth_unit
        }
