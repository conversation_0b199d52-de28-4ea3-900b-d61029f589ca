"""测试图像保存工具 (saver.py)。

测试save_figure相关函数的功能，包括：
- 单格式和多格式保存
- 路径处理和目录创建
- 配置参数应用
- 错误处理
"""

import pytest
from pathlib import Path

from logwp.extras.plotting import SaveConfig
from logwp.extras.plotting.saver import (
    save_figure,
    save_figure_with_path,
    create_save_config_from_legacy,
    _normalize_formats,
    _apply_figure_size
)
from logwp.extras.plotting.exceptions import ProfileIOError


class TestSaveConfig:
    """测试SaveConfig在保存功能中的使用。"""

    def test_save_config_validation(self):
        """测试SaveConfig验证（在profiles测试中已覆盖，这里做补充）。"""
        # 测试有效配置
        config = SaveConfig(format="png", dpi=300, width=10.0, height=8.0)
        assert config.format == "png"
        assert config.dpi == 300
        assert config.width == 10.0
        assert config.height == 8.0


class TestSaveFigure:
    """测试save_figure函数。"""

    def test_save_single_format(self, matplotlib_figure, temp_dir):
        """测试单格式保存。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(format="png", dpi=150)

        saved_paths = save_figure(fig, config, temp_dir, "test_single")

        # 验证返回值
        assert len(saved_paths) == 1
        assert saved_paths[0] == temp_dir / "test_single.png"

        # 验证文件存在
        assert saved_paths[0].exists()
        assert saved_paths[0].stat().st_size > 0  # 文件不为空

    def test_save_multiple_formats(self, matplotlib_figure, temp_dir):
        """测试多格式保存。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(format=["png", "svg"], dpi=150)

        saved_paths = save_figure(fig, config, temp_dir, "test_multi")

        # 验证返回值
        assert len(saved_paths) == 2
        expected_paths = [temp_dir / "test_multi.png", temp_dir / "test_multi.svg"]
        assert saved_paths == expected_paths

        # 验证文件存在
        for path in saved_paths:
            assert path.exists()
            assert path.stat().st_size > 0

    def test_save_with_custom_size(self, matplotlib_figure, temp_dir):
        """测试自定义尺寸保存。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(
            format="png",
            dpi=100,
            width=12.0,
            height=9.0
        )

        saved_paths = save_figure(fig, config, temp_dir, "test_size")

        # 验证文件保存成功
        assert len(saved_paths) == 1
        assert saved_paths[0].exists()

        # 验证图像尺寸已应用（通过检查figure对象）
        size_inches = fig.get_size_inches()
        assert size_inches[0] == 12.0
        assert size_inches[1] == 9.0

    def test_save_with_transparency(self, matplotlib_figure, temp_dir):
        """测试透明背景保存。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(
            format="png",
            dpi=150,
            transparent=True
        )

        saved_paths = save_figure(fig, config, temp_dir, "test_transparent")

        # 验证文件保存成功
        assert len(saved_paths) == 1
        assert saved_paths[0].exists()

    def test_save_directory_creation(self, matplotlib_figure, temp_dir):
        """测试目录自动创建。"""
        fig, ax = matplotlib_figure

        # 使用不存在的子目录
        nested_dir = temp_dir / "nested" / "subdir"
        assert not nested_dir.exists()

        config = SaveConfig(format="png")

        saved_paths = save_figure(fig, config, nested_dir, "test_nested")

        # 验证目录和文件都已创建
        assert nested_dir.exists()
        assert saved_paths[0].exists()
        assert saved_paths[0] == nested_dir / "test_nested.png"

    def test_save_with_save_kwargs(self, matplotlib_figure, temp_dir):
        """测试额外保存参数。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(
            format="png",
            dpi=150,
            save_kwargs={
                "bbox_inches": "tight",
                "pad_inches": 0.2
            }
        )

        saved_paths = save_figure(fig, config, temp_dir, "test_kwargs")

        # 验证文件保存成功
        assert len(saved_paths) == 1
        assert saved_paths[0].exists()

    def test_save_error_handling(self, matplotlib_figure):
        """测试保存错误处理。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(format="png")

        # 在Windows上使用一个真正无效的路径
        import platform
        if platform.system() == "Windows":
            # 使用保留设备名作为无效路径
            invalid_path = Path("CON:/invalid/readonly/path")
        else:
            invalid_path = Path("/proc/invalid/readonly/path")

        with pytest.raises(ProfileIOError) as exc_info:
            save_figure(fig, config, invalid_path, "test_error")

        assert exc_info.value.context.operation == "save_figure"


class TestSaveFigureWithPath:
    """测试save_figure_with_path函数。"""

    def test_save_with_full_path(self, matplotlib_figure, temp_dir):
        """测试使用完整路径保存。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(dpi=200, transparent=True)
        full_path = temp_dir / "test_full_path.svg"

        saved_path = save_figure_with_path(fig, config, full_path)

        # 验证返回值和文件
        assert saved_path == full_path
        assert saved_path.exists()
        assert saved_path.stat().st_size > 0

    def test_save_format_inference(self, matplotlib_figure, temp_dir):
        """测试格式自动推断。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(dpi=150)

        # 测试不同扩展名
        for ext in ["png", "svg", "pdf"]:
            full_path = temp_dir / f"test_inference.{ext}"
            saved_path = save_figure_with_path(fig, config, full_path)

            assert saved_path.exists()
            assert saved_path.suffix == f".{ext}"

    def test_save_no_extension_error(self, matplotlib_figure, temp_dir):
        """测试无扩展名错误。"""
        fig, ax = matplotlib_figure

        config = SaveConfig()
        path_no_ext = temp_dir / "no_extension"

        with pytest.raises(ProfileIOError, match="图像保存失败"):
            save_figure_with_path(fig, config, path_no_ext)

    def test_save_nested_directory_creation(self, matplotlib_figure, temp_dir):
        """测试嵌套目录创建。"""
        fig, ax = matplotlib_figure

        config = SaveConfig(format="png")
        nested_path = temp_dir / "deep" / "nested" / "path" / "test.png"

        saved_path = save_figure_with_path(fig, config, nested_path)

        # 验证目录和文件都已创建
        assert nested_path.parent.exists()
        assert saved_path.exists()


class TestHelperFunctions:
    """测试辅助函数。"""

    def test_normalize_formats(self):
        """测试格式标准化。"""
        # 单个格式
        assert _normalize_formats("PNG") == ["png"]
        assert _normalize_formats("svg") == ["svg"]

        # 多个格式
        assert _normalize_formats(["PNG", "SVG"]) == ["png", "svg"]
        assert _normalize_formats(["pdf", "EPS"]) == ["pdf", "eps"]

    def test_apply_figure_size(self, matplotlib_figure):
        """测试图像尺寸应用。"""
        fig, ax = matplotlib_figure

        # 测试设置尺寸
        config_with_size = SaveConfig(width=15.0, height=10.0)
        _apply_figure_size(fig, config_with_size)

        size_inches = fig.get_size_inches()
        assert size_inches[0] == 15.0
        assert size_inches[1] == 10.0

        # 测试不设置尺寸
        config_no_size = SaveConfig()
        original_size = fig.get_size_inches().copy()
        _apply_figure_size(fig, config_no_size)

        # 尺寸应该保持不变
        new_size = fig.get_size_inches()
        assert new_size[0] == original_size[0]
        assert new_size[1] == original_size[1]

    def test_create_save_config_from_legacy(self, temp_dir):
        """测试从传统参数创建SaveConfig。"""
        filepath = temp_dir / "legacy_test.png"

        config, base_path, name = create_save_config_from_legacy(
            filepath=filepath,
            dpi=250,
            transparent=True,
            bbox_inches="tight",
            pad_inches=0.1
        )

        # 验证SaveConfig
        assert config.format == "png"
        assert config.dpi == 250
        assert config.transparent is True
        assert config.save_kwargs["bbox_inches"] == "tight"
        assert config.save_kwargs["pad_inches"] == 0.1

        # 验证路径分解
        assert base_path == temp_dir
        assert name == "legacy_test"

    def test_create_save_config_from_legacy_no_extension(self, temp_dir):
        """测试无扩展名的传统参数转换。"""
        filepath = temp_dir / "no_extension"

        config, base_path, name = create_save_config_from_legacy(filepath)

        # 格式应该为空字符串
        assert config.format == ""
        assert base_path == temp_dir
        assert name == "no_extension"


class TestIntegrationScenarios:
    """测试集成场景。"""

    def test_complete_save_workflow(self, matplotlib_figure, temp_dir):
        """测试完整的保存工作流。"""
        fig, ax = matplotlib_figure

        # 添加一些内容到图表
        ax.plot([1, 2, 3, 4], [1, 4, 2, 3], 'b-', linewidth=2, label="Test Data")
        ax.set_title("Integration Test Plot")
        ax.set_xlabel("X Values")
        ax.set_ylabel("Y Values")
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 配置多格式保存
        config = SaveConfig(
            format=["png", "svg", "pdf"],
            dpi=200,
            width=10.0,
            height=6.0,
            transparent=False,
            bbox_inches="tight"
        )

        # 保存图像
        saved_paths = save_figure(fig, config, temp_dir, "integration_test")

        # 验证所有格式都已保存
        assert len(saved_paths) == 3

        expected_files = [
            temp_dir / "integration_test.png",
            temp_dir / "integration_test.svg",
            temp_dir / "integration_test.pdf"
        ]

        for expected_path in expected_files:
            assert expected_path in saved_paths
            assert expected_path.exists()
            assert expected_path.stat().st_size > 0

    def test_error_recovery_workflow(self, matplotlib_figure, temp_dir):
        """测试错误恢复工作流。"""
        fig, ax = matplotlib_figure

        # 创建一个部分有效的配置
        config = SaveConfig(
            format=["png", "invalid_format", "svg"],  # 包含无效格式
            dpi=150
        )

        # 保存应该部分成功
        try:
            saved_paths = save_figure(fig, config, temp_dir, "error_recovery")

            # 检查有效格式是否保存成功
            png_path = temp_dir / "error_recovery.png"
            svg_path = temp_dir / "error_recovery.svg"

            # 至少PNG应该成功
            assert png_path.exists()

        except Exception:
            # 如果抛出异常，确保至少部分文件可能已保存
            pass
