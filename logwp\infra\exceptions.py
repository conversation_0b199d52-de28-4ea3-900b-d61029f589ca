from __future__ import annotations

from typing import Any, NamedTuple


# ------------------------------------------------------------
# 基础异常上下文信息（从models.exceptions移动到此处）
# ------------------------------------------------------------


class ErrorContext(NamedTuple):
    """异常上下文信息，提供丰富的诊断信息。

    Architecture
    ------------
    层次/依赖: 基础类型，被所有异常类使用
    设计原则: 结构化信息、类型安全、便于序列化
    性能特征: 轻量级NamedTuple，创建开销极小
    """
    operation: str
    file_path: str | None = None
    sheet_name: str | None = None
    dataset_name: str | None = None
    bundle_name: str | None = None
    line_number: int | None = None
    column_name: str | None = None
    reason: str | None = None
    additional_info: dict[str, Any] | None = None


class CompContext(NamedTuple):
    """COMP类型数据上下文信息。

    Architecture
    ------------
    层次/依赖: COMP类型处理层专用上下文
    设计原则: 结构化诊断、JSON错误定位、类型验证
    性能特征: 轻量级结构，支持COMP错误诊断
    """
    attribute_name: str | None = None
    json_content: str | None = None
    validation_error: str | None = None
    expected_structure: dict[str, Any] | None = None
    actual_structure: dict[str, Any] | None = None
    field_path: str | None = None  # JSON字段路径，如 "T2_Start.v"


# ------------------------------------------------------------
# 根异常类（从models.exceptions移动到此处）
# ------------------------------------------------------------


class WpError(Exception):
    """logwp 现代化根异常。

    任何 logwp 及其横切包抛出的异常均应派生自本类。

    现代化特性：
    - 结构化异常信息（ErrorContext）
    - 丰富的诊断上下文
    - 支持 Exception Groups 批量处理
    - GPU计算异常支持

    Architecture
    ------------
    层次/依赖: 根异常类，所有logwp异常的基类
    设计原则: 结构化信息、异常链保持、诊断友好
    性能特征: 轻量级设计，上下文信息可选

    Args:
        message: 异常描述信息
        context: 结构化上下文信息，便于诊断和UI展示

    Examples:
        >>> # 基本使用
        >>> raise WpError("数据处理失败")

        >>> # 带上下文信息
        >>> ctx = ErrorContext(
        ...     operation="read_excel",
        ...     file_path="data.xlsx",
        ...     sheet_name="OBMIQ_logs"
        ... )
        >>> raise WpError("Excel读取失败", context=ctx)
    """

    def __init__(
        self,
        message: str,
        *,
        context: ErrorContext | None = None
    ) -> None:
        super().__init__(message)
        self.context = context

    def __str__(self) -> str:
        """返回包含上下文信息的异常描述。"""
        base_msg = super().__str__()
        if not self.context:
            return base_msg

        ctx_parts = []
        if self.context.operation:
            ctx_parts.append(f"操作: {self.context.operation}")
        if self.context.file_path:
            ctx_parts.append(f"文件: {self.context.file_path}")
        if self.context.sheet_name:
            ctx_parts.append(f"工作表: {self.context.sheet_name}")
        if self.context.dataset_name:
            ctx_parts.append(f"数据集: {self.context.dataset_name}")
        if self.context.line_number:
            ctx_parts.append(f"行号: {self.context.line_number}")
        if self.context.column_name:
            ctx_parts.append(f"列名: {self.context.column_name}")

        if ctx_parts:
            return f"{base_msg} [{', '.join(ctx_parts)}]"
        return base_msg


# ------------------------------------------------------------
# GPU计算上下文信息
# ------------------------------------------------------------


class GpuContext(NamedTuple):
    """GPU计算上下文信息。

    Architecture
    ------------
    层次/依赖: GPU计算层专用上下文
    设计原则: GPU状态诊断、设备信息、内存状态
    性能特征: 轻量级结构，支持GPU错误诊断
    """
    device_id: int | None = None
    device_name: str | None = None
    memory_used: int | None = None  # MB
    memory_total: int | None = None  # MB
    cuda_version: str | None = None
    operation: str | None = None


class WpGpuError(WpError):
    """GPU 计算相关错误基类。

    处理CUDA、OpenCL等GPU计算框架的错误。

    Architecture
    ------------
    层次/依赖: GPU计算层专用异常
    设计原则: GPU状态诊断、自动回退、设备管理
    性能特征: 支持GPU/CPU自动切换
    """

    def __init__(
        self,
        message: str,
        *,
        context: ErrorContext | None = None,
        gpu_context: GpuContext | None = None
    ) -> None:
        super().__init__(message, context=context)
        self.gpu_context = gpu_context

    def __str__(self) -> str:
        """返回包含GPU上下文信息的异常描述。"""
        base_msg = super().__str__()
        if not self.gpu_context:
            return base_msg

        gpu_parts = []
        if self.gpu_context.device_id is not None:
            gpu_parts.append(f"设备ID: {self.gpu_context.device_id}")
        if self.gpu_context.device_name:
            gpu_parts.append(f"设备: {self.gpu_context.device_name}")
        if self.gpu_context.memory_used and self.gpu_context.memory_total:
            usage = self.gpu_context.memory_used / self.gpu_context.memory_total * 100
            gpu_parts.append(f"显存: {self.gpu_context.memory_used}MB/{self.gpu_context.memory_total}MB ({usage:.1f}%)")
        if self.gpu_context.cuda_version:
            gpu_parts.append(f"CUDA: {self.gpu_context.cuda_version}")

        if gpu_parts:
            return f"{base_msg} [GPU: {', '.join(gpu_parts)}]"
        return base_msg


class WpGpuMemoryError(WpGpuError):
    """GPU 内存不足或分配失败。

    Examples:
        >>> gpu_ctx = GpuContext(
        ...     device_id=0,
        ...     device_name="NVIDIA RTX 4090",
        ...     memory_used=22000,
        ...     memory_total=24000,
        ...     operation="cudf_dataframe_creation"
        ... )
        >>> ctx = ErrorContext(
        ...     operation="load_large_dataset_gpu",
        ...     dataset_name="HUGE_LOGS",
        ...     additional_info={"requested_memory_mb": 4000}
        ... )
        >>> raise WpGpuMemoryError("GPU内存不足", context=ctx, gpu_context=gpu_ctx)
    """


class WpGpuDeviceError(WpGpuError):
    """GPU 设备不可用或初始化失败。

    Examples:
        >>> gpu_ctx = GpuContext(
        ...     device_id=0,
        ...     cuda_version="12.0",
        ...     operation="device_initialization"
        ... )
        >>> ctx = ErrorContext(
        ...     operation="initialize_gpu_computing",
        ...     additional_info={"available_devices": 0, "required_compute_capability": "7.0"}
        ... )
        >>> raise WpGpuDeviceError("GPU设备不可用", context=ctx, gpu_context=gpu_ctx)
    """


class WpGpuComputationError(WpGpuError):
    """GPU 计算过程中的错误。

    Examples:
        >>> gpu_ctx = GpuContext(
        ...     device_id=0,
        ...     device_name="NVIDIA RTX 4090",
        ...     operation="cupy_matrix_multiplication"
        ... )
        >>> ctx = ErrorContext(
        ...     operation="gpu_feature_computation",
        ...     dataset_name="OBMIQ_logs",
        ...     additional_info={"matrix_shape": [10000, 500], "operation": "matmul"}
        ... )
        >>> raise WpGpuComputationError("GPU计算失败", context=ctx, gpu_context=gpu_ctx)
    """

