"""logwp.models.service - 内部服务层

遵循SAD文档的内部服务层设计模式（Utility/Helper Pattern），
为WpWellProject等模型类提供无状态的服务函数。

Architecture
------------
层次/依赖: models/service层，依赖models、datasets、exceptions
设计原则: 无状态服务、参数验证、错误处理、日志记录
性能特征: 向量化操作、批量处理、内存优化

服务模块：
- well_mapping: 井名映射服务
- curve_extraction: 曲线提取服务
- dataset_merge: 数据集合并服务
- summary_service: 数据概况生成服务
- head_summary: 井头信息概况服务
- wellmap_summary: 井名映射概况服务

References
----------
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
- 《SCAPE_DDS_logwp_曲线提取.md》§3 - 服务层设计
"""

# 服务模块导入
from . import well_mapping
from ..datasets.internal import curve_extraction
from . import dataset_merge
from . import summary_service
from . import head_summary
from . import wellmap_summary

__all__ = [
    "well_mapping",
    "curve_extraction",
    "dataset_merge",
    "summary_service",
    "head_summary",
    "wellmap_summary",
]
