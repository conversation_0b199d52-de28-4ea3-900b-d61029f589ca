"""logwp.extras.petroplot.crossplot.internal.marginal_plot_helper - 边缘图辅助工具

本模块定义了 MarginalPlotHelper，一个用于处理交会图边缘分布图的辅助类。
它封装了子图创建、数据聚合和边缘图绘制的所有复杂性。
"""

from typing import Optional, Dict, Any, Tuple
import pandas as pd
from plotly.subplots import make_subplots
import plotly.graph_objects as go
from logwp.infra import get_logger

from ..config import CrossPlotConfig, CrossPlotColumnSelectors
from logwp.extras.plotting import PlotProfile

logger = get_logger(__name__)


class MarginalPlotHelper:
    """
    一个辅助类，封装了创建和绘制边缘分布图的所有逻辑。
    """

    def __init__(
        self,
        config: CrossPlotConfig,
        plot_profile: PlotProfile,
        data_dict: Dict[str, pd.DataFrame],
        selectors: CrossPlotColumnSelectors,
    ):
        self.config = config
        self.plot_profile = plot_profile
        self.data_dict = data_dict
        self.selectors = selectors

        mx_cfg = self.config.marginal_x
        my_cfg = self.config.marginal_y

        self._has_marginal_x = mx_cfg and mx_cfg.show
        self._has_marginal_y = my_cfg and my_cfg.show

        self.main_subplot_pos: Optional[Tuple[int, int]] = None
        self.x_marginal_pos: Optional[Tuple[int, int]] = None
        self.y_marginal_pos: Optional[Tuple[int, int]] = None

        if self.is_active:
            self.main_subplot_pos = (2, 1)
            self.x_marginal_pos = (1, 1)
            self.y_marginal_pos = (2, 2)

    @property
    def is_active(self) -> bool:
        """如果启用了任何边缘图，则返回True。"""
        return self._has_marginal_x or self._has_marginal_y

    def setup_figure(self) -> go.Figure:
        """
        根据是否启用边缘图，创建并返回一个 go.Figure 对象。
        """
        if not self.is_active:
            return go.Figure()

        mx_cfg = self.config.marginal_x
        my_cfg = self.config.marginal_y
        x_size = mx_cfg.size if self._has_marginal_x and mx_cfg else 0
        y_size = my_cfg.size if self._has_marginal_y and my_cfg else 0

        return make_subplots(
            rows=2, cols=2,
            column_widths=[1.0 - y_size, y_size],
            row_heights=[x_size, 1.0 - x_size],
            shared_xaxes=True, shared_yaxes=True,
            vertical_spacing=0.02, horizontal_spacing=0.02,
        )

    def get_main_subplot_args(self) -> Dict[str, Any]:
        """返回用于在主子图中添加轨迹或图形的参数字典。"""
        if not self.is_active or not self.main_subplot_pos:
            return {}
        return {'row': self.main_subplot_pos[0], 'col': self.main_subplot_pos[1]}

    def draw_marginal_plots(self, fig: go.Figure):
        """
        收集所有被标记的系列的数据，并在提供的Figure上绘制聚合的边缘分布图。
        """
        if not self.is_active:
            return

        x_data_list, y_data_list = [], []
        for selector in self.selectors.series:
            if selector.include_in_marginals:
                df = self.data_dict.get(selector.bundle_name)
                if df is None or df.empty: continue

                # --- 【新增】前置列存在性检查，防止KeyError ---
                required_cols_for_marginal = [col for col in [selector.x_col, selector.y_col] if col]
                missing_cols = [c for c in required_cols_for_marginal if c not in df.columns]
                if missing_cols:
                    logger.warning(
                        f"系列 '{selector.name}' 的数据源 '{selector.bundle_name}' "
                        f"缺少用于边缘图的列: {missing_cols}，将跳过此系列的边缘图数据。"
                    )
                    continue

                required_cols = [col for col in [selector.x_col, selector.y_col] if col]
                df_clean = df.dropna(subset=required_cols)
                if df_clean.empty: continue

                if self._has_marginal_x and selector.x_col:
                    x_data_list.append(df_clean[selector.x_col])
                if self._has_marginal_y and selector.y_col:
                    y_data_list.append(df_clean[selector.y_col])

        if self._has_marginal_x and x_data_list:
            all_x_data = pd.concat(x_data_list, ignore_index=True)
            if not all_x_data.empty:
                self._add_single_marginal(fig, all_x_data, axis='x')
        if self._has_marginal_y and y_data_list:
            all_y_data = pd.concat(y_data_list, ignore_index=True)
            if not all_y_data.empty:
                self._add_single_marginal(fig, all_y_data, axis='y')

    def _add_single_marginal(self, fig: go.Figure, data: pd.Series, axis: str):
        """为单个轴添加边缘分布图轨迹。"""
        is_x_axis = axis == 'x'
        marginal_cfg = self.config.marginal_x if is_x_axis else self.config.marginal_y
        row_col = self.x_marginal_pos if is_x_axis else self.y_marginal_pos
        if not marginal_cfg or not row_col: return

        plot_type = marginal_cfg.plot_type
        profile_style = self.plot_profile.artist_props.get(f"marginal_{plot_type}", {})
        style_props = {**profile_style, **marginal_cfg.style_props}
        trace_args = {'showlegend': False, 'name': '', 'hoverinfo': 'skip'}

        if plot_type == 'histogram':
            trace = go.Histogram(x=data if is_x_axis else None, y=data if not is_x_axis else None, orientation='v' if is_x_axis else 'h', marker=style_props, **trace_args)
        elif plot_type == 'box':
            trace = go.Box(x=data if is_x_axis else None, y=data if not is_x_axis else None, orientation='v' if is_x_axis else 'h', **style_props, **trace_args)
        elif plot_type == 'rug':
            trace = go.Scatter(x=data if is_x_axis else [0] * len(data), y=[0] * len(data) if is_x_axis else data, mode='markers', marker={'symbol': 'line-ns-open' if is_x_axis else 'line-ew-open', **style_props}, **trace_args)
        else:
            return

        fig.add_trace(trace, row=row_col[0], col=row_col[1])
