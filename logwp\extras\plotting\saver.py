"""logwp.extras.plotting.saver - 图像保存工具函数

实现基于SaveConfig的图像保存功能，支持多格式保存和高质量输出。

Architecture
------------
层次/依赖: logwp.extras.plotting包工具层
设计原则: 单一职责、异常安全、多格式支持
性能特征: 批量保存、目录自动创建、格式自动推断

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持完整性
- LG-2: 结构化日志信息
- PF-2: 异步I/O支持（未来扩展）

References
----------
- 《绘图系统全新重构设计文档》§1.4 - saver工具函数设计
- 《SCAPE_CCG_编码与通用规范》§PF - 性能优化要求
"""

from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING, Union

from logwp.extras.plotting.exceptions import ProfileIOError

if TYPE_CHECKING:
    import matplotlib.pyplot as plt
    from .profiles import SaveConfig

# 延迟导入logger避免循环依赖
def _get_logger():
    try:
        from logwp.infra import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)


def save_figure(
    fig: plt.Figure,
    save_config: SaveConfig,
    base_path: Union[str, Path],
    base_name: str
) -> list[Path]:
    """根据SaveConfig保存matplotlib Figure对象。

    支持单格式或多格式保存，自动处理目录创建、格式推断和参数优化。
    这是新绘图系统的核心保存函数，完全替代旧的保存逻辑。

    Args:
        fig: 要保存的matplotlib Figure对象
        save_config: 保存配置，包含格式、DPI、尺寸等参数
        base_path: 保存的基础路径（目录）
        base_name: 文件基础名称（不含扩展名）

    Returns:
        list[Path]: 实际保存的文件路径列表

    Raises:
        ProfileIOError: 文件保存失败

    Examples:
        >>> import matplotlib.pyplot as plt
        >>> from logwp.extras.plotting import SaveConfig, save_figure
        >>>
        >>> fig, ax = plt.subplots()
        >>> ax.plot([1, 2, 3], [1, 4, 2])
        >>>
        >>> # 单格式保存
        >>> config = SaveConfig(format="png", dpi=300)
        >>> paths = save_figure(fig, config, "output/plots", "test_plot")
        >>> # 保存为: output/plots/test_plot.png
        >>>
        >>> # 多格式保存
        >>> config = SaveConfig(format=["png", "svg", "pdf"], dpi=300)
        >>> paths = save_figure(fig, config, "output/plots", "test_plot")
        >>> # 保存为: output/plots/test_plot.png, test_plot.svg, test_plot.pdf
    """
    _get_logger().info(
        "开始保存图像",
        operation="save_figure",
        base_path=str(base_path),
        base_name=base_name,
        formats=save_config.format
    )

    try:
        # 确保基础路径存在
        base_path = Path(base_path)
        base_path.mkdir(parents=True, exist_ok=True)

        # 应用尺寸设置
        _apply_figure_size(fig, save_config)

        # 确定保存格式列表
        formats = _normalize_formats(save_config.format)

        # 执行多格式保存
        saved_paths = []
        for fmt in formats:
            file_path = base_path / f"{base_name}.{fmt}"
            _save_single_format(fig, file_path, save_config, fmt)
            saved_paths.append(file_path)

        _get_logger().info(
            "图像保存成功",
            operation="save_figure",
            saved_count=len(saved_paths),
            saved_paths=[str(p) for p in saved_paths]
        )

        return saved_paths

    except Exception as e:
        from logwp.models.exceptions import ErrorContext
        context = ErrorContext(
            operation="save_figure",
            file_path=str(base_path / base_name)
        )
        raise ProfileIOError(f"图像保存失败: {e}", context=context) from e


def save_figure_with_path(
    fig: plt.Figure,
    save_config: SaveConfig,
    full_path: Union[str, Path]
) -> Path:
    """使用完整路径保存图像（单格式）。

    这是save_figure的简化版本，适用于只需要保存单个格式的场景。
    格式从文件扩展名自动推断。

    Args:
        fig: 要保存的matplotlib Figure对象
        save_config: 保存配置
        full_path: 完整的文件路径（包含扩展名）

    Returns:
        Path: 实际保存的文件路径

    Raises:
        ProfileIOError: 文件保存失败

    Examples:
        >>> config = SaveConfig(dpi=300, transparent=True)
        >>> path = save_figure_with_path(fig, config, "output/my_plot.png")
    """
    try:
        full_path = Path(full_path)

        # 从扩展名推断格式
        format_from_ext = full_path.suffix.lstrip('.').lower()
        if not format_from_ext:
            raise ValueError(f"无法从路径推断文件格式: {full_path}")

        # 确保目录存在
        full_path.parent.mkdir(parents=True, exist_ok=True)

        # 应用尺寸设置
        _apply_figure_size(fig, save_config)

        # 保存文件
        _save_single_format(fig, full_path, save_config, format_from_ext)

        _get_logger().info(
            "图像保存成功",
            operation="save_figure_with_path",
            path=str(full_path),
            format=format_from_ext
        )

        return full_path

    except Exception as e:
        from logwp.infra.exceptions import ErrorContext
        context = ErrorContext(
            operation="save_figure_with_path",
            file_path=str(full_path)
        )
        raise ProfileIOError(f"图像保存失败: {e}", context=context) from e


def _apply_figure_size(fig: plt.Figure, save_config: SaveConfig) -> None:
    """应用图像尺寸设置。

    Args:
        fig: matplotlib Figure对象
        save_config: 保存配置
    """
    if save_config.width is not None and save_config.height is not None:
        fig.set_size_inches(save_config.width, save_config.height)
        _get_logger().debug(
            "图像尺寸已设置",
            width=save_config.width,
            height=save_config.height
        )


def _normalize_formats(format_spec: Union[str, list[str]]) -> list[str]:
    """标准化格式规格为格式列表。

    Args:
        format_spec: 格式规格，可以是字符串或字符串列表

    Returns:
        list[str]: 标准化的格式列表
    """
    if isinstance(format_spec, str):
        return [format_spec.lower()]
    else:
        return [fmt.lower() for fmt in format_spec]


def _save_single_format(
    fig: plt.Figure,
    file_path: Path,
    save_config: SaveConfig,
    format_name: str
) -> None:
    """保存单个格式的图像文件。

    Args:
        fig: matplotlib Figure对象
        file_path: 保存路径
        save_config: 保存配置
        format_name: 格式名称
    """
    # 准备savefig参数
    save_kwargs = {
        "dpi": save_config.dpi,
        "bbox_inches": save_config.bbox_inches,
        "transparent": save_config.transparent,
        "format": format_name,
    }

    # 添加格式特定参数
    if format_name in ["jpg", "jpeg"]:
        # JPEG不支持透明度
        save_kwargs["transparent"] = False
        # 如果配置中有质量设置，添加到参数中
        if hasattr(save_config, 'quality') and save_config.quality is not None:
            save_kwargs["quality"] = save_config.quality

    # 合并用户自定义参数
    save_kwargs.update(save_config.save_kwargs)

    # 执行保存
    fig.savefig(file_path, **save_kwargs)

    _get_logger().debug(
        "单格式保存完成",
        path=str(file_path),
        format=format_name,
        dpi=save_config.dpi
    )


def create_save_config_from_legacy(
    filepath: Union[str, Path],
    dpi: int = 300,
    transparent: bool = False,
    **kwargs
) -> tuple["SaveConfig", Path, str]:
    """从传统参数创建SaveConfig（兼容性函数）。

    这个函数帮助从旧的绘图系统迁移到新系统，将传统的保存参数
    转换为新的SaveConfig格式。

    Args:
        filepath: 完整文件路径
        dpi: 图像分辨率
        transparent: 是否透明背景
        **kwargs: 其他savefig参数

    Returns:
        tuple[SaveConfig, Path, str]: (保存配置, 基础路径, 文件名)

    Examples:
        >>> # 迁移旧代码
        >>> # 旧: fig.savefig("output/plot.png", dpi=300, transparent=True)
        >>> # 新:
        >>> config, base_path, name = create_save_config_from_legacy(
        ...     "output/plot.png", dpi=300, transparent=True
        ... )
        >>> save_figure_with_path(fig, config, "output/plot.png")
    """
    filepath = Path(filepath)

    # 从路径提取格式
    format_name = filepath.suffix.lstrip('.').lower()

    # 创建SaveConfig
    from .profiles import SaveConfig
    save_config = SaveConfig(
        format=format_name,
        dpi=dpi,
        transparent=transparent,
        save_kwargs=kwargs
    )

    return save_config, filepath.parent, filepath.stem
