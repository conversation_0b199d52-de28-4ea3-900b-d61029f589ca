"""NMR相关常量定义。

定义NMR模块使用的所有常量，包括算法参数、物理约束、
数值计算配置等。

Architecture
------------
层次/依赖: NMR常量层，被所有NMR模块引用
设计原则: 集中管理、类型安全、易于维护
性能特征: 编译时常量、快速访问
"""

from __future__ import annotations

from enum import Enum
from typing import Final

# =============================================================================
# NMR算法相关常量
# =============================================================================

class WpNmrAlgorithm(str, Enum):
    """NMR算法类型枚举。
    
    Architecture
    ------------
    层次/依赖: NMR算法选择，支持多种实现
    设计原则: 算法抽象、性能优化、自动选择
    性能特征: GPU优先、CPU回退、自适应选择
    """
    T2_POROSITY_DIVISION = "t2_porosity_division"    # T2谱孔隙度划分
    PERMEABILITY_ESTIMATION = "permeability_estimation"  # 渗透率估算
    RELAXATION_ANALYSIS = "relaxation_analysis"      # 弛豫时间分析


class WpNmrPorosityType(str, Enum):
    """NMR孔隙度类型枚举。
    
    Architecture
    ------------
    层次/依赖: 孔隙度分类，基于T2截止值
    设计原则: 物理意义明确、标准化分类
    性能特征: 快速分类、精确计算
    """
    MICRO = "micro"     # 微孔（束缚水孔隙度）
    MESO = "meso"       # 中孔（可动水孔隙度）
    MACRO = "macro"     # 大孔（自由水孔隙度）


class WpNmrIntegrationMethod(str, Enum):
    """NMR积分计算方法枚举。
    
    Architecture
    ------------
    层次/依赖: 数值积分方法，影响计算精度
    设计原则: 数值稳定、精度保证、性能平衡
    性能特征: 梯形积分优先、矩形积分备选
    """
    TRAPEZOID = "trapezoid"     # 梯形积分（推荐）
    RECTANGLE = "rectangle"     # 矩形积分
    SIMPSON = "simpson"         # 辛普森积分（高精度）


# =============================================================================
# 默认参数配置
# =============================================================================

# T2谱孔隙度划分默认参数
DEFAULT_T2_CUTOFF_SHORT: Final[float] = 3.0    # 短T2截止值，单位ms
DEFAULT_T2_CUTOFF_LONG: Final[float] = 33.0    # 长T2截止值，单位ms
DEFAULT_VALIDATE_PHYSICS: Final[bool] = True   # 默认进行物理约束验证

# 数值计算默认参数
DEFAULT_INTEGRATION_METHOD: Final[str] = WpNmrIntegrationMethod.TRAPEZOID
DEFAULT_NUMERICAL_TOLERANCE: Final[float] = 1e-10
DEFAULT_ZERO_THRESHOLD: Final[float] = 1e-12

# 物理约束参数
MIN_POROSITY: Final[float] = 0.0      # 最小孔隙度
MAX_POROSITY: Final[float] = 1.0      # 最大孔隙度
MIN_T2_VALUE: Final[float] = 0.001    # 最小T2值，单位ms
MAX_T2_VALUE: Final[float] = 100000.0 # 最大T2值，单位ms

# =============================================================================
# 错误消息常量
# =============================================================================

# 数据验证错误消息
ERROR_INVALID_T2_SPECTRUM: Final[str] = "T2谱数据无效，必须为一维numpy数组"
ERROR_T2_AXIS_MISMATCH: Final[str] = "T2轴与T2谱数据长度不匹配"
ERROR_INVALID_CUTOFF_ORDER: Final[str] = "T2截止值顺序错误，短截止值必须小于长截止值"
ERROR_NEGATIVE_CUTOFF: Final[str] = "T2截止值必须为正数"
ERROR_INVALID_T2_RANGE: Final[str] = "T2范围无效，最小值必须小于最大值"

# 物理约束错误消息
ERROR_NEGATIVE_POROSITY: Final[str] = "孔隙度不能为负数"
ERROR_POROSITY_EXCEEDS_UNITY: Final[str] = "孔隙度不能大于1"
ERROR_INVALID_POROSITY_VALUES: Final[str] = "孔隙度组分包含无效值（NaN或Inf）"
ERROR_TOTAL_POROSITY_WARNING: Final[str] = "总孔隙度超过1，可能存在数据质量问题"

# 计算错误消息
ERROR_INTEGRATION_FAILED: Final[str] = "积分计算失败，已回退到简单求和"
ERROR_NO_DATA_IN_RANGE: Final[str] = "指定T2范围内无有效数据点"
ERROR_COMPUTATION_FAILED: Final[str] = "NMR计算失败，请检查输入数据"

# =============================================================================
# 日志相关常量
# =============================================================================

# 日志操作类型
LOG_OPERATION_COMPUTE: Final[str] = "nmr_compute"
LOG_OPERATION_VALIDATE: Final[str] = "nmr_validate"
LOG_OPERATION_INTEGRATE: Final[str] = "nmr_integrate"
LOG_OPERATION_PREPROCESS: Final[str] = "nmr_preprocess"

# 日志阶段
LOG_STAGE_START: Final[str] = "start"
LOG_STAGE_PROGRESS: Final[str] = "progress"
LOG_STAGE_COMPLETE: Final[str] = "complete"
LOG_STAGE_ERROR: Final[str] = "error"
LOG_STAGE_WARNING: Final[str] = "warning"

# 日志组件
LOG_COMPONENT_T2_DIVISION: Final[str] = "t2_porosity_division"
LOG_COMPONENT_VALIDATION: Final[str] = "data_validation"
LOG_COMPONENT_INTEGRATION: Final[str] = "numerical_integration"

# =============================================================================
# 性能优化常量
# =============================================================================

# 数组处理阈值
LARGE_ARRAY_THRESHOLD: Final[int] = 10000      # 大数组处理阈值
CHUNK_SIZE_THRESHOLD: Final[int] = 100000      # 分块处理阈值
MEMORY_LIMIT_MB: Final[int] = 1024             # 内存限制，单位MB

# 并行计算参数
DEFAULT_N_JOBS: Final[int] = -1                # 默认并行作业数（-1表示使用所有CPU）
MIN_SAMPLES_FOR_PARALLEL: Final[int] = 1000    # 启用并行计算的最小样本数

# =============================================================================
# 单位转换常量
# =============================================================================

class WpNmrTimeUnit(str, Enum):
    """NMR时间单位枚举。
    
    Architecture
    ------------
    层次/依赖: 时间单位标准化，支持单位转换
    设计原则: 标准化单位、自动转换、精度保证
    性能特征: 快速转换、数值稳定
    """
    MILLISECOND = "ms"      # 毫秒（默认）
    SECOND = "s"            # 秒
    MICROSECOND = "us"      # 微秒


# 时间单位转换因子（转换为毫秒）
TIME_UNIT_CONVERSION: Final[dict[str, float]] = {
    WpNmrTimeUnit.MILLISECOND: 1.0,
    WpNmrTimeUnit.SECOND: 1000.0,
    WpNmrTimeUnit.MICROSECOND: 0.001,
}

# =============================================================================
# 科学计算常量
# =============================================================================

# 数学常量
PI: Final[float] = 3.141592653589793
E: Final[float] = 2.718281828459045
SQRT_2: Final[float] = 1.4142135623730951

# 数值稳定性常量
MACHINE_EPSILON: Final[float] = 2.220446049250313e-16
FLOAT32_EPS: Final[float] = 1.1920929e-07
FLOAT64_EPS: Final[float] = 2.220446049250313e-16

# 收敛判据
DEFAULT_MAX_ITERATIONS: Final[int] = 1000
DEFAULT_CONVERGENCE_TOLERANCE: Final[float] = 1e-8
