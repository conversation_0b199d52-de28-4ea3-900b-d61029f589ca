"""数据集构建器 - 测试专用

提供流畅的API来构建各种类型的测试数据集。

Examples
--------
>>> # 快速创建连续型数据集
>>> dataset = DatasetBuilder.quick_continuous_dataset(
...     name="test_logs",
...     well_name="W-1",
...     depth_range=(2500, 2510),
...     curves={"GR": 50, "PHIT": 0.15}
... )
>>>
>>> # 使用构建器模式
>>> dataset = DatasetBuilder.continuous("logs") \
...     .with_wells(["W-1", "W-2"]) \
...     .with_depth_range(2500, 2510, 0.5) \
...     .with_curves({"GR": lambda d: 50 + d*0.01, "PHIT": 0.15}) \
...     .build()
"""

from __future__ import annotations

import pandas as pd
import numpy as np
from typing import Any, Callable

from logwp.models.datasets import WpContinuousDataset, WpDiscreteDataset, WpIntervalDataset
from logwp.models.curve import CurveMetadata, CurveBasicAttributes
from logwp.models.constants import WpDataType, WpCurveCategory, WpDepthUnit
from logwp.testing.utils.quick_generators import quick_continuous_df, quick_discrete_df, quick_interval_df, quick_metadata


class DatasetBuilder:
    """数据集构建器 - 测试专用。

    提供静态方法和构建器模式两种方式来创建测试数据集。
    """

    @staticmethod
    def quick_continuous_dataset(
        name: str,
        *,
        well_name: str = "W-1",
        depth_range: tuple[float, float] = (2500.0, 2510.0),
        interval: float = 0.5,
        curves: dict[str, Any] | None = None,
        depth_unit: str = WpDepthUnit.METER
    ) -> WpContinuousDataset:
        """快速创建连续型测试数据集。

        此方法现在支持直接传入由 `SyntheticCurveFactory` 生成的二维数组
        来创建组合曲线（例如 NMR T2 谱）。

        Args:
            name: 数据集名称
            well_name: 井名
            depth_range: 深度范围 (start, end)
            interval: 深度间隔
            curves: 曲线数据字典。值可以是标量、列表、lambda函数，或一个二维
                    NumPy数组（用于生成组合曲线，如T2谱）。
            depth_unit: 深度单位

        Returns:
            WpContinuousDataset: 创建的连续型数据集
        """
        if curves is None:
            curves = {"GR": 50.0, "PHIT": 0.15}

        n_points = int(round((depth_range[1] - depth_range[0]) / interval)) + 1

        # 1. 智能处理曲线数据并构建元数据
        processed_curves_for_df = {}
        metadata = CurveMetadata()
        metadata.add_standard_system_curves(
            dataset_type="continuous", depth_unit=depth_unit
        )

        for curve_name, value in curves.items():
            if isinstance(value, np.ndarray) and value.ndim == 2:
                # 处理二维组合曲线 (如 T2_DIST)
                if value.shape[0] != n_points:
                    raise ValueError(
                        f"二维曲线 '{curve_name}' 的深度点数 ({value.shape[0]}) "
                        f"与根据深度范围计算的点数 ({n_points}) 不匹配。"
                    )

                num_elements = value.shape[1]
                curve_attr = CurveBasicAttributes.create_2d_composite_curve(
                    name=curve_name, element_count=num_elements
                )
                metadata.add_curve(curve_attr)

                for i, col_name in enumerate(curve_attr.dataframe_element_names):
                    processed_curves_for_df[col_name] = value[:, i]
            else:
                # 处理一维曲线 (标量, 列表, lambda, 或一维数组)
                curve_attr = CurveBasicAttributes.create_1d_curve(name=curve_name)
                metadata.add_curve(curve_attr)
                processed_curves_for_df[curve_name] = value

        # 2. 生成DataFrame
        df = quick_continuous_df(
            well_name=well_name,
            start_depth=depth_range[0],
            end_depth=depth_range[1],
            interval=interval,
            **processed_curves_for_df,
        )

        # 3. 创建数据集
        return WpContinuousDataset.create_with_data(
            name=name,
            df=df,
            curve_metadata=metadata,
            depth_sampling_rate=interval
        )

    @staticmethod
    def quick_discrete_dataset(
        name: str,
        *,
        well_name: str = "W-1",
        depths: list[float] | None = None,
        curves: dict[str, Any] | None = None
    ) -> WpDiscreteDataset:
        """快速创建离散型测试数据集。

        Args:
            name: 数据集名称
            well_name: 井名
            depths: 深度列表
            curves: 曲线数据字典

        Returns:
            WpDiscreteDataset: 创建的离散型数据集
        """
        if depths is None:
            depths = [2500.5, 2502.3, 2505.1]
        if curves is None:
            curves = {"PERM": [12.5, 8.9, 15.2], "FACIES": ["砂岩", "泥岩", "砂岩"]}

        # 生成DataFrame
        df = quick_discrete_df(
            well_name=well_name,
            depths=depths,
            **curves
        )

        # 生成元数据
        curve_names = ["WELL", "MD"] + list(curves.keys())
        metadata = quick_metadata(*curve_names)

        # 创建数据集
        return WpDiscreteDataset.create_with_data(
            name=name,
            df=df,
            curve_metadata=metadata
        )

    @staticmethod
    def quick_interval_dataset(
        name: str,
        *,
        well_name: str = "W-1",
        intervals: list[tuple[float, float]] | None = None,
        curves: dict[str, Any] | None = None
    ) -> WpIntervalDataset:
        """快速创建区间型测试数据集。

        Args:
            name: 数据集名称
            well_name: 井名
            intervals: 区间列表 [(top, bottom), ...]
            curves: 曲线数据字典

        Returns:
            WpIntervalDataset: 创建的区间型数据集
        """
        if intervals is None:
            intervals = [(2500.0, 2505.0), (2505.0, 2510.0), (2510.0, 2515.0)]
        if curves is None:
            curves = {"FACIES": ["砂岩", "泥岩", "灰岩"], "PHIT_AVG": [0.15, 0.12, 0.18]}

        # 生成DataFrame
        df = quick_interval_df(
            well_name=well_name,
            intervals=intervals,
            **curves
        )

        # 生成元数据
        curve_names = ["WELL", "MD_Top", "MD_Bottom"] + list(curves.keys())
        metadata = quick_metadata(*curve_names)

        # 创建数据集
        return WpIntervalDataset.create_with_data(
            name=name,
            df=df,
            curve_metadata=metadata
        )

    @staticmethod
    def synthetic_nmr_dataset(
        name: str,
        *,
        well_name: str = "W-1",
        depth_range: tuple[float, float] = (2500.0, 2510.0),
        interval: float = 0.5,
        t2_bins: int = 32
    ) -> WpContinuousDataset:
        """创建合成NMR测试数据集。

        Args:
            name: 数据集名称
            well_name: 井名
            depth_range: 深度范围
            interval: 深度间隔
            t2_bins: T2谱bin数量

        Returns:
            WpContinuousDataset: 包含NMR数据的连续型数据集
        """
        # 生成基础数据
        depths = np.arange(depth_range[0], depth_range[1] + interval, interval)
        n_points = len(depths)

        # 生成NMR相关曲线
        np.random.seed(42)  # 确保可重复性
        curves = {
            "PHIT_NMR": 0.15 + 0.05 * np.sin(depths * 0.1) + np.random.normal(0, 0.01, n_points),
            "T2LM": 100 + 50 * np.cos(depths * 0.05) + np.random.normal(0, 10, n_points),
            "T2_P50": 80 + 30 * np.sin(depths * 0.08) + np.random.normal(0, 5, n_points)
        }

        # 生成T2谱数据（二维组合曲线）
        for i in range(t2_bins):
            t2_value = 0.01 + i * 0.001 + np.random.normal(0, 0.0001, n_points)
            curves[f"T2_DIST_{i:02d}"] = t2_value

        return DatasetBuilder.quick_continuous_dataset(
            name=name,
            well_name=well_name,
            depth_range=depth_range,
            interval=interval,
            curves=curves
        )
