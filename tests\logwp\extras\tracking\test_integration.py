"""集成测试 - 验证tracking包的完整工作流。

测试RunContext和ModelRegistry的集成使用，模拟真实的SCAPE工作流场景。
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict

import pytest

from logwp.extras.tracking import (
    RunContext,
    ModelRegistry,
    BaseRunConfig,
)


class ScapeTestConfig(BaseRunConfig):
    """测试用的SCAPE配置模型。"""

    algorithm: str = "SWIFT-PSO"
    learning_rate: float = 0.01
    bootstrap_iterations: int = 20
    enable_validation: bool = True


class TestTrackingIntegration:
    """测试tracking包的集成功能。"""

    def test_complete_experiment_workflow(self, temp_dir: Path):
        """测试完整的实验工作流。"""
        # 准备配置
        config = ScapeTestConfig(
            project_name="SCAPE",
            run_name="integration_test_experiment",
            description="集成测试实验",
            tags=["test", "integration"],
            algorithm="FOSTER-NMR",
            learning_rate=0.005,
            bootstrap_iterations=25
        )

        run_dir = temp_dir / "test_run"
        registry_path = temp_dir / "model_registry.json"

        # 执行实验
        with RunContext(
            run_dir=run_dir,
            config=config.to_dict(),
            metadata={"git_commit": "test123", "user": "test_user"}
        ) as ctx:
            # 记录训练参数
            ctx.log_parameter("learning_rate", config.learning_rate)
            ctx.log_parameter("algorithm", config.algorithm)
            ctx.log_parameter("bootstrap_iterations", config.bootstrap_iterations)

            # 模拟训练步骤
            training_dir = ctx.get_step_dir("training")
            training_results = {
                "final_loss": 0.123,
                "convergence_iterations": 45
            }

            # 保存训练结果
            results_file = training_dir / "results.json"
            with open(results_file, "w") as f:
                json.dump(training_results, f)

            # 确保文件写入完成
            import time
            time.sleep(0.1)
            ctx.log_artifact(results_file, artifact_name="training/results.json", artifact_path="results.json")
            ctx.log_metrics(training_results, step_name="training")

            # 模拟验证步骤
            if config.enable_validation:
                validation_dir = ctx.get_step_dir("validation")
                validation_results = {
                    "spearman_rho": 0.88,
                    "rmse": 0.12
                }

                # 保存验证结果
                val_file = validation_dir / "validation.json"
                with open(val_file, "w") as f:
                    json.dump(validation_results, f)

                time.sleep(0.1)
                ctx.log_artifact(val_file, artifact_name="validation.json", artifact_path="validation.json")
                ctx.log_metrics(validation_results, step_name="validation")

            # 模拟模型文件
            model_dir = ctx.get_step_dir("models")
            model_data = {
                "model_type": config.algorithm,
                "parameters": {"learning_rate": config.learning_rate},
                "performance": training_results
            }

            import uuid
            unique_suffix = str(uuid.uuid4())[:8]
            model_file = model_dir / f"final_model_{unique_suffix}.json"
            with open(model_file, "w") as f:
                json.dump(model_data, f)

            time.sleep(0.1)  # 短暂延迟确保文件写入完成
            ctx.log_artifact(model_file, artifact_name="final_model.json", artifact_path="models/final_model.json")

            # 记录最终指标
            ctx.log_metrics({
                "experiment_success": True,
                "total_steps": 2 if config.enable_validation else 1
            })

        # 验证运行完成
        assert ctx.is_successful
        assert ctx.is_completed

        # 验证清单内容
        manifest = ctx.manifest
        assert manifest["status"] == "COMPLETED"
        assert "learning_rate" in manifest["parameters"]
        assert "training" in manifest["metrics"]
        assert "final_model.json" in manifest["artifacts"]

        if config.enable_validation:
            assert "validation" in manifest["metrics"]
            assert "validation.json" in manifest["artifacts"]

        # 检查训练结果产物
        assert "training/results.json" in manifest["artifacts"]

        # 模型注册
        registry = ModelRegistry(registry_path)

        # 注册模型
        model_info = registry.register_model(
            model_name="test-model",
            run_id=ctx.run_id,
            artifact_path="models/final_model.json",
            description=f"Model from {config.run_name}",
            metrics={
                "final_loss": training_results["final_loss"],
                "spearman_rho": validation_results.get("spearman_rho", 0.0)
            },
            tags=config.tags
        )

        # 验证模型注册
        assert model_info["name"] == "test-model"
        assert model_info["version"] == "1.0.0"
        assert model_info["source_run"]["run_id"] == ctx.run_id
        assert model_info["metrics"]["final_loss"] == 0.123

        # 验证可以加载运行
        loaded_ctx = RunContext.load(run_dir)
        assert loaded_ctx.run_id == ctx.run_id
        assert loaded_ctx.is_successful

        # 验证可以查询模型
        version_info = registry.get_model_version("test-model", "1.0.0")
        assert version_info is not None
        assert version_info["source_run"]["run_id"] == ctx.run_id

    def test_failed_experiment_handling(self, temp_dir: Path):
        """测试失败实验的处理。"""
        run_dir = temp_dir / "failed_run"

        # 模拟实验失败
        with pytest.raises(ValueError):
            with RunContext(run_dir=run_dir) as ctx:
                ctx.log_parameter("test_param", "value")
                ctx.log_metrics({"initial_metric": 1.0})

                # 模拟实验中的错误
                raise ValueError("Simulated experiment failure")

        # 验证失败状态
        assert not ctx.is_successful
        assert ctx.is_completed
        assert ctx.manifest["status"] == "FAILED"
        assert "ValueError" in ctx.manifest["error_message"]

        # 验证参数和指标仍然被记录
        assert ctx.parameters["test_param"] == "value"
        assert ctx.metrics["summary"]["initial_metric"] == 1.0

    def test_model_lifecycle_management(self, temp_dir: Path):
        """测试模型生命周期管理。"""
        registry_path = temp_dir / "lifecycle_registry.json"
        registry = ModelRegistry(registry_path)

        # 注册多个版本
        v1_info = registry.register_model(
            model_name="lifecycle-model",
            run_id="run-001",
            artifact_path="models/v1.pkl",
            stage="Development"
        )

        v2_info = registry.register_model(
            model_name="lifecycle-model",
            run_id="run-002",
            artifact_path="models/v2.pkl",
            stage="Staging"
        )

        v3_info = registry.register_model(
            model_name="lifecycle-model",
            run_id="run-003",
            artifact_path="models/v3.pkl",
            stage="Staging"
        )

        # 验证版本递增
        assert v1_info["version"] == "1.0.0"
        assert v2_info["version"] == "2.0.0"
        assert v3_info["version"] == "3.0.0"

        # 提升最佳版本到生产
        registry.transition_stage("lifecycle-model", "3.0.0", "Production")

        # 验证阶段查询
        prod_versions = registry.get_latest_versions(
            "lifecycle-model",
            stages=["Production"]
        )
        assert len(prod_versions) == 1
        assert prod_versions[0]["version"] == "3.0.0"

        staging_versions = registry.get_latest_versions(
            "lifecycle-model",
            stages=["Staging"]
        )
        assert len(staging_versions) == 1
        assert staging_versions[0]["version"] == "2.0.0"

        # 验证模型摘要
        summary = registry.get_model_summary("lifecycle-model")
        assert summary["total_versions"] == 3
        assert summary["stage_counts"]["Development"] == 1
        assert summary["stage_counts"]["Staging"] == 1
        assert summary["stage_counts"]["Production"] == 1

    def test_artifact_management(self, temp_dir: Path):
        """测试产物管理功能。"""
        run_dir = temp_dir / "artifact_test"

        with RunContext(run_dir=run_dir) as ctx:
            # 创建多种类型的产物
            artifacts = {}

            # 模型文件
            model_file = temp_dir / "test_model.pkl"
            model_file.write_text("mock model data")
            ctx.log_artifact(model_file, artifact_name="final_model.pkl", artifact_path="models/final_model.pkl")
            artifacts["final_model.pkl"] = "models/final_model.pkl"

            # 配置文件
            config_file = temp_dir / "config.yaml"
            config_file.write_text("mock config data")
            ctx.log_artifact(config_file, artifact_name="experiment.yaml", artifact_path="config/experiment.yaml")
            artifacts["experiment.yaml"] = "config/experiment.yaml"

            # 结果文件
            results_file = temp_dir / "results.json"
            results_file.write_text('{"accuracy": 0.95}')
            ctx.log_artifact(results_file, artifact_name="results.json")  # 使用默认路径
            artifacts["results.json"] = "results.json"

        # 验证所有产物都被注册
        for artifact_name, expected_path in artifacts.items():
            assert artifact_name in ctx.artifacts
            artifact_info = ctx.artifacts[artifact_name]
            assert artifact_info["path"] == expected_path

            # 验证文件确实存在
            actual_path = ctx.get_artifact_path(artifact_name)
            assert actual_path.exists()

        # 验证产物类型推断
        assert ctx.artifacts["final_model.pkl"]["type"] == "model"
        assert ctx.artifacts["experiment.yaml"]["type"] == "config"
        assert ctx.artifacts["results.json"]["type"] == "data"

    def test_step_directory_organization(self, temp_dir: Path):
        """测试步骤目录组织。"""
        run_dir = temp_dir / "step_test"

        with RunContext(run_dir=run_dir) as ctx:
            # 创建多个步骤目录
            steps = ["data_preprocessing", "model_training", "validation", "evaluation"]

            step_dirs = {}
            for step in steps:
                step_dir = ctx.get_step_dir(step)
                step_dirs[step] = step_dir

                # 在每个步骤目录中创建文件
                step_file = step_dir / f"{step}_output.txt"
                step_file.write_text(f"Output from {step}")

                # 记录步骤指标
                ctx.log_metrics({f"{step}_completed": True}, step_name=step)

        # 验证目录结构
        for step, step_dir in step_dirs.items():
            assert step_dir.exists()
            assert step_dir.is_dir()
            assert step_dir.parent == run_dir

            # 验证步骤文件存在
            step_file = step_dir / f"{step}_output.txt"
            assert step_file.exists()

        # 验证步骤指标
        for step in steps:
            assert step in ctx.metrics
            assert ctx.metrics[step][f"{step}_completed"] is True
