"""scape.core.swift_pso.internal.pso_check_early_stopping - PSO早停检查逻辑

实现PSO优化过程中的早停判断，支持Bootstrap+LOWO和Fine-Tuning两种模式。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部早停检查
设计原则: 纯函数、双重条件判断、模式自适应
性能特征: NumPy优化、滑动窗口、数值稳定性

遵循CCG规范：
- SC-1: 算法正确性
- SC-2: 数值稳定性
- TS-1: 完整类型注解

References
----------
- 《SCAPE_MS_方法说明书》§4.3 - 早停策略定义
- 迁移自 scape/core/swift_pso_backup/internal/pso_check_early_stopping.py
"""

from __future__ import annotations

from typing import Optional

import numpy as np


def check_early_stopping(
    loss_history: Optional[np.ndarray],
    current_iteration: int,
    mode: str = 'lowo'
) -> bool:
    """根据历史损失和当前迭代次数，判断是否满足早停条件。

    实现基于滑动窗口的双重条件早停策略：
    1. 相对改善率 (RI) 条件：连续窗口的相对改善率低于阈值
    2. 绝对下降量条件：连续窗口的绝对下降量低于动态阈值

    Args:
        loss_history: 从开始到现在的损失值历史记录
        current_iteration: 当前的迭代次数
        mode: 判断模式，'lowo' (外松) 或 'finetune' (内紧)

    Returns:
        bool: 如果满足早停条件，则返回 True，否则返回 False

    Raises:
        ValueError: mode参数无效时抛出

    References:
        《SCAPE_MS_方法说明书》§4.3 - 早停策略定义
    """
    if loss_history is None or len(loss_history) == 0:
        return False

    # 1. 根据模式选择判断参数
    if mode == 'lowo':
        # Bootstrap+LOWO阶段的"外松"策略
        window_length = 10         # W
        consecutive_windows = 5    # K
        ri_threshold = 0.015       # 1.5%
        dyn_thresh_factor = 0.05
        abs_floor = 5e-4
    elif mode == 'finetune':
        # Fine-Tuning阶段的"内紧"策略
        window_length = 5          # W_FT
        consecutive_windows = 4    # K_FT
        ri_threshold = 0.008       # 0.8%
        dyn_thresh_factor = 0.03
        abs_floor = 2e-4
    else:
        raise ValueError("mode 必须是 'lowo' 或 'finetune'")

    # 2. 检查是否达到可判断的最小迭代次数
    min_iterations = window_length * consecutive_windows
    if current_iteration < min_iterations:
        return False

    # 3. 检查最近K个窗口是否"连续"满足停止条件
    for k in range(consecutive_windows):
        # 定义当前窗口和上一个窗口的索引范围
        # k=0 是最近的窗口, k=1是次近的, ...
        end_idx_current = len(loss_history) - (k * window_length)
        start_idx_current = end_idx_current - window_length

        end_idx_prev = start_idx_current
        start_idx_prev = end_idx_prev - window_length

        if start_idx_prev < 0:
            # 历史记录不足以形成更早的窗口
            return False

        # 计算窗口平均损失
        loss_current_window = np.mean(loss_history[start_idx_current:end_idx_current])
        loss_prev_window = np.mean(loss_history[start_idx_prev:end_idx_prev])

        # 计算绝对下降量 (delta_L) 和相对改善率 (RI)
        absolute_drop = max(0, loss_prev_window - loss_current_window)
        relative_improvement = absolute_drop / loss_prev_window if loss_prev_window > 1e-9 else 0

        # 计算动态停止阈值
        dynamic_threshold = dyn_thresh_factor * loss_current_window
        stop_threshold = max(dynamic_threshold, abs_floor)

        # 判断当前窗口是否满足双重条件
        is_stagnant = (relative_improvement <= ri_threshold) and \
                      (absolute_drop <= stop_threshold)

        if not is_stagnant:
            # 只要有一个窗口不满足，则立即返回False
            return False

    # 如果所有K个窗口都满足了停滞条件，则返回True
    return True
