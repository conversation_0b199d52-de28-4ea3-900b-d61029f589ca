"""tests.scape.core.swift_pso.conftest - SWIFT-PSO测试固件

定义SWIFT-PSO模块所有测试共享的pytest固件。

Architecture
------------
层次/依赖: tests/scape/core/swift_pso层，测试固件
设计原则: 固件复用、测试隔离、数据预加载
性能特征: 会话级数据加载、函数级上下文隔离

References
----------
- `docs/DDS/refactor/swift_pso重构.md` §5.3 - 集成测试详细设计
"""

from __future__ import annotations

from pathlib import Path

import pytest
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.models import WpWellProject
from logwp.io import WpExcelReader

TEST_DATA_ROOT = Path(__file__).parent.parent.parent.parent / "test_data"


@pytest.fixture(scope="function")
def run_context(tmp_path: Path) -> RunContext:
    """提供一个临时的、隔离的RunContext实例，用于测试。"""
    run_dir = tmp_path / "test_run"
    run_dir.mkdir()
    with RunContext(run_dir=run_dir) as ctx:
        yield ctx


@pytest.fixture(scope="session")
def swift_pso_train_bundle() -> WpDataFrameBundle:
    """从真实的测试数据文件中加载SWIFT-PSO训练数据。"""
    data_file_path = TEST_DATA_ROOT / "real" / "swift_pso" / "swift_pso_train.wp.xlsx"
    reader = WpExcelReader()
    project = reader.read(data_file_path)
    train_bundle = project.get_dataset("swift_pso").extract_curve_dataframe_bundle(
        curve_names=[
            'PHIT_NMR',
            'T2LM',
            'DT2_P50',
            'T2_P50',
            'DPHIT_NMR',
            'PHI_T2_DIST',  # T2谱分布的原始名称
            'K_LABEL',
            'K_LABEL_TYPE'
        ],
        rename_map={
            'PHI_T2_DIST': 'T2_VALUE'  # 映射为训练所需的名称
        },
        include_system_columns=True
    )
    return train_bundle


@pytest.fixture(scope="session")
def swift_pso_pred_bundle() -> WpDataFrameBundle:
    """从真实的测试数据文件中加载SWIFT-PSO预测数据。"""
    data_file_path = TEST_DATA_ROOT / "real" / "swift_pso" / "swift_pso_pred.wp.xlsx"
    reader = WpExcelReader()
    project = reader.read(data_file_path)
    # 注意：根据README，预测数据没有K_LABEL和K_LABEL_TYPE
    pred_bundle = project.get_dataset("swift_pso_pred").extract_curve_dataframe_bundle(
        curve_names=[
            'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',
            'PHI_T2_DIST',
        ],
        rename_map={
            'PHI_T2_DIST': 'T2_VALUE'
        },
        include_system_columns=True
    )
    return pred_bundle
