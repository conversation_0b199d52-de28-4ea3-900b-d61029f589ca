"""核心统计计算服务。

提供一个可重用的、无状态的函数，用于在pandas Series上计算单个统计量。
专供 `WpDepthIndexedDatasetBase` 和 `WpDataFrameBundle` 内部调用。

Architecture
------------
层次/依赖: models/datasets/internal服务层，无状态服务函数
设计原则: Service Layer模式, DRY (Don't Repeat Yourself)
"""

from __future__ import annotations

import pandas as pd
from logwp.models.exceptions import WpDataError, WpValidationError
from logwp.infra.exceptions import ErrorContext


def calculate_series_statistic(
    series: pd.Series,
    statistic: str,
    *,
    curve_name: str,
    dataset_name: str
) -> float:
    """
    在给定的pandas Series上计算指定的统计量。

    Args:
        series (pd.Series): 要计算统计量的数据序列。
        statistic (str): 要计算的统计量的名称。
            支持: 'mean', 'median', 'std', 'min', 'max', 'p25', 'p50', 'p75', 'p90' 等。
        curve_name (str): 正在处理的曲线名称，用于错误上下文。
        dataset_name (str): 正在处理的数据集名称，用于错误上下文。

    Returns:
        float: 计算出的统计值。

    Raises:
        WpValidationError: 如果统计量名称无效或不受支持。
        WpDataError: 如果数据无法用于计算（例如，非数值型或全部为NaN）。
    """
    # 1. 验证数据有效性
    if not pd.api.types.is_numeric_dtype(series):
        raise WpDataError(
            f"曲线 '{curve_name}' 不是数值类型，无法计算统计值。",
            context=ErrorContext(operation="calculate_series_statistic", dataset_name=dataset_name)
        )

    series_numeric = series.dropna()
    if series_numeric.empty:
        raise WpDataError(
            f"曲线 '{curve_name}' 没有有效的数值数据用于计算统计值。",
            context=ErrorContext(operation="calculate_series_statistic", dataset_name=dataset_name)
        )

    # 2. 根据名称计算统计量
    s_lower = statistic.lower()
    if s_lower == 'mean':
        return float(series_numeric.mean())
    elif s_lower == 'median' or s_lower == 'p50':
        return float(series_numeric.median())
    elif s_lower == 'std':
        return float(series_numeric.std())
    elif s_lower == 'min':
        return float(series_numeric.min())
    elif s_lower == 'max':
        return float(series_numeric.max())
    elif s_lower.startswith('p'):
        try:
            quantile = float(s_lower[1:]) / 100.0
            if not (0 <= quantile <= 1):
                raise ValueError("分位数必须在0到100之间。")
            return float(series_numeric.quantile(quantile))
        except (ValueError, IndexError) as e:
            raise WpValidationError(
                f"无效的分位数格式: '{statistic}'。应为 'p' 后跟0到100的数字，例如 'p25'、'p75'。",
                context=ErrorContext(operation="calculate_series_statistic", dataset_name=dataset_name)
            ) from e
    else:
        supported_statistics = ['mean', 'median', 'std', 'min', 'max', 'p<number>']
        raise WpValidationError(
            f"不支持的统计量: '{statistic}'。",
            context=ErrorContext(
                operation="calculate_series_statistic",
                dataset_name=dataset_name,
                additional_info={"supported_statistics": supported_statistics}
            )
        )
