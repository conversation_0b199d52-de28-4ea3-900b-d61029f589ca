from __future__ import annotations

from pydantic import BaseModel, Field, PositiveInt, conint, confloat, conlist


class DnnTrainingConfig(BaseModel):
    """
    Hybrid DNN基准模型训练步骤的流程控制配置。

    该配置模型不定义超参数的搜索空间（由Optuna在代码中动态定义），
    而是控制整个训练和寻优流程的行为。
    """

    # Reproducibility
    random_seed: int = Field(2025, description="用于所有随机操作的全局种子，确保可复现性")

    # Optuna Hyperparameter Tuning
    n_trials: PositiveInt = Field(
        100, description="Optuna超参数搜索的总试验次数"
    )

    # Training Loop Control
    max_epochs_per_trial: PositiveInt = Field(
        50, description="在超参数寻优的单次试验中，模型训练的最大轮次"
    )
    final_train_epochs: PositiveInt = Field(
        150, description="在找到最佳超参数后，最终模型训练的最大轮次"
    )
    patience: PositiveInt = Field(
        10,
        description="早停机制的耐心轮次数。如果在patience个轮次内验证集性能没有提升，则停止训练。",
    )
    batch_size: conint(gt=0) = Field(
        64, description="训练和验证时使用的批处理大小"
    )

    # Cross-Validation Data Split
    val_split_ratio: confloat(gt=0, lt=1) = Field(
        0.2, description="在每个LOWO-CV折内部，用于从训练集中划分出验证集的比例"
    )

class DnnPredictionConfig(BaseModel):
    """
    Hybrid DNN基准模型预测步骤的配置。

    目前为空，为未来可能增加的预测时参数（如推理批处理大小）保留扩展性。
    """

    pass
