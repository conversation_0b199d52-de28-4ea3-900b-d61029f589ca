## Metadata
- **Version:** 4.1
- **Date:** 2025‑07‑02
- **Maintainer:** SCAPE Core Team

## 🚨 规则优先级声明

本文件为 **SCAPE（Santos Carbonate Adaptive Permeability Estimator）** 项目开发的核心规则文件。

**强制要求**：
- **开发前必读**：所有开发工作前必须阅读本规则和项目核心文档
- **严格遵守**：编写、重构或生成任何代码/文档时必须遵守本文规则
- **最高优先级**：本文件规则为最高等级，与其他文档冲突时以本文为准

**适用对象**：所有人类开发者、AI编程助手和AI开发工具

## 🎯 SCAPE项目开发指导原则

### 项目定位
SCAPE是一个**内部科学研究项目**，专注于碳酸盐岩储层渗透率预测的机器学习算法研究与验证。

### 核心特点
- **研究导向**：主要用于算法验证、实验复现和科学研究，不涉及生产部署
- **内部协作**：团队规模小，成员间直接沟通，无需复杂的工程流程
- **快速迭代**：算法和数据处理方法需要频繁调整和优化
- **数据敏感**：处理石油工程敏感数据，仅限内部使用

### 设计原则
1. **科学研究优先**：架构设计服务于算法研究和实验验证
2. **实用主义**：避免过度工程化，重点解决实际问题
3. **内部协作**：简化流程，提高团队协作效率
4. **持续改进**：支持快速迭代和算法优化

### 开发重点（必须关注）
- ✅ **核心算法实现**：OBMIQ、FOSTER-NMR、SWIFT-PSO等算法的准确性和性能
- ✅ **GPU计算加速**：利用CUDA/OpenCL实现高性能并行计算，10-15倍性能提升
- ✅ **类型安全保证**：Protocol、TypedDict、严格类型检查保证代码质量
- ✅ **异步处理优化**：大文件I/O和并发计算的异步优化
- ✅ **数据处理能力**：多源测井数据的统一建模和高效处理
- ✅ **实验复现性**：确保研究结果可重复和可验证
- ✅ **现代化工具链**：ruff、structlog、pydantic等现代Python生态

### 可以简化（不必过度关注）
- ⚠️ **测试覆盖率**：重点测试核心算法，GPU代码适度覆盖（目标75%而非90%+）
- ⚠️ **CI/CD流程**：使用ruff自动化代码质量检查，无需复杂的自动化流程
- ⚠️ **部署考虑**：无需考虑生产环境部署和运维
- ⚠️ **版本管理**：简单的标签管理，无需严格的版本治理
- ⚠️ **文档完整性**：重点文档化核心设计，细节可以简化

### AMD原子模块开发模式
SCAPE采用**Atomic Module Development (AMD)**：以功能完整的子模块作为原子开发单位，避免过细粒度的增量开发。

#### 开发阶段划分原则
- **包级划分**：先按架构包进行阶段划分（logwp → logwp_extras → scape/core → scape/study → scape_case）
- **子模块划分**：在每个包内部，按功能相对独立的子模块进行划分
- **顺序执行**：按依赖关系确定开发顺序，逐个完成

#### 子模块开发流程
1. **基础设施** → 2. **完整实现** → 3. **模块测试** → 4. **集成测试**

#### 项目整体开发
- **分层推进**：按依赖关系逐层完成子模块（logwp → logwp_extras → scape/core → scape/study → scape_case）
- **迭代重构**：上层模块使用中发现问题时，可以重构下层模块
- **渐进集成**：每完成一层进行跨层集成测试
- **持续优化**：根据算法研究需要持续改进和优化

#### 核心规则
- **原子单位**：以功能相对独立的子模块为开发单位
- **完整性优先**：子模块功能100%完成后才测试
- **避免瞎测试**：不测试功能不完整的实现
- **粒度适中**：原子单位不能太细，要有独立的业务价值
- **支持重构**：上层使用中发现问题可以重构下层模块

#### 适用原因
- Protocol/TypedDict/常量/异常/配置/日志等基础设施需要完整实现才有意义
- 功能完整的模块更容易测试和调试

### 现代化代码质量要求
- **统一工具链**：使用ruff进行代码格式化、导入排序、代码检查
- **严格类型检查**：mypy严格模式，支持Protocol和TypedDict
- **结构化日志**：structlog JSON格式，便于分析和监控
- **GPU性能测试**：验证GPU加速效果和CPU回退机制
- **异常处理现代化**：Exception Groups和结构化异常信息

### 现代化技术栈选型（强制要求）

#### Python版本要求
- **Python 3.11+**：必须使用Python 3.11或更高版本，以支持Exception Groups、性能优化等现代特性

#### 核心现代化技术栈
- **ruff>=0.1.8**：统一代码质量工具，替代black+isort+flake8+pylint组合
- **structlog>=23.0.0**：结构化日志框架，JSON格式便于分析和监控
- **pydantic>=2.5.0**：数据验证与序列化，性能提升5-50倍
- **pydantic-settings>=2.1.0**：现代配置管理，支持环境变量覆盖
- **mypy>=1.8.0**：严格类型检查，支持Protocol和TypedDict

#### 依赖管理现代化
- **pyproject.toml**：使用现代依赖管理，替代requirements.txt
- **可选依赖分组**：
  - `scape[ml]`：机器学习组件（scikit-learn, xgboost, optuna）
  - `scape[gpu]`：GPU计算支持（cupy, cudf, numba）
  - `scape[viz]`：可视化组件（plotly, matplotlib）
  - `scape[dev]`：开发工具（ruff, mypy, pytest）

#### GPU计算集成
- **cupy>=12.0.0**：GPU加速的numpy替代，无缝切换CPU/GPU计算
- **cudf>=23.12.0**：GPU加速的pandas替代，大数据处理性能提升
- **numba>=0.58.0**：JIT编译，支持CUDA kernel编写
- **自动回退机制**：GPU不可用时自动切换到CPU计算

#### 现代Python特性要求
- **Exception Groups (PEP 654)**：批量异常处理，提升错误诊断能力
- **Protocol和TypedDict**：类型安全的接口定义和数据结构
- **functools.singledispatch**：替代传统Registry模式
- **异步I/O**：大文件处理使用异步操作

#### 配置管理现代化
- **TOML优先**：配置文件优先使用TOML格式，YAML作为兼容选项
- **环境变量支持**：通过pydantic-settings支持环境变量覆盖
- **类型安全配置**：所有配置使用pydantic BaseModel定义

#### 禁用的老技术
- ❌ **black, isort, flake8, pylint**：已被ruff替代
- ❌ **loguru**：使用structlog替代
- ❌ **requirements.txt**：使用pyproject.toml替代
- ❌ **Ray分布式计算**：使用joblib+GPU并行替代
- ❌ **xlsxwriter**：使用openpyxl统一Excel处理

---

## 项目核心文档命名及目录存放规范

- 项目核心文档全部放在`/docs`目录下

### SCAPE方法文档
- **方法说明书/MS**：`SCAPE_MS_方法说明书.md` - SCAPE项目目标和科学定义，所有实现的基准

### wp测井数据格式规范文档
- **WP文件规范/WFS**：`SCAPE_WFS_WP文件规范.md` - WP数据文件格式规范、LAS转换规则

### 架构设计文档
- **软件架构设计/SAD**：在`/docs/SAD`目录下，文件名以`SCAPE_SAD_*.md`命名，其中`SCAPE_SAD_logwp.md` - 为logwp包的详细设计提供背景，包括系统整体架构、设计原则、技术选型
- **logwp详细设计/DDS**：在`/docs/DDS`目录下， 包含各个具体子模块的详细设计，文件名以`SCAPE_DDS_logwp_*.md`命名
- **设计文档**：上述SAD和DDS文档的统称

### 编码规范文档
- **编码与通用规范/CCG**：在`/docs/coding`目录下，文件名以`SCAPE_CCG_*.md`命名，`SCAPE_CCG_编码与通用规范.md` - 代码风格、命名约定、异常处理等
- **API文档注释规范/ADG**：在`/docs/coding`目录下，文件名以`SCAPE_ADG_*.md`命名，`SCAPE_ADG_API文档注释规范.md` - API文档和代码注释规范

### 测试规范文档
- **软件测试指南/STG**：在`/docs/STG`目录下，文件名以`SCAPE_STG_*.md`命名，`SCAPE_STG_软件测试指南.md` - 测试策略、用例设计、执行规范

### 其它文档
- 位于`/docs/misc`目录下，但不属于上述分类的其它文档

**使用说明**：
- 在代码、注释、文档中应使用统一简称进行引用
- 在编写或生成文档时，请按照类别存放入相应的目录下
- 上述文档的文件名中如果出现`老版本`字样，表示该文档为历史版本，其中的内容与代码库当前状态可能有不一致的现象，请以代码库为准

## logwp测井数据模型格式无关架构设计核心规则 (Format-Agnostic Principle, FAP)

### 架构设计原则
- **FAP-1 格式无关原则**：logwp不绑定任何特定文件格式
  - WpWellProject ≠ WP文件直接映射，WpHead ≠ _Head_Info工作表直接映射
  - 支持WP、LAS、JSON、HDF5等多种格式

- **FAP-2 四层架构分离**：应用层→models层→I/O层→存储层
  - models层：格式无关业务模型
  - I/O层：格式特定解析器
  - 严格分层，禁止跨层依赖

- **FAP-3 WFS规范定位**：设计模板而非实现约束
  - 借鉴最佳实践，不受格式细节约束
  - WP是主要格式但非唯一格式

### 设计实施规则
- **FAP-4 业务优先顺序**：业务需求→代码模型→格式支持
  - 先理解业务本质，再设计模型，最后考虑格式

- **FAP-5 抽象具体分离**：业务模型与格式解析器严格解耦
  - 通过依赖注入和适配器模式实现

- **FAP-6 多源融合设计**：从WFS、LAS、DataFrame等学习最佳实践

### 扩展性规则
- **FAP-7 格式扩展**：新格式只需添加I/O层解析器，models层不变

- **FAP-8 属性扩展**：通过WpHead元数据系统扩展，保持向后兼容

### 验证规则
- **FAP-9 格式独立验证**：业务逻辑禁止包含格式特定代码

- **FAP-10 关系对应理解**：WpHead与_head_info = WpWellProject与WP文件
  - 都体现格式无关模型与具体格式的关系

### 数据模型职责边界
- **FAP-11 物理约束分离**：logwp数据模型层不实现物理约束验证
  - **设计原理**：数据模型保持表达的广泛性，支持各种数据状态和用途
  - **职责分工**：物理约束验证属于scape算法层的业务逻辑
  - **执行时机**：仅在用户执行特定算法任务时根据需要进行物理约束验证
  - **架构优势**：避免数据加载时的过度约束，支持数据预处理、清洗、转换等多种用途

### 导入规范边界
- **FAP-12 导入层次规范**：严格按照四层架构分离进行导入
  - **I/O层常量**：`WpXlsxKey`、`WpFileFormat`仅从`logwp.io.constants`导入
  - **业务模型常量**：`WpDsType`、`WpApiKeys`等从`logwp.constants`导入
  - **禁止跨层导入**：models层不得导入I/O层常量，datasets层不得导入格式特定常量
  - **导入清理原则**：移除未使用的导入，避免依赖冗余和循环依赖风险

## 大小写不敏感标识符架构规则 (Case-Insensitive Identifier Architecture, CIIA)

### 核心原则
- **CIIA-1 显示索引分离**：原始格式用于显示，规范化格式用于比较和索引
- **CIIA-2 Unicode标准化**：采用Unicode NFKC归一化 + Case Folding，优于简单大写转换
- **CIIA-3 冲突检测**：同一作用域内规范化后相同的标识符必须报错

### 实施规则
- **CIIA-4 透明集成**：现有API保持不变，内部透明处理大小写不敏感
- **CIIA-5 类型安全**：使用WpIdentifier封装标识符，提供编译时类型检查
- **CIIA-6 格式无关**：标识符处理完全独立于具体数据格式

### 应用范围
- **CIIA-7 全覆盖**：井名、数据集名、曲线名、属性名等所有标识符
- **CIIA-8 容器支持**：CaseInsensitiveDict/Set等容器类支持大小写不敏感操作

---

## AI编程助手工作原则

### 准备阶段
- **AP-1 文档优先**：浏览docs/目录核心文档，理解项目目标、架构和实现方式
- **AP-2 代码库理解**：全面了解当前代码库状态和结构

### 任务执行
#### 需求分析
- **RA-1 用户视角**：站在用户角度理解真实需求
- **RA-2 需求补全**：作为产品经理识别需求缺漏，与用户探讨完善

#### 代码开发
- **CD-1 规划先行**：分析需求与现有代码库，制定实施计划
- **CD-2 架构设计**：遵循SOLID原则，使用适当设计模式
- **CD-3 全局分析原则**：在进行重构或修改代码时，禁止仅通过字符串搜索定位问题，必须从全局架构出发深入分析
  - 先理解整个代码库的架构和设计原则
  - 使用codebase-retrieval工具进行语义分析而非简单字符串匹配
  - 从数据结构、API接口、算法逻辑等多个层面系统性检查
  - 避免表面修复，确保架构一致性和设计原则遵循

#### 问题解决
- **PS-1 全面分析**：完整理解代码库功能和逻辑
- **PS-2 根因定位**：分析错误原因，提出解决思路
- **PS-3 迭代优化**：多轮交互调整方案直至用户满意

### 质量保证
- **QA-1 任务反思**：完成后反思步骤和结果，识别改进点
- **QA-2 开发记录**：按用户指示使用开发计划和日志文件记录过程

### Hello
- 当用户说：`Hello`，表明要开始一个新的对话，这时你应该阅读本文件，了解核心规则；同时阅读项目核心文档，了解架构和设计；最后全面分析整个代码库，了解代码库现状。
- 此后，所有回答用户问题和编写代码，在遵守项目核心文档基础之上，最重要的是要从代码库的实际情况出发，杜绝臆测行事，不慌不忙，一步一步稳健执行任务。
- 当准备就绪后就回答：`一切就绪！`

---

## BDR: 蓝图偏差处理规则

 适用范围
   AI编程助手遇到 SCAPE_MS、SCAPE_SAD、各类 SCAPE_DDS 等蓝图文档与实际编码或运行结果存在不一致、且影响当前任务推进时生效。

 核心原则
   1. 任务优先：始终以“代码可运行、实验结果正确”为最高准则。
   2. 实际为准：AI 编程助手应先按「现实需求」完成代码，即使这暂时偏离蓝图设计。
   3. 必须留痕：任何与蓝图不一致的实现都必须记录，方便后续审阅与文档同步。

 操作流程
   A. 发现偏差
      • 当执行编程任务时，经过全面思考与评估，发现实际情况需要做出与蓝图所定义的 API、流程或公式不同的实现策略时
      • 规范要求与依赖版本冲突
      • 运行结果与蓝图承诺的数值范围不符

   B. 立即处理
      • 必须先征求用户意见，得到明确同意后，
        方可按实际需求修改或新增代码以保证任务完成

   C. 记录偏差
      • 在根目录 `BP_DEV_LOG.md` 顶部追加条目
      • 日志模板：
         偏差记录 YYYY-MM-DD HH:MM
        - 蓝图来源: MS/SAD/DDS + 章节号
        - 影响模块: path/to/file.py
        - 不一致描述:
          > 简述发现的冲突
        - 临时实现:
          > 说明已采取的实际代码方案
        - 后续建议:
          - [ ] 更新蓝图文档
          - [ ] 增补测试 / 性能验证

   D. 提醒维护
      • 记录完成后继续编程任务
      • 维护者在每周审阅时决定同步蓝图或调整代码

 日志规范
   • `BP_DEV_LOG.md` 按“最新记录在最上方”方式追加


---

## SCAPE 编码规范要点（基于 CCG v5.0）

### 代码风格规范
- **CS-1 命名约定**：包名全小写下划线分隔，类名PascalCase，函数snake_case，常量UPPER_SNAKE_CASE
- **CS-2 包前缀**：logwp包用`Wp`前缀，scape/core用`Scape`前缀，scape/study用`Study`前缀
- **CS-3 工具链**：强制使用ruff进行代码质量检查和格式化，line-length=88

### 现代化类型系统
- **TS-1 类型注解覆盖**：所有函数、方法、类属性必须有完整类型注解
- **TS-2 Protocol接口**：使用`@runtime_checkable`的Protocol定义类型安全接口
- **TS-3 TypedDict数据**：结构化数据使用TypedDict，可选字段用NotRequired
- **TS-4 现代语法**：使用`from __future__ import annotations`和Python 3.11+联合类型语法

### 现代化异常处理
- **EH-1 Exception Groups**：批量异常处理使用Python 3.11+ Exception Groups
- **EH-2 结构化异常**：异常包含ErrorContext结构化上下文信息
- **EH-3 分层异常树**：WpError根异常，按功能域派生具体异常类
- **EH-4 异常链保持**：使用`raise ... from e`保持异常链完整性

### 结构化日志系统
- **LG-1 structlog配置**：使用structlog替代标准logging，JSON格式输出
- **LG-2 结构化信息**：日志包含operation、stage、dataset_name等结构化字段
- **LG-3 日志级别**：DEBUG(详细诊断)、INFO(关键操作)、WARNING(可恢复异常)、ERROR(严重错误)
- **LG-4 性能友好**：使用延迟格式化避免性能影响

### 数据验证规范
- **DV-1 pydantic v2模型**：使用BaseModel定义数据模型，ConfigDict配置验证行为
- **DV-2 字段验证**：使用Field定义约束，@field_validator自定义验证逻辑
- **DV-3 配置管理**：使用pydantic-settings管理环境变量和配置文件
- **DV-4 不可变模型**：关键数据模型使用frozen=True保证不可变性

### 常量管理规范
- **CT-1 分层枚举**：按功能域组织常量枚举类，使用包前缀命名
- **CT-2 禁止硬编码**：所有字符串键名、配置值必须定义为枚举常量
- **CT-3 类型安全**：枚举继承str/int等基础类型，保证类型一致性
- **CT-4 统一引用**：通过枚举常量引用，避免散落的硬编码字符串

### GPU计算规范
- **GP-1 自动检测回退**：GPU不可用时自动切换到CPU计算
- **GP-2 统一接口**：ComputeEngine提供CPU/GPU统一的计算接口
- **GP-3 条件导入**：GPU库使用延迟导入，避免依赖问题
- **GP-4 内存管理**：智能GPU内存分配和释放机制

### 性能优化要求
- **PF-1 内存控制**：使用adaptive_chunk_size自适应分块处理
- **PF-2 异步I/O**：大文件处理使用异步操作，避免阻塞
- **PF-3 性能监控**：关键函数使用@performance_monitor装饰器
- **PF-4 资源释放**：及时释放不需要的数据，避免内存泄漏

### 科学计算规范（SC）
- **SC-1 算法正确性**：核心算法实现必须包含数学公式注释和参考文献
- **SC-2 数值稳定性**：浮点计算必须考虑数值稳定性，使用适当的数值方法
- **SC-3 实验可复现**：随机种子必须可配置，确保实验结果可重复
- **SC-4 单位一致性**：物理量计算必须明确单位，避免单位转换错误

**完整规范请见 [SCAPE_CCG_编码与通用规范.md](docs/SCAPE_CCG_编码与通用规范.md) 文档**

---

## SCAPE项目 API文档注释核心规则 (基于ADG文档)

### API文档编写核心要求
- **ADG-R1**: 使用Google风格docstring，包含Args/Returns/Raises/Example/References
- **ADG-R2**: 所有公共API必须提供完整类型注解，使用Protocol/TypedDict/Literal
- **ADG-R3**: 核心算法必须包含数学公式、参数说明和《SCAPE_MS》章节引用
- **ADG-R4**: 异常类必须详细说明触发条件、处理建议和示例代码

### AI编程助手核心规则
- **ADG-R9 杜绝臆测原则**: 严禁臆测API参数名、类型、常量名、异常参数等
  - 必须使用codebase-retrieval确认API定义
  - 必须使用view工具查看实际实现
  - 禁止假设，一切以实际代码为准
- **ADG-R10**: 执行完整API探查流程：架构定位→类型确认→常量验证→异常了解→代码查看
- **ADG-R11**: 生成代码必须遵循：现代类型注解、常量枚举、架构分层、异常规范

### 代码注释核心要求
- **ADG-R5**: 模块级docstring必须说明架构位置、依赖关系、主要功能
- **ADG-R6**: 复杂算法必须包含详细行内注释，解释每个步骤的数学原理
- **ADG-R8**: 业务逻辑必须解释业务规则和决策依据，引用相关规范

### 架构和技术栈要求
- **ADG-R13**: API文档必须明确标识架构层次(logwp→logwp_extras→scape/core→scape/study→scape_case)
- **ADG-R15**: 充分利用Python 3.11+特性：Protocol、TypedDict、Literal、异常组
- **ADG-R16**: 支持异步编程，大文件I/O使用async/await
- **ADG-R17**: 支持GPU加速，自动回退CPU实现

### 质量检查核心项
- **ADG-C29**: 已严格遵循"杜绝臆测"原则
- **ADG-C30**: 已通过工具确认所有API定义
- **ADG-C1**: 包含完整Google风格docstring
- **ADG-C6**: 算法API包含数学公式和参数说明
- **ADG-C8**: 明确架构层次和依赖关系
- **ADG-C9**: 使用常量枚举，避免硬编码字符串

### 示例模板
```python
def foster_nmr_permeability(
    phit: np.ndarray,
    t2lm: np.ndarray,
    *,
    c_factor: float = 4.0
) -> np.ndarray:
    """FOSTER-NMR渗透率计算公式实现。

    实现经典FOSTER-NMR方程：K = C × (φ^α) × (T2LM^β) × CF

    Args:
        phit: 总孔隙度数组，范围[0, 1]
        t2lm: T2对数平均值数组，单位ms
        c_factor: 岩石系数C，影响绝对渗透率水平

    Returns:
        np.ndarray: 渗透率数组，单位mD

    References:
        《SCAPE_MS_方法说明书》第2.2节 - FOSTER-NMR方程
    """

---

## SCAPE软件测试规范要点（基于STG v2.0）

### 核心测试原则
- **算法正确性优先**：重点测试OBMIQ、FOSTER-NMR、SWIFT-PSO三大核心算法
- **最小可行测试**：只测试影响科学结果的核心功能，避免过度工程化

### 测试优先级
- **🔴 必须测试**：核心算法数学正确性、物理约束验证、数值稳定性
- **🟡 重要测试**：WP文件解析、GPU/CPU一致性、数据验证
- **🟢 可选测试**：性能提升、异常处理、用户界面

### 关键要求
- **STG-1**：使用"黄金数据"验证算法正确性
- **STG-2**：GPU/CPU计算结果相对误差<1e-6
- **STG-3**：物理约束验证（渗透率>0、孔隙度∈[0,1]）
- **STG-4**：不设覆盖率指标，关注功能正确性

### 测试数据使用规范
- **STG-D1 数据选择原则**：根据测试目的选择合适规模的测试数据
  - 单元测试：使用 `small_test.wp.xlsx` (快速执行)
  - 算法验证：使用 `obmiq_golden.wp.xlsx` (黄金标准)
  - 物理模型：使用 `foster_nmr_test.wp.xlsx` (边界条件)
  - 性能测试：使用 `gpu_consistency_*.wp.xlsx` (多规模)
- **STG-D2 数据路径规范**：测试中使用相对路径 `tests/test_data/generated/`
- **STG-D3 数据完整性**：测试前验证WP文件格式和数据完整性
- **STG-D4 预期结果**：算法测试必须对照TDD文档中的预期结果进行验证

**完整规范参见 [SCAPE_STG_软件测试指南.md](docs/SCAPE_STG_软件测试指南.md) 文档**

---

### 开发计划与日志控制（DPLOG）

#### 文档体系
- **主开发计划**：`DEV_PLAN.md` - 项目整体开发进度和里程碑
- **主开发进展日志**：`DEV_LOG.md` - 主要开发活动记录
- **临时开发计划**：`SUB_DEV_PLAN_<任务描述>.md` - 临时任务和短期计划
- **临时开发进展日志**：`SUB_DEV_LOG_<任务描述>.md` - 临时开发活动记录

#### 任务执行流程
- **DPLOG-1 任务识别**：收到"执行主开发任务"、"开始下一个任务"等指令时，读取主开发计划，定位首个未勾选`[ ]`的任务
- **DPLOG-2 任务声明**：编码前回复确认执行的任务编号与标题
- **DPLOG-3 代码实现**：实现必须100%符合项目核心文档要求
- **DPLOG-4 记录进展**：完成后在对应进展日志**顶部**追加记录，使用标准模板格式
- **DPLOG-5 更新计划**：将对应任务标记为`[x]`完成状态

#### 计划管理规则
- **DPLOG-6 增加主任务**：收到"增加主任务"、"插入新任务"等指令时，在指定位置插入新任务，保持`[ ]`未完成状态
- **DPLOG-7 创建临时计划**：收到"创建临时计划"、"新建子计划"等指令时，在指定目录创建临时计划和日志文件
- **DPLOG-8 计划外任务**：执行不在计划中的任务时，同样必须记录进展日志，标题标识为"计划外任务"或"临时修改"

#### 日志记录模板
```markdown
### 任务 [任务编号]: [任务标题]
- **完成时间**: YYYY-MM-DD HH:MM:SS TZ
- **工作摘要**:
  - ...
- **涉及文件**:
  - **创建**: `path/to/new_file.py`
  - **修改**: `path/to/modified_file.py`


---

## PDM项目文档元规范（Project Documentation Meta-rule）

### 适用范围
- **架构文档**、**详细设计**、**测试指南**、**用户运维指南**等所有技术文档

### 模板体系
| 文档类别 | 模板文件 | 简称 | 必备章节 |
|----------|----------|------|----------|
| 架构文档 | `PDM_Arch_架构文档模板.md` | PDM-Arch | 0–14 |
| 详细设计 | `PDM_DDS_详细设计文档模板.md` | PDM-DDS | 0–11 |
| 测试指南 | `PDM_STG_测试文档模板.md` | PDM-STG | 0–10 |
| 用户指南 | `PDM_UG_用户指南模板.md` | PDM-UG | 0–8 |

### 核心约束
- **强制使用模板**：新建文档必须复制对应模板，保留全部占位符标题
- **禁止删改结构**：不得删除或重排模板必备章节，可在末尾追加扩展章节
- **清理占位符**：提交前必须删除所有`TODO:`占位提示
- **版本同步**：文档修改必须更新版本号与日期

### 变更流程
- **模板调整**：需先提交PDM变更提案，经维护者批准后执行
- **自动检查**：CI自动验证模板一致性、章节完整性、版本更新
- **兼容性**：采用语义化版本，MAJOR版本表示向后不兼容调整

**完整规范参见 [PDM项目文档元规范.md](docs/PDM项目文档元规范.md) 文档**

---
