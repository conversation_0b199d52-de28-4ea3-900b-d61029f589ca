"""Internal plotting logic for the LogScout step.

This module contains functions for generating all visual artifacts, such as
heatmaps, clustermaps, pairplots, and target relationship plots. These
functions take data and a PlotProfile as input and return a matplotlib Figure
object, separating the plotting logic from the main workflow orchestration.
"""

from __future__ import annotations

from typing import TYPE_CHECKING

import matplotlib.pyplot as plt
import seaborn as sns

from logwp.extras.plotting import apply_profile

if TYPE_CHECKING:
    import pandas as pd
    from matplotlib.figure import Figure

    from logwp.extras.plotting import PlotProfile


def plot_heatmap(
    corr_matrix: pd.DataFrame, profile: PlotProfile, title: str
) -> Figure:
    """Generates a standard correlation heatmap.

    Args:
        corr_matrix: The correlation matrix to plot.
        profile: The PlotProfile object containing style configurations.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object containing the heatmap.
    """
    fig, ax = plt.subplots(**profile.figure_props)
    apply_profile(ax, profile)

    sns.heatmap(corr_matrix, ax=ax, **profile.artist_props)

    # Apply title properties from the profile
    title_props = profile.title_props.copy()
    title_props["label"] = title
    ax.set_title(**title_props)

    return fig


def plot_clustermap(
    corr_matrix: pd.DataFrame, profile: PlotProfile, title: str
) -> Figure:
    """Generates a clustered correlation heatmap (clustermap).

    Args:
        corr_matrix: The correlation matrix to plot.
        profile: The PlotProfile object containing style configurations.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object containing the clustermap.
    """
    # Clustermap creates its own figure. We must selectively pass arguments
    # from the PlotProfile to avoid sending unsupported keywords like 'dpi'.

    # Start with artist properties, which are passed to the underlying heatmap.
    clustermap_kws = profile.artist_props.copy()

    # Add supported figure-level properties. `clustermap` accepts `figsize`.
    if "figsize" in profile.figure_props:
        clustermap_kws["figsize"] = profile.figure_props["figsize"]
    g = sns.clustermap(corr_matrix, **clustermap_kws)

    # Apply title to the figure itself. The first argument to suptitle is the text.
    # We need to remove 'pad' as it's not a valid kwarg for suptitle.
    title_kws = profile.title_props.copy()
    title_kws.pop("pad", None)
    g.fig.suptitle(title, **title_kws)

    return g.figure


def plot_pairplot(
    df: pd.DataFrame, features: list[str], profile: PlotProfile, title: str
) -> Figure:
    """Generates a pairplot (scatterplot matrix) for the given features.

    Note:
        Unlike other plots, seaborn's pairplot creates its own Figure.
        Size control should be done via `height` and `aspect` parameters
        within the `artist_props` of the PlotProfile, not `figure_props`.

    Args:
        df: The input DataFrame containing the feature data.
        features: The list of feature columns to include in the pairplot.
        profile: The PlotProfile object for styling.
        title: The super-title for the entire plot grid.

    Returns:
        A matplotlib Figure object containing the pairplot.
    """
    g = sns.pairplot(df[features], **profile.artist_props)

    # Apply title to the figure itself. The first argument to suptitle is the text.
    # We need to remove 'pad' as it's not a valid kwarg for suptitle.
    title_kws = profile.title_props.copy()
    title_kws.pop("pad", None)
    g.figure.suptitle(title, **title_kws)

    return g.figure


def plot_regression_target(
    df: pd.DataFrame, feature: str, target: str, profile: PlotProfile, title: str
) -> Figure:
    """Generates a regression plot for a feature against a continuous target.

    Args:
        df: The input DataFrame containing the feature and target data.
        feature: The name of the input feature column (x-axis).
        target: The name of the continuous target column (y-axis).
        profile: The PlotProfile object for styling.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object containing the regression plot.
    """
    fig, ax = plt.subplots(**profile.figure_props)
    apply_profile(ax, profile)

    sns.regplot(data=df, x=feature, y=target, ax=ax, **profile.artist_props)

    # Apply title and labels from the profile
    title_props = profile.title_props.copy()
    title_props["label"] = title
    ax.set_title(**title_props)

    label_props = profile.label_props.copy()
    ax.set_xlabel(feature, **label_props)
    ax.set_ylabel(target, **label_props)

    return fig


def plot_classification_target(
    df: pd.DataFrame, feature: str, target: str, profile: PlotProfile, title: str
) -> Figure:
    """Generates a box plot for a feature grouped by a categorical target.

    Args:
        df: The input DataFrame containing the feature and target data.
        feature: The name of the input feature column (y-axis).
        target: The name of the categorical target column (x-axis).
        profile: The PlotProfile object for styling.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object containing the box plot.
    """
    fig, ax = plt.subplots(**profile.figure_props)
    apply_profile(ax, profile)

    # For boxplot, it's conventional to have the categorical variable on the x-axis
    sns.boxplot(data=df, x=target, y=feature, ax=ax, **profile.artist_props)

    # Apply title and labels from the profile
    title_props = profile.title_props.copy()
    title_props["label"] = title
    ax.set_title(**title_props)

    label_props = profile.label_props.copy()
    ax.set_xlabel(target, **label_props)
    ax.set_ylabel(feature, **label_props)

    return fig
