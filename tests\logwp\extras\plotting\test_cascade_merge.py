"""测试绘图配置注册表的层叠合并逻辑。"""

import pytest
from logwp.extras.plotting import registry as plot_registry
from scape.core.validation import PltAnalysisPlotProfiles


def test_base_profiles_have_save_config():
    """测试所有基础模板都有有效的 SaveConfig。"""
    base_profiles = plot_registry.list_base_profiles()
    assert len(base_profiles) > 0, "应该至少有一个基础模板"
    
    for base_name in base_profiles:
        base_profile = plot_registry.get_base(base_name)
        assert base_profile.save_config is not None, f"基础模板 {base_name} 应该有 SaveConfig"
        assert hasattr(base_profile.save_config, 'format'), f"基础模板 {base_name} 的 SaveConfig 应该有 format 属性"
        assert hasattr(base_profile.save_config, 'dpi'), f"基础模板 {base_name} 的 SaveConfig 应该有 dpi 属性"


def test_global_base_template():
    """测试全局 base 模板的配置。"""
    base_profiles = plot_registry.list_base_profiles()
    assert "base" in base_profiles, "应该有全局 base 模板"
    
    global_base = plot_registry.get_base("base")
    assert global_base.save_config is not None, "全局 base 模板应该有 SaveConfig"
    assert global_base.save_config.format == ["png"], "全局 base 模板的默认格式应该是 png"
    assert global_base.save_config.dpi == 150, "全局 base 模板的默认 DPI 应该是 150"


def test_cascade_merge_preserves_save_config():
    """测试层叠合并保持 SaveConfig 配置。"""
    # 使用 PLT 分析的 CAPTURE_CURVE 配置进行测试
    capture_name = PltAnalysisPlotProfiles.CAPTURE_CURVE
    
    # 获取原始配置（未合并）
    registry_internal = plot_registry._profiles
    assert capture_name in registry_internal, f"配置 {capture_name} 应该在注册表中"
    
    raw_profile = registry_internal[capture_name]
    
    # 获取合并后的配置
    merged_profile = plot_registry.get(capture_name)
    
    # 合并后的配置应该有 SaveConfig
    assert merged_profile.save_config is not None, "合并后的配置应该有 SaveConfig"
    
    # 如果原始配置没有 SaveConfig，应该从基础模板继承
    if raw_profile.save_config is None:
        # 应该从基础模板继承 SaveConfig
        global_base = plot_registry.get_base("base")
        assert merged_profile.save_config.format == global_base.save_config.format
        assert merged_profile.save_config.dpi == global_base.save_config.dpi
    else:
        # 如果原始配置有 SaveConfig，应该保持不变
        assert merged_profile.save_config.format == raw_profile.save_config.format
        assert merged_profile.save_config.dpi == raw_profile.save_config.dpi


def test_manual_cascade_merge():
    """测试手动执行层叠合并逻辑。"""
    capture_name = PltAnalysisPlotProfiles.CAPTURE_CURVE
    
    # 获取原始配置
    target_profile = plot_registry._profiles[capture_name]
    
    # 手动执行层叠合并
    result = plot_registry._perform_cascade_merge(target_profile)
    
    # 结果应该有 SaveConfig
    assert result.save_config is not None, "手动合并结果应该有 SaveConfig"
    assert hasattr(result.save_config, 'get_preferred_extension'), "SaveConfig 应该有 get_preferred_extension 方法"
    
    # 测试 get_preferred_extension 方法
    extension = result.save_config.get_preferred_extension()
    assert extension.startswith('.'), "扩展名应该以点开头"
    assert len(extension) > 1, "扩展名应该不为空"


def test_save_config_get_preferred_extension():
    """测试 SaveConfig 的 get_preferred_extension 方法。"""
    # 获取一个有 SaveConfig 的配置
    global_base = plot_registry.get_base("base")
    save_config = global_base.save_config
    
    # 测试方法存在
    assert hasattr(save_config, 'get_preferred_extension'), "SaveConfig 应该有 get_preferred_extension 方法"
    
    # 测试方法返回值
    extension = save_config.get_preferred_extension()
    assert isinstance(extension, str), "扩展名应该是字符串"
    assert extension.startswith('.'), "扩展名应该以点开头"
    
    # 对于默认的 png 格式
    assert extension == '.png', "默认扩展名应该是 .png"


def test_all_registered_profiles_have_save_config_after_merge():
    """测试所有注册的配置在合并后都有 SaveConfig。"""
    all_profiles = plot_registry.list_profiles()
    
    for profile_name in all_profiles:
        merged_profile = plot_registry.get(profile_name)
        assert merged_profile.save_config is not None, f"配置 {profile_name} 合并后应该有 SaveConfig"
        
        # 测试 SaveConfig 的基本属性
        save_config = merged_profile.save_config
        assert hasattr(save_config, 'format'), f"配置 {profile_name} 的 SaveConfig 应该有 format 属性"
        assert hasattr(save_config, 'dpi'), f"配置 {profile_name} 的 SaveConfig 应该有 dpi 属性"
        assert hasattr(save_config, 'get_preferred_extension'), f"配置 {profile_name} 的 SaveConfig 应该有 get_preferred_extension 方法"
