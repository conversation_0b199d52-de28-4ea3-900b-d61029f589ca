"""logwp.extras.nmr.internal.compute - NMR核心计算实现

NMR数据分析的核心计算算法实现，包括T2谱孔隙度划分等功能。

Architecture
------------
层次/依赖: NMR内部计算层，实现核心算法逻辑
设计原则: 数值稳定、性能优化、算法正确性
性能特征: 真正向量化计算、内存优化、数值稳定性

警告：
    此模块为内部实现，不对外暴露，仅供NMR包内部使用。

CRITICAL ALGORITHM CORRECTION (2025-07-13)
==========================================
**重大算法修正：T2谱孔隙度组分计算方法**

问题描述：
---------
原实现错误地使用积分方法（np.trapz）计算T2谱孔隙度组分，导致：
- 数值结果放大约100倍（因为乘以了时间间隔dx）
- 孔隙度组分超出物理范围[0,1]
- Vmicro + Vmeso + Vmacro ≠ PHIT_NMR

根本原因：
---------
T2谱数据的每个bin已经代表该T2范围内的孔隙度值（v/v单位），
不应该进行积分（∫ f(t) dt），而应该直接求和（Σ f[i]）。

修正方案：
---------
- 移除错误的积分函数：_integrate_porosity_component_agnostic()
- 实现正确的求和函数：_sum_porosity_component_agnostic()
- 算法变更：np.trapz(y=spectrum, x=t2_time) → np.sum(spectrum)

修正效果：
---------
- ✅ 数值结果正确：Vmicro + Vmeso + Vmacro = PHIT_NMR
- ✅ 物理范围合理：所有组分都在[0,1]范围内
- ✅ 代码简化：移除162行复杂积分代码
- ✅ 性能提升：求和比积分更高效

此修正影响所有使用T2谱孔隙度组分的算法，包括SWIFT-PSO渗透率预测。

Performance Improvements
-----------------------
- 真正的向量化求和：避免Python循环，使用numpy广播和矩阵运算
- 统一验证逻辑：消除重复代码，提取共享验证函数
- 简化计算逻辑：直接求和替代复杂积分，提升性能和可读性
- 数值稳定性：避免积分误差累积，确保计算精度
"""

from __future__ import annotations

from typing import Optional, Tuple, Union, Any
import structlog
from logwp.infra.exceptions import ErrorContext
from ..exceptions import WpNmrError, WpNmrDataError, WpNmrComputationError, WpNmrParameterError
from ..constants import (
    ERROR_INVALID_T2_SPECTRUM, ERROR_T2_AXIS_MISMATCH, ERROR_INVALID_CUTOFF_ORDER,
    ERROR_NEGATIVE_CUTOFF, ERROR_INVALID_T2_RANGE, ERROR_NO_DATA_IN_RANGE,
    LOG_OPERATION_COMPUTE, LOG_STAGE_COMPLETE, LOG_STAGE_WARNING
)
from logwp.extras.backend import BackendService

# 获取logger
logger = structlog.get_logger("logwp.extras.nmr.internal")

# =============================================================================
# 数据驱动的单点积分算法
# =============================================================================

def calculate_t2_porosity_components_impl(
    t2_spectrum: Any,
    t2_time: Any,
    t2cutoff_short: float,
    t2cutoff_long: float,
    *,
    service: BackendService,
    t2_range_min: Optional[float] = None,
    t2_range_max: Optional[float] = None
) -> Tuple[float, float, float]:
    """T2谱孔隙度组分计算的内部实现。

    Args:
        t2_spectrum: T2谱孔隙度分布，一维数组，单位v/v
        t2_time: T2时间轴数组，单位ms，必须与t2_spectrum长度一致且单调递增
        t2cutoff_short: 短T2截止值，单位ms，区分微孔和中孔
        t2cutoff_long: 长T2截止值，单位ms，区分中孔和大孔
        t2_range_min: T2时间轴计算范围最小值，单位ms，默认为None（使用全部范围）
        t2_range_max: T2时间轴计算范围最大值，单位ms，默认为None（使用全部范围）

    Returns:
        Tuple[float, float, float]: (V_micro, V_meso, V_macro)

    Raises:
        WpNmrError: NMR计算异常
    """
    try:
        # 步骤1：输入数据验证（使用统一验证函数）
        _validate_common_parameters(t2cutoff_short, t2cutoff_long, t2_range_min, t2_range_max)
        _validate_single_spectrum_data(t2_spectrum)

        # 步骤2：验证数据长度一致性
        if len(t2_spectrum) != len(t2_time):
            raise WpNmrDataError(
                ERROR_T2_AXIS_MISMATCH,
                context=ErrorContext(
                    operation="calculate_t2_porosity_components_impl",
                    additional_info={
                        "data_shape": t2_spectrum.shape,
                        "t2_spectrum_length": len(t2_spectrum),
                        "t2_time_points": len(t2_time),
                        "t2_time_type": "numpy.ndarray"
                    }
                )
            )

        # 步骤3：验证T2时间轴数据质量
        if service.any(service.isnan(t2_time)) or service.any(service.isinf(t2_time)):
            is_finite_mask = ~(service.isnan(t2_time) | service.isinf(t2_time))
            raise WpNmrDataError(
                "T2时间轴包含无效值（NaN或Inf）",
                context=ErrorContext(
                    operation="calculate_t2_porosity_components_impl",
                    additional_info={
                        "nan_count": service.as_scalar(service.sum(service.isnan(t2_time))),
                        "inf_count": service.as_scalar(service.sum(service.isinf(t2_time))),
                        "t2_time_range": (service.as_scalar(service.min(t2_time[is_finite_mask])) if service.any(is_finite_mask) else None,
                                         service.as_scalar(service.max(t2_time[is_finite_mask])) if service.any(is_finite_mask) else None
                                         )
                    }
                )
            )

        # 步骤4：确定计算范围
        if t2_range_min is None:
            t2_range_min = service.as_scalar(service.min(t2_time))
        if t2_range_max is None:
            t2_range_max = service.as_scalar(service.max(t2_time))

        # 步骤5：创建范围掩码
        range_mask = (t2_time >= t2_range_min) & (t2_time <= t2_range_max)

        # 步骤5：在指定范围内计算孔隙度组分
        v_micro, v_meso, v_macro = _calculate_porosity_components_agnostic(
            t2_spectrum, t2_time, range_mask, t2cutoff_short, t2cutoff_long, service, is_vectorized=False
        )

        logger.debug(
            "T2谱孔隙度组分计算完成",
            operation=LOG_OPERATION_COMPUTE,
            stage=LOG_STAGE_COMPLETE,
            v_micro=service.as_scalar(v_micro),
            v_meso=service.as_scalar(v_meso),
            v_macro=service.as_scalar(v_macro),
            total_porosity=service.as_scalar(v_micro + v_meso + v_macro),
            t2_range=(t2_range_min, t2_range_max),
            t2_cutoffs=(t2cutoff_short, t2cutoff_long)
        )

        return service.as_scalar(v_micro), service.as_scalar(v_meso), service.as_scalar(v_macro)

    except Exception as e:
        if isinstance(e, WpNmrError):
            raise

        raise WpNmrComputationError(
            f"T2谱孔隙度组分计算失败: {e}",
            context=ErrorContext(
                operation="calculate_t2_porosity_components_impl",
                additional_info={
                    "t2_spectrum_shape": t2_spectrum.shape if hasattr(t2_spectrum, 'shape') else None,
                    "t2_time_shape": t2_time.shape if hasattr(t2_time, 'shape') else None,
                    "t2cutoff_short": t2cutoff_short,
                    "t2cutoff_long": t2cutoff_long,
                    "t2_range": (t2_range_min, t2_range_max)
                }
            )
        ) from e


def calculate_t2_porosity_components_vectorized_impl(
    t2_spectrum_matrix: Any,
    t2_time: Any,
    t2cutoff_short: float,
    t2cutoff_long: float,
    *,
    service: BackendService,
    t2_range_min: Optional[float] = None,
    t2_range_max: Optional[float] = None,
    t2_spectrum_unit: str = "v/v"
) -> Tuple[Any, Any, Any]:
    """向量化T2谱孔隙度组分计算的内部实现。

    使用真正的numpy向量化操作批量处理多个深度点的T2谱数据，显著提升性能。
    完全避免Python循环，使用矩阵运算和广播机制。

    Args:
        t2_spectrum_matrix: T2谱孔隙度分布矩阵，形状(n_depths, n_bins)
        t2_time: T2时间轴数组，单位ms，必须与t2_spectrum_matrix的列数一致且单调递增
        t2cutoff_short: 短T2截止值，单位ms，区分微孔和中孔
        t2cutoff_long: 长T2截止值，单位ms，区分中孔和大孔
        t2_range_min: T2时间轴计算范围最小值，单位ms，默认为None（使用全部范围）
        t2_range_max: T2时间轴计算范围最大值，单位ms，默认为None（使用全部范围）
        t2_spectrum_unit: T2谱数据的孔隙度单位，默认为"v/v"

    Returns:
        Tuple[np.ndarray, np.ndarray, np.ndarray]: (V_micro, V_meso, V_macro)，单位与t2_spectrum_unit一致

    Raises:
        WpNmrError: NMR计算异常
    """
    try:
        # 步骤1：输入数据验证（使用统一验证函数）
        _validate_common_parameters(t2cutoff_short, t2cutoff_long, t2_range_min, t2_range_max)
        _validate_matrix_spectrum_data(t2_spectrum_matrix)

        # 步骤2：验证数据维度一致性
        n_depths, n_bins = t2_spectrum_matrix.shape
        if n_bins != len(t2_time):
            raise WpNmrDataError(
                ERROR_T2_AXIS_MISMATCH,
                context=ErrorContext(
                    operation="calculate_t2_porosity_components_vectorized_impl",
                    additional_info={
                        "data_shape": t2_spectrum_matrix.shape,
                        "t2_spectrum_bins": n_bins,
                        "t2_time_points": len(t2_time),
                        "t2_time_type": "numpy.ndarray"
                    }
                )
            )

        # 步骤3：验证T2时间轴数据质量
        if service.any(service.isnan(t2_time)) or service.any(service.isinf(t2_time)):
            is_finite_mask = ~(service.isnan(t2_time) | service.isinf(t2_time))
            raise WpNmrDataError(
                "T2时间轴包含无效值（NaN或Inf）",
                context=ErrorContext(
                    operation="calculate_t2_porosity_components_vectorized_impl",
                    additional_info={
                        "nan_count": service.as_scalar(service.sum(service.isnan(t2_time))),
                        "inf_count": service.as_scalar(service.sum(service.isinf(t2_time))),
                        "t2_time_range": (service.as_scalar(service.min(t2_time[is_finite_mask])) if service.any(is_finite_mask) else None,
                                         service.as_scalar(service.max(t2_time[is_finite_mask])) if service.any(is_finite_mask) else None
                                         )
                    }
                )
            )

        # 步骤4：确定计算范围
        if t2_range_min is None:
            t2_range_min = service.as_scalar(service.min(t2_time))
        if t2_range_max is None:
            t2_range_max = service.as_scalar(service.max(t2_time))

        # 步骤5：创建范围掩码
        range_mask = (t2_time >= t2_range_min) & (t2_time <= t2_range_max)

        # 步骤5：真正向量化计算孔隙度组分
        v_micro, v_meso, v_macro = _calculate_porosity_components_agnostic(
            t2_spectrum_matrix, t2_time, range_mask, t2cutoff_short, t2cutoff_long, service, is_vectorized=True
        )

        # 步骤6：单位处理 - 确保返回结果与输入T2谱数据单位一致
        if t2_spectrum_unit.lower() == "percent":
            # 如果输入是百分比单位，需要将求和结果转换为百分比
            # 求和结果默认是v/v单位，需要乘以100转换为百分比
            v_micro = v_micro * 100.0
            v_meso = v_meso * 100.0
            v_macro = v_macro * 100.0
        elif t2_spectrum_unit.lower() == "v/v":
            # 如果输入是v/v单位，积分结果已经是正确的单位，无需转换
            pass
        else:
            # 不支持的单位，记录警告但继续处理
            logger.warning(
                "不支持的T2谱孔隙度单位，将按v/v处理",
                operation=LOG_OPERATION_COMPUTE,
                stage="warning",
                t2_spectrum_unit=t2_spectrum_unit,
                supported_units=["v/v", "percent"]
            )

        logger.debug(
            "向量化T2谱孔隙度组分计算完成",
            operation=LOG_OPERATION_COMPUTE,
            stage=LOG_STAGE_COMPLETE,
            n_depths=n_depths,
            n_bins=n_bins,
            t2_spectrum_unit=t2_spectrum_unit,
            v_micro_stats=(service.as_scalar(service.min(v_micro)), service.as_scalar(service.mean(v_micro)), service.as_scalar(service.max(v_micro))),
            v_meso_stats=(service.as_scalar(service.min(v_meso)), service.as_scalar(service.mean(v_meso)), service.as_scalar(service.max(v_meso))),
            v_macro_stats=(service.as_scalar(service.min(v_macro)), service.as_scalar(service.mean(v_macro)), service.as_scalar(service.max(v_macro))),
            t2_range=(t2_range_min, t2_range_max),
            t2_cutoffs=(t2cutoff_short, t2cutoff_long)
        )

        return v_micro, v_meso, v_macro

    except Exception as e:
        if isinstance(e, WpNmrError):
            raise

        raise WpNmrComputationError(
            f"向量化T2谱孔隙度组分计算失败: {e}",
            context=ErrorContext(
                operation="calculate_t2_porosity_components_vectorized",
                additional_info={
                    "algorithm": "vectorized_t2_porosity_division",
                    "array_size": t2_spectrum_matrix.shape[0] if hasattr(t2_spectrum_matrix, 'shape') else None,
                    "t2_spectrum_shape": t2_spectrum_matrix.shape if hasattr(t2_spectrum_matrix, 'shape') else None,
                    "t2_time_shape": t2_time.shape if hasattr(t2_time, 'shape') else None,
                    "t2cutoff_short": t2cutoff_short,
                    "t2cutoff_long": t2cutoff_long,
                    "t2_range": (t2_range_min, t2_range_max)
                }
            )
        ) from e


# =============================================================================
# 统一验证函数 - 消除重复逻辑
# =============================================================================

def _validate_common_parameters(
    t2cutoff_short: float,
    t2cutoff_long: float,
    t2_range_min: Optional[float],
    t2_range_max: Optional[float]
) -> None:
    """验证T2截止值和范围参数（单点和向量化版本共用）。"""
    # 验证T2截止值
    if t2cutoff_short >= t2cutoff_long:
        raise WpNmrParameterError(
            ERROR_INVALID_CUTOFF_ORDER,
            parameter_name="t2cutoff",
            t2cutoff_short=t2cutoff_short,
            t2cutoff_long=t2cutoff_long,
            parameter_constraint="t2cutoff_short < t2cutoff_long"
        )

    if t2cutoff_short <= 0 or t2cutoff_long <= 0:
        raise WpNmrParameterError(
            ERROR_NEGATIVE_CUTOFF,
            parameter_name="t2cutoff",
            t2cutoff_short=t2cutoff_short,
            t2cutoff_long=t2cutoff_long,
            parameter_constraint="t2cutoff > 0"
        )

    # 验证T2范围
    if t2_range_min is not None and t2_range_max is not None:
        if t2_range_min >= t2_range_max:
            raise WpNmrParameterError(
                ERROR_INVALID_T2_RANGE,
                parameter_name="t2_range",
                t2_range_min=t2_range_min,
                t2_range_max=t2_range_max,
                parameter_constraint="t2_range_min < t2_range_max"
            )

    if t2_range_min is not None and t2_range_min <= 0:
        raise WpNmrParameterError(
            ERROR_INVALID_T2_RANGE,
            parameter_name="t2_range_min",
            parameter_value=t2_range_min,
            parameter_constraint="t2_range_min > 0"
        )

    if t2_range_max is not None and t2_range_max <= 0:
        raise WpNmrParameterError(
            ERROR_INVALID_T2_RANGE,
            parameter_name="t2_range_max",
            parameter_value=t2_range_max,
            parameter_constraint="t2_range_max > 0"
        )


def _validate_single_spectrum_data(t2_spectrum: Any) -> None:
    """验证单点T2谱数据。"""
    if not hasattr(t2_spectrum, 'ndim'):
        raise WpNmrDataError(
            ERROR_INVALID_T2_SPECTRUM,
            data_type=type(t2_spectrum).__name__,
            expected_type="array-like"
        )

    if t2_spectrum.ndim != 1:
        raise WpNmrDataError(
            ERROR_INVALID_T2_SPECTRUM,
            data_shape=t2_spectrum.shape,
            expected_shape="(n,)",
            actual_dimensions=t2_spectrum.ndim
        )


def _validate_matrix_spectrum_data(t2_spectrum_matrix: Any) -> None:
    """验证向量化T2谱矩阵数据。"""
    if not hasattr(t2_spectrum_matrix, 'ndim'):
        raise WpNmrDataError(
            "T2谱数据必须为array-like对象",
            data_type=type(t2_spectrum_matrix).__name__,
        )

    if t2_spectrum_matrix.ndim != 2:
        raise WpNmrDataError(
            "T2谱数据必须为二维数组（n_depths, n_bins）",
            data_shape=t2_spectrum_matrix.shape,
            expected_ndim=2
        )

    if t2_spectrum_matrix.size == 0:
        raise WpNmrDataError(
            "T2谱数据不能为空",
            data_shape=t2_spectrum_matrix.shape
        )


# =============================================================================
# 统一的孔隙度组分计算逻辑 - 消除重复
# =============================================================================

def _calculate_porosity_components_agnostic(
    t2_spectrum_data: Any,
    t2_time: Any,
    range_mask: Any,
    t2cutoff_short: float,
    t2cutoff_long: float,
    service: BackendService,
    is_vectorized: bool = False
) -> Tuple[Any, Any, Any]:
    """统一的孔隙度组分计算逻辑，支持单点和向量化。

    修复了数组掩码逻辑错误：所有掩码都在完整的原始数组上创建，
    确保掩码长度与数组长度始终匹配。

    Args:
        t2_spectrum_data: T2谱数据，单点为1D数组，向量化为2D数组
        t2_time: T2时间轴，1D数组
        range_mask: 范围掩码，1D布尔数组，长度与t2_time一致
        t2cutoff_short: 短T2截止值
        t2cutoff_long: 长T2截止值
        service: 后端服务实例
        is_vectorized: 是否为向量化模式

    Returns:
        孔隙度组分元组，单点返回标量，向量化返回数组
    """
    # 检查是否有数据在指定范围内
    if not service.any(range_mask):
        logger.warning(
            ERROR_NO_DATA_IN_RANGE,
            operation=LOG_OPERATION_COMPUTE,
            stage=LOG_STAGE_WARNING,
            t2_time_range=(service.as_scalar(service.min(t2_time)), service.as_scalar(service.max(t2_time))) if len(t2_time) > 0 else (0, 0)
        )
        if is_vectorized:
            n_depths = t2_spectrum_data.shape[0]
            return (service.zeros(n_depths), service.zeros(n_depths), service.zeros(n_depths))
        else:
            return (0.0, 0.0, 0.0)

    # 数值稳定性处理：确保孔隙度值非负，并处理NaN值
    # 首先将NaN值替换为0，然后确保非负
    t2_spectrum_clean = service.nan_to_num(t2_spectrum_data, nan=0.0, posinf=0.0, neginf=0.0)
    t2_spectrum_safe = service.maximum(t2_spectrum_clean, 0.0)

    # 检查并记录数据质量问题
    if service.any(service.isnan(t2_spectrum_data)):
        nan_count = service.sum(service.isnan(t2_spectrum_data))
        logger.warning(
            "T2谱数据包含NaN值，已替换为0",
            operation=LOG_OPERATION_COMPUTE,
            stage=LOG_STAGE_WARNING,
            nan_count=service.as_scalar(nan_count),
            total_elements=t2_spectrum_data.size,
            nan_ratio=service.as_scalar(nan_count) / t2_spectrum_data.size
        )

    # 在完整的原始数组上创建组分掩码，然后与范围掩码组合
    # 这确保了所有掩码的长度都与原始t2_time数组一致
    micro_base_mask = t2_time < t2cutoff_short
    meso_base_mask = (t2_time >= t2cutoff_short) & (t2_time < t2cutoff_long)
    macro_base_mask = t2_time >= t2cutoff_long

    # 组合范围掩码和组分掩码：只有同时满足范围和组分条件的点才被包含
    micro_combined_mask = range_mask & micro_base_mask
    meso_combined_mask = range_mask & meso_base_mask
    macro_combined_mask = range_mask & macro_base_mask

    # 计算孔隙度组分（使用正确的求和方法）
    # T2谱数据的每个bin已经代表该T2范围内的孔隙度，应该求和而不是积分
    v_micro = _sum_porosity_component_agnostic(t2_spectrum_safe, micro_combined_mask, service, is_vectorized)
    v_meso = _sum_porosity_component_agnostic(t2_spectrum_safe, meso_combined_mask, service, is_vectorized)
    v_macro = _sum_porosity_component_agnostic(t2_spectrum_safe, macro_combined_mask, service, is_vectorized)

    return v_micro, v_meso, v_macro

# =============================================================================
# 统一的求和函数 - T2谱数据应该求和而不是积分
# =============================================================================

def _sum_porosity_component_agnostic(
    t2_spectrum_data: Any,
    component_mask: Any,
    service: BackendService,
    is_vectorized: bool = False
) -> Union[float, Any]:
    """统一的孔隙度组分求和计算，支持单点和向量化。

    T2谱数据的每个bin已经代表该T2范围内的孔隙度值，
    因此应该直接求和而不是积分（乘以时间间隔）。

    Args:
        t2_spectrum_data: T2谱数据，单点为1D数组，向量化为2D数组
        component_mask: 组分掩码，1D布尔数组
        service: 后端服务实例
        is_vectorized: 是否为向量化模式

    Returns:
        求和结果，单点返回标量，向量化返回数组
    """
    if not service.any(component_mask):
        if is_vectorized:
            return service.zeros(t2_spectrum_data.shape[0])
        else:
            return 0.0

    # 提取组分数据并求和
    if is_vectorized:
        spectrum_component = t2_spectrum_data[:, component_mask]  # 形状: (n_depths, n_component_bins)
        # 对每一行的组分数据求和
        return service.sum(spectrum_component, axis=1)
    else:
        spectrum_component = t2_spectrum_data[component_mask]     # 形状: (n_component_bins,)
        # 对组分数据求和
        return service.sum(spectrum_component)
