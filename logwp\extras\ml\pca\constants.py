"""PCA相关常量定义。

定义PCA模块使用的所有常量，包括算法参数、文件格式、
可视化配置等。

Architecture
------------
层次/依赖: PCA常量层，被所有PCA模块引用
设计原则: 集中管理、类型安全、易于维护
性能特征: 编译时常量、快速访问
"""

from __future__ import annotations

from enum import Enum
from typing import Final

# =============================================================================
# PCA算法相关常量
# =============================================================================

class WpPcaAlgorithm(str, Enum):
    """PCA算法类型枚举。
    
    Architecture
    ------------
    层次/依赖: PCA算法选择，支持多种实现
    设计原则: 算法抽象、性能优化、自动选择
    性能特征: GPU优先、CPU回退、自适应选择
    """
    AUTO = "auto"           # 自动选择最佳算法
    SKLEARN = "sklearn"     # scikit-learn PCA实现
    CUML = "cuml"          # RAPIDS cuML GPU实现
    NUMPY = "numpy"        # 基于numpy的SVD实现


class WpPcaMethod(str, Enum):
    """PCA计算方法枚举。
    
    Architecture
    ------------
    层次/依赖: PCA数学方法，影响数值稳定性
    设计原则: 数值稳定、精度保证、性能平衡
    性能特征: SVD优先、特征值分解备选
    """
    SVD = "svd"                    # 奇异值分解（推荐）
    EIGEN = "eigen"                # 特征值分解
    RANDOMIZED = "randomized"      # 随机化PCA（大数据集）


# =============================================================================
# 默认参数配置
# =============================================================================

# PCA计算默认参数
DEFAULT_N_COMPONENTS: Final[int | None] = None
DEFAULT_CURVE_NAME: Final[str] = "PC"
DEFAULT_RANDOM_STATE: Final[int] = 42

# 数据预处理默认参数
DEFAULT_STANDARDIZE: Final[bool] = True
DEFAULT_CENTER: Final[bool] = True
DEFAULT_MIN_COMPLETENESS: Final[float] = 0.5  # 最小数据完整性要求

# 方差解释阈值
DEFAULT_VARIANCE_THRESHOLD_95: Final[float] = 0.95
DEFAULT_VARIANCE_THRESHOLD_90: Final[float] = 0.90
DEFAULT_VARIANCE_THRESHOLD_85: Final[float] = 0.85

# 数值计算容差
NUMERICAL_TOLERANCE: Final[float] = 1e-10
RECONSTRUCTION_TOLERANCE: Final[float] = 1e-6

# =============================================================================
# 文件格式常量
# =============================================================================

class WpPcaModelFormat(str, Enum):
    """PCA模型保存格式枚举。
    
    Architecture
    ------------
    层次/依赖: 模型持久化格式，影响兼容性
    设计原则: 多格式支持、跨平台兼容、性能优化
    性能特征: JSON人类可读、Pickle高效、NPZ压缩
    """
    JSON = "json"       # JSON格式，人类可读，跨平台
    PICKLE = "pickle"   # Python pickle，高效但不跨语言
    NPZ = "npz"        # NumPy压缩格式，适合大型数组
    AUTO = "auto"      # 自动检测格式


# 文件扩展名映射
MODEL_FORMAT_EXTENSIONS: Final[dict[str, str]] = {
    WpPcaModelFormat.JSON: ".json",
    WpPcaModelFormat.PICKLE: ".pkl",
    WpPcaModelFormat.NPZ: ".npz",
}

# 支持的文件扩展名
SUPPORTED_EXTENSIONS: Final[set[str]] = {
    ".json", ".pkl", ".pickle", ".npz"
}

# =============================================================================
# 可视化配置常量
# =============================================================================

class WpPcaPlotType(str, Enum):
    """PCA可视化图表类型枚举。
    
    Architecture
    ------------
    层次/依赖: 可视化类型，支持多种图表
    设计原则: 科学可视化、信息丰富、美观实用
    性能特征: 高分辨率、矢量格式、交互支持
    """
    VARIANCE_EXPLAINED = "variance_explained"    # 方差解释图
    SCATTER_2D = "scatter_2d"                   # 2D散点图
    SCATTER_3D = "scatter_3d"                   # 3D散点图
    HEATMAP = "heatmap"                         # 主成分热图
    BIPLOT = "biplot"                           # 双标图


# 默认可视化参数
DEFAULT_DPI: Final[int] = 300
DEFAULT_FIGURE_SIZE: Final[tuple[int, int]] = (10, 8)
DEFAULT_IMAGE_FORMAT: Final[str] = "png"
DEFAULT_BBOX_INCHES: Final[str] = "tight"

# 支持的图像格式
SUPPORTED_IMAGE_FORMATS: Final[set[str]] = {
    "png", "jpg", "jpeg", "pdf", "svg", "eps"
}

# 颜色配置
DEFAULT_COLORMAP: Final[str] = "viridis"
VARIANCE_COLOR: Final[str] = "#1f77b4"
CUMULATIVE_COLOR: Final[str] = "#ff7f0e"

# =============================================================================
# GPU计算相关常量
# =============================================================================

class WpPcaComputeDevice(str, Enum):
    """PCA计算设备类型枚举。
    
    Architecture
    ------------
    层次/依赖: 计算设备选择，影响性能
    设计原则: GPU优先、CPU回退、自动选择
    性能特征: 10-15倍GPU加速、透明切换
    """
    AUTO = "auto"       # 自动选择最佳设备
    GPU = "gpu"         # 强制使用GPU
    CPU = "cpu"         # 强制使用CPU


# GPU内存管理
GPU_MEMORY_FRACTION: Final[float] = 0.8  # GPU内存使用比例
MIN_GPU_MEMORY_MB: Final[int] = 1024     # 最小GPU内存要求(MB)
CHUNK_SIZE_THRESHOLD: Final[int] = 100000  # 分块处理阈值

# =============================================================================
# 错误消息常量
# =============================================================================

# 数据验证错误消息
ERROR_INSUFFICIENT_CURVES: Final[str] = "数据集中适合PCA分析的曲线数量不足"
ERROR_INSUFFICIENT_SAMPLES: Final[str] = "数据集中样本数量不足以进行PCA分析"
ERROR_NON_NUMERIC_DATA: Final[str] = "发现非数值型数据，无法进行PCA计算"
ERROR_MISSING_DATA: Final[str] = "数据完整性不足，存在过多缺失值"

# 计算错误消息
ERROR_SINGULAR_MATRIX: Final[str] = "协方差矩阵奇异，无法进行PCA分解"
ERROR_GPU_MEMORY: Final[str] = "GPU内存不足，已自动切换到CPU计算"
ERROR_COMPUTATION_FAILED: Final[str] = "PCA计算失败，请检查数据质量"

# 模型错误消息
ERROR_MODEL_INCONSISTENT: Final[str] = "PCA模型参数不一致"
ERROR_DIMENSION_MISMATCH: Final[str] = "数据维度与模型不匹配"
ERROR_INVALID_N_COMPONENTS: Final[str] = "主成分数量超出有效范围"

# =============================================================================
# 日志相关常量
# =============================================================================

# 日志操作类型
LOG_OPERATION_PREPROCESS: Final[str] = "pca_preprocess"
LOG_OPERATION_COMPUTE: Final[str] = "pca_compute"
LOG_OPERATION_TRANSFORM: Final[str] = "pca_transform"
LOG_OPERATION_VISUALIZE: Final[str] = "pca_visualize"
LOG_OPERATION_PERSIST: Final[str] = "pca_persist"

# 日志阶段
LOG_STAGE_START: Final[str] = "start"
LOG_STAGE_PROGRESS: Final[str] = "progress"
LOG_STAGE_COMPLETE: Final[str] = "complete"
LOG_STAGE_ERROR: Final[str] = "error"
