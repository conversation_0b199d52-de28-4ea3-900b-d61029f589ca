"""Configuration models for the LogScout step."""

from pydantic import BaseModel, Field


class LogScoutConfig(BaseModel):
    """Configuration model for the LogScout step.

    Attributes:
        pairplot_max_features: The maximum number of features for which to
            automatically generate a pairplot. If the number of input features
            exceeds this threshold, the pairplot generation will be skipped to
            prevent performance issues.
    """

    pairplot_max_features: int = Field(
        10,
        description=(
            "The maximum number of features for which to automatically generate a pairplot. "
            "If the number of input features exceeds this threshold, the pairplot generation "
            "will be skipped to prevent performance issues."
        ),
        gt=1,
    )
