{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ 端到端工作流 (PyTorch版)\n", "\n", "缓解过拟合现象：调整超参数范围  和 引入权重衰减 。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-24T14:48:51.352640Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 128.09, 'cpu_percent': 0.0}\n", "2025-07-24T14:48:53.631380Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 713.29, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-24T14:48:53.634872Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 713.3, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-24T14:48:53.634872Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 713.3, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-24T14:48:54.379152Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_base_profile profile_name=obmiq.base\n", "2025-07-24T14:48:54.390456Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.training_history\n", "2025-07-24T14:48:54.403347Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.crossplot\n", "2025-07-24T14:48:54.414063Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_plot\n", "2025-07-24T14:48:54.425865Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_hist\n", "2025-07-24T14:48:54.436329Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.shap_summary\n", "2025-07-24T14:48:54.445873Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.captum_ig_summary\n", "2025-07-24T14:48:54.458932Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.grad_cam\n", "环境设置与导入完成。\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "from pathlib import Path\n", "\n", "# 导入 SCAPE 和 LOGWP 的核心组件\n", "from logwp.io import WpExcelReader\n", "from logwp.extras.tracking import RunContext\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "\n", "# 导入新版OBMIQ组件\n", "import scape.core.obmiq.plot_profiles # 导入以注册绘图模板\n", "from scape.core.obmiq import (\n", "    run_obmiq_training_step,\n", "    run_obmiq_prediction_step,\n", "    ObmiqTrainingConfig,\n", "    ObmiqPredictionConfig,\n", "    ObmiqTrainingArtifacts,\n", "    ObmiqArtifactHandler\n", ")\n", "from logwp.models.constants import WpDepthRole\n", "\n", "# 设置\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', 50)\n", "\n", "print(\"环境设置与导入完成。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据\n", "\n", "在此步骤中，我们从 `.wp.xlsx` 文件中加载训练和预测所需的数据集。\n", "\n", "**请注意**:\n", "- `train_bundle` 应包含所有用于训练的井（如 C-1, C-2）。\n", "- `prediction_bundle` 可以是训练井的一部分（用于验证），也可以是全新的盲井（如 T-1）。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:48:54.478063Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.28, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx\n", "2025-07-24T14:48:54.492503Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.66, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx file_size_mb=1.67 sheet_count=1\n", "2025-07-24T14:48:54.501341Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.69, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all\n", "2025-07-24T14:48:54.512697Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.7, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all\n", "2025-07-24T14:48:54.525748Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.81, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T14:48:54.543288Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.85, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=17 well_curves=1\n", "2025-07-24T14:48:55.829576Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 778.85, 'cpu_percent': 0.0} shape=(2667, 80) sheet_name=nmr_obmiq\n", "2025-07-24T14:48:55.844043Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 778.87, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq')\n", "2025-07-24T14:48:55.852386Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 778.87, 'cpu_percent': 0.0} curve_count=17 dataset_name=nmr_obmiq dataset_type=Continuous df_shape=(2667, 80) processing_time=1.33\n", "2025-07-24T14:48:55.865883Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 778.87, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-24T14:48:55.879087Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 778.87, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-24T14:48:55.887499Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 778.87, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx processing_time=1.409 project_name=WpIdentifier('santos_obmiq_cum_all')\n", "2025-07-24T14:48:55.900171Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 778.87, 'cpu_percent': 0.0} extracted_curve_count=15 operation=extract_curve_dataframe_bundle\n", "2025-07-24T14:48:55.938639Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 783.24, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['SDR_PROXY', 'DRES', 'PHIT_NMR', 'PHI_T2_DIST_CUM', 'PHIE_NMR', 'RS_LOG10', 'DEN', 'DT', 'RD_LOG10', 'DT2_P50', 'SWB_NMR', 'MD', 'BFV_NMR', 'BVI_NMR', 'WELL_NO', 'CN', 'DPHIT_NMR'] operation=extract_metadata output_curve_count=17 output_curves=['SDR_PROXY', 'DRES', 'PHIT_NMR', 'PHI_T2_DIST_CUM', 'PHIE_NMR', 'RS_LOG10', 'DEN', 'DT', 'RD_LOG10', 'DT2_P50', 'SWB_NMR', 'MD', 'BFV_NMR', 'BVI_NMR', 'WELL_NO', 'CN', 'DPHIT_NMR']\n", "2025-07-24T14:48:55.953502Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 783.25, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx\n", "2025-07-24T14:48:55.964850Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 783.26, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx file_size_mb=2.11 sheet_count=1\n", "2025-07-24T14:48:55.980699Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 783.26, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all_apply\n", "2025-07-24T14:48:55.985489Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 783.26, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all_apply\n", "2025-07-24T14:48:56.001469Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 783.28, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T14:48:56.018695Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 783.29, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=17 well_curves=1\n", "2025-07-24T14:48:58.147233Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.11, 'cpu_percent': 0.0} shape=(4598, 80) sheet_name=nmr_obmiq_apply\n", "2025-07-24T14:48:58.165759Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.12, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_apply')\n", "2025-07-24T14:48:58.171782Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.12, 'cpu_percent': 0.0} curve_count=17 dataset_name=nmr_obmiq_apply dataset_type=Continuous df_shape=(4598, 80) processing_time=2.186\n", "2025-07-24T14:48:58.180278Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.12, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-24T14:48:58.192462Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.12, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-24T14:48:58.197999Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.12, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=2.244 project_name=WpIdentifier('santos_obmiq_cum_all_apply')\n", "2025-07-24T14:48:58.215004Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.12, 'cpu_percent': 0.0} extracted_curve_count=15 operation=extract_curve_dataframe_bundle\n", "2025-07-24T14:48:58.264392Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.85, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['SDR_PROXY', 'DRES', 'PHIT_NMR', 'PHI_T2_DIST_CUM', 'PHIE_NMR', 'RS_LOG10', 'DEN', 'DT', 'RD_LOG10', 'DT2_P50', 'SWB_NMR', 'MD', 'BFV_NMR', 'BVI_NMR', 'WELL_NO', 'CN', 'DPHIT_NMR'] operation=extract_metadata output_curve_count=17 output_curves=['SDR_PROXY', 'DRES', 'PHIT_NMR', 'PHI_T2_DIST_CUM', 'PHIE_NMR', 'RS_LOG10', 'DEN', 'DT', 'RD_LOG10', 'DT2_P50', 'SWB_NMR', 'MD', 'BFV_NMR', 'BVI_NMR', 'WELL_NO', 'CN', 'DPHIT_NMR']\n", "数据加载成功。\n", "训练数据束: nmr_obmiq, 形状: (2667, 80), 井名: WELL_NO\n", "预测数据束: nmr_obmiq_apply, 形状: (4598, 80)\n", "T2时间轴长度: 64\n"]}], "source": ["# --- 请在此处填写您的数据路径 ---\n", "\n", "\n", "TRAIN_WP_FILE_PATH = \"santos_obmiq_cum_all.wp.xlsx\"\n", "TRAIN_DATASET_NAME = \"nmr_obmiq\"\n", "\n", "PRED_WP_FILE_PATH = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "PREDICTION_DATASET_NAME = \"nmr_obmiq_apply\"\n", "# ---------------------------------\n", "\n", "# 加载整个工区文件\n", "try:\n", "    # 获取训练和预测数据束\n", "    reader = WpExcelReader()\n", "    project = reader.read(TRAIN_WP_FILE_PATH)\n", "    train_ds = project.get_dataset(TRAIN_DATASET_NAME)\n", "    train_bundle = train_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "    well_no_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "    depth_name = train_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "    pred_reader = WpExcelReader()\n", "    pred_project = pred_reader.read(PRED_WP_FILE_PATH)\n", "    prediction_ds = pred_project.get_dataset(PREDICTION_DATASET_NAME)\n", "    prediction_bundle = prediction_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "\n", "    # 从 head_info 获取 T2 时间轴\n", "    # 假设所有井共享一个T2轴定义\n", "\n", "    t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "    t2_time_array = t2_axis_info.calculate_values()\n", "\n", "    print(\"数据加载成功。\")\n", "    print(f\"训练数据束: {train_ds.name}, 形状: {train_ds.df.shape}, 井名: {well_no_name}\")\n", "    print(f\"预测数据束: {prediction_ds.name}, 形状: {prediction_ds.df.shape}\")\n", "    print(f\"T2时间轴长度: {len(t2_time_array)}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 数据加载失败: {e}\")\n", "    project = None\n", "    pred_project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 运行 OBMIQ 工作流\n", "\n", "我们使用 `RunContext` 来包裹整个实验流程，以确保所有参数、指标和产物都被追踪。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:48:58.295894Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.86, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\obmiq_run_pytorch_20250724_224858 run_id=20250724-144858-1ebd9f6f\n", "--- 开始 OBMIQ 训练步骤 (PyTorch) ---\n", "2025-07-24T14:48:58.309320Z [info     ] ===== OBMIQ Training Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.87, 'cpu_percent': 0.0}\n", "2025-07-24T14:48:58.321737Z [info     ] 曲线名已成功解析为DataFrame列名。          [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.87, 'cpu_percent': 0.0}\n", "2025-07-24T14:48:58.326568Z [info     ] --- Stage 0: Saving Configuration Snapshot --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.87, 'cpu_percent': 0.0}\n", "2025-07-24T14:48:58.337225Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.configs.training_config_snapshot artifact_path=obmiq_training_pytorch\\training_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.87, 'cpu_percent': 0.0} description=Snapshot of the training configuration used for this run. operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T14:48:58.348612Z [info     ] --- Stage 2: Hyperparameter Tuning using LOWO-CV --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.87, 'cpu_percent': 0.0}\n", "2025-07-24T14:48:58.365078Z [info     ] Tuning procedure started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.06, 'cpu_percent': 0.0}\n", "2025-07-24T14:48:58.381806Z [info     ] --- Starting CV Fold 1/2 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.09, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-24 22:48:58,400] A new study created in memory with name: no-name-f3b45884-9c5c-4d27-b588-c5ec7b1733dd\n", "[I 2025-07-24 22:49:11,445] Trial 0 finished with value: -0.7242027223110199 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.5553704269769286, 'learning_rate': 0.00013636515503668797, 'weight_decay': 0.0004129769412781528}. Best is trial 0 with value: -0.7242027223110199.\n", "[I 2025-07-24 22:49:21,696] Trial 1 finished with value: -0.9296967685222626 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.29968850121174123, 'learning_rate': 0.00016884536302774433, 'weight_decay': 0.0001714672969166618}. Best is trial 1 with value: -0.9296967685222626.\n", "[I 2025-07-24 22:49:34,094] Trial 2 finished with value: -9.500974814097086 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.3892589333295361, 'learning_rate': 0.004099074108907153, 'weight_decay': 0.00015027683870437136}. Best is trial 2 with value: -9.500974814097086.\n", "[I 2025-07-24 22:49:46,250] Trial 3 finished with value: -9.450305938720703 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.3205348578018573, 'learning_rate': 0.004537407703452234, 'weight_decay': 1.2849030205921083e-05}. Best is trial 2 with value: -9.500974814097086.\n", "[I 2025-07-24 22:49:58,146] Trial 4 finished with value: -9.309752623240152 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.3620569695473935, 'learning_rate': 0.005123641048579456, 'weight_decay': 0.0002144726581339455}. Best is trial 2 with value: -9.500974814097086.\n", "[I 2025-07-24 22:49:58,233] Trial 5 pruned. \n", "[I 2025-07-24 22:49:58,344] Trial 6 pruned. \n", "[I 2025-07-24 22:49:58,446] Trial 7 pruned. \n", "[I 2025-07-24 22:49:58,549] Trial 8 pruned. \n", "[I 2025-07-24 22:50:07,695] Trial 9 pruned. \n", "[I 2025-07-24 22:50:19,022] Trial 10 finished with value: -9.280463536580404 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.44144463223416813, 'learning_rate': 0.009098622334895346, 'weight_decay': 0.0006899722165603207}. Best is trial 2 with value: -9.500974814097086.\n", "[I 2025-07-24 22:50:19,108] Trial 11 pruned. \n", "[I 2025-07-24 22:50:19,218] Trial 12 pruned. \n", "[I 2025-07-24 22:50:19,300] Trial 13 pruned. \n", "[I 2025-07-24 22:50:27,982] Trial 14 finished with value: -9.304469108581543 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.4696230547281446, 'learning_rate': 0.009879024530778205, 'weight_decay': 3.478078218367735e-05}. Best is trial 2 with value: -9.500974814097086.\n", "[I 2025-07-24 22:50:28,054] Trial 15 pruned. \n", "[I 2025-07-24 22:50:28,138] Trial 16 pruned. \n", "[I 2025-07-24 22:50:28,207] Trial 17 pruned. \n", "[I 2025-07-24 22:50:28,278] Trial 18 pruned. \n", "[I 2025-07-24 22:50:28,344] Trial 19 pruned. \n", "[I 2025-07-24 22:50:40,558] Trial 20 finished with value: -9.515429178873697 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.32545321387277903, 'learning_rate': 0.006803063792057003, 'weight_decay': 0.000319158784883047}. Best is trial 20 with value: -9.515429178873697.\n", "[I 2025-07-24 22:50:50,712] Trial 21 finished with value: -9.360522588094076 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.33016368386133754, 'learning_rate': 0.0069492230006155285, 'weight_decay': 0.0002786745426544732}. Best is trial 20 with value: -9.515429178873697.\n", "[I 2025-07-24 22:50:50,806] Trial 22 pruned. \n", "[I 2025-07-24 22:51:03,629] Trial 23 finished with value: -9.554247538248697 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.3456332143553949, 'learning_rate': 0.006597106985788541, 'weight_decay': 0.0005263823786463438}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:51:04,010] Trial 24 pruned. \n", "[I 2025-07-24 22:51:04,112] Trial 25 pruned. \n", "[I 2025-07-24 22:51:15,477] Trial 26 finished with value: -9.46874475479126 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.33811231880105486, 'learning_rate': 0.007672543014911891, 'weight_decay': 0.00024547142008971934}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:51:15,560] Trial 27 pruned. \n", "[I 2025-07-24 22:51:15,644] Trial 28 pruned. \n", "[I 2025-07-24 22:51:15,735] Trial 29 pruned. \n", "[I 2025-07-24 22:51:15,813] Trial 30 pruned. \n", "[I 2025-07-24 22:51:28,066] Trial 31 pruned. \n", "[I 2025-07-24 22:51:31,594] Trial 32 pruned. \n", "[I 2025-07-24 22:51:31,672] Trial 33 pruned. \n", "[I 2025-07-24 22:51:31,747] Trial 34 pruned. \n", "[I 2025-07-24 22:51:39,869] Trial 35 finished with value: -9.255781014760336 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.3548679537124197, 'learning_rate': 0.007989584305936479, 'weight_decay': 0.0005009858962172749}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:51:39,962] Trial 36 pruned. \n", "[I 2025-07-24 22:51:40,041] Trial 37 pruned. \n", "[I 2025-07-24 22:51:40,113] Trial 38 pruned. \n", "[I 2025-07-24 22:51:40,186] Trial 39 pruned. \n", "[I 2025-07-24 22:51:40,257] Trial 40 pruned. \n", "[I 2025-07-24 22:51:40,329] Trial 41 pruned. \n", "[I 2025-07-24 22:51:48,775] Trial 42 pruned. \n", "[I 2025-07-24 22:51:48,850] Trial 43 pruned. \n", "[I 2025-07-24 22:52:00,510] Trial 44 finished with value: -9.347556114196777 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.29751430571115384, 'learning_rate': 0.00975276319587037, 'weight_decay': 0.00011748308769975397}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:52:00,620] Trial 45 pruned. \n", "[I 2025-07-24 22:52:00,753] Trial 46 pruned. \n", "[I 2025-07-24 22:52:00,863] Trial 47 pruned. \n", "[I 2025-07-24 22:52:01,029] Trial 48 pruned. \n", "[I 2025-07-24 22:52:01,131] Trial 49 pruned. \n", "[I 2025-07-24 22:52:10,647] Trial 50 finished with value: -9.472887674967447 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.23901945538080416, 'learning_rate': 0.008124341502252509, 'weight_decay': 0.00016659680730791749}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:52:17,002] Trial 51 finished with value: -9.299832979838053 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.2405336347362625, 'learning_rate': 0.008463643277247503, 'weight_decay': 0.000162048529368611}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:52:17,097] Trial 52 pruned. \n", "[I 2025-07-24 22:52:26,020] Trial 53 finished with value: -9.30210542678833 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.22193028472117185, 'learning_rate': 0.007809114220176951, 'weight_decay': 1.062492134782523e-05}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:52:26,122] Trial 54 pruned. \n", "[I 2025-07-24 22:52:26,236] Trial 55 pruned. \n", "[I 2025-07-24 22:52:34,915] Trial 56 pruned. \n", "[I 2025-07-24 22:52:35,008] Trial 57 pruned. \n", "[I 2025-07-24 22:52:35,119] Trial 58 pruned. \n", "[I 2025-07-24 22:52:35,214] Trial 59 pruned. \n", "[I 2025-07-24 22:52:35,311] Trial 60 pruned. \n", "[I 2025-07-24 22:52:35,407] Trial 61 pruned. \n", "[I 2025-07-24 22:52:40,983] Trial 62 pruned. \n", "[I 2025-07-24 22:52:41,102] Trial 63 pruned. \n", "[I 2025-07-24 22:52:41,210] Trial 64 pruned. \n", "[I 2025-07-24 22:52:41,304] Trial 65 pruned. \n", "[I 2025-07-24 22:52:41,636] Trial 66 pruned. \n", "[I 2025-07-24 22:52:41,758] Trial 67 pruned. \n", "[I 2025-07-24 22:52:52,976] Trial 68 finished with value: -9.455517609914144 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.3069397537706991, 'learning_rate': 0.008583633716897817, 'weight_decay': 0.00013655260089343708}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:52:53,383] Trial 69 pruned. \n", "[I 2025-07-24 22:52:53,473] Trial 70 pruned. \n", "[I 2025-07-24 22:52:53,583] Trial 71 pruned. \n", "[I 2025-07-24 22:53:06,219] Trial 72 finished with value: -9.431670665740967 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2961913226250653, 'learning_rate': 0.008203305320389109, 'weight_decay': 0.00010572128865093355}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:53:13,607] Trial 73 finished with value: -9.436172485351562 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.29170901959947426, 'learning_rate': 0.008585623507345125, 'weight_decay': 0.00010975909443321726}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:53:23,259] Trial 74 finished with value: -9.46108071009318 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2328613873046198, 'learning_rate': 0.009596221313527636, 'weight_decay': 8.641811261290931e-05}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:53:32,390] Trial 75 finished with value: -9.346809228261312 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.23313047344595586, 'learning_rate': 0.009916501180775366, 'weight_decay': 8.830931286501719e-05}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:53:32,490] Trial 76 pruned. \n", "[I 2025-07-24 22:53:32,581] Trial 77 pruned. \n", "[I 2025-07-24 22:53:32,694] Trial 78 pruned. \n", "[I 2025-07-24 22:53:32,800] Trial 79 pruned. \n", "[I 2025-07-24 22:53:32,915] Trial 80 pruned. \n", "[I 2025-07-24 22:53:43,270] Trial 81 finished with value: -9.50221316019694 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2615000015191364, 'learning_rate': 0.008772522590644998, 'weight_decay': 0.00011364605982872611}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:53:43,366] Trial 82 pruned. \n", "[I 2025-07-24 22:53:44,060] Trial 83 pruned. \n", "[I 2025-07-24 22:53:44,163] Trial 84 pruned. \n", "[I 2025-07-24 22:53:53,916] Trial 85 finished with value: -9.539830525716146 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2378538170120715, 'learning_rate': 0.009972679535811843, 'weight_decay': 0.00013192187136463004}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:00,130] Trial 86 pruned. \n", "[I 2025-07-24 22:54:00,239] Trial 87 pruned. \n", "[I 2025-07-24 22:54:00,349] Trial 88 pruned. \n", "[I 2025-07-24 22:54:00,807] Trial 89 pruned. \n", "[I 2025-07-24 22:54:00,902] Trial 90 pruned. \n", "[I 2025-07-24 22:54:09,103] Trial 91 finished with value: -9.29351313908895 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.2609146823804783, 'learning_rate': 0.009992608539750248, 'weight_decay': 0.00014600789687761064}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:09,181] Trial 92 pruned. \n", "[I 2025-07-24 22:54:09,244] Trial 93 pruned. \n", "[I 2025-07-24 22:54:09,323] Trial 94 pruned. \n", "[I 2025-07-24 22:54:09,386] Trial 95 pruned. \n", "[I 2025-07-24 22:54:09,449] Trial 96 pruned. \n", "[I 2025-07-24 22:54:09,528] Trial 97 pruned. \n", "[I 2025-07-24 22:54:09,658] Trial 98 pruned. \n", "[I 2025-07-24 22:54:09,717] Trial 99 pruned. \n", "[I 2025-07-24 22:54:09,796] Trial 100 pruned. \n", "[I 2025-07-24 22:54:10,095] Trial 101 pruned. \n", "[I 2025-07-24 22:54:17,481] Trial 102 finished with value: -9.323625405629477 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2959889751320127, 'learning_rate': 0.008942829860718614, 'weight_decay': 9.97845586984137e-05}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:17,593] Trial 103 pruned. \n", "[I 2025-07-24 22:54:17,703] Trial 104 pruned. \n", "[I 2025-07-24 22:54:25,586] Trial 105 finished with value: -9.396400928497314 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.24713156798458455, 'learning_rate': 0.00928552256821746, 'weight_decay': 0.0001199283571356079}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:25,670] Trial 106 pruned. \n", "[I 2025-07-24 22:54:25,750] Trial 107 pruned. \n", "[I 2025-07-24 22:54:25,827] Trial 108 pruned. \n", "[I 2025-07-24 22:54:25,897] Trial 109 pruned. \n", "[I 2025-07-24 22:54:31,512] Trial 110 finished with value: -9.252396742502848 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.32951225052380045, 'learning_rate': 0.008753269772181577, 'weight_decay': 7.877054875572785e-05}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:31,592] Trial 111 pruned. \n", "[I 2025-07-24 22:54:42,790] Trial 112 finished with value: -9.494035402933756 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.278843887298834, 'learning_rate': 0.009914877596301577, 'weight_decay': 0.00010701228857269528}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:51,097] Trial 113 finished with value: -9.41016928354899 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2807963365442788, 'learning_rate': 0.009976410285223152, 'weight_decay': 0.00016756734291591802}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:51,191] Trial 114 pruned. \n", "[I 2025-07-24 22:54:51,301] Trial 115 pruned. \n", "[I 2025-07-24 22:54:58,567] Trial 116 finished with value: -9.298068205515543 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.23698668809871484, 'learning_rate': 0.009201264146537, 'weight_decay': 9.817710984764818e-05}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:54:58,678] Trial 117 pruned. \n", "[I 2025-07-24 22:54:58,790] Trial 118 pruned. \n", "[I 2025-07-24 22:54:58,920] Trial 119 pruned. \n", "[I 2025-07-24 22:54:59,042] Trial 120 pruned. \n", "[I 2025-07-24 22:54:59,149] Trial 121 pruned. \n", "[I 2025-07-24 22:55:00,841] Trial 122 pruned. \n", "[I 2025-07-24 22:55:08,481] Trial 123 finished with value: -9.346807479858398 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.3152758170846495, 'learning_rate': 0.009959084877429424, 'weight_decay': 8.545847188873847e-05}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:55:08,598] Trial 124 pruned. \n", "[I 2025-07-24 22:55:08,702] Trial 125 pruned. \n", "[I 2025-07-24 22:55:08,812] Trial 126 pruned. \n", "[I 2025-07-24 22:55:08,934] Trial 127 pruned. \n", "[I 2025-07-24 22:55:09,044] Trial 128 pruned. \n", "[I 2025-07-24 22:55:09,163] Trial 129 pruned. \n", "[I 2025-07-24 22:55:09,262] Trial 130 pruned. \n", "[I 2025-07-24 22:55:18,810] Trial 131 finished with value: -9.463696479797363 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.28274589242758585, 'learning_rate': 0.009989932673291273, 'weight_decay': 0.00017144837064151156}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:55:25,114] Trial 132 finished with value: -9.288995424906412 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.256766928524805, 'learning_rate': 0.009983966350623496, 'weight_decay': 0.00013422610492317555}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:55:25,718] Trial 133 pruned. \n", "[I 2025-07-24 22:55:25,817] Trial 134 pruned. \n", "[I 2025-07-24 22:55:25,943] Trial 135 pruned. \n", "[I 2025-07-24 22:55:26,173] Trial 136 pruned. \n", "[I 2025-07-24 22:55:26,289] Trial 137 pruned. \n", "[I 2025-07-24 22:55:26,386] Trial 138 pruned. \n", "[I 2025-07-24 22:55:26,497] Trial 139 pruned. \n", "[I 2025-07-24 22:55:26,594] Trial 140 pruned. \n", "[I 2025-07-24 22:55:26,980] Trial 141 pruned. \n", "[I 2025-07-24 22:55:27,089] Trial 142 pruned. \n", "[I 2025-07-24 22:55:27,528] Trial 143 pruned. \n", "[I 2025-07-24 22:55:28,149] Trial 144 pruned. \n", "[I 2025-07-24 22:55:28,342] Trial 145 pruned. \n", "[I 2025-07-24 22:55:28,512] Trial 146 pruned. \n", "[I 2025-07-24 22:55:28,612] Trial 147 pruned. \n", "[I 2025-07-24 22:55:35,343] Trial 148 pruned. \n", "[I 2025-07-24 22:55:35,455] Trial 149 pruned. \n", "[I 2025-07-24 22:55:35,539] Trial 150 pruned. \n", "[I 2025-07-24 22:55:36,415] Trial 151 pruned. \n", "[I 2025-07-24 22:55:36,541] Trial 152 pruned. \n", "[I 2025-07-24 22:55:36,646] Trial 153 pruned. \n", "[I 2025-07-24 22:55:36,763] Trial 154 pruned. \n", "[I 2025-07-24 22:55:50,555] Trial 155 finished with value: -9.528019428253174 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.22851662760945096, 'learning_rate': 0.009942764685145367, 'weight_decay': 0.0002777518362638257}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:56:04,035] Trial 156 finished with value: -9.54046376546224 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.21118725223043502, 'learning_rate': 0.009919114004146695, 'weight_decay': 0.00026983378390636064}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:56:04,118] Trial 157 pruned. \n", "[I 2025-07-24 22:56:04,272] Trial 158 pruned. \n", "[I 2025-07-24 22:56:04,658] Trial 159 pruned. \n", "[I 2025-07-24 22:56:04,793] Trial 160 pruned. \n", "[I 2025-07-24 22:56:05,430] Trial 161 pruned. \n", "[I 2025-07-24 22:56:12,058] Trial 162 finished with value: -9.372454643249512 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.2228831700420641, 'learning_rate': 0.00988987172235137, 'weight_decay': 0.00024257419883431025}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:56:12,141] Trial 163 pruned. \n", "[I 2025-07-24 22:56:12,220] Trial 164 pruned. \n", "[I 2025-07-24 22:56:12,303] Trial 165 pruned. \n", "[I 2025-07-24 22:56:12,386] Trial 166 pruned. \n", "[I 2025-07-24 22:56:12,778] Trial 167 pruned. \n", "[I 2025-07-24 22:56:12,863] Trial 168 pruned. \n", "[I 2025-07-24 22:56:12,941] Trial 169 pruned. \n", "[I 2025-07-24 22:56:13,020] Trial 170 pruned. \n", "[I 2025-07-24 22:56:13,102] Trial 171 pruned. \n", "[I 2025-07-24 22:56:13,185] Trial 172 pruned. \n", "[I 2025-07-24 22:56:13,707] Trial 173 pruned. \n", "[I 2025-07-24 22:56:13,996] Trial 174 pruned. \n", "[I 2025-07-24 22:56:14,079] Trial 175 pruned. \n", "[I 2025-07-24 22:56:18,640] Trial 176 pruned. \n", "[I 2025-07-24 22:56:18,718] Trial 177 pruned. \n", "[I 2025-07-24 22:56:18,799] Trial 178 pruned. \n", "[I 2025-07-24 22:56:18,938] Trial 179 pruned. \n", "[I 2025-07-24 22:56:19,021] Trial 180 pruned. \n", "[I 2025-07-24 22:56:26,581] Trial 181 finished with value: -9.506335735321045 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.22383586509643189, 'learning_rate': 0.009870580372037948, 'weight_decay': 0.00024645359814334464}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:56:26,663] Trial 182 pruned. \n", "[I 2025-07-24 22:56:27,135] Trial 183 pruned. \n", "[I 2025-07-24 22:56:27,637] Trial 184 pruned. \n", "[I 2025-07-24 22:56:27,748] Trial 185 pruned. \n", "[I 2025-07-24 22:56:27,826] Trial 186 pruned. \n", "[I 2025-07-24 22:56:31,915] Trial 187 pruned. \n", "[I 2025-07-24 22:56:31,979] Trial 188 pruned. \n", "[I 2025-07-24 22:56:32,204] Trial 189 pruned. \n", "[I 2025-07-24 22:56:32,287] Trial 190 pruned. \n", "[I 2025-07-24 22:56:41,469] Trial 191 finished with value: -9.306732813517252 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.21869712147306872, 'learning_rate': 0.009890065185825594, 'weight_decay': 0.00024032111130010213}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:56:41,532] Trial 192 pruned. \n", "[I 2025-07-24 22:56:41,720] Trial 193 pruned. \n", "[I 2025-07-24 22:56:41,858] Trial 194 pruned. \n", "[I 2025-07-24 22:56:41,937] Trial 195 pruned. \n", "[I 2025-07-24 22:56:42,015] Trial 196 pruned. \n", "[I 2025-07-24 22:56:42,077] Trial 197 pruned. \n", "[I 2025-07-24 22:56:49,470] Trial 198 finished with value: -9.390223344167074 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.29879588797571605, 'learning_rate': 0.00998434391685059, 'weight_decay': 0.0008713870872953363}. Best is trial 23 with value: -9.554247538248697.\n", "[I 2025-07-24 22:56:49,564] Trial 199 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:56:49.564510Z [info     ] Fold 1 best trial: value=-9.5542, params={'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.3456332143553949, 'learning_rate': 0.006597106985788541, 'weight_decay': 0.0005263823786463438} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1385.64, 'cpu_percent': 0.0}\n", "2025-07-24T14:56:49.564510Z [info     ] --- Performing blind test for Fold 1 on well 'C-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1385.64, 'cpu_percent': 0.0}\n", "2025-07-24T14:56:57.004619Z [info     ] --- Starting CV Fold 2/2 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1389.42, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-24 22:56:57,022] A new study created in memory with name: no-name-edb21bde-89f1-4f52-a933-e47a0f9a23d1\n", "[I 2025-07-24 22:57:03,379] Trial 0 finished with value: -8.081711053848267 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.5819494470753481, 'learning_rate': 0.006443587046791349, 'weight_decay': 1.0534934718089841e-05}. Best is trial 0 with value: -8.081711053848267.\n", "[I 2025-07-24 22:57:11,863] Trial 1 finished with value: -1.6154335141181946 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.4494934185979712, 'learning_rate': 0.000442296739555775, 'weight_decay': 1.8222493526669076e-05}. Best is trial 0 with value: -8.081711053848267.\n", "[I 2025-07-24 22:57:18,702] Trial 2 finished with value: -8.57214879989624 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.20786407936977414, 'learning_rate': 0.008456362375424766, 'weight_decay': 2.9132027209961706e-05}. Best is trial 2 with value: -8.57214879989624.\n", "[I 2025-07-24 22:57:26,716] Trial 3 finished with value: -1.9501480460166931 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.41207816911087936, 'learning_rate': 0.0005434790111307499, 'weight_decay': 1.4529244135176841e-05}. Best is trial 2 with value: -8.57214879989624.\n", "[I 2025-07-24 22:57:34,934] Trial 4 finished with value: -8.179227948188782 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.4963961263665618, 'learning_rate': 0.005299430741137199, 'weight_decay': 0.0001105467746922952}. Best is trial 2 with value: -8.57214879989624.\n", "[I 2025-07-24 22:57:34,977] Trial 5 pruned. \n", "[I 2025-07-24 22:57:35,028] Trial 6 pruned. \n", "[I 2025-07-24 22:57:35,074] Trial 7 pruned. \n", "[I 2025-07-24 22:57:35,120] Trial 8 pruned. \n", "[I 2025-07-24 22:57:35,168] Trial 9 pruned. \n", "[I 2025-07-24 22:57:35,214] Trial 10 pruned. \n", "[I 2025-07-24 22:57:44,230] Trial 11 finished with value: -8.599850177764893 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.21487975938131523, 'learning_rate': 0.008702922813152467, 'weight_decay': 5.16079016553054e-05}. Best is trial 11 with value: -8.599850177764893.\n", "[I 2025-07-24 22:57:51,292] Trial 12 finished with value: -8.789764165878296 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.20926838707385603, 'learning_rate': 0.008186949955449708, 'weight_decay': 4.2687526435326524e-05}. Best is trial 12 with value: -8.789764165878296.\n", "[I 2025-07-24 22:57:51,339] Trial 13 pruned. \n", "[I 2025-07-24 22:58:00,091] Trial 14 finished with value: -8.469102621078491 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2988668466396013, 'learning_rate': 0.009533889276484063, 'weight_decay': 4.209341736687515e-05}. Best is trial 12 with value: -8.789764165878296.\n", "[I 2025-07-24 22:58:00,167] Trial 15 pruned. \n", "[I 2025-07-24 22:58:00,234] Trial 16 pruned. \n", "[I 2025-07-24 22:58:00,341] Trial 17 pruned. \n", "[I 2025-07-24 22:58:00,434] Trial 18 pruned. \n", "[I 2025-07-24 22:58:00,523] Trial 19 pruned. \n", "[I 2025-07-24 22:58:00,597] Trial 20 pruned. \n", "[I 2025-07-24 22:58:05,839] Trial 21 pruned. \n", "[I 2025-07-24 22:58:05,910] Trial 22 pruned. \n", "[I 2025-07-24 22:58:06,310] Trial 23 pruned. \n", "[I 2025-07-24 22:58:11,917] Trial 24 finished with value: -7.9727703332901 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.2848265034590237, 'learning_rate': 0.009942855815420336, 'weight_decay': 6.072738251375801e-05}. Best is trial 12 with value: -8.789764165878296.\n", "[I 2025-07-24 22:58:11,990] Trial 25 pruned. \n", "[I 2025-07-24 22:58:12,053] Trial 26 pruned. \n", "[I 2025-07-24 22:58:12,118] Trial 27 pruned. \n", "[I 2025-07-24 22:58:12,199] Trial 28 pruned. \n", "[I 2025-07-24 22:58:12,259] Trial 29 pruned. \n", "[I 2025-07-24 22:58:12,352] Trial 30 pruned. \n", "[I 2025-07-24 22:58:19,422] Trial 31 finished with value: -8.376618385314941 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.30317732580870377, 'learning_rate': 0.009605486482952218, 'weight_decay': 4.080858722869879e-05}. Best is trial 12 with value: -8.789764165878296.\n", "[I 2025-07-24 22:58:19,500] Trial 32 pruned. \n", "[I 2025-07-24 22:58:19,573] Trial 33 pruned. \n", "[I 2025-07-24 22:58:19,638] Trial 34 pruned. \n", "[I 2025-07-24 22:58:19,706] Trial 35 pruned. \n", "[I 2025-07-24 22:58:22,257] Trial 36 pruned. \n", "[I 2025-07-24 22:58:22,337] Trial 37 pruned. \n", "[I 2025-07-24 22:58:22,421] Trial 38 pruned. \n", "[I 2025-07-24 22:58:22,497] Trial 39 pruned. \n", "[I 2025-07-24 22:58:22,565] Trial 40 pruned. \n", "[I 2025-07-24 22:58:22,644] Trial 41 pruned. \n", "[I 2025-07-24 22:58:27,507] Trial 42 pruned. \n", "[I 2025-07-24 22:58:27,587] Trial 43 pruned. \n", "[I 2025-07-24 22:58:27,657] Trial 44 pruned. \n", "[I 2025-07-24 22:58:27,722] Trial 45 pruned. \n", "[I 2025-07-24 22:58:27,795] Trial 46 pruned. \n", "[I 2025-07-24 22:58:27,868] Trial 47 pruned. \n", "[I 2025-07-24 22:58:27,940] Trial 48 pruned. \n", "[I 2025-07-24 22:58:28,015] Trial 49 pruned. \n", "[I 2025-07-24 22:58:28,093] Trial 50 pruned. \n", "[I 2025-07-24 22:58:31,862] Trial 51 pruned. \n", "[I 2025-07-24 22:58:31,927] Trial 52 pruned. \n", "[I 2025-07-24 22:58:31,999] Trial 53 pruned. \n", "[I 2025-07-24 22:58:32,053] Trial 54 pruned. \n", "[I 2025-07-24 22:58:32,147] Trial 55 pruned. \n", "[I 2025-07-24 22:58:32,226] Trial 56 pruned. \n", "[I 2025-07-24 22:58:32,290] Trial 57 pruned. \n", "[I 2025-07-24 22:58:32,353] Trial 58 pruned. \n", "[I 2025-07-24 22:58:32,432] Trial 59 pruned. \n", "[I 2025-07-24 22:58:32,495] Trial 60 pruned. \n", "[I 2025-07-24 22:58:32,573] Trial 61 pruned. \n", "[I 2025-07-24 22:58:35,678] Trial 62 pruned. \n", "[I 2025-07-24 22:58:35,758] Trial 63 pruned. \n", "[I 2025-07-24 22:58:40,115] Trial 64 pruned. \n", "[I 2025-07-24 22:58:40,177] Trial 65 pruned. \n", "[I 2025-07-24 22:58:40,256] Trial 66 pruned. \n", "[I 2025-07-24 22:58:40,344] Trial 67 pruned. \n", "[I 2025-07-24 22:58:40,413] Trial 68 pruned. \n", "[I 2025-07-24 22:58:40,477] Trial 69 pruned. \n", "[I 2025-07-24 22:58:40,794] Trial 70 pruned. \n", "[I 2025-07-24 22:58:40,857] Trial 71 pruned. \n", "[I 2025-07-24 22:58:43,956] Trial 72 pruned. \n", "[I 2025-07-24 22:58:44,035] Trial 73 pruned. \n", "[I 2025-07-24 22:58:49,303] Trial 74 pruned. \n", "[I 2025-07-24 22:58:49,365] Trial 75 pruned. \n", "[I 2025-07-24 22:58:49,566] Trial 76 pruned. \n", "[I 2025-07-24 22:58:49,634] Trial 77 pruned. \n", "[I 2025-07-24 22:58:49,717] Trial 78 pruned. \n", "[I 2025-07-24 22:58:49,776] Trial 79 pruned. \n", "[I 2025-07-24 22:58:49,866] Trial 80 pruned. \n", "[I 2025-07-24 22:58:49,942] Trial 81 pruned. \n", "[I 2025-07-24 22:58:50,013] Trial 82 pruned. \n", "[I 2025-07-24 22:58:50,092] Trial 83 pruned. \n", "[I 2025-07-24 22:58:50,175] Trial 84 pruned. \n", "[I 2025-07-24 22:58:50,250] Trial 85 pruned. \n", "[I 2025-07-24 22:58:50,315] Trial 86 pruned. \n", "[I 2025-07-24 22:58:50,376] Trial 87 pruned. \n", "[I 2025-07-24 22:58:50,455] Trial 88 pruned. \n", "[I 2025-07-24 22:58:50,881] Trial 89 pruned. \n", "[I 2025-07-24 22:58:50,961] Trial 90 pruned. \n", "[I 2025-07-24 22:58:51,039] Trial 91 pruned. \n", "[I 2025-07-24 22:58:51,118] Trial 92 pruned. \n", "[I 2025-07-24 22:58:51,181] Trial 93 pruned. \n", "[I 2025-07-24 22:58:51,260] Trial 94 pruned. \n", "[I 2025-07-24 22:58:51,349] Trial 95 pruned. \n", "[I 2025-07-24 22:58:51,415] Trial 96 pruned. \n", "[I 2025-07-24 22:58:51,487] Trial 97 pruned. \n", "[I 2025-07-24 22:58:51,554] Trial 98 pruned. \n", "[I 2025-07-24 22:58:51,925] Trial 99 pruned. \n", "[I 2025-07-24 22:58:52,004] Trial 100 pruned. \n", "[I 2025-07-24 22:58:52,369] Trial 101 pruned. \n", "[I 2025-07-24 22:58:52,444] Trial 102 pruned. \n", "[I 2025-07-24 22:58:52,794] Trial 103 pruned. \n", "[I 2025-07-24 22:58:52,873] Trial 104 pruned. \n", "[I 2025-07-24 22:58:52,958] Trial 105 pruned. \n", "[I 2025-07-24 22:58:53,035] Trial 106 pruned. \n", "[I 2025-07-24 22:58:53,112] Trial 107 pruned. \n", "[I 2025-07-24 22:58:53,189] Trial 108 pruned. \n", "[I 2025-07-24 22:58:57,690] Trial 109 pruned. \n", "[I 2025-07-24 22:58:57,848] Trial 110 pruned. \n", "[I 2025-07-24 22:59:01,891] Trial 111 pruned. \n", "[I 2025-07-24 22:59:02,097] Trial 112 pruned. \n", "[I 2025-07-24 22:59:02,182] Trial 113 pruned. \n", "[I 2025-07-24 22:59:05,593] Trial 114 pruned. \n", "[I 2025-07-24 22:59:05,647] Trial 115 pruned. \n", "[I 2025-07-24 22:59:05,702] Trial 116 pruned. \n", "[I 2025-07-24 22:59:05,757] Trial 117 pruned. \n", "[I 2025-07-24 22:59:05,815] Trial 118 pruned. \n", "[I 2025-07-24 22:59:05,864] Trial 119 pruned. \n", "[I 2025-07-24 22:59:05,920] Trial 120 pruned. \n", "[I 2025-07-24 22:59:06,834] Trial 121 pruned. \n", "[I 2025-07-24 22:59:11,141] Trial 122 pruned. \n", "[I 2025-07-24 22:59:11,226] Trial 123 pruned. \n", "[I 2025-07-24 22:59:11,290] Trial 124 pruned. \n", "[I 2025-07-24 22:59:11,393] Trial 125 pruned. \n", "[I 2025-07-24 22:59:11,454] Trial 126 pruned. \n", "[I 2025-07-24 22:59:11,512] Trial 127 pruned. \n", "[I 2025-07-24 22:59:11,573] Trial 128 pruned. \n", "[I 2025-07-24 22:59:11,628] Trial 129 pruned. \n", "[I 2025-07-24 22:59:11,876] Trial 130 pruned. \n", "[I 2025-07-24 22:59:11,930] Trial 131 pruned. \n", "[I 2025-07-24 22:59:12,022] Trial 132 pruned. \n", "[I 2025-07-24 22:59:12,518] Trial 133 pruned. \n", "[I 2025-07-24 22:59:12,572] Trial 134 pruned. \n", "[I 2025-07-24 22:59:12,623] Trial 135 pruned. \n", "[I 2025-07-24 22:59:15,145] Trial 136 pruned. \n", "[I 2025-07-24 22:59:15,208] Trial 137 pruned. \n", "[I 2025-07-24 22:59:15,289] Trial 138 pruned. \n", "[I 2025-07-24 22:59:15,382] Trial 139 pruned. \n", "[I 2025-07-24 22:59:15,459] Trial 140 pruned. \n", "[I 2025-07-24 22:59:19,041] Trial 141 pruned. \n", "[I 2025-07-24 22:59:23,546] Trial 142 pruned. \n", "[I 2025-07-24 22:59:23,626] Trial 143 pruned. \n", "[I 2025-07-24 22:59:23,705] Trial 144 pruned. \n", "[I 2025-07-24 22:59:23,785] Trial 145 pruned. \n", "[I 2025-07-24 22:59:27,519] Trial 146 pruned. \n", "[I 2025-07-24 22:59:27,584] Trial 147 pruned. \n", "[I 2025-07-24 22:59:27,678] Trial 148 pruned. \n", "[I 2025-07-24 22:59:27,752] Trial 149 pruned. \n", "[I 2025-07-24 22:59:27,834] Trial 150 pruned. \n", "[I 2025-07-24 22:59:34,587] Trial 151 finished with value: -8.266958951950073 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.3271450359551288, 'learning_rate': 0.009843239720476741, 'weight_decay': 7.550348069592969e-05}. Best is trial 12 with value: -8.789764165878296.\n", "[I 2025-07-24 22:59:39,037] Trial 152 pruned. \n", "[I 2025-07-24 22:59:39,458] Trial 153 pruned. \n", "[I 2025-07-24 22:59:39,526] Trial 154 pruned. \n", "[I 2025-07-24 22:59:39,590] Trial 155 pruned. \n", "[I 2025-07-24 22:59:39,668] Trial 156 pruned. \n", "[I 2025-07-24 22:59:43,289] Trial 157 pruned. \n", "[I 2025-07-24 22:59:43,373] Trial 158 pruned. \n", "[I 2025-07-24 22:59:43,432] Trial 159 pruned. \n", "[I 2025-07-24 22:59:43,660] Trial 160 pruned. \n", "[I 2025-07-24 22:59:47,090] Trial 161 pruned. \n", "[I 2025-07-24 22:59:51,243] Trial 162 pruned. \n", "[I 2025-07-24 22:59:51,436] Trial 163 pruned. \n", "[I 2025-07-24 22:59:51,516] Trial 164 pruned. \n", "[I 2025-07-24 22:59:54,917] Trial 165 pruned. \n", "[I 2025-07-24 22:59:55,674] Trial 166 pruned. \n", "[I 2025-07-24 22:59:55,754] Trial 167 pruned. \n", "[I 2025-07-24 22:59:55,816] Trial 168 pruned. \n", "[I 2025-07-24 22:59:55,895] Trial 169 pruned. \n", "[I 2025-07-24 22:59:59,632] Trial 170 pruned. \n", "[I 2025-07-24 22:59:59,711] Trial 171 pruned. \n", "[I 2025-07-24 23:00:03,182] Trial 172 pruned. \n", "[I 2025-07-24 23:00:03,261] Trial 173 pruned. \n", "[I 2025-07-24 23:00:03,324] Trial 174 pruned. \n", "[I 2025-07-24 23:00:03,766] Trial 175 pruned. \n", "[I 2025-07-24 23:00:04,569] Trial 176 pruned. \n", "[I 2025-07-24 23:00:04,632] Trial 177 pruned. \n", "[I 2025-07-24 23:00:04,711] Trial 178 pruned. \n", "[I 2025-07-24 23:00:04,807] Trial 179 pruned. \n", "[I 2025-07-24 23:00:04,901] Trial 180 pruned. \n", "[I 2025-07-24 23:00:06,084] Trial 181 pruned. \n", "[I 2025-07-24 23:00:06,446] Trial 182 pruned. \n", "[I 2025-07-24 23:00:06,525] Trial 183 pruned. \n", "[I 2025-07-24 23:00:06,935] Trial 184 pruned. \n", "[I 2025-07-24 23:00:07,013] Trial 185 pruned. \n", "[I 2025-07-24 23:00:07,076] Trial 186 pruned. \n", "[I 2025-07-24 23:00:07,265] Trial 187 pruned. \n", "[I 2025-07-24 23:00:07,344] Trial 188 pruned. \n", "[I 2025-07-24 23:00:07,485] Trial 189 pruned. \n", "[I 2025-07-24 23:00:10,920] Trial 190 pruned. \n", "[I 2025-07-24 23:00:10,984] Trial 191 pruned. \n", "[I 2025-07-24 23:00:17,294] Trial 192 finished with value: -8.38309931755066 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.32944063579603483, 'learning_rate': 0.009981787251095107, 'weight_decay': 9.105803563416597e-05}. Best is trial 12 with value: -8.789764165878296.\n", "[I 2025-07-24 23:00:17,928] Trial 193 pruned. \n", "[I 2025-07-24 23:00:17,988] Trial 194 pruned. \n", "[I 2025-07-24 23:00:18,055] Trial 195 pruned. \n", "[I 2025-07-24 23:00:18,921] Trial 196 pruned. \n", "[I 2025-07-24 23:00:18,988] Trial 197 pruned. \n", "[I 2025-07-24 23:00:23,189] Trial 198 finished with value: -8.04581332206726 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.2661500693250692, 'learning_rate': 0.009997816094232682, 'weight_decay': 3.389509740307363e-05}. Best is trial 12 with value: -8.789764165878296.\n", "[I 2025-07-24 23:00:23,672] Trial 199 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:00:23.674304Z [info     ] Fold 2 best trial: value=-8.7898, params={'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.20926838707385603, 'learning_rate': 0.008186949955449708, 'weight_decay': 4.2687526435326524e-05} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1409.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:23.679660Z [info     ] --- Performing blind test for Fold 2 on well 'T-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1409.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.343919Z [info     ] Aggregating results from all CV folds... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.11, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.355889Z [info     ] 成功从交叉验证流程中收集到泛化能力评估数据。         [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.88, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.367862Z [info     ] Best hyperparameters found: {'cnn_filters': 32.0, 'cnn_kernel_size': 5.0, 'dropout_rate': 0.3456332143553949, 'learning_rate': 0.006597106985788541, 'mlp_units': 64.0, 'weight_decay': 0.0005263823786463438} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.88, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.375335Z [info     ] --- Stage 2 Artifacts: Saving CV and Tuning Reports --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.88, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.394259Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.cv_performance artifact_path=obmiq_training_pytorch\\cv_performance_report.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.9, 'cpu_percent': 0.0} description=LOWO-CV中每一折的最佳验证损失和对应的超参数。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:33.416021Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.hyperparameter_tuning artifact_path=obmiq_training_pytorch\\hyperparameter_tuning_report.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.9, 'cpu_percent': 0.0} description=在所有CV折中聚合得到的全局最佳超参数组合。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:33.430026Z [info     ] --- Stage 2 Artifacts: Generating Generalization Evaluation Artifacts --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.9, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.479259Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.lowo_cv_performance_summary artifact_path=obmiq_training_pytorch\\lowo_cv_performance_summary.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.9, 'cpu_percent': 0.0} description=LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:33.502724Z [info     ] 成功生成并保存了泛化能力性能评估表: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\lowo_cv_performance_summary.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.9, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.621521Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.lowo_cv_predictions artifact_path=obmiq_training_pytorch\\lowo_cv_predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1417.47, 'cpu_percent': 0.0} description=LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:33.636669Z [info     ] 成功保存了泛化能力评估的数据快照: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\lowo_cv_predictions.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1417.47, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:33.676045Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1419.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:00:33.691314Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1419.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:34.128366Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1443.2, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.svg']\n", "2025-07-24T15:00:34.140411Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1441.55, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dt2 P50 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:34.183626Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1443.2, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:00:34.193730Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1443.2, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:34.579232Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1465.85, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.svg']\n", "2025-07-24T15:00:34.593729Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.21, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dphit Nmr operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:34.660350Z [info     ] 成功生成并保存了泛化能力交叉图。               [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.21, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:34.673817Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.21, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\tensorboard_logs' operation=register_artifact\n", "2025-07-24T15:00:34.685392Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.logs.tensorboard artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.21, 'cpu_percent': 0.0} description=用于TensorBoard可视化的日志文件目录。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:34.699244Z [info     ] --- Stage 3: Final Model Training --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.21, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:34.707222Z [info     ] Final model training started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.21, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:34.968115Z [info     ] Epoch 1/300, Train Loss: -0.0267, Val Loss: -0.2536 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1554.02, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.115955Z [info     ] Epoch 2/300, Train Loss: -0.4848, Val Loss: -0.6719 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.12, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.212312Z [info     ] Epoch 3/300, Train Loss: -0.9033, Val Loss: -1.1028 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.320400Z [info     ] Epoch 4/300, Train Loss: -1.3019, Val Loss: -1.4373 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.418637Z [info     ] Epoch 5/300, Train Loss: -1.6665, Val Loss: -1.6951 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.532569Z [info     ] Epoch 6/300, Train Loss: -2.0264, Val Loss: -2.0527 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.641902Z [info     ] Epoch 7/300, Train Loss: -2.3517, Val Loss: -2.4601 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.777913Z [info     ] Epoch 8/300, Train Loss: -2.6583, Val Loss: -2.7395 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.887411Z [info     ] Epoch 9/300, Train Loss: -2.9660, Val Loss: -3.0259 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:35.983509Z [info     ] Epoch 10/300, Train Loss: -3.2647, Val Loss: -3.1882 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.083267Z [info     ] Epoch 11/300, Train Loss: -3.5298, Val Loss: -3.4838 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.183934Z [info     ] Epoch 12/300, Train Loss: -3.7691, Val Loss: -3.7964 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.277069Z [info     ] Epoch 13/300, Train Loss: -3.9881, Val Loss: -3.9720 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.378217Z [info     ] Epoch 14/300, Train Loss: -4.2381, Val Loss: -3.9821 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.478692Z [info     ] Epoch 15/300, Train Loss: -4.4609, Val Loss: -4.2844 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.634950Z [info     ] Epoch 16/300, Train Loss: -4.6976, Val Loss: -4.6016 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.732670Z [info     ] Epoch 17/300, Train Loss: -4.9153, Val Loss: -4.8426 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.837106Z [info     ] Epoch 18/300, Train Loss: -5.1211, Val Loss: -5.1514 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:36.942853Z [info     ] Epoch 19/300, Train Loss: -5.3315, Val Loss: -5.3179 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:37.031978Z [info     ] Epoch 20/300, Train Loss: -5.6039, Val Loss: -5.0782 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:37.131696Z [info     ] Epoch 21/300, Train Loss: -5.7873, Val Loss: -5.3441 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:37.349664Z [info     ] Epoch 22/300, Train Loss: -6.0055, Val Loss: -5.8984 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:37.549737Z [info     ] Epoch 23/300, Train Loss: -6.2223, Val Loss: -5.9653 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:37.812198Z [info     ] Epoch 24/300, Train Loss: -6.4378, Val Loss: -6.2670 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:38.011012Z [info     ] Epoch 25/300, Train Loss: -6.6015, Val Loss: -6.4304 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:38.198551Z [info     ] Epoch 26/300, Train Loss: -6.8123, Val Loss: -6.6471 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:38.405067Z [info     ] Epoch 27/300, Train Loss: -6.9682, Val Loss: -6.7928 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:38.605118Z [info     ] Epoch 28/300, Train Loss: -7.1773, Val Loss: -6.6463 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:38.799617Z [info     ] Epoch 29/300, Train Loss: -7.3315, Val Loss: -7.1801 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:38.988562Z [info     ] Epoch 30/300, Train Loss: -7.4765, Val Loss: -7.0848 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:39.184675Z [info     ] Epoch 31/300, Train Loss: -7.5673, Val Loss: -7.2572 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:39.436327Z [info     ] Epoch 32/300, Train Loss: -7.6909, Val Loss: -7.1805 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:39.656633Z [info     ] Epoch 33/300, Train Loss: -7.6937, Val Loss: -7.2411 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:39.875413Z [info     ] Epoch 34/300, Train Loss: -7.8689, Val Loss: -7.4345 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:40.109121Z [info     ] Epoch 35/300, Train Loss: -7.9651, Val Loss: -7.6031 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:40.277107Z [info     ] Epoch 36/300, Train Loss: -8.0478, Val Loss: -7.3752 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:40.417520Z [info     ] Epoch 37/300, Train Loss: -8.0091, Val Loss: -7.8431 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:40.538084Z [info     ] Epoch 38/300, Train Loss: -8.0110, Val Loss: -7.7098 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:40.706988Z [info     ] Epoch 39/300, Train Loss: -8.0433, Val Loss: -7.7738 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:40.826549Z [info     ] Epoch 40/300, Train Loss: -8.0707, Val Loss: -7.6572 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:40.965986Z [info     ] Epoch 41/300, Train Loss: -7.9095, Val Loss: -7.9724 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.081509Z [info     ] Epoch 42/300, Train Loss: -8.1149, Val Loss: -7.9084 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.192169Z [info     ] Epoch 43/300, Train Loss: -8.1280, Val Loss: -7.8673 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.303842Z [info     ] Epoch 44/300, Train Loss: -8.2318, Val Loss: -7.9373 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.422757Z [info     ] Epoch 45/300, Train Loss: -8.2275, Val Loss: -8.0421 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.538063Z [info     ] Epoch 46/300, Train Loss: -8.2601, Val Loss: -7.8559 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.687153Z [info     ] Epoch 47/300, Train Loss: -8.1824, Val Loss: -8.0058 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.804730Z [info     ] Epoch 48/300, Train Loss: -8.1177, Val Loss: -8.0086 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:41.933614Z [info     ] Epoch 49/300, Train Loss: -8.3098, Val Loss: -7.9192 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:42.062089Z [info     ] Epoch 50/300, Train Loss: -8.3028, Val Loss: -8.0258 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:42.293959Z [info     ] Epoch 51/300, Train Loss: -8.3764, Val Loss: -7.7935 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:42.525137Z [info     ] Epoch 52/300, Train Loss: -8.1509, Val Loss: -7.6487 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:42.758415Z [info     ] Epoch 53/300, Train Loss: -8.2843, Val Loss: -7.4272 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:42.989050Z [info     ] Epoch 54/300, Train Loss: -8.3262, Val Loss: -8.0367 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:43.249442Z [info     ] Epoch 55/300, Train Loss: -8.3306, Val Loss: -7.8095 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:43.477246Z [info     ] Epoch 56/300, Train Loss: -8.2864, Val Loss: -7.8965 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:43.718292Z [info     ] Epoch 57/300, Train Loss: -8.3360, Val Loss: -7.9182 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:43.977714Z [info     ] Epoch 58/300, Train Loss: -8.4234, Val Loss: -8.0912 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:44.210854Z [info     ] Epoch 59/300, Train Loss: -8.4042, Val Loss: -7.9156 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:44.505379Z [info     ] Epoch 60/300, Train Loss: -8.3599, Val Loss: -8.0426 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:44.741014Z [info     ] Epoch 61/300, Train Loss: -8.4327, Val Loss: -7.7926 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:44.966428Z [info     ] Epoch 62/300, Train Loss: -8.4496, Val Loss: -8.1434 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:45.222750Z [info     ] Epoch 63/300, Train Loss: -8.4308, Val Loss: -7.7031 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:45.424074Z [info     ] Epoch 64/300, Train Loss: -8.4243, Val Loss: -7.9482 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:45.635034Z [info     ] Epoch 65/300, Train Loss: -8.3638, Val Loss: -7.8930 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:45.854763Z [info     ] Epoch 66/300, Train Loss: -8.3992, Val Loss: -8.1805 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:46.060693Z [info     ] Epoch 67/300, Train Loss: -8.4667, Val Loss: -7.9394 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:46.260879Z [info     ] Epoch 68/300, Train Loss: -8.4766, Val Loss: -7.9986 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:46.494109Z [info     ] Epoch 69/300, Train Loss: -8.4261, Val Loss: -7.1756 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:46.677562Z [info     ] Epoch 70/300, Train Loss: -8.4844, Val Loss: -7.9768 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:46.901428Z [info     ] Epoch 71/300, Train Loss: -8.5638, Val Loss: -7.8732 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:47.116429Z [info     ] Epoch 72/300, Train Loss: -8.4846, Val Loss: -7.8639 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:47.314626Z [info     ] Epoch 73/300, Train Loss: -8.5924, Val Loss: -7.3458 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:47.636993Z [info     ] Epoch 74/300, Train Loss: -8.4374, Val Loss: -7.8877 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:47.854721Z [info     ] Epoch 75/300, Train Loss: -8.4940, Val Loss: -7.2659 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:47.976593Z [info     ] Epoch 76/300, Train Loss: -8.4524, Val Loss: -7.6778 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.085568Z [info     ] Epoch 77/300, Train Loss: -8.4236, Val Loss: -7.4295 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.200245Z [info     ] Epoch 78/300, Train Loss: -8.4710, Val Loss: -7.9160 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.325114Z [info     ] Epoch 79/300, Train Loss: -8.4732, Val Loss: -7.8595 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.447041Z [info     ] Epoch 80/300, Train Loss: -8.5400, Val Loss: -7.8982 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.593471Z [info     ] Epoch 81/300, Train Loss: -8.5654, Val Loss: -7.8564 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.703286Z [info     ] Epoch 82/300, Train Loss: -8.5110, Val Loss: -8.0259 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.809561Z [info     ] Epoch 83/300, Train Loss: -8.5098, Val Loss: -7.6046 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:48.921284Z [info     ] Epoch 84/300, Train Loss: -8.5124, Val Loss: -7.8276 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.026811Z [info     ] Epoch 85/300, Train Loss: -8.5138, Val Loss: -7.8840 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.130300Z [info     ] Epoch 86/300, Train Loss: -8.6101, Val Loss: -7.9249 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.138834Z [info     ] Early stopping triggered at epoch 86. [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.25, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.190994Z [info     ] Loaded best model state with validation loss: -8.1805 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.72, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.201120Z [info     ] Performing final evaluation on the full training dataset... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.72, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.267649Z [info     ] Final model training completed. [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.38, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.276818Z [info     ] --- Stage 3 Artifacts: Saving Final Model Assets --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.26, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.292300Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.assets_pytorch artifact_path=obmiq_training_pytorch\\model_assets_pytorch.pkl context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.32, 'cpu_percent': 0.0} description=包含模型权重、超参数和预处理器的PyTorch模型资产包。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:49.309017Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.32, 'cpu_percent': 0.0} description=最终模型训练过程中的损失变化历史。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:49.346328Z [info     ] Plotting final training history... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.32, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.367932Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.training_history\n", "2025-07-24T15:00:49.381074Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=final_training_history base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.54, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:49.676131Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1583.46, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\final_training_history.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\final_training_history.svg']\n", "2025-07-24T15:00:49.695581Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1583.46, 'cpu_percent': 0.0} description=最终模型训练的损失曲线图。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:49.721667Z [info     ] Saving final model evaluation data and generating plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1583.46, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.890271Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_model_evaluation artifact_path=obmiq_training_pytorch\\final_model_evaluation.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1586.91, 'cpu_percent': 0.0} description=最终模型在全部训练数据上的预测和残差结果。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:49.904385Z [info     ] Generating evaluation plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1586.91, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:49.936619Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1588.64, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:00:49.949911Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1588.64, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:50.361459Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1611.34, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.svg']\n", "2025-07-24T15:00:50.394765Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1609.64, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:50.438635Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1612.08, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:00:50.451456Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1612.08, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:50.872274Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1634.7, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.svg']\n", "2025-07-24T15:00:50.890113Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1633.01, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:50.939055Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1635.41, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:00:50.951249Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1635.41, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:51.395442Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.31, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.svg']\n", "2025-07-24T15:00:51.418031Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1658.62, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:51.465844Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.35, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:00:51.477653Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.35, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:51.955156Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1685.05, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.svg']\n", "2025-07-24T15:00:51.977105Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1683.36, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:52.028263Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1685.8, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:00:52.082783Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1686.32, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:52.461054Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1711.02, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.svg']\n", "2025-07-24T15:00:52.473372Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1709.33, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:52.528162Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1711.82, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:00:52.649879Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1711.82, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:53.038994Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1736.6, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.svg']\n", "2025-07-24T15:00:53.053962Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1734.91, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:53.073860Z [info     ] --- Stage 3: Model Interpretability Analysis (Captum) --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1734.91, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:53.176556Z [info     ] Running Captum analysis for target: DT2_P50... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1736.74, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:55.716101Z [info     ] Running Captum analysis for target: DPHIT_NMR... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1843.79, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:58.416696Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1843.95, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-24T15:00:58.431595Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1843.95, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-24T15:00:58.447958Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1843.95, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:58.523732Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50_data.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1845.46, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50) operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:58.554638Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1846.18, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-24T15:00:58.573398Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1846.18, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:58.866726Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1870.91, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.svg']\n", "2025-07-24T15:00:58.881258Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1870.91, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-24T15:00:58.891666Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1870.91, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-24T15:00:58.950212Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1870.91, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:59.004248Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR_data.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1871.52, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR) operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:00:59.028250Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1872.08, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-24T15:00:59.043092Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1872.08, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:00:59.328023Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1896.78, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.svg']\n", "2025-07-24T15:00:59.369788Z [warning  ] 井名列 'WELL_NO' 不是字符串类型，将进行区分大小写的精确匹配。这可能导致因大小写或空格问题而找不到井。 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1896.78, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.393438Z [info     ] 找到样本: 井='c-1', 目标深度=6311.73, 实际深度=6311.80 (索引=9) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1896.84, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.411184Z [info     ] 找到样本: 井='c-1', 目标深度=6313.38, 实际深度=6313.32 (索引=19) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1897.44, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.417868Z [info     ] 找到样本: 井='c-1', 目标深度=6318.8, 实际深度=6318.81 (索引=53) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1897.44, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.426797Z [info     ] 找到样本: 井='c-1', 目标深度=6334.55, 实际深度=6334.51 (索引=140) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1897.45, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.433870Z [info     ] 找到样本: 井='c-1', 目标深度=6409.94, 实际深度=6409.94 (索引=635) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1897.45, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.441810Z [info     ] 找到样本: 井='c-1', 目标深度=6426.71, 实际深度=6426.71 (索引=745) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1897.45, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.449990Z [info     ] 找到样本: 井='c-1', 目标深度=6440.34, 实际深度=6440.27 (索引=834) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1897.45, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.457629Z [info     ] 找到样本: 井='t-1', 目标深度=6426.51, 实际深度=6426.56 (索引=1080) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1897.8, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.465639Z [info     ] 找到样本: 井='t-1', 目标深度=6471.0, 实际深度=6471.06 (索引=1338) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1898.79, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.472707Z [info     ] 找到样本: 井='t-1', 目标深度=6552.36, 实际深度=6552.29 (索引=1846) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1898.79, 'cpu_percent': 0.0}\n", "2025-07-24T15:00:59.515699Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1898.79, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:00:59.560117Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1899.47, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:02.330741Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1951.28, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.svg']\n", "2025-07-24T15:01:02.369597Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1951.3, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:02.453105Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1952.07, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:05.275653Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2004.34, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:05.298405Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2004.38, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:05.346875Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2005.34, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:08.182222Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2058.96, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.svg']\n", "2025-07-24T15:01:08.214223Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2059.03, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:08.261832Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2060.08, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:11.197481Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2040.5, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:11.242170Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2040.5, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:11.284592Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2040.49, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:14.216311Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2090.3, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.svg']\n", "2025-07-24T15:01:14.242651Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2090.4, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:14.330415Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2091.14, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:17.132008Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2144.34, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:17.159381Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2144.41, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:17.198496Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2145.2, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:19.966294Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2198.79, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.svg']\n", "2025-07-24T15:01:19.993894Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2198.86, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:20.036089Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2199.68, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:22.842565Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2253.44, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:22.864544Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2253.51, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:22.944433Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2254.25, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:26.024461Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2184.65, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.svg']\n", "2025-07-24T15:01:26.044416Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2184.75, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:26.098015Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2184.76, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:29.041711Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2234.89, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:29.076137Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2234.92, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:29.127261Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2235.23, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:32.184501Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2288.56, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.svg']\n", "2025-07-24T15:01:32.282577Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2288.6, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:32.345951Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2289.73, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:35.087419Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2343.11, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:35.119314Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2343.15, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:35.163827Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2344.01, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:37.937454Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2397.52, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.svg']\n", "2025-07-24T15:01:37.975300Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2397.58, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:38.021651Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2398.58, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:41.292592Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2330.36, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:41.315068Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2330.53, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:41.425340Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2331.04, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:44.398148Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2381.2, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.svg']\n", "2025-07-24T15:01:44.424606Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2381.2, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:44.473630Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2381.24, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:47.532537Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2432.73, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:47.557126Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2432.77, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:47.617168Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2433.89, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:50.699379Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2486.7, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.svg']\n", "2025-07-24T15:01:50.723879Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2486.87, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:50.765713Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2487.92, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:53.771344Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2541.18, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:53.796442Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2541.24, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:53.838077Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2542.0, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:56.698125Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2595.63, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.svg']\n", "2025-07-24T15:01:56.807754Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2595.71, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:01:56.849898Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2596.77, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:01:59.775625Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2504.28, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.svg']\n", "2025-07-24T15:01:59.787122Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2504.28, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_sequence_attributions_dir' operation=register_artifact\n", "2025-07-24T15:01:59.808374Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_sequence_attributions_dir artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2504.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:01:59.829902Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2504.28, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir' operation=register_artifact\n", "2025-07-24T15:01:59.925175Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_saliency_examples_dir artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2504.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:01:59.968286Z [info     ] --- Stage 5: Exporting Model to ONNX --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2504.28, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.051590Z [info     ] Model successfully exported to ONNX format at: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_training_pytorch\\obmiq_model.onnx [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.1, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.060441Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.onnx_model artifact_path=obmiq_training_pytorch\\obmiq_model.onnx context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.1, 'cpu_percent': 0.0} description=可用于跨平台部署的ONNX格式模型。 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:00.078058Z [info     ] ===== OBMIQ Training Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.11, 'cpu_percent': 0.0}\n", "训练步骤完成。最佳超参数: {'cnn_filters': 32.0, 'cnn_kernel_size': 5.0, 'dropout_rate': 0.3456332143553949, 'learning_rate': 0.006597106985788541, 'mlp_units': 64.0, 'weight_decay': 0.0005263823786463438}\n", "\n", "--- 开始 OBMIQ 预测步骤 (PyTorch) ---\n", "2025-07-24T15:02:00.119373Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.14, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.139349Z [info     ] Validating prediction inputs... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.14, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.150478Z [info     ] Loading model assets and reconstructing model... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.14, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.162112Z [info     ] Preprocessing prediction data... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.14, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.171779Z [info     ] 成功将模型所需的逻辑曲线名解析为预测数据的物理列名。     [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.15, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.185465Z [info     ] Performing inference on device: cuda... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2513.11, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.201674Z [info     ] Formatting predictions and saving artifacts... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2434.92, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.424741Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.datasets.predictions artifact_path=obmiq_prediction_pytorch\\predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2441.29, 'cpu_percent': 0.0} description=包含原始输入和模型预测结果的数据集 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:00.431601Z [info     ] Ground truth found in prediction data. Generating evaluation plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2441.29, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.695496Z [info     ] Updated prediction snapshot with residual columns at: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch\\predictions.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2441.36, 'cpu_percent': 0.0}\n", "2025-07-24T15:02:00.731573Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2446.22, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:02:00.751944Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2446.31, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:02:01.165567Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2468.96, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.svg']\n", "2025-07-24T15:02:01.179604Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2466.05, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:01.290257Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2469.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:02:01.300373Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2469.64, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:02:01.701856Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2492.27, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.svg']\n", "2025-07-24T15:02:01.723199Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2489.36, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:01.842831Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2492.82, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:02:01.853844Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2492.91, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:02:02.251317Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2517.63, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.svg']\n", "2025-07-24T15:02:02.272582Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_plot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2514.71, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:02.383015Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:02:02.392755Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.48, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:02:02.835805Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2543.21, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.svg']\n", "2025-07-24T15:02:02.857961Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_plot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2540.29, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:02.968758Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2544.14, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:02:03.024635Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2544.28, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:02:03.359420Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2568.98, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.svg']\n", "2025-07-24T15:02:03.371595Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_hist_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2566.07, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50 operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:03.481401Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2570.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:02:03.532409Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2570.13, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:02:03.893387Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2594.83, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_224858\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.svg']\n", "2025-07-24T15:02:03.918106Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_hist_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2591.92, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr operation=register_artifact run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:03.979986Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2591.92, 'cpu_percent': 0.0}\n", "预测步骤完成。\n", "\n", "预测结果已保存至: output01\\obmiq_run_pytorch_20250724_224858\\obmiq_prediction_pytorch\\predictions.csv\n", "\n", "预测结果预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL_NO</th>\n", "      <th>MD</th>\n", "      <th>DT2_P50</th>\n", "      <th>DT2_P50_PRED</th>\n", "      <th>DPHIT_NMR</th>\n", "      <th>DPHIT_NMR_PRED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C-1</td>\n", "      <td>6310.4268</td>\n", "      <td>0.104076</td>\n", "      <td>0.113324</td>\n", "      <td>0.065477</td>\n", "      <td>0.038003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C-1</td>\n", "      <td>6310.5792</td>\n", "      <td>-0.148789</td>\n", "      <td>0.047718</td>\n", "      <td>0.061988</td>\n", "      <td>0.047133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C-1</td>\n", "      <td>6310.7316</td>\n", "      <td>-0.211292</td>\n", "      <td>-0.066034</td>\n", "      <td>0.052548</td>\n", "      <td>0.029831</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C-1</td>\n", "      <td>6310.8840</td>\n", "      <td>-0.245636</td>\n", "      <td>0.003856</td>\n", "      <td>0.038409</td>\n", "      <td>0.014436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C-1</td>\n", "      <td>6311.0364</td>\n", "      <td>0.112414</td>\n", "      <td>0.108042</td>\n", "      <td>0.051449</td>\n", "      <td>0.032005</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  WELL_NO         MD   DT2_P50  DT2_P50_PRED  DPHIT_NMR  DPHIT_NMR_PRED\n", "0     C-1  6310.4268  0.104076      0.113324   0.065477        0.038003\n", "1     C-1  6310.5792 -0.148789      0.047718   0.061988        0.047133\n", "2     C-1  6310.7316 -0.211292     -0.066034   0.052548        0.029831\n", "3     C-1  6310.8840 -0.245636      0.003856   0.038409        0.014436\n", "4     C-1  6311.0364  0.112414      0.108042   0.051449        0.032005"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:02:04.068108Z [info     ] Run marked as successful       [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2591.72, 'cpu_percent': 0.0} operation=mark_success run_id=20250724-144858-1ebd9f6f\n", "2025-07-24T15:02:04.084722Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2591.72, 'cpu_percent': 0.0} duration_seconds=785.781 manifest_path=output01\\obmiq_run_pytorch_20250724_224858\\manifest.json operation=finalize run_id=20250724-144858-1ebd9f6f status=COMPLETED\n"]}], "source": ["if project:\n", "    # 定义输出目录\n", "    output_dir = Path(\"./output01\") # 使用新的输出目录\n", "    output_dir.mkdir(exist_ok=True)\n", "\n", "    run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"obmiq_run_pytorch\")\n", "    # 使用 RunContext 包裹整个工作流\n", "    with RunContext(run_dir=output_dir / run_dir_name, overwrite=True) as ctx:\n", "\n", "        # --- 1. 训练步骤 ---\n", "        print(\"--- 开始 OBMIQ 训练步骤 (PyTorch) ---\")\n", "\n", "        # a. 定义训练配置\n", "        training_config = ObmiqTrainingConfig(\n", "            n_trials=200,\n", "            max_epochs_per_trial=150,\n", "            final_train_epochs=300,\n", "            patience=20,\n", "            batch_size=64\n", "        )\n", "\n", "        # b. 定义facade函数的关键字参数\n", "        training_kwargs = {\n", "            \"sequence_feature\": \"PHI_T2_DIST_CUM\",\n", "            \"normalization_feature\": \"PHIT_NMR\",\n", "            \"tabular_features\": [\"SWB_NMR\", \"BFV_NMR\", \"PHIE_NMR\", \"RD_LOG10\", \"CN\", \"DRES\", \"DEN\", \"RS_LOG10\", \"DT\", \"BVI_NMR\", \"SDR_PROXY\"],\n", "            \"target_features\": [\"DT2_P50\", \"DPHIT_NMR\"],\n", "            \"grouping_feature\": well_no_name, # 使用新的参数名\n", "            \"t2_time_axis\": t2_time_array,\n", "        }\n", "\n", "        saliency_samples = [(\"c-1\",6311.73),\n", "                            (\"c-1\",6313.38),\n", "                            (\"c-1\",6318.8),\n", "                            (\"c-1\",6334.55),\n", "                            (\"c-1\",6409.94),\n", "                            (\"c-1\",6426.71),\n", "                            (\"c-1\",6440.34),\n", "                            (\"t-1\",6426.51),\n", "                            (\"t-1\",6471.0),\n", "                            (\"t-1\",6552.36)]\n", "\n", "        # c. 执行训练步骤\n", "        # 新版facade直接接收WpDataFrameBundle，无需手动准备X, y\n", "        training_results = run_obmiq_training_step(\n", "            config=training_config,\n", "            ctx=ctx,\n", "            train_bundle=train_bundle,\n", "            depth_feature=depth_name,\n", "            saliency_samples=saliency_samples,\n", "            **training_kwargs\n", "        )\n", "\n", "        print(f\"训练步骤完成。最佳超参数: {training_results.get('best_hyperparameters')}\")\n", "\n", "        # --- 2. 预测步骤 ---\n", "        print(\"\\n--- 开始 OBMIQ 预测步骤 (PyTorch) ---\")\n", "\n", "        # a. 从上下文中获取模型资产的路径\n", "        model_assets_path = ctx.get_artifact_path(ObmiqTrainingArtifacts.MODEL_ASSETS.value)\n", "\n", "        # b. 使用产物处理器加载模型资产\n", "        handler = ObmiqArtifactHandler()\n", "        model_assets = handler.load_model_assets(model_assets_path)\n", "\n", "        # c. 定义预测配置\n", "        prediction_config = ObmiqPredictionConfig()\n", "\n", "        # d. 定义facade函数的关键字参数\n", "        prediction_kwargs = {\n", "            \"source_t2_time_axis\": t2_time_array, # 假设预测数据与训练数据T2轴相同\n", "            \"output_curve_names\": (\"DT2_P50_PRED\", \"DPHIT_NMR_PRED\")\n", "        }\n", "\n", "        # e. 执行预测步骤\n", "        prediction_results = run_obmiq_prediction_step(\n", "            config=prediction_config,\n", "            ctx=ctx,\n", "            model_assets=model_assets,\n", "            prediction_bundle=prediction_bundle,\n", "            **prediction_kwargs\n", "        )\n", "\n", "        print(\"预测步骤完成。\")\n", "\n", "        # --- 3. 结果检查 ---\n", "        # 新版facade返回的是预测结果文件的路径\n", "        prediction_path = Path(prediction_results[\"output_path\"])\n", "        if prediction_path.exists():\n", "            print(f\"\\n预测结果已保存至: {prediction_path}\")\n", "            # 从CSV加载结果进行预览\n", "            predicted_df = pd.read_csv(prediction_path)\n", "            print(\"\\n预测结果预览:\")\n", "            display(predicted_df[[\"WELL_NO\", \"MD\", \"DT2_P50\", \"DT2_P50_PRED\", \"DPHIT_NMR\", \"DPHIT_NMR_PRED\"]].head())\n", "        else:\n", "            print(f\"❌ 预测结果文件未找到: {prediction_path}\")\n", "\n", "else:\n", "    print(\"因数据加载失败，跳过工作流执行。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}