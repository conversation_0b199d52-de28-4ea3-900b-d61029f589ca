#!/usr/bin/env python3
"""数据集合并服务层模块。

提供WpWellProject的数据集合并功能，支持通过连续型转换方式合并两个数据集。

Architecture
------------
层次/依赖: models/service层，数据集合并业务逻辑
设计原则: Service Layer模式、无状态设计、类型安全
性能特征: 复用现有服务、智能插值选择、内存优化

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_DDS_logwp_数据集合并.md》§3 - 服务层设计
- 《SCAPE_SAD_软件架构设计.md》§4.12 - 内部服务层设计
"""

from __future__ import annotations

import copy
from typing import TYPE_CHECKING

import pandas as pd

from logwp.models.exceptions import (
    WpDatasetNotFoundError, WpValidationError, WpDataError
)
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger
from logwp.infra.depth_utils import standardize_depth_unit, are_depth_units_equivalent

if TYPE_CHECKING:
    from logwp.models.well_project import WpWellProject
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase

# 导入数据集类型用于类型判断
from logwp.models.datasets.continuous import WpContinuousDataset
from logwp.models.datasets.discrete import WpDiscreteDataset
from logwp.models.datasets.interval import WpIntervalDataset
from logwp.models.curve.metadata import CurveMetadata

logger = get_logger(__name__)


def merge_datasets_via_continuous(
    project: WpWellProject,
    left_dataset: str,
    left_curves: list[str],
    left_query: str | None,
    right_dataset: str,
    right_curves: list[str],
    right_query: str | None,
    *,
    merge_sampling_interval: float | None = None,
    interpolation_method: str = "nearest",
    result_mode: str = "full",
    new_dataset_name: str | None = None
) -> WpDepthIndexedDatasetBase:
    """数据集合并服务层实现（无状态纯函数）。

    **设计原则**：
    - SVC-1: 职责分离 - 专注数据集合并业务逻辑
    - SVC-2: 无状态服务 - 纯函数设计，无副作用
    - SVC-3: 内部使用 - 仅供logwp包内部调用
    - SVC-4: 操作原子性 - 完整的数据集合并操作
    - SVC-5: 参数通用性 - 依赖基础数据结构和接口
    - SVC-6: 命名语义化 - 函数名明确表示合并操作

    Args:
        project: WpWellProject实例（提供数据集访问）
        left_dataset: 左数据集名称
        left_curves: 左数据集要提取的曲线列表（紧凑格式）
        left_query: 左数据集查询条件（可选，使用extract_curves语法）
        right_dataset: 右数据集名称
        right_curves: 右数据集要提取的曲线列表（紧凑格式）
        right_query: 右数据集查询条件（可选，使用extract_curves语法）
        merge_sampling_interval: 合并后的深度采样间隔，None表示自动确定
        interpolation_method: 插值方法，默认"nearest"
        result_mode: 结果模式，"full"保留所有深度点，"compact"移除全空行
        new_dataset_name: 新数据集名称，None表示自动生成

    Returns:
        WpDepthIndexedDatabaseBase: 合并后的数据集

    Raises:
        WpDatasetNotFoundError: 源数据集不存在
        WpValidationError: 深度单位不一致或参数无效
        WpDataError: 数据集状态异常或合并失败

    Note:
        - 服务函数不修改输入的project状态
        - 返回新创建的数据集，不添加到project中
        - 复用现有的成熟服务层组件

    Warning:
        此方法会通过重采样改变原始深度值，适用于需要统一深度采样间隔的合并场景。
        如需保持原始深度值，请考虑其他合并方法。

    Examples:
        >>> # 基本合并
        >>> merged_dataset = merge_datasets_via_continuous(
        ...     project=project,
        ...     left_dataset="logs",
        ...     left_curves=["GR", "DEN", "PHIT"],
        ...     left_query=None,
        ...     right_dataset="core",
        ...     right_curves=["PERM", "GR"],  # GR会被重命名为GR_1
        ...     right_query=None,
        ...     merge_sampling_interval=0.5
        ... )

        >>> # 带查询条件的合并
        >>> filtered_merged = merge_datasets_via_continuous(
        ...     project=project,
        ...     left_dataset="logs",
        ...     left_curves=["GR", "DEN"],
        ...     left_query="${GR} > 50",
        ...     right_dataset="nmr",
        ...     right_curves=["T2_VALUE", "PHIT"],
        ...     right_query="${PHIT} > 0.1",
        ...     result_mode="compact"
        ... )

    References:
        《SCAPE_DDS_logwp_数据集合并.md》§3.2 - 实现步骤详解
    """
    logger.info(
        "开始数据集合并",
        operation="merge_datasets_via_continuous",
        left_dataset=left_dataset,
        right_dataset=right_dataset,
        left_curves_count=len(left_curves),
        right_curves_count=len(right_curves),
        has_left_query=left_query is not None,
        has_right_query=right_query is not None,
        auto_sampling_interval=merge_sampling_interval is None,
        interpolation_method=interpolation_method,
        result_mode=result_mode
    )

    try:
        # 步骤1: 参数验证和数据提取
        _validate_merge_parameters(
            project, left_dataset, right_dataset, merge_sampling_interval,
            interpolation_method, result_mode
        )

        # 提取左右数据集（复用现有服务）
        from logwp.models.datasets.internal import curve_extraction

        left_extracted = curve_extraction.extract_curves(
            project, left_dataset, f"{left_dataset}_left_temp", left_curves, left_query
        )
        right_extracted = curve_extraction.extract_curves(
            project, right_dataset, f"{right_dataset}_right_temp", right_curves, right_query
        )

        # 调试日志：检查提取后的曲线元数据
        logger.debug(
            "曲线提取完成，检查元数据",
            operation="merge_datasets_via_continuous",
            left_dataset_name=left_extracted.name,
            left_df_columns=list(left_extracted.df.columns),
            left_well_curves=left_extracted.curve_metadata.get_well_identifier_curves(),
            left_depth_curves=left_extracted.curve_metadata.get_depth_reference_curves(),
            right_dataset_name=right_extracted.name,
            right_df_columns=list(right_extracted.df.columns),
            right_well_curves=right_extracted.curve_metadata.get_well_identifier_curves(),
            right_depth_curves=right_extracted.curve_metadata.get_depth_reference_curves()
        )

        # 调试日志：检查提取后的曲线元数据
        logger.debug(
            "曲线提取完成，检查元数据",
            operation="merge_datasets_via_continuous",
            left_well_curves=left_extracted.curve_metadata.get_well_identifier_curves(),
            left_depth_curves=left_extracted.curve_metadata.get_depth_reference_curves(),
            right_well_curves=right_extracted.curve_metadata.get_well_identifier_curves(),
            right_depth_curves=right_extracted.curve_metadata.get_depth_reference_curves()
        )

        # 检查深度单位一致性
        _validate_depth_unit_consistency(left_extracted, right_extracted)

        # 步骤2: 确定合并参数
        if merge_sampling_interval is None:
            merge_sampling_interval = _determine_merge_sampling_interval(
                left_extracted, right_extracted
            )

        per_well_depth_ranges = _calculate_per_well_depth_ranges(left_extracted, right_extracted)

        right_curves_renamed, rename_mapping = _resolve_curve_name_conflicts(
            [curve.name for curve in left_extracted.curve_metadata.list_curves()],
            [curve.name for curve in right_extracted.curve_metadata.list_curves()],
            left_extracted.curve_metadata,
            right_extracted.curve_metadata
        )

        logger.info(
            "合并参数确定完成",
            operation="merge_datasets_via_continuous",
            merge_sampling_interval=merge_sampling_interval,
            per_well_depth_ranges=per_well_depth_ranges,
            rename_mapping=rename_mapping
        )

        # 步骤3: 按井分别转换为连续型数据集（SQL FULL JOIN逻辑）
        left_continuous = _convert_dataset_to_continuous_per_well(
            left_extracted, merge_sampling_interval, per_well_depth_ranges, interpolation_method
        )
        right_continuous = _convert_dataset_to_continuous_per_well(
            right_extracted, merge_sampling_interval, per_well_depth_ranges,
            interpolation_method, rename_mapping
        )

        # 调试日志：检查转换后的数据集
        logger.debug(
            "转换为连续型数据集完成",
            operation="merge_datasets_via_continuous",
            left_continuous_columns=list(left_continuous.df.columns),
            left_continuous_well_curves=left_continuous.curve_metadata.get_well_identifier_curves(),
            left_continuous_depth_curves=left_continuous.curve_metadata.get_depth_reference_curves(),
            right_continuous_columns=list(right_continuous.df.columns),
            right_continuous_well_curves=right_continuous.curve_metadata.get_well_identifier_curves(),
            right_continuous_depth_curves=right_continuous.curve_metadata.get_depth_reference_curves()
        )

        # 步骤4: 执行合并
        merged_df = _merge_continuous_dataframes_by_depth(
            left_continuous.df, left_continuous.curve_metadata,
            right_continuous.df, right_continuous.curve_metadata
        )

        # 确保合并后DataFrame符合CDP-1索引规范
        merged_df = _ensure_dataframe_index_compliance(merged_df)

        # 合并曲线元数据
        merged_metadata = _merge_curve_metadata(
            left_continuous.curve_metadata,
            right_continuous.curve_metadata,
            rename_mapping,
            merge_sampling_interval
        )

        # 步骤5: 生成最终结果
        final_dataset_name = new_dataset_name or f"{left_dataset}_{right_dataset}_merged"

        if result_mode == "compact":
            # 移除全空行
            merged_df = _remove_all_null_rows(merged_df)

            # 确保处理后DataFrame仍符合CDP-1索引规范
            merged_df = _ensure_dataframe_index_compliance(merged_df)

            # 智能类型判断
            result_dataset = _create_final_dataset_with_smart_type_detection(
                merged_df, merged_metadata, merge_sampling_interval, final_dataset_name
            )
        else:
            # full模式：直接创建连续型数据集
            result_dataset = WpContinuousDataset.create_with_data(
                name=final_dataset_name,
                df=merged_df,
                curve_metadata=merged_metadata,
                depth_sampling_rate=merge_sampling_interval
            )

        logger.info(
            "数据集合并完成",
            operation="merge_datasets_via_continuous",
            result_dataset_name=str(result_dataset.name),
            result_dataset_type=type(result_dataset).__name__,
            result_rows=len(result_dataset.df),
            result_columns=len(result_dataset.df.columns)
        )

        return result_dataset

    except Exception as e:
        logger.error(
            "数据集合并失败",
            operation="merge_datasets_via_continuous",
            left_dataset=left_dataset,
            right_dataset=right_dataset,
            error_type=type(e).__name__,
            error_message=str(e)
        )
        raise


def _validate_merge_parameters(
    project: WpWellProject,
    left_dataset: str,
    right_dataset: str,
    merge_sampling_interval: float | None,
    interpolation_method: str,
    result_mode: str
) -> None:
    """验证数据集合并参数。

    Args:
        project: WpWellProject实例
        left_dataset: 左数据集名称
        right_dataset: 右数据集名称
        merge_sampling_interval: 合并采样间隔
        interpolation_method: 插值方法
        result_mode: 结果模式

    Raises:
        WpDatasetNotFoundError: 数据集不存在
        WpValidationError: 参数无效
    """
    # 验证数据集存在性
    if left_dataset not in project.datasets:
        raise WpDatasetNotFoundError(
            f"左数据集 '{left_dataset}' 不存在",
            context=ErrorContext(
                operation="validate_merge_parameters",
                additional_info={
                    "left_dataset": left_dataset,
                    "available_datasets": list(project.datasets.keys())
                }
            )
        )

    if right_dataset not in project.datasets:
        raise WpDatasetNotFoundError(
            f"右数据集 '{right_dataset}' 不存在",
            context=ErrorContext(
                operation="validate_merge_parameters",
                additional_info={
                    "right_dataset": right_dataset,
                    "available_datasets": list(project.datasets.keys())
                }
            )
        )

    # 验证采样间隔
    if merge_sampling_interval is not None and merge_sampling_interval <= 0:
        raise WpValidationError(
            "合并采样间隔必须为正数",
            context=ErrorContext(
                operation="validate_merge_parameters",
                additional_info={
                    "merge_sampling_interval": merge_sampling_interval,
                    "requirement": "merge_sampling_interval > 0"
                }
            )
        )

    # 验证插值方法
    if not isinstance(interpolation_method, str) or not interpolation_method.strip():
        raise WpValidationError(
            "插值方法必须为非空字符串",
            context=ErrorContext(
                operation="validate_merge_parameters",
                additional_info={
                    "interpolation_method": interpolation_method,
                    "requirement": "non-empty string"
                }
            )
        )

    # 验证结果模式
    if result_mode not in ["full", "compact"]:
        raise WpValidationError(
            "结果模式必须为 'full' 或 'compact'",
            context=ErrorContext(
                operation="validate_merge_parameters",
                additional_info={
                    "result_mode": result_mode,
                    "valid_modes": ["full", "compact"]
                }
            )
        )


def _validate_depth_unit_consistency(
    left_dataset: WpDepthIndexedDatasetBase,
    right_dataset: WpDepthIndexedDatasetBase
) -> None:
    """检查两个数据集的深度单位一致性。

    Args:
        left_dataset: 左数据集
        right_dataset: 右数据集

    Raises:
        WpDepthUnitMismatchError: 深度单位不一致

    Note:
        - 使用数据集的get_depth_reference_unit()方法获取深度单位
        - 支持语义等价的深度单位（如METERS和METRES）
    """
    # 导入异常类型
    from logwp.models.exceptions import WpDepthUnitMismatchError

    left_unit = left_dataset.get_depth_reference_unit()
    right_unit = right_dataset.get_depth_reference_unit()

    if not are_depth_units_equivalent(left_unit, right_unit):
        raise WpDepthUnitMismatchError(
            f"数据集深度单位不一致: 左数据集={left_unit}, 右数据集={right_unit}",
            context=ErrorContext(
                operation="validate_depth_unit_consistency",
                additional_info={
                    "left_dataset": str(left_dataset.name),
                    "right_dataset": str(right_dataset.name),
                    "left_unit": left_unit,
                    "right_unit": right_unit,
                    "standardized_left": standardize_depth_unit(left_unit),
                    "standardized_right": standardize_depth_unit(right_unit)
                }
            )
        )

    logger.debug(
        "深度单位一致性验证通过",
        operation="_validate_depth_unit_consistency",
        left_unit=left_unit,
        right_unit=right_unit,
        standardized_left=standardize_depth_unit(left_unit),
        standardized_right=standardize_depth_unit(right_unit)
    )





def _determine_merge_sampling_interval(
    left_dataset: WpDepthIndexedDatasetBase,
    right_dataset: WpDepthIndexedDatasetBase
) -> float:
    """自动确定合并后的深度采样间隔。

    算法规则（参考现有实现）：
    1. 两个continuous：取最小的depth_sampling_rate
    2. 一个discrete：取continuous和discrete的calculate_depth_sampling_rate最小值
    3. 一个interval：取另一个数据集的采样间隔（discrete用calculate_depth_sampling_rate）

    Args:
        left_dataset: 左数据集
        right_dataset: 右数据集

    Returns:
        float: 确定的采样间隔

    Note:
        - 使用数据集的depth_sampling_rate属性（continuous）
        - 使用数据集的calculate_depth_sampling_rate()方法（discrete）
        - interval数据集不参与采样间隔计算
    """
    left_type = type(left_dataset)
    right_type = type(right_dataset)

    # 获取左数据集的采样间隔
    if left_type == WpContinuousDataset:
        left_interval = left_dataset.depth_sampling_rate
    elif left_type == WpDiscreteDataset:
        left_interval = left_dataset.calculate_depth_sampling_rate()
    else:  # WpIntervalDataset
        left_interval = None

    # 获取右数据集的采样间隔
    if right_type == WpContinuousDataset:
        right_interval = right_dataset.depth_sampling_rate
    elif right_type == WpDiscreteDataset:
        right_interval = right_dataset.calculate_depth_sampling_rate()
    else:  # WpIntervalDataset
        right_interval = None

    # 根据算法规则确定最终采样间隔
    if left_interval is not None and right_interval is not None:
        # 两个都有采样间隔，取最小值
        return min(left_interval, right_interval)
    elif left_interval is not None:
        # 只有左数据集有采样间隔
        return left_interval
    elif right_interval is not None:
        # 只有右数据集有采样间隔
        return right_interval
    else:
        # 两个都是interval类型，使用默认值
        return 1.0  # 默认1米采样间隔


def _calculate_per_well_depth_ranges(
    left_dataset: WpDepthIndexedDatasetBase,
    right_dataset: WpDepthIndexedDatasetBase
) -> dict[str, tuple[float, float]]:
    """计算每口井的深度范围并集（SQL FULL JOIN逻辑）。

    Args:
        left_dataset: 左数据集
        right_dataset: 右数据集

    Returns:
        dict[str, tuple[float, float]]: {井名: (最小深度, 最大深度)}

    Note:
        实现SQL FULL JOIN语义：
        - 对于两个数据集都有的井：计算深度范围并集
        - 对于只在一个数据集中的井：使用该数据集的深度范围
        - 避免生成跨井的无意义深度点

    Examples:
        >>> # 左数据集：C-1(2500-2600), C-2(2700-2800)
        >>> # 右数据集：C-1(2550-2650), C-3(2900-3000)
        >>> # 结果：C-1(2500-2650), C-2(2700-2800), C-3(2900-3000)
    """
    left_wells = left_dataset.get_wells_depth_ranges()
    right_wells = right_dataset.get_wells_depth_ranges()

    # 获取所有井的并集
    all_wells = set(left_wells.keys()) | set(right_wells.keys())

    well_ranges = {}
    for well in all_wells:
        left_range = left_wells.get(well)
        right_range = right_wells.get(well)

        if left_range and right_range:
            # 两个数据集都有这口井：计算深度范围并集
            well_ranges[well] = (
                min(left_range[0], right_range[0]),
                max(left_range[1], right_range[1])
            )
        elif left_range:
            # 只有左数据集有这口井
            well_ranges[well] = left_range
        elif right_range:
            # 只有右数据集有这口井
            well_ranges[well] = right_range

    logger.debug(
        "按井深度范围计算完成",
        operation="calculate_per_well_depth_ranges",
        left_wells=left_wells,
        right_wells=right_wells,
        result_wells=well_ranges
    )

    return well_ranges


def _resolve_curve_name_conflicts(
    left_curves: list[str],
    right_curves: list[str],
    left_metadata: CurveMetadata,
    right_metadata: CurveMetadata
) -> tuple[list[str], dict[str, str]]:
    """处理曲线名冲突。

    Args:
        left_curves: 左数据集曲线名列表
        right_curves: 右数据集曲线名列表
        left_metadata: 左数据集曲线元数据
        right_metadata: 右数据集曲线元数据

    Returns:
        tuple[list[str], dict[str, str]]: (右数据集重命名后曲线列表, 重命名映射)

    Note:
        - 左数据集曲线名保持不变
        - 右数据集重名曲线按GR→GR_1, GR_1→GR_2的规则重命名
        - 井名和深度曲线不参与重命名，通过元数据属性识别
    """
    # 获取左右数据集的系统曲线（井名和深度曲线）
    left_system_curves = _get_system_curve_names(left_metadata)
    right_system_curves = _get_system_curve_names(right_metadata)

    left_curves_set = set(left_curves)
    rename_mapping = {}
    renamed_right_curves = []

    logger.debug(
        "开始处理曲线名冲突",
        operation="_resolve_curve_name_conflicts",
        left_system_curves=left_system_curves,
        right_system_curves=right_system_curves,
        left_curves=left_curves,
        right_curves=right_curves
    )

    for curve_name in right_curves:
        # 系统曲线（井名和深度曲线）不重命名
        if curve_name in right_system_curves:
            renamed_right_curves.append(curve_name)
            logger.debug(
                "系统曲线保持不变",
                operation="_resolve_curve_name_conflicts",
                curve_name=curve_name,
                curve_type="system"
            )
            continue

        # 检查是否与左数据集冲突
        if curve_name in left_curves_set:
            # 生成新名称
            new_name = _generate_unique_curve_name(curve_name, left_curves_set | set(renamed_right_curves))
            rename_mapping[curve_name] = new_name
            renamed_right_curves.append(new_name)
            logger.debug(
                "数据曲线重命名",
                operation="_resolve_curve_name_conflicts",
                original_name=curve_name,
                new_name=new_name,
                curve_type="data"
            )
        else:
            renamed_right_curves.append(curve_name)
            logger.debug(
                "数据曲线无冲突",
                operation="_resolve_curve_name_conflicts",
                curve_name=curve_name,
                curve_type="data"
            )

    return renamed_right_curves, rename_mapping


def _get_system_curve_names(metadata: CurveMetadata) -> set[str]:
    """获取系统曲线名称（井名和深度曲线）。

    Args:
        metadata: 曲线元数据

    Returns:
        set[str]: 系统曲线名称集合

    Note:
        - 通过is_well_identifier和depth_role属性识别系统曲线
        - 不依赖硬编码的曲线名称
    """
    system_curves = set()

    for curve in metadata.list_curves():
        # 井名曲线
        if curve.is_well_identifier:
            system_curves.add(curve.name)
        # 深度曲线
        elif curve.depth_role is not None:
            system_curves.add(curve.name)

    return system_curves


def _generate_unique_curve_name(base_name: str, existing_names: set[str]) -> str:
    """生成唯一的曲线名称。

    Args:
        base_name: 基础名称
        existing_names: 已存在的名称集合

    Returns:
        str: 唯一的曲线名称
    """
    counter = 1
    while True:
        new_name = f"{base_name}_{counter}"
        if new_name not in existing_names:
            return new_name
        counter += 1


def _convert_dataset_to_continuous(
    dataset: WpDepthIndexedDatasetBase,
    sampling_interval: float,
    depth_range: tuple[float, float],
    interpolation_method: str,
    rename_mapping: dict[str, str] | None = None
) -> WpContinuousDataset:
    """将数据集转换为连续型数据集（服务层内部函数）。

    **设计原则**：复用现有成熟服务，避免重复实现

    根据数据集类型调用相应的转换服务：
    - WpContinuousDataset: 使用resample_continuous_dataset服务
    - WpDiscreteDataset: 使用convert_discrete_to_continuous服务
    - WpIntervalDataset: 使用convert_interval_to_continuous服务

    Args:
        dataset: 源数据集
        sampling_interval: 目标采样间隔
        depth_range: 目标深度范围
        interpolation_method: 插值方法
        rename_mapping: 曲线重命名映射（可选）

    Returns:
        WpContinuousDataset: 转换后的连续型数据集

    Note:
        - 严格遵循SVC-5原则：只依赖基础数据结构
        - 复用现有的成熟转换服务，保证一致性
        - 应用曲线重命名映射（如果提供）
        - 使用智能插值方法选择（参考SAD文档§4.14）
    """
    # 调试日志：检查输入数据集
    logger.debug(
        "开始数据集转换",
        operation="_convert_dataset_to_continuous",
        dataset_name=dataset.name,
        dataset_type=type(dataset).__name__,
        input_df_columns=list(dataset.df.columns),
        input_well_curves=dataset.curve_metadata.get_well_identifier_curves(),
        input_depth_curves=dataset.curve_metadata.get_depth_reference_curves(),
        sampling_interval=sampling_interval,
        depth_range=depth_range
    )

    # 根据数据集类型选择相应的转换服务
    if isinstance(dataset, WpContinuousDataset):
        # ✅ 修复完成：现在可以安全使用重采样函数处理多井数据
        #
        # 修复说明：
        # continuous_resample.resample_continuous_dataset() 函数已修复多井数据处理缺陷：
        # 1. 自动检测单井/多井数据
        # 2. 单井数据：使用原有高效逻辑
        # 3. 多井数据：按井分组重采样，正确保留所有井的数据
        # 4. 支持部分曲线为空的情况（保留NaN值）
        #
        # 现在可以安全地对连续型数据集进行重采样，包括多井数据
        from logwp.models.datasets.internal import continuous_resample
        result_df, result_metadata, actual_range = continuous_resample.resample_continuous_dataset(
            dataset, sampling_interval,
            depth_range=depth_range,
            interpolation_method=interpolation_method
        )


    elif isinstance(dataset, WpDiscreteDataset):
        from logwp.models.datasets.internal import discrete_to_continuous
        result_df, result_metadata, actual_range = discrete_to_continuous.convert_discrete_to_continuous(
            dataset, sampling_interval,
            depth_range=depth_range,
            interpolation_method=interpolation_method
        )
    elif isinstance(dataset, WpIntervalDataset):
        from logwp.models.datasets.internal import interval_to_continuous
        result_df, result_metadata, actual_range = interval_to_continuous.convert_interval_to_continuous(
            dataset, sampling_interval,
            depth_range=depth_range
        )
    else:
        from logwp.models.exceptions import WpDatasetTypeError
        raise WpDatasetTypeError(f"不支持的数据集类型: {type(dataset)}")

    # 应用曲线重命名映射（如果提供）
    if rename_mapping:
        result_df, result_metadata = _apply_curve_renaming(result_df, result_metadata, rename_mapping)

    # 调试日志：检查转换结果
    logger.debug(
        "数据集转换完成",
        operation="_convert_dataset_to_continuous",
        dataset_name=dataset.name,
        result_df_columns=list(result_df.columns),
        result_well_curves=result_metadata.get_well_identifier_curves(),
        result_depth_curves=result_metadata.get_depth_reference_curves()
    )

    # 创建WpContinuousDataset（所有转换服务都返回连续型格式）
    result_dataset = WpContinuousDataset.create_with_data(
        name=f"{dataset.name}_continuous_temp",
        df=result_df,
        curve_metadata=result_metadata,
        depth_sampling_rate=sampling_interval
    )

    return result_dataset


def _apply_curve_renaming(
    df: pd.DataFrame,
    metadata: CurveMetadata,
    rename_mapping: dict[str, str]
) -> tuple[pd.DataFrame, CurveMetadata]:
    """应用曲线重命名映射。

    Args:
        df: 源DataFrame
        metadata: 源曲线元数据
        rename_mapping: 重命名映射 {原名称: 新名称}

    Returns:
        tuple[pd.DataFrame, CurveMetadata]: (重命名后的DataFrame, 重命名后的元数据)

    Note:
        - 同时修改DataFrame列名和CurveBasicAttributes的name、dataframe_column_name
        - 保持井名和深度曲线不变
    """
    # 深拷贝DataFrame和元数据
    new_df = df.copy()
    new_metadata = copy.deepcopy(metadata)

    # 应用DataFrame列名重命名
    df_rename_mapping = {}
    for old_name, new_name in rename_mapping.items():
        # 查找对应的DataFrame列名
        curve_attrs = metadata.get_curve(old_name)
        if curve_attrs:
            old_df_column = curve_attrs.dataframe_column_name
            new_df_column = new_name.replace('[', '_').replace(']', '')  # DataFrame友好格式
            df_rename_mapping[old_df_column] = new_df_column

    new_df = new_df.rename(columns=df_rename_mapping)

    # 应用元数据重命名
    for old_name, new_name in rename_mapping.items():
        curve_attrs = new_metadata.get_curve(old_name)
        if curve_attrs:
            # 更新曲线属性
            object.__setattr__(curve_attrs, 'name', new_name)
            object.__setattr__(curve_attrs, 'dataframe_column_name',
                             new_name.replace('[', '_').replace(']', ''))

            # 在元数据中更新映射
            new_metadata.curves[new_name] = curve_attrs
            if old_name != new_name:
                del new_metadata.curves[old_name]

    return new_df, new_metadata


def _merge_continuous_dataframes_by_depth(
    left_df: pd.DataFrame,
    left_metadata: CurveMetadata,
    right_df: pd.DataFrame,
    right_metadata: CurveMetadata
) -> pd.DataFrame:
    """按井名和深度合并两个连续型DataFrame。

    Args:
        left_df: 左数据集DataFrame
        left_metadata: 左数据集曲线元数据
        right_df: 右数据集DataFrame
        right_metadata: 右数据集曲线元数据

    Returns:
        pd.DataFrame: 合并后的DataFrame

    Note:
        - 由于两个DataFrame已经有相同的深度采样间隔，可以直接按深度合并
        - 使用pandas.merge进行外连接，保留所有深度点
        - 井名列和深度列作为合并键
        - 使用CurveMetadata的重命名服务统一列名
        - 合并后重置为默认整数索引

    Raises:
        WpDataError: 无法找到深度列或井名列时抛出异常
    """
    # 调试日志：检查输入数据
    logger.debug(
        "开始DataFrame合并",
        operation="merge_continuous_dataframes_by_depth",
        left_df_columns=list(left_df.columns),
        left_df_shape=left_df.shape,
        right_df_columns=list(right_df.columns),
        right_df_shape=right_df.shape
    )
    # 1. 获取左右数据集的深度和井名曲线
    left_depth_curves = left_metadata.get_depth_reference_curves()
    left_well_curves = left_metadata.get_well_identifier_curves()

    right_depth_curves = right_metadata.get_depth_reference_curves()
    right_well_curves = right_metadata.get_well_identifier_curves()

    # 2. 验证必要曲线存在
    if not left_depth_curves or not right_depth_curves:
        raise WpDataError(
            "无法找到深度参考曲线，数据集合并失败",
            context=ErrorContext(
                operation="merge_continuous_dataframes_by_depth",
                additional_info={
                    "left_depth_curves": left_depth_curves,
                    "right_depth_curves": right_depth_curves
                }
            )
        )

    if not left_well_curves or not right_well_curves:
        raise WpDataError(
            "无法找到井标识符曲线，数据集合并失败",
            context=ErrorContext(
                operation="merge_continuous_dataframes_by_depth",
                additional_info={
                    "left_well_curves": left_well_curves,
                    "right_well_curves": right_well_curves
                }
            )
        )

    # 3. 统一右数据集的深度和井名曲线名称（以左边为准）
    right_df_normalized = right_df.copy()

    # 重命名深度曲线
    depth_rename_mapping = right_metadata.rename_depth_reference_curves(
        target_depth_curves=left_depth_curves,
        update_dataframe_column_names=False  # 只更新元数据，不影响DataFrame列名
    )

    # 重命名井名曲线
    well_rename_mapping = right_metadata.rename_well_identifier_curves(
        target_well_curves=left_well_curves,
        update_dataframe_column_names=False  # 只更新元数据，不影响DataFrame列名
    )

    # 应用DataFrame列名重命名
    df_rename_mapping = {}
    df_rename_mapping.update(depth_rename_mapping)
    df_rename_mapping.update(well_rename_mapping)

    if df_rename_mapping:
        logger.info(
            "统一DataFrame列名",
            operation="merge_continuous_dataframes_by_depth",
            rename_mapping=df_rename_mapping,
            right_df_columns_before=list(right_df_normalized.columns)
        )
        right_df_normalized = right_df_normalized.rename(columns=df_rename_mapping)
        logger.info(
            "DataFrame列名重命名完成",
            operation="merge_continuous_dataframes_by_depth",
            right_df_columns_after=list(right_df_normalized.columns)
        )
    else:
        logger.info(
            "无需重命名DataFrame列名",
            operation="merge_continuous_dataframes_by_depth",
            left_well_curves=left_well_curves,
            left_depth_curves=left_depth_curves,
            right_well_curves=right_well_curves,
            right_depth_curves=right_depth_curves
        )

    # 4. 按深度值进行合并（绝不按索引合并）
    merge_keys = left_well_curves + left_depth_curves

    logger.info(
        "开始按深度值合并DataFrame",
        operation="merge_continuous_dataframes_by_depth",
        merge_keys=merge_keys,
        left_rows=len(left_df),
        right_rows=len(right_df_normalized)
    )

    # 执行外连接合并
    merged_df = pd.merge(
        left_df, right_df_normalized,
        on=merge_keys,
        how='outer',
        suffixes=('', '_right')  # 右侧重复列添加后缀（理论上不应该有，因为已经重命名）
    )

    # 5. 按井名和深度排序
    merged_df = merged_df.sort_values(by=merge_keys)

    # 6. 重置为默认整数索引（符合CDP-1规范）
    merged_df = merged_df.reset_index(drop=True)

    logger.info(
        "DataFrame合并完成",
        operation="merge_continuous_dataframes_by_depth",
        merged_rows=len(merged_df),
        merged_columns=len(merged_df.columns)
    )

    return merged_df


def _ensure_dataframe_index_compliance(df: pd.DataFrame) -> pd.DataFrame:
    """确保DataFrame符合CDP-1索引规范。

    Args:
        df: 输入DataFrame

    Returns:
        pd.DataFrame: 符合索引规范的DataFrame

    Note:
        - 强制重置为默认整数索引（RangeIndex）
        - 如果原索引包含有用信息，会转换为普通列
        - 严格遵循《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》CDP-1核心原则
    """
    if not isinstance(df.index, pd.RangeIndex):
        # 如果索引不是默认整数索引，重置为RangeIndex
        df = df.reset_index(drop=True)

    return df


def _merge_curve_metadata(
    left_metadata: CurveMetadata,
    right_metadata: CurveMetadata,
    rename_mapping: dict[str, str],
    merge_sampling_interval: float
) -> CurveMetadata:
    """合并两个数据集的曲线元数据。

    参考interval_to_continuous.py和discrete_to_continuous.py的元数据处理模式。

    Args:
        left_metadata: 左数据集曲线元数据
        right_metadata: 右数据集曲线元数据
        rename_mapping: 右数据集曲线重命名映射
        merge_sampling_interval: 合并后的采样间隔

    Returns:
        CurveMetadata: 合并后的曲线元数据

    Note:
        - 深拷贝左数据集元数据作为基础
        - 添加右数据集的曲线（应用重命名）
        - 统一深度曲线为WpStandardColumn.DEPTH
        - 统一井名曲线dataframe_column_name为WpStandardColumn.WELL_NAME
        - 更新时间戳
    """
    from datetime import datetime
    from logwp.models.constants import WpStandardColumn, WpCurveCategory, WpDepthRole
    from logwp.models.curve.metadata import CurveBasicAttributes

    # 深拷贝左数据集元数据作为基础（参考现有模式）
    merged_metadata = copy.deepcopy(left_metadata)

    # 添加右数据集的曲线（应用重命名映射）
    for curve in right_metadata.list_curves():
        curve_name = curve.name
        # 应用重命名
        final_curve_name = rename_mapping.get(curve_name, curve_name)

        if not merged_metadata.has_curve(final_curve_name):
            curve_attrs = right_metadata.get_curve(curve_name)
            if curve_attrs:
                # 创建新的曲线属性（应用重命名）
                new_curve_attrs = copy.deepcopy(curve_attrs)
                object.__setattr__(new_curve_attrs, 'name', final_curve_name)
                object.__setattr__(new_curve_attrs, 'dataframe_column_name',
                                 final_curve_name.replace('[', '_').replace(']', ''))
                merged_metadata.add_curve(new_curve_attrs)

    # 统一深度曲线处理（参考现有模式）
    _standardize_depth_curves(merged_metadata, merge_sampling_interval)

    # 统一井名曲线处理（参考现有模式）
    _standardize_well_curves(merged_metadata)

    # 更新时间戳
    merged_metadata.modified_at = datetime.now()

    return merged_metadata


def _standardize_depth_curves(metadata: CurveMetadata, sampling_interval: float) -> None:
    """统一深度曲线处理。"""
    # 不需要添加新的深度曲线，保持现有的深度曲线即可
    # 合并后的元数据已经包含了正确的深度曲线
    pass


def _standardize_well_curves(metadata: CurveMetadata) -> None:
    """统一井名曲线处理。"""
    # 不需要修改dataframe_column_name，保持与DataFrame列名一致
    # 合并后的DataFrame列名已经通过重命名统一，元数据应该与之保持一致
    pass


def _remove_all_null_rows(df: pd.DataFrame) -> pd.DataFrame:
    """移除全空行。

    Args:
        df: 输入DataFrame

    Returns:
        pd.DataFrame: 移除全空行后的DataFrame

    Note:
        - 保留井名和深度列，只检查数据列
        - 如果数据列全为空，则移除该行
    """
    # 获取井名和深度列
    system_columns = []
    for col in df.columns:
        if ('well' in col.lower() or col.upper() in ['WELL', 'WELL_NAME'] or
            'depth' in col.lower() or col.upper() in ['MD', 'TVD', 'DEPTH']):
            system_columns.append(col)

    # 获取数据列
    data_columns = [col for col in df.columns if col not in system_columns]

    if not data_columns:
        # 如果没有数据列，返回原DataFrame
        return df

    # 移除数据列全为空的行
    mask = df[data_columns].notna().any(axis=1)
    return df[mask].copy()


def _create_final_dataset_with_smart_type_detection(
    merged_df: pd.DataFrame,
    merged_metadata: CurveMetadata,
    sampling_interval: float,
    dataset_name: str
) -> WpDepthIndexedDatasetBase:
    """智能判断最终数据集类型（参考extract_curves实现）。

    Args:
        merged_df: 合并后的DataFrame
        merged_metadata: 合并后的曲线元数据
        sampling_interval: 采样间隔
        dataset_name: 数据集名称

    Returns:
        WpDepthIndexedDatabaseBase: 最终数据集（continuous或discrete）

    Note:
        - 参考extract_curves的智能类型判断逻辑
        - 使用check_uniform_depth_sampling检查采样间隔一致性
        - 等间隔→WpContinuousDataset，非等间隔→WpDiscreteDataset
    """
    # 创建临时连续型数据集用于深度采样检查
    temp_dataset = WpContinuousDataset.create_with_data(
        name=f"temp_{dataset_name}",
        df=merged_df,
        curve_metadata=merged_metadata,
        depth_sampling_rate=sampling_interval
    )

    try:
        # 检查深度采样间隔（参考extract_curves逻辑）
        is_uniform, actual_interval = temp_dataset.check_uniform_depth_sampling()

        if is_uniform:
            # 等间隔采样 → Continuous类型
            return WpContinuousDataset.create_with_data(
                name=dataset_name,
                df=merged_df,
                curve_metadata=merged_metadata,
                depth_sampling_rate=actual_interval or sampling_interval
            )
        else:
            # 非等间隔采样 → Discrete类型
            return WpDiscreteDataset.create_with_data(
                name=dataset_name,
                df=merged_df,
                curve_metadata=merged_metadata
            )

    except Exception as e:
        # 如果深度采样检查失败，回退到连续型（参考extract_curves逻辑）
        logger.warning(
            "深度采样检查失败，回退到连续型数据集",
            operation="create_final_dataset_with_smart_type_detection",
            dataset_name=dataset_name,
            error_type=type(e).__name__,
            error_message=str(e)
        )
        return WpContinuousDataset.create_with_data(
            name=dataset_name,
            df=merged_df,
            curve_metadata=merged_metadata,
            depth_sampling_rate=sampling_interval
        )


def _convert_dataset_to_continuous_per_well(
    dataset: WpDepthIndexedDatasetBase,
    sampling_interval: float,
    per_well_depth_ranges: dict[str, tuple[float, float]],
    interpolation_method: str,
    rename_mapping: dict[str, str] | None = None
) -> WpContinuousDataset:
    """按井分别转换数据集为连续型（SQL FULL JOIN逻辑）。

    Args:
        dataset: 要转换的数据集
        sampling_interval: 采样间隔
        per_well_depth_ranges: 每口井的深度范围 {井名: (最小深度, 最大深度)}
        interpolation_method: 插值方法
        rename_mapping: 曲线重命名映射

    Returns:
        WpContinuousDataset: 转换后的连续型数据集

    Note:
        实现SQL FULL JOIN语义：
        - 只为数据集中实际存在的井生成深度序列
        - 每口井在指定的深度范围内进行转换
        - 避免生成跨井的无意义深度点
    """
    if dataset.df.empty:
        return WpContinuousDataset.create_with_data(
            name=f"{dataset.name}_continuous_per_well",
            df=pd.DataFrame(),
            curve_metadata=CurveMetadata(),
            depth_sampling_rate=sampling_interval
        )

    # 获取数据集中实际存在的井
    dataset_wells = set(dataset.get_wells())

    # 只处理数据集中实际存在的井
    relevant_wells = {well: depth_range
                     for well, depth_range in per_well_depth_ranges.items()
                     if well in dataset_wells}

    if not relevant_wells:
        logger.warning(
            "数据集中没有相关的井",
            operation="convert_dataset_to_continuous_per_well",
            dataset_name=str(dataset.name),
            dataset_wells=list(dataset_wells),
            target_wells=list(per_well_depth_ranges.keys())
        )
        return WpContinuousDataset.create_with_data(
            name=f"{dataset.name}_continuous_per_well",
            df=pd.DataFrame(),
            curve_metadata=CurveMetadata(),
            depth_sampling_rate=sampling_interval
        )

    # 按井分别转换
    well_datasets = []

    for well, depth_range in relevant_wells.items():
        logger.debug(
            "开始转换单井数据",
            operation="convert_dataset_to_continuous_per_well",
            well_name=well,
            depth_range=depth_range,
            sampling_interval=sampling_interval
        )

        # 提取当前井的数据
        well_data = _extract_well_data(dataset, well)
        if well_data.df.empty:
            continue

        # 转换当前井的数据
        well_continuous = _convert_dataset_to_continuous(
            well_data, sampling_interval, depth_range, interpolation_method, rename_mapping
        )

        if not well_continuous.df.empty:
            well_datasets.append(well_continuous)

        logger.debug(
            "单井转换完成",
            operation="convert_dataset_to_continuous_per_well",
            well_name=well,
            converted_rows=len(well_continuous.df)
        )

    # 合并所有井的数据
    if not well_datasets:
        return WpContinuousDataset.create_with_data(
            name=f"{dataset.name}_continuous_per_well",
            df=pd.DataFrame(),
            curve_metadata=CurveMetadata(),
            depth_sampling_rate=sampling_interval
        )

    # 合并所有井的DataFrame
    all_dfs = [wd.df for wd in well_datasets]
    merged_df = pd.concat(all_dfs, ignore_index=True)

    # 使用第一个井的元数据作为基础
    merged_metadata = well_datasets[0].curve_metadata

    logger.debug(
        "按井转换完成",
        operation="convert_dataset_to_continuous_per_well",
        total_wells=len(relevant_wells),
        converted_wells=len(well_datasets),
        total_rows=len(merged_df)
    )

    return WpContinuousDataset.create_with_data(
        name=f"{dataset.name}_continuous_per_well",
        df=merged_df,
        curve_metadata=merged_metadata,
        depth_sampling_rate=sampling_interval
    )


def _extract_well_data(dataset: WpDepthIndexedDatasetBase, well_name: str) -> WpDepthIndexedDatasetBase:
    """提取指定井的数据。"""
    # 获取井名曲线
    well_curves = dataset.curve_metadata.get_well_identifier_curves()
    if not well_curves or well_curves[0] not in dataset.df.columns:
        # 没有井名曲线，返回整个数据集
        return dataset

    well_column = well_curves[0]
    well_data = dataset.df[dataset.df[well_column] == well_name].copy()

    # 重置索引以符合CDP-1规范
    well_data = well_data.reset_index(drop=True)

    # 创建新的数据集实例
    from logwp.models.datasets.continuous import WpContinuousDataset
    from logwp.models.datasets.discrete import WpDiscreteDataset
    from logwp.models.datasets.interval import WpIntervalDataset

    if isinstance(dataset, WpContinuousDataset):
        return WpContinuousDataset.create_with_data(
            name=f"{dataset.name}_{well_name}",
            df=well_data,
            curve_metadata=dataset.curve_metadata,
            depth_sampling_rate=dataset.depth_sampling_rate
        )
    elif isinstance(dataset, WpDiscreteDataset):
        return WpDiscreteDataset.create_with_data(
            name=f"{dataset.name}_{well_name}",
            df=well_data,
            curve_metadata=dataset.curve_metadata
        )
    elif isinstance(dataset, WpIntervalDataset):
        return WpIntervalDataset.create_with_data(
            name=f"{dataset.name}_{well_name}",
            df=well_data,
            curve_metadata=dataset.curve_metadata
        )
    else:
        # 回退到原数据集
        return dataset
