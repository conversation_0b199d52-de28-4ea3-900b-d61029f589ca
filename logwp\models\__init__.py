from __future__ import annotations

"""logwp.models - 格式无关测井数据模型层

统一的格式无关测井数据模型实现，包含所有业务模型组件。

Architecture
------------
层次/依赖: logwp包格式无关数据模型层，依赖types、constants、exceptions
设计原则: 格式无关、DDD聚合根、组合模式、类型安全
性能特征: 内存优化、类型安全、业务逻辑封装

Core Features
-------------
- **WpWellProject**: 井工程项目聚合根，管理数据集和元信息
- **WpHead**: 项目头部信息管理，支持扩展属性查找
- **WpWellMap**: 井名映射管理，支持循环检测和规范化
- **数据集模型**: 连续型、离散型、区间型数据集
- **曲线模型**: 曲线元数据和基本属性管理
- **扩展属性**: 6类属性管理和分层查找机制

Package Structure
-----------------
- base: WpProjectComponent抽象基类
- well_project: WpWellProject聚合根实现
- head: WpHead项目属性管理
- mapping: WpWellMap井名映射管理
- datasets/: 数据集模型（WpDepthIndexedDatasetBase及其子类）
- curve/: 曲线模型（CurveMetadata, CurveBasicAttributes）
- ext_attr/: 扩展属性模型（ExtAttributeManager）
- service/: 私有helper/utility服务

Examples
--------
>>> from logwp.models import WpWellProject, WpHead, WpWellMap
>>> from logwp.models.datasets import WpContinuousDataset
>>> from logwp.models.curve import CurveMetadata
>>> from logwp.models.ext_attr import ExtAttributeManager
>>>
>>> # 创建井工程项目
>>> project = WpWellProject(name="Santos_Study")
>>>
>>> # 创建数据集
>>> dataset = WpContinuousDataset(name="OBMIQ_logs", df=logs_df)
>>> project.add_dataset(dataset)
>>>
>>> # 直接使用数据集进行机器学习
>>> dataset = project.get_dataset("OBMIQ_logs")
>>> ml_data = dataset.df  # 直接获取DataFrame用于ML

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§4 - models层设计
- 《SCAPE_SAD_软件架构设计.md》§5.1 - logwp包核心类图
"""

# 核心领域模型
from .base import WpProjectComponent
from .well_project import WpWellProject
from .head import WpHead
from .mapping import WpWellMap

# 子包导入（便捷访问）
from . import datasets
from . import curve
from . import ext_attr



__all__ = [
    # 基类
    "WpProjectComponent",

    # 聚合根
    "WpWellProject",

    # 值对象和实体
    "WpHead",
    "WpWellMap",

    # 子包
    "datasets",
    "curve",
    "ext_attr",
]
