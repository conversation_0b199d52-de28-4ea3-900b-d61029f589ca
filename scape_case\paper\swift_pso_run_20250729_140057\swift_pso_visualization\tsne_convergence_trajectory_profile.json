{"name": "swift_pso.tsne_convergence", "save_config": {"format": ["png", "svg"], "dpi": 300, "width": null, "height": null, "bbox_inches": "tight", "transparent": false, "save_kwargs": {}}, "rc_params": {"font.family": "<PERSON><PERSON>", "font.size": 12, "figure.facecolor": "white", "axes.grid": true, "grid.alpha": 0.3, "grid.linestyle": "-", "axes.edgecolor": "black", "axes.linewidth": 1.0}, "figure_props": {"dpi": 150, "layout": "constrained", "figsize": [16, 12]}, "title_props": {"label": "SWIFT-PSO Parameter Evolution (Trajectory)", "fontsize": 16, "fontweight": "bold", "pad": 20}, "label_props": {"xlabel": "t-SNE Dimension 1", "ylabel": "t-SNE Dimension 2", "fontsize": 14}, "artist_props": {"trajectory": {"color": "grey", "alpha": 0.4, "linewidth": 0.8, "zorder": 1}, "scatter": {"s": 80, "edgecolor": "white", "linewidth": 0.5, "zorder": 2}, "start_point": {"marker": "x", "s": 50, "color": "black", "linewidth": 1.0, "zorder": 3, "label": "Start Point"}, "end_point": {"s": 100, "facecolors": "none", "edgecolors": "red", "linewidth": 1.2, "zorder": 4, "label": "End Point"}, "palette": {"name": "viridis", "n_colors": null}, "legend": {"title": "Legend", "bbox_to_anchor": [1.05, 1], "loc": "upper left"}}, "is_frozen": false}