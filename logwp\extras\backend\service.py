"""logwp.extras.backend.service - 后端服务接口定义

使用Protocol定义了所有后端服务必须遵循的接口，确保类型安全和IDE智能提示。
"""

from __future__ import annotations

from typing import Protocol, Any, runtime_checkable


@runtime_checkable
class RandomService(Protocol):
    """计算后端随机数生成服务的接口协议。

    定义了所有后端（如Numpy, Cupy）的随机数生成器必须实现的方法。
    这是对 `numpy.random` 或 `cupy.random` 的一层抽象。
    """

    def rand(self, *size: int) -> Any:
        """在[0.0, 1.0)区间内返回均匀分布的随机样本。"""
        ...

    def uniform(self, low: float = 0.0, high: float = 1.0, size: Any = None) -> Any:
        """在[low, high)区间内返回均匀分布的随机样本。"""
        ...

    def normal(self, loc: float = 0.0, scale: float = 1.0, size: Any = None) -> Any:
        """从正态（高斯）分布中抽取随机样本。"""
        ...


# 注意：具体的 _RandomService 辅助类将在实现 NumpyService 和 CupyService 时创建，
# 此处仅定义其必须遵循的协议接口。


@runtime_checkable
class BackendService(Protocol):
    """计算后端服务的接口协议。

    定义了所有后端服务（如NumpyService, CupyService）必须实现的方法和属性。
    这确保了上层代码可以以一种与后端无关的方式进行调用，同时获得
    静态类型检查和IDE智能提示的支持。
    """

    @property
    def name(self) -> str:
        """返回后端服务的名称 ('cpu' 或 'gpu')。"""
        ...

    def as_scalar(self, value: Any) -> int | float | bool:
        """将0维数组安全地转换为Python原生标量类型。

        这是处理CPU/GPU边界问题的关键函数，确保传递给非GPU感知库
        （如日志、JSON序列化）的是标准Python类型。
        """
        ...

    def argmin(self, array: Any, axis: int | None = None) -> Any:
        """返回数组中最小值的索引。"""
        ...

    def sum(self, array: Any, **kwargs: Any) -> Any:
        """对数组求和。"""
        ...

    def min(self, array: Any, **kwargs: Any) -> Any:
        """获取数组的最小值。"""
        ...

    def max(self, array: Any, **kwargs: Any) -> Any:
        """获取数组的最大值。"""
        ...

    def mean(self, array: Any, **kwargs: Any) -> Any:
        """计算数组的平均值。"""
        ...

    def any(self, array: Any, **kwargs: Any) -> Any:
        """检查数组中是否有任何元素为True。"""
        ...

    def nan_to_num(self, array: Any, **kwargs: Any) -> Any:
        """将NaN替换为0，无穷大替换为有限的大数。"""
        ...

    def maximum(self, array1: Any, array2: Any) -> Any:
        """逐元素比较两个数组，返回最大值。"""
        ...

    def isnan(self, array: Any) -> Any:
        """逐元素检查是否为NaN。"""
        ...

    def isinf(self, array: Any) -> Any:
        """逐元素检查是否为无穷大。"""
        ...

    def clip(self, array: Any, a_min: Any, a_max: Any) -> Any:
        """将数组中的值裁剪到指定的最小和最大值之间。"""
        ...

    # --- 数组创建方法 ---

    def zeros(self, shape: Any, dtype: Any = None) -> Any:
        """创建一个全为0的数组。"""
        ...

    def zeros_like(self, array: Any, dtype: Any = None) -> Any:
        """创建一个与给定数组形状和类型相同的全零数组。"""
        ...

    def ones(self, shape: Any, dtype: Any = None) -> Any:
        """创建一个全为1的数组。"""
        ...

    def ones_like(self, array: Any, dtype: Any = None) -> Any:
        """创建一个与给定数组形状和类型相同的全一数组。"""
        ...

    def full(self, shape: Any, fill_value: Any, dtype: Any = None) -> Any:
        """创建一个以指定值填充的数组。"""
        ...

    def full_like(self, array: Any, fill_value: Any, dtype: Any = None) -> Any:
        """创建一个与给定数组形状和类型相同，并以指定值填充的数组。"""
        ...

    def arange(self, start: Any, stop: Any = None, step: Any = 1, dtype: Any = None) -> Any:
        """在给定间隔内返回均匀间隔的值。"""
        ...

    def linspace(self, start: Any, stop: Any, num: int = 50, dtype: Any = None) -> Any:
        """在指定间隔内返回等间隔的数字。"""
        ...

    def logspace(self, start: Any, stop: Any, num: int = 50, base: float = 10.0, dtype: Any = None) -> Any:
        """在指定间隔内返回对数刻度上等间隔的数字。"""
        ...

    # --- 核心数学与统计函数 ---

    def std(self, array: Any, **kwargs: Any) -> Any:
        """计算数组的标准差。"""
        ...

    def var(self, array: Any, **kwargs: Any) -> Any:
        """计算数组的方差。"""
        ...

    def abs(self, array: Any) -> Any:
        """计算数组的绝对值。"""
        ...

    def sqrt(self, array: Any) -> Any:
        """计算数组的非负平方根。"""
        ...

    def log(self, array: Any) -> Any:
        """计算数组的自然对数。"""
        ...

    def log10(self, array: Any) -> Any:
        """计算数组的以10为底的对数。"""
        ...

    def exp(self, array: Any) -> Any:
        """计算数组所有元素的指数。"""
        ...

    def trapz(self, y: Any, x: Any = None, **kwargs: Any) -> Any:
        """使用梯形法则沿给定轴积分。"""
        ...

    # --- 数据操作与逻辑运算 ---

    def all(self, array: Any, **kwargs: Any) -> Any:
        """检查数组中是否所有元素都为True。"""
        ...

    def where(self, condition: Any, x: Any, y: Any) -> Any:
        """根据条件从x或y中返回元素。"""
        ...

    def reshape(self, array: Any, newshape: Any) -> Any:
        """在不改变数据的情况下为数组赋予新的形状。"""
        ...

    def transpose(self, array: Any, axes: Any = None) -> Any:
        """反转或排列数组的轴。"""
        ...

    def concatenate(self, arrays: Any, axis: int = 0) -> Any:
        """沿现有轴连接一系列数组。"""
        ...

    def copy(self, array: Any) -> Any:
        """创建数组的副本。"""
        ...

    @property
    def random(self) -> RandomService:
        """获取当前backend对应的随机数生成器服务。

        返回一个遵循RandomService协议的对象。
        """
        ...
    # --- 数据传输与检查 ---

    def to_cpu(self, array: Any) -> Any:
        """确保数组位于CPU内存中（作为NumPy数组）。

        如果输入是CuPy数组，则会将其转换为NumPy数组。
        """
        ...

    def to_gpu(self, array: Any) -> Any:
        """尝试将数组移动到GPU内存中（作为CuPy数组）。

        如果CuPy不可用，将按原样返回数组。
        """
        ...

    def is_on_gpu(self, array: Any) -> bool:
        """检查一个数组是否位于GPU上。"""
        ...

    def to_backend(self, array: Any) -> Any:
        """将数组移动到当前backend对应的设备上。

        如果当前backend是GPU，则移动到GPU；如果是CPU，则移动到CPU。
        这是一个便利方法，根据当前backend自动选择to_gpu或to_cpu。
        """
        ...
