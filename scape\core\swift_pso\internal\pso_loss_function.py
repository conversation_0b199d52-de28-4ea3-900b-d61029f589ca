"""scape.core.swift_pso.internal.pso_loss_function - PSO损失函数计算

实现SWIFT-PSO优化的向量化损失函数，包含加权对数误差、正则化和约束惩罚项。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部损失计算
设计原则: 向量化计算、后端无关、数值稳定性
性能特征: GPU/CPU兼容、批量处理、内存优化

遵循CCG规范：
- SC-1: 算法正确性
- SC-2: 数值稳定性
- GP-1: 自动检测回退
- PF-1: 内存控制

References
----------
- 《SCAPE_MS_方法说明书》§4.4.1 - 损失函数定义
- 迁移自 scape/core/swift_pso_backup/internal/pso_loss_function.py
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict

if TYPE_CHECKING:
    from logwp.extras.backend import BackendService

from scape.core.foster_nmr.calculate_foster_nmr import calculate_foster_nmr_permeability_vectorized_for_loss


def compute_loss_vectorized(
    parameters_matrix: Any,
    train_data: Dict[str, Any],
    mode: str,
    backend_service: BackendService
) -> Any:
    """(PSO专用) 计算整个粒子群的向量化损失函数。

    此函数接收一个参数矩阵，并为所有粒子并行计算损失值，返回一个损失向量。
    它实现了加权对数误差、L2正则化、物理边界软约束和delta_MDT惩罚项。
    该函数是后端无关的，可同时在CPU(numpy)和GPU(cupy)上运行。

    Args:
        parameters_matrix: 参数矩阵 (n_particles, n_parameters)
        train_data: 包含numpy/cupy数组的训练数据字典。
                   必须包含 'k_label', 'pzi', 'param_keys' 等。
        mode: 计算模式, 'bootstrap' 或 'finetune'
        backend_service: 用于计算的后端服务实例

    Returns:
        Any: 计算出的损失向量 (n_particles,)

    Note:
        损失函数包含五个主要组成部分：
        1. 加权对数均方误差 (MSE_w)
        2. 非产层惩罚项 (Omega_nonprod)
        3. L2正则化项 (敏感参数)
        4. 物理边界软约束惩罚项 (仅Bootstrap模式)
        5. delta_MDT独立L2正则项

    References:
        《SCAPE_MS_方法说明书》§4.3 - 损失函数数学定义
    """
    # 0. 准备工作
    lambda_1 = 0.02
    lambda_MDT = 0.2
    # 从配置中获取惩罚项超参数
    lambda_penalty = train_data.get('lambda_penalty', 0.5)
    K_penalty_thresh = train_data.get('k_penalty_thresh', 1.0)
    # 从配置中获取PZI加权超参数
    w_prod = train_data.get('w_prod', 1.0)
    w_nonprod = train_data.get('w_nonprod', 0.1)

    epsilon = 1e-9
    n_particles = parameters_matrix.shape[0]

    # --- 新增：参数动态重组 ---
    # 从train_data中获取参数信息，这些信息由training_facade注入
    param_keys = train_data['param_keys']  # 这是优化参数列表 (N个)
    fixed_params = train_data.get('fixed_params', {})
    all_param_names_ordered = train_data.get('all_param_names_ordered')

    n_total_dims = len(all_param_names_ordered)
    full_parameters_matrix = backend_service.zeros((n_particles, n_total_dims), dtype=parameters_matrix.dtype)

    # 1. 填充优化参数
    for i, param_name in enumerate(param_keys):
        col_idx = all_param_names_ordered.index(param_name)
        full_parameters_matrix[:, col_idx] = parameters_matrix[:, i]

    # 2. 填充固定参数
    for param_name, value in fixed_params.items():
        col_idx = all_param_names_ordered.index(param_name)
        full_parameters_matrix[:, col_idx] = value  # 广播填充
    # --- 重组结束 ---

    k_label = train_data['k_label']
    pzi_truth = train_data['pzi']
    boundaries = train_data.get('parameters_boundaries', {})  # 从训练数据中获取边界

    # 1. 获取模型预测值
    # 在调用底层物理模型前，需要确保它知道完整的10个参数的顺序
    # 创建一个临时的数据包，将param_keys替换为全量参数列表
    loss_calc_data_package = train_data.copy()
    loss_calc_data_package['param_keys'] = all_param_names_ordered

    # k_pred 的形状为 (n_depths, n_particles)
    k_pred = calculate_foster_nmr_permeability_vectorized_for_loss(
        full_parameters_matrix, loss_calc_data_package, backend_service
    )

    k_pred_safe = backend_service.maximum(k_pred, epsilon)
    k_label_safe = backend_service.maximum(k_label, epsilon)

    # 2. 加权对数均方误差 (MSE_w)
    k_label_r = k_label_safe.reshape(-1, 1)

    # 根据 PZI 和传入的权重参数定义样本权重
    weights = backend_service.where(pzi_truth.reshape(-1, 1) == 1, w_prod, w_nonprod)
    log_errors = backend_service.log10(k_pred_safe) - backend_service.log10(k_label_r)
    weighted_squared_errors = weights * (log_errors ** 2)

    sum_of_weights = backend_service.sum(weights)
    # 沿深度轴(axis=0)求和，得到每个粒子的损失
    mse_w_term = backend_service.sum(weighted_squared_errors, axis=0) / sum_of_weights if backend_service.as_scalar(sum_of_weights) > 0 else 0.0

    # 3. 新增：非产层惩罚项 (Omega_nonprod)
    # 惩罚开关 (1 - PZI)，当PZI=0(非产层)时，开关为1
    penalty_switch = 1 - pzi_truth.reshape(-1, 1)
    # 计算预测值超出惩罚阈值的部分
    over_prediction = backend_service.maximum(0, k_pred - K_penalty_thresh)
    # 计算惩罚项，沿深度轴(axis=0)求和
    nonprod_penalty_term = lambda_penalty * backend_service.sum(
        penalty_switch * (over_prediction ** 2), axis=0
    )
    # 4. L2正则项
    sensitive_param_keys = [
        'PHIT_EXP', 'T2LM_EXP', 'KMACRO_B', 'beta_1', 'beta_2', 'RHO_NMR'
    ]
    # 仅对当前正在优化的敏感参数进行正则化
    optimizing_sensitive_keys = [k for k in sensitive_param_keys if k in param_keys]
    if optimizing_sensitive_keys:
        sensitive_param_indices = [param_keys.index(k) for k in optimizing_sensitive_keys]
        theta_sensitive = parameters_matrix[:, sensitive_param_indices]
        l2_term = lambda_1 * backend_service.sum(theta_sensitive**2, axis=1)
    else:
        l2_term = backend_service.zeros(n_particles, dtype=parameters_matrix.dtype)

    # 5. 物理边界软约束惩罚项 (仅在bootstrap模式下)
    physical_penalty_term = backend_service.zeros(n_particles, dtype=parameters_matrix.dtype)
    if mode == 'bootstrap':
        total_penalty = backend_service.zeros_like(physical_penalty_term)
        max_violation_ratio = backend_service.zeros_like(physical_penalty_term)

        # 此循环现在只作用于N个优化参数，是正确的
        for i, param_name in enumerate(param_keys):
            if param_name in boundaries:
                h_lb, s_lb, s_ub, h_ub = boundaries[param_name]
                p_vec = parameters_matrix[:, i]

                violation_ub = backend_service.maximum(0, p_vec - s_ub)
                violation_lb = backend_service.maximum(0, s_lb - p_vec)
                total_penalty += violation_ub**2 + violation_lb**2

                gap_ub = h_ub - s_ub
                gap_lb = s_lb - h_lb
                ratio_ub = violation_ub / gap_ub if gap_ub > 1e-9 else 0
                ratio_lb = violation_lb / gap_lb if gap_lb > 1e-9 else 0
                max_violation_ratio = backend_service.maximum(max_violation_ratio, backend_service.maximum(ratio_ub, ratio_lb))

        lambda_2 = backend_service.where(max_violation_ratio > 0.5, 10.0, 5.0)
        lambda_2 = backend_service.where(total_penalty > 0, lambda_2, 0.0)
        physical_penalty_term = lambda_2 * total_penalty

    # 6. delta_MDT 的独立L2正则项
    delta_mdt_term = backend_service.zeros(n_particles, dtype=parameters_matrix.dtype)
    if 'delta_MDT' in param_keys:
        delta_mdt_idx = param_keys.index('delta_MDT')
        delta_mdt_vec = parameters_matrix[:, delta_mdt_idx]
        delta_mdt_term = lambda_MDT * (delta_mdt_vec**2)

    # 7. 加总得到最终Loss
    total_loss = mse_w_term + nonprod_penalty_term + l2_term + physical_penalty_term + delta_mdt_term

    return total_loss
