"""logwp.extras.tracking.utils - 内部工具函数

提供实验追踪系统的内部工具函数，包括ID生成、文件操作、字典合并等。

Architecture
------------
层次/依赖: logwp.extras.tracking包工具层
设计原则: 无状态函数、原子性操作、错误恢复
性能特征: 高效ID生成、安全文件操作、内存友好

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- PF-2: 异步I/O支持（大文件处理）
- PF-4: 资源释放和内存管理

References
----------
- 《SCAPE_CCG_编码与通用规范》§PF - 性能优化要求
- 《logwp_extras_tracking设计.md》§3 - 核心组件设计
"""

from __future__ import annotations

import hashlib
import json
import shutil
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional, Union

import numpy as np

from .exceptions import ArtifactIOError, ManifestError

# 延迟导入logger避免循环依赖
def _get_logger():
    try:
        from logwp.infra import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)


def generate_run_id() -> str:
    """生成唯一的运行ID。

    生成格式：{timestamp}-{uuid_short}
    例如：20250720-103000-a1b2c3d4

    Returns:
        str: 唯一的运行ID

    Examples:
        >>> run_id = generate_run_id()
        >>> print(run_id)  # 20250720-103000-a1b2c3d4
    """
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d-%H%M%S")
    uuid_short = str(uuid.uuid4())[:8]
    return f"{timestamp}-{uuid_short}"


def get_current_utc_timestamp() -> str:
    """获取当前UTC时间戳（ISO格式）。

    Returns:
        str: ISO格式的UTC时间戳

    Examples:
        >>> timestamp = get_current_utc_timestamp()
        >>> print(timestamp)  # 2025-07-20T10:30:00.123Z
    """
    return datetime.now(timezone.utc).isoformat().replace("+00:00", "Z")


def calculate_file_hash(file_path: Union[str, Path], algorithm: str = "sha256") -> str:
    """计算文件的哈希值。

    Args:
        file_path: 文件路径
        algorithm: 哈希算法，如 "sha256", "md5"

    Returns:
        str: 文件的哈希值（十六进制）

    Raises:
        ArtifactIOError: 文件读取失败时抛出

    Examples:
        >>> hash_value = calculate_file_hash("model.pkl")
        >>> print(hash_value)  # e3b0c44298fc1c149afbf4c8996fb924...
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise ArtifactIOError(
            f"File not found: {file_path}",
            source_path=str(file_path),
            operation="hash"
        )

    try:
        hasher = hashlib.new(algorithm)
        with open(file_path, "rb") as f:
            # 分块读取，避免大文件内存问题
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()
    except Exception as e:
        raise ArtifactIOError(
            f"Failed to calculate file hash: {e}",
            source_path=str(file_path),
            operation="hash"
        ) from e


def get_file_size(file_path: Union[str, Path]) -> int:
    """获取文件大小（字节）。

    Args:
        file_path: 文件路径

    Returns:
        int: 文件大小（字节）

    Raises:
        ArtifactIOError: 文件不存在或无法访问时抛出

    Examples:
        >>> size = get_file_size("model.pkl")
        >>> print(f"File size: {size} bytes")
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise ArtifactIOError(
            f"File not found: {file_path}",
            source_path=str(file_path),
            operation="stat"
        )

    try:
        return file_path.stat().st_size
    except Exception as e:
        raise ArtifactIOError(
            f"Failed to get file size: {e}",
            source_path=str(file_path),
            operation="stat"
        ) from e


def copy_artifact_file(
    source_path: Union[str, Path],
    target_path: Union[str, Path],
    *,
    create_dirs: bool = True
) -> None:
    """安全地复制产物文件。

    Args:
        source_path: 源文件路径
        target_path: 目标文件路径
        create_dirs: 是否自动创建目标目录

    Raises:
        ArtifactIOError: 文件操作失败时抛出

    Examples:
        >>> copy_artifact_file("temp/model.pkl", "output/run-001/models/model.pkl")
    """
    source_path = Path(source_path)
    target_path = Path(target_path)

    if not source_path.exists():
        raise ArtifactIOError(
            f"Source file not found: {source_path}",
            source_path=str(source_path),
            target_path=str(target_path),
            operation="copy"
        )

    try:
        if create_dirs:
            target_path.parent.mkdir(parents=True, exist_ok=True)

        shutil.copy2(source_path, target_path)
    except Exception as e:
        raise ArtifactIOError(
            f"Failed to copy file: {e}",
            source_path=str(source_path),
            target_path=str(target_path),
            operation="copy"
        ) from e


def atomic_write_json(
    data: Dict[str, Any],
    file_path: Union[str, Path],
    *,
    indent: int = 4
) -> None:
    """原子性地写入JSON文件。

    使用临时文件+重命名的方式确保写入的原子性，
    避免写入过程中断导致文件损坏。

    Args:
        data: 要写入的数据
        file_path: 目标文件路径
        indent: JSON缩进空格数

    Raises:
        ManifestError: JSON写入失败时抛出

    Examples:
        >>> data = {"run_id": "test", "status": "completed"}
        >>> atomic_write_json(data, "manifest.json")
    """
    file_path = Path(file_path)
    temp_path = file_path.with_suffix(f"{file_path.suffix}.tmp")

    try:
        def convert_numpy_floats(obj: Any) -> Any:
            if isinstance(obj, dict):
                return {k: convert_numpy_floats(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_floats(v) for v in obj]
            elif isinstance(obj, (np.float32, np.float64)):
                return float(obj)
            return obj
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # 写入临时文件
        with open(temp_path, "w", encoding="utf-8") as f:
            json.dump(convert_numpy_floats(data), f, indent=indent, ensure_ascii=False)

        # 原子性重命名
        temp_path.replace(file_path)

    except Exception as e:
        # 尝试清理临时文件，但即使失败也不要屏蔽原始异常
        try:
            if temp_path.exists():
                temp_path.unlink()
        except Exception as cleanup_exc:
            # 使用logger记录清理失败的错误，而不是抛出
            _get_logger().warning(
                "Failed to clean up temporary file during atomic write failure",
                temp_path=str(temp_path),
                cleanup_error=str(cleanup_exc),
                operation="atomic_write_cleanup"
            )

        # 重新抛出原始异常，保持异常链
        raise ManifestError(
            f"Failed to write JSON file: {e}",
            manifest_path=str(file_path),
            operation="write"
        ) from e


def load_json_file(file_path: Union[str, Path]) -> Dict[str, Any]:
    """安全地加载JSON文件。

    Args:
        file_path: JSON文件路径

    Returns:
        Dict[str, Any]: 解析后的JSON数据

    Raises:
        ManifestError: 文件不存在或JSON格式错误时抛出

    Examples:
        >>> data = load_json_file("manifest.json")
        >>> print(data["run_id"])
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise ManifestError(
            f"JSON file not found: {file_path}",
            manifest_path=str(file_path),
            operation="read"
        )

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        raise ManifestError(
            f"Invalid JSON format: {e}",
            manifest_path=str(file_path),
            operation="read"
        ) from e
    except Exception as e:
        raise ManifestError(
            f"Failed to read JSON file: {e}",
            manifest_path=str(file_path),
            operation="read"
        ) from e


def deep_merge_dict(base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
    """深度合并两个字典。

    递归合并字典，update中的值会覆盖base中的同名键。

    Args:
        base: 基础字典
        update: 更新字典

    Returns:
        Dict[str, Any]: 合并后的字典

    Examples:
        >>> base = {"a": 1, "b": {"c": 2}}
        >>> update = {"b": {"d": 3}, "e": 4}
        >>> result = deep_merge_dict(base, update)
        >>> print(result)  # {"a": 1, "b": {"c": 2, "d": 3}, "e": 4}
    """
    result = base.copy()

    for key, value in update.items():
        if (
            key in result
            and isinstance(result[key], dict)
            and isinstance(value, dict)
        ):
            result[key] = deep_merge_dict(result[key], value)
        else:
            result[key] = value

    return result


def ensure_directory(dir_path: Union[str, Path]) -> Path:
    """确保目录存在，如果不存在则创建。

    Args:
        dir_path: 目录路径

    Returns:
        Path: 目录路径对象

    Examples:
        >>> output_dir = ensure_directory("output/run-001")
        >>> print(output_dir.exists())  # True
    """
    dir_path = Path(dir_path)
    dir_path.mkdir(parents=True, exist_ok=True)
    return dir_path
