"""数据集解析服务。

解析数据集表单，支持三种数据集类型识别、采样间隔解析、表头结构验证等功能。
实现WFS 3章和4章规范，为数据集构造提供核心解析能力。
"""

from __future__ import annotations

import warnings
from typing import Any, Dict, List, Tuple

import openpyxl
import pandas as pd
import structlog

from logwp.io.exceptions import WpFileFormatError
from logwp.models.constants import WpDsType
from logwp.models.exceptions import WpValidationError
from logwp.io.constants import WpXlsxKey

logger = structlog.get_logger(__name__)


def parse_metadata_from_header_rows(row1: tuple, row2: tuple, sheet_name: str) -> dict[str, Any]:
    """从数据集工作表的前两行（流式）解析元数据。

    整合了数据集类型、索引、采样率和描述的解析，专为流式处理设计。

    Args:
        row1: 工作表的第一行数据
        row2: 工作表的第二行数据
        sheet_name: 工作表名称，用于日志和错误报告

    Returns:
        dict[str, Any]: 包含元数据的字典:
                        {'type': WpDsType, 'desc': str, 'sampling_rate': float | None}

    Raises:
        WpFileFormatError: 元数据格式不符合WFS规范
    """
    # --- Row 1 Parsing ---
    if len(row1) < 4:
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的第一行元数据不完整，至少需要4列。")

    # A1:B1 - Dataset Type
    type_key = str(row1[0] or "").strip()
    if type_key.upper() != WpXlsxKey.HDR_DS_TYPE.upper():
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的A1单元格必须为'{WpXlsxKey.HDR_DS_TYPE}', 实际: '{type_key}'")

    type_value = str(row1[1] or "").strip()
    if not type_value:
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的B1单元格（数据集类型）不能为空")

    dataset_type = _parse_dataset_type_string(type_value, sheet_name)

    # C1:D1 - Index Type
    index_key = str(row1[2] or "").strip()
    if index_key.upper() != WpXlsxKey.HDR_DS_INDEX_TYPE.upper():
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的C1单元格必须为'{WpXlsxKey.HDR_DS_INDEX_TYPE}', 实际: '{index_key}'")

    index_value = str(row1[3] or "").strip()
    if not index_value:
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的D1单元格（数据集索引类型）不能为空")

    if index_value.upper() != WpXlsxKey.DATASET_INDEX_TYPE_DEPTH.upper():
        raise WpFileFormatError(f"工作表 '{sheet_name}' 不支持的数据集索引类型: '{index_value}', 目前只支持: '{WpXlsxKey.DATASET_INDEX_TYPE_DEPTH}'")

    # --- Row 2 Parsing ---
    if len(row2) < 4:
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的第二行元数据不完整，至少需要4列。")

    # A2:B2 - Sampling Rate
    rate_key = str(row2[0] or "").strip()
    if rate_key.upper() != WpXlsxKey.HDR_SAMPLE_RATE.upper():
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的A2单元格必须为'{WpXlsxKey.HDR_SAMPLE_RATE}', 实际: '{rate_key}'")

    rate_value = row2[1]
    sampling_rate = None
    if rate_value is not None and str(rate_value).strip() != "":
        try:
            sampling_rate = float(rate_value)
            if sampling_rate <= 0:
                raise WpFileFormatError(f"工作表 '{sheet_name}' 的采样间隔必须大于0: {sampling_rate}")
        except (ValueError, TypeError) as e:
            raise WpFileFormatError(f"工作表 '{sheet_name}' 的采样间隔格式错误: '{rate_value}', 错误: {e}") from e

    # C2:D2 - Description
    desc_key = str(row2[2] or "").strip()
    description = ""
    if desc_key.upper() == WpXlsxKey.HDR_DS_DESC.upper():
        description = str(row2[3] or "").strip()

    logger.debug("从表头行解析元数据成功", sheet_name=sheet_name, dataset_type=dataset_type.value, sampling_rate=sampling_rate, description=description)
    return {"type": dataset_type, "desc": description, "sampling_rate": sampling_rate}

def extract_curve_definitions_from_rows(header_rows: list[tuple], sheet_name: str) -> list[dict[str, Any]]:
    """从数据集的表头行（流式）提取曲线定义。

    此函数接收代表工作表第3-7行的5行数据，并从中解析出所有曲线的
    定义（Name, Unit, Type, Class, Comment）。

    Args:
        header_rows: 包含5行表头数据的列表
        sheet_name: 工作表名称，用于日志和错误报告

    Returns:
        list[dict[str, Any]]: 曲线定义列表，每个字典包含曲线的详细信息。

    Raises:
        WpFileFormatError: 表头结构不符合WFS规范
    """
    if len(header_rows) != 5:
        raise WpFileFormatError(f"工作表 '{sheet_name}' 的曲线定义区需要5行 (Name, Unit, Type, Class, Comment)，实际接收到 {len(header_rows)} 行。")

    name_row, unit_row, type_row, class_row, comment_row = header_rows

    # 验证行标题 (A3 to A7)
    expected_titles = [
        (name_row, WpXlsxKey.ROW_TITLE_NAME),
        (unit_row, WpXlsxKey.ROW_TITLE_UNIT),
        (type_row, WpXlsxKey.ROW_TITLE_TYPE),
        (class_row, WpXlsxKey.ROW_TITLE_CLASS),
        (comment_row, WpXlsxKey.ROW_TITLE_COMMENT)
    ]
    for i, (row_data, expected_title_enum) in enumerate(expected_titles, start=3):
        expected_title = expected_title_enum.value
        actual_title = str(row_data[0] or "").strip()
        if actual_title.upper() != expected_title.upper():
            raise WpFileFormatError(f"工作表 '{sheet_name}' 的第{i}行A列标题错误: 期望'{expected_title}', 实际'{actual_title}'")

    curve_definitions = []
    num_cols = len(name_row)

    # 从第2列开始（第1列是行标题）
    for col_idx in range(1, num_cols):
        name = str(name_row[col_idx] or "").strip()
        if not name:
            continue

        unit = str(unit_row[col_idx] or "").strip() if col_idx < len(unit_row) else ""
        data_type = str(type_row[col_idx] or "").strip() if col_idx < len(type_row) else ""
        class_value = str(class_row[col_idx] or "").strip() if col_idx < len(class_row) else ""
        comment = str(comment_row[col_idx] or "").strip() if col_idx < len(comment_row) else ""

        curve_def = {
            "name": name,
            "unit": unit,
            "type": data_type,
            "class": class_value,
            "comment": comment,
            "column_index": col_idx + 1  # 1-based Excel column index
        }
        curve_definitions.append(curve_def)

    logger.debug("从表头行提取曲线定义成功", sheet_name=sheet_name, curve_count=len(curve_definitions))
    return curve_definitions


def _parse_dataset_type_string(type_str: str, sheet_name: str) -> WpDsType:
    """将数据集类型字符串解析为WpDsType枚举。"""
    dataset_types = WpXlsxKey.dataset_types()
    type_str_upper = type_str.upper()

    if type_str_upper == WpXlsxKey.DATASET_TYPE_CONTINUOUS.upper():
        logger.debug("识别数据集类型", sheet_name=sheet_name, type="Continuous")
        return WpDsType.CONTINUOUS
    elif type_str_upper == WpXlsxKey.DATASET_TYPE_POINT.upper():
        logger.debug("识别数据集类型", sheet_name=sheet_name, type="Point")
        return WpDsType.POINT
    elif type_str_upper == WpXlsxKey.DATASET_TYPE_INTERVAL.upper():
        logger.debug("识别数据集类型", sheet_name=sheet_name, type="Interval")
        return WpDsType.INTERVAL

    raise WpFileFormatError(
        f"工作表 '{sheet_name}' 包含不支持的数据集类型: '{type_str}', 支持的类型: {list(dataset_types)}")
