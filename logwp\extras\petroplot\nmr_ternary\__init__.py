"""logwp.extras.petroplot.nmr_ternary - 可追踪的NMR孔隙度三元图组件

本组件提供了一个标准化的、可复现的、可被工作流编排的绘图功能，
专门用于绘制NMR孔隙度组分（宏孔、中孔、微孔）三元图。

核心功能:
- **三层API**: 提供步骤门面、普通门面和重绘函数，适应不同使用场景。
- **配置驱动**: 通过Pydantic模型将绘图逻辑与样式完全分离。
- **可追踪性**: 与`logwp.extras.tracking`框架集成，自动记录产物。
- **可复现性**: 支持从数据和逻辑快照精确复现图表。
- **可定制化**: 支持通过`logwp.extras.plotting`的PlotProfile系统定制外观。

公共API:
- **门面函数**:
  - `run_nmr_ternary_plot_step`: 与工作流集成的步骤门面。
  - `generate_nmr_ternary_plot`: 接收DataFrame的普通门面。
  - `replot_nmr_ternary_from_snapshot`: 从快照复现图表的重绘函数。
- **配置模型**:
  - `NmrTernaryPlotConfig`: 绘图的逻辑与表现层配置。
  - `NmrTernaryDataSelectors`: 用户层API，定义逻辑曲线名。
- **常量**:
  - `NmrTernaryPlotArtifacts`: 产物逻辑名称。
  - `NmrTernaryPlotProfiles`: 绘图模板名称。
"""

# 1. 导入 plot_profiles 模块以触发其内部的自动注册机制。
# 这是实现“导入即注册”模式的关键，符合项目中的通用约定。
from . import plot_profiles

# 2. 从各模块导出公共API
from .config import (
    NmrTernaryPlotConfig,
    NmrTernaryDataSelectors,
    BackgroundRegionConfig,
)
from .constants import (
    NmrTernaryPlotArtifacts,
    NmrTernaryPlotProfiles,
    NMR_TERNARY_REGION_DEFINITIONS,
    NMR_TERNARY_REGION_COLOR_SCHEMES,
)
from .facade import (
    run_nmr_ternary_plot_step,
    generate_nmr_ternary_plot,
    replot_nmr_ternary_from_snapshot,
)

from .presets import (
     create_publication_ready_perm_config,
)

# 3. 定义 __all__ 以便 `from . import *` 使用
__all__ = [
    # Facade functions
    "run_nmr_ternary_plot_step", "generate_nmr_ternary_plot", "replot_nmr_ternary_from_snapshot",
    # Preset function
    "create_publication_ready_perm_config",
    # Configuration models
    "NmrTernaryPlotConfig", "NmrTernaryDataSelectors", "BackgroundRegionConfig",
    # Constants
    "NmrTernaryPlotArtifacts", "NmrTernaryPlotProfiles", "NMR_TERNARY_REGION_DEFINITIONS", "NMR_TERNARY_REGION_COLOR_SCHEMES",
]
