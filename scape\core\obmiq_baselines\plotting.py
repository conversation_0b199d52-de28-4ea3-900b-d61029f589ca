from __future__ import annotations

from pathlib import Path

import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns

from logwp.extras.plotting import PlotProfile, apply_profile, save_figure


def replot_crossplot(snapshot_path: Path, profile: PlotProfile, output_path: Path):
    """从数据快照复现预测值-真实值交会图。"""
    data = pd.read_csv(snapshot_path)
    actual_col = profile.label_props.get("actual_col", "actual")
    predicted_col = profile.label_props.get("predicted_col", "predicted")

    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    sns.scatterplot(
        data=data, x=actual_col, y=predicted_col, ax=ax, **profile.artist_props.get("scatter", {})
    )
    min_val = min(data[actual_col].min(), data[predicted_col].min())
    max_val = max(data[actual_col].max(), data[predicted_col].max())
    ax.plot([min_val, max_val], [min_val, max_val], **profile.artist_props.get("reference_line", {}))

    ax.set_xlabel(f"Actual {actual_col}")
    ax.set_ylabel(f"Predicted {predicted_col}")
    ax.set_title(profile.title_props.get("label", "Actual vs. Predicted"))

    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_residuals_plot(snapshot_path: Path, profile: PlotProfile, output_path: Path):
    """从数据快照复现残差图。"""
    data = pd.read_csv(snapshot_path)
    predicted_col = profile.label_props.get("predicted_col", "predicted")
    residual_col = profile.label_props.get("residual_col", "residual")

    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    sns.scatterplot(
        data=data, x=predicted_col, y=residual_col, ax=ax, **profile.artist_props.get("scatter", {})
    )
    ax.axhline(y=0, **profile.artist_props.get("zero_line", {}))

    ax.set_xlabel(f"Predicted Values ({predicted_col})")
    ax.set_ylabel(f"Residuals ({residual_col})")
    ax.set_title(profile.title_props.get("label", "Residuals vs. Predicted"))

    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_residuals_hist(snapshot_path: Path, profile: PlotProfile, output_path: Path):
    """从数据快照复现残差直方图。"""
    data = pd.read_csv(snapshot_path)
    residual_col = profile.label_props.get("residual_col", "residual")

    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    sns.histplot(data=data, x=residual_col, ax=ax, **profile.artist_props.get("hist", {}))
    mean_val = data[residual_col].mean()
    median_val = data[residual_col].median()
    ax.axvline(mean_val, label=f"Mean: {mean_val:.2f}", **profile.artist_props.get("mean_line", {}))
    ax.axvline(median_val, label=f"Median: {median_val:.2f}", **profile.artist_props.get("median_line", {}))

    ax.set_xlabel(f"Residuals ({residual_col})")
    ax.set_ylabel("Frequency")
    ax.set_title(profile.title_props.get("label", "Distribution of Residuals"))
    ax.legend()

    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_feature_importance(snapshot_path: Path, profile: PlotProfile, output_path: Path):
    """从数据快照复现特征重要性图。"""
    data = pd.read_csv(snapshot_path)
    # 假设数据有两列: 'feature' 和 'importance'
    data = data.sort_values(by="importance", ascending=False).head(20)  # 最多显示前20个

    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    sns.barplot(data=data, x="importance", y="feature", ax=ax, **profile.artist_props.get("barplot", {}))

    ax.set_xlabel("Importance Score")
    ax.set_ylabel("Feature")
    ax.set_title(profile.title_props.get("label", "Feature Importance"))

    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)
