"""logwp.extras.tracking.registry - 模型注册表管理

实现ModelRegistry类，管理跨运行的模型版本和生命周期跟踪。

Architecture
------------
层次/依赖: logwp.extras.tracking包核心层
设计原则: 版本化管理、生命周期跟踪、原子性操作
性能特征: JSON存储、原子性写入、版本索引

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- EH-2: 结构化异常上下文信息
- LG-1: structlog结构化日志
- PF-4: 资源释放和内存管理

References
----------
- 《logwp_extras_tracking设计.md》§3.2 - ModelRegistry详细设计
- 《SCAPE_CCG_编码与通用规范》- 编码规范要求
"""

from __future__ import annotations

from pathlib import Path
from packaging.version import Version, InvalidVersion

from typing import Any, Dict, List, Optional, Union

from .exceptions import (
    ModelNotRegisteredError,
    RegistryError,
)
from .utils import (
    atomic_write_json,
    get_current_utc_timestamp,
    load_json_file,
)

# 延迟导入logger避免循环依赖
def _get_logger():
    try:
        from logwp.infra import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)


class ModelRegistry:
    """管理模型产物的版本和生命周期阶段。

    它操作一个单一的注册表文件（通常是JSON格式），这个文件
    独立于任何单次运行的目录。

    Architecture
    ------------
    层次/依赖: 模型管理核心组件，跨运行状态管理
    设计原则: 版本化管理、阶段转换、中央注册表
    性能特征: 文件锁定、原子性更新、索引查询

    支持的模型阶段：
    - Development: 开发阶段，实验性模型
    - Staging: 暂存阶段，待验证模型
    - Production: 生产阶段，已验证可用模型
    - Archived: 归档阶段，已废弃模型

    Attributes:
        registry_path (Path): 注册表文件路径
        _registry_data (Dict[str, List[Dict[str, Any]]]): 内存中的注册表数据
        _logger: 结构化日志记录器

    Examples:
        >>> # 创建注册表
        >>> registry = ModelRegistry("model_registry.json")
        >>>
        >>> # 注册新模型
        >>> registry.register_model(
        ...     model_name="foster-nmr",
        ...     run_id="run-001",
        ...     artifact_path="models/final_model.pkl",
        ...     metrics={"rmse": 0.85, "r2": 0.92}
        ... )
        >>>
        >>> # 查询模型
        >>> latest = registry.get_latest_versions("foster-nmr", stages=["Production"])
        >>> print(latest[0]["version"])
    """

    # 支持的模型阶段
    VALID_STAGES = {"Development", "Staging", "Production", "Archived"}

    def __init__(self, registry_path: Union[str, Path]) -> None:
        """初始化一个模型注册表。

        Args:
            registry_path: 注册表文件的路径。如果文件不存在，将会被创建

        Examples:
            >>> registry = ModelRegistry("project_root/model_registry.json")
        """
        self.registry_path = Path(registry_path)
        self._logger = _get_logger()
        self._load_registry()

        self._logger.info(
            "ModelRegistry initialized",
            registry_path=str(self.registry_path),
            models_count=len(self._registry_data),
            operation="init_registry"
        )

    def _load_registry(self) -> None:
        """加载注册表数据。"""
        if self.registry_path.exists():
            try:
                self._registry_data = load_json_file(self.registry_path)
                self._logger.debug(
                    "Registry loaded from file",
                    registry_path=str(self.registry_path),
                    operation="load_registry"
                )
            except Exception as e:
                raise RegistryError(
                    f"Failed to load registry: {e}",
                    registry_path=str(self.registry_path),
                    operation="load"
                ) from e
        else:
            # 创建空注册表
            self._registry_data = {}
            self._save_registry()
            self._logger.info(
                "New registry created",
                registry_path=str(self.registry_path),
                operation="create_registry"
            )

    def _save_registry(self) -> None:
        """保存注册表数据到文件。"""
        try:
            atomic_write_json(self._registry_data, self.registry_path)
            self._logger.debug(
                "Registry saved to file",
                registry_path=str(self.registry_path),
                operation="save_registry"
            )
        except Exception as e:
            raise RegistryError(
                f"Failed to save registry: {e}",
                registry_path=str(self.registry_path),
                operation="save"
            ) from e

    def register_model(
        self,
        model_name: str,
        run_id: str,
        artifact_path: str,
        version: Optional[str] = None,
        description: Optional[str] = None,
        metrics: Optional[Dict[str, float]] = None,
        stage: str = "Staging",
        tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """将某次运行中产生的一个模型产物注册为一个新的模型版本。

        Args:
            model_name: 模型的逻辑名称，如 "foster-nmr-permeability"
            run_id: 产生该模型的运行ID
            artifact_path: 模型文件在对应运行目录中的相对路径
            version: 模型的版本号。如果为None，将自动递增版本号
            description: 可选的模型描述
            metrics: 与该模型版本直接相关的关键性能指标
            stage: 模型的生命周期阶段，默认为"Staging"
            tags: 可选的标签列表

        Returns:
            Dict[str, Any]: 注册的模型版本信息

        Raises:
            RegistryError: 注册失败时抛出

        Examples:
            >>> model_info = registry.register_model(
            ...     model_name="foster-nmr",
            ...     run_id="run-001",
            ...     artifact_path="models/final_model.pkl",
            ...     description="Baseline FOSTER-NMR model",
            ...     metrics={"rmse": 0.85, "r2": 0.92},
            ...     tags=["baseline", "validated"]
            ... )
            >>> print(model_info["version"])  # 1.0.0
        """
        if stage not in self.VALID_STAGES:
            raise ValueError(f"Invalid stage '{stage}'. Must be one of {self.VALID_STAGES}")

        # 自动生成版本号
        if version is None:
            version = self._generate_next_version(model_name)

        # 创建模型版本记录
        model_version = {
            "version": version,
            "name": model_name,
            "stage": stage,
            "description": description or "",
            "registered_at_utc": get_current_utc_timestamp(),
            "source_run": {
                "run_id": run_id,
                "artifact_path": artifact_path
            },
            "metrics": metrics or {},
            "tags": tags or []
        }

        # 添加到注册表
        if model_name not in self._registry_data:
            self._registry_data[model_name] = []

        self._registry_data[model_name].append(model_version)

        # 保存到文件
        self._save_registry()

        self._logger.info(
            "Model registered",
            model_name=model_name,
            version=version,
            run_id=run_id,
            stage=stage,
            operation="register_model"
        )

        return model_version

    def _generate_next_version(self, model_name: str) -> str:
        """为模型生成下一个版本号。"""
        if model_name not in self._registry_data or not self._registry_data[model_name]:
            return "1.0.0"

        versions = self._registry_data[model_name]

        parsed_versions = []
        for version_info in versions:
            try:
                # 使用 packaging.version 解析版本号，健壮地处理SemVer
                parsed_versions.append(Version(version_info["version"]))
            except InvalidVersion:
                # 忽略无法解析的无效版本字符串
                self._logger.warning(
                    "Ignoring invalid version string during version generation",
                    model_name=model_name,
                    invalid_version=version_info.get("version"),
                    operation="generate_version"
                )
                continue

        if not parsed_versions:
            return "1.0.0"

        # 找到最大的版本
        # packaging.version.Version 对象正确处理版本比较
        max_version = max(parsed_versions)

        # 递增主版本号并返回 "X.0.0" 格式
        return f"{max_version.major + 1}.0.0"

    def get_model_version(
        self,
        model_name: str,
        version: str
    ) -> Optional[Dict[str, Any]]:
        """获取指定模型的特定版本信息。

        Args:
            model_name: 模型名称
            version: 版本号

        Returns:
            Optional[Dict[str, Any]]: 模型版本信息，如果不存在则返回None

        Examples:
            >>> version_info = registry.get_model_version("foster-nmr", "1.0.0")
            >>> if version_info:
            ...     print(version_info["stage"])  # Staging
        """
        if model_name not in self._registry_data:
            return None

        for version_info in self._registry_data[model_name]:
            if version_info["version"] == version:
                return version_info.copy()

        return None

    def get_latest_versions(
        self,
        model_name: str,
        stages: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """获取一个模型在特定阶段的最新版本信息。

        Args:
            model_name: 模型名称
            stages: 可选的阶段列表，如果为None则返回所有阶段

        Returns:
            List[Dict[str, Any]]: 按注册时间降序排列的版本信息列表

        Examples:
            >>> # 获取生产阶段的最新版本
            >>> latest = registry.get_latest_versions(
            ...     "foster-nmr",
            ...     stages=["Production"]
            ... )
            >>> if latest:
            ...     print(f"Latest production version: {latest[0]['version']}")
        """
        if model_name not in self._registry_data:
            return []

        versions = self._registry_data[model_name]

        # 过滤阶段
        if stages:
            versions = [v for v in versions if v["stage"] in stages]

        # 按注册时间降序排序
        versions.sort(
            key=lambda x: x["registered_at_utc"],
            reverse=True
        )

        return [v.copy() for v in versions]

    def transition_stage(
        self,
        model_name: str,
        version: str,
        new_stage: str
    ) -> None:
        """改变一个模型版本的生命周期阶段。

        Args:
            model_name: 模型名称
            version: 版本号
            new_stage: 新的阶段名称

        Raises:
            ModelNotRegisteredError: 模型或版本不存在时抛出
            ValueError: 无效的阶段名称时抛出

        Examples:
            >>> # 将模型从Staging提升到Production
            >>> registry.transition_stage("foster-nmr", "1.0.0", "Production")
        """
        if new_stage not in self.VALID_STAGES:
            raise ValueError(f"Invalid stage '{new_stage}'. Must be one of {self.VALID_STAGES}")

        if model_name not in self._registry_data:
            raise ModelNotRegisteredError(
                f"Model '{model_name}' not found in registry",
                model_name=model_name,
                available_models=list(self._registry_data.keys())
            )  # 不存在模型时，available_versions 无意义

        # 查找指定版本
        version_found = False
        for version_info in self._registry_data[model_name]:
            if version_info["version"] == version:
                old_stage = version_info["stage"]
                version_info["stage"] = new_stage
                version_found = True

                self._logger.info(
                    "Model stage transitioned",
                    model_name=model_name,
                    version=version,
                    old_stage=old_stage,
                    new_stage=new_stage,
                    operation="transition_stage"
                )
                break

        if not version_found:
            available_versions = [v["version"] for v in self._registry_data[model_name]]
            raise ModelNotRegisteredError(
                f"Version '{version}' not found for model '{model_name}'",
                model_name=model_name,
                version=version,
                available_versions=available_versions  # 仅传递版本号列表
            )


        # 保存更改
        self._save_registry()

    def list_registered_models(self) -> List[str]:
        """获取所有已注册的模型名称列表。

        Returns:
            List[str]: 模型名称列表

        Examples:
            >>> models = registry.list_registered_models()
            >>> print(models)  # ["foster-nmr", "obmiq-permeability"]
        """
        return list(self._registry_data.keys())

    def get_model_summary(self, model_name: str) -> Optional[Dict[str, Any]]:
        """获取模型的摘要信息。

        Args:
            model_name: 模型名称

        Returns:
            Optional[Dict[str, Any]]: 模型摘要信息，包含版本统计等

        Examples:
            >>> summary = registry.get_model_summary("foster-nmr")
            >>> if summary:
            ...     print(f"Total versions: {summary['total_versions']}")
            ...     print(f"Production versions: {summary['stage_counts']['Production']}")
        """
        if model_name not in self._registry_data:
            return None

        versions = self._registry_data[model_name]

        # 统计各阶段版本数
        stage_counts = {}
        for stage in self.VALID_STAGES:
            stage_counts[stage] = sum(1 for v in versions if v["stage"] == stage)

        # 获取最新版本
        latest_version = None
        if versions:
            latest_version = max(versions, key=lambda x: x["registered_at_utc"])

        return {
            "model_name": model_name,
            "total_versions": len(versions),
            "stage_counts": stage_counts,
            "latest_version": latest_version["version"] if latest_version else None,
            "latest_stage": latest_version["stage"] if latest_version else None,
            "latest_registered_at": latest_version["registered_at_utc"] if latest_version else None
        }

    def delete_model_version(
        self,
        model_name: str,
        version: str
    ) -> None:
        """删除指定的模型版本。

        Args:
            model_name: 模型名称
            version: 版本号

        Raises:
            ModelNotRegisteredError: 模型或版本不存在时抛出

        Examples:
            >>> registry.delete_model_version("foster-nmr", "0.1.0")
        """
        if model_name not in self._registry_data:
            raise ModelNotRegisteredError(
                f"Model '{model_name}' not found in registry",
                model_name=model_name,
                available_models=list(self._registry_data.keys())
            )

        versions = self._registry_data[model_name]
        original_count = len(versions)

        # 移除指定版本
        self._registry_data[model_name] = [
            v for v in versions if v["version"] != version
        ]

        if len(self._registry_data[model_name]) == original_count:
            available_versions = [v["version"] for v in versions]
            raise ModelNotRegisteredError(
                f"Version '{version}' not found for model '{model_name}'",
                model_name=model_name,
                version=version,
                available_versions=available_versions
            )


        # 如果模型没有版本了，删除整个模型条目
        if not self._registry_data[model_name]:
            del self._registry_data[model_name]

        # 保存更改
        self._save_registry()

        self._logger.info(
            "Model version deleted",
            model_name=model_name,
            version=version,
            operation="delete_version"
        )
