from __future__ import annotations

from typing import Any, NamedTuple

from logwp.infra.exceptions import WpError


class WpFileError(WpError):
    """文件相关错误基类（打开、解析、写入等）。

    支持同步和异步I/O操作的错误处理。
    """


class WpFileFormatError(WpFileError):
    """WP Excel 文件结构/内容不符合规范。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="validate_excel_structure",
        ...     file_path="invalid.xlsx",
        ...     sheet_name="head_info"
        ... )
        >>> raise WpFileFormatError("缺少必需的表头行", context=ctx)
    """


class WpIOError(WpFileError):
    """底层 I/O 失败（文件占用、磁盘写保护等）。

    处理传统同步I/O错误。
    """


class WpAsyncIOError(WpFileError):
    """异步 I/O 操作失败。

    处理异步文件读写、网络I/O等现代化I/O操作错误。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="async_read_large_file",
        ...     file_path="large_dataset.xlsx",
        ...     additional_info={"chunk_size": 50000, "worker_count": 4}
        ... )
        >>> raise WpAsyncIOError("异步读取超时", context=ctx)
    """


class WpMemoryLimitError(WpFileError):
    """处理大文件时内存峰值超限。

    支持CPU和GPU内存限制检查。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="load_large_dataset",
        ...     file_path="huge_data.xlsx",
        ...     additional_info={
        ...         "memory_used_mb": 8192,
        ...         "memory_limit_mb": 4096,
        ...         "chunk_size": 100000
        ...     }
        ... )
        >>> raise WpMemoryLimitError("内存使用超限", context=ctx)
    """

