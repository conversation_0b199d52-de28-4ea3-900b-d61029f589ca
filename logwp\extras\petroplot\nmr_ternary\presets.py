"""logwp.extras.petroplot.nmr_ternary.presets - NMR三元图便捷配置预设

本模块提供了一系列便捷函数，用于快速生成针对特定场景的 `NmrTernaryPlotConfig`
和 `PlotProfile` 对象。这旨在简化用户操作，避免直接处理复杂的配置模型。

Architecture
------------
层次/依赖: petroplot/nmr_ternary便捷方法层，被最终用户调用
设计原则: 用户友好、封装复杂性、提供最佳实践
"""

from typing import Literal, Tuple, Optional, Dict, Any

from logwp.extras.plotting import PlotProfile, registry
from .config import (
    NmrTernaryPlotConfig,
    ContinuousColorConfig,
    DataMarkerStyle,
    NullMarkerStyle,
    BackgroundRegionConfig,
    LegendConfig,
    ColorBarConfig
)
from .constants import NmrTernaryPlotProfiles, NMR_TERNARY_REGION_COLOR_SCHEMES


def create_publication_ready_perm_config(
    title: Optional[str] = None,
    colorscale: str = "Plasma",
    cmin: float = -2.0,
    cmax: float = 3.0,
    distinguish_null_color: bool = True,
    enable_background: bool = True,
    region_color_scheme: str = 'CNLC',
    show_legend: bool = True,
    width_inches: float = 3.5,
    height_inches: float = 3.2,
    dpi: int = 300,
    font_size_pt: int = 10,
    tick_font_size_pt: int = 8,
    marker_size: int = 8,
    legend_position: Literal['right', 'bottom'] = 'right',
) -> Tuple[NmrTernaryPlotConfig, PlotProfile]:
    """
    创建一个适用于英文论文发表的、以渗透率为颜色轴的便捷配置。

    此预设针对的场景是：
    - 颜色轴为对数渗透率 (log(K))。
    - 数据已预先归一化。
    - 标签和图例针对出版物进行了优化。
    - **输出图像尺寸符合常见期刊单栏宽度要求。**

    Note:
        对于科学期刊，常见的图表宽度标准如下：
        - **单栏图 (Single-column)**: 约 3.5 英寸 (8.5 - 9 cm)
        - **1.5栏图 (1.5-column)**: 约 5.5 英寸 (13 - 14 cm)
        - **双栏/通栏图 (Double-column/Full-width)**: 约 7 英寸 (17 - 18 cm)

        此预设的默认宽度 `width_inches=3.5` 即是针对最常见的单栏图。

    Args:
        title: 图表主标题。如果为None，则不显示标题。
        colorscale: Plotly颜色映射方案。对于渗透率这类顺序数据，强烈推荐使用
                    'Viridis', 'Plasma', 'YlGnBu' 等感知均匀的色阶，避免使用 'Jet'。
        cmin: 颜色条范围的最小值。
        cmax: 颜色条范围的最大值。
        distinguish_null_color: 是否区分并独立显示颜色列中的空值点。
        enable_background: 是否绘制岩石物理背景分区。
        region_color_scheme: 背景分区的颜色方案 ('CNLC' 或 'Geolog')。
        show_legend: 是否显示图例。
        width_inches: 输出图像的物理宽度（英寸）。默认为3.5英寸，适合期刊单栏图。
        height_inches: 输出图像的物理高度（英寸）。
        dpi: 输出图像的分辨率 (dots per inch)。
        font_size_pt: 主字体大小（点），用于坐标轴标签等。
        tick_font_size_pt: 刻度和图例的字体大小（点）。
        marker_size: 数据点的大小。
        legend_position: 图例位置。'right'为图表右侧（默认），'bottom'为图表下方。
                         当图例项过多时，推荐使用 'bottom'，图例将自动水平排列。

    Returns:
        一个元组，包含配置好的 (NmrTernaryPlotConfig, PlotProfile) 对象。
    """
    # --- 1. 准备配置 ---
    # a. 根据图例位置，调整图像高度以确保有足够空间
    if legend_position == 'bottom':
        height_inches += 0.6

    # b. 构建主绘图配置对象
    plot_config = NmrTernaryPlotConfig(
        # 如果title为None，则不显示标题
        show_title=title is not None,
        title=title or "",
        # 测井数据通常已归一化
        normalize_data=False,
        # 【重构】直接传递布局意图，让配置模型自动处理具体坐标
        legend_position=legend_position,
        # 配置颜色映射
        color_mapping=ContinuousColorConfig(
            log_transform=True,
            colorscale=colorscale,
            cmin=cmin,
            cmax=cmax,
        ),
        # 【重构】颜色条只需配置内容，布局由 legend_position 驱动
        color_bar=ColorBarConfig(
            title="log(K)",
        ),
        # 配置数据点和空值点的图例标签
        distinguish_null_color=distinguish_null_color,
        data_marker_style=DataMarkerStyle(label="w/ K"),
        null_marker_style=NullMarkerStyle(
            symbol="circle-open",
            label="w/o K",
            size_factor=0.5,
            color="grey"),

        # 配置背景分区
        background_regions=BackgroundRegionConfig(
            enable=enable_background,
            opacity=0.2,
            legend_title="",  # 论文图中通常不显示分区的总标题
            region_colors=NMR_TERNARY_REGION_COLOR_SCHEMES.get(region_color_scheme)
        ),
        # 【重构】图例只需配置内容，布局由 legend_position 驱动
        legend=LegendConfig(
            show=show_legend,
            title="",
        )
    )

    # --- 2. 基于默认模板修改 PlotProfile ---
    base_profile = registry.get(NmrTernaryPlotProfiles.DEFAULT.value)
    plot_profile = base_profile.with_updates(
        save_config={
            "width": width_inches,
            "height": height_inches,
            "dpi": dpi
        },
        # 更新字体大小以符合出版标准
        rc_params={
            "font.size": font_size_pt,
            "legend.fontsize": tick_font_size_pt,
        },

        title_props={
            "fontsize": font_size_pt + 2,  # 主标题通常比坐标轴标签大2pt
        },
        artist_props={
            "colorbar": {
                "tickfont": {"size": tick_font_size_pt},
                "titlefont": {"size": tick_font_size_pt} # 新增：确保标题字体与刻度字体大小一致
            },

            "data_marker": {
                "size": marker_size,
                "line": {
                    "width": 0.3,
                    "color": "black"
                }

            }
        },
    )
    return plot_config, plot_profile
