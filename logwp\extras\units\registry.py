"""
Defines the UnitRegistry, which manages all unit definitions.
"""
from typing import Union, Optional
import os
import yaml
from .contexts import Context, temperature_context
from .containers import Dimension, Unit
from .exceptions import UndefinedUnitError
from .quantity import Quantity


class UnitRegistry:
    """
    Manages the definition, parsing, and retrieval of units.
    This is the central object that users will interact with.
    """

    def __init__(self, definition_filepath: Optional[str] = None):
        self._units: dict[str, Unit] = {}
        self._map: dict[str, Unit] = {}
        self._contexts: dict[str, Context] = {}
        self._contexts_by_dimension: dict[Dimension, Context] = {}

        # --- Define Base Dimensions for convenience ---
        self.dimensionless = Dimension()
        self.length = Dimension(length=1)
        self.mass = Dimension(mass=1)
        self.time = Dimension(time=1)
        self.temperature = Dimension(temperature=1)
        self.electric_current = Dimension(electric_current=1) # 电流

        # --- 定义派生量纲，方便使用 ---
        self.area = self.length**2
        self.volume = self.length**3
        self.density = self.mass / self.volume
        self.force = self.mass * self.length / self.time**2
        self.pressure = self.force / self.area
        # 电阻 = 质量 * 长度^2 / (时间^3 * 电流^2)
        self.resistance = self.mass * self.length**2 / (self.time**3 * self.electric_current**2)
        # 电阻率 = 电阻 * 长度
        self.resistivity = self.resistance * self.length
        # 声波时差 (慢度) = 时间 / 长度
        self.slowness = self.time / self.length
        # 粘度 = 质量 / (长度 * 时间)
        self.viscosity = self.mass / (self.length * self.time)

        # --- 注册基础和常用SI单位 ---
        self._register_unit(Unit("meter", "m", self.length, 1.0, aliases=("meters",)))
        self._register_unit(Unit("kilogram", "kg", self.mass, 1.0, aliases=("kilograms",)))
        self._register_unit(Unit("second", "s", self.time, 1.0, aliases=("seconds",)))
        self._register_unit(Unit("ampere", "A", self.electric_current, 1.0, aliases=("amperes",)))
        self._register_unit(Unit("centimeter", "cm", self.length, 0.01, aliases=("centimeters",)))
        self._register_unit(Unit("gram", "g", self.mass, 0.001, aliases=("grams",)))
        self._register_unit(Unit("hour", "h", self.time, 3600, aliases=("hours",)))
        self._register_unit(Unit("pascal", "Pa", self.pressure, 1.0))

        # --- 注册石油工程单位 ---

        # Pressure
        self._register_unit(Unit("psi", "psi", self.pressure, 6894.757, aliases=("pound_force_per_square_inch",)))
        self._register_unit(Unit("bar", "bar", self.pressure, 100000.0))
        self._register_unit(Unit("megapascal", "MPa", self.pressure, 1e6))
        self._register_unit(Unit("kilopascal", "kPa", self.pressure, 1e3))

        # Permeability (Dimensionally an area)
        self._register_unit(Unit("darcy", "D", self.area, 9.869233e-13))
        self._register_unit(Unit("millidarcy", "mD", self.area, 9.869233e-16))

        # Density
        self._register_unit(Unit("g_cm3", "g/cm³", self.density, 1000.0, aliases=("gram_per_cubic_centimeter",)))

        # Resistivity / 电阻率
        self._register_unit(Unit("ohm", "ohm", self.resistance, 1.0, aliases=("Ω",)))
        self._register_unit(Unit("ohm_meter", "ohm.m", self.resistivity, 1.0))

        # Slowness / 声波时差
        self._register_unit(Unit("us_ft", "us/ft", self.slowness, 3.28084e-6)) # 1 us/ft = 3.28084e-6 s/m
        self._register_unit(Unit("us_m", "us/m", self.slowness, 1e-6)) # 1 us/m = 1e-6 s/m

        # Viscosity / 粘度
        self._register_unit(Unit("pascal_second", "Pa.s", self.viscosity, 1.0))
        self._register_unit(Unit("centipoise", "cP", self.viscosity, 0.001)) # 1 cP = 0.001 Pa.s

        # Volume
        self._register_unit(Unit("barrel", "bbl", self.volume, 0.1589873, aliases=("barrels",)))

        # --- 注册需要上下文转换的单位 ---

        # Temperature (handled by a context)
        self.add_context(temperature_context, self.temperature)
        self._register_unit(Unit("kelvin", "K", self.temperature, 1.0, 0.0, aliases=("degK",)))
        self._register_unit(Unit("celsius", "degC", self.temperature, 1.0, 273.15, aliases=("C",)))
        # The conversion from Fahrenheit to Kelvin is K = (F - 32) * 5/9 + 273.15
        # This can be rewritten as K = F * (5/9) + (273.15 - 32 * 5/9)
        # So, scale = 5/9 and offset = 273.15 - 32 * 5/9
        self._register_unit(Unit("fahrenheit", "degF", self.temperature, 5/9, 273.15 - (32*5/9), aliases=("F",)))

        # Load custom units from a file if provided
        if definition_filepath and os.path.exists(definition_filepath):
            self.load_definitions_from_file(definition_filepath)

    def _register_unit(self, unit: Unit):
        """Internal helper to register a unit and its names/symbols."""
        # Allow overriding of existing units to support customization.
        if unit.name in self._units:
            # A more advanced implementation might use Python's logging module.
            print(f"Info: Overriding existing unit definition for '{unit.name}'.")
        self._units[unit.name] = unit

        keys_to_register = [unit.name, unit.symbol] + list(unit.aliases)
        for key in keys_to_register:
            if key and key in self._map:
                raise ValueError(f"Name or symbol '{key}' for unit '{unit.name}' conflicts with existing unit '{self._map[key].name}'.")
            if key:
                self._map[key] = unit

    def register_unit(self, unit: Unit):
        """Public method to programmatically register a new unit."""
        self._register_unit(unit)

    def add_context(self, context: Context, dimension: Dimension):
        """Registers a context for a given dimension."""
        if context.name in self._contexts:
            raise ValueError(f"Context '{context.name}' is already defined.")
        self._contexts[context.name] = context
        self._contexts_by_dimension[dimension] = context

    def __getitem__(self, key: str) -> Unit:
        """Allows retrieving units using dictionary-style access (e.g., ureg['m'])."""
        try:
            return self._map[key]
        except KeyError:
            raise UndefinedUnitError(f"Unit '{key}' is not defined in the registry.") from None

    def __getattr__(self, name: str) -> Unit:
        """Allows retrieving units using attribute-style access (e.g., ureg.meter)."""
        try:
            return self[name]
        except UndefinedUnitError:
            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

    def Quantity(self, value: Union[int, float], unit_name: Union[str, Unit]) -> Quantity:
        """Factory method to create a Quantity object."""
        unit_object = unit_name if isinstance(unit_name, Unit) else self[unit_name]
        return Quantity(value, unit_object, registry=self)

    def get_context_for_dimension(self, dimension: Dimension) -> Optional[Context]:
        """Retrieves the context associated with a dimension, if any."""
        return self._contexts_by_dimension.get(dimension)

    def load_definitions_from_file(self, filepath: str):
        """
        Loads and registers unit definitions from a YAML file.

        The file should contain a top-level 'units' key, which is a list of
        unit definition dictionaries.
        """
        print(f"Info: Loading custom unit definitions from '{filepath}'...")
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
        except Exception as e:
            print(f"Warning: Could not read or parse YAML file at '{filepath}'. Error: {e}")
            return

        if not isinstance(data, dict) or 'units' not in data:
            print(f"Warning: Invalid format in '{filepath}'. Expected a top-level 'units' key containing a list.")
            return

        for definition in data.get('units', []):
            try:
                # The dimension name in the YAML (e.g., "pressure") must match an attribute
                # on the UnitRegistry instance (e.g., self.pressure).
                dimension_name = definition['dimension']
                dimension_obj = getattr(self, dimension_name)

                unit = Unit(
                    name=definition['name'],
                    symbol=definition['symbol'],
                    dimension=dimension_obj,
                    scale=float(definition['scale']),
                    offset=float(definition.get('offset', 0.0)),
                    aliases=tuple(definition.get('aliases', []))
                )
                self.register_unit(unit)
            except Exception as e:
                print(f"Warning: Skipping invalid unit definition {definition}. Error: {e}")
