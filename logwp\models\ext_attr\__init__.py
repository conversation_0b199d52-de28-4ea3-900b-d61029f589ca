"""
logwp.attr - 测井属性管理统一模块

提供统一的属性管理接口，包括核心管理器、处理器和预定义属性。

Architecture
------------
层次/依赖: logwp包属性管理层，统一入口点
设计原则: 统一接口、格式无关、类型安全、遵循现有组织模式
性能特征: 高效查找、缓存优化、批量处理

Core Components
---------------
- **manager**: 属性管理核心组件（AttributeManager）
- **comp_processor**: COMP类型JSON处理器
- **predefined**: 预定义属性（T2_Axis等标准属性）

Examples
--------
>>> from logwp.attr import AttributeManager, CompProcessor, T2AxisProcessor
>>>
>>> # 创建属性管理器
>>> manager = AttributeManager()
>>> manager.add_attribute("DS", "NMR_logs", None, None, "T2_AXIS", "COMP", "ms", t2_config)
>>>
>>> # 使用COMP处理器
>>> processor = CompProcessor()
>>> validation = processor.validate_json_structure(comp_data)
>>>
>>> # 使用预定义属性
>>> t2_processor = T2AxisProcessor()
>>> t2_axis = t2_processor.create_domain_object(json_data)

References
----------
- 《SCAPE_SAD_软件架构设计.md》FAP-1到FAP-10 - 格式无关原则
- 《SCAPE_CCG_编码与通用规范.md》CS-2 - 包前缀命名规范
"""

from __future__ import annotations

# 核心属性管理组件
from .manager import (
    ExtAttributeManager,
    ExtAttributeRecord,
)

# COMP类型处理器
from .comp_processor import (
    StandardCompProcessor,
    JsonValidationResult,
    get_default_json_processor,
)

# 预定义属性
from .predefined import (
    T2AxisProcessor,
    T2AxisLog10,
    T2AxisExp2,
)

__all__ = [
    # 核心组件
    "ExtAttributeManager",
    "ExtAttributeRecord",

    # COMP处理器
    "StandardCompProcessor",
    "JsonValidationResult",
    "get_default_json_processor",

    # 预定义属性
    "T2AxisProcessor",
    "T2AxisLog10",
    "T2AxisExp2",
]
