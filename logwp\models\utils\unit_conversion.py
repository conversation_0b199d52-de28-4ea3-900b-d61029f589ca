#!/usr/bin/env python3
"""深度单位转换工具模块。

提供测井数据中深度单位的标准化转换功能，支持米和英尺的相互转换。

Architecture
------------
层次/依赖: utils层，单位转换工具
设计原则: 精确转换、类型安全、性能优化
性能特征: 数值计算优化、批量转换支持

Examples:
    >>> # 基本转换
    >>> meters = feet_to_meters(100.0)
    >>> feet = meters_to_feet(30.48)

    >>> # 批量转换
    >>> import numpy as np
    >>> depths_m = convert_depth_array(depths_ft, "ft", "m")

    >>> # 单位验证
    >>> assert is_valid_depth_unit("m")
    >>> assert not is_valid_depth_unit("km")

References:
    《SCAPE_DDS_详细设计_logwp.md》§4.2 - 深度单位管理系统
"""

import numpy as np
import pandas as pd
from typing import Union

from logwp.models.constants import WpDepthUnit
from logwp.models.exceptions import WpValidationError
from logwp.infra.exceptions import ErrorContext

# 转换常数
FEET_TO_METER_FACTOR = 0.3048  # 1英尺 = 0.3048米
METER_TO_FEET_FACTOR = 1.0 / FEET_TO_METER_FACTOR  # 1米 = 3.28084英尺


def feet_to_meters(feet: float) -> float:
    """将英尺转换为米。

    Args:
        feet: 英尺值

    Returns:
        float: 对应的米值

    Examples:
        >>> meters = feet_to_meters(100.0)
        >>> assert abs(meters - 30.48) < 1e-10
    """
    return feet * FEET_TO_METER_FACTOR


def meters_to_feet(meters: float) -> float:
    """将米转换为英尺。

    Args:
        meters: 米值

    Returns:
        float: 对应的英尺值

    Examples:
        >>> feet = meters_to_feet(30.48)
        >>> assert abs(feet - 100.0) < 1e-10
    """
    return meters * METER_TO_FEET_FACTOR


def convert_depth_value(value: float, from_unit: str, to_unit: str) -> float:
    """转换单个深度值的单位。

    Args:
        value: 要转换的深度值
        from_unit: 源单位（"m" 或 "ft"）
        to_unit: 目标单位（"m" 或 "ft"）

    Returns:
        float: 转换后的深度值

    Raises:
        WpValidationError: 当单位不支持时抛出

    Examples:
        >>> # 英尺转米
        >>> meters = convert_depth_value(100.0, "ft", "m")
        >>> assert abs(meters - 30.48) < 1e-10

        >>> # 米转英尺
        >>> feet = convert_depth_value(30.48, "m", "ft")
        >>> assert abs(feet - 100.0) < 1e-10

        >>> # 相同单位
        >>> same = convert_depth_value(100.0, "m", "m")
        >>> assert same == 100.0
    """
    # 验证单位
    if not is_valid_depth_unit(from_unit):
        raise WpValidationError(
            f"不支持的源深度单位: {from_unit}",
            context=ErrorContext(
                operation="convert_depth_value",
                additional_info={
                    "from_unit": from_unit,
                    "to_unit": to_unit,
                    "supported_units": list(WpDepthUnit.get_all_units())
                }
            )
        )

    if not is_valid_depth_unit(to_unit):
        raise WpValidationError(
            f"不支持的目标深度单位: {to_unit}",
            context=ErrorContext(
                operation="convert_depth_value",
                additional_info={
                    "from_unit": from_unit,
                    "to_unit": to_unit,
                    "supported_units": list(WpDepthUnit.get_all_units())
                }
            )
        )

    # 相同单位直接返回
    if from_unit == to_unit:
        return value

    # 执行转换
    if from_unit == WpDepthUnit.FEET and to_unit == WpDepthUnit.METER:
        return feet_to_meters(value)
    elif from_unit == WpDepthUnit.METER and to_unit == WpDepthUnit.FEET:
        return meters_to_feet(value)
    else:
        # 理论上不会到达这里，因为已经验证了单位
        raise WpValidationError(
            f"不支持的单位转换: {from_unit} -> {to_unit}",
            context=ErrorContext(
                operation="convert_depth_value",
                additional_info={
                    "from_unit": from_unit,
                    "to_unit": to_unit
                }
            )
        )


def convert_depth_array(
    values: Union[np.ndarray, pd.Series, list],
    from_unit: str,
    to_unit: str
) -> np.ndarray:
    """批量转换深度数组的单位。

    Args:
        values: 要转换的深度值数组
        from_unit: 源单位（"m" 或 "ft"）
        to_unit: 目标单位（"m" 或 "ft"）

    Returns:
        np.ndarray: 转换后的深度值数组

    Examples:
        >>> import numpy as np
        >>> depths_ft = np.array([100.0, 200.0, 300.0])
        >>> depths_m = convert_depth_array(depths_ft, "ft", "m")
        >>> expected = np.array([30.48, 60.96, 91.44])
        >>> assert np.allclose(depths_m, expected)
    """
    # 转换为numpy数组
    if isinstance(values, pd.Series):
        arr = values.values
    elif isinstance(values, list):
        arr = np.array(values)
    else:
        arr = np.asarray(values)

    # 相同单位直接返回副本
    if from_unit == to_unit:
        return arr.copy()

    # 验证单位并执行转换
    if from_unit == WpDepthUnit.FEET and to_unit == WpDepthUnit.METER:
        return arr * FEET_TO_METER_FACTOR
    elif from_unit == WpDepthUnit.METER and to_unit == WpDepthUnit.FEET:
        return arr * METER_TO_FEET_FACTOR
    else:
        # 使用单个值转换函数进行验证
        convert_depth_value(0.0, from_unit, to_unit)  # 这会抛出适当的异常
        return arr  # 不会到达这里


def is_valid_depth_unit(unit: str) -> bool:
    """检查是否为有效的深度单位。

    Args:
        unit: 要检查的单位字符串

    Returns:
        bool: 是否为有效的深度单位

    Examples:
        >>> assert is_valid_depth_unit("m")
        >>> assert is_valid_depth_unit("ft")
        >>> assert not is_valid_depth_unit("km")
        >>> assert not is_valid_depth_unit("inch")
    """
    return unit in WpDepthUnit.get_all_units()


def get_conversion_factor(from_unit: str, to_unit: str) -> float:
    """获取单位转换系数。

    Args:
        from_unit: 源单位
        to_unit: 目标单位

    Returns:
        float: 转换系数

    Raises:
        WpValidationError: 当单位不支持时抛出

    Examples:
        >>> factor = get_conversion_factor("ft", "m")
        >>> assert abs(factor - 0.3048) < 1e-10

        >>> factor = get_conversion_factor("m", "ft")
        >>> assert abs(factor - 3.28084) < 1e-5

        >>> factor = get_conversion_factor("m", "m")
        >>> assert factor == 1.0
    """
    if from_unit == to_unit:
        return 1.0
    elif from_unit == WpDepthUnit.FEET and to_unit == WpDepthUnit.METER:
        return FEET_TO_METER_FACTOR
    elif from_unit == WpDepthUnit.METER and to_unit == WpDepthUnit.FEET:
        return METER_TO_FEET_FACTOR
    else:
        # 验证单位并抛出异常
        convert_depth_value(0.0, from_unit, to_unit)
        return 1.0  # 不会到达这里


def format_depth_with_unit(value: float, unit: str, precision: int = 2) -> str:
    """格式化深度值并添加单位。

    Args:
        value: 深度值
        unit: 单位
        precision: 小数位数

    Returns:
        str: 格式化后的深度字符串

    Examples:
        >>> formatted = format_depth_with_unit(2500.123, "m", 2)
        >>> assert formatted == "2500.12 m"

        >>> formatted = format_depth_with_unit(8202.1, "ft", 1)
        >>> assert formatted == "8202.1 ft"
    """
    return f"{value:.{precision}f} {unit}"
