"""logwp.models.curve.service - 曲线元数据服务层

实现曲线元数据管理的内部服务层，遵循SAD文档的Utility/Helper Pattern设计模式。

Architecture
------------
层次/依赖: curve模块服务层，依赖curve、constants、exceptions
设计原则: 无状态服务、职责分离、类型安全
性能特征: 高效算法、内存优化、批量处理

服务模块：
- curve_analysis: 曲线列表分析服务
- metadata_extraction: 曲线元数据提取服务
- query_translation: 查询字符串转换服务
- curve_expansion: 曲线名称展开服务
- metadata_rename: 曲线元数据重命名服务
- curve_statistics: 曲线统计分析服务

References
----------
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
- 《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata服务层重构
"""

# 服务模块导入
from . import curve_analysis
from . import metadata_extraction
from . import query_translation
from . import curve_expansion
from . import metadata_rename
from logwp.models.datasets.internal import curve_statistics

__all__ = [
    "curve_analysis",
    "metadata_extraction",
    "query_translation",
    "curve_expansion",
    "metadata_rename",
    "curve_statistics",
]
