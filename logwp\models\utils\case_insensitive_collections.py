from __future__ import annotations

"""logwp.models.utils.case_insensitive_collections - CIIA大小写不敏感容器

实现CIIA架构的大小写不敏感容器类，支持冲突检测和原始格式保持。

Architecture
------------
层次/依赖: utils层容器，CIIA架构第三层（大小写不敏感容器）
设计原则: 容器抽象、冲突检测、原始格式保持、性能优化
性能特征: O(1)查找、内存优化、批量操作

遵循CIIA规范：
- CIIA-1: 显示索引分离 - 保存原始键用于显示
- CIIA-3: 冲突检测 - 检测规范化后相同的键
- CIIA-4: 透明集成 - 提供标准容器接口
- CIIA-8: 容器支持 - 实现Dict和Set容器

Best Practices - 最佳实践
-----------------------
**推荐的类型注解和初始化方式**：

```python
from dataclasses import dataclass, field
from logwp.models.utils import CaseInsensitiveDict

@dataclass
class MyClass:
    # ✅ 正确方式：直接使用类名，不添加类型参数
    data: CaseInsensitiveDict = field(default_factory=CaseInsensitiveDict)
    tags: CaseInsensitiveSet = field(default_factory=CaseInsensitiveSet)

    # ❌ 错误方式：不要添加额外的类型参数
    # data: CaseInsensitiveDict[Any] = field(default_factory=CaseInsensitiveDict)
    # data: CaseInsensitiveDict[str] = field(default_factory=CaseInsensitiveDict)
```

**原因说明**：
- `CaseInsensitiveDict` 已定义为 `MutableMapping[str, Any]`
- `CaseInsensitiveSet` 已定义为 `MutableSet[str]`
- 添加类型参数会导致类型检查器报错
- 保持代码简洁和类型安全

**使用示例**：
```python
# 基本使用
data = CaseInsensitiveDict()
data["OBMIQ_logs"] = dataset1
assert data["obmiq_logs"] == dataset1  # 大小写不敏感

# 初始化时传入数据
data = CaseInsensitiveDict({"Project_Name": "Santos", "Version": "1.0"})
assert data["project_name"] == "Santos"

# 冲突检测
try:
    data["PROJECT_NAME"] = "Other"  # 抛出 WpValidationError
except WpValidationError as e:
    print(f"检测到冲突: {e}")
```

References
----------
- 《SCAPE_SAD_软件架构设计.md》§4.6 - CIIA架构设计
- .augment-guidelines CIIA规则 - CIIA-1, CIIA-3, CIIA-4, CIIA-8
"""

from typing import Any, Iterator, KeysView, ValuesView, ItemsView
from collections.abc import MutableMapping, MutableSet

from logwp.models.utils.string_normalizer import WpStringNormalizer
from logwp.models.constants import WpNormalizationMode
from logwp.models.exceptions import WpValidationError
from logwp.infra.exceptions import ErrorContext

__all__ = [
    "CaseInsensitiveDict",
    "CaseInsensitiveSet",
]


class CaseInsensitiveDict(MutableMapping[str, Any]):
    """大小写不敏感字典（格式无关）。

    提供大小写不敏感的键值存储，同时保持原始键的显示格式。
    支持冲突检测，确保同一作用域内不存在规范化后相同的键。

    Architecture
    ------------
    层次/依赖: utils层容器，CIIA架构第三层
    设计原则: 容器抽象、冲突检测、原始格式保持
    性能特征: O(1)查找、内存优化、批量操作

    遵循CIIA规范：
    - CIIA-1: 显示索引分离 - _key_map保存原始键映射
    - CIIA-3: 冲突检测 - 设置时检测大小写冲突
    - CIIA-4: 透明集成 - 提供标准dict接口
    - CIIA-8: 容器支持 - 实现MutableMapping协议

    Examples:
        >>> # 基本使用
        >>> d = CaseInsensitiveDict()
        >>> d["OBMIQ_logs"] = dataset1
        >>> d["K_Label"] = dataset2
        >>>
        >>> # 大小写不敏感查找
        >>> assert d["obmiq_logs"] == dataset1
        >>> assert d["k_label"] == dataset2
        >>>
        >>> # 冲突检测
        >>> d["OBMIQ_LOGS"] = dataset3  # 抛出WpValidationError

    Type Annotation Best Practices:
        >>> from dataclasses import dataclass, field
        >>>
        >>> @dataclass
        >>> class MyModel:
        ...     # ✅ 正确：直接使用类名
        ...     data: CaseInsensitiveDict = field(default_factory=CaseInsensitiveDict)
        ...
        ...     # ❌ 错误：不要添加类型参数
        ...     # data: CaseInsensitiveDict[Any] = field(default_factory=CaseInsensitiveDict)

    References:
        《SCAPE_SAD_软件架构设计.md》§4.6.3 - 核心组件集成策略
    """

    def __init__(self, *args, **kwargs):
        """初始化大小写不敏感字典。

        Args:
            *args: 位置参数，支持dict、可迭代对象等
            **kwargs: 关键字参数
        """
        self._data: dict[str, Any] = {}
        self._key_map: dict[str, str] = {}  # 规范化键 -> 原始键映射
        self._mode = WpNormalizationMode.UNICODE_CASEFOLD

        # 初始化数据
        if args or kwargs:
            self.update(*args, **kwargs)

    def __setitem__(self, key: str, value: Any) -> None:
        """设置键值对，检测大小写冲突（CIIA-3规范）。

        Args:
            key: 键名
            value: 值

        Raises:
            WpValidationError: 检测到大小写冲突时抛出

        Examples:
            >>> d = CaseInsensitiveDict()
            >>> d["OBMIQ_logs"] = "dataset1"
            >>> d["obmiq_LOGS"] = "dataset2"  # 抛出异常
        """
        if not isinstance(key, str):
            raise TypeError(f"键必须是字符串，得到: {type(key).__name__}")

        normalized_key = WpStringNormalizer.normalize(key, self._mode)

        # 检查冲突（CIIA-3规范）
        if normalized_key in self._key_map:
            existing_key = self._key_map[normalized_key]
            if existing_key != key:
                raise WpValidationError(
                    f"检测到大小写冲突：'{key}' 与 '{existing_key}' 规范化后相同",
                    context=ErrorContext(
                        operation="set_case_insensitive_key",
                        additional_info={
                            "conflicting_keys": [key, existing_key],
                            "normalized_key": normalized_key
                        }
                    )
                )

        # 更新映射和存储
        self._key_map[normalized_key] = key
        self._data[key] = value

    def __getitem__(self, key: str) -> Any:
        """大小写不敏感获取值（CIIA-1规范）。

        Args:
            key: 键名（大小写不敏感）

        Returns:
            Any: 对应的值

        Raises:
            KeyError: 键不存在时抛出

        Examples:
            >>> d = CaseInsensitiveDict({"OBMIQ_logs": "dataset1"})
            >>> assert d["obmiq_logs"] == "dataset1"
            >>> assert d["OBMIQ_LOGS"] == "dataset1"
        """
        normalized_key = WpStringNormalizer.normalize(key, self._mode)
        if normalized_key in self._key_map:
            original_key = self._key_map[normalized_key]
            return self._data[original_key]
        raise KeyError(f"键 '{key}' 不存在")

    def __delitem__(self, key: str) -> None:
        """大小写不敏感删除键值对。

        Args:
            key: 键名（大小写不敏感）

        Raises:
            KeyError: 键不存在时抛出
        """
        normalized_key = WpStringNormalizer.normalize(key, self._mode)
        if normalized_key in self._key_map:
            original_key = self._key_map[normalized_key]
            del self._data[original_key]
            del self._key_map[normalized_key]
        else:
            raise KeyError(f"键 '{key}' 不存在")

    def __contains__(self, key: object) -> bool:
        """大小写不敏感包含检查。

        Args:
            key: 键名

        Returns:
            bool: 是否包含该键
        """
        if not isinstance(key, str):
            return False
        normalized_key = WpStringNormalizer.normalize(key, self._mode)
        return normalized_key in self._key_map

    def __iter__(self) -> Iterator[str]:
        """迭代原始键名。"""
        return iter(self._data)

    def __len__(self) -> int:
        """返回字典长度。"""
        return len(self._data)

    def __repr__(self) -> str:
        """字典的字符串表示。"""
        return f"{self.__class__.__name__}({dict(self._data)})"

    def keys(self) -> KeysView[str]:
        """返回原始格式的键视图。"""
        return self._data.keys()

    def values(self) -> ValuesView[Any]:
        """返回值视图。"""
        return self._data.values()

    def items(self) -> ItemsView[str, Any]:
        """返回键值对视图（原始键格式）。"""
        return self._data.items()

    def get_original_key(self, key: str) -> str | None:
        """获取原始键格式（CIIA-1规范）。

        Args:
            key: 键名（大小写不敏感）

        Returns:
            str | None: 原始键格式，不存在时返回None

        Examples:
            >>> d = CaseInsensitiveDict({"OBMIQ_logs": "dataset1"})
            >>> assert d.get_original_key("obmiq_logs") == "OBMIQ_logs"
        """
        normalized_key = WpStringNormalizer.normalize(key, self._mode)
        return self._key_map.get(normalized_key)

    def keys_normalized(self) -> list[str]:
        """返回规范化格式的键列表。

        Returns:
            list[str]: 规范化键列表
        """
        return list(self._key_map.keys())

    def clear(self) -> None:
        """清空字典。"""
        self._data.clear()
        self._key_map.clear()


class CaseInsensitiveSet(MutableSet[str]):
    """大小写不敏感集合（格式无关）。

    提供大小写不敏感的集合操作，同时保持原始元素的显示格式。

    Architecture
    ------------
    层次/依赖: utils层容器，CIIA架构第三层
    设计原则: 容器抽象、冲突检测、原始格式保持
    性能特征: O(1)查找、内存优化、集合运算

    遵循CIIA规范：
    - CIIA-1: 显示索引分离 - _item_map保存原始项映射
    - CIIA-3: 冲突检测 - 添加时检测大小写冲突
    - CIIA-8: 容器支持 - 实现MutableSet协议

    Examples:
        >>> # 基本使用
        >>> s = CaseInsensitiveSet()
        >>> s.add("OBMIQ_logs")
        >>> s.add("K_Label")
        >>>
        >>> # 大小写不敏感查找
        >>> assert "obmiq_logs" in s
        >>> assert "k_label" in s
        >>>
        >>> # 冲突检测
        >>> s.add("OBMIQ_LOGS")  # 抛出WpValidationError

    Type Annotation Best Practices:
        >>> from dataclasses import dataclass, field
        >>>
        >>> @dataclass
        >>> class MyModel:
        ...     # ✅ 正确：直接使用类名
        ...     tags: CaseInsensitiveSet = field(default_factory=CaseInsensitiveSet)
        ...
        ...     # ❌ 错误：不要添加类型参数
        ...     # tags: CaseInsensitiveSet[str] = field(default_factory=CaseInsensitiveSet)
    """

    def __init__(self, iterable=None):
        """初始化大小写不敏感集合。

        Args:
            iterable: 可迭代对象，用于初始化集合
        """
        self._data: set[str] = set()
        self._item_map: dict[str, str] = {}  # 规范化项 -> 原始项映射
        self._mode = WpNormalizationMode.UNICODE_CASEFOLD

        if iterable:
            self.update(iterable)

    def add(self, item: str) -> None:
        """添加项，检测冲突（CIIA-3规范）。

        Args:
            item: 要添加的项

        Raises:
            WpValidationError: 检测到大小写冲突时抛出
        """
        if not isinstance(item, str):
            raise TypeError(f"集合项必须是字符串，得到: {type(item).__name__}")

        normalized_item = WpStringNormalizer.normalize(item, self._mode)

        if normalized_item in self._item_map:
            existing_item = self._item_map[normalized_item]
            if existing_item != item:
                raise WpValidationError(
                    f"集合中已存在大小写冲突项：'{existing_item}' 与 '{item}'",
                    context=ErrorContext(
                        operation="add_case_insensitive_item",
                        additional_info={
                            "conflicting_items": [existing_item, item],
                            "normalized_item": normalized_item
                        }
                    )
                )

        self._item_map[normalized_item] = item
        self._data.add(item)

    def discard(self, item: str) -> None:
        """大小写不敏感移除项。"""
        normalized_item = WpStringNormalizer.normalize(item, self._mode)
        if normalized_item in self._item_map:
            original_item = self._item_map[normalized_item]
            self._data.discard(original_item)
            del self._item_map[normalized_item]

    def __contains__(self, item: object) -> bool:
        """大小写不敏感包含检查。"""
        if not isinstance(item, str):
            return False
        normalized_item = WpStringNormalizer.normalize(item, self._mode)
        return normalized_item in self._item_map

    def __iter__(self) -> Iterator[str]:
        """迭代原始项。"""
        return iter(self._data)

    def __len__(self) -> int:
        """返回集合大小。"""
        return len(self._data)

    def __repr__(self) -> str:
        """集合的字符串表示。"""
        return f"{self.__class__.__name__}({set(self._data)})"
