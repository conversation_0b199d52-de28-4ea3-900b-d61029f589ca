# SCAPE_API_logwp_models - logwp.models包API使用文档

## 文档信息

**文档标题**: SCAPE_API_logwp_models - logwp.models包API使用文档
**版本**: 1.0
**日期**: 2025-01-10
**维护者**: SCAPE Core Team

## 概述

logwp.models包是SCAPE项目的核心数据模型层，提供格式无关的测井数据对象模型。本文档详细介绍了所有公共API的使用方法、参数说明和示例代码。

### 核心特性
- **格式无关设计**：与具体文件格式解耦，支持WP、LAS、JSON等多种格式
- **类型安全**：完整的类型注解和TypedDict支持
- **现代化架构**：遵循DDD设计原则，支持GPU加速和异步处理
- **WFS v1.0规范**：完整支持COMP类型、T2_AXIS、二维组合曲线等

### 包结构
```
logwp.models/
├── base.py              # 基础组件抽象类
├── well_project.py      # 井工程项目聚合根
├── head.py              # 项目头部信息管理
├── mapping.py           # 井名映射管理
├── datasets/            # 数据集模型
├── curve/               # 曲线元数据管理
├── ext_attr/            # 扩展属性管理
├── types/               # 类型定义
└── constants.py         # 常量定义
```

## 主要章节

1. [快速开始](#快速开始) - 基本导入和项目创建
2. [核心API参考](#核心api参考) - 详细API文档
   - [WpWellProject](#1-wpwellproject---井工程项目聚合根) - 项目管理
   - [WpHead](#2-wphead---项目头部信息管理) - 头部信息
   - [WpWellMap](#3-wpwellmap---井名映射管理) - 井名映射
   - [数据集模型](#4-数据集模型) - 各种数据集类型
   - [曲线元数据](#5-曲线元数据管理) - 曲线管理
   - [扩展属性](#6-扩展属性管理) - 属性扩展
3. [常量和异常使用规范](#常量和异常使用规范) - **架构必读**
4. [logwp.testing 测试工具包](#logwptesting-测试工具包使用指南) - **测试必读**
5. [参考文档](#参考文档) - 相关文档链接

## 快速开始

### 基本导入
```python
from logwp.models import WpWellProject, WpHead, WpWellMap
from logwp.models.datasets import WpContinuousDataset, WpDiscreteDataset, WpIntervalDataset
from logwp.models.datasets.bundle import WpDatasetBundle, WpDataFrameBundle, WpArrayBundle
from logwp.models.curve import CurveMetadata, CurveBasicAttributes
from logwp.models.ext_attr import ExtAttributeManager
```

### 创建井工程项目
```python
# 创建井工程项目
project = WpWellProject(name="Santos_Study")

# 创建数据集
import pandas as pd
logs_df = pd.DataFrame({
    'WELL': ['W1'] * 100,
    'MD': range(1000, 1100),
    'GR': [50 + i*0.1 for i in range(100)],
    'PHIT': [0.15 + i*0.001 for i in range(100)]
})

dataset = WpContinuousDataset(name="OBMIQ_logs", df=logs_df)
project.add_dataset(dataset)

# 直接使用数据集进行分析
ml_data = dataset.df  # 获取DataFrame用于机器学习
```

## 核心API参考

### 1. WpWellProject - 井工程项目聚合根

#### 1.1 基本操作
```python
class WpWellProject:
    """井工程项目聚合根，管理数据集和元信息。"""

    def __init__(self, name: str, head: WpHead = None, well_map: WpWellMap = None) -> None:
        """初始化井工程项目。

        Args:
            name: 项目名称
            head: 项目头部信息（可选）
            well_map: 井名映射（可选）
        """

    def add_dataset(self, name: str, dataset: Any) -> None:
        """添加数据集到项目。

        此方法支持在项目构建过程中渐进式地添加数据集。

        Args:
            name: 数据集名称
            dataset: 数据集对象

        Raises:
            WpConsistencyError: 数据集名称冲突或参数无效
        """

    def has_dataset(self, name: str) -> bool:
        """检查指定名称的数据集是否存在（大小写不敏感）。

        Args:
            name: 数据集名称

        Returns:
            bool: 数据集存在返回True，否则返回False
        """

    def add_dataframe_bundle(
        self,
        dataset_name: str,
        bundle: WpDataFrameBundle,
        *,
        copy_data: bool = False
    ) -> WpDepthIndexedDatasetBase:
        """
        将一个WpDataFrameBundle作为新数据集添加到项目中。

        此方法会自动检测Bundle的数据类型（连续、离散或区间），
        并创建相应的数据集对象。

        Args:
            dataset_name: 要创建的新数据集的名称。
            bundle: 包含数据和元数据的Bundle对象。
            copy_data: 是否深度拷贝Bundle中的数据。默认为False。

        Returns:
            WpDepthIndexedDatasetBase: 已添加到项目中的新数据集对象。

        Raises:
            WpDataError: 如果同名数据集已存在，或Bundle数据无效。
        """

    def get_dataset(self, name: str) -> WpDepthIndexedDatasetBase | None:
        """获取数据集。

        Args:
            name: 数据集名称

        Returns:
            数据集对象，不存在时返回None
        """

    def list_datasets(self) -> list[str]:
        """列出所有数据集名称。

        Returns:
            数据集名称列表
        """

    def remove_dataset(self, name: str) -> bool:
        """移除数据集。

        Args:
            name: 数据集名称

        Returns:
            是否成功移除
        """
    @classmethod
    def create_with_datasets(
        cls,
        name: str,
        datasets: dict[str, Any],
        *,
        head_attributes: dict[str, Any] | None = None,
        well_mappings: dict[str, str] | None = None,
        default_depth_unit: str = WpDepthUnit.METER
    ) -> 'WpWellProject':
        """创建项目并批量添加数据集的便捷方法。

        Args:
            name: 项目名称
            datasets: 数据集字典，键为数据集名称，值为数据集对象
            head_attributes: 项目头部属性字典（可选）
            well_mappings: 井名映射字典（可选）
            default_depth_unit: 默认深度单位

        Returns:
            WpWellProject: 创建的项目实例
        """
```

#### 1.1.1 批量创建使用示例
```python
# 批量创建项目和数据集
datasets = {
    "logs": continuous_dataset,
    "core": discrete_dataset,
    "zones": interval_dataset
}
project = WpWellProject.create_with_datasets(
    name="Santos_Study",
    datasets=datasets,
    head_attributes={"version": "1.0", "field": "Santos"},
    well_mappings={"C-1A": "C-1", "C-1B": "C-1"}
)
assert len(project.datasets) == 3
assert project.head.get_attribute("version") == "1.0"
```

#### 1.2 数据概况生成
```python
def generate_summary(self, format: str = "markdown") -> str:
    """生成项目数据概况的详细报告。

    此方法会遍历项目中的所有数据集，生成一个全面的概况，包括：
    - **项目级信息**: 项目名称、数据集数量、总井数等。
    - **数据集级信息**: 每个数据集的类型、数据点数、深度范围等。
    - **曲线统计**: 对每个数据集中的曲线进行详细统计分析：
        - **数值型**: 数量、缺失值、均值、中位数、标准差、分位数、异常值等。
        - **类别型**: 唯一值数量、众数、值分布等。
        - **标识型**: 唯一值数量、完整度等。

    Args:
        format: 输出格式，支持"markdown"、"json"

    Returns:
        格式化的概况报告

    Examples:
        >>> project = WpWellProject(name="Santos_Study")
        >>> summary = project.generate_summary()
        >>> print(summary)
        # 井工程项目概况
        ## 基本信息
        - 项目名称: Santos_Study
        - 数据集数量: 3
        - 井数量: 15
        ...
    """
```

### 2. 数据集API - 三层数据集架构

logwp包提供三种不同层次的数据集概念：

#### 2.1 完整数据集层 (WpDepthIndexedDatasetBase)

**抽象基类**：
```python
class WpDepthIndexedDatasetBase:
    """深度索引数据集抽象基类，用于项目级数据管理。"""

    # 轻量级数据包提取API
    def extract_curve_dataframe_bundle(
        self,
        curve_names: list[str],
        *,
        include_system_columns: bool = False,
        validate_existence: bool = True,
        query_condition: Optional[str] = None
    ) -> WpDataFrameBundle:
        """提取曲线DataFrame数据包。

        Args:
            curve_names: 要提取的曲线名称列表
            include_system_columns: 是否包含系统列（井名、深度）
            validate_existence: 是否验证曲线存在性
            query_condition: 查询条件（DataFrame.query语法）

        Returns:
            WpDataFrameBundle: DataFrame数据包，包含便捷元数据访问
        """

    def extract_curve_array_bundle(
        self,
        curve_names: list[str],
        *,
        include_system_columns: bool = False,
        validate_existence: bool = True,
        query_condition: Optional[str] = None
    ) -> WpArrayBundle:
        """提取曲线NumPy数组数据包。

        Args:
            curve_names: 要提取的曲线名称列表
            include_system_columns: 是否包含系统列（井名、深度）
            validate_existence: 是否验证曲线存在性
            query_condition: 查询条件（DataFrame.query语法）

        Returns:
            WpArrayBundle: NumPy数组数据包，包含ML辅助方法
        """

    # 新增API（为PCA包优化）
    def get_system_curves(self) -> list[str]:
        """获取系统曲线名称列表（井名+深度）。"""

    def get_analysis_suitable_curves(self) -> list[str]:
        """获取适合数据分析的曲线名称列表。

        自动过滤：
        - 排除系统曲线（井名、深度等标识曲线）
        - 排除类别型曲线（CATEGORICAL类型）
        - 只保留数值型曲线（FLOAT、INT类型）
        - 只保留数据曲线（LOGGING、COMPUTED、CUSTOM类别）
        """

    def validate_numeric_data(self, curve_names: list[str] | None = None) -> dict[str, Any]:
        """验证数值型数据的质量。

        此方法对指定的数值型曲线进行全面的质量检查，包括：
        - **数据类型检查**: 确认DataFrame中的列是否为数值类型。
        - **缺失值统计**: 统计每条曲线的NaN数量。
        - **无穷值检测**: 检查是否存在`np.inf`或`-np.inf`。

        Args:
            curve_names: 要验证的曲线名称列表，None表示验证所有数值型曲线

        Returns:
            dict: 包含详细验证结果的字典，结构如下：
            {
                "total_curves": int,      # 验证的曲线总数
                "total_rows": int,        # 数据总行数
                "nan_counts": {str: int}, # 每条曲线的NaN数量
                "inf_counts": {str: int}, # 每条曲线的无穷值数量
                "data_types": {str: str}, # 每条曲线的物理数据类型
                "issues": list[str],      # 发现的问题描述列表
                "validation_passed": bool # 是否通过所有验证
            }
        """

    def get_data_completeness(self) -> dict[str, float]:
        """获取数据完整性统计。

        此方法为数据集中的每一条曲线（包括系统曲线）计算其数据完整性。
        完整性定义为非缺失值（non-NaN）占总数据点的比例。

        Returns:
            dict[str, float]: 一个字典，键为曲线的逻辑名称，值为该曲线的
                              数据完整性比例（范围从0.0到1.0）。

        Examples:
            >>> completeness = dataset.get_data_completeness()
            >>> print(completeness)
            {
                'WELL': 1.0,
                'MD': 1.0,
                'GR': 0.95,  # 假设GR有5%的缺失值
                'PHIT': 0.88
            }
        """

    def check_uniform_depth_sampling(
        self,
        algorithm: str = "mad",
        tolerance: float = 1e-5
    ) -> tuple[bool, float | None]:
        """检查深度采样是否为等间隔。

        此方法对于许多信号处理和插值算法至关重要，因为它们通常假设
        数据是等间隔采样的。

        Args:
            algorithm: 检查算法。
                - 'mad' (默认): 使用中位数绝对偏差，对异常采样点不敏感，更稳健。
                - 'std_dev': 使用标准差，对所有采样点敏感。
            tolerance: 判断为等间隔的容差。如果采样间隔的变化小于此值，
                       则认为采样是等间隔的。

        Returns:
            一个元组 `(is_uniform, sampling_interval)`:
            - `is_uniform` (bool): 如果采样是等间隔的，则为True。
            - `sampling_interval` (float | None): 如果是等间隔的，则返回估算的
              采样间隔；否则返回None。

        Examples:
            >>> is_uniform, interval = dataset.check_uniform_depth_sampling()
            >>> if is_uniform:
            ...     print(f"数据是等间隔采样的，间隔为: {interval} m")
            ... else:
            ...     print("数据不是等间隔采样的，可能需要重采样。")
        """

    # 数据集类型判断
    def is_continuous_dataset(self) -> bool:
        """判断是否为连续型数据集。"""

    def is_discrete_dataset(self) -> bool:
        """判断是否为离散型数据集。"""

    def is_interval_dataset(self) -> bool:
        """判断是否为区间型数据集。"""

    def get_dataset_type_name(self) -> str:
        """获取数据集类型名称字符串。"""
```

#### 2.2 轻量级数据包层 (WpDatasetBundle)

**抽象基类**：
```python
@dataclass
class WpDatasetBundle(ABC):
    """轻量级数据传输对象（DTO），专注于数据封装。"""

    # 基础属性
    name: str                                    # 数据集名称
    curve_metadata: CurveMetadata               # 完整的曲线元数据

    # 便捷属性（自动计算）
    well_curve_map: CaseInsensitiveDict         # 井名曲线映射
    is_interval_bundle: bool                    # 是否为区间数据集
    depth_curve_map: Optional[CaseInsensitiveDict]      # 单深度映射
    depth_top_curve_map: Optional[CaseInsensitiveDict]  # 顶深映射
    depth_bottom_curve_map: Optional[CaseInsensitiveDict] # 底深映射

    @property
    @abstractmethod
    def data(self) -> Any:
        """数据本身（子类定义具体类型）。"""

    # 便捷访问方法
    def get_well_names(self) -> Optional[Any]:
        """获取井名数据。"""

    def get_depths(self) -> Optional[Any]:
        """获取深度数据（单深度情况）。"""

    def get_top_depths(self) -> Optional[Any]:
        """获取顶深数据（区间情况）。"""

    def get_bottom_depths(self) -> Optional[Any]:
        """获取底深数据（区间情况）。"""
```

**DataFrame数据包**：
```python
@dataclass
class WpDataFrameBundle(WpDatasetBundle):
    """DataFrame数据包，适合数据预处理场景。"""

    data: pd.DataFrame                          # DataFrame数据
    curve_to_columns_map: Dict[str, list[str]]  # 曲线到列名映射

    # 便捷访问方法返回pd.Series
    def get_well_names(self) -> Optional[pd.Series]:
        """获取井名Series。"""

    def get_depths(self) -> Optional[pd.Series]:
        """获取深度Series（单深度情况）。"""

    # 新增API - 数据提取与转换
    def to_all_wells_data(self) -> Dict[str, pd.DataFrame]:
        """将包含多井数据的DataFrame拆分为按井名组织的字典。

        此方法是进行逐井分析的常用预处理步骤。它会根据井名标识曲线
        对数据进行分组，为每一口井创建一个独立的DataFrame。

        Returns:
            Dict[str, pd.DataFrame]: 一个字典，其中键是井名，值是对应井的
                                     完整DataFrame（不包含其他井的数据）。

        Raises:
            WpDataError: 如果缺少井名标识曲线。

        Examples:
            >>> # 假设 bundle.data 包含 W1 和 W2 两口井的数据
            >>> well_data_map = bundle.to_all_wells_data()
            >>> print(well_data_map.keys())
            dict_keys(['W1', 'W2'])
            >>> w1_df = well_data_map['W1']
            >>> # w1_df 是一个只包含W1井数据的DataFrame
        """

    def get_interval_curve_values(
        self, well_name: str, curve_names: list[str]
    ) -> list[list[Any]]:
        """获取指定井的区间型曲线值。

        此方法仅适用于`interval`类型的Bundle。

        Args:
            well_name: 井名。
            curve_names: 要提取的曲线名称列表。

        Returns:
            list[list[Any]]: 每个子列表代表一个层段，
            格式为 `[顶深, 底深, 曲线1值, 曲线2值, ...]`。

        Raises:
            WpValidationError: 如果Bundle不是`interval`类型。
            WpDataError: 如果缺少井名/深度曲线或井不存在。

        Examples:
            >>> # 假设bundle是包含地层数据的区间型Bundle
            >>> zones = bundle.get_interval_curve_values(
            ...     well_name="W1",
            ...     curve_names=["FORMATION", "NET_PAY"]
            ... )
            >>> for zone in zones:
            ...     # zone: [顶深, 底深, 地层名, 净储层厚度]
            ...     print(f"层段 {zone[0]}-{zone[1]}: {zone[2]}, 净厚度 {zone[3]}")
        """

    def get_curve_values_at_depths(
        self,
        well_name: str,
        depths: list[float],
        curve_name: str,
        interpolation_method: str = 'nearest'
    ) -> list[Any]:
        """根据离散深度点列表，从连续曲线上查询（或插值）对应的值。

        常用于岩心渗透率盲井检验等场景。此方法仅适用于非`interval`类型的Bundle。

        Args:
            well_name: 要查询的井名。
            depths: 目标深度列表。
            curve_name: 要查询的曲线名称。
            interpolation_method: 插值方法, 支持 'nearest' (默认) 和 'linear'。

        Returns:
            list[Any]: 与输入depths列表长度相同、顺序一致的结果列表。

        Raises:
            WpValidationError: 如果Bundle是`interval`类型或插值方法不受支持。
            WpDataError: 如果缺少井名/深度曲线，或指定的井不存在。
            ImportError: 如果需要'scipy'库但未安装。

        Examples:
            >>> # 假设bundle是包含连续测井数据的Bundle
            >>> core_depths = [2501.5, 2503.8, 2505.2]
            >>> log_phit_at_core_depths = bundle.get_curve_values_at_depths(
            ...     well_name="W1",
            ...     depths=core_depths,
            ...     curve_name="PHIT",
            ...     interpolation_method="linear"
            ... )
            >>> for depth, value in zip(core_depths, log_phit_at_core_depths):
            ...     print(f"深度 {depth} m 处的测井孔隙度为: {value:.4f}")
        """

    def get_interval_curve_values_for_wells(
        self, curve_names: list[str]
    ) -> 'CaseInsensitiveDict[str, list[list[Any]]]':
        """获取所有井的区间型曲线值。

        此方法是 `get_interval_curve_values` 的多井版本。

        Args:
            curve_names: 要提取值的曲线名称列表。

        Returns:
            CaseInsensitiveDict[str, list[list[Any]]]: 键为井名，值为对应井的区间值列表。

        Examples:
            >>> # 假设bundle是包含多口井地层数据的区间型Bundle
            >>> all_zones = bundle.get_interval_curve_values_for_wells(
            ...     curve_names=["FORMATION", "NET_PAY"]
            ... )
            >>>
            >>> for well, zones in all_zones.items():
            ...     print(f"--- 井: {well} ---")
            ...     for zone in zones:
            ...         # zone: [顶深, 底深, 地层名, 净储层厚度]
            ...         print(f"  层段 {zone[0]}-{zone[1]}: {zone[2]}, 净厚度 {zone[3]}")
            --- 井: W1 ---
              层段 2500-2505: FM_A, 净厚度 4.5
              层段 2505-2510: FM_B, 净厚度 6.2
        """

    def get_curve_values_with_zone(
        self, well_name: str, top_depth: float, bottom_depth: float, curve_name: str
    ) -> list[Any]:
        """为指定井提取某条曲线在给定深度层段内的所有数据点。

        此方法专门用于处理非`interval`类型的Bundle，是连接PLT分层信息和
        连续预测曲线的关键桥梁。

        Args:
            well_name: 要查询的井名。
            top_depth: 层段的顶界深度。
            bottom_depth: 层段的底界深度。
            curve_name: 要提取值的曲线名称。

        Returns:
            list[Any]: 包含该层段内所有曲线值的列表。

        Raises:
            WpValidationError: 如果此Bundle是`interval`类型。
            WpDataError: 如果缺少井名/深度曲线，或指定的井/曲线不存在。

        Examples:
            >>> # 假设bundle是包含连续测井数据的Bundle
            >>> # 并且我们从一个区间数据集中得知W1井有一个产层在 2505m - 2510m
            >>> top, bottom = 2505.0, 2510.0
            >>> perm_values_in_zone = bundle.get_curve_values_with_zone(
            ...     well_name="W1",
            ...     top_depth=top,
            ...     bottom_depth=bottom,
            ...     curve_name="PERM"
            ... )
            >>> print(f"层段 {top}-{bottom} 内的渗透率数据点: {len(perm_values_in_zone)}")
            >>> # 可以进一步计算该层段的平均渗透率
            >>> import numpy as np
            >>> avg_perm = np.mean(perm_values_in_zone)
            >>> print(f"平均渗透率: {avg_perm:.2f} mD")
        """

    def get_curve_statistic_with_zone(
        self,
        well_name: str,
        top_depth: float,
        bottom_depth: float,
        curve_name: str,
        statistic: str,
    ) -> float:
        """为指定井计算某条曲线在给定深度层段内的统计值。

        此方法是对 `get_curve_values_with_zone` 的进一步封装，直接计算常用统计量。

        Args:
            well_name: 要查询的井名。
            top_depth: 层段的顶界深度。
            bottom_depth: 层段的底界深度。
            curve_name: 要计算的曲线名称。
            statistic: 要计算的统计量, 如 'mean', 'median', 'std', 'min', 'max'等。

        Returns:
            float: 计算出的统计值。

        Raises:
            WpValidationError: 如果Bundle是`interval`类型或参数无效。
            WpDataError: 如果层段内无有效数据。
            ValueError: 如果指定的统计量不受支持。

        Examples:
            >>> # 假设bundle是包含连续测井数据的Bundle
            >>> # 并且我们从一个区间数据集中得知W1井有一个产层在 2505m - 2510m
            >>> top, bottom = 2505.0, 2510.0
            >>> avg_perm_in_zone = bundle.get_curve_statistic_with_zone(
            ...     well_name="W1",
            ...     top_depth=top,
            ...     bottom_depth=bottom,
            ...     curve_name="PERM",
            ...     statistic="mean"
            ... )
            >>> print(f"层段 {top}-{bottom} 内的平均渗透率: {avg_perm_in_zone:.2f} mD")
        """

    def add_1d_curve(
        self,
        curve_name: str,
        curve_data: np.ndarray,
        *,
        unit: str = "",
        description: str = "",
        category: WpCurveCategory = WpCurveCategory.COMPUTED,
        overwrite: bool = False,
    ) -> None:
        """
        动态地将一条新的一维曲线添加到Bundle中。

        此方法会同步更新DataFrame数据、曲线元数据和列映射，确保Bundle的
        内部一致性。

        Args:
            curve_name: 新曲线的逻辑名称。
            curve_data: 包含曲线数据的NumPy数组。
            unit: 曲线单位。
            description: 曲线描述。
            category: 曲线类别，默认为 COMPUTED。
            overwrite: 如果曲线已存在，是否覆盖。默认为False。

        Raises:
            WpDataError: 如果数据长度不匹配，或曲线已存在且overwrite=False。
        """

    def add_2d_composite_curve(
        self,
        curve_name: str,
        curve_data: np.ndarray,
        *,
        unit: str = "",
        description: str = "",
        category: WpCurveCategory = WpCurveCategory.COMPUTED,
        overwrite: bool = False,
    ) -> None:
        """
        动态地将一条新的二维组合曲线添加到Bundle中。

        此方法会同步更新DataFrame数据、曲线元数据和列映射。
        例如，添加一条名为 "T2_COMP" 的64元曲线，会向DataFrame中添加
        "T2_COMP_1", "T2_COMP_2", ..., "T2_COMP_64" 等列。

        Args:
            curve_name: 新曲线的逻辑名称。
            curve_data: 包含曲线数据的二维NumPy数组，形状为 (n_samples, n_elements)。
            unit: 曲线单位。
            description: 曲线描述。
            category: 曲线类别，默认为 COMPUTED。
            overwrite: 如果曲线已存在，是否覆盖。默认为False。

        Raises:
            WpDataError: 如果数据形状不匹配，或曲线已存在且overwrite=False。
        """

    def remove_curve(self, curve_name: str, *, ignore_missing: bool = False) -> None:
        """
        从Bundle中移除一条曲线（一维或二维）。

        此方法会同步更新DataFrame数据、曲线元数据和列映射，
        确保Bundle的内部一致性。

        Args:
            curve_name: 要移除的曲线的逻辑名称。
            ignore_missing: 如果为True，当曲线不存在时不会抛出异常。
                            默认为False。

        Raises:
            WpCurveMetadataError: 如果曲线不存在且ignore_missing=False。
        """

    def validate_required_curves(
        self, required_curves: list[str] | dict[str, 'WpDataType']
    ) -> bool:
        """
        全面验证Bundle中是否存在所有必需的曲线，并同时检查元数据和物理数据类型。

        此方法支持两种模式：
        1. 列表模式: `required_curves` 是一个字符串列表，验证所有这些曲线都存在，
           且其元数据类型和物理数据类型均为数值型。
        2. 字典模式: `required_curves` 是一个字典 `{'curve_name': WpDataType}`，
           验证每个曲线都存在且其元数据和物理数据类型都与指定类型匹配。

        Args:
            required_curves: 必需的曲线列表或曲线到期望类型的字典。

        Returns:
            bool: 如果所有必需曲线都存在且满足条件，则返回True。

        Raises:
            WpValidationError: 如果有任何曲线缺失或不满足类型要求。
        """

```

**NumPy数组数据包**：
```python
@dataclass
class WpArrayBundle(WpDatasetBundle):
    """NumPy数组数据包，适合算法计算和机器学习场景。"""

    data: CaseInsensitiveDict                   # 曲线名到numpy数组的映射

    # 便捷访问方法返回np.ndarray
    def get_well_names(self) -> Optional[np.ndarray]:
        """获取井名数组。"""

    def get_depths(self) -> Optional[np.ndarray]:
        """获取深度数组（单深度情况）。"""

    # 机器学习辅助方法
    def to_sklearn_format(
        self,
        target_curve: Optional[str] = None,
        exclude_system_curves: bool = True
    ) -> tuple[np.ndarray, Optional[np.ndarray]]:
        """转换为scikit-learn格式 (X, y)。

        Args:
            target_curve: 目标曲线名称（y），如果为None则只返回特征矩阵
            exclude_system_curves: 是否排除系统曲线（井名、深度），默认True

        Returns:
            tuple[np.ndarray, Optional[np.ndarray]]: (特征矩阵X, 目标向量y)
            - X: 形状为(n_samples, n_features)的特征矩阵
            - y: 形状为(n_samples,)的目标向量，如果target_curve为None则为None
        """

        Examples:
            >>> # 假设 bundle 包含 GR, PHIT, PERM 三条曲线
            >>> # 1. 提取特征和目标
            >>> X, y = bundle.to_sklearn_format(target_curve="PERM")
            >>> print(f"特征矩阵 X 的形状: {X.shape}") # (n_samples, 2)
            >>> print(f"目标向量 y 的形状: {y.shape}") # (n_samples,)
            >>>
            >>> # 2. 只提取特征矩阵 (无监督学习场景)
            >>> X_only, y_none = bundle.to_sklearn_format()
            >>> print(f"特征矩阵 X_only 的形状: {X_only.shape}") # (n_samples, 3)
            >>> print(f"目标向量: {y_none}") # None


    def get_feature_curves(self, exclude_system_curves: bool = True) -> list[str]:
        """获取特征曲线名称列表。

        此方法返回的列表与 `to_sklearn_format` 方法生成的特征矩阵 `X` 的
        列顺序一致，可用于解释特征的重要性。

        Args:
            exclude_system_curves: 是否从列表中排除系统曲线（如井名、深度），
                                   默认为True。

        Returns:
            list[str]: 特征曲线的逻辑名称列表。

        Examples:
            >>> # 假设 bundle 包含 GR, PHIT, PERM 三条曲线
            >>> feature_names = ml_bundle.get_feature_curves(exclude_system_curves=True)
            >>> print(feature_names)
            ['GR', 'PHIT'] # 假设目标是PERM
            >>> X, y = ml_bundle.to_sklearn_format(target_curve="PERM")
            >>> # X 的第一列对应 GR, 第二列对应 PHIT
        """

    def get_data_summary(self) -> Dict[str, Dict[str, Any]]:
        """获取数据统计摘要。

        为Bundle中的每一条数值型曲线计算一组描述性统计量。

        Returns:
            Dict[str, Dict[str, Any]]: 一个字典，键为曲线名，值为包含
                                       以下统计量的字典：
                                       - 'count': 非空值数量
                                       - 'mean': 均值
                                       - 'std': 标准差
                                       - 'min': 最小值
                                       - '25%': 第一四分位数
                                       - '50%': 中位数
                                       - '75%': 第三四分位数
                                       - 'max': 最大值

        Examples:
            >>> summary = ml_bundle.get_data_summary()
            >>> print(summary['GR']['mean'])
            55.4
        """
```

#### 2.3 连续型数据集
```python
class WpContinuousDataset(WpDepthIndexedDatasetBase):
    """连续型数据集，支持测井曲线数据。

    适用场景：
    - 常规测井数据（GR、PHIT、PERM等）
    - 成像测井数据
    - NMR测井数据
    """

    def __init__(self, name: str, df: pd.DataFrame, curve_metadata: CurveMetadata = None):
        """初始化连续型数据集。

        Args:
            name: 数据集名称
            df: 数据DataFrame
            curve_metadata: 曲线元数据（可选，会自动推断）
        """

    def resample_depth(
        self,
        new_sampling_interval: float,
        *,
        depth_range: tuple[float, float] | None = None,
        interpolation_method: str = 'linear',
        out_of_range_fill_value: any = None,
        new_dataset_name: str | None = None
    ) -> 'WpContinuousDataset':
        """对连续型数据集进行深度重采样。

        此方法创建一个新的数据集，其深度是等间隔的。
        支持密集化和粗化采样，并提供多种插值方法。

        Args:
            new_sampling_interval: 新的深度采样间隔。
            depth_range: (可选) 新的深度范围 (顶深, 底深)。
                         如果为None，则使用原始数据的范围。
            interpolation_method: 数值型曲线的插值方法，如 'linear', 'nearest', 'cubic'。
                                类别型曲线将强制使用 'nearest'。
            out_of_range_fill_value: (可选) 当扩展深度范围时，用于填充超出原始范围的值。
            new_dataset_name: (可选) 新数据集的名称。如果为None，则自动生成。

        Returns:
            WpContinuousDataset: 一个新的、经过重采样的数据集实例。
        """
```

#### 2.4 离散型数据集
```python
class WpDiscreteDataset(WpDepthIndexedDatasetBase):
    """离散型数据集，支持不等间距采样的点数据。

    适用场景：
    - 岩心分析数据
    - 地层测试数据
    - 特殊事件标记
    """
```

#### 2.5 区间型数据集
```python
class WpIntervalDataset(WpDepthIndexedDatasetBase):
    """区间型数据集，支持层状数据的双深度索引。

    适用场景：
    - 测井解释结论
    - 地层分层数据
    - 储层参数统计
    """
```

#### 2.6 Bundle使用示例

**基本数据提取**：
```python
# 从完整数据集提取轻量级数据包
project = read_wp_excel("data/santos_field.wp.xlsx")
dataset = project.get_dataset("OBMIQ_logs")

# 提取DataFrame数据包（适合数据预处理）
df_bundle = dataset.extract_curve_dataframe_bundle([
    "PHIT", "GR", "T2_VALUE"
])

# 访问DataFrame数据
df = df_bundle.data
phit_series = df["PHIT"]

# 便捷属性访问
well_names = df_bundle.get_well_names()  # pd.Series
depths = df_bundle.get_depths()          # pd.Series

# 提取NumPy数组数据包（适合算法计算）
array_bundle = dataset.extract_curve_array_bundle([
    "PHIT", "GR", "T2_VALUE"
])

# 访问数组数据（大小写不敏感）
arrays = array_bundle.data
phit_values = arrays["PHIT"]    # np.ndarray
gr_values = arrays["gr"]        # 大小写不敏感

# 便捷属性访问
well_names = array_bundle.get_well_names()  # np.ndarray
depths = array_bundle.get_depths()          # np.ndarray
```

**机器学习场景**：
```python
# 提取特征和目标变量
ml_bundle = dataset.extract_curve_array_bundle([
    "PHIT", "GR", "RHOB", "PERM"
])

# 转换为scikit-learn格式
X, y = ml_bundle.to_sklearn_format(target_curve="PERM")

# 获取特征曲线列表（自动排除系统曲线）
feature_curves = ml_bundle.get_feature_curves()
print(f"特征曲线: {feature_curves}")  # ["PHIT", "GR", "RHOB"]

# 获取数据统计摘要
summary = ml_bundle.get_data_summary()
print(f"PHIT统计: {summary['PHIT']}")

# 训练模型
from sklearn.ensemble import RandomForestRegressor
model = RandomForestRegressor()
model.fit(X, y)
```

**区间数据集处理**：
```python
# 区间数据集的Bundle会自动处理双深度
interval_dataset = project.get_dataset("zones")
bundle = interval_dataset.extract_curve_array_bundle([
    "FORMATION", "NET_PAY"
])

# 自动判断数据集类型
if bundle.is_interval_bundle:
    top_depths = bundle.get_top_depths()     # np.ndarray
    bottom_depths = bundle.get_bottom_depths() # np.ndarray
    print(f"区间数据: {len(top_depths)} 个层段")
else:
    depths = bundle.get_depths()             # np.ndarray
    print(f"点数据: {len(depths)} 个深度点")
```

**查询条件过滤**：
```python
# 使用查询条件过滤数据
quality_bundle = dataset.extract_curve_array_bundle([
    "PHIT", "GR", "PERM"
], query_condition="GR > 50 and PHIT > 0.1")

# 只包含高GR和高孔隙度的数据
filtered_data = quality_bundle.data
print(f"过滤后数据量: {len(filtered_data['PHIT'])}")
```

### 3. 曲线元数据API - CurveMetadata

#### 3.1 基本操作
```python
class CurveMetadata:
    """曲线元数据管理器。"""

    def add_curve(self, curve: CurveBasicAttributes) -> None:
        """添加曲线基本属性。"""

    def get_curve(self, name: str) -> CurveBasicAttributes | None:
        """获取曲线基本属性。"""

    def has_curve(self, name: str) -> bool:
        """检查曲线是否存在。"""

    def list_curves(self, is_numeric: bool = None) -> list[str]:
        """列出曲线名称。

        Args:
            is_numeric: 是否只返回数值型曲线
        """
```

#### 3.2 新增便捷API
```python
# 曲线过滤和分类
def get_system_curves(self) -> list[str]:
    """获取所有系统曲线的名称列表（井名+深度）。"""

def get_data_curves(self) -> list[str]:
    """获取所有数据曲线的名称列表（即非系统曲线）。"""

def get_numeric_curves(self) -> list[str]:
    """获取所有数值型（FLOAT, INT）曲线的名称列表。"""

def get_analysis_suitable_curves(self) -> list[str]:
    """获取适合数据分析的曲线名称列表。

    此方法是进行机器学习、统计分析前数据准备的核心API。
    它会自动过滤掉不适合分析的曲线，包括：
    - 系统曲线 (井名, 深度等)
    - 类别型曲线 (如岩性)
    - 非数值型曲线
    """

# DataFrame列名映射
def get_dataframe_columns_for_curves(self, curve_names: list[str]) -> list[str]:
    """获取曲线对应的DataFrame列名列表。

    支持一维曲线和二维组合曲线的批量映射。对于二维曲线，会返回其所有
    元素列的名称。
    """

def get_curves_for_dataframe_columns(self, column_names: list[str]) -> list[str]:
    """获取DataFrame列名对应的曲线名称列表。

    支持一维曲线和二维组合曲线元素的批量反向映射。
    """

# 曲线名称展开
def expand_curve_names(
    self,
    curve_names: list[str],
    mode: 'CurveExpansionMode' = CurveExpansionMode.EXPANDED
) -> list[str]:
    """展开曲线名称列表，将二维组合曲线展开为其元素。

    Args:
        curve_names: 待展开的曲线名称列表
        mode: 展开模式，控制元素命名风格

    Returns:
        展开后的曲线/列名列表

    Examples:
        >>> # CurveExpansionMode.EXPANDED -> 'T2_VALUE[1]'
        >>> # CurveExpansionMode.DATAFRAME -> 'T2_VALUE_1'
    """

# 曲线列表分析
def analyze_curve_list(self, curve_names: list[str]) -> 'CurveListAnalysis':
    """分析曲线名称列表，提供详细的类型和冲突信息。

    此方法用于在执行操作前，全面了解一个曲线列表的构成，
    例如：
    - 识别哪些是系统曲线、数据曲线、一维、二维等。
    - 检查是否存在名称冲突或不存在的曲线。

    Args:
        curve_names: 待分析的曲线名称列表。

    Returns:
        CurveListAnalysis: 包含详细分析结果的数据对象。

    See Also:
        - `CurveListAnalysis`: 详细分析结果的数据类。
        - `CurveItemAnalysis`: 单个曲线的分析条目。
    """
```

### 4. 曲线基本属性API - CurveBasicAttributes

#### 4.1 创建方法
```python
class CurveBasicAttributes:
    """曲线基本属性。"""

    @classmethod
    def create_1d_curve(
        cls,
        name: str,
        unit: str = "",
        data_type: WpDataType = WpDataType.FLOAT,
        category: WpCurveCategory = WpCurveCategory.LOGGING,
        description: str = "",
        curve_class: WpCurveClass = WpCurveClass.CONTINUOUS,
        is_well_identifier: bool = False,
        depth_role: WpDepthRole | None = None
    ) -> 'CurveBasicAttributes':
        """创建一维曲线属性。"""

    @classmethod
    def create_2d_composite_curve(
        cls,
        name: str,
        element_count: int,
        unit: str = "",
        data_type: WpDataType = WpDataType.FLOAT,
        category: WpCurveCategory = WpCurveCategory.COMPUTED,
        description: str = "",
        curve_class: WpCurveClass = WpCurveClass.CONTINUOUS
    ) -> 'CurveBasicAttributes':
        """创建二维组合曲线属性。"""

    @classmethod
    def create_well_identifier_curve(
        cls,
        name: str | None = None,
        description: str | None = None
    ) -> 'CurveBasicAttributes':
        """创建井名标识曲线的便捷方法。

        Args:
            name: 井名曲线名称，默认为WpStandardColumn.WELL_NAME
            description: 备注信息，默认为"井名标识曲线"
        """

    @classmethod
    def create_depth_reference_curve(
        cls,
        name: str | None = None,
        unit: str | None = None,
        description: str | None = None
    ) -> 'CurveBasicAttributes':
        """创建单深度参考曲线的便捷方法。

        Args:
            name: 深度曲线名称，默认为WpStandardColumn.DEPTH
            unit: 深度单位，默认为WpDepthUnit.METER
            description: 备注信息，默认为"深度参考曲线"
        """

    @classmethod
    def create_interval_depth_reference_curves(
        cls,
        top_name: str | None = None,
        bottom_name: str | None = None,
        unit: str | None = None,
        top_description: str | None = None,
        bottom_description: str | None = None
    ) -> tuple['CurveBasicAttributes', 'CurveBasicAttributes']:
        """创建区间深度参考曲线对的便捷方法。

        Args:
            top_name: 顶深曲线名称，默认为WpStandardColumn.DEPTH_TOP
            bottom_name: 底深曲线名称，默认为WpStandardColumn.DEPTH_BOTTOM
            unit: 深度单位，默认为WpDepthUnit.METER
            top_description: 顶深曲线备注信息
            bottom_description: 底深曲线备注信息

        Returns:
            tuple[CurveBasicAttributes, CurveBasicAttributes]: (顶深曲线, 底深曲线)
        """
```

#### 4.1.1 便捷方法使用示例
```python
# 创建标准井名曲线
well_attrs = CurveBasicAttributes.create_well_identifier_curve()
assert well_attrs.name == WpStandardColumn.WELL_NAME
assert well_attrs.is_well_identifier is True

# 创建自定义名称的井名曲线
custom_well = CurveBasicAttributes.create_well_identifier_curve(name="WELL_NO")
assert custom_well.name == "WELL_NO"

# 创建标准深度曲线（米）
depth_attrs = CurveBasicAttributes.create_depth_reference_curve()
assert depth_attrs.name == WpStandardColumn.DEPTH
assert depth_attrs.unit == WpDepthUnit.METER
assert depth_attrs.depth_role == WpDepthRole.SINGLE

# 创建自定义深度曲线（英尺）
md_attrs = CurveBasicAttributes.create_depth_reference_curve(
    name="MD", unit=WpDepthUnit.FEET
)
assert md_attrs.name == "MD"
assert md_attrs.unit == WpDepthUnit.FEET

# 创建标准区间深度曲线对
top_attrs, bottom_attrs = CurveBasicAttributes.create_interval_depth_reference_curves()
assert top_attrs.name == WpStandardColumn.DEPTH_TOP
assert top_attrs.depth_role == WpDepthRole.TOP
assert bottom_attrs.name == WpStandardColumn.DEPTH_BOTTOM
assert bottom_attrs.depth_role == WpDepthRole.BOTTOM

# 创建自定义区间深度曲线对
top_md, bottom_md = CurveBasicAttributes.create_interval_depth_reference_curves(
    top_name="MD_Top", bottom_name="MD_Bottom", unit=WpDepthUnit.FEET
)
assert top_md.name == "MD_Top"
assert bottom_md.name == "MD_Bottom"
```

#### 4.1.2 批量操作便捷方法
```python
class CurveMetadata:
    def add_standard_system_curves(
        self,
        well_name: str | None = None,
        depth_name: str | None = None,
        depth_unit: str | None = None,
        dataset_type: WpDsType = WpDsType.CONTINUOUS
    ) -> None:
        """添加标准系统曲线（井名+深度）的便捷方法。

        Args:
            well_name: 井名曲线名称，默认为WpStandardColumn.WELL_NAME
            depth_name: 深度曲线名称，默认为WpStandardColumn.DEPTH
            depth_unit: 深度单位，默认为WpDepthUnit.METER
            dataset_type: 数据集类型，CONTINUOUS/POINT使用单深度，INTERVAL使用双深度
        """
```

#### 4.1.3 批量操作使用示例
```python
# 为连续型数据集添加标准系统曲线
metadata = CurveMetadata()
metadata.add_standard_system_curves()
assert metadata.has_curve(WpStandardColumn.WELL_NAME)
assert metadata.has_curve(WpStandardColumn.DEPTH)

# 为区间型数据集添加系统曲线
metadata.add_standard_system_curves(dataset_type=WpDsType.INTERVAL)
assert metadata.has_curve(WpStandardColumn.DEPTH_TOP)
assert metadata.has_curve(WpStandardColumn.DEPTH_BOTTOM)

# 自定义名称的系统曲线
metadata.add_standard_system_curves(
    well_name="WELL_NO",
    depth_name="MD",
    depth_unit=WpDepthUnit.FEET
)
```

#### 4.2 克隆方法（新增）
```python
def clone_with_unit(self, new_unit: str) -> 'CurveBasicAttributes':
    """克隆曲线属性并修改单位。"""

def clone_with_category(self, new_category: WpCurveCategory) -> 'CurveBasicAttributes':
    """克隆曲线属性并修改类别。"""

def clone_with_description(self, new_description: str) -> 'CurveBasicAttributes':
    """克隆曲线属性并修改描述。"""
```

### 5. 扩展属性API - ExtAttributeManager

```python
class ExtAttributeManager:
    """扩展属性管理器。"""

    def add_attribute(
        self,
        scope: str,
        scope_name: str,
        curve_name: str | None,
        element_name: str | None,
        attr_name: str,
        attr_type: str,
        unit: str,
        value: Any
    ) -> None:
        """添加扩展属性。"""

    def get_attribute(
        self,
        scope: str,
        scope_name: str,
        curve_name: str | None,
        element_name: str | None,
        attr_name: str
    ) -> Any:
        """获取扩展属性值。"""
```

## 使用示例

### 完整的数据处理流程
```python
import pandas as pd
from logwp.models import WpWellProject
from logwp.models.datasets import WpContinuousDataset
from logwp.models.curve import CurveMetadata, CurveBasicAttributes
from logwp.models.constants import WpCurveCategory, WpDataType

# 1. 创建项目
project = WpWellProject(name="Santos_PCA_Study")

# 2. 准备数据
df = pd.DataFrame({
    'WELL': ['W1'] * 1000,
    'MD': [1000 + i*0.1 for i in range(1000)],
    'GR': [50 + i*0.01 for i in range(1000)],
    'PHIT': [0.15 + i*0.0001 for i in range(1000)],
    'PERM': [100 + i*0.1 for i in range(1000)]
})

# 3. 创建曲线元数据
metadata = CurveMetadata()

# 井名曲线
well_attrs = CurveBasicAttributes.create_1d_curve(
    name="WELL",
    data_type=WpDataType.STR,
    category=WpCurveCategory.IDENTIFIER,
    is_well_identifier=True
)
metadata.add_curve(well_attrs)

# 深度曲线
depth_attrs = CurveBasicAttributes.create_1d_curve(
    name="MD",
    unit="m",
    category=WpCurveCategory.IDENTIFIER
)
metadata.add_curve(depth_attrs)

# 数据曲线
for curve_name, unit in [("GR", "API"), ("PHIT", "v/v"), ("PERM", "mD")]:
    curve_attrs = CurveBasicAttributes.create_1d_curve(
        name=curve_name,
        unit=unit,
        category=WpCurveCategory.LOGGING
    )
    metadata.add_curve(curve_attrs)

# 4. 创建数据集
dataset = WpContinuousDataset(
    name="well_logs",
    df=df,
    curve_metadata=metadata
)

# 5. 添加到项目
project.add_dataset(dataset)

# 6. 使用新增API进行数据分析准备
# 获取适合分析的曲线
analysis_curves = dataset.curve_metadata.get_analysis_suitable_curves()
print(f"适合分析的曲线: {analysis_curves}")  # ['GR', 'PHIT', 'PERM']

# 数据质量检查
validation_result = dataset.validate_numeric_data(analysis_curves)
print(f"数据验证通过: {validation_result['validation_passed']}")

# 获取DataFrame列名用于实际计算
df_columns = dataset.curve_metadata.get_dataframe_columns_for_curves(analysis_curves)
print(f"DataFrame列名: {df_columns}")  # ['GR', 'PHIT', 'PERM']

# 7. 生成项目概况
summary = project.generate_summary()
print(summary)
```

## 常见使用模式

### 1. PCA数据准备模式
```python
# 智能曲线过滤
analysis_curves = dataset.curve_metadata.get_analysis_suitable_curves()

# 数据质量验证
validation_result = dataset.validate_numeric_data(analysis_curves)
if not validation_result["validation_passed"]:
    print(f"数据质量问题: {validation_result['issues']}")

# 获取实际计算用的DataFrame列
df_columns = dataset.curve_metadata.get_dataframe_columns_for_curves(analysis_curves)
ml_data = dataset.df[df_columns]
```

### 2. 数据集类型适配模式
```python
if dataset.is_continuous_dataset():
    print("处理连续型测井数据")
elif dataset.is_interval_dataset():
    print("处理区间型解释数据")
    # 区间数据集会自动排除双深度索引
elif dataset.is_discrete_dataset():
    print("处理离散型点数据")
```

### 3. 曲线属性克隆模式
```python
# 标准化处理后修改单位
original_curve = metadata.get_curve("GR")
standardized_curve = original_curve.clone_with_unit("dimensionless")

# 计算曲线改变类别
computed_curve = original_curve.clone_with_category(WpCurveCategory.COMPUTED)
```

## 性能优化建议

### 1. 批量操作
```python
# ✅ 推荐：批量获取DataFrame列名
curve_names = ["GR", "PHIT", "T2_VALUE"]
columns = metadata.get_dataframe_columns_for_curves(curve_names)

# ❌ 避免：逐个查询
columns = []
for curve_name in curve_names:
    curve = metadata.get_curve(curve_name)
    if curve.is_2d_composite_curve():
        columns.extend(curve.dataframe_element_names)
    else:
        columns.append(curve.dataframe_column_name)
```

### 2. 数据验证
```python
# ✅ 推荐：一次性验证所有相关曲线
validation_result = dataset.validate_numeric_data(analysis_curves)

# ❌ 避免：逐个验证
for curve in analysis_curves:
    # 重复的验证逻辑...
```

## 错误处理

### 常见异常类型
```python
from logwp.models.exceptions import (
    WpDataError,
    WpCurveMetadataError,
    WpDatasetTypeError
)

try:
    dataset = WpContinuousDataset(name="test", df=invalid_df)
except WpDataError as e:
    print(f"数据错误: {e}")
    print(f"错误上下文: {e.context}")

try:
    curve = metadata.get_curve("NONEXISTENT")
except WpCurveMetadataError as e:
    print(f"曲线元数据错误: {e}")
```

## 高级用法

### 1. 自定义数据验证
```python
def custom_data_validation(dataset: WpDepthIndexedDatasetBase) -> dict[str, Any]:
    """自定义数据验证逻辑。"""
    analysis_curves = dataset.curve_metadata.get_analysis_suitable_curves()

    # 基础验证
    validation_result = dataset.validate_numeric_data(analysis_curves)

    # 自定义业务规则验证
    custom_issues = []

    # 检查孔隙度范围
    if "PHIT" in analysis_curves:
        phit_data = dataset.df["PHIT"]
        if (phit_data < 0).any() or (phit_data > 1).any():
            custom_issues.append("孔隙度超出物理范围[0,1]")

    # 检查渗透率范围
    if "PERM" in analysis_curves:
        perm_data = dataset.df["PERM"]
        if (perm_data < 0).any():
            custom_issues.append("渗透率存在负值")

    # 合并验证结果
    validation_result["custom_issues"] = custom_issues
    validation_result["validation_passed"] = (
        validation_result["validation_passed"] and len(custom_issues) == 0
    )

    return validation_result
```

### 2. 动态曲线元数据生成
```python
def create_pca_result_metadata(
    original_metadata: CurveMetadata,
    n_components: int,
    curve_name_prefix: str = "PC"
) -> CurveMetadata:
    """为PCA结果创建曲线元数据。"""
    new_metadata = CurveMetadata()

    # 保留系统曲线
    system_curves = original_metadata.get_system_curves()
    for curve_name in system_curves:
        original_curve = original_metadata.get_curve(curve_name)
        new_metadata.add_curve(original_curve)

    # 创建PCA主成分曲线
    for i in range(1, n_components + 1):
        pc_curve = CurveBasicAttributes.create_1d_curve(
            name=f"{curve_name_prefix}{i}",
            unit="dimensionless",
            category=WpCurveCategory.COMPUTED,
            description=f"第{i}主成分"
        )
        new_metadata.add_curve(pc_curve)

    return new_metadata
```

### 3. 数据集转换和合并
```python
def merge_datasets(
    datasets: list[WpDepthIndexedDatasetBase],
    merge_strategy: str = "inner"
) -> WpDepthIndexedDatasetBase:
    """合并多个数据集。"""
    if not datasets:
        raise ValueError("数据集列表不能为空")

    # 检查数据集类型一致性
    dataset_types = [type(ds) for ds in datasets]
    if len(set(dataset_types)) > 1:
        raise ValueError("只能合并相同类型的数据集")

    # 获取公共曲线
    common_curves = set(datasets[0].curve_metadata.get_analysis_suitable_curves())
    for dataset in datasets[1:]:
        dataset_curves = set(dataset.curve_metadata.get_analysis_suitable_curves())
        if merge_strategy == "inner":
            common_curves &= dataset_curves
        elif merge_strategy == "outer":
            common_curves |= dataset_curves

    # 执行合并逻辑...
    # 这里简化实现，实际需要考虑深度对齐、数据插值等

    return merged_dataset
```

### 4. 扩展属性高级用法
```python
from logwp.models.ext_attr import ExtAttributeManager, T2AxisProcessor

def setup_nmr_attributes(manager: ExtAttributeManager, dataset_name: str):
    """设置NMR相关的扩展属性。"""

    # T2轴配置
    t2_config = {
        "type": "log10",
        "min_value": 0.1,
        "max_value": 10000.0,
        "num_points": 64
    }

    manager.add_attribute(
        scope="DS",
        scope_name=dataset_name,
        curve_name="T2_VALUE",
        element_name=None,
        attr_name="T2_AXIS",
        attr_type="COMP",
        unit="ms",
        value=t2_config
    )

    # 处理器使用
    processor = T2AxisProcessor()
    t2_axis = processor.create_domain_object(t2_config)

    return t2_axis
```

## 插值与重采样健robust性重构 (Robustness Refactoring for Interpolation & Resampling)

> **🎯 核心目标**: 确保所有插值和重采样操作的科学性和健壮性，防止生成不合理的推断数据。
>
> **🔑 关键变更**: 引入 `max_interpolation_distance` 参数，并将其推广为所有插值方法的通用“置信区间”控制参数。

### 问题背景

在早期版本中，部分数据处理函数（如数据集合并、离散转连续、数据重采样）在处理稀疏数据或存在数据空洞时，可能会进行无限制的插值或外插，从而生成远离真实数据点、缺乏科学依据的推断值。

### 统一解决方案：`max_interpolation_distance`

为了从根本上解决此问题，我们进行了一项系统性的重构，核心是引入了 `max_interpolation_distance` 参数：

- **功能**: 对于任何一个需要插值的目标深度点，如果它与原始数据集中**最近的有效数据点**的距离超过了 `max_interpolation_distance`，那么该点的插值结果将被强制覆盖为 `NaN`。
- **适用范围**: 此参数被统一应用于所有插值方法，包括 `nearest`, `linear`, `cubic` 等。

### 受影响的核心API

以下核心API已完成此项健壮性升级：

1.  **`WpWellProject.merge_datasets_left_aligned`**:
    - `max_interpolation_distance` 现在是一个**必须**的关键字参数，强制用户在进行左对齐合并时考虑插值的置信范围。

2.  **`WpContinuousDataset.resample_depth`**:
    - 新增了 `max_interpolation_distance` 可选参数。
    - **默认值**: `new_sampling_interval / 2`。这是一个安全的默认设置，意味着只有当新采样点离最近的原始数据点在半个新步长内时，才进行插值。

3.  **`discrete_to_continuous` (内部服务)**:
    - 同样新增了 `max_interpolation_distance` 可选参数，默认值为 `sampling_interval / 2`。

### 架构级重构：DRY原则

为了确保逻辑的一致性和可维护性，所有距离限制的检查逻辑都被提取到了一个通用的工具函数中：

- **`logwp.models.utils.interpolation_utils.apply_interpolation_distance_limit`**

所有受影响的模块现在都调用此函数，遵循了 **DRY (Don't Repeat Yourself)** 原则，保证了代码的整洁和健壮。

### 性能优化

在重构过程中，对底层的 `_interpolate_nearest` 函数进行了性能优化，使用 **NumPy向量化** 操作替代了原有的低效Python循环，大幅提升了最近邻插值的计算效率。


## 最佳实践

### 1. 内存管理
```python
# ✅ 推荐：及时释放大型数据集
def process_large_dataset(dataset: WpDepthIndexedDatasetBase):
    try:
        # 处理数据
        analysis_curves = dataset.curve_metadata.get_analysis_suitable_curves()
        result = some_heavy_computation(dataset.df[analysis_curves])
        return result
    finally:
        # 清理引用
        del dataset
        import gc
        gc.collect()

# ✅ 推荐：使用数据完整性检查避免无效计算
def safe_analysis(dataset: WpDepthIndexedDatasetBase):
    completeness = dataset.get_data_completeness()

    # 过滤数据完整性低的曲线
    good_curves = [
        curve for curve in dataset.curve_metadata.get_analysis_suitable_curves()
        if all(completeness.get(col, 0.0) > 0.8
               for col in dataset.curve_metadata.get_dataframe_columns_for_curves([curve]))
    ]

    if len(good_curves) < 3:
        raise ValueError("可用于分析的高质量曲线不足")

    return good_curves
```

### 2. 错误处理模式
```python
from logwp.models.exceptions import WpDataError, ErrorContext

def robust_dataset_creation(name: str, df: pd.DataFrame) -> WpDepthIndexedDatasetBase:
    """健壮的数据集创建。"""
    try:
        # 数据预验证
        if df.empty:
            raise WpDataError("DataFrame不能为空")

        # 尝试创建连续型数据集
        dataset = WpContinuousDataset(name=name, df=df)

        # 验证数据质量
        validation_result = dataset.validate_numeric_data()
        if not validation_result["validation_passed"]:
            logger.warning(f"数据质量问题: {validation_result['issues']}")

        return dataset

    except Exception as e:
        # 提供详细的错误上下文
        context = ErrorContext(
            operation="dataset_creation",
            dataset_name=name,
            additional_info={
                "dataframe_shape": df.shape,
                "dataframe_columns": list(df.columns),
                "error_type": type(e).__name__
            }
        )
        raise WpDataError(f"数据集创建失败: {e}", context=context) from e
```

### 3. 性能监控
```python
import time
from functools import wraps

def monitor_api_performance(func):
    """API性能监控装饰器。"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time

            # 记录性能指标
            logger.info(f"{func.__name__} 执行时间: {execution_time:.3f}s")

            # 性能警告
            if execution_time > 1.0:
                logger.warning(f"{func.__name__} 执行时间过长: {execution_time:.3f}s")

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} 执行失败 (耗时: {execution_time:.3f}s): {e}")
            raise
    return wrapper

# 使用示例
@monitor_api_performance
def analyze_dataset(dataset: WpDepthIndexedDatasetBase):
    analysis_curves = dataset.curve_metadata.get_analysis_suitable_curves()
    return dataset.validate_numeric_data(analysis_curves)
```

## 故障排除

### 常见问题和解决方案

#### 1. 曲线名称冲突
```python
# 问题：添加曲线时名称冲突
try:
    metadata.add_curve(new_curve)
except WpCurveMetadataError as e:
    if "已存在" in str(e):
        # 解决方案：使用唯一名称
        unique_name = f"{new_curve.name}_{int(time.time())}"
        new_curve = new_curve.clone_with_description(f"重命名为: {unique_name}")
        metadata.add_curve(new_curve)
```

#### 2. DataFrame列名不匹配
```python
# 问题：曲线元数据与DataFrame列名不一致
def fix_column_mismatch(dataset: WpDepthIndexedDatasetBase):
    metadata_columns = dataset.curve_metadata.get_all_dataframe_column_names()
    df_columns = list(dataset.df.columns)

    missing_in_df = set(metadata_columns) - set(df_columns)
    missing_in_metadata = set(df_columns) - set(metadata_columns)

    if missing_in_df:
        logger.warning(f"DataFrame中缺少列: {missing_in_df}")

    if missing_in_metadata:
        logger.warning(f"元数据中缺少曲线: {missing_in_metadata}")
        # 自动添加缺失的曲线元数据
        for col in missing_in_metadata:
            auto_curve = CurveBasicAttributes.create_1d_curve(
                name=col,
                description="自动生成的曲线属性"
            )
            dataset.curve_metadata.add_curve(auto_curve)
```

#### 3. 数据类型不匹配
```python
# 问题：数据类型与元数据定义不符
def fix_data_type_mismatch(dataset: WpDepthIndexedDatasetBase):
    validation_result = dataset.validate_numeric_data()

    for curve_name, issue in validation_result.get("issues", []):
        if "不是数值型" in issue:
            # 尝试类型转换
            columns = dataset.curve_metadata.get_dataframe_columns_for_curves([curve_name])
            for col in columns:
                try:
                    dataset.df[col] = pd.to_numeric(dataset.df[col], errors='coerce')
                    logger.info(f"成功转换 {col} 为数值型")
                except Exception as e:
                    logger.error(f"无法转换 {col} 为数值型: {e}")
```

## 常量和异常使用规范

> **🏗️ 架构原则**: logwp包采用分层架构，不同层级的常量和异常有明确的使用边界
>
> **📦 包级隔离**: 各二级子包有自己特定的常量和异常定义
>
> **🔄 重构方向**: 最终目标是实现包级自治，减少跨包依赖

### 概述

logwp包采用分层架构设计，各二级子包有自己特定的常量和异常。**特别地，`logwp/models/` 作为整个项目的数据基础层，其常量、异常、对象模型可以被除 `logwp/infra/` 外的所有其他包使用。** 根据功能特点和依赖关系，二级子包分为两种架构模式。

### 架构模式分类

#### 模式1: 数据基础型 - 全局共享常量管理

**特征**: 作为整个项目的数据基础层，提供核心数据模型、常量和异常

**代表包**: `logwp/models/` （**项目数据基础层**）

**常量和异常使用原则**:
- **全局共享**: 除 `logwp/infra/` 外，所有其他包都可以使用 models 的常量、异常、对象模型
- **数据基础**: 提供测井数据的核心抽象和标准定义
- **内部统一**: models 内部所有子包统一使用二级子包中的常量和异常
- **架构基石**: 作为整个项目的架构基础，保证数据模型的一致性

```python
# ✅ models内部：所有子包使用统一常量
from logwp.models.constants import WpDataType, WpCurveCategory, WpDepthRole
from logwp.models.exceptions import WpError, WpValidationError

# logwp/models/datasets/continuous.py
class WpContinuousDataset:
    def validate(self):
        if self.depth_sampling_rate <= 0:
            raise WpValidationError("采样率必须大于0")  # 使用models统一异常

# ✅ 其他包使用models：作为数据基础层
# logwp/io/wp_excel/reader.py
from logwp.models.constants import WpDataType, WpStandardColumn  # 可以使用
from logwp.models.datasets import WpContinuousDataset           # 可以使用
from logwp.models.exceptions import WpValidationError          # 可以使用

# logwp/extras/pca/analyzer.py
from logwp.models.datasets import WpContinuousDataset          # 可以使用
from logwp.models.constants import WpCurveCategory             # 可以使用

# ❌ 例外：utils包不使用models
# logwp/infra/ 包应该保持独立，不依赖models
```

**包结构示例**:
```
logwp/models/                    # 🏛️ 项目数据基础层
├── constants.py                 # 🎯 全局共享常量定义
├── exceptions.py                # 🎯 全局共享异常定义
├── types.py                     # 🎯 全局共享类型定义
├── datasets/
│   ├── continuous.py            # 使用 models.constants, models.exceptions
│   ├── discrete.py              # 使用 models.constants, models.exceptions
│   └── interval.py              # 使用 models.constants, models.exceptions
├── curve/
│   ├── metadata.py              # 使用 models.constants, models.exceptions
│   └── attributes.py            # 使用 models.constants, models.exceptions
└── mapping.py                   # 使用 models.constants, models.exceptions

# 其他包可以使用models作为基础
logwp/io/wp_excel/reader.py      # 可以导入 models.constants, models.datasets
logwp/extras/pca/analyzer.py     # 可以导入 models.datasets, models.exceptions
scape/core/algorithms/           # 可以导入 models.* (除utils外都可以)
```

#### 模式2: 独立功能型 - 分布式常量管理

**特征**: 二级子包下面的三级子包功能相对独立

**代表包**: `logwp/extras/`、`logwp/io/`、`logwp/infra/`

**常量和异常使用原则**:
- **可以使用models**: 作为数据基础层，可以导入使用 `logwp.models` 的常量、异常、对象模型（`logwp/infra/` 除外）
- **包级特定**: 二级子包有基本的常量和定义，三级子包有自己特定的常量和定义
- **重构方向**: 最终目标是三级子包使用自己的常量和异常，但仍可使用models作为数据基础

```python
# ✅ 目标状态：三级子包使用自己的常量和异常 + models作为数据基础
from logwp.models.datasets import WpContinuousDataset        # 数据基础层
from logwp.models.constants import WpCurveCategory           # 数据基础层
from logwp.extras.pca.constants import PcaMethod, PcaValidationMode  # PCA专用
from logwp.extras.pca.exceptions import PcaError, PcaConvergenceError # PCA专用

# logwp/extras/pca/analyzer.py
class PcaAnalyzer:
    def __init__(self, method: PcaMethod = PcaMethod.SVD):
        self.method = method  # 使用PCA专用常量

    def analyze(self, dataset: WpContinuousDataset):  # 使用models数据模型
        if not self._validate_data(dataset):
            raise PcaConvergenceError("PCA收敛失败")  # 使用PCA专用异常

# ⚠️ 过渡状态：可能暂时使用二级包常量（重构前）
from logwp.models.constants import WpDataType       # 始终可用（数据基础）
from logwp.extras.constants import ExtrasDataType  # 临时使用
from logwp.extras.pca.constants import PcaMethod   # 最终目标
```

**包结构示例**:
```
logwp/extras/
├── constants.py          # 🔧 基本常量（过渡期）
├── exceptions.py         # 🔧 基本异常（过渡期）
├── pca/
│   ├── constants.py      # 🎯 PCA专用常量（目标）
│   ├── exceptions.py     # 🎯 PCA专用异常（目标）
│   ├── analyzer.py       # 使用 pca.constants, pca.exceptions
│   └── validator.py      # 使用 pca.constants, pca.exceptions
├── nmr/
│   ├── constants.py      # 🎯 NMR专用常量（目标）
│   ├── exceptions.py     # 🎯 NMR专用异常（目标）
│   └── processor.py      # 使用 nmr.constants, nmr.exceptions
└── stats/
    ├── constants.py      # 🎯 统计专用常量（目标）
    ├── exceptions.py     # 🎯 统计专用异常（目标）
    └── calculator.py     # 使用 stats.constants, stats.exceptions
```

### 具体包的使用规范

#### 1. logwp/models/ - 数据基础型（全局共享）

```python
# ✅ models内部标准用法
from logwp.models.constants import (
    WpDataType, WpCurveCategory, WpDepthRole, WpDsType,
    WpStandardColumn, WpDepthUnit, WpCurveDescription, WpLogMessage
)
from logwp.models.exceptions import (
    WpError, WpValidationError, WpConsistencyError, WpCircularMappingError
)

# ✅ 其他包使用models（除utils外）
# logwp/io/wp_excel/reader.py
from logwp.models.constants import WpStandardColumn, WpDataType
from logwp.models.datasets import WpContinuousDataset
from logwp.models.exceptions import WpValidationError

# logwp/extras/pca/analyzer.py
from logwp.models.datasets import WpContinuousDataset
from logwp.models.constants import WpCurveCategory

# scape/core/algorithms/obmiq.py
from logwp.models.datasets import WpContinuousDataset
from logwp.models.constants import WpCurveCategory

# ❌ 错误：不要在models子包中定义独立常量
# logwp/models/datasets/constants.py  # 不应该存在

# ❌ 例外：utils包不使用models
# logwp/infra/ 应该保持独立，不依赖models
```

#### 2. logwp/io/ - 独立功能型（可使用models）

```python
# ✅ 使用models作为数据基础
from logwp.models.datasets import WpContinuousDataset, WpDiscreteDataset
from logwp.models.constants import WpStandardColumn, WpDataType
from logwp.models.exceptions import WpValidationError

# 🎯 目标状态：各格式处理器使用自己的常量
from logwp.io.wp_excel.constants import WpXlsxKey, WpFileFormat
from logwp.io.wp_excel.exceptions import WpExcelError, WpExcelFormatError

from logwp.io.las.constants import LasSection, LasKeyword
from logwp.io.las.exceptions import LasError, LasParseError

# ⚠️ 过渡状态：可能暂时使用io级别常量
from logwp.io.constants import IoDataType  # 临时

# 示例：WP Excel读取器
class WpExcelReader:
    def read(self, file_path: str) -> WpContinuousDataset:  # 返回models数据类型
        # 使用models常量进行数据验证
        if column_name == WpStandardColumn.WELL_NAME:
            # 处理井名列
            pass
```

#### 3. logwp/extras/ - 独立功能型（可使用models）

```python
# ✅ 使用models作为数据基础
from logwp.models.datasets import WpContinuousDataset, WpDiscreteDataset
from logwp.models.constants import WpCurveCategory, WpDataType
from logwp.models.exceptions import WpValidationError

# 🎯 目标状态：各功能模块使用自己的常量
from logwp.extras.pca.constants import PcaMethod, PcaComponents
from logwp.extras.pca.exceptions import PcaError

from logwp.extras.nmr.constants import T2AxisType, NmrProcessingMode
from logwp.extras.nmr.exceptions import NmrError

from logwp.extras.stats.constants import StatMethod, DistributionType
from logwp.extras.stats.exceptions import StatError

# 示例：PCA分析器
class PcaAnalyzer:
    def analyze(self, dataset: WpContinuousDataset) -> WpContinuousDataset:  # 使用models数据类型
        # 使用models常量进行曲线过滤
        numeric_curves = [c for c in dataset.curve_metadata.curves
                         if c.category != WpCurveCategory.IDENTIFIER]
```

#### 4. logwp/infra/ - 独立功能型（不使用models）

```python
# ✅ utils包保持独立，不依赖models
# 🎯 目标状态：各工具模块使用自己的常量
from logwp.infra.validation.constants import ValidationLevel, ValidationRule
from logwp.infra.validation.exceptions import ValidationError

from logwp.infra.performance.constants import PerformanceMetric, OptimizationLevel
from logwp.infra.performance.exceptions import PerformanceError

from logwp.infra.logging.constants import LogLevel, LogFormat
from logwp.infra.logging.exceptions import LoggingError

# ❌ utils包不应该导入models
# from logwp.models.constants import WpDataType  # 不应该这样做
# from logwp.models.datasets import WpContinuousDataset  # 不应该这样做

# ✅ utils包应该保持通用性和独立性
class PerformanceMonitor:
    def __init__(self, level: PerformanceMetric = PerformanceMetric.BASIC):
        self.level = level  # 使用utils自己的常量
```

### 重构演进路径

#### 阶段1: 初始状态（当前部分包的状态）
```python
# 可能存在的跨包依赖（除models外）
from logwp.models.constants import WpDataType  # ✅ 在extras中使用（允许）
from logwp.io.constants import IoFormat        # ❌ 在utils中使用（不推荐）
from logwp.extras.constants import ExtrasType  # ❌ 在io中使用（不推荐）
```

#### 阶段2: 过渡状态（重构进行中）
```python
# 逐步建立包级常量，但保持models作为数据基础
from logwp.models.constants import WpDataType        # ✅ 始终可用（数据基础）
from logwp.models.datasets import WpContinuousDataset # ✅ 始终可用（数据基础）
from logwp.extras.constants import ExtrasDataType    # 二级包常量
from logwp.extras.pca.constants import PcaMethod     # 三级包常量（部分）
```

#### 阶段3: 目标状态（重构完成）
```python
# 包级自治 + models作为数据基础
from logwp.models.constants import WpDataType, WpCurveCategory  # ✅ 数据基础层
from logwp.models.datasets import WpContinuousDataset          # ✅ 数据基础层
from logwp.extras.pca.constants import PcaMethod, PcaDataType  # 包级专用
from logwp.extras.pca.exceptions import PcaError              # 包级专用
# 不依赖其他非models包的常量和异常
```

### 开发指导原则

#### 1. 新代码开发原则

```python
# ✅ 推荐：新功能使用目标架构 + models作为数据基础
# 新建 logwp/extras/new_feature/
from logwp.models.datasets import WpContinuousDataset          # ✅ 数据基础层
from logwp.models.constants import WpCurveCategory             # ✅ 数据基础层
from logwp.extras.new_feature.constants import NewFeatureConfig # 功能专用
from logwp.extras.new_feature.exceptions import NewFeatureError # 功能专用

# ❌ 避免：新功能依赖其他非models包常量
from logwp.io.constants import IoFormat        # 避免跨包依赖
from logwp.extras.pca.constants import PcaMethod  # 避免跨功能依赖

# ❌ 特别注意：utils包不使用models
# 在logwp/utils/中不应该导入models
```

#### 2. 重构现有代码原则

```python
# 🔄 重构步骤：
# 1. 识别不合理的跨包依赖（非models包之间）
# 2. 创建包级常量和异常
# 3. 逐步替换导入
# 4. 保持models作为数据基础

# 重构前（不合理的跨包依赖）
from logwp.io.constants import IoDataType      # 在extras中使用
from logwp.extras.constants import ExtrasType  # 在io中使用

# 重构后（包级自治 + models基础）
from logwp.models.constants import WpDataType     # ✅ 数据基础层
from logwp.extras.pca.constants import PcaDataType # 包级专用
from logwp.io.wp_excel.constants import ExcelDataType # 包级专用
```

#### 3. 常量命名规范

```python
# ✅ 包级前缀命名
class WpDataType(str, Enum):      # models包：Wp前缀
class PcaMethod(str, Enum):       # pca包：Pca前缀
class LasSection(str, Enum):      # las包：Las前缀
class NmrProcessing(str, Enum):   # nmr包：Nmr前缀

# ✅ 异常命名规范
class WpError(Exception):         # models包：Wp前缀
class PcaError(Exception):        # pca包：Pca前缀
class LasError(Exception):        # las包：Las前缀
class NmrError(Exception):        # nmr包：Nmr前缀
```

### 总结

**logwp包常量和异常使用的核心原则**:

1. **models是数据基础**: `logwp/models/` 作为整个项目的数据基础层，除 `logwp/infra/` 外的所有包都可以使用其常量、异常、对象模型
2. **utils保持独立**: `logwp/infra/` 包应该保持独立，不依赖 `logwp/models/`，确保工具的通用性
3. **包级自治**: 各包使用自己的专用常量和异常，避免非models包之间的相互依赖
4. **分层架构**: 数据基础层(models) → 功能层(io/extras) → 应用层(scape)，上层可以使用下层
5. **新代码优先**: 新功能直接采用目标架构（包级专用 + models基础）
6. **渐进重构**: 现有代码逐步向目标架构演进

**记住：models是数据基础，utils保持独立，其他包实现自治！**

### 依赖关系图

```
                    ┌─────────────────┐
                    │   logwp/models  │ ← 数据基础层（全局共享）
                    │   - constants   │
                    │   - exceptions  │
                    │   - datasets    │
                    │   - types       │
                    └─────────────────┘
                            ↑
                ┌───────────┼───────────┐
                │           │           │
        ┌───────▼───┐  ┌───▼────┐  ┌───▼──────┐
        │ logwp/io  │  │logwp/  │  │ logwp/   │
        │           │  │extras  │  │ testing  │
        │ ┌───────┐ │  │┌─────┐ │  │          │
        │ │wp_excel│ │  ││ pca │ │  │          │
        │ │  las  │ │  ││ nmr │ │  │          │
        │ │ ...   │ │  ││stats│ │  │          │
        │ └───────┘ │  │└─────┘ │  │          │
        └───────────┘  └────────┘  └──────────┘
                            ↑
                    ┌───────┼───────┐
                    │       │       │
            ┌───────▼──┐ ┌──▼────┐ ┌▼─────────┐
            │ scape/   │ │scape/ │ │ scape/   │
            │ core     │ │study  │ │ case     │
            └──────────┘ └───────┘ └──────────┘

        ┌─────────────┐
        │ logwp/infra │ ← 独立工具层（不依赖models）
        │ - logging   │
        │ - performance│
        │ - validation│
        └─────────────┘

依赖规则：
✅ 所有包（除utils）可以使用 models
✅ 上层包可以使用下层包
❌ 同层包之间不相互依赖
❌ utils 不依赖 models
❌ 下层包不依赖上层包
```

## logwp.testing 测试工具包使用指南

> **🚨 重要提示**: 在编写任何测试代码之前，请先阅读本章节！
>
> **📋 测试规范**: 所有SCAPE项目的测试必须优先使用 `logwp.testing` 工具包
>
> **🚫 禁止重复造轮子**: 不要手动构建测试数据，使用现有的便捷方法

### 概述

`logwp.testing` 是专门为SCAPE项目设计的测试工具包，提供快速生成测试数据的便捷方法。**在编写测试时，请优先使用此工具包，避免重复造轮子。**

### 设计原则

- **测试专用**: 仅用于测试目的，不应在生产代码中使用
- **快速构建**: 提供简洁的API，减少测试代码复杂度
- **标准化**: 确保测试数据的一致性和可重复性
- **职责分离**: 与logwp.models核心模型完全分离

### 核心组件

#### 1. 快速生成器 (logwp.testing.utils)

```python
from logwp.testing.utils import quick_continuous_df, quick_metadata

# 快速生成DataFrame
df = quick_continuous_df(
    well_name="TEST-1",
    start_depth=2500,
    end_depth=2510,
    interval=0.5,
    GR=lambda d: 50 + d*0.01,  # 函数形式
    PHIT=0.15,                 # 常数形式
    FACIES=["砂岩", "泥岩"] * 10  # 列表形式
)

# 快速生成元数据（自动推断类型）
metadata = quick_metadata("WELL", "MD", "GR", "PHIT", "FACIES")
```

#### 2. 数据集构建器 (logwp.testing.builders)

```python
from logwp.testing.builders import DatasetBuilder

# 连续型数据集
dataset = DatasetBuilder.quick_continuous_dataset(
    name="test_logs",
    well_name="W-1",
    depth_range=(2500, 2510),
    curves={"GR": 50, "PHIT": 0.15}
)

# 离散型数据集
core_dataset = DatasetBuilder.quick_discrete_dataset(
    name="test_core",
    depths=[2500.5, 2502.3, 2505.1],
    curves={"PERM": [12.5, 8.9, 15.2], "FACIES": ["砂岩", "泥岩", "砂岩"]}
)

# 区间型数据集
interval_dataset = DatasetBuilder.quick_interval_dataset(
    name="test_zones",
    intervals=[(2500, 2505), (2505, 2510)],
    curves={"FORMATION": ["FM_A", "FM_B"], "NET_PAY": [4.5, 6.2]}
)

# NMR数据集
nmr_dataset = DatasetBuilder.synthetic_nmr_dataset(
    name="nmr_test",
    depth_range=(2500, 2520),
    t2_bins=64
)
```

#### 3. 项目构建器 (logwp.testing.builders)

```python
from logwp.testing.builders import ProjectBuilder

# 使用流畅API构建项目
project = ProjectBuilder("Test_Project") \
    .with_dataset("logs", continuous_dataset) \
    .with_dataset("core", discrete_dataset) \
    .with_head_attribute("version", "1.0") \
    .with_head_attribute("field", "Santos") \
    .with_well_mapping("C-1A", "C-1") \
    .with_well_mapping("C-1B", "C-1") \
    .build()
```

#### 4. 便捷方法集成

```python
# 使用logwp.models的便捷方法
from logwp.models import WpWellProject

# 批量创建项目（结合testing工具包）
datasets = {
    "logs": DatasetBuilder.quick_continuous_dataset("logs", curves={"GR": 50}),
    "core": DatasetBuilder.quick_discrete_dataset("core", curves={"PERM": [12.5]})
}

project = WpWellProject.create_with_datasets(
    name="Test_Project",
    datasets=datasets,
    head_attributes={"version": "1.0", "field": "Santos"},
    well_mappings={"C-1A": "C-1", "C-1B": "C-1"}
)
```

### 测试数据工厂

#### 预定义测试项目

```python
from logwp.testing.factories import TestDataFactory

# Santos演示项目
project = TestDataFactory.santos_demo_project()

# 最小化项目
minimal_project = TestDataFactory.minimal_project()
```

#### 标准测试夹具

```python
from logwp.testing.fixtures import StandardDatasets

# 获取标准数据集（带缓存）
continuous_ds = StandardDatasets.get_continuous_dataset()
discrete_ds = StandardDatasets.get_discrete_dataset()
interval_ds = StandardDatasets.get_interval_dataset()
nmr_ds = StandardDatasets.get_nmr_dataset()

# 获取所有标准数据集
all_datasets = StandardDatasets.get_all_datasets()
```

### 数据验证工具

```python
from logwp.testing.utils import validate_test_dataset, assert_dataset_structure, check_curve_consistency

# 基本验证
is_valid = validate_test_dataset(dataset)
assert is_valid, "数据集验证失败"

# 结构断言
assert_dataset_structure(
    dataset,
    expected_columns=["WELL", "MD", "GR", "PHIT"],
    min_rows=10,
    max_rows=100
)

# 一致性检查
report = check_curve_consistency(dataset)
if report['issues']:
    print(f"发现 {len(report['issues'])} 个问题: {report['issues']}")
```

### 高级构建器模式

#### 曲线构建器

```python
from logwp.testing.builders import CurveBuilder

# 构建曲线元数据
metadata = CurveBuilder.metadata() \
    .add_well_curve("WELL") \
    .add_depth_curve("MD") \
    .add_logging_curve("GR", unit="API") \
    .add_logging_curve("PHIT", unit="v/v") \
    .build()

# 构建曲线数据
depths = [2500.0, 2501.0, 2502.0, 2503.0, 2504.0]
curve_data = CurveBuilder.data() \
    .with_constant("GR", 50.0) \
    .with_linear("PHIT", start=0.10, end=0.20) \
    .with_function("SW", lambda d: 0.3 + 0.1 * np.sin(d/100)) \
    .build(depths)
```

### 测试最佳实践

#### 1. 优先使用快速方法

```python
# ✅ 推荐：使用快速方法
dataset = DatasetBuilder.quick_continuous_dataset(
    name="test", curves={"GR": 50, "PHIT": 0.15}
)

# ❌ 不推荐：手动构建
df = pd.DataFrame({...})  # 大量重复代码
metadata = CurveMetadata()  # 手动添加曲线
# ...
```

#### 2. 复杂场景使用构建器

```python
# ✅ 推荐：复杂配置使用构建器
project = ProjectBuilder("Complex_Test") \
    .with_dataset("logs", logs_dataset) \
    .with_dataset("core", core_dataset) \
    .with_dataset("zones", zones_dataset) \
    .with_head_attributes({
        "version": "1.0",
        "field": "Santos",
        "operator": "Petrobras"
    }) \
    .build()
```

#### 3. 标准数据使用夹具

```python
# ✅ 推荐：一致性测试使用标准夹具
def test_algorithm_consistency():
    dataset = StandardDatasets.get_continuous_dataset()
    result1 = algorithm_v1(dataset)
    result2 = algorithm_v2(dataset)
    assert_results_similar(result1, result2)
```

#### 4. 验证数据质量

```python
# ✅ 推荐：验证测试数据质量
def test_with_validation():
    dataset = DatasetBuilder.quick_continuous_dataset(...)

    # 验证数据质量
    assert validate_test_dataset(dataset), "测试数据无效"

    # 执行测试
    result = my_algorithm(dataset)
    assert result is not None
```

### 注意事项

#### 1. 环境依赖

```python
# testing工具包需要完整的logwp环境
# 如果环境不完整，会抛出RuntimeError
try:
    dataset = DatasetBuilder.quick_continuous_dataset(...)
except RuntimeError as e:
    pytest.skip(f"需要完整logwp环境: {e}")
```

#### 2. 性能考虑

```python
# 对于大量测试，使用缓存的标准夹具
class TestAlgorithm:
    @classmethod
    def setup_class(cls):
        cls.test_dataset = StandardDatasets.get_continuous_dataset()

    def test_case_1(self):
        result = algorithm(self.test_dataset)  # 复用数据集
```

#### 3. 数据范围

```python
# testing工具包生成的数据主要用于功能验证
# 不保证物理意义的完全正确性
dataset = DatasetBuilder.quick_continuous_dataset(...)
# 适用于：功能测试、集成测试、性能测试
# 不适用于：物理模型验证、生产环境
```

### 快速参考表

| 需求场景 | 推荐方法 | 示例代码 |
|---------|---------|---------|
| 简单连续型数据 | `DatasetBuilder.quick_continuous_dataset()` | `DatasetBuilder.quick_continuous_dataset("test", curves={"GR": 50})` |
| 简单离散型数据 | `DatasetBuilder.quick_discrete_dataset()` | `DatasetBuilder.quick_discrete_dataset("core", curves={"PERM": [12.5]})` |
| 区间数据 | `DatasetBuilder.quick_interval_dataset()` | `DatasetBuilder.quick_interval_dataset("zones", intervals=[(2500, 2505)])` |
| NMR数据 | `DatasetBuilder.synthetic_nmr_dataset()` | `DatasetBuilder.synthetic_nmr_dataset("nmr", t2_bins=64)` |
| 项目创建 | `WpWellProject.create_with_datasets()` | `WpWellProject.create_with_datasets("test", datasets)` |
| 复杂项目 | `ProjectBuilder` | `ProjectBuilder("test").with_dataset(...).build()` |
| 标准数据 | `StandardDatasets` | `StandardDatasets.get_continuous_dataset()` |
| 数据验证 | `validate_test_dataset()` | `validate_test_dataset(dataset)` |
| 元数据生成 | `quick_metadata()` | `quick_metadata("WELL", "MD", "GR", "PHIT")` |

### 总结

**logwp.testing工具包是SCAPE项目测试的标准工具，请在编写测试时优先使用：**

1. **减少代码量**: 从几十行减少到几行
2. **提高质量**: 标准化数据生成和验证
3. **加速开发**: 快速创建各种测试场景
4. **保证一致性**: 标准夹具确保测试数据一致
5. **避免重复**: 不要重复实现已有功能

**记住：不要重复造轮子，优先使用现有工具！**

## 参考文档

- 《SCAPE_MS_方法说明书》- 业务需求和科学定义
- 《SCAPE_SAD_logwp.md》- logwp包架构设计
- 《SCAPE_WFS_WP文件规范.md》- 数据格式规范
- 《SCAPE_CCG_编码与通用规范.md》- 编码规范
- 《SCAPE_STG_软件测试指南.md》- 测试策略
- 《SCAPE_DDS_logwp_extras_pca.md》- PCA包设计文档（API使用示例）
- **logwp/testing/README.md** - 测试工具包详细说明
