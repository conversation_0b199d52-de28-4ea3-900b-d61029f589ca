from __future__ import annotations

"""logwp.models.curve.service.query_translation - 查询字符串转换服务

实现查询字符串中占位符的智能转换功能，支持一维曲线和二维组合曲线元素的DataFrame友好列名转换。
遵循SAD文档的内部服务层设计模式（Utility/Helper Pattern）。

Architecture
------------
层次/依赖: curve模块服务层，依赖curve.metadata、constants、exceptions
设计原则: 无状态服务、职责分离、类型安全
性能特征: 高效正则表达式、内存优化、批量处理

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
- 《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata服务层重构
"""

import re
from typing import TYPE_CHECKING

from logwp.models.exceptions import WpCurveMetadataError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.curve.metadata import CurveBasicAttributes

logger = get_logger(__name__)


def translate_query_for_dataframe(
    query_string: str,
    curves_dict: dict[str, 'CurveBasicAttributes']
) -> str:
    """将包含占位符的查询字符串转换为DataFrame友好列名。

    支持pandas DataFrame.query()方法，将用户使用占位符格式的查询条件转换为
    实际的DataFrame列名（友好名称）。要求查询字符串中的曲线名必须使用
    ${curve_name} 格式的占位符。

    占位符格式要求：
    - 一维曲线：${CURVE_NAME}，例如 ${GR}、${DEN}
    - 二维组合曲线元素：${CURVE_NAME[index]}，例如 ${T2_VALUE[1]}、${PHI_T2_DIST[3]}
    - 不允许使用二维组合曲线基础名称：${T2_VALUE} 是错误的，必须使用 ${T2_VALUE[1]}

    转换规则：
    - 一维曲线占位符转换为对应的dataframe_column_name
    - 二维组合曲线元素占位符转换为对应的dataframe_element_names中的友好名称
    - 其他内容（操作符、数值、括号等）保持不变

    Args:
        query_string: 包含占位符的查询条件字符串
        curves_dict: 曲线字典，用于查找曲线信息

    Returns:
        str: 转换后的查询字符串，使用DataFrame友好列名

    Raises:
        WpCurveMetadataError: 当占位符中包含二维组合曲线基础名称时
        WpCurveMetadataError: 当占位符中包含不存在的曲线名时
        WpCurveMetadataError: 当二维组合曲线元素索引无效时

    Examples:
        >>> # 一维曲线查询
        >>> query = "${GR} > 50 and ${MD} < 2000"
        >>> result = translate_query_for_dataframe(query, curves_dict)
        >>> print(result)  # "GR > 50 and MD < 2000"

        >>> # 二维组合曲线元素查询
        >>> query = "${T2_VALUE[1]} > 10 and ${T2_VALUE[2]} < 20"
        >>> result = translate_query_for_dataframe(query, curves_dict)
        >>> print(result)  # "T2_VALUE_1 > 10 and T2_VALUE_2 < 20"

        >>> # 复合查询
        >>> query = "(${GR} > 50 or ${MD} < 1500) and ${T2_VALUE[1]} > 10"
        >>> result = translate_query_for_dataframe(query, curves_dict)
        >>> print(result)  # "(GR > 50 or MD < 1500) and T2_VALUE_1 > 10"

    Performance Notes:
        - 时间复杂度：O(n×m)，其中n为查询字符串长度，m为占位符数量
        - 空间复杂度：O(n)，需要构建新的查询字符串
        - 建议缓存转换结果以提高重复查询的性能

    References:
        pandas DataFrame.query() 文档：
        https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.query.html
    """
    # 匹配 ${curve_name} 或 ${curve_name[index]} 格式的占位符
    placeholder_pattern = r'\$\{([^}]+)\}'

    def replace_placeholder(match):
        """替换单个占位符的内部函数"""
        curve_ref = match.group(1).strip()  # 提取占位符内容，如 "GR" 或 "T2_VALUE[1]"

        # 检查是否是二维组合曲线元素格式 CURVE_NAME[index]
        if '[' in curve_ref and curve_ref.endswith(']'):
            return translate_2d_curve_element(curve_ref, curves_dict)
        else:
            return translate_1d_curve(curve_ref, curves_dict)

    try:
        # 使用正则表达式替换所有占位符
        translated_query = re.sub(placeholder_pattern, replace_placeholder, query_string)

        logger.debug(
            "查询字符串转换成功",
            operation="translate_query_for_dataframe",
            original_query=query_string,
            translated_query=translated_query,
            placeholder_count=len(re.findall(placeholder_pattern, query_string))
        )

        return translated_query

    except Exception as e:
        # 重新抛出为更具体的错误类型
        if isinstance(e, WpCurveMetadataError):
            raise
        else:
            raise WpCurveMetadataError(
                f"查询字符串转换失败: {str(e)}",
                context=ErrorContext(
                    operation="translate_query_for_dataframe",
                    additional_info={
                        "original_query": query_string,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    }
                )
            ) from e


def translate_1d_curve(
    curve_name: str,
    curves_dict: dict[str, 'CurveBasicAttributes']
) -> str:
    """转换一维曲线名称为DataFrame友好列名。

    Args:
        curve_name: 一维曲线名称
        curves_dict: 曲线字典，用于查找曲线信息

    Returns:
        str: DataFrame友好列名

    Raises:
        WpCurveMetadataError: 当曲线不存在或是二维组合曲线基础名称时
    """
    if curve_name not in curves_dict:
        raise WpCurveMetadataError(
            f"查询中的曲线 '{curve_name}' 不存在",
            context=ErrorContext(
                operation="translate_1d_curve",
                additional_info={
                    "curve_name": curve_name,
                    "available_curves": list(curves_dict.keys())
                }
            )
        )

    curve_attrs = curves_dict[curve_name]

    # 检查是否误用了二维组合曲线基础名称
    if curve_attrs.is_2d_composite_curve():
        raise WpCurveMetadataError(
            f"不允许在查询中使用二维组合曲线基础名称 '{curve_name}'。"
            f"请使用元素形式，如 '{curve_name}[1]'、'{curve_name}[2]' 等",
            context=ErrorContext(
                operation="translate_1d_curve",
                additional_info={
                    "curve_name": curve_name,
                    "curve_dimension": curve_attrs.dimension.value,
                    "available_elements": curve_attrs.element_names or [],
                    "suggestion": f"使用 ${{{curve_name}[1]}} 替代 ${{{curve_name}}}"
                }
            )
        )

    return curve_attrs.dataframe_column_name


def translate_2d_curve_element(
    element_ref: str,
    curves_dict: dict[str, 'CurveBasicAttributes']
) -> str:
    """转换二维组合曲线元素引用为DataFrame友好列名。

    Args:
        element_ref: 二维组合曲线元素引用，格式为 "CURVE_NAME[index]"
        curves_dict: 曲线字典，用于查找曲线信息

    Returns:
        str: DataFrame友好列名

    Raises:
        WpCurveMetadataError: 当曲线不存在、不是二维组合曲线或元素索引无效时
    """
    try:
        # 解析 CURVE_NAME[index] 格式
        if not element_ref.endswith(']'):
            raise ValueError("格式错误：缺少闭合括号")

        bracket_pos = element_ref.rfind('[')
        if bracket_pos == -1:
            raise ValueError("格式错误：缺少开放括号")

        base_name = element_ref[:bracket_pos]
        index_str = element_ref[bracket_pos+1:-1]

        if not index_str.isdigit():
            raise ValueError(f"索引必须是数字，得到: '{index_str}'")

        element_index = int(index_str)

    except ValueError as e:
        raise WpCurveMetadataError(
            f"二维组合曲线元素引用格式错误 '{element_ref}': {str(e)}。"
            f"正确格式应为 'CURVE_NAME[数字]'，如 'T2_VALUE[1]'",
            context=ErrorContext(
                operation="translate_2d_curve_element",
                additional_info={
                    "element_ref": element_ref,
                    "parse_error": str(e),
                    "expected_format": "CURVE_NAME[index]"
                }
            )
        ) from e

    # 检查基础曲线是否存在
    if base_name not in curves_dict:
        raise WpCurveMetadataError(
            f"查询中的二维组合曲线 '{base_name}' 不存在",
            context=ErrorContext(
                operation="translate_2d_curve_element",
                additional_info={
                    "base_name": base_name,
                    "element_ref": element_ref,
                    "available_curves": list(curves_dict.keys())
                }
            )
        )

    curve_attrs = curves_dict[base_name]

    # 检查是否是二维组合曲线
    if not curve_attrs.is_2d_composite_curve():
        raise WpCurveMetadataError(
            f"曲线 '{base_name}' 不是二维组合曲线，不能使用索引格式 '{element_ref}'。"
            f"请使用 ${{{base_name}}} 格式",
            context=ErrorContext(
                operation="translate_2d_curve_element",
                additional_info={
                    "base_name": base_name,
                    "element_ref": element_ref,
                    "curve_dimension": curve_attrs.dimension.value,
                    "suggestion": f"使用 ${{{base_name}}} 替代 ${{{element_ref}}}"
                }
            )
        )

    # 检查元素索引是否有效
    if not curve_attrs.element_names or element_index < 1 or element_index > len(curve_attrs.element_names):
        max_index = len(curve_attrs.element_names) if curve_attrs.element_names else 0
        raise WpCurveMetadataError(
            f"二维组合曲线 '{base_name}' 的元素索引 {element_index} 无效。"
            f"有效索引范围: 1 到 {max_index}",
            context=ErrorContext(
                operation="translate_2d_curve_element",
                additional_info={
                    "base_name": base_name,
                    "element_ref": element_ref,
                    "requested_index": element_index,
                    "valid_range": f"1-{max_index}",
                    "available_elements": curve_attrs.element_names or []
                }
            )
        )

    # 获取对应的友好名称
    element_name = curve_attrs.element_names[element_index - 1]  # 转换为0基索引
    friendly_name = curve_attrs.dataframe_element_names[element_index - 1]

    logger.debug(
        "二维组合曲线元素转换成功",
        operation="translate_2d_curve_element",
        element_ref=element_ref,
        base_name=base_name,
        element_index=element_index,
        element_name=element_name,
        friendly_name=friendly_name
    )

    return friendly_name
