"""
Plotly三元图绘制示例脚本
实现Macro、<PERSON><PERSON>、Micro孔隙度三元图，使用渗透率作为颜色映射

注意: 本脚本基于 Plotly 6.0.0 及以上版本。
绘图语法、属性、函数等均遵循 Plotly 6.0+ 的风格和标准。
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
from typing import Optional, Tuple, Dict, Any


def load_data_from_csv(filepath: str = 'nmr_porosity_partioning_data.csv') -> pd.DataFrame:
    """从CSV文件加载数据并进行预处理。

    Args:
        filepath: CSV文件路径。

    Returns:
        包含孔隙度组分和对数渗透率的DataFrame。
    """
    try:
        data = pd.read_csv(filepath)
    except FileNotFoundError:
        print(f"错误: 未找到数据文件 '{filepath}'。请确保文件存在于脚本所在目录。")
        # 返回一个空DataFrame或退出
        return pd.DataFrame()

    # 对渗透率K_LABEL取对数，并处理小于等于0的值以避免数学错误
    if 'K_LABEL' in data.columns:
        epsilon = 1e-6  # 一个小的正数，防止log(0)
        data['log10_K_LABEL'] = np.log10(np.maximum(data['K_LABEL'], epsilon))
    else:
        raise ValueError(f"列 'K_LABEL' 在文件 '{filepath}' 中未找到。")

    return data


def create_ternary_plot(
    data: pd.DataFrame,
    macro_col: str = 'Macro',
    meso_col: str = 'Meso',
    micro_col: str = 'Micro',
    color_col: str = 'Perm',
    title: str = "Macro, Meso, Micro Porosity Ternary Plot",
    well_name: str = "1-SPS-108_CURACAO-1",
    colorscale: str = 'Viridis',
    colorbar_title: Optional[str] = None,
    cmin: Optional[float] = None,
    cmax: Optional[float] = None,
    size: int = 8,
    width: int = 800,
    height: int = 700,
) -> go.Figure:
    """创建三元图

    Args:
        data: 包含三元组分和颜色数据的DataFrame
        macro_col: Macro孔隙度列名
        meso_col: Meso孔隙度列名
        micro_col: Micro孔隙度列名
        color_col: 颜色映射列名
        title: 图表标题
        well_name: 井名
        colorscale: 颜色映射方案
        colorbar_title: 颜色条标题
        cmin: 颜色条最小值
        cmax: 颜色条最大值
        size: 数据点大小
        width: 图表宽度
        height: 图表高度

    Returns:
        plotly Figure对象
    """

    # 检查必须的列是否存在
    for col in [macro_col, meso_col, micro_col, color_col]:
        if col not in data.columns:
            raise ValueError(f"Column '{col}' not found in the data.")

    if colorbar_title is None:
        colorbar_title = color_col

    # 1. 将数据根据颜色列（K_LABEL）是否存在有效值进行拆分
    data_with_color = data[data[color_col].notna()].copy()
    data_without_color = data[data[color_col].isna()].copy()

    fig = go.Figure()

    # 2. 首先绘制没有颜色值的点（灰色），这样它们会位于图层底部
    if not data_without_color.empty:
        fig.add_trace(go.Scatterternary({
            'mode': 'markers',
            'a': data_without_color[macro_col],
            'b': data_without_color[micro_col],
            'c': data_without_color[meso_col],
            'marker': {
                'color': 'lightgray',
                'size': size - 2,  # 使其比有数据的点稍小
                'symbol': 'x'      # 使用不同的标记符号
            },
            'name': '无渗透率数据',
            'hovertemplate':
                f'<b>{macro_col}</b>: %{{a:.1f}}%<br>' +
                f'<b>{micro_col}</b>: %{{b:.1f}}%<br>' +
                f'<b>{meso_col}</b>: %{{c:.1f}}%<br>' +
                '渗透率: N/A<br>' +
                '<extra></extra>'
        }))

    # 3. 接着绘制有颜色值的点
    if not data_with_color.empty:
        fig.add_trace(go.Scatterternary({
            'mode': 'markers',
            'a': data_with_color[macro_col],
            'b': data_with_color[micro_col],
            'c': data_with_color[meso_col],
            'marker': {
                'color': data_with_color[color_col],
                'colorscale': colorscale,
                'size': size,
                'colorbar': {
                    'title': {
                        'text': colorbar_title,
                        'side': 'right'
                    },
                    'thickness': 15,
                    'len': 0.7
                },
                'line': {'width': 0.5, 'color': 'white'},
                'cmin': cmin,
                'cmax': cmax
            },
            'name': '有渗透率数据',
            'text': [f'{color_col}: {val:.2f}' for val in data_with_color[color_col]],
            'hovertemplate':
                f'<b>{macro_col}</b>: %{{a:.1f}}%<br>' +
                f'<b>{micro_col}</b>: %{{b:.1f}}%<br>' +
                f'<b>{meso_col}</b>: %{{c:.1f}}%<br>' +
                f'<b>{color_col}</b>: %{{marker.color:.2f}}<br>' +
                '<extra></extra>'
        }))

    # 定义各组分的颜色方案
    macro_color = 'red'
    micro_color = 'blue'
    meso_color = 'green'

    fig.update_layout({
        'title': {
            'text': f'{title}<br><sub>Well: {well_name}</sub>',
            'x': 0.5,
            'font': {'size': 16}
        },
        'ternary': {
            'sum': 100,
            'aaxis': {
                'title': {'text': '<b>Macro</b>', 'font': {'color': macro_color}},  # 顶端
                'min': 0,
                'linewidth': 2,
                'linecolor': macro_color,
                'ticks': 'outside',
                'tickfont': {'color': macro_color},
                'gridcolor': macro_color,
                'gridwidth': 0.5,  # 设置等值线线宽
                'griddash': 'dot', # 设置等值线为点线
                'dtick': 10,  # 设置等值线间隔
            },
            'baxis': {
                'title': {'text': '<b>Micro</b>', 'font': {'color': micro_color}},  # 左下角
                'min': 0,
                'linewidth': 2,
                'linecolor': micro_color,
                'ticks': 'outside',
                'tickfont': {'color': micro_color},
                'gridcolor': micro_color,
                'gridwidth': 0.5,  # 设置等值线线宽
                'griddash': 'dot', # 设置等值线为点线
                'dtick': 10,  # 设置等值线间隔
            },
            'caxis': {
                'title': {'text': '<b>Meso</b>', 'font': {'color': meso_color}},   # 右下角
                'min': 0,
                'linewidth': 2,
                'linecolor': meso_color,
                'ticks': 'outside',
                'tickfont': {'color': meso_color},
                'gridcolor': meso_color,
                'gridwidth': 0.5,  # 设置等值线线宽
                'griddash': 'dot', # 设置等值线为点线
                'dtick': 10,  # 设置等值线间隔
            }
        },
        'width': width,
        'height': height,
        'font': {'family': 'Arial', 'size': 12},
        'showlegend': True  # 显示图例以区分两类数据
    })

    return fig


def _hex_to_rgba(hex_color: str, opacity: float) -> str:
    """将HEX颜色代码转换为带透明度的RGBA字符串。"""
    hex_color = hex_color.lstrip('#')
    if len(hex_color) != 6:
        raise ValueError(f"无效的HEX颜色代码: {hex_color}")
    r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    return f'rgba({r}, {g}, {b}, {opacity})'


def add_polygon_regions(
    fig: go.Figure,
    color_scheme: str = 'Geolog',
    opacity: float = 0.5
) -> go.Figure:
    """添加多边形区域到三元图

    Args:
        fig: plotly Figure对象
        color_scheme: 颜色方案 ('Geolog' 或 'CNLC')
        opacity: 填充区域的透明度 (0 to 1)

    Returns:
        更新后的Figure对象
    """
    color_schemes = {
        'Geolog': {
            'Macro': '#ffff00', 'Macro-Micro': '#cca344', 'Macro-Meso': '#ffaf0e',
            'Meso': '#ff6b15', 'Meso-Micro': '#e84400', 'Micro': '#56004f',
            'Micro-Meso': '#a00068', 'Micro-Macro': '#c6c600'
        },
        'CNLC': {
            'Macro': '#ff0000', 'Macro-Micro': '#c00000', 'Macro-Meso': '#ffff00',
            'Meso': '#00ff00', 'Meso-Micro': '#92d050', 'Micro': '#3365ff',
            'Micro-Meso': '#00b0f0', 'Micro-Macro': '#ff00ff'
        }
    }

    # a=Macro, b=Micro, c=Meso
    regions_def = {
        'Macro': [(100, 0, 0), (60, 0, 40), (60, 40, 0)],
        'Macro-Micro': [(60, 40, 0), (60, 25, 15), (25, 25, 50), (25, 50, 25), (50, 50, 0)],
        'Macro-Meso': [(60, 25, 15), (60, 0, 40), (25, 0, 75), (25, 25, 50)],
        # 注意: 原始坐标(25,25,70)之和为120, 已修正为(25,25,50)以确保闭合
        'Meso': [(25, 25, 50), (25, 0, 75), (0, 0, 100), (0, 25, 75)],
        'Meso-Micro': [(25, 50, 25), (25, 25, 50), (0, 25, 75), (0, 50, 50)],
        'Micro': [(0, 100, 0), (25, 75, 0), (0, 75, 25)],
        'Micro-Meso': [(25, 75, 0), (25, 50, 25), (0, 50, 50), (0, 75, 25)],
        'Micro-Macro': [(50, 50, 0), (25, 50, 25), (25, 75, 0)]
    }

    colors = color_schemes.get(color_scheme)
    if not colors:
        raise ValueError(f"未知的颜色方案: '{color_scheme}'. 可用方案: {list(color_schemes.keys())}")

    # 存储已有的数据点轨迹
    existing_traces = fig.data
    region_traces = []

    for name, coords in regions_def.items():
        # 闭合多边形路径
        closed_coords = coords + [coords[0]]

        # 将坐标元组列表解压为a, b, c三个列表
        a_coords = [p[0] for p in closed_coords]
        b_coords = [p[1] for p in closed_coords]
        c_coords = [p[2] for p in closed_coords]

        fill_color = _hex_to_rgba(colors[name], opacity)

        region_traces.append(go.Scatterternary({
            'mode': 'lines',
            'a': a_coords,
            'b': b_coords,
            'c': c_coords,
            'fill': 'toself',
            'fillcolor': fill_color,
            'line': {'width': 0}, # 移除边框线
            'name': name,
            'showlegend': True,
            'hoverinfo': 'name',
            'legendgroup': 'regions' # 将所有分区归为一组
        }))

    # 将分区轨迹放在数据点轨迹之前（作为背景）。
    # Plotly不允许直接向fig.data赋值来添加新轨迹，
    # 正确的方法是创建一个包含所有轨迹（新+旧）的新Figure对象。
    fig = go.Figure(data=tuple(region_traces) + existing_traces, layout=fig.layout)

    # 更新图例，使其更清晰
    fig.update_layout(
        legend_title_text='图例',
        legend_tracegroupgap=20,
    )

    return fig


def save_ternary_plot(
    fig: go.Figure,
    filename: str,
    formats: Tuple[str, ...] = ('html', 'png', 'svg', 'pdf'),
    width: Optional[int] = None,
    height: Optional[int] = None,
    **html_kwargs: Any
) -> None:
    """保存三元图到文件

    Args:
        fig: plotly Figure对象
        filename: 文件名（不含扩展名）
        formats: 保存格式元组
        width: 图像宽度 (仅用于 'png', 'pdf', 'svg')
        height: 图像高度 (仅用于 'png', 'pdf', 'svg')
        **html_kwargs: 传递给 write_html 的额外参数
    """
    # 为图像导出准备参数
    image_kwargs = {}
    if width:
        image_kwargs['width'] = width
    if height:
        image_kwargs['height'] = height

    for fmt in formats:
        if fmt == 'html':
            fig.write_html(f"./{filename}.html", **html_kwargs)
            print(f"已保存HTML文件: {filename}.html")
        elif fmt in ('png', 'pdf', 'svg'):
            output_path = f"./{filename}.{fmt}"
            fig.write_image(output_path, **image_kwargs)
            print(f"已保存 {fmt.upper()} 文件: {output_path}")


def main():
    """主函数：演示三元图绘制"""

    # 从CSV文件加载数据
    print("从 'nmr_porosity_partioning_data.csv' 加载数据...")
    data = load_data_from_csv('nmr_porosity_partioning_data.csv')

    if data.empty:
        print("数据加载失败，程序退出。")
        return

    # 创建基础三元图
    print("创建三元图...")
    fig = create_ternary_plot(
        data=data,
        macro_col='VMACRO',
        meso_col='VMESO',
        micro_col='VMICRO',
        color_col='log10_K_LABEL',
        title="NMR Porosity Partitioning Ternary Plot",
        well_name="From CSV Data",
        colorscale='Viridis',
        colorbar_title='log10(K_LABEL) (mD)',
        cmin=-2,
        cmax=3
    )

    # 添加背景分区，使用 'Geolog' 颜色方案
    print("添加背景分区 (使用 'Geolog' 颜色方案)...")
    fig = add_polygon_regions(fig, color_scheme='Geolog', opacity=0.5)

    # 显示图表
    print("显示图表...")
    fig.show()

    # 保存图表
    print("保存图表...")
    save_ternary_plot(
        fig,
        filename="ternary_plot_example",
        formats=('html', 'png'), # 可以调整为 ('html', 'png', 'svg', 'pdf')
        width=800,
        height=700
    )

    print("三元图绘制完成！")


if __name__ == "__main__":
    # 运行主函数，该函数会使用 add_polygon_regions 中的默认分区。
    # main_with_regions() 函数是另一个示例，用于展示如何添加一个自定义的小区域，
    # 如果需要运行它，请取消注释下一行并注释掉 main()。
    main()

#fixme: use better and descriptive names
#fixme: improve the plot so it is easier to see
#fixme: make use of subplots
