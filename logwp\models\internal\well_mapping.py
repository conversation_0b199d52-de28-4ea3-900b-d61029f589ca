from __future__ import annotations

"""logwp.models.service.well_mapping - 井名映射服务层

井名映射功能的服务层实现，遵循SAD文档的内部服务层设计模式。
WpWellProject通过转发调用使用此服务。

Architecture
------------
层次/依赖: models/service层，依赖models、datasets、exceptions
设计原则: 无状态服务、参数验证、错误处理、日志记录
性能特征: 向量化操作、批量处理、内存优化

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- LG-2: 结构化日志信息
- EH-1: Exception Groups异常处理

References
----------
- 《SCAPE_DDS_logwp_井名映射.md》- 井名映射详细设计
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
"""

from typing import TYPE_CHECKING, Any
from datetime import datetime
import pandas as pd

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from logwp.models.well_project import WpWellProject
from logwp.models.mapping import WpWellMap
from logwp.models.exceptions import WpDatasetNotFoundError, WpConsistencyError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

logger = get_logger(__name__)


def apply_well_mapping(
    project: 'WpWellProject',
    dataset_names: str | list[str] | None = None,
    *,
    create_new: bool = False,
    new_dataset_names: str | list[str] | None = None
) -> dict[str, str] | None:
    """统一的井名映射服务函数。

    接收WpWellProject对象和用户参数，完成所有参数处理、验证和井名映射执行。
    WpWellProject只需简单转发调用，所有复杂逻辑在服务层处理。

    Args:
        project: WpWellProject项目对象
        dataset_names: 要映射的数据集名称
            - None: 对所有数据集进行映射
            - str: 对单个数据集进行映射
            - list[str]: 对指定的多个数据集进行映射
        create_new: 是否创建新数据集
            - False: 就地修改原数据集
            - True: 克隆新数据集并映射
        new_dataset_names: 新数据集名称（仅在create_new=True时有效）
            - None: 自动生成唯一名称
            - str: 单个新数据集名称
            - list[str]: 多个新数据集名称（必须与dataset_names一一对应）

    Returns:
        dict[str, str] | None:
            - create_new=False: 返回None
            - create_new=True: 返回{原数据集名称: 新数据集名称}映射字典

    Raises:
        WpDatasetNotFoundError: 指定的数据集不存在
        WpConsistencyError: 新数据集名称与现有数据集冲突
        ValueError: 参数数量不匹配或参数无效

    Note:
        - 包含完整的参数处理、验证和执行逻辑
        - 自动处理数据集名称标准化和新名称生成
        - 统一的井名映射核心逻辑，支持两种操作模式
    """

    logger.info("开始井名映射", extra={
        "operation": "apply_well_mapping",
        "create_new": create_new,
        "project_name": str(project.name)
    })

    # 边界情况检查
    if not project.datasets:
        logger.warning("项目中没有数据集，跳过井名映射", extra={
            "operation": "apply_well_mapping",
            "project_name": str(project.name)
        })
        return {} if create_new else None

    # 检查井名映射是否为空
    if not project.well_map.mappings:
        logger.warning("井名映射为空，跳过映射操作", extra={
            "operation": "apply_well_mapping",
            "project_name": str(project.name)
        })
        return {} if create_new else None

    # 1. 数据集名称标准化
    target_datasets = _normalize_dataset_names(project.datasets, dataset_names)

    # 检查是否有有效的目标数据集
    if not target_datasets:
        logger.warning("没有有效的目标数据集，跳过井名映射", extra={
            "operation": "apply_well_mapping",
            "dataset_names": dataset_names
        })
        return {} if create_new else None

    # 2. 新数据集名称处理（仅当create_new=True时）
    final_new_names = None
    if create_new:
        final_new_names = _process_new_dataset_names(
            project, target_datasets, new_dataset_names
        )

    # 3. 执行井名映射
    cloned_datasets = _execute_well_mapping(
        project.datasets,
        project.well_map,
        target_datasets,
        create_new=create_new,
        new_dataset_names=final_new_names
    )

    # 4. 处理返回结果
    if create_new:
        # 将新数据集添加到项目中
        for new_name, cloned_dataset in cloned_datasets.items():
            project.add_dataset(new_name, cloned_dataset)

        logger.info("井名映射完成（克隆模式）", extra={
            "operation": "apply_well_mapping",
            "processed_datasets": len(target_datasets),
            "new_datasets": list(cloned_datasets.keys())
        })

        # 返回原名称到新名称的映射
        return dict(zip(target_datasets, final_new_names))
    else:
        logger.info("井名映射完成（就地模式）", extra={
            "operation": "apply_well_mapping",
            "processed_datasets": len(target_datasets)
        })
        return None


def _normalize_dataset_names(
    datasets: dict[str, WpDepthIndexedDatasetBase],
    dataset_names: str | list[str] | None
) -> list[str]:
    """标准化数据集名称参数。

    Args:
        datasets: 项目的数据集字典
        dataset_names: 用户输入的数据集名称

    Returns:
        list[str]: 标准化后的数据集名称列表
    """
    if dataset_names is None:
        # 对所有数据集进行映射
        return list(datasets.keys())
    elif isinstance(dataset_names, str):
        # 单个数据集
        return [dataset_names]
    else:
        # 多个数据集
        return list(dataset_names)


def _process_new_dataset_names(
    project: 'WpWellProject',
    target_datasets: list[str],
    new_dataset_names: str | list[str] | None
) -> list[str]:
    """处理新数据集名称参数。

    Args:
        project: WpWellProject项目对象
        target_datasets: 目标数据集名称列表
        new_dataset_names: 用户输入的新数据集名称

    Returns:
        list[str]: 处理后的新数据集名称列表

    Raises:
        ValueError: 参数数量不匹配
        WpConsistencyError: 数据集名称冲突
    """
    if new_dataset_names is None:
        # 自动生成唯一名称
        return [
            project.generate_unique_dataset_name(name)
            for name in target_datasets
        ]

    # 用户提供了新名称
    if isinstance(new_dataset_names, str):
        new_dataset_names = [new_dataset_names]

    # 数量一致性检查
    if len(new_dataset_names) != len(target_datasets):
        raise ValueError(
            f"新数据集名称数量({len(new_dataset_names)}) "
            f"必须与目标数据集数量({len(target_datasets)})一致"
        )

    # 重名冲突检查
    for name in new_dataset_names:
        if project.has_dataset(name):
            ctx = ErrorContext(
                operation="apply_well_mapping",
                dataset_name=name,
                additional_info={
                    "conflict_type": "new_dataset_name_exists",
                    "target_datasets": target_datasets
                }
            )
            raise WpConsistencyError(f"数据集名称已存在: {name}", context=ctx)

    return list(new_dataset_names)


def _execute_well_mapping(
    datasets: dict[str, WpDepthIndexedDatasetBase],
    well_map: WpWellMap,
    target_dataset_names: list[str],
    *,
    create_new: bool = False,
    new_dataset_names: list[str] | None = None
) -> dict[str, WpDepthIndexedDatasetBase] | None:
    """执行井名映射的核心逻辑。

    Args:
        datasets: 项目的数据集字典
        well_map: 井名映射管理器
        target_dataset_names: 目标数据集名称列表
        create_new: 是否创建新数据集
        new_dataset_names: 新数据集名称列表

    Returns:
        dict[str, WpDepthIndexedDatabaseBase] | None: 克隆的数据集字典或None
    """

    # 验证数据集存在性
    for dataset_name in target_dataset_names:
        if dataset_name not in datasets:
            ctx = ErrorContext(
                operation="apply_well_mapping",
                dataset_name=dataset_name,
                additional_info={
                    "available_datasets": list(datasets.keys()),
                    "target_datasets": target_dataset_names
                }
            )
            raise WpDatasetNotFoundError(
                f"数据集不存在: {dataset_name}",
                context=ctx
            )

    if create_new:
        # 克隆模式：先克隆，再映射
        result = {}
        for i, original_name in enumerate(target_dataset_names):
            original_dataset = datasets[original_name]

            # 克隆数据集
            cloned_dataset = original_dataset.clone_dataset()

            # 应用井名映射
            _apply_mapping_to_dataset(cloned_dataset, well_map)

            # 使用新名称
            new_name = new_dataset_names[i]
            result[new_name] = cloned_dataset

        return result
    else:
        # 就地模式：直接修改原数据集
        for dataset_name in target_dataset_names:
            dataset = datasets[dataset_name]
            _apply_mapping_to_dataset(dataset, well_map)

        return None


def _apply_mapping_to_dataset(dataset: WpDepthIndexedDatasetBase, well_map: WpWellMap) -> None:
    """对单个数据集应用井名映射。

    通过曲线元数据获取井名曲线列表，对每个井名曲线应用映射。
    遵循logwp架构设计原则，使用曲线元数据系统而不是硬编码列名。

    Args:
        dataset: 目标数据集
        well_map: 井名映射管理器

    Note:
        - 通过curve_metadata.get_well_identifier_curves()获取井名曲线
        - 使用dataframe_column_name获取DataFrame友好的列名
        - 映射后自动更新数据集的modified_at时间戳
        - 使用pandas.map()进行高效的向量化映射
    """

    try:
        # 通过曲线元数据获取井名曲线列表（正确的方式）
        well_curves = dataset.curve_metadata.get_well_identifier_curves()

        if not well_curves:
            logger.warning("数据集没有井名曲线，跳过映射", extra={
                "dataset_name": str(dataset.name),
                "available_curves": list(dataset.curve_metadata.curves.keys())
            })
            return

        # 处理每个井名曲线
        for well_curve_name in well_curves:
            try:
                # 获取井名曲线的DataFrame列名
                well_curve = dataset.curve_metadata.get_curve(well_curve_name)
                if well_curve is None:
                    logger.warning("井名曲线元数据不存在", extra={
                        "dataset_name": str(dataset.name),
                        "well_curve_name": well_curve_name
                    })
                    continue

                well_column_name = well_curve.dataframe_column_name

                # 检查DataFrame中是否存在该列
                if well_column_name not in dataset.df.columns:
                    logger.error("井名列在DataFrame中不存在", extra={
                        "dataset_name": str(dataset.name),
                        "well_curve_name": well_curve_name,
                        "expected_column": well_column_name,
                        "available_columns": list(dataset.df.columns)
                    })
                    continue

                # 检查井名列是否为空
                if dataset.df[well_column_name].isna().all():
                    logger.warning("井名列全部为空值，跳过映射", extra={
                        "dataset_name": str(dataset.name),
                        "well_column_name": well_column_name,
                        "well_column_type": str(dataset.df[well_column_name].dtype)
                    })
                    continue

                # 记录映射前的井名统计
                original_wells = dataset.df[well_column_name].dropna().unique()
                if len(original_wells) == 0:
                    logger.warning("没有有效的井名数据，跳过映射", extra={
                        "dataset_name": str(dataset.name),
                        "well_column_name": well_column_name
                    })
                    continue

                logger.debug("开始井名映射", extra={
                    "dataset_name": str(dataset.name),
                    "well_curve_name": well_curve_name,
                    "well_column_name": well_column_name,
                    "original_wells": list(original_wells),
                    "well_count": len(original_wells)
                })

                # 应用井名映射（向量化操作）
                def map_well_name(well_name: Any) -> Any:
                    # 处理空值
                    if pd.isna(well_name):
                        return well_name

                    # 转换为字符串进行映射
                    well_str = str(well_name)
                    mapped_name = well_map.get_mapped_name(well_str)
                    return mapped_name

                dataset.df[well_column_name] = dataset.df[well_column_name].map(map_well_name)

                # 记录映射后的井名统计
                mapped_wells = dataset.df[well_column_name].dropna().unique()
                mapping_count = sum(
                    1 for well in original_wells
                    if well_map.get_mapped_name(str(well)) != str(well)
                )

                logger.debug("井名映射完成", extra={
                    "dataset_name": str(dataset.name),
                    "well_curve_name": well_curve_name,
                    "well_column_name": well_column_name,
                    "mapped_wells": list(mapped_wells),
                    "mapping_count": mapping_count,
                    "total_wells": len(original_wells)
                })

            except Exception as curve_error:
                # 单个井名曲线处理失败，记录错误但继续处理其他曲线
                logger.error("单个井名曲线映射失败", extra={
                    "dataset_name": str(dataset.name),
                    "well_curve_name": well_curve_name,
                    "error_type": type(curve_error).__name__,
                    "error_message": str(curve_error)
                })
                continue

        # 更新时间戳
        dataset.modified_at = datetime.now()

    except Exception as e:
        # 整个数据集处理失败，记录错误但不中断整个流程
        logger.error("数据集井名映射失败", extra={
            "dataset_name": str(dataset.name),
            "error_type": type(e).__name__,
            "error_message": str(e)
        })
        # 可以选择重新抛出异常或继续处理其他数据集
        # 这里选择继续处理，但记录错误
        pass
