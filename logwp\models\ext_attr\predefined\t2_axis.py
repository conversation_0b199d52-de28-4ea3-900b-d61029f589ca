from __future__ import annotations

"""logwp.attr.predefined.t2_axis - T2_Axis工厂类

T2_Axis属性的工厂类，负责JSON验证和领域对象创建。

Architecture
------------
层次/依赖: attr层工厂类，依赖领域对象、JSON处理器
设计原则: 工厂模式、JSON验证、领域对象创建
性能特征: 高效验证、类型安全、错误诊断

职责：
- 验证T2_AXIS的JSON结构
- 根据轴类型创建对应的领域对象
- 提供统一的工厂接口

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-1: 使用结构化异常处理

References
----------
- 《SCAPE_MS_方法说明书.md》§1.3 - T2谱相关参数
- 《SCAPE_WFS_WP文件规范.md》§5.4.3 - T2_AXIS属性规范
"""

from typing import Any
from dataclasses import dataclass

from ..comp_processor import StandardCompProcessor, JsonValidationResult
from .t2_axis_domain import T2AxisLog10, T2AxisExp2


@dataclass(frozen=True)
class T2AxisProcessor:
    """T2_Axis工厂类。

    负责T2_AXIS JSON数据的验证和领域对象创建。

    Architecture
    ------------
    层次/依赖: attr层工厂类，依赖JSON处理器和领域对象
    设计原则: 工厂模式、JSON验证、类型安全
    性能特征: 高效验证、错误诊断、对象创建

    职责：
    - 验证T2_AXIS的JSON结构有效性
    - 根据轴类型创建对应的领域对象
    - 提供统一的工厂接口

    Examples
    --------
    >>> # 创建T2轴工厂
    >>> processor = T2AxisProcessor()
    >>>
    >>> # 验证JSON数据
    >>> json_data = {"Axis_Type": "log10", "T2_Start": {...}, ...}
    >>> result = processor.validate_json(json_data)
    >>> assert result.is_valid
    >>>
    >>> # 创建领域对象
    >>> t2_axis = processor.create_domain_object(json_data)
    >>> assert isinstance(t2_axis, T2AxisLog10)

    References
    ----------
    《SCAPE_WFS_WP文件规范.md》§5.4.3 - T2_AXIS属性规范
    """

    json_processor: StandardCompProcessor = StandardCompProcessor()
    """JSON处理器实例"""

    def validate_json(self, json_data: dict[str, Any]) -> JsonValidationResult:
        """验证T2_AXIS JSON数据的有效性。

        Args:
            json_data: T2_AXIS的JSON数据

        Returns:
            ValidationResult: 验证结果

        Examples:
            >>> processor = T2AxisProcessor()
            >>> json_data = {"Axis_Type": "log10", "T2_Start": {...}, ...}
            >>> result = processor.validate_json(json_data)
            >>> assert result["is_valid"] is True
        """
        errors = []
        warnings = []

        # 基础JSON结构验证
        json_validation = self.json_processor.validate_json_structure(json_data)
        if not json_validation.is_valid:
            errors.extend(json_validation.errors)
            return {
                "is_valid": False,
                "errors": errors,
                "warnings": warnings
            }

        # 验证轴类型
        axis_type = json_data.get("Axis_Type")
        if axis_type not in {"log10", "exp2"}:
            errors.append(f"无效的轴类型: {axis_type}，支持的类型: log10, exp2")

        # 验证必需字段（根据轴类型）
        required_fields = ["T2_Start", "N"]
        if axis_type == "log10":
            required_fields.append("T2_End")
        elif axis_type == "exp2":
            required_fields.append("T2_Step")

        for field in required_fields:
            if field not in json_data:
                errors.append(f"缺少必需字段: {field}")

        # 验证字段格式（WFS规范的COMP格式）
        for field in ["T2_Start", "T2_End", "T2_Step"]:
            if field in json_data:
                field_data = json_data[field]
                if not isinstance(field_data, dict) or "v" not in field_data:
                    errors.append(f"字段 {field} 格式无效，必须包含 'v' 字段")

        if "N" in json_data:
            n_data = json_data["N"]
            if not isinstance(n_data, dict) or "v" not in n_data:
                errors.append("字段 N 格式无效，必须包含 'v' 字段")

        return JsonValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            details={
                "axis_type": axis_type,
                "field_count": len(json_data)
            }
        )

    def create_domain_object(self, json_data: dict[str, Any]) -> T2AxisLog10 | T2AxisExp2:
        """根据JSON数据创建对应的T2轴领域对象。

        Args:
            json_data: T2_AXIS的JSON数据

        Returns:
            T2AxisLog10 | T2AxisExp2: 对应的领域对象

        Raises:
            ValueError: JSON数据无效或创建失败

        Examples:
            >>> processor = T2AxisProcessor()
            >>> json_data = {"Axis_Type": "log10", "T2_Start": {...}, ...}
            >>> t2_axis = processor.create_domain_object(json_data)
            >>> assert isinstance(t2_axis, T2AxisLog10)
        """
        # 验证JSON数据
        validation = self.validate_json(json_data)
        if not validation["is_valid"]:
            raise ValueError(f"T2轴JSON数据无效: {validation['errors']}")

        # 根据轴类型创建对应的领域对象
        axis_type = json_data.get("Axis_Type")
        if axis_type == "log10":
            return T2AxisLog10.from_json(json_data)
        elif axis_type == "exp2":
            return T2AxisExp2.from_json(json_data)
        else:
            raise ValueError(f"不支持的轴类型: {axis_type}")
