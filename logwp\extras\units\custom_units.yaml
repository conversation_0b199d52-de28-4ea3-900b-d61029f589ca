# 自定义单位定义文件
# ------------------------------------
# 该文件用于扩展或覆盖 `logwp.extras.units` 包中的默认单位。
#
# 格式说明:
# - dimension: 必须是 registry 中已定义的量纲名称 (如 length, pressure, volume 等)。
# - scale:     换算到该量纲的国际单位制(SI)基准单位的乘数因子。
#              例如，压力的基准是 Pa，长度的基准是 m。
# - aliases:   单位的别名列表，方便在代码中以不同名称引用。

units:
  # --- 压力 (Pressure) ---
  - name: kilogram_force_per_square_centimeter
    symbol: kgf/cm²
    dimension: pressure
    scale: 98066.5  # 1 kgf/cm² = 98066.5 Pa
    aliases:
      - at # 工程大气压

  # --- 长度 (Length) ---
  - name: foot
    symbol: ft
    dimension: length
    scale: 0.3048  # 1 ft = 0.3048 m
    aliases: ['feet']

  - name: inch
    symbol: in
    dimension: length
    scale: 0.0254 # 1 in = 0.0254 m
    aliases: ['inches']

  # --- API 比重 (无量纲) ---
  - name: api_gravity
    symbol: API
    dimension: dimensionless # API度是无量纲的，其与密度的转换是非线性的，需要上下文处理
    scale: 1.0
