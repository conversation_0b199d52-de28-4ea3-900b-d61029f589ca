"""PLT盲井检验步骤的门面(Facade)模块。

此模块提供了 `run_plt_analysis_step` 函数，作为PLT盲井检验步骤
的唯一公共入口。它遵循《可追踪机器学习组件开发框架》的规范，负责
流程编排、与RunContext交互、以及调用内部计算逻辑。
"""

from __future__ import annotations

import pandas as pd
from typing import TYPE_CHECKING, Any, Callable, Dict, Optional

from logwp.infra import get_logger

from .artifact_handler import ValidationArtifactHandler
from .config import PltAnalysisConfig
from .constants import PltAnalysisArtifacts, PltPlotTypes
from .internal import plt_computer
from . import plotting

if TYPE_CHECKING:
    from pathlib import Path
    from logwp.extras.plotting import PlotProfile
    from logwp.extras.tracking import RunContext
    from logwp.models.datasets.bundle import WpDataFrameBundle

logger = get_logger(__name__)


def _process_plot_artifacts(
    ctx: "RunContext",
    handler: "ValidationArtifactHandler",
    step_dir: Path,
    well_name: str,
    profile: "PlotProfile",
    data_snapshot_df: pd.DataFrame,
    plot_artifact_prefix: "PltAnalysisArtifacts",
    data_artifact_prefix: "PltAnalysisArtifacts",
    profile_artifact_prefix: "PltAnalysisArtifacts",
    replot_function: Callable,
    metrics_source: Dict[str, Any],
):
    """一个用于保存和注册单个图表所有产物的辅助函数。"""

    # 保存并注册配置快照
    profile_artifact_name = f"{profile_artifact_prefix}{well_name}"
    profile_path = step_dir / f"{profile_artifact_name}.json"
    handler.save_plot_profile(profile, profile_path)
    ctx.register_artifact(profile_path.relative_to(ctx.run_dir), profile_artifact_name)

    # 保存并注册数据快照
    data_artifact_name = f"{data_artifact_prefix}{well_name}"
    data_path = step_dir / f"{data_artifact_name}.csv"
    handler.save_dataframe(data_snapshot_df, data_path)
    ctx.register_artifact(data_path.relative_to(ctx.run_dir), data_artifact_name)

    # 从快照复现绘图并注册图表产物
    plot_artifact_name = f"{plot_artifact_prefix}{well_name}"
    # 处理save_config为None的情况，使用默认扩展名
    if profile.save_config is not None:
        extension = profile.save_config.get_preferred_extension()
    else:
        extension = ".png"  # 默认使用PNG格式
    plot_path = step_dir / f"{plot_artifact_name}{extension}"
    replot_function(
        snapshot_path=data_path,
        plot_profile=profile,
        output_path=plot_path,
        metrics=metrics_source,
    )
    ctx.register_artifact(
        plot_path.relative_to(ctx.run_dir),
        plot_artifact_name,
        description=f"{well_name} 井的 {plot_artifact_prefix.rstrip('_')} 图表。",
    )


def run_plt_analysis_step(
    config: PltAnalysisConfig,
    ctx: RunContext,
    prediction_bundle: WpDataFrameBundle,
    plt_bundle: WpDataFrameBundle,
    *,
    prefix: str = "",
    permeability_curve: str,
    flow_rate_curve: str,
    plot_profiles: Dict[PltPlotTypes, PlotProfile],
) -> Dict[str, Any]:
    """执行PLT盲井检验步骤。

    此函数是PLT盲井检验的门面(facade)，负责编排整个分析流程，
    包括调用内部计算、记录产物和指标、以及生成图表。

    Args:
        config (PltAnalysisConfig): PLT分析步骤的配置对象。
        ctx (RunContext): 当前运行的上下文，用于追踪。
        prediction_bundle (WpDataFrameBundle): 包含模型预测渗透率的数据集。
        plt_bundle (WpDataFrameBundle): 包含PLT分层解释结果的数据集。
        prefix (str, optional): 产物输出目录和指标名称的前缀，用于在同一
            Workflow中多次运行时区分产物。默认为 ""。
        permeability_curve (str): 要使用的渗透率曲线名称。
        flow_rate_curve (str): 要使用的流量贡献曲线名称。
        plot_profiles (Dict[PltPlotTypes, PlotProfile]): 一个字典，将图表类型
            (PltPlotTypes枚举) 映射到要使用的PlotProfile对象。

    Returns:
        Dict[str, Any]: 一个包含分析摘要的轻量级字典。
    """
    base_step_name = "plt_analysis"
    step_name = f"{prefix}_{base_step_name}" if prefix else base_step_name

    logger.info("开始执行PLT盲井检验步骤...", step_name=step_name)
    step_dir = ctx.get_step_dir(step_name)
    handler = ValidationArtifactHandler()

    # 1. 找出共同井进行分析
    prediction_wells = set(prediction_bundle.safe_get_well_names())
    plt_wells = set(plt_bundle.safe_get_well_names())
    common_wells_to_analyze = sorted(list(prediction_wells & plt_wells))

    if not common_wells_to_analyze:
        logger.warning("预测数据包和PLT数据包中没有共同的井可供分析，步骤终止。")
        return {}

    logger.info(f"找到 {len(common_wells_to_analyze)} 口共同井进行分析: {common_wells_to_analyze}")

    # 2. 循环处理每口井
    summary_results = {}
    for well_name in common_wells_to_analyze:
        logger.info(f"正在处理井: {well_name}", well_name=well_name)

        # 2.1. 调用内部计算模块
        well_results = plt_computer.compute_all_metrics_for_well(
            well_name=well_name,
            prediction_bundle=prediction_bundle,
            permeability_curve=permeability_curve,
            plt_bundle=plt_bundle,
            flow_rate_curve=flow_rate_curve,
        )

        # 2.2. 记录数值型指标 (Metrics)
        # 使用井名作为后缀，确保每个井的指标在 manifest.json 中是唯一的
        step_name_with_well = f"{step_name}.{well_name}"
        ctx.log_metrics(well_results.get("correlations", {}), step_name=step_name_with_well)
        ctx.log_metrics(well_results.get("errors", {}), step_name=step_name_with_well)
        # 从曲线指标中排除坐标数据，只记录数值
        capture_metrics = {k: v for k, v in well_results.get("capture_curve", {}).items() if k != "coords"}
        lorenz_metrics = {k: v for k, v in well_results.get("lorenz_curve", {}).items() if k != "coords"}
        ctx.log_metrics(capture_metrics, step_name=step_name_with_well)
        ctx.log_metrics(lorenz_metrics, step_name=step_name_with_well)

        # 2.3. 保存并注册表格产物 (Artifact)
        analyzed_layers_df = well_results.get("analyzed_layers_df")
        if analyzed_layers_df is not None and not analyzed_layers_df.empty:
            table_artifact_name = f"{PltAnalysisArtifacts.ANALYZED_LAYERS_TABLE_PREFIX.value}{well_name}"
            table_path = step_dir / f"{table_artifact_name}.csv"
            handler.save_dataframe(analyzed_layers_df, table_path)
            ctx.register_artifact(
                artifact_path=table_path.relative_to(ctx.run_dir),
                artifact_name=table_artifact_name,
                description=f"PLT分析后 {well_name} 井的分层属性表。",
            )

        # 2.4. 处理所有绘图产物
        # --- 2.4.1. 贡献率交会图 ---
        profile = plot_profiles.get(PltPlotTypes.CONTRIBUTION_CROSSPLOT)
        if profile and analyzed_layers_df is not None and not analyzed_layers_df.empty:
            _process_plot_artifacts(
                ctx=ctx,
                handler=handler,
                step_dir=step_dir,
                well_name=well_name,
                profile=profile,
                data_snapshot_df=analyzed_layers_df[["r_pred", "r_plt"]],
                plot_artifact_prefix=PltAnalysisArtifacts.CONTRIBUTION_CROSSPLOT_PREFIX.value,
                data_artifact_prefix=PltAnalysisArtifacts.CONTRIBUTION_CROSSPLOT_DATA_PREFIX.value,
                profile_artifact_prefix=PltAnalysisArtifacts.CONTRIBUTION_CROSSPLOT_PROFILE_PREFIX.value,
                replot_function=plotting.replot_plt_contribution_crossplot_from_snapshot,
                metrics_source=well_results,
            )

        # --- 2.4.2. 捕获曲线图 ---
        profile = plot_profiles.get(PltPlotTypes.CAPTURE_CURVE)
        if profile:
            coords = well_results.get("capture_curve", {}).get("coords", ([], []))
            _process_plot_artifacts(
                ctx=ctx,
                handler=handler,
                step_dir=step_dir,
                well_name=well_name,
                profile=profile,
                data_snapshot_df=pd.DataFrame({"x": coords[0], "y": coords[1]}),
                plot_artifact_prefix=PltAnalysisArtifacts.CAPTURE_CURVE_PLOT_PREFIX.value,
                data_artifact_prefix=PltAnalysisArtifacts.CAPTURE_CURVE_PLOT_DATA_PREFIX.value,
                profile_artifact_prefix=PltAnalysisArtifacts.CAPTURE_CURVE_PLOT_PROFILE_PREFIX.value,
                replot_function=plotting.replot_plt_capture_curve_from_snapshot,
                metrics_source=well_results.get("capture_curve", {}),
            )

        # --- 2.4.3. 洛伦兹曲线图 ---
        profile = plot_profiles.get(PltPlotTypes.LORENZ_CURVE)
        if profile:
            coords = well_results.get("lorenz_curve", {}).get("coords", ([], []))
            _process_plot_artifacts(
                ctx=ctx,
                handler=handler,
                step_dir=step_dir,
                well_name=well_name,
                profile=profile,
                data_snapshot_df=pd.DataFrame({"x": coords[0], "y": coords[1]}),
                plot_artifact_prefix=PltAnalysisArtifacts.LORENZ_CURVE_PLOT_PREFIX.value,
                data_artifact_prefix=PltAnalysisArtifacts.LORENZ_CURVE_PLOT_DATA_PREFIX.value,
                profile_artifact_prefix=PltAnalysisArtifacts.LORENZ_CURVE_PLOT_PROFILE_PREFIX.value,
                replot_function=plotting.replot_plt_lorenz_curve_from_snapshot,
                metrics_source=well_results.get("lorenz_curve", {}),
            )

        # 2.5. 汇总关键结果
        summary_results[well_name] = {
            "spearman_rho": well_results.get("correlations", {}).get("spearman_rho"),
            "gini_capture": well_results.get("capture_curve", {}).get("gini"),
            "gini_lorenz": well_results.get("lorenz_curve", {}).get("gini"),
        }

    logger.info("PLT盲井检验步骤执行完毕。")
    return summary_results
