"""_Head_Info表单解析服务。

实现_Head_Info表单的解析功能，严格按照WFS v1.0规范。
支持流式处理，以优化大文件读取性能。

Architecture
------------
层次/依赖: I/O层内部服务，被wp_excel_reader调用
设计原则: WFS规范严格遵循、无状态函数、流式处理
性能特征: 内存优化、迭代器处理

Functions
---------
- parse_head_info_sheet: 从行迭代器解析井头信息

References
----------
- 《SCAPE_DDS_logwp_io层的渐进式生成流程.md》- 构造流程设计
- 《SCAPE_WFS_WP文件规范.md》- WFS v1.0规范
"""

from __future__ import annotations

from typing import Iterator, Any

import structlog

from logwp.models.head import WpHead
from logwp.io.exceptions import WpFileFormatError
from logwp.io.constants import WpXlsxKey
from logwp.models.ext_attr.manager import ExtAttributeRecord
from logwp.models.ext_attr.comp_processor import get_default_json_processor

logger = structlog.get_logger(__name__)

__all__ = ["parse_head_info_sheet"]


def parse_head_info_sheet(row_iterator: Iterator[tuple], sheet_name: str) -> WpHead:
    """从_Head_Info工作表的行迭代器解析井头信息。

    Args:
        row_iterator: 行数据迭代器（已跳过标题行）
        sheet_name: 工作表名称，用于日志记录

    Returns:
        WpHead: 包含所有扩展属性的井头信息对象
    """
    # 获取并验证表头行
    try:
        header_row = next(row_iterator)
    except StopIteration:
        logger.warning("_Head_Info表单为空", sheet_name=sheet_name)
        return WpHead()

    _validate_head_info_header(header_row, sheet_name)

    head = WpHead()
    first_record_processed = False
    # 迭代器现在位于第一个数据行
    for row_idx, row in enumerate(row_iterator):
        # 跳过空行
        if _is_empty_row(row):
            continue

        # WFS规范要求至少9列
        if len(row) < 9:
            logger.warning("井头信息行数据不完整，已跳过", sheet_name=sheet_name, row_index=row_idx, row_data=row)
            continue

        try:
            record = _parse_attribute_record(row, row_idx, sheet_name)
            if not first_record_processed:

                if record is None:
                    raise WpFileFormatError("井头信息的第一个有效属性必须是版本属性，但没有找到有效的属性记录")

                # 判断文件版本
                if record["category"] != WpXlsxKey.SEC_VERSION.value:
                    raise WpFileFormatError(
                        f"井头信息的第一个有效属性必须是'{WpXlsxKey.SEC_VERSION.value}'(版本)类别，但在第{row_idx}行找到'{record['category']}'"
                    )

                if record["attribute"] != WpXlsxKey.ATTR_VERSION.value.upper():
                    raise WpFileFormatError(
                        f"版本属性的名称必须是'{WpXlsxKey.ATTR_VERSION.value.upper()}'，但在第{row_idx}行找到'{record['attribute']}'"
                    )

                if record["value"] != WpXlsxKey.CURRENT_VERSION.value:
                    raise WpFileFormatError(f"版本号不匹配，期望'{WpXlsxKey.CURRENT_VERSION.value}'，实际'{record['value']}'")

                first_record_processed = True

            if record is not None:
                try:
                    # 添加属性是会进行作用域检查
                    head.add_attribute(
                        category=record["category"],
                        attribute=record["attribute"],
                        value=record["value"],
                        dataset=record["dataset"],
                        well=record["well"],
                        curve=record["curve"],
                        data_type=record["data_type"],
                        unit=record["unit"],
                        description=record["description"]
                    )
                except Exception as e:
                    logger.warning(
                        "添加属性到WpHead时发生错误，忽略该属性",
                        sheet_name=sheet_name,
                        category=record["category"],
                        row_index=row_idx,
                        attribute=record["attribute"],
                        error=str(e)
                    )
            else:
                logger.debug("跳过无效的井头信息行", sheet_name=sheet_name, row_index=row_idx)

        except Exception as e:
            logger.warning(
                "解析井头信息行时发生未知错误",
                sheet_name=sheet_name,
                row_index=row_idx,
                error=str(e)
            )
            raise

    logger.debug("井头信息解析完成", sheet_name=sheet_name, attribute_count=len(head.attribute_records))
    return head


def _validate_head_info_header(header_row: tuple, sheet_name: str) -> None:
    """验证_Head_Info表单的表头。

    严格检查列名和顺序是否符合WFS 5.2.1节规范。

    Args:
        header_row: 表头行数据
        sheet_name: 工作表名称，用于日志记录

    Raises:
        WpFileFormatError: 表头不符合规范
    """
    expected_headers = WpXlsxKey.expected_head_headers()
    if len(header_row) < len(expected_headers):
        raise WpFileFormatError(f"_Head_Info表单 '{sheet_name}' 的列数不足。期望 {len(expected_headers)} 列，实际 {len(header_row)} 列。")

    # 逐个比较表头，不区分大小写
    for i, expected in enumerate(expected_headers):
        actual = str(header_row[i] or "").strip()
        if actual.upper() != expected.upper():
            raise WpFileFormatError(
                f"_Head_Info表单 '{sheet_name}' 的第 {i+1} 列标题错误。"
                f" 期望 '{expected}', 实际 '{actual}'。"
            )

    logger.debug("_Head_Info表头验证通过", sheet_name=sheet_name)

def _parse_attribute_record(row: tuple, row_idx: int, sheet_name: str) -> dict[str, Any] | None:
    """解析并验证单行井头信息。"""
    category = str(row[0] or "").strip().upper()
    dataset = str(row[1] or "").strip()
    well = str(row[2] or "").strip()
    curve = str(row[3] or "").strip()
    attribute = str(row[4] or "").strip().upper()
    data_type = str(row[5] or "").strip().upper()
    unit = str(row[6] or "").strip()
    value = row[7]
    description = str(row[8] or "").strip()

    # 1. 验证必填字段
    if not category or not attribute or value is None:
        logger.warning("井头信息行缺少必填字段，已跳过", sheet_name=sheet_name, row_index=row_idx, row_data=row)
        return None


    # 2. 验证属性类别 (S列)
    # 确保验证集合也是大写的，以实现真正的、健壮的大小写不敏感比较
    # 遵循WFS 7.2节规范
    if not category or category not in WpXlsxKey.head_info_sections():
        logger.warning(
            "井头信息行包含无效的属性类别(S列)，已跳过",
            sheet_name=sheet_name, row_index=row_idx, invalid_category=str(row[0] or "")
        )
        return None

    if category == WpXlsxKey.SEC_VERSION.value:
        # 版本节，数据类型强制为STR
        data_type = WpXlsxKey.TYPE_STR.value
    else:
        # 5. 验证数据类型
        if not data_type or data_type not in WpXlsxKey.data_types():
            logger.warning("井头信息行包含无效的'data_type'，已跳过", sheet_name=sheet_name, row_index=row_idx, attribute=attribute, invalid_type=data_type)
            return None

    # 6. 根据数据类型验证并转换VALUE
    converted_value: Any
    if data_type == WpXlsxKey.TYPE_INT.value:
        try:
            # Excel may read numbers as float, so convert to float first
            converted_value = int(float(value))
        except (ValueError, TypeError):
            logger.warning("INT类型的值无法转换为整数，已跳过", sheet_name=sheet_name, row_index=row_idx, attribute=attribute, value=value)
            return None
    elif data_type == WpXlsxKey.TYPE_FLOAT.value:
        try:
            converted_value = float(value)
        except (ValueError, TypeError):
            logger.warning("FLOAT类型的值无法转换为浮点数，已跳过", sheet_name=sheet_name, row_index=row_idx, attribute=attribute, value=value)
            return None
    elif data_type == WpXlsxKey.TYPE_BOOL.value:
        # WFS 5.2.2: 真值表示：T、TRUE、Y、Yes、1
        true_values = {v.upper() for v in WpXlsxKey.bool_true_values().values()}
        converted_value = str(value).strip().upper() in true_values
    elif data_type == WpXlsxKey.TYPE_COMP.value:
        if not isinstance(value, str):
            logger.warning("COMP类型的'value'字段必须是字符串，但收到了其他类型。已跳过此记录。",
                         sheet_name=sheet_name, row_index=row_idx, attribute=attribute, value_type=type(value).__name__)
            return None

        json_processor = get_default_json_processor()
        try:
            # 验证并解析JSON字符串
            converted_value = json_processor.parse_json_string(value)
        except Exception as e:  # WpCompValidationError
            logger.warning("COMP类型的'value'字段不是有效的JSON字符串。已跳过此记录。",
                         sheet_name=sheet_name, row_index=row_idx, attribute=attribute, error=str(e))
            return None
    else:  # STR
        converted_value = str(value)

    return {
        "category": category,
        "dataset": dataset,
        "well": well,
        "curve": curve,
        "attribute": attribute,
        "data_type": data_type,
        "unit": unit,
        "value": converted_value,
        "description": description
    }

def _is_empty_row(row: tuple) -> bool:
    """检查是否为空行。

    Args:
        row: 行数据元组

    Returns:
        bool: 是否为空行
    """
    return all(not str(cell or "").strip() for cell in row)
