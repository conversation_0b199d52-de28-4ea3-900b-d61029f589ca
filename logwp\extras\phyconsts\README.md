# Physical Constants for Engineering (`logwp.extras.physconsts`)

A package for providing reliable, version-controlled physical and petroleum engineering constants for use in scientific and engineering calculations.

This package works seamlessly with `logwp.extras.units`, as every constant's value is a `Quantity` object, ensuring dimensional correctness.

## Quickstart

The package exposes a global constant registry `pconsts`. You can access constants by their full name as an attribute.

```python
from logwp.extras import physconsts

# Access the standard gravity constant
g_const = physconsts.pconsts.standard_gravity

print(g_const)
# > <Constant(name='standard_gravity', value=<Quantity(9.80665, 'meter/second**2')>, symbol='g', reference='ISO 80000-3:2006')>

# The value is a Quantity object from the units package
g_value = g_const.value
print(g_value)
# > 9.80665 meter/second**2
```

## Using Constants in Calculations

The primary benefit of this package is using trusted, unit-aware constants directly in your formulas.

```python
from logwp.extras import units, physconsts

mass = units.Q_(70, 'kg')
gravity = physconsts.pconsts.standard_gravity.value

# The calculation is dimensionally aware
weight = mass * gravity
print(weight)
# > 686.4655 kilogram*meter/second**2
```

## Available Constants

The registry includes a set of core universal and petroleum-specific constants. Each constant object contains the `.name`, `.value` (a `Quantity`), `.symbol`, and `.reference` attributes.

### Universal Constants
- `standard_gravity` (g): Standard acceleration of gravity.
- `speed_of_light` (c): The speed of light in a vacuum.

### Petroleum Engineering Constants
- `standard_pressure` (P_std): Standard pressure, typically 14.696 psi.
- `standard_temperature` (T_std): Standard temperature, typically 60 °F.
