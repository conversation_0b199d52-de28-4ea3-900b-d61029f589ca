"""元数据工厂 - 测试专用

提供预定义的曲线元数据创建方法。

Examples
--------
>>> # 创建标准测井元数据
>>> metadata = MetadataFactory.standard_logging_metadata()
>>>
>>> # 创建NMR元数据
>>> nmr_metadata = MetadataFactory.nmr_metadata()
>>>
>>> # 创建岩心分析元数据
>>> core_metadata = MetadataFactory.core_analysis_metadata()
"""

from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.models.curve import CurveMetadata


class MetadataFactory:
    """元数据工厂类。
    
    提供预定义的曲线元数据创建方法，用于快速生成标准化的测试元数据。
    """
    
    @staticmethod
    def standard_logging_metadata() -> 'CurveMetadata':
        """创建标准测井曲线元数据。
        
        Returns:
            CurveMetadata: 标准测井元数据
            
        Note:
            需要完整的logwp环境才能工作。
        """
        try:
            from logwp.testing.utils import quick_metadata
            
            return quick_metadata(
                "WELL",      # 井名
                "MD",        # 深度
                "GR",        # 自然伽马
                "PHIT",      # 总孔隙度
                "SW",        # 含水饱和度
                "PERM",      # 渗透率
                "RHOB",      # 体积密度
                "NPHI",      # 中子孔隙度
                "RT",        # 真电阻率
                "SP",        # 自然电位
                "CALI"       # 井径
            )
            
        except ImportError:
            raise RuntimeError(
                "MetadataFactory.standard_logging_metadata() requires full logwp environment."
            )
    
    @staticmethod
    def nmr_metadata(t2_bins: int = 64) -> 'CurveMetadata':
        """创建NMR相关曲线元数据。
        
        Args:
            t2_bins: T2谱bin数量
            
        Returns:
            CurveMetadata: NMR元数据
        """
        try:
            from logwp.testing.utils import quick_metadata
            
            # 基础曲线
            curve_names = [
                "WELL",         # 井名
                "MD",           # 深度
                "PHIT_NMR",     # NMR总孔隙度
                "T2LM",         # T2对数平均值
                "T2_P50",       # T2中值
                "DPHIT_NMR",    # NMR有效孔隙度
                "BVI",          # 束缚水体积指数
                "FFI",          # 自由流体指数
                "MPHI",         # 微孔隙度
                "MACRO_PORO"    # 宏观孔隙度
            ]
            
            # 添加T2谱分布曲线
            for i in range(t2_bins):
                curve_names.append(f"T2_DIST_{i:02d}")
            
            return quick_metadata(*curve_names)
            
        except ImportError:
            raise RuntimeError(
                "MetadataFactory.nmr_metadata() requires full logwp environment."
            )
    
    @staticmethod
    def core_analysis_metadata() -> 'CurveMetadata':
        """创建岩心分析曲线元数据。
        
        Returns:
            CurveMetadata: 岩心分析元数据
        """
        try:
            from logwp.testing.utils import quick_metadata
            
            return quick_metadata(
                "WELL",         # 井名
                "MD",           # 深度
                "PERM_CORE",    # 岩心渗透率
                "PHIT_CORE",    # 岩心孔隙度
                "GRAIN_DENS",   # 颗粒密度
                "BULK_DENS",    # 体积密度
                "FACIES",       # 岩相
                "GRAIN_SIZE",   # 粒度
                "SORTING",      # 分选性
                "CEMENT_TYPE",  # 胶结类型
                "CLAY_CONTENT", # 粘土含量
                "CARB_CONTENT"  # 碳酸盐含量
            )
            
        except ImportError:
            raise RuntimeError(
                "MetadataFactory.core_analysis_metadata() requires full logwp environment."
            )
    
    @staticmethod
    def interval_metadata() -> 'CurveMetadata':
        """创建区间数据元数据。
        
        Returns:
            CurveMetadata: 区间数据元数据
        """
        try:
            from logwp.testing.utils import quick_metadata
            
            return quick_metadata(
                "WELL",         # 井名
                "MD_Top",       # 顶深
                "MD_Bottom",    # 底深
                "FORMATION",    # 地层
                "ZONE_TYPE",    # 层段类型
                "NET_PAY",      # 有效厚度
                "AVG_PORO",     # 平均孔隙度
                "AVG_PERM",     # 平均渗透率
                "AVG_SW",       # 平均含水饱和度
                "HCPV",         # 烃类孔隙体积
                "OOIP",         # 原始石油地质储量
                "RECOVERY"      # 采收率
            )
            
        except ImportError:
            raise RuntimeError(
                "MetadataFactory.interval_metadata() requires full logwp environment."
            )
    
    @staticmethod
    def minimal_metadata() -> 'CurveMetadata':
        """创建最小化元数据。
        
        Returns:
            CurveMetadata: 最小化元数据
        """
        try:
            from logwp.testing.utils import quick_metadata
            
            return quick_metadata(
                "WELL",    # 井名
                "MD",      # 深度
                "GR",      # 自然伽马
                "PHIT"     # 总孔隙度
            )
            
        except ImportError:
            raise RuntimeError(
                "MetadataFactory.minimal_metadata() requires full logwp environment."
            )
    
    @staticmethod
    def custom_metadata(curve_names: list[str]) -> 'CurveMetadata':
        """创建自定义元数据。
        
        Args:
            curve_names: 曲线名称列表
            
        Returns:
            CurveMetadata: 自定义元数据
        """
        try:
            from logwp.testing.utils import quick_metadata
            
            return quick_metadata(*curve_names)
            
        except ImportError:
            raise RuntimeError(
                "MetadataFactory.custom_metadata() requires full logwp environment."
            )
