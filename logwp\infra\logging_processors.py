from __future__ import annotations

from typing import Any, Dict

import psutil
import structlog

# 严格遵守架构：仅从同级或下级导入
from .gpu.gpu_utils import get_gpu_info, is_gpu_available


def add_performance_context(
    logger: structlog.BoundLogger,
    method_name: str,
    event_dict: Dict[str, Any]
) -> Dict[str, Any]:
    """向日志事件中添加性能上下文 (CPU, 内存)。"""
    try:
        process = psutil.Process()
        # 使用标准化的键名
        event_dict["context_performance"] = {
            "memory_rss_mb": round(process.memory_info().rss / 1024 / 1024, 2),
            "cpu_percent": process.cpu_percent(),
        }
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        # 在进程已结束或无权限等情况下优雅地跳过
        pass
    return event_dict


def add_gpu_context(
    logger: structlog.BoundLogger,
    method_name: str,
    event_dict: Dict[str, Any]
) -> Dict[str, Any]:
    """如果GPU可用，则向日志事件中添加GPU上下文。"""
    # 先检查，再执行，避免不必要的尝试和错误
    if not is_gpu_available():
        return event_dict

    try:
        # 使用专用的工具函数获取信息，而不是自己实现
        info = get_gpu_info()
        if info:
            # 使用标准化的键名
            event_dict["context_gpu"] = {
                "device_name": info.get("device_name"),
                "memory_used_mb": info.get("memory_used_mb"),
                "memory_total_mb": info.get("memory_total_mb"),
            }
    except Exception as e:
        # 即使 is_gpu_available() 为True，获取信息也可能失败
        event_dict["context_gpu"] = {"error": f"Failed to retrieve GPU info: {e}"}
    return event_dict
