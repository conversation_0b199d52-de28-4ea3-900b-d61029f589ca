{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a9e0a2e7", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "1e86095f", "metadata": {}, "source": ["# 基准模型对比案例 (Benchmark Comparison Case)"]}, {"cell_type": "markdown", "id": "4266395e", "metadata": {}, "source": ["## 1. 初始化与导入"]}, {"cell_type": "code", "execution_count": 2, "id": "3e9b170c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-28T15:53:24.120253Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 127.39, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:25.017737Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.16, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-28T15:53:25.033378Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.17, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-28T15:53:25.033378Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.18, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-28T15:53:28.481728Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 764.04, 'cpu_percent': 0.0} operation=register_base_profile profile_name=validation.plt.base\n", "2025-07-28T15:53:28.497600Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 764.06, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:53:28.513691Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 764.09, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.capture_curve\n", "2025-07-28T15:53:28.524070Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 764.11, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:53:28.574360Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 764.13, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.perm_corr.permeability_crossplot\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "# 导入 baselines 组件\n", "from scape.core.baselines import (\n", "    run_sdr_training_step, run_sdr_prediction_step,\n", "    run_timur_training_step, run_timur_prediction_step,\n", "    run_dnn_training_step, run_dnn_prediction_step\n", ")\n", "from scape.core.baselines.sdr import SdrTrainingConfig, SdrPredictionConfig, SdrArtifacts\n", "from scape.core.baselines.timur import TimurTrainingConfig, TimurPredictionConfig, TimurArtifacts\n", "from scape.core.baselines.hybrid_dnn import DnnTrainingConfig, DnnPredictionConfig, DnnArtifacts\n", "\n", "# 导入 validation 组件\n", "from scape.core.validation import (\n", "    run_plt_analysis_step,\n", "    PltAnalysisConfig,\n", "    PltAnalysisArtifacts,\n", "    PltPlotTypes,\n", "    PltAnalysisPlotProfiles\n", ")\n", "\n", "import scape.core.validation.plot_profiles"]}, {"cell_type": "markdown", "id": "f519543e", "metadata": {}, "source": ["## 2. 实验设置与数据加载"]}, {"cell_type": "code", "execution_count": 3, "id": "e446002f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-28T15:53:28.649911Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 765.15, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\baselines_run_20250728_235328 run_id=20250728-155328-c6b76c91\n", "实验运行已初始化，所有产物将保存至: X:\\03.Project\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\25_baselines\\case01\\output01\\baselines_run_20250728_235328\n", "2025-07-28T15:53:28.663975Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 765.16, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-28T15:53:28.702296Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 765.45, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx file_size_mb=0.2 sheet_count=1\n", "2025-07-28T15:53:28.720952Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 765.47, 'cpu_percent': 0.0} project_name=scape_swift_pso_train_cleaned\n", "2025-07-28T15:53:28.729671Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 765.48, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_train_cleaned\n", "2025-07-28T15:53:28.754095Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 765.82, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-28T15:53:28.785594Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 765.96, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=12 well_curves=1\n", "2025-07-28T15:53:29.053196Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.9, 'cpu_percent': 0.0} shape=(457, 75) sheet_name=swift_pso_train_cleaned\n", "2025-07-28T15:53:29.074000Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.91, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned')\n", "2025-07-28T15:53:29.092749Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.91, 'cpu_percent': 0.0} curve_count=12 dataset_name=swift_pso_train_cleaned dataset_type=Point df_shape=(457, 75) processing_time=0.343\n", "2025-07-28T15:53:29.114325Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.91, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-28T15:53:29.129487Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.91, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-28T15:53:29.164229Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.91, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=0.5 project_name=WpIdentifier('scape_swift_pso_train_cleaned')\n", "2025-07-28T15:53:29.190768Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.09, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=10 input_curves=['BFV_NMR', 'T2_P50', 'MD', 'DT2_P50', 'T2LM', 'K_LABEL', 'PHIT_NMR', 'DPHIT_NMR', 'WELL_NO', 'PHI_T2_DIST'] operation=extract_metadata output_curve_count=10 output_curves=['BFV_NMR', 'T2_P50', 'MD', 'DT2_P50', 'T2LM', 'K_LABEL', 'PHIT_NMR', 'DPHIT_NMR', 'WELL_NO', 'PHI_T2_DIST']\n", "2025-07-28T15:53:29.207268Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.09, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-28T15:53:29.214217Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.09, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-28T15:53:29.227835Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.09, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-28T15:53:29.240659Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.1, 'cpu_percent': 0.0}\n", "✅ 成功读取训练数据: ./scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-28T15:53:29.248041Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.1, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-28T15:53:29.281078Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.11, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx file_size_mb=1.9 sheet_count=1\n", "2025-07-28T15:53:29.307296Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.11, 'cpu_percent': 0.0} project_name=scape_swift_pso_apply_cleaned\n", "2025-07-28T15:53:29.314224Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.11, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_apply_cleaned\n", "2025-07-28T15:53:29.329664Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.13, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-28T15:53:29.352776Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.14, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-28T15:53:31.311468Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 781.44, 'cpu_percent': 0.0} shape=(4689, 72) sheet_name=swift_pso_apply_cleaned\n", "2025-07-28T15:53:31.329464Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 781.44, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned')\n", "2025-07-28T15:53:31.380317Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 781.44, 'cpu_percent': 0.0} curve_count=9 dataset_name=swift_pso_apply_cleaned dataset_type=Continuous df_shape=(4689, 72) processing_time=2.053\n", "2025-07-28T15:53:31.392176Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 781.44, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-28T15:53:31.405297Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 781.44, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-28T15:53:31.430558Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 781.44, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=2.183 project_name=WpIdentifier('scape_swift_pso_apply_cleaned')\n", "2025-07-28T15:53:31.459870Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.54, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['BFV_NMR', 'T2_P50', 'MD', 'DT2_P50', 'T2LM', 'PHIT_NMR', 'DPHIT_NMR', 'WELL_NO', 'PHI_T2_DIST'] operation=extract_metadata output_curve_count=9 output_curves=['BFV_NMR', 'T2_P50', 'MD', 'DT2_P50', 'T2LM', 'PHIT_NMR', 'DPHIT_NMR', 'WELL_NO', 'PHI_T2_DIST']\n", "2025-07-28T15:53:31.480509Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.54, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-28T15:53:31.491436Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.54, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-28T15:53:31.502946Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.54, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-28T15:53:31.514301Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.54, 'cpu_percent': 0.0}\n", "✅ 成功读取应用数据: ./scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-28T15:53:31.524978Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.54, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx\n", "2025-07-28T15:53:31.541064Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-28T15:53:31.551714Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} project_name=scape_plt_val\n", "2025-07-28T15:53:31.560943Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_plt_val\n", "2025-07-28T15:53:31.588648Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-28T15:53:31.608257Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-28T15:53:31.618879Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-28T15:53:31.633757Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-28T15:53:31.648133Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-28T15:53:31.680705Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} shape=(14, 4) sheet_name=PLT\n", "2025-07-28T15:53:31.694551Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-28T15:53:31.703584Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(14, 4) processing_time=0.073\n", "2025-07-28T15:53:31.719346Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-28T15:53:31.730979Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-28T15:53:31.750070Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx processing_time=0.225 project_name=WpIdentifier('scape_plt_val')\n", "2025-07-28T15:53:31.766456Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} extracted_curve_count=1 operation=extract_curve_dataframe_bundle\n", "2025-07-28T15:53:31.780487Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.55, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['QOZI', 'WELL_NO', 'MD_Top', 'MD_Bottom'] operation=extract_metadata output_curve_count=4 output_curves=['QOZI', 'WELL_NO', 'MD_Top', 'MD_Bottom']\n", "✅ 成功读取PLT验证数据: ./scape_plt_val.wp.xlsx\n", "  - T2轴信息: T2AxisLog10(t2_start=0.1, t2_end=10000, n=64, unit='ms')\n"]}], "source": ["# --- 实验设置 ---\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"baselines_run\")\n", "run_context = RunContext(output_dir / run_dir_name, overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")\n", "\n", "# --- 加载数据 ---\n", "reader = WpExcelReader()\n", "\n", "# 训练数据\n", "train_data_path = \"./scape_swift_pso_train_cleaned.wp.xlsx\"\n", "train_project = reader.read(train_data_path)\n", "train_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'BFV_NMR', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',\n", "        'PHI_T2_DIST', 'K_LABEL'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print(f\"✅ 成功读取训练数据: {train_data_path}\")\n", "\n", "# 应用数据\n", "apply_data_path = \"./scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "apply_project = reader.read(apply_data_path)\n", "apply_bundle = apply_project.get_dataset(\"swift_pso_apply_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'BFV_NMR', 'DT2_P50', 'T2_P50', 'DPHIT_NMR', 'PHI_T2_DIST'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print(f\"✅ 成功读取应用数据: {apply_data_path}\")\n", "\n", "# PLT验证数据\n", "plt_val_path = \"./scape_plt_val.wp.xlsx\"\n", "plt_val_project = reader.read(plt_val_path)\n", "plt_bundle = plt_val_project.get_dataset(\"PLT\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取PLT验证数据: {plt_val_path}\")\n", "\n", "# --- 准备数据依赖参数 ---\n", "t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "t2_time_array = t2_axis_info.calculate_values()\n", "print(f\"  - T2轴信息: {t2_axis_info}\")"]}, {"cell_type": "markdown", "id": "e5b87190", "metadata": {}, "source": ["## 3. 基准模型一：SDR 模型"]}, {"cell_type": "code", "execution_count": 4, "id": "76495147", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 1/4] 开始执行 SDR 基准模型...\n", "2025-07-28T15:53:31.846135Z [info     ] 开始SDR基准模型训练步骤                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.57, 'cpu_percent': 0.0} operation=sdr_training_step\n", "2025-07-28T15:53:31.870999Z [warning  ] 移除了包含非正数值的行，这些值在对数计算中无效        [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.59, 'cpu_percent': 0.0} operation=sdr_training_step remaining_rows=304 removed_rows=153\n", "2025-07-28T15:53:31.879501Z [info     ] 调用内部优化器寻找最佳SDR参数...            [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.59, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:31.911155Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_training.models.sdr_assets artifact_path=sdr_baseline_training\\sdr_model_assets.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.97, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:31.928602Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_training.configs.training_config_snapshot artifact_path=sdr_baseline_training\\training_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.97, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:31.953295Z [info     ] SDR基准模型训练步骤完成                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.97, 'cpu_percent': 0.0} final_loss=1.3347507415777813\n", "  - SDR 训练完成，最终损失: 1.3348\n", "2025-07-28T15:53:31.979573Z [info     ] 开始SDR基准模型预测步骤                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.97, 'cpu_percent': 0.0} operation=sdr_prediction_step\n", "2025-07-28T15:53:31.979573Z [info     ] 调用内部计算逻辑进行渗透率预测...             [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.98, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:31.995392Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 787.99, 'cpu_percent': 0.0} curve_name=K_SDR_PRED curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-28T15:53:32.029605Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_prediction.datasets.sdr_predictions artifact_path=sdr_baseline_prediction\\sdr_predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:32.040623Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_prediction.configs.prediction_config_snapshot artifact_path=sdr_baseline_prediction\\prediction_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.03, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:32.056251Z [info     ] SDR基准模型预测步骤完成                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.03, 'cpu_percent': 0.0} output_curve=K_SDR_PRED predicted_samples=4689\n", "  - SDR 预测完成，已将 'K_SDR_PRED' 添加到应用集。\n"]}], "source": ["print(\"🚀 [Step 1/4] 开始执行 SDR 基准模型...\")\n", "\n", "# --- 训练 ---\n", "sdr_train_result = run_sdr_training_step(\n", "    config=SdrTrainingConfig(),\n", "    ctx=run_context,\n", "    train_bundle=train_bundle,\n", "    k_label_curve='K_LABEL',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    t2lm_curve='T2LM'\n", ")\n", "print(f\"  - SDR 训练完成，最终损失: {sdr_train_result['final_loss']:.4f}\")\n", "\n", "# --- 预测 ---\n", "from scape.core.baselines.sdr.artifact_handler import SdrArtifactHandler\n", "handler = SdrArtifactHandler()\n", "model_params_path = run_context.get_artifact_path(SdrArtifacts.MODEL_ASSETS.value)\n", "sdr_model_assets = handler.load_model_assets(model_params_path)\n", "sdr_pred_result = run_sdr_prediction_step(\n", "    config=SdrPredictionConfig(),\n", "    ctx=run_context,\n", "    model_assets=sdr_model_assets,\n", "    prediction_bundle=apply_bundle, # 直接修改应用集Bundle\n", "    output_curve_name='K_SDR_PRED',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    t2lm_curve='T2LM'\n", ")\n", "print(f\"  - SDR 预测完成，已将 'K_SDR_PRED' 添加到应用集。\")"]}, {"cell_type": "markdown", "id": "b1c2d3e4", "metadata": {}, "source": ["## 4. 基准模型二：Timur/Coates 模型"]}, {"cell_type": "code", "execution_count": 5, "id": "f5g6h7i8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 2/4] 开始执行 Timur/Coates 基准模型...\n", "2025-07-28T15:53:32.103844Z [info     ] 开始Timur/Coates基准模型训练步骤         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0} operation=timur_training_step\n", "2025-07-28T15:53:32.119552Z [warning  ] 移除了包含无效物理值的行（如非正渗透率/孔隙度，或PHIT<=BFV） [scape.core.baselines.timur.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0} operation=timur_training_step remaining_rows=304 removed_rows=153\n", "2025-07-28T15:53:32.130069Z [info     ] 调用内部优化器寻找最佳Timur/Coates参数...   [scape.core.baselines.timur.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:32.176798Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_training.models.timur_assets artifact_path=timur_baseline_training\\timur_model_assets.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:32.196223Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_training.configs.training_config_snapshot artifact_path=timur_baseline_training\\training_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:32.211622Z [info     ] Timur/Coates基准模型训练步骤完成         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0} final_loss=1.1931803726134353\n", "  - Timur 训练完成，最终损失: 1.1932\n", "2025-07-28T15:53:32.221648Z [info     ] 开始Timur/Coates基准模型预测步骤         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0} operation=timur_prediction_step\n", "2025-07-28T15:53:32.224844Z [info     ] 调用内部计算逻辑进行渗透率预测...             [scape.core.baselines.timur.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:32.252804Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.05, 'cpu_percent': 0.0} curve_name=K_TIMUR_PRED curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-28T15:53:32.292123Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_prediction.datasets.timur_predictions artifact_path=timur_baseline_prediction\\timur_predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:32.326303Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_prediction.configs.prediction_config_snapshot artifact_path=timur_baseline_prediction\\prediction_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:32.341516Z [info     ] Timur/Coates基准模型预测步骤完成         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.07, 'cpu_percent': 0.0} output_curve=K_TIMUR_PRED predicted_samples=4689\n", "  - Timur 预测完成，已将 'K_TIMUR_PRED' 添加到应用集。\n"]}], "source": ["print(\"🚀 [Step 2/4] 开始执行 Timur/Coates 基准模型...\")\n", "\n", "# --- 训练 ---\n", "timur_train_result = run_timur_training_step(\n", "    config=TimurTrainingConfig(bfv_min=0.02),\n", "    ctx=run_context,\n", "    train_bundle=train_bundle,\n", "    k_label_curve='K_LABEL',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    bfv_nmr_curve='BFV_NMR'\n", ")\n", "print(f\"  - Timur 训练完成，最终损失: {timur_train_result['final_loss']:.4f}\")\n", "\n", "# --- 预测 ---\n", "from scape.core.baselines.timur.artifact_handler import TimurArtifactHandler\n", "handler = TimurArtifactHandler()\n", "model_params_path = run_context.get_artifact_path(TimurArtifacts.MODEL_ASSETS.value)\n", "timur_model_assets = handler.load_model_assets(model_params_path)\n", "timur_pred_result = run_timur_prediction_step(\n", "    config=TimurPredictionConfig(bfv_min=0.02),\n", "    ctx=run_context,\n", "    model_assets=timur_model_assets,\n", "    prediction_bundle=apply_bundle, # 继续修改同一个应用集Bundle\n", "    output_curve_name='K_TIMUR_PRED',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    bfv_nmr_curve='BFV_NMR'\n", ")\n", "print(f\"  - Timur 预测完成，已将 'K_TIMUR_PRED' 添加到应用集。\")"]}, {"cell_type": "markdown", "id": "n3o4p5q6", "metadata": {}, "source": ["## 5. 基准模型三：Hybrid DNN 模型"]}, {"cell_type": "code", "execution_count": 6, "id": "r7s8t9u0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 3/4] 开始执行 Hybrid DNN 基准模型...\n", "2025-07-28T15:53:32.414075Z [info     ] ===== Hybrid DNN Training Step Started ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.08, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:32.419204Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.configs.training_config artifact_path=dnn_hybrid_training\\training_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.08, 'cpu_percent': 0.0} description=本次DNN训练运行的完整配置参数。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:53:32.447270Z [info     ] 训练配置已作为产物保存。                   [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.08, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:32.455399Z [info     ] 曲线名已成功解析为DataFrame列名。          [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.08, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:32.463941Z [info     ] --- Stage 1: Hyperparameter Tuning using LOWO-CV --- [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.08, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:32.468933Z [info     ] Tuning procedure started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.26, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:32.484564Z [info     ] --- Starting CV Fold 1/3 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 788.27, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-28 23:53:32,505] A new study created in memory with name: no-name-f08a150c-1534-48a2-b9a4-cb2798d600ef\n", "[I 2025-07-28 23:53:35,976] Trial 0 finished with value: 12.558058738708496 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.43796191804446355, 'learning_rate': 0.0005684942737620711, 'weight_decay': 0.0006382284235007244}. Best is trial 0 with value: 12.558058738708496.\n", "[I 2025-07-28 23:53:36,538] Trial 1 finished with value: 12.2123384475708 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.20112114785633528, 'learning_rate': 0.003934298824671848, 'weight_decay': 6.973733120833994e-05}. Best is trial 1 with value: 12.2123384475708.\n", "[I 2025-07-28 23:53:37,936] Trial 2 finished with value: 16.969433307647705 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.5538294278118285, 'learning_rate': 0.0001850491174056762, 'weight_decay': 0.00018704105369190174}. Best is trial 1 with value: 12.2123384475708.\n", "[I 2025-07-28 23:53:38,611] Trial 3 finished with value: 11.602498054504395 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 8, 'dropout_rate': 0.3402954537451703, 'learning_rate': 0.006558291504975571, 'weight_decay': 3.314400023250966e-05}. Best is trial 3 with value: 11.602498054504395.\n", "[I 2025-07-28 23:53:39,625] Trial 4 finished with value: 11.224386215209961 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.41884752466963293, 'learning_rate': 0.006037384802325004, 'weight_decay': 1.9291583316336262e-05}. Best is trial 4 with value: 11.224386215209961.\n", "[I 2025-07-28 23:53:39,726] Trial 5 pruned. \n", "[I 2025-07-28 23:53:39,757] Trial 6 pruned. \n", "[I 2025-07-28 23:53:40,250] Trial 7 pruned. \n", "[I 2025-07-28 23:53:40,275] Trial 8 pruned. \n", "[I 2025-07-28 23:53:40,304] Trial 9 pruned. \n", "[I 2025-07-28 23:53:40,398] Trial 10 pruned. \n", "[I 2025-07-28 23:53:41,016] Trial 11 finished with value: 11.208704471588135 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 8, 'dropout_rate': 0.38014346227842627, 'learning_rate': 0.009986742555637128, 'weight_decay': 2.2996002232087546e-05}. Best is trial 11 with value: 11.208704471588135.\n", "[I 2025-07-28 23:53:41,052] Trial 12 pruned. \n", "[I 2025-07-28 23:53:43,301] Trial 13 finished with value: 11.072521209716797 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.38455786273558656, 'learning_rate': 0.009763627461535218, 'weight_decay': 2.0261609848459406e-05}. Best is trial 13 with value: 11.072521209716797.\n", "[I 2025-07-28 23:53:43,876] Trial 14 finished with value: 11.781086921691895 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.3721633340205783, 'learning_rate': 0.009210467836812244, 'weight_decay': 3.8508595190472115e-05}. Best is trial 13 with value: 11.072521209716797.\n", "[I 2025-07-28 23:53:43,907] Trial 15 pruned. \n", "[I 2025-07-28 23:53:43,960] Trial 16 pruned. \n", "[I 2025-07-28 23:53:43,990] Trial 17 pruned. \n", "[I 2025-07-28 23:53:44,066] Trial 18 pruned. \n", "[I 2025-07-28 23:53:44,205] Trial 19 pruned. \n", "[I 2025-07-28 23:53:44,241] Trial 20 pruned. \n", "[I 2025-07-28 23:53:44,297] Trial 21 pruned. \n", "[I 2025-07-28 23:53:44,368] Trial 22 pruned. \n", "[I 2025-07-28 23:53:44,418] Trial 23 pruned. \n", "[I 2025-07-28 23:53:44,450] Trial 24 pruned. \n", "[I 2025-07-28 23:53:44,586] Trial 25 pruned. \n", "[I 2025-07-28 23:53:45,860] Trial 26 finished with value: 11.423411846160889 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.404435279779291, 'learning_rate': 0.007050180340848197, 'weight_decay': 2.0049980847064424e-05}. Best is trial 13 with value: 11.072521209716797.\n", "[I 2025-07-28 23:53:45,903] Trial 27 pruned. \n", "[I 2025-07-28 23:53:45,941] Trial 28 pruned. \n", "[I 2025-07-28 23:53:45,974] Trial 29 pruned. \n", "[I 2025-07-28 23:53:46,007] Trial 30 pruned. \n", "[I 2025-07-28 23:53:46,117] Trial 31 pruned. \n", "[I 2025-07-28 23:53:46,574] Trial 32 finished with value: 11.512232303619385 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.3603817461527566, 'learning_rate': 0.007535516488314301, 'weight_decay': 2.2418462681954027e-05}. Best is trial 13 with value: 11.072521209716797.\n", "[I 2025-07-28 23:53:47,148] Trial 33 finished with value: 12.047699451446533 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.39573002322407047, 'learning_rate': 0.004713300114954373, 'weight_decay': 6.258058474293632e-05}. Best is trial 13 with value: 11.072521209716797.\n", "[I 2025-07-28 23:53:47,190] Trial 34 pruned. \n", "[I 2025-07-28 23:53:47,231] Trial 35 pruned. \n", "[I 2025-07-28 23:53:47,276] Trial 36 pruned. \n", "[I 2025-07-28 23:53:47,346] Trial 37 pruned. \n", "[I 2025-07-28 23:53:47,388] Trial 38 pruned. \n", "[I 2025-07-28 23:53:47,446] Trial 39 pruned. \n", "[I 2025-07-28 23:53:47,484] Trial 40 pruned. \n", "[I 2025-07-28 23:53:48,027] Trial 41 finished with value: 11.843208312988281 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.3641796160947869, 'learning_rate': 0.007620335260986089, 'weight_decay': 2.1246112183182746e-05}. Best is trial 13 with value: 11.072521209716797.\n", "[I 2025-07-28 23:53:48,208] Trial 42 pruned. \n", "[I 2025-07-28 23:53:48,252] Trial 43 pruned. \n", "[I 2025-07-28 23:53:48,290] Trial 44 pruned. \n", "[I 2025-07-28 23:53:48,341] Trial 45 pruned. \n", "[I 2025-07-28 23:53:48,374] Trial 46 pruned. \n", "[I 2025-07-28 23:53:48,474] Trial 47 pruned. \n", "[I 2025-07-28 23:53:48,513] Trial 48 pruned. \n", "[I 2025-07-28 23:53:49,068] Trial 49 finished with value: 12.002370357513428 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.21501583162915444, 'learning_rate': 0.008115743390829978, 'weight_decay': 5.882679167385783e-05}. Best is trial 13 with value: 11.072521209716797.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-28T15:53:49.074459Z [info     ] Fold 1 best trial: value=11.0725, params={'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.38455786273558656, 'learning_rate': 0.009763627461535218, 'weight_decay': 2.0261609848459406e-05} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1375.86, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:49.082265Z [info     ] --- Performing blind test for Fold 1 on well 'C-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1375.86, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:50.635531Z [info     ] --- Starting CV Fold 2/3 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1376.47, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-28 23:53:50,652] A new study created in memory with name: no-name-1a34b003-c732-41ef-a4c5-887d0df0c548\n", "[I 2025-07-28 23:53:51,314] Trial 0 finished with value: 10.513397216796875 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 8, 'dropout_rate': 0.505901224863718, 'learning_rate': 0.0029262256410947673, 'weight_decay': 2.5420921044232935e-05}. Best is trial 0 with value: 10.513397216796875.\n", "[I 2025-07-28 23:53:51,845] Trial 1 finished with value: 10.090442657470703 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.36293055216105335, 'learning_rate': 0.002676006622735815, 'weight_decay': 0.0002461812366404677}. Best is trial 1 with value: 10.090442657470703.\n", "[I 2025-07-28 23:53:53,458] Trial 2 finished with value: 24.477081298828125 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.2949293154944204, 'learning_rate': 0.00011292296385275346, 'weight_decay': 0.0006747012508944893}. Best is trial 1 with value: 10.090442657470703.\n", "[I 2025-07-28 23:53:54,442] Trial 3 finished with value: 8.656386375427246 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.4497608217464172, 'learning_rate': 0.008764565210618096, 'weight_decay': 0.00048032471348580897}. Best is trial 3 with value: 8.656386375427246.\n", "[I 2025-07-28 23:53:54,914] Trial 4 finished with value: 8.091381072998047 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.47940357535905476, 'learning_rate': 0.004209266506900967, 'weight_decay': 0.000270602711220251}. Best is trial 4 with value: 8.091381072998047.\n", "[I 2025-07-28 23:53:54,935] Trial 5 pruned. \n", "[I 2025-07-28 23:53:54,957] Trial 6 pruned. \n", "[I 2025-07-28 23:53:54,974] Trial 7 pruned. \n", "[I 2025-07-28 23:53:55,001] Trial 8 pruned. \n", "[I 2025-07-28 23:53:55,024] Trial 9 pruned. \n", "[I 2025-07-28 23:53:55,574] Trial 10 finished with value: 8.498058319091797 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.408002489943051, 'learning_rate': 0.009831394184628058, 'weight_decay': 0.0001403338379044238}. Best is trial 4 with value: 8.091381072998047.\n", "[I 2025-07-28 23:53:56,049] Trial 11 finished with value: 8.829788208007812 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.4218511939762973, 'learning_rate': 0.0097001435005516, 'weight_decay': 0.00014213078227735563}. Best is trial 4 with value: 8.091381072998047.\n", "[I 2025-07-28 23:53:56,136] Trial 12 pruned. \n", "[I 2025-07-28 23:53:56,919] Trial 13 finished with value: 8.393144607543945 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.4685403202878746, 'learning_rate': 0.0054975709443684856, 'weight_decay': 0.000259716494700594}. Best is trial 4 with value: 8.091381072998047.\n", "[I 2025-07-28 23:53:56,965] Trial 14 pruned. \n", "[I 2025-07-28 23:53:56,996] Trial 15 pruned. \n", "[I 2025-07-28 23:53:57,658] Trial 16 finished with value: 8.975736618041992 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.4707916321654835, 'learning_rate': 0.005307348954937569, 'weight_decay': 0.0009905857451309788}. Best is trial 4 with value: 8.091381072998047.\n", "[I 2025-07-28 23:53:57,685] Trial 17 pruned. \n", "[I 2025-07-28 23:53:57,718] Trial 18 pruned. \n", "[I 2025-07-28 23:53:57,768] Trial 19 pruned. \n", "[I 2025-07-28 23:53:57,800] Trial 20 pruned. \n", "[I 2025-07-28 23:53:58,524] Trial 21 finished with value: 8.544820785522461 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.41418644318293163, 'learning_rate': 0.006903544238956977, 'weight_decay': 0.00015045049073071978}. Best is trial 4 with value: 8.091381072998047.\n", "[I 2025-07-28 23:53:58,573] Trial 22 pruned. \n", "[I 2025-07-28 23:53:58,619] Trial 23 pruned. \n", "[I 2025-07-28 23:53:58,646] Trial 24 pruned. \n", "[I 2025-07-28 23:53:58,668] Trial 25 pruned. \n", "[I 2025-07-28 23:53:58,702] Trial 26 pruned. \n", "[I 2025-07-28 23:53:58,729] Trial 27 pruned. \n", "[I 2025-07-28 23:53:58,774] Trial 28 pruned. \n", "[I 2025-07-28 23:53:58,824] Trial 29 pruned. \n", "[I 2025-07-28 23:53:58,858] Trial 30 pruned. \n", "[I 2025-07-28 23:53:59,062] Trial 31 pruned. \n", "[I 2025-07-28 23:53:59,102] Trial 32 pruned. \n", "[I 2025-07-28 23:53:59,129] Trial 33 pruned. \n", "[I 2025-07-28 23:53:59,162] Trial 34 pruned. \n", "[I 2025-07-28 23:53:59,197] Trial 35 pruned. \n", "[I 2025-07-28 23:53:59,227] Trial 36 pruned. \n", "[I 2025-07-28 23:53:59,261] Trial 37 pruned. \n", "[I 2025-07-28 23:53:59,290] Trial 38 pruned. \n", "[I 2025-07-28 23:53:59,318] Trial 39 pruned. \n", "[I 2025-07-28 23:53:59,358] Trial 40 pruned. \n", "[I 2025-07-28 23:53:59,390] Trial 41 pruned. \n", "[I 2025-07-28 23:53:59,424] Trial 42 pruned. \n", "[I 2025-07-28 23:53:59,458] Trial 43 pruned. \n", "[I 2025-07-28 23:53:59,496] Trial 44 pruned. \n", "[I 2025-07-28 23:53:59,529] Trial 45 pruned. \n", "[I 2025-07-28 23:53:59,562] Trial 46 pruned. \n", "[I 2025-07-28 23:53:59,602] Trial 47 pruned. \n", "[I 2025-07-28 23:53:59,635] Trial 48 pruned. \n", "[I 2025-07-28 23:53:59,823] Trial 49 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-28T15:53:59.823821Z [info     ] Fold 2 best trial: value=8.0914, params={'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.47940357535905476, 'learning_rate': 0.004209266506900967, 'weight_decay': 0.000270602711220251} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1377.04, 'cpu_percent': 0.0}\n", "2025-07-28T15:53:59.835839Z [info     ] --- Performing blind test for Fold 2 on well 'C-2' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1377.04, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:00.379593Z [info     ] --- Starting CV Fold 3/3 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1377.61, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-28 23:54:00,403] A new study created in memory with name: no-name-8662d857-30ef-4699-a682-e18ed2201d73\n", "[I 2025-07-28 23:54:03,662] Trial 0 finished with value: 25.370136260986328 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.30644643837152424, 'learning_rate': 0.00012446130093648068, 'weight_decay': 0.00037915664012834}. Best is trial 0 with value: 25.370136260986328.\n", "[I 2025-07-28 23:54:04,867] Trial 1 finished with value: 11.184452533721924 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.24097478762777316, 'learning_rate': 0.0064812743370024125, 'weight_decay': 0.00032371017836974096}. Best is trial 1 with value: 11.184452533721924.\n", "[I 2025-07-28 23:54:07,709] Trial 2 finished with value: 17.52761173248291 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.5989256890857306, 'learning_rate': 0.00013416470766847957, 'weight_decay': 1.4830443679331929e-05}. Best is trial 1 with value: 11.184452533721924.\n", "[I 2025-07-28 23:54:09,458] Trial 3 finished with value: 11.88011360168457 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2730254070037954, 'learning_rate': 0.002881591985139882, 'weight_decay': 2.5527630343020573e-05}. Best is trial 1 with value: 11.184452533721924.\n", "[I 2025-07-28 23:54:11,135] Trial 4 finished with value: 11.097416877746582 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.5981982179206622, 'learning_rate': 0.004720554131410718, 'weight_decay': 2.5820335051528094e-05}. Best is trial 4 with value: 11.097416877746582.\n", "[I 2025-07-28 23:54:11,173] Trial 5 pruned. \n", "[I 2025-07-28 23:54:11,222] Trial 6 pruned. \n", "[I 2025-07-28 23:54:11,300] Trial 7 pruned. \n", "[I 2025-07-28 23:54:12,171] Trial 8 finished with value: 11.543694019317627 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.34217655941750313, 'learning_rate': 0.008796555690247664, 'weight_decay': 0.0001875978272784631}. Best is trial 4 with value: 11.097416877746582.\n", "[I 2025-07-28 23:54:12,216] Trial 9 pruned. \n", "[I 2025-07-28 23:54:13,719] Trial 10 finished with value: 10.6988844871521 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.4672278634576283, 'learning_rate': 0.0036007004204548776, 'weight_decay': 3.7494694203348164e-05}. Best is trial 10 with value: 10.6988844871521.\n", "[I 2025-07-28 23:54:13,868] Trial 11 pruned. \n", "[I 2025-07-28 23:54:13,906] Trial 12 pruned. \n", "[I 2025-07-28 23:54:13,944] Trial 13 pruned. \n", "[I 2025-07-28 23:54:13,985] Trial 14 pruned. \n", "[I 2025-07-28 23:54:14,022] Trial 15 pruned. \n", "[I 2025-07-28 23:54:14,229] Trial 16 pruned. \n", "[I 2025-07-28 23:54:14,270] Trial 17 pruned. \n", "[I 2025-07-28 23:54:14,921] Trial 18 finished with value: 10.796514511108398 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.36644259586282135, 'learning_rate': 0.00943867515943422, 'weight_decay': 8.52990903662218e-05}. Best is trial 10 with value: 10.6988844871521.\n", "[I 2025-07-28 23:54:14,965] Trial 19 pruned. \n", "[I 2025-07-28 23:54:15,007] Trial 20 pruned. \n", "[I 2025-07-28 23:54:15,044] Trial 21 pruned. \n", "[I 2025-07-28 23:54:15,890] Trial 22 finished with value: 10.669307708740234 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.20393788530533788, 'learning_rate': 0.006946463529585452, 'weight_decay': 6.295078994239704e-05}. Best is trial 22 with value: 10.669307708740234.\n", "[I 2025-07-28 23:54:16,607] Trial 23 finished with value: 10.423481941223145 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.21528571791462095, 'learning_rate': 0.006859367657316656, 'weight_decay': 7.479145807582407e-05}. Best is trial 23 with value: 10.423481941223145.\n", "[I 2025-07-28 23:54:16,652] Trial 24 pruned. \n", "[I 2025-07-28 23:54:16,702] Trial 25 pruned. \n", "[I 2025-07-28 23:54:17,551] Trial 26 finished with value: 10.767147541046143 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.23737166669479354, 'learning_rate': 0.00636754974167301, 'weight_decay': 7.249381360657203e-05}. Best is trial 23 with value: 10.423481941223145.\n", "[I 2025-07-28 23:54:17,602] Trial 27 pruned. \n", "[I 2025-07-28 23:54:17,643] Trial 28 pruned. \n", "[I 2025-07-28 23:54:17,786] Trial 29 pruned. \n", "[I 2025-07-28 23:54:17,840] Trial 30 pruned. \n", "[I 2025-07-28 23:54:18,021] Trial 31 pruned. \n", "[I 2025-07-28 23:54:18,909] Trial 32 finished with value: 11.466931819915771 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.24052874038768055, 'learning_rate': 0.0068367338215643255, 'weight_decay': 3.92402905659179e-05}. Best is trial 23 with value: 10.423481941223145.\n", "[I 2025-07-28 23:54:19,022] Trial 33 pruned. \n", "[I 2025-07-28 23:54:19,070] Trial 34 pruned. \n", "[I 2025-07-28 23:54:20,034] Trial 35 finished with value: 11.289389610290527 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.2147151429776735, 'learning_rate': 0.007739846182206647, 'weight_decay': 0.00010663543842231446}. Best is trial 23 with value: 10.423481941223145.\n", "[I 2025-07-28 23:54:20,092] Trial 36 pruned. \n", "[I 2025-07-28 23:54:20,140] Trial 37 pruned. \n", "[I 2025-07-28 23:54:20,190] Trial 38 pruned. \n", "[I 2025-07-28 23:54:20,240] Trial 39 pruned. \n", "[I 2025-07-28 23:54:20,287] Trial 40 pruned. \n", "[I 2025-07-28 23:54:20,335] Trial 41 pruned. \n", "[I 2025-07-28 23:54:20,382] Trial 42 pruned. \n", "[I 2025-07-28 23:54:20,429] Trial 43 pruned. \n", "[I 2025-07-28 23:54:21,220] Trial 44 finished with value: 11.194547176361084 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.25820824970042433, 'learning_rate': 0.00974005356702512, 'weight_decay': 5.9516813972494744e-05}. Best is trial 23 with value: 10.423481941223145.\n", "[I 2025-07-28 23:54:22,129] Trial 45 finished with value: 10.876916408538818 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.33900554578675285, 'learning_rate': 0.007443351755894377, 'weight_decay': 9.769666430627603e-05}. Best is trial 23 with value: 10.423481941223145.\n", "[I 2025-07-28 23:54:22,177] Trial 46 pruned. \n", "[I 2025-07-28 23:54:22,229] Trial 47 pruned. \n", "[I 2025-07-28 23:54:22,282] Trial 48 pruned. \n", "[I 2025-07-28 23:54:22,371] Trial 49 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-28T15:54:22.372935Z [info     ] Fold 3 best trial: value=10.4235, params={'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.21528571791462095, 'learning_rate': 0.006859367657316656, 'weight_decay': 7.479145807582407e-05} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1378.25, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:22.425846Z [info     ] --- Performing blind test for Fold 3 on well 'T-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1378.25, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:23.423612Z [info     ] Aggregating results from all CV folds... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1378.71, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:23.442171Z [info     ] Best hyperparameters found: {'cnn_filters': 8.0, 'cnn_kernel_size': 5.0, 'dropout_rate': 0.47940357535905476, 'learning_rate': 0.004209266506900967, 'mlp_units': 64.0, 'weight_decay': 0.000270602711220251} [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1378.88, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:23.468664Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.reports.cv_performance artifact_path=dnn_hybrid_training\\cv_performance_report.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1378.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:23.499663Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.reports.hyperparameter_tuning artifact_path=dnn_hybrid_training\\hyperparameter_tuning_report.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1378.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:23.549361Z [info     ] --- Stage 2: Final Model Training --- [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1378.88, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:23.560160Z [info     ] Final model training started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1379.0, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:23.834924Z [info     ] Epoch 1/150, Train Loss: 29.2845, Val Loss: 30.8861 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1466.71, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:23.899704Z [info     ] Epoch 2/150, Train Loss: 23.0472, Val Loss: 25.5398 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:23.950882Z [info     ] Epoch 3/150, Train Loss: 16.5693, Val Loss: 17.4507 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.001501Z [info     ] Epoch 4/150, Train Loss: 12.1638, Val Loss: 13.0671 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.079457Z [info     ] Epoch 5/150, Train Loss: 11.4495, Val Loss: 12.6119 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.121813Z [info     ] Epoch 6/150, Train Loss: 9.9926, Val Loss: 12.1450 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.171902Z [info     ] Epoch 7/150, Train Loss: 10.5960, Val Loss: 12.2139 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.229402Z [info     ] Epoch 8/150, Train Loss: 10.7395, Val Loss: 11.7411 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.288430Z [info     ] Epoch 9/150, Train Loss: 9.4130, Val Loss: 11.5410 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.346116Z [info     ] Epoch 10/150, Train Loss: 9.4067, Val Loss: 11.4970 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.435501Z [info     ] Epoch 11/150, Train Loss: 9.9263, Val Loss: 11.4257 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.490497Z [info     ] Epoch 12/150, Train Loss: 10.1691, Val Loss: 11.6281 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.557483Z [info     ] Epoch 13/150, Train Loss: 9.9842, Val Loss: 11.2928 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.604896Z [info     ] Epoch 14/150, Train Loss: 9.0633, Val Loss: 11.1378 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.651697Z [info     ] Epoch 15/150, Train Loss: 9.5470, Val Loss: 11.1488 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.704595Z [info     ] Epoch 16/150, Train Loss: 9.1507, Val Loss: 11.2232 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.790640Z [info     ] Epoch 17/150, Train Loss: 9.0865, Val Loss: 11.3478 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.843691Z [info     ] Epoch 18/150, Train Loss: 9.6704, Val Loss: 11.4630 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.902446Z [info     ] Epoch 19/150, Train Loss: 9.1570, Val Loss: 11.3470 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:24.958227Z [info     ] Epoch 20/150, Train Loss: 8.9756, Val Loss: 11.3825 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.015137Z [info     ] Epoch 21/150, Train Loss: 8.9093, Val Loss: 11.4471 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.068539Z [info     ] Epoch 22/150, Train Loss: 10.0026, Val Loss: 11.2855 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.164998Z [info     ] Epoch 23/150, Train Loss: 9.2627, Val Loss: 11.3490 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.212620Z [info     ] Epoch 24/150, Train Loss: 8.7139, Val Loss: 11.4317 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.262707Z [info     ] Epoch 25/150, Train Loss: 9.1337, Val Loss: 11.3472 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.307122Z [info     ] Epoch 26/150, Train Loss: 8.9981, Val Loss: 11.1283 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.366074Z [info     ] Epoch 27/150, Train Loss: 8.6960, Val Loss: 11.4034 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.423773Z [info     ] Epoch 28/150, Train Loss: 9.0632, Val Loss: 11.1059 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.507141Z [info     ] Epoch 29/150, Train Loss: 9.5590, Val Loss: 11.3909 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.576033Z [info     ] Epoch 30/150, Train Loss: 8.8403, Val Loss: 11.5607 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.625789Z [info     ] Epoch 31/150, Train Loss: 8.8863, Val Loss: 11.5389 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.674028Z [info     ] Epoch 32/150, Train Loss: 8.6711, Val Loss: 11.3974 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.723750Z [info     ] Epoch 33/150, Train Loss: 8.8362, Val Loss: 11.1772 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.771450Z [info     ] Epoch 34/150, Train Loss: 8.3276, Val Loss: 11.2585 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.858587Z [info     ] Epoch 35/150, Train Loss: 9.4384, Val Loss: 11.6569 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.912698Z [info     ] Epoch 36/150, Train Loss: 9.2799, Val Loss: 12.6756 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:25.963747Z [info     ] Epoch 37/150, Train Loss: 9.0631, Val Loss: 12.7032 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.016865Z [info     ] Epoch 38/150, Train Loss: 9.0165, Val Loss: 12.2940 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.065044Z [info     ] Epoch 39/150, Train Loss: 8.5938, Val Loss: 12.0405 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.118053Z [info     ] Epoch 40/150, Train Loss: 8.4119, Val Loss: 11.5778 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.207381Z [info     ] Epoch 41/150, Train Loss: 8.2337, Val Loss: 11.6343 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.266382Z [info     ] Epoch 42/150, Train Loss: 8.4744, Val Loss: 11.5207 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.322181Z [info     ] Epoch 43/150, Train Loss: 8.4330, Val Loss: 11.5973 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.390554Z [info     ] Epoch 44/150, Train Loss: 8.9451, Val Loss: 11.6533 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.445061Z [info     ] Epoch 45/150, Train Loss: 8.5626, Val Loss: 11.4599 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.498429Z [info     ] Epoch 46/150, Train Loss: 8.7045, Val Loss: 11.4844 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.585223Z [info     ] Epoch 47/150, Train Loss: 8.5166, Val Loss: 11.4963 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.634943Z [info     ] Epoch 48/150, Train Loss: 9.1791, Val Loss: 11.5730 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.644416Z [info     ] Early stopping triggered at epoch 48. [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.07, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.668793Z [info     ] Loaded best model state with validation loss: 11.1059 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.68, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.680086Z [info     ] Performing final evaluation on the full training dataset... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1468.73, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.736304Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.models.assets_pytorch artifact_path=dnn_hybrid_training\\model_assets_pytorch.joblib context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1469.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:26.790752Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.data_snapshots.final_training_history artifact_path=dnn_hybrid_training\\final_training_history.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1469.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:26.801310Z [info     ] ===== Hybrid DNN Training Step Finished ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1469.45, 'cpu_percent': 0.0}\n", "  - DNN 训练完成，最佳超参数: {'cnn_filters': 8.0, 'cnn_kernel_size': 5.0, 'dropout_rate': 0.47940357535905476, 'learning_rate': 0.004209266506900967, 'mlp_units': 64.0, 'weight_decay': 0.000270602711220251}\n", "2025-07-28T15:54:26.816435Z [info     ] ===== Hybrid DNN Prediction Step Started ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1469.49, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:26.824589Z [info     ] Curve names successfully resolved to DataFrame column names (Prediction). [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1469.49, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:36.951616Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1477.92, 'cpu_percent': 0.0} curve_name=K_DNN_PRED curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-28T15:54:36.990456Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_prediction.datasets.predictions artifact_path=dnn_hybrid_prediction\\dnn_hybrid_predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:37.004353Z [info     ] ===== Hybrid DNN Prediction Step Finished ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.59, 'cpu_percent': 0.0} output_curve=K_DNN_PRED predicted_samples=4689\n", "  - DNN 预测完成，已将 'K_DNN_PRED' 添加到应用集。\n"]}], "source": ["print(\"🚀 [Step 3/4] 开始执行 Hybrid DNN 基准模型...\")\n", "\n", "# --- 训练 ---\n", "well_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "dnn_train_config = DnnTrainingConfig(\n", "    n_trials=50,\n", "    max_epochs_per_trial = 100,\n", "    final_train_epochs = 150,\n", "    patience=20,\n", "    batch_size=48, #这个参数影响很大，越小性能越好\n", "    random_seed=2025)\n", "dnn_train_result = run_dnn_training_step(\n", "    config=dnn_train_config,\n", "    ctx=run_context,\n", "    train_bundle=train_bundle,\n", "    sequence_feature='T2_VALUE',\n", "    normalization_feature='PHIT_NMR',\n", "    tabular_features=['PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR'],\n", "    target_feature='K_LABEL',\n", "    grouping_feature=well_name,\n", "    t2_time_axis=t2_time_array,\n", ")\n", "print(f\"  - DNN 训练完成，最佳超参数: {dnn_train_result['best_hyperparameters']}\")\n", "\n", "# --- 预测 ---\n", "from scape.core.baselines.hybrid_dnn.artifact_handler import DnnArtifactHandler\n", "handler = DnnArtifactHandler()\n", "model_params_path = run_context.get_artifact_path(DnnArtifacts.MODEL_ASSETS.value)\n", "dnn_model_assets = handler.load_model_assets(model_params_path)\n", "dnn_pred_result = run_dnn_prediction_step(\n", "    config=DnnPredictionConfig(),\n", "    ctx=run_context,\n", "    model_assets=dnn_model_assets,\n", "    prediction_bundle=apply_bundle, # 继续修改同一个应用集Bundle\n", "    output_curve_name='K_DNN_PRED'\n", ")\n", "print(f\"  - DNN 预测完成，已将 'K_DNN_PRED' 添加到应用集。\")"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["## 6. 统一验证：PLT 盲井检验\n", "\n", "现在，`apply_bundle` 中已经包含了三个不同基准模型预测的渗透率曲线。我们将循环调用 `run_plt_analysis_step`，对每一条预测曲线进行独立的盲井检验，并通过 `prefix` 参数来区分不同模型的产物。"]}, {"cell_type": "code", "execution_count": 7, "id": "w5x6y7z8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 4/4] 开始执行统一的 PLT 盲井检验...\n", "--- 正在验证模型: SDR (曲线: K_SDR_PRED) ---\n", "2025-07-28T15:54:37.113091Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.46, 'cpu_percent': 0.0} step_name=sdr_plt_analysis\n", "2025-07-28T15:54:37.129671Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.48, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:37.135475Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.48, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-28T15:54:37.146906Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:37.168758Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:37.207422Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:37.229964Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:37.253138Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:37.291508Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:37.314363Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:37.351780Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.73, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:37.375445Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.73, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:37.403538Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:37.429876Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1471.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:37.474013Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1472.09, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:37.496453Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1472.14, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:37.657003Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1475.94, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-28T15:54:37.673067Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1475.95, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:37.685332Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1475.95, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:37.700966Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1475.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:37.763417Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1475.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:37.779207Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1475.95, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:37.801951Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1475.95, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:37.962975Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.74, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-28T15:54:38.000129Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.74, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.038486Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.74, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:38.044355Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.74, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.107694Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.74, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.130512Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.74, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:38.139229Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1478.74, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:38.251195Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.64, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-28T15:54:38.318864Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.64, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.334697Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.64, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-28T15:54:38.349965Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:38.388985Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:38.432081Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.66, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:38.451924Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.66, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:38.473414Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.67, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:38.510290Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.68, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:38.534848Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.68, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:38.578141Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.68, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.604691Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.68, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:38.621625Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.68, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.651942Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.68, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.674246Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.8, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:38.689923Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1481.83, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:38.835588Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1484.88, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-28T15:54:38.857379Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1484.88, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.880096Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1484.88, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:38.891028Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1484.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.930388Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1484.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:38.952411Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1485.01, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:38.970987Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1485.04, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:39.113674Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1488.19, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-28T15:54:39.141043Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1488.19, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:39.168977Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1488.19, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:39.168977Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1488.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:39.220340Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1488.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:39.255552Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1488.32, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:39.262296Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\baselines_run_20250728_235328\\sdr_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1488.35, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:39.398138Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.48, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-28T15:54:39.445903Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.48, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:39.469088Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.48, 'cpu_percent': 0.0}\n", "  - SDR 模型验证完成。\n", "\n", "--- 正在验证模型: TIMUR (曲线: K_TIMUR_PRED) ---\n", "2025-07-28T15:54:39.480155Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.48, 'cpu_percent': 0.0} step_name=timur_plt_analysis\n", "2025-07-28T15:54:39.491603Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.48, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:39.507008Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.48, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-28T15:54:39.547986Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:39.569696Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:39.592404Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.87, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:39.621246Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.88, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:39.646822Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.88, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:39.673402Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.88, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:39.692604Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.89, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:39.742701Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=timur_plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.92, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:39.764271Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.92, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:39.780233Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.92, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:39.819943Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1491.92, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:39.849847Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1492.04, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:39.864280Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\baselines_run_20250728_235328\\timur_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1492.05, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:39.997138Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1495.06, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-28T15:54:40.019578Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=timur_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1495.06, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:40.045126Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1495.06, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:40.088932Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1495.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:40.136329Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1495.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:40.327739Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1495.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:40.341415Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\baselines_run_20250728_235328\\timur_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1495.15, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:40.457110Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1498.51, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-28T15:54:40.474994Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1498.51, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:40.532760Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1498.52, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:40.547710Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1498.52, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:40.576530Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1498.53, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:40.592371Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1498.68, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:40.635555Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\baselines_run_20250728_235328\\timur_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1498.71, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:40.741699Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.08, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-28T15:54:40.764133Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.08, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:40.780501Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.08, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-28T15:54:40.831110Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.08, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:40.851992Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.08, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:40.869963Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.08, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:40.914360Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.09, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:40.936469Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:40.979482Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:41.001981Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:41.036181Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=timur_plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.12, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.075414Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.12, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:41.090181Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.121541Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.147410Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:41.172232Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\baselines_run_20250728_235328\\timur_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1502.27, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:41.302543Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1505.41, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-28T15:54:41.324164Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=timur_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1505.41, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.341387Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1505.41, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:41.353063Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1505.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.420042Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1505.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.435864Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1505.56, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:41.449107Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\baselines_run_20250728_235328\\timur_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1505.59, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:41.581594Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1509.06, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-28T15:54:41.601909Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1509.06, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.619920Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1509.06, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:41.633663Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1509.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.685866Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1509.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.718637Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1509.2, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:41.741975Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\baselines_run_20250728_235328\\timur_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1509.22, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:41.852679Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.68, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-28T15:54:41.874806Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.68, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:41.886295Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.68, 'cpu_percent': 0.0}\n", "  - TIMUR 模型验证完成。\n", "\n", "--- 正在验证模型: DNN (曲线: K_DNN_PRED) ---\n", "2025-07-28T15:54:41.897566Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.68, 'cpu_percent': 0.0} step_name=dnn_plt_analysis\n", "2025-07-28T15:54:41.942862Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.68, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:41.947627Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.68, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-28T15:54:41.960677Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.06, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:41.974480Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.06, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:42.002381Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:42.029244Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.08, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:42.056993Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.09, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:42.094360Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.09, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:42.113377Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.1, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:42.151474Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.1, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.177277Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.1, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:42.196565Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.1, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.227276Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.1, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.252764Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.22, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:42.264109Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1513.24, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:42.425144Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1516.27, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-28T15:54:42.447294Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1516.27, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.468940Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1516.27, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:42.481975Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1516.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.520508Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1516.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.547277Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1516.4, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:42.558527Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1516.43, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:42.723896Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.7, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-28T15:54:42.741977Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.7, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.758634Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.7, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:42.813840Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.841622Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:42.885472Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.84, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:42.896024Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.86, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:43.028189Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.27, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-28T15:54:43.088539Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.27, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.102107Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.27, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-28T15:54:43.107125Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:43.164815Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:43.184326Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:43.227207Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:43.247862Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.29, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:43.283947Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.29, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:43.302070Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.29, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:43.347170Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.31, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.365114Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.31, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:43.374176Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.411940Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.443251Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.44, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-28T15:54:43.459197Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1523.47, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:43.634989Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.23, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-28T15:54:43.651955Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.23, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.674587Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.23, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:43.686050Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.733729Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.752073Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.37, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-28T15:54:43.771856Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.4, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:43.902731Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.81, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-28T15:54:43.924970Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.81, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:43.941836Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.81, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:43.973714Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.81, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:44.008034Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.82, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:44.028449Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.96, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-28T15:54:44.058442Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\baselines_run_20250728_235328\\dnn_plt_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1531.03, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-28T15:54:44.185894Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1534.44, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-28T15:54:44.213073Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1534.44, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:44.259283Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1534.44, 'cpu_percent': 0.0}\n", "  - DNN 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"sdr\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.42857142857142866,\n", "      \"gini_capture\": 0.3953631010250631,\n", "      \"gini_lorenz\": 0.40567730664384183\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.3571428571428572,\n", "      \"gini_capture\": 0.21257750409937892,\n", "      \"gini_lorenz\": 0.1925231229973331\n", "    }\n", "  },\n", "  \"timur\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.25,\n", "      \"gini_capture\": 0.3513401288404263,\n", "      \"gini_lorenz\": 0.11098362740506895\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.642857142857143,\n", "      \"gini_capture\": -0.28910402953673175,\n", "      \"gini_lorenz\": 0.3863542083475868\n", "    }\n", "  },\n", "  \"dnn\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.5,\n", "      \"gini_capture\": 0.3513401288404263,\n", "      \"gini_lorenz\": 0.3905058989312259\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.7142857142857144,\n", "      \"gini_capture\": 0.0897681461984996,\n", "      \"gini_lorenz\": 0.6243193191749852\n", "    }\n", "  }\n", "}\n"]}], "source": ["print(\"🚀 [Step 4/4] 开始执行统一的 PLT 盲井检验...\")\n", "\n", "models_to_validate = {\n", "    \"sdr\": \"K_SDR_PRED\",\n", "    \"timur\": \"K_TIMUR_PRED\",\n", "    \"dnn\": \"K_DNN_PRED\"\n", "}\n", "\n", "validation_results = {}\n", "\n", "for model_prefix, perm_curve_name in models_to_validate.items():\n", "    print(f\"--- 正在验证模型: {model_prefix.upper()} (曲线: {perm_curve_name}) ---\")\n", "\n", "    # 1. 创建PLT分析配置\n", "    plt_config = PltAnalysisConfig()\n", "\n", "    # 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "    contribution_profile = plot_registry.get(PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT).with_updates(\n", "        title_props={\"label\": f\"Flow Contribution Crossplot ({model_prefix.upper()})\"}\n", "    )\n", "    capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE).with_updates(\n", "        title_props={\"label\": f\"Permeability Capture Curve ({model_prefix.upper()})\"}\n", "    )\n", "    lorenz_profile = plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE).with_updates(\n", "        title_props={\"label\": f\"Lorenz Curve Analysis ({model_prefix.upper()})\"}\n", "    )\n", "    plt_plot_profiles = {\n", "        PltPlotTypes.CONTRIBUTION_CROSSPLOT: contribution_profile,\n", "        PltPlotTypes.CAPTURE_CURVE: capture_profile,\n", "        PltPlotTypes.LORENZ_CURVE: lorenz_profile\n", "    }\n", "\n", "    # 3. 执行PLT分析步骤\n", "    plt_result = run_plt_analysis_step(\n", "        config=plt_config,\n", "        ctx=run_context,\n", "        prediction_bundle=apply_bundle, # 使用已包含所有预测结果的应用集\n", "        plt_bundle=plt_bundle,\n", "        permeability_curve=perm_curve_name, # 指定当前要验证的曲线\n", "        flow_rate_curve=\"QOZI\",\n", "        plot_profiles=plt_plot_profiles,\n", "        prefix=model_prefix # 使用模型名为前缀，区分产物\n", "    )\n", "    validation_results[model_prefix] = plt_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(validation_results, indent=2))"]}, {"cell_type": "markdown", "id": "cf9915b8", "metadata": {}, "source": ["## 7. 训练集岩心渗透率与预测渗透率交会图分析"]}, {"cell_type": "code", "execution_count": 8, "id": "41f99fde", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 5/5] 开始执行统一的 岩心渗透率 检验...\n", "--- 正在验证模型: SDR (曲线: K_SDR_PRED) ---\n", "2025-07-28T15:54:44.320146Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1534.49, 'cpu_percent': 0.0} step_name=sdr_perm_corr_analysis\n", "2025-07-28T15:54:44.336594Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1534.49, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:44.346706Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1534.49, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-28T15:54:44.391613Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1535.01, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:44.419708Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1535.01, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:44.435405Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1535.01, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:44.458147Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1535.03, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:44.488572Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1535.16, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:44.513986Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\baselines_run_20250728_235328\\sdr_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1535.22, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:45.271019Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.37, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\baselines_run_20250728_235328\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-28T15:54:45.291533Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.37, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:45.302699Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.37, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-28T15:54:45.330254Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.39, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:45.380769Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.39, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:45.397254Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:45.423975Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:45.470010Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.72, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:45.485243Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\baselines_run_20250728_235328\\sdr_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1545.76, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:46.128907Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.13, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\baselines_run_20250728_235328\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-28T15:54:46.164178Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:46.212067Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.14, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-28T15:54:46.242045Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.79, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:46.258975Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.8, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:46.279621Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.8, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:46.315017Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:46.336758Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:46.352960Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\baselines_run_20250728_235328\\sdr_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.12, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:47.085908Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.04, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\baselines_run_20250728_235328\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-28T15:54:47.107023Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:47.119299Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.04, 'cpu_percent': 0.0}\n", "  - SDR 模型验证完成。\n", "\n", "--- 正在验证模型: TIMUR (曲线: K_TIMUR_PRED) ---\n", "2025-07-28T15:54:47.130448Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.05, 'cpu_percent': 0.0} step_name=timur_perm_corr_analysis\n", "2025-07-28T15:54:47.141748Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.06, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:47.177878Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.06, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-28T15:54:47.207221Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.35, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:47.225551Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.35, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:47.240382Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.36, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:47.287233Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.37, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:47.324972Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.51, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:47.341869Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\baselines_run_20250728_235328\\timur_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1565.54, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:48.213006Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.77, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\baselines_run_20250728_235328\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-28T15:54:48.230550Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.77, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:48.253739Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.77, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-28T15:54:48.275766Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.81, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:48.327362Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.81, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:48.343064Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.81, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:48.375033Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.81, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:48.419545Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.82, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:48.440868Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\baselines_run_20250728_235328\\timur_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1512.82, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:49.208347Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.46, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\baselines_run_20250728_235328\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-28T15:54:49.230683Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.46, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:49.270934Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.46, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-28T15:54:49.290128Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.66, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:49.307550Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.66, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:49.324072Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:49.359477Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:49.396053Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.67, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:49.411686Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\baselines_run_20250728_235328\\timur_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1517.67, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:50.135876Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.71, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\baselines_run_20250728_235328\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-28T15:54:50.166752Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.71, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:50.178639Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.71, 'cpu_percent': 0.0}\n", "  - TIMUR 模型验证完成。\n", "\n", "--- 正在验证模型: DNN (曲线: K_DNN_PRED) ---\n", "2025-07-28T15:54:50.205290Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.71, 'cpu_percent': 0.0} step_name=dnn_perm_corr_analysis\n", "2025-07-28T15:54:50.235993Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.71, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:50.241513Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.71, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-28T15:54:50.269498Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:50.291028Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:50.323016Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:50.345103Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:50.368313Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:50.385763Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\baselines_run_20250728_235328\\dnn_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:51.074661Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.33, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\baselines_run_20250728_235328\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-28T15:54:51.091630Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.33, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:51.109432Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.34, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-28T15:54:51.130210Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.39, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:51.182553Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.39, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:51.197932Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:51.252628Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:51.288862Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.41, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:51.307888Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\baselines_run_20250728_235328\\dnn_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1527.47, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:52.040461Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1536.11, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\baselines_run_20250728_235328\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-28T15:54:52.059123Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1536.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:52.075064Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1536.11, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-28T15:54:52.091394Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1536.62, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:52.144309Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1536.62, 'cpu_percent': 0.0} path=output01\\baselines_run_20250728_235328\\dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:52.152511Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1536.63, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:52.169216Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1536.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:52.214195Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1537.02, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-28T15:54:52.236610Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\baselines_run_20250728_235328\\dnn_perm_corr_analysis context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1537.09, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-28T15:54:53.046084Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250728_235328\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\baselines_run_20250728_235328\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-28T15:54:53.088725Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250728-155328-c6b76c91\n", "2025-07-28T15:54:53.114602Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0}\n", "  - DNN 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"sdr\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.2240759989365644,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 19.6% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 38.6% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.49887985193949685,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 15.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 28.5% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6384523002796805,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 39.3% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 60.7% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"timur\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.39378216511054764,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 20.1% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 41.3% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.5835835569671806,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 10.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 32.2% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6863572337363831,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 28.6% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 60.7% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"dnn\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.498443872668893,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 8.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 19.0% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6617693744944103,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 7.4% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 19.0% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.7706811224525674,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 32.1% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 53.6% <= 90%\\u3002\"\n", "    }\n", "  }\n", "}\n"]}], "source": ["from scape.core.validation.config import PermCorrelationConfig\n", "from scape.core.validation.constants import PermCorrelationPlotProfiles\n", "from scape.core.validation.perm_corr_facade import run_perm_correlation_step\n", "\n", "\n", "print(\"🚀 [Step 5/5] 开始执行统一的 岩心渗透率 检验...\")\n", "\n", "models_to_validate = {\n", "    \"sdr\": \"K_SDR_PRED\",\n", "    \"timur\": \"K_TIMUR_PRED\",\n", "    \"dnn\": \"K_DNN_PRED\"\n", "}\n", "\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "\n", "perm_corr_results = {}\n", "\n", "for model_prefix, perm_curve_name in models_to_validate.items():\n", "    print(f\"--- 正在验证模型: {model_prefix.upper()} (曲线: {perm_curve_name}) ---\")\n", "\n", "\n", "    perm_corr_config = PermCorrelationConfig()\n", "\n", "    perm_corr_profile = original_perm_corr_profile.with_updates(\n", "        title_props={\"label\": f\"Core Perm vs. Predicted Perm ({model_prefix.upper()})\"},\n", "        save_config={\"format\": [\"png\", \"svg\"]}\n", "    )\n", "\n", "    perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle, # 使用带预测结果的应用集\n", "    right_bundle=train_bundle,\n", "    left_curve=perm_curve_name, # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=model_prefix\n", "    )\n", "\n", "    perm_corr_results[model_prefix] = perm_corr_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(perm_corr_results, indent=2))"]}, {"cell_type": "markdown", "id": "i9j0k1l2", "metadata": {}, "source": ["## 8. 总结与产物保存\n", "\n", "将包含了所有基准模型预测结果的应用集数据保存为一个新的 `.wp.xlsx` 文件，并最终化本次实验运行。"]}, {"cell_type": "code", "execution_count": 9, "id": "n2o3p4q5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-28T15:54:53.163723Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} copy_data=False new_dataset_name=apply_with_predictions operation=add_dataframe_bundle project_name=WpIdentifier('baselines_apply_result')\n", "2025-07-28T15:54:53.200264Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-28T15:54:53.219303Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=apply_with_predictions dataset_type=Continuous operation=dataset_initialization\n", "2025-07-28T15:54:53.235196Z [info     ] 成功添加 'apply_with_predictions' (WpContinuousDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('baselines_apply_result')\n", "2025-07-28T15:54:53.246406Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} dataset_count=1 file_path=output01\\baselines_run_20250728_235328\\baselines_apply_result.wp.xlsx project_name=WpIdentifier('baselines_apply_result') save_head_info=True save_well_map=True\n", "2025-07-28T15:54:53.282456Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1530.25, 'cpu_percent': 0.0} curve_count=12 dataset_name=WpIdentifier('apply_with_predictions') dataset_type=Continuous df_shape=(4689, 75)\n", "2025-07-28T15:54:54.192536Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1593.77, 'cpu_percent': 0.0} dataset_name=WpIdentifier('apply_with_predictions') processing_time=0.91\n", "2025-07-28T15:54:54.212976Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1591.12, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-28T15:54:54.225555Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1591.12, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:54.236182Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1591.12, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-28T15:54:59.673975Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1630.89, 'cpu_percent': 0.0}\n", "2025-07-28T15:54:59.681641Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1630.89, 'cpu_percent': 0.0}\n", "2025-07-28T15:55:02.771976Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1633.2, 'cpu_percent': 0.0} file_path=output01\\baselines_run_20250728_235328\\baselines_apply_result.wp.xlsx processing_time=9.526 project_name=WpIdentifier('baselines_apply_result')\n", "✅ 包含所有预测结果的应用集已保存至: output01\\baselines_run_20250728_235328\\baselines_apply_result.wp.xlsx\n", "2025-07-28T15:55:02.819129Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1633.2, 'cpu_percent': 0.0} duration_seconds=94.166 manifest_path=output01\\baselines_run_20250728_235328\\manifest.json operation=finalize run_id=20250728-155328-c6b76c91 status=RUNNING\n", "✅ 运行已最终化，所有记录已保存。\n"]}], "source": ["# --- 保存最终结果 ---\n", "final_project = WpWellProject(name=\"baselines_apply_result\")\n", "final_project.add_dataframe_bundle(\"apply_with_predictions\", apply_bundle)\n", "\n", "writer = WpExcelWriter()\n", "final_result_path = run_context.run_dir / \"baselines_apply_result.wp.xlsx\"\n", "writer.write(final_project, final_result_path, apply_formatting=True)\n", "print(f\"✅ 包含所有预测结果的应用集已保存至: {final_result_path}\")\n", "\n", "# --- 最终化运行 ---\n", "run_context.finalize()\n", "print(\"✅ 运行已最终化，所有记录已保存。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}