"""scape.core.baselines.hybrid_dnn.internal.model_builder - Hybrid DNN模型构建器

本模块负责构建用于渗透率预测的混合输入深度学习模型。

Architecture
------------
层次/依赖: scape/core/baselines/hybrid_dnn/internal层，被tuning和training规程调用
设计原则: 遵循方法说明书，将模型架构与训练逻辑解耦

Classes:
    HybridDnnPermModel: 混合输入模型 (CNN + MLP)

References:
    - 架构灵感来自 `scape.core.obmiq`
"""
from __future__ import annotations

from typing import Any, Dict

import torch
import torch.nn as nn


class HybridDnnPermModel(nn.Module):
    """用于渗透率预测的混合输入DNN模型。

    该模型包含两个分支：
    1. 一个1D-CNN分支，用于处理序列数据（如T2谱）。
    2. 一个MLP分支，用于处理表格化数据（如常规测井曲线）。
    两个分支的输出被融合后，通过一个回归头进行最终预测。
    """

    def __init__(self, hp: Dict[str, Any], num_tabular_features: int):
        """初始化混合DNN渗透率模型。

        Args:
            hp: 包含模型超参数的字典，由Optuna提供。
            num_tabular_features: 表格输入特征的数量。
        """
        super().__init__()

        # --- 类型转换以增强稳健性 ---
        # 确保从配置中获取的超参数是正确的整数类型，以防止因上游
        # 数据处理（如Pandas聚合）导致的类型变化（例如，int变为float）。
        cnn_filters = int(hp["cnn_filters"])
        cnn_kernel_size = int(hp["cnn_kernel_size"])
        mlp_units = int(hp["mlp_units"])
        dropout_rate = float(hp["dropout_rate"])

        # CNN 分支
        self.cnn_branch = nn.Sequential(
            nn.Conv1d(
                in_channels=1,
                out_channels=cnn_filters,
                kernel_size=cnn_kernel_size,
            ),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(cnn_filters, 16),
            nn.ReLU(),
        )

        # MLP 分支
        self.mlp_branch = nn.Sequential(
            nn.Linear(num_tabular_features, mlp_units),
            nn.ReLU(),
        )

        # 回归头
        fusion_dim = 16 + mlp_units  # cnn_output_dim + mlp_output_dim
        self.regression_head = nn.Sequential(
            nn.BatchNorm1d(fusion_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(fusion_dim, 16),
            nn.ReLU(),
            nn.Linear(16, 1),  # 最终输出层，预测单个渗透率值
        )

    def forward(self, x: Dict[str, torch.Tensor]) -> torch.Tensor:
        """定义模型的前向传播路径。"""
        sequence_input = x["sequence_input"]
        tabular_input = x["tabular_input"]

        cnn_out = self.cnn_branch(sequence_input)
        mlp_out = self.mlp_branch(tabular_input)

        combined = torch.cat([cnn_out, mlp_out], dim=1)
        prediction = self.regression_head(combined)

        return prediction
