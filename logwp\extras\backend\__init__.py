"""logwp.extras.backend - 公共后端服务

提供统一的CPU/GPU计算后端服务，为上层应用（如NMR、PCA）提供与后端无关的计算接口。

Architecture
------------
层次/依赖: extras层公共服务，被其他extras模块和scape_core模块依赖
设计原则: 服务对象模式、接口协议驱动、后端透明
性能特征: 最小化后端检测开销，支持可选的GPU加速

核心API:
- create_backend_service_from_data: 根据输入数据自动创建后端服务实例
- create_backend_service_by_name: 根据名称明确创建后端服务实例
- BackendService: 后端服务接口协议
"""

from __future__ import annotations

from .internal.factory import (
    create_backend_service_by_name,
    create_backend_service_from_data,
    is_gpu_available,
)
from .service import BackendService
from .service import RandomService

__all__ = [
    "create_backend_service_by_name",
    "create_backend_service_from_data",
    "is_gpu_available",
    "BackendService",
    "RandomService",
]
