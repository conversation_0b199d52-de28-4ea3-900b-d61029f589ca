from __future__ import annotations

from typing import Any, Dict, <PERSON>ple

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import RFECV
from sklearn.impute import SimpleImputer
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import KFold, LeaveOneGroupOut, RandomizedSearchCV
from sklearn.pipeline import Pipeline

from logwp.infra import get_logger

from ..config import ObmiqBaselinesTrainingConfig
from .model_definitions import get_candidate_models_and_param_spaces

logger = get_logger(__name__)


def run_nested_cv(
    X: pd.DataFrame,
    y: pd.Series,
    groups: pd.Series,
    config: ObmiqBaselinesTrainingConfig,
    target_name: str,
) -> <PERSON><PERSON>[Dict[str, Any], pd.DataFrame]:
    """
    执行嵌套交叉验证，以无偏地评估模型并选择最佳融合组合。

    该流程严格遵循开发计划，使用scikit-learn的Pipeline机制来整合
    数据填充、特征选择(RFECV)和模型训练，以从根本上防止数据泄露。

    Args:
        X: 特征DataFrame。
        y: 目标Series。
        groups: 用于LOWO外层循环的分组Series (井号)。
        config: 训练配置对象。
        target_name: 当前预测的目标名称，用于日志记录。

    Returns:
        一个元组，包含:
        - results (dict): 包含评估结果和最佳模型融合策略的字典。
        - lowo_cv_predictions_df (pd.DataFrame): 包含所有样本在LOWO-CV中
          作为验证集时的真实值和加权预测值的DataFrame。
    """
    logger.info(f"开始为目标 '{target_name}' 执行嵌套交叉验证...")

    candidate_models = get_candidate_models_and_param_spaces(config)
    all_model_rmse_scores = {name: [] for name in candidate_models}
    all_model_r2_scores = {name: [] for name in candidate_models}
    all_model_predictions = {name: [] for name in candidate_models}

    # 外层循环: 留一井交叉验证 (LOWO)
    lowo_cv = LeaveOneGroupOut()
    n_splits = lowo_cv.get_n_splits(groups=groups)

    for fold_idx, (train_idx, test_idx) in enumerate(lowo_cv.split(X, y, groups)):
        X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
        y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
        held_out_well = groups.iloc[test_idx].unique()[0]
        logger.info(f"  外层折叠 {fold_idx + 1}/{n_splits}: 验证井 '{held_out_well}'")

        # 内层循环: 对每个候选模型进行优化
        for model_name, model_details in candidate_models.items():
            logger.debug(f"    正在评估模型: {model_name}")

            # --- 防数据泄露核心: 构建完整的处理流水线 ---
            rfe_base_estimator = RandomForestRegressor(n_estimators=50, random_state=config.random_seed, n_jobs=1)
            inner_cv = KFold(n_splits=config.inner_cv_folds, shuffle=True, random_state=config.random_seed)

            pipeline = Pipeline([
                ("imputer", SimpleImputer(strategy="median")),
                # Critical Fix: Set n_jobs=1 for the inner RFECV to prevent nested parallelism and memory explosion.
                ("selector", RFECV(
                    estimator=rfe_base_estimator,
                    step=1, cv=inner_cv,
                    scoring="neg_root_mean_squared_error",
                    n_jobs=1  # The main parallelism is handled by RandomizedSearchCV's n_jobs=-1.
                )),
                ("model", model_details["estimator"]),
            ])

            # --- 超参数寻优 ---
            random_search = RandomizedSearchCV(
                estimator=pipeline,
                param_distributions=model_details["param_space"],
                n_iter=config.n_iter_random_search,
                cv=inner_cv,
                scoring="neg_root_mean_squared_error",
                random_state=config.random_seed,
                n_jobs=-1,
            )
            random_search.fit(X_train, y_train)

            # --- 外层评估 ---
            best_pipeline_for_fold = random_search.best_estimator_
            y_pred = best_pipeline_for_fold.predict(X_test)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            r2 = r2_score(y_test, y_pred)
            all_model_rmse_scores[model_name].append(rmse)
            all_model_r2_scores[model_name].append(r2)

            # 关键修改：保留原始特征和索引，以实现与obmiq对等的详细产物
            # 创建一个包含原始特征、真实值和预测值的结果帧
            fold_pred_df = X_test.copy()
            fold_pred_df["actual"] = y_test
            fold_pred_df["predicted"] = y_pred
            fold_pred_df[groups.name] = groups.loc[test_idx] # 明确添加分组列
            all_model_predictions[model_name].append(fold_pred_df)
            logger.info(f"    - {model_name} 在井 '{held_out_well}' 上的RMSE: {rmse:.4f}")

    logger.info("所有交叉验证折叠完成，正在聚合结果...")
    avg_rmse_scores = {name: np.mean(scores) for name, scores in all_model_rmse_scores.items()}
    sorted_models = sorted(avg_rmse_scores.items(), key=lambda item: item[1])

    if len(sorted_models) < 2:
        raise ValueError("评估的模型数量少于2，无法进行融合。")

    m1_name, m1_rmse = sorted_models[0]
    m2_name, m2_rmse = sorted_models[1]

    s1 = 1 / m1_rmse if m1_rmse > 1e-9 else 1e9
    s2 = 1 / m2_rmse if m2_rmse > 1e-9 else 1e9
    w1 = s1 / (s1 + s2)
    w2 = s2 / (s1 + s2)

    # 准备返回的产物
    results = {
        "best_models": [{"name": m1_name, "weight": w1}, {"name": m2_name, "weight": w2}],
        "avg_rmse_scores": avg_rmse_scores,
        "all_rmse_scores": all_model_rmse_scores,
        "all_r2_scores": all_model_r2_scores,
    }
    logger.info(f"最佳模型组合: {m1_name} (权重: {w1:.2f}) 和 {m2_name} (权重: {w2:.2f})")

    # 生成用于泛化能力评估的合并交叉验证预测结果
    logger.info("正在为泛化能力评估生成合并的交叉验证预测...")
    concatenated_predictions = {
        name: pd.concat(preds) for name, preds in all_model_predictions.items()
    }
    m1_preds_df = concatenated_predictions[m1_name].sort_index()
    m2_preds_df = concatenated_predictions[m2_name].sort_index()

    # 关键修改：使用m1的预测结果作为基础（它已包含所有原始特征和真实值），
    # 然后计算并覆盖加权后的预测列。
    lowo_cv_predictions_df = m1_preds_df.drop(columns=["predicted"])
    lowo_cv_predictions_df["predicted"] = (
        m1_preds_df["predicted"] * w1 + m2_preds_df["predicted"] * w2
    )

    return results, lowo_cv_predictions_df
