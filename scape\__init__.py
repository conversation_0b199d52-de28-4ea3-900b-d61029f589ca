from __future__ import annotations

"""scape - SCAPE核心算法包群

Santos Carbonate Adaptive Permeability Estimator核心算法实现。

Architecture
------------
层次/依赖: SCAPE五层架构的算法层，依赖logwp_extras和logwp
设计原则: 科学计算、GPU加速、类型安全、模块化设计
性能特征: 10-15倍GPU加速、异步计算、内存优化

Core Features
-------------
- **三大核心算法**: OBMIQ、FOSTER-NMR、SWIFT-PSO
- **GPU计算引擎**: 统一CPU/GPU计算接口，自动回退
- **科学计算**: 数值稳定性、物理约束、实验可重现
- **研究工具**: 实验管理、结果分析、报告生成
- **应用接口**: CLI、Notebook、交互组件

Package Structure
-----------------
- core/: 核心算法实现
  - obmiq/: OBMIQ机器学习算法
  - foster_nmr/: FOSTER-NMR物理模型
  - swift_pso/: SWIFT-PSO优化算法
  - compute/: 统一GPU计算引擎
- study/: 研究工具层
  - config/: 实验配置管理
  - runner/: 研究执行引擎
  - analysis/: 结果分析工具
  - report/: 报告生成器

Examples
--------
>>> import scape
>>> from logwp import read_wp_excel
>>>
>>> # 读取数据
>>> project = read_wp_excel("data/santos_field.wp.xlsx")
>>> logs_dataset = project.get_dataset("OBMIQ_logs")
>>> label_dataset = project.get_dataset("K_Label")
>>>
>>> # OBMIQ训练
>>> obmiq_trainer = scape.ScapeObmiqTrainer()
>>> obmiq_result = obmiq_trainer.fit(logs_dataset, label_dataset)
>>>
>>> # FOSTER-NMR计算
>>> foster_calc = scape.ScapeFosterCalculator()
>>> foster_result = foster_calc.compute_permeability(logs_dataset)
>>>
>>> # SWIFT-PSO优化
>>> pso_optimizer = scape.ScapePsoOptimizer()
>>> pso_result = pso_optimizer.optimize(logs_dataset, foster_result)
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "SCAPE Team"

__all__ = [
    # 版本信息
    "__version__",
    "__author__",

    # 核心算法 - OBMIQ
    "ScapeObmiqTrainer",
    "ScapeObmiqValidator",
    "ScapeObmiqPredictor",

    # 核心算法 - FOSTER-NMR
    "ScapeFosterCalculator",
    "ScapeFosterCalibrator",
    "ScapeFosterValidator",

    # 核心算法 - SWIFT-PSO
    "ScapePsoOptimizer",
    "ScapePsoParticle",
    "ScapePsoObjective",

    # 统一计算引擎
    "ScapeComputeEngine",
    "ScapeGpuManager",
    "ScapePerformanceMonitor",

    # 研究工具
    "StudyConfig",
    "StudyRunner",
    "StudyAnalyzer",
    "StudyReporter",

    # 工作流
    "run_full_scape_workflow",
    "run_obmiq_stage",
    "run_foster_stage",
    "run_pso_stage",
]


def __getattr__(name: str) -> object:
    """延迟导入SCAPE算法组件。

    Architecture
    ------------
    层次/依赖: 算法层门面，提供统一API入口
    设计原则: 延迟导入、GPU检测、算法隔离
    性能特征: 按需加载、设备优化、内存管理
    """
    # OBMIQ算法
    if name == "ScapeObmiqTrainer":
        from scape.core.obmiq.trainer import ScapeObmiqTrainer
        return ScapeObmiqTrainer
    elif name == "ScapeObmiqValidator":
        from scape.core.obmiq.validator import ScapeObmiqValidator
        return ScapeObmiqValidator
    elif name == "ScapeObmiqPredictor":
        from scape.core.obmiq.predictor import ScapeObmiqPredictor
        return ScapeObmiqPredictor

    # FOSTER-NMR算法
    elif name == "ScapeFosterCalculator":
        from scape.core.foster_nmr.calculator import ScapeFosterCalculator
        return ScapeFosterCalculator
    elif name == "ScapeFosterCalibrator":
        from scape.core.foster_nmr.calibrator import ScapeFosterCalibrator
        return ScapeFosterCalibrator
    elif name == "ScapeFosterValidator":
        from scape.core.foster_nmr.validator import ScapeFosterValidator
        return ScapeFosterValidator

    # SWIFT-PSO算法
    elif name == "ScapePsoOptimizer":
        from scape.core.swift_pso.optimizer import ScapePsoOptimizer
        return ScapePsoOptimizer
    elif name == "ScapePsoParticle":
        from scape.core.swift_pso.particle import ScapePsoParticle
        return ScapePsoParticle
    elif name == "ScapePsoObjective":
        from scape.core.swift_pso.objective import ScapePsoObjective
        return ScapePsoObjective

    # 计算引擎
    elif name == "ScapeComputeEngine":
        from scape.core.compute.engine import ScapeComputeEngine
        return ScapeComputeEngine
    elif name == "ScapeGpuManager":
        from scape.core.compute.gpu_manager import ScapeGpuManager
        return ScapeGpuManager
    elif name == "ScapePerformanceMonitor":
        from scape.core.compute.performance import ScapePerformanceMonitor
        return ScapePerformanceMonitor

    # 研究工具
    elif name == "StudyConfig":
        from scape.study.config import StudyConfig
        return StudyConfig
    elif name == "StudyRunner":
        from scape.study.runner import StudyRunner
        return StudyRunner
    elif name == "StudyAnalyzer":
        from scape.study.analyzer import StudyAnalyzer
        return StudyAnalyzer
    elif name == "StudyReporter":
        from scape.study.reporter import StudyReporter
        return StudyReporter

    # 工作流函数
    elif name in ("run_full_scape_workflow", "run_obmiq_stage", "run_foster_stage", "run_pso_stage"):
        from scape.workflows import run_full_scape_workflow, run_obmiq_stage, run_foster_stage, run_pso_stage
        return locals()[name]

    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
