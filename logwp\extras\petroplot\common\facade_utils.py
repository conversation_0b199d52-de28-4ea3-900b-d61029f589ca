"""logwp.extras.petroplot.common.facade_utils - 通用门面辅助函数

本模块提供了所有 petroplot 组件在门面层可复用的辅助函数。
"""

from typing import Dict, List
from pathlib import Path
import plotly.graph_objects as go

from logwp.extras.tracking import RunContext
from logwp.extras.plotting import PlotProfile, SaveConfig


def save_and_register_plots(
    ctx: RunContext,
    figs_dict: Dict[str, go.Figure],
    profile: PlotProfile,
    step_dir: Path,
    prefix: str,
    plot_base_name_template: str,
    artifact_plot_prefix: str,
    output_formats: List[str],
) -> None:
    """
    一个通用的辅助函数，用于保存和注册由绘图组件生成的图表。

    它封装了以下通用逻辑：
    - 遍历图表字典。
    - 根据模板生成唯一的文件名。
    - 从PlotProfile计算输出图像的物理尺寸和分辨率。
    - 将图表保存为多种格式（html, png, svg等）。
    - 将每个图表文件注册为可追踪的产物。

    Args:
        ctx: 当前运行的上下文。
        figs_dict: 包含图表对象的字典。
        profile: 绘图样式配置。
        step_dir: 步骤的输出目录。
        prefix: 用于保证产物名称唯一性的前缀。
        plot_base_name_template: 文件名模板, e.g., "ternary_plot_{split_value}"。
        artifact_plot_prefix: 产物逻辑名称的前缀, e.g., "plots.ternary"。
        output_formats: 输出格式列表, e.g., ["html", "png"]。
    """
    for split_value, fig in figs_dict.items():
        safe_split_value = str(split_value).replace('/', '_').replace('\\', '_').replace(' ', '_')
        plot_base_name = plot_base_name_template.format(split_value=safe_split_value)

        save_cfg = profile.save_config or SaveConfig()

        width_px = int(save_cfg.width * save_cfg.dpi) if save_cfg.width is not None else profile.figure_props.get("width")
        height_px = int(save_cfg.height * save_cfg.dpi) if save_cfg.height is not None else profile.figure_props.get("height")
        scale = 1.0 if save_cfg.width is not None else (save_cfg.dpi / 96.0 if save_cfg.dpi > 0 else 1.0)

        for fmt in output_formats:
            output_file = step_dir / f"{plot_base_name}.{fmt}"
            if fmt == "html":
                fig.write_html(output_file)
            else:
                fig.write_image(output_file, width=width_px, height=height_px, scale=scale)

            artifact_name = f"{prefix}.{artifact_plot_prefix}_{safe_split_value}_{fmt}"
            ctx.register_artifact(output_file.relative_to(ctx.run_dir), artifact_name)
