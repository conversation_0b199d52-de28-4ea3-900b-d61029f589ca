from __future__ import annotations

"""logwp.models.curve.service.curve_expansion - 曲线名称展开服务

实现混合紧凑曲线名称列表的智能展开功能，支持一维曲线、二维组合曲线基础名称和二维组合曲线元素的混合输入。
遵循SAD文档的内部服务层设计模式（Utility/Helper Pattern）。

Architecture
------------
层次/依赖: curve模块服务层，依赖curve.metadata、constants、exceptions
设计原则: 无状态服务、职责分离、类型安全
性能特征: 高效算法、内存优化、批量处理

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
- 《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata服务层重构
"""

from typing import TYPE_CHECKING

from logwp.models.constants import WpStatisticsKeys
from logwp.models.exceptions import WpCurveMetadataError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.curve.metadata import (
        CurveBasicAttributes, CurveListAnalysis, CurveItemType, CurveExpansionMode
    )

logger = get_logger(__name__)


def expand_curve_names(
    curve_list: list[str],
    mode: 'CurveExpansionMode',
    curves_dict: dict[str, 'CurveBasicAttributes'],
    analyze_curve_list_func,
    translate_2d_curve_element_func
) -> list[str]:
    """展开混合紧凑曲线名称列表。

    将用户提供的混合紧凑曲线列表展开为指定模式的列表。支持一维曲线、二维组合曲线基础名称
    和二维组合曲线元素的混合输入。

    Args:
        curve_list: 混合紧凑曲线列表，支持：
            - 一维曲线：['GR', 'DEN']
            - 二维组合曲线基础名称：['T2_VALUE'] （会展开为所有元素）
            - 二维组合曲线元素：['T2_VALUE[1]'] （不会展开，直接使用）
            - 混合格式：['GR', 'T2_VALUE', 'T2_TIME[1]']
        mode: 展开模式
            - EXPANDED: 展开曲线列表，如 ['GR', 'T2_VALUE[1]', 'T2_VALUE[2]', ...]
            - DATAFRAME: DataFrame列名列表，如 ['GR', 'T2_VALUE_1', 'T2_VALUE_2', ...]
        curves_dict: 曲线字典，用于查找曲线信息
        analyze_curve_list_func: 曲线列表分析函数
        translate_2d_curve_element_func: 二维组合曲线元素转换函数

    Returns:
        list[str]: 展开后的曲线名称列表

    Raises:
        WpCurveMetadataError: 当曲线列表中包含未知曲线或存在二维组合曲线表示冲突时抛出

    Examples:
        >>> # 传统紧凑列表（保持向后兼容）
        >>> compact_list = ['GR', 'DEN', 'T2_VALUE']
        >>> expanded = expand_curve_names(compact_list, CurveExpansionMode.EXPANDED, ...)
        >>> print(expanded)  # ['GR', 'DEN', 'T2_VALUE[1]', 'T2_VALUE[2]', 'T2_VALUE[3]', 'T2_VALUE[4]']

        >>> # 混合列表（新功能）
        >>> mixed_list = ['GR', 'T2_VALUE', 'T2_TIME[1]']
        >>> expanded = expand_curve_names(mixed_list, CurveExpansionMode.EXPANDED, ...)
        >>> print(expanded)  # ['GR', 'T2_VALUE[1]', 'T2_VALUE[2]', 'T2_VALUE[3]', 'T2_VALUE[4]', 'T2_TIME[1]']

    References:
        《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata服务层重构
    """
    # 导入需要的类型（避免循环导入）
    from logwp.models.curve.metadata import CurveItemType, CurveExpansionMode

    # 第一步：分析曲线列表
    analysis = analyze_curve_list_func(curve_list)

    # 第二步：检查冲突
    if analysis.has_conflicts:
        conflict_details = []
        for conflict in analysis.conflicts:
            conflict_details.append(
                f"'{conflict.base_curve_name}': 紧凑形式 '{conflict.compact_form}' "
                f"与元素形式 {conflict.element_forms} 同时存在"
            )

        raise WpCurveMetadataError(
            f"检测到二维组合曲线表示冲突。请选择使用紧凑形式或元素形式，不要混用。冲突详情: {'; '.join(conflict_details)}",
            context=ErrorContext(
                operation=WpStatisticsKeys.OPERATION_EXPAND_CURVE_NAMES,
                additional_info={
                    "requested_curve_list": curve_list,
                    "expansion_mode": mode.value,
                    "conflicts": [
                        {
                            "base_curve": c.base_curve_name,
                            "compact_form": c.compact_form,
                            "element_forms": c.element_forms
                        } for c in analysis.conflicts
                    ]
                }
            )
        )

    # 第三步：检查不存在的曲线
    if analysis.not_found_curves:
        raise WpCurveMetadataError(
            f"曲线列表中包含不存在的曲线: {analysis.not_found_curves}",
            context=ErrorContext(
                operation=WpStatisticsKeys.OPERATION_EXPAND_CURVE_NAMES,
                additional_info={
                    "available_curves": list(curves_dict.keys()),
                    "requested_curve_list": curve_list,
                    "not_found_curves": analysis.not_found_curves,
                    "expansion_mode": mode.value
                }
            )
        )

    # 第四步：智能展开
    expanded_names = []
    for item in analysis.curve_items:
        if item.item_type == CurveItemType.ONE_DIMENSIONAL:
            # 一维曲线：根据模式选择名称
            curve_attrs = curves_dict[item.curve_name]
            if mode == CurveExpansionMode.EXPANDED:
                expanded_names.append(curve_attrs.name)
            elif mode == CurveExpansionMode.DATAFRAME:
                expanded_names.append(curve_attrs.dataframe_column_name)

        elif item.item_type == CurveItemType.TWO_DIMENSIONAL:
            # 二维组合曲线基础名称：展开为所有元素
            curve_attrs = curves_dict[item.curve_name]
            if mode == CurveExpansionMode.EXPANDED:
                if curve_attrs.element_names:
                    expanded_names.extend(curve_attrs.element_names)
            elif mode == CurveExpansionMode.DATAFRAME:
                if curve_attrs.dataframe_element_names:
                    expanded_names.extend(curve_attrs.dataframe_element_names)

        elif item.item_type == CurveItemType.TWO_DIMENSIONAL_ELEMENT:
            # 二维组合曲线元素：不展开，直接使用
            if mode == CurveExpansionMode.EXPANDED:
                expanded_names.append(item.curve_name)
            elif mode == CurveExpansionMode.DATAFRAME:
                # 转换为DataFrame友好名称
                friendly_name = translate_2d_curve_element_func(item.curve_name)
                expanded_names.append(friendly_name)

    logger.debug(
        "曲线名称展开完成",
        operation=WpStatisticsKeys.OPERATION_EXPAND_CURVE_NAMES,
        input_curve_count=len(curve_list),
        output_curve_count=len(expanded_names),
        expansion_mode=mode.value,
        input_curves=curve_list,
        output_curves=expanded_names
    )

    return expanded_names
