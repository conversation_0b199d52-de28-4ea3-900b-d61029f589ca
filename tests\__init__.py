from __future__ import annotations

"""tests - SCAPE项目测试套件

SCAPE项目的完整测试套件，遵循pytest标准结构。

Architecture
------------
层次/依赖: 测试层，验证所有包的功能正确性
设计原则: 科学正确性优先、快速验证、最小可行测试
性能特征: 快速执行、算法验证、GPU一致性

Test Structure
--------------
- test_logwp/: logwp包测试
  - test_constants.py: 常量系统测试
  - test_exceptions.py: 异常体系测试
  - test_datasets/: 数据集层测试
  - test_models/: 模型层测试
  - test_types/: 类型系统测试
  - test_utils/: 工具层测试
  - test_config/: 配置系统测试
- test_data/: 测试数据和数据生成器
- conftest.py: pytest配置和fixture

Examples
--------
>>> # 运行所有测试
>>> pytest tests/

>>> # 运行特定包测试
>>> pytest tests/test_logwp/

>>> # 运行特定功能测试
>>> pytest tests/test_logwp/test_datasets/
"""

__version__ = "1.0.0"
__author__ = "SCAPE Team"
