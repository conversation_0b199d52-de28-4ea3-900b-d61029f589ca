{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SCAPE数据准备,数据源:`santos_data_v3.wp.xlsx`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:09:58.223453Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 384.41, 'cpu_percent': 0.0} file_path=santos_data_v3.wp.xlsx\n", "2025-07-29T04:09:58.316545Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 385.54, 'cpu_percent': 0.0} file_path=santos_data_v3.wp.xlsx file_size_mb=27.29 sheet_count=8\n", "2025-07-29T04:09:58.335346Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 385.55, 'cpu_percent': 0.0} project_name=santos_data_v3\n", "2025-07-29T04:09:58.351588Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 385.56, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data_v3\n", "2025-07-29T04:09:58.372396Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 385.59, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T04:09:58.386781Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 385.6, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T04:09:58.403749Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 385.65, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-07-29T04:09:58.424957Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 385.65, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T04:09:58.450496Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 386.0, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:09:58.503072Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 386.38, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=62 well_curves=1\n", "2025-07-29T04:10:37.729174Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 454.46, 'cpu_percent': 0.0} shape=(16303, 251) sheet_name=Logs\n", "2025-07-29T04:10:37.796052Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 454.77, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-07-29T04:10:37.806566Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 454.77, 'cpu_percent': 0.0} curve_count=62 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 251) processing_time=39.365\n", "2025-07-29T04:10:37.839885Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 454.82, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:37.871919Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 454.82, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=91 well_curves=1\n", "2025-07-29T04:10:42.438863Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.11, 'cpu_percent': 0.0} shape=(4502, 91) sheet_name=OBMIQ_Pred\n", "2025-07-29T04:10:42.479302Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.12, 'cpu_percent': 0.0} dataset_name=WpIdentifier('OBMIQ_Pred')\n", "2025-07-29T04:10:42.501252Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.12, 'cpu_percent': 0.0} curve_count=91 dataset_name=OBMIQ_Pred dataset_type=Continuous df_shape=(4502, 91) processing_time=4.667\n", "2025-07-29T04:10:42.531405Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.13, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:42.557632Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.13, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T04:10:42.630286Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} shape=(356, 9) sheet_name=K_Label\n", "2025-07-29T04:10:42.650320Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-07-29T04:10:42.660347Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(356, 9) processing_time=0.134\n", "2025-07-29T04:10:42.685051Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T04:10:42.711732Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T04:10:42.738945Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} shape=(15, 4) sheet_name=PLT\n", "2025-07-29T04:10:42.754462Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T04:10:42.763965Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(15, 4) processing_time=0.089\n", "2025-07-29T04:10:42.792674Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:42.810211Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.18, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T04:10:42.832761Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.21, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-07-29T04:10:42.845030Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.22, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-29T04:10:42.862665Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.22, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.074\n", "2025-07-29T04:10:42.882400Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.22, 'cpu_percent': 0.0} dataset_count=5 has_head_info=True has_well_map=True\n", "2025-07-29T04:10:42.896772Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.22, 'cpu_percent': 0.0} dataset_count=5\n", "2025-07-29T04:10:42.906642Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.22, 'cpu_percent': 0.0} dataset_count=5 file_path=santos_data_v3.wp.xlsx processing_time=44.683 project_name=WpIdentifier('santos_data_v3')\n", "2025-07-29T04:10:42.920320Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.22, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data_v3'}\n", "2025-07-29T04:10:42.968830Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.32, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 5}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data_v3\n", "📅 数据集: dict_keys(['Logs', 'OBMIQ_Pred', 'K_Label', 'PLT', 'K_<PERSON>'])\n"]}], "source": ["data_file_path = \"./santos_data_v3.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 数据集: {project.datasets.keys()}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 合并K_Label和Logs数据集生成SCAPE训练数据"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 定义K_Label数据集的曲线列表\n", "k_label_curves = [\n", "     'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "]\n", "\n", "# 定义Logs数据集的曲线列表\n", "logs_curves = [\n", "    'T2LM',\n", "    'T2_P50',\n", "    'PHIT_NMR',\n", "    'PHI_T2_DIST'\n", "]\n", "\n", "obmiq_pred_curves = [\n", "    'DT2_P50',\n", "    'DPHIT_NMR',\n", "]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始合并K_Label和Logs数据集...\n", "\n", "📍 执行左对齐合并...\n", "2025-07-29T04:10:43.155410Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.34, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] left_dataset=K_Label operation=merge_datasets_left_aligned right_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST'] right_dataset=Logs\n", "2025-07-29T04:10:43.176559Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.34, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=K_Label target_dataset=K_Label_left_temp\n", "2025-07-29T04:10:43.206384Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.34, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL_TYPE', 'PZI', 'WELL_NO', 'K_LABEL', 'MD'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL_TYPE', 'PZI', 'WELL_NO', 'K_LABEL', 'MD']\n", "2025-07-29T04:10:43.229513Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.48, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_K_Label_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:43.253847Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.56, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T04:10:43.277783Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.56, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=K_Label_left_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-07-29T04:10:43.304897Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.56, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:43.322578Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.56, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=5 source_rows=356 target_rows=356\n", "2025-07-29T04:10:43.340673Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.56, 'cpu_percent': 0.0} curve_count=4 has_query=False operation=extract_curves source_dataset=Logs target_dataset=Logs_right_temp\n", "2025-07-29T04:10:43.359559Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.56, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=6 input_curves=['T2_P50', 'PHIT_NMR', 'PHI_T2_DIST', 'T2LM', 'WELL_NO', 'MD'] operation=extract_metadata output_curve_count=6 output_curves=['T2_P50', 'PHIT_NMR', 'PHI_T2_DIST', 'T2LM', 'WELL_NO', 'MD']\n", "2025-07-29T04:10:43.476386Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 501.55, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_Logs_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:43.503633Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.26, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-29T04:10:43.522651Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.26, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=Logs_right_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-29T04:10:43.546126Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.26, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:43.560976Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.26, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] operation=extract_curves selected_columns=69 source_rows=16303 target_rows=16303\n", "2025-07-29T04:10:43.581548Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 471.7, 'cpu_percent': 0.0} left_dataset_type=WpDiscreteDataset left_sampling_interval=0.010000000000218279 operation=merge_datasets_left_aligned\n", "2025-07-29T04:10:43.597199Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 471.7, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['T2_P50', 'PHIT_NMR', 'PHI_T2_DIST', 'T2LM', 'WELL_NO', 'MD']\n", "2025-07-29T04:10:43.628676Z [info     ] 开始连续型数据集深度重采样                  [logwp.models.datasets.internal.continuous_resample] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 471.7, 'cpu_percent': 0.0} custom_depth_range=False dataset_name=Logs_right_temp interpolation_method=nearest new_sampling_interval=0.010000000000218279 operation=resample_continuous_dataset original_sampling_interval=0.03809999999975844 out_of_range_fill_value=nan\n", "2025-07-29T04:10:44.303859Z [info     ] 连续型数据集深度重采样完成                  [logwp.models.datasets.internal.continuous_resample] actual_depth_range=(6310.27000013774, 6962.020000151966) context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 532.34, 'cpu_percent': 0.0} dataset_name=Logs_right_temp depth_points=per_well_calculated operation=resample_continuous_dataset original_rows=16303 resampled_rows=118298\n", "2025-07-29T04:10:44.344626Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 532.34, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_right_temp_converted_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:44.736939Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 549.95, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_right_temp_converted_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:44.787088Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 536.82, 'cpu_percent': 0.0} left_rows=356 operation=_merge_dataframes_left_aligned right_data_columns=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] right_rows=356\n", "2025-07-29T04:10:44.806574Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 536.84, 'cpu_percent': 0.0} merged_columns=72 merged_rows=356 operation=_merge_dataframes_left_aligned\n", "2025-07-29T04:10:44.819251Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 536.84, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=k_logs dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:44.824079Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 536.84, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('k_logs') result_dataset_type=WpDiscreteDataset result_shape=(356, 72)\n", "2025-07-29T04:10:44.869911Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] left_dataset=k_logs operation=merge_datasets_left_aligned right_curves=['DT2_P50', 'DPHIT_NMR'] right_dataset=OBMIQ_Pred\n", "2025-07-29T04:10:44.883775Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} curve_count=7 has_query=False operation=extract_curves source_dataset=k_logs target_dataset=k_logs_left_temp\n", "2025-07-29T04:10:44.908281Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['T2_P50', 'PHIT_NMR', 'K_LABEL_TYPE', 'PHI_T2_DIST', 'T2LM', 'PZI', 'K_LABEL', 'WELL_NO', 'MD'] operation=extract_metadata output_curve_count=9 output_curves=['T2_P50', 'PHIT_NMR', 'K_LABEL_TYPE', 'PHI_T2_DIST', 'T2LM', 'PZI', 'K_LABEL', 'WELL_NO', 'MD']\n", "2025-07-29T04:10:44.948378Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_k_logs_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:44.967397Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T04:10:44.987973Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=k_logs_left_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-07-29T04:10:45.017229Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=k_logs_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:45.021239Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=72 source_rows=356 target_rows=356\n", "2025-07-29T04:10:45.048652Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} curve_count=2 has_query=False operation=extract_curves source_dataset=OBMIQ_Pred target_dataset=OBMIQ_Pred_right_temp\n", "2025-07-29T04:10:45.064419Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.82, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['WELL_NO', 'MD', 'DPHIT_NMR', 'DT2_P50'] operation=extract_metadata output_curve_count=4 output_curves=['WELL_NO', 'MD', 'DPHIT_NMR', 'DT2_P50']\n", "2025-07-29T04:10:45.111922Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.12, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:45.147066Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:10:45.168557Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.12, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=OBMIQ_Pred_right_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-29T04:10:45.184494Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.12, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:45.202469Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.12, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=4 source_rows=4502 target_rows=4502\n", "2025-07-29T04:10:45.218147Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.03, 'cpu_percent': 0.0} left_dataset_type=WpDiscreteDataset left_sampling_interval=0.010000000000218279 operation=merge_datasets_left_aligned\n", "2025-07-29T04:10:45.233920Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.03, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['WELL_NO', 'MD', 'DPHIT_NMR', 'DT2_P50']\n", "2025-07-29T04:10:45.243945Z [info     ] 开始连续型数据集深度重采样                  [logwp.models.datasets.internal.continuous_resample] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.03, 'cpu_percent': 0.0} custom_depth_range=False dataset_name=OBMIQ_Pred_right_temp interpolation_method=nearest new_sampling_interval=0.010000000000218279 operation=resample_continuous_dataset original_sampling_interval=0.15239999999994325 out_of_range_fill_value=nan\n", "2025-07-29T04:10:45.324745Z [info     ] 连续型数据集深度重采样完成                  [logwp.models.datasets.internal.continuous_resample] actual_depth_range=(6310.570000137746, 6733.490000146978) context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.27, 'cpu_percent': 0.0} dataset_name=OBMIQ_Pred_right_temp depth_points=per_well_calculated operation=resample_continuous_dataset original_rows=4502 resampled_rows=70248\n", "2025-07-29T04:10:45.342144Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.27, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:45.414778Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.48, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:45.435154Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.48, 'cpu_percent': 0.0} left_rows=356 operation=_merge_dataframes_left_aligned right_data_columns=['DT2_P50', 'DPHIT_NMR'] right_rows=356\n", "2025-07-29T04:10:45.451037Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.48, 'cpu_percent': 0.0} merged_columns=74 merged_rows=356 operation=_merge_dataframes_left_aligned\n", "2025-07-29T04:10:45.482130Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.48, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=k_logs_obmiq dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:45.499917Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.48, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64', 'K_LABEL', 'K_LABEL_TYPE', 'PZI', 'DT2_P50', 'DPHIT_NMR'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('k_logs_obmiq') result_dataset_type=WpDiscreteDataset result_shape=(356, 74)\n", "✅ 数据集合并完成\n", "   数据形状: (356, 74)\n", "\n", "💾 保存SCAPE训练数据集...\n", "2025-07-29T04:10:45.518179Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.41, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train.wp.xlsx project_name=WpIdentifier('swift_pso_train') save_head_info=True save_well_map=True\n", "2025-07-29T04:10:45.545420Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.41, 'cpu_percent': 0.0} curve_count=11 dataset_name=WpIdentifier('k_logs_obmiq') dataset_type=Point df_shape=(356, 74)\n", "2025-07-29T04:10:45.693110Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.66, 'cpu_percent': 0.0} dataset_name=WpIdentifier('k_logs_obmiq') processing_time=0.148\n", "2025-07-29T04:10:45.714152Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.66, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-29T04:10:45.747673Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.66, 'cpu_percent': 0.0}\n", "2025-07-29T04:10:45.762394Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.66, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-29T04:10:46.667503Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.66, 'cpu_percent': 0.0}\n", "2025-07-29T04:10:46.683254Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.66, 'cpu_percent': 0.0}\n", "2025-07-29T04:10:47.222608Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.7, 'cpu_percent': 0.0} file_path=scape_swift_pso_train.wp.xlsx processing_time=1.704 project_name=WpIdentifier('swift_pso_train')\n", "2025-07-29T04:10:47.253516Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.7, 'cpu_percent': 0.0} format=markdown project_name=swift_pso_train\n", "2025-07-29T04:10:48.076805Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.1, 'cpu_percent': 0.0} file_size=35479 format=markdown output_path=scape_swift_pso_train_report.md\n", "2025-07-29T04:10:48.116027Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=9 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.1, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-29T04:10:48.133071Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.1, 'cpu_percent': 0.0} curve_count=9 dropna_how=any new_dataset=swift_pso_train_cleaned operation=dropna_dataset source_dataset=swift_pso_train\n", "2025-07-29T04:10:48.159642Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=298 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.11, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=356 removed_rows=58\n", "2025-07-29T04:10:48.173772Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.11, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:10:48.202670Z [info     ] 源数据集类型为 WpDiscreteDataset, 新数据集类型保持不变。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.11, 'cpu_percent': 0.0} operation=dropna_dataset\n", "✅ 合并后的数据集去空值处理并完成\n", "   数据形状: (298, 74)\n", "   数据集类型: WpDiscreteDataset\n", "2025-07-29T04:10:48.220205Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.11, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx project_name=WpIdentifier('Santos_SCAPE_train_cleaned') save_head_info=True save_well_map=True\n", "2025-07-29T04:10:48.253358Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 469.11, 'cpu_percent': 0.0} curve_count=11 dataset_name=WpIdentifier('swift_pso_train_cleaned') dataset_type=Point df_shape=(298, 74)\n", "2025-07-29T04:10:48.355728Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned') processing_time=0.103\n", "2025-07-29T04:10:48.368554Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-29T04:10:48.386791Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0}\n", "2025-07-29T04:10:48.395830Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-29T04:10:49.160816Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0}\n", "2025-07-29T04:10:49.176647Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0}\n", "2025-07-29T04:10:49.595499Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=1.375 project_name=WpIdentifier('Santos_SCAPE_train_cleaned')\n", "2025-07-29T04:10:49.626753Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.37, 'cpu_percent': 0.0} format=markdown project_name=Santos_SCAPE_train_cleaned\n", "2025-07-29T04:10:50.280090Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.39, 'cpu_percent': 0.0} file_size=29761 format=markdown output_path=scape_swift_pso_train_cleaned_report.md\n", "\n", "🎉 SWIFT-PSO 训练数据集生成完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.models.well_project import WpWellProject\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "\n", "    print(\"🔧 开始合并K_Label和Logs数据集...\")\n", "\n", "    try:\n", "        # 使用左对齐合并方式\n", "        print(\"\\n📍 执行左对齐合并...\")\n", "        la1_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"K_Label\",\n", "            left_curves=k_label_curves,\n", "            right_dataset=\"Logs\",\n", "            right_curves=logs_curves,\n", "            new_dataset_name=\"k_logs\"\n", "        )\n", "        project.add_dataset(\"k_logs\", la1_ds)\n", "\n", "        la2_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"k_logs\",\n", "            left_curves= logs_curves + k_label_curves,\n", "            right_dataset=\"OBMIQ_Pred\",\n", "            right_curves=obmiq_pred_curves,\n", "            new_dataset_name=\"k_logs_obmiq\"\n", "        )\n", "        project.add_dataset(\"swift_pso_train\", la2_ds)\n", "\n", "        print(f\"✅ 数据集合并完成\")\n", "        print(f\"   数据形状: {la2_ds.df.shape}\")\n", "\n", "        # =============================\n", "\n", "        # 创建临时项目并保存\n", "        print(\"\\n💾 保存SCAPE训练数据集...\")\n", "        temp_project = WpWellProject(name=\"swift_pso_train\")\n", "        temp_project.add_dataset(\"swift_pso_train\", la2_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_swift_pso_train.wp.xlsx\"\n", "        writer.write(temp_project, output_path, apply_formatting=True)\n", "        report_path = temp_project.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"scape_swift_pso_train_report.md\"\n", "        )\n", "\n", "        # =============================\n", "\n", "        swift_pso_train_cleaned_ds = project.dropna_dataset(\n", "            source_dataset_name=\"swift_pso_train\",\n", "            curve_names=[],\n", "            new_dataset_name=\"swift_pso_train_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        print(f\"✅ 合并后的数据集去空值处理并完成\")\n", "        print(f\"   数据形状: {swift_pso_train_cleaned_ds.df.shape}\")\n", "        print(f\"   数据集类型: {type(swift_pso_train_cleaned_ds).__name__}\")\n", "\n", "        # =============================\n", "\n", "        temp_project2 = WpWellProject(name=\"Santos_SCAPE_train_cleaned\")\n", "        temp_project2.add_dataset(\"swift_pso_train_cleaned\", swift_pso_train_cleaned_ds)\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_swift_pso_train_cleaned.wp.xlsx\"\n", "        writer.write(temp_project2, output_path, apply_formatting=True)\n", "        report_path = temp_project2.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"scape_swift_pso_train_cleaned_report.md\"\n", "        )\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 数据集合并/清理失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 SWIFT-PSO 训练数据集生成完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过SWIFT-PSO训练数据集生成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 生成预测数据集"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:10:50.381620Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.39, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST'] left_dataset=Logs operation=merge_datasets_left_aligned right_curves=['DT2_P50', 'DPHIT_NMR'] right_dataset=OBMIQ_Pred\n", "2025-07-29T04:10:50.396352Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.39, 'cpu_percent': 0.0} curve_count=4 has_query=False operation=extract_curves source_dataset=Logs target_dataset=Logs_left_temp\n", "2025-07-29T04:10:50.425915Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 470.39, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=6 input_curves=['T2_P50', 'PHIT_NMR', 'PHI_T2_DIST', 'T2LM', 'WELL_NO', 'MD'] operation=extract_metadata output_curve_count=6 output_curves=['T2_P50', 'PHIT_NMR', 'PHI_T2_DIST', 'T2LM', 'WELL_NO', 'MD']\n", "2025-07-29T04:10:50.541324Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.81, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_Logs_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:50.563498Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.4, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-29T04:10:50.582616Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.4, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=Logs_left_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-29T04:10:50.610183Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.4, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:50.634532Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.4, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] operation=extract_curves selected_columns=69 source_rows=16303 target_rows=16303\n", "2025-07-29T04:10:50.653281Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.86, 'cpu_percent': 0.0} curve_count=2 has_query=False operation=extract_curves source_dataset=OBMIQ_Pred target_dataset=OBMIQ_Pred_right_temp\n", "2025-07-29T04:10:50.661334Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.86, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['WELL_NO', 'MD', 'DPHIT_NMR', 'DT2_P50'] operation=extract_metadata output_curve_count=4 output_curves=['WELL_NO', 'MD', 'DPHIT_NMR', 'DT2_P50']\n", "2025-07-29T04:10:50.725219Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 483.09, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:50.752599Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 483.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:10:50.771620Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 483.11, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=OBMIQ_Pred_right_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-29T04:10:50.797091Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 483.11, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:50.814670Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 483.11, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=4 source_rows=4502 target_rows=4502\n", "2025-07-29T04:10:50.825025Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.01, 'cpu_percent': 0.0} left_dataset_type=WpContinuousDataset left_sampling_interval=0.03809999999975844 operation=merge_datasets_left_aligned\n", "2025-07-29T04:10:50.847284Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.01, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['WELL_NO', 'MD', 'DPHIT_NMR', 'DT2_P50']\n", "2025-07-29T04:10:50.859669Z [info     ] 开始连续型数据集深度重采样                  [logwp.models.datasets.internal.continuous_resample] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.01, 'cpu_percent': 0.0} custom_depth_range=False dataset_name=OBMIQ_Pred_right_temp interpolation_method=nearest new_sampling_interval=0.03809999999975844 operation=resample_continuous_dataset original_sampling_interval=0.15239999999994325 out_of_range_fill_value=nan\n", "2025-07-29T04:10:50.926407Z [info     ] 连续型数据集深度重采样完成                  [logwp.models.datasets.internal.continuous_resample] actual_depth_range=(6310.57919995999, 6733.527299957308) context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.62, 'cpu_percent': 0.0} dataset_name=OBMIQ_Pred_right_temp depth_points=per_well_calculated operation=resample_continuous_dataset original_rows=4502 resampled_rows=18442\n", "2025-07-29T04:10:50.947350Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.62, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:51.014526Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.21, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:51.033011Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 479.95, 'cpu_percent': 0.0} left_rows=16303 operation=_merge_dataframes_left_aligned right_data_columns=['DT2_P50', 'DPHIT_NMR'] right_rows=16303\n", "2025-07-29T04:10:51.083007Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 488.66, 'cpu_percent': 0.0} merged_columns=71 merged_rows=16303 operation=_merge_dataframes_left_aligned\n", "2025-07-29T04:10:51.101660Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 488.66, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:51.120201Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 488.66, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64', 'DT2_P50', 'DPHIT_NMR'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('swift_pso_apply') result_dataset_type=WpContinuousDataset result_shape=(16303, 71)\n", "2025-07-29T04:10:51.136143Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=6 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.95, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-29T04:10:51.147051Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.95, 'cpu_percent': 0.0} curve_count=6 dropna_how=any new_dataset=swift_pso_apply_cleaned operation=dropna_dataset source_dataset=swift_pso_apply\n", "2025-07-29T04:10:51.183216Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=4690 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.46, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=11613\n", "2025-07-29T04:10:51.203479Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.46, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:10:51.226676Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.46, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:10:51.248638Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.46, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-29T04:10:51.259378Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.46, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx project_name=WpIdentifier('swift_pso_apply_cleaned') save_head_info=True save_well_map=True\n", "2025-07-29T04:10:51.292112Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 481.46, 'cpu_percent': 0.0} curve_count=8 dataset_name=WpIdentifier('swift_pso_apply_cleaned') dataset_type=Continuous df_shape=(4690, 71)\n", "2025-07-29T04:10:52.805082Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 524.02, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned') processing_time=1.514\n", "2025-07-29T04:10:52.820729Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 521.52, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-29T04:10:52.860473Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 521.52, 'cpu_percent': 0.0}\n", "2025-07-29T04:10:52.868230Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 521.52, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-29T04:11:02.800475Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 562.29, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:02.827027Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 562.29, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:08.083962Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.48, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=16.825 project_name=WpIdentifier('swift_pso_apply_cleaned')\n"]}], "source": ["ds_apply = project.merge_datasets_left_aligned(\n", "    left_dataset=\"Logs\",\n", "    left_curves=logs_curves,\n", "    right_dataset=\"OBMIQ_Pred\",\n", "    right_curves=obmiq_pred_curves,\n", "    new_dataset_name=\"swift_pso_apply\"\n", ")\n", "\n", "project.add_dataset(\"swift_pso_apply\",ds_apply)\n", "\n", "ds_apply_cleaned=project.dropna_dataset(\n", "    source_dataset_name=\"swift_pso_apply\",\n", "    curve_names=[],\n", "    new_dataset_name=\"swift_pso_apply_cleaned\",\n", "    dropna_how=\"any\"\n", ")\n", "temp_project = WpWellProject(name=\"swift_pso_apply_cleaned\")\n", "temp_project.add_dataset(\"swift_pso_apply_cleaned\", ds_apply_cleaned)\n", "\n", "writer = WpExcelWriter()\n", "output_path = \"scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "writer.write(temp_project, output_path, apply_formatting=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. PLT & Core_K"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:11:08.191858Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.48, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx project_name=WpIdentifier('santos_data_v3') save_head_info=True save_well_map=True\n", "2025-07-29T04:11:08.226473Z [info     ] 开始写入井头信息表单                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.48, 'cpu_percent': 0.0} total_attributes=4\n", "2025-07-29T04:11:08.236345Z [info     ] 井头信息表单写入完成                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} processing_time=0.01 total_attributes=4\n", "2025-07-29T04:11:08.249906Z [info     ] 开始写入井名映射表单                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} total_mappings=4\n", "2025-07-29T04:11:08.254812Z [info     ] 井名映射表单写入完成                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} processing_time=0.005 total_mappings=4\n", "2025-07-29T04:11:08.270544Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} curve_count=4 dataset_name=WpIdentifier('PLT') dataset_type=Interval df_shape=(15, 4)\n", "2025-07-29T04:11:08.294517Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT') processing_time=0.024\n", "2025-07-29T04:11:08.309082Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} dataset_count=1 head_info=True total_sheets=3 well_map=True\n", "2025-07-29T04:11:08.330662Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:08.342230Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} workbook_sheets=3\n", "2025-07-29T04:11:08.369586Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:08.386151Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:08.426599Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx processing_time=0.235 project_name=WpIdentifier('santos_data_v3')\n", "2025-07-29T04:11:08.438633Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val.wp.xlsx project_name=WpIdentifier('santos_data_v3') save_head_info=True save_well_map=True\n", "2025-07-29T04:11:08.469414Z [info     ] 开始写入井头信息表单                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} total_attributes=4\n", "2025-07-29T04:11:08.483258Z [info     ] 井头信息表单写入完成                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} processing_time=0.014 total_attributes=4\n", "2025-07-29T04:11:08.502206Z [info     ] 开始写入井名映射表单                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} total_mappings=4\n", "2025-07-29T04:11:08.519649Z [info     ] 井名映射表单写入完成                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} processing_time=0.017 total_mappings=4\n", "2025-07-29T04:11:08.533029Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} curve_count=9 dataset_name=WpIdentifier('K_Val') dataset_type=Point df_shape=(45, 9)\n", "2025-07-29T04:11:08.561402Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val') processing_time=0.028\n", "2025-07-29T04:11:08.576682Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} dataset_count=1 head_info=True total_sheets=3 well_map=True\n", "2025-07-29T04:11:08.592758Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:08.604245Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} workbook_sheets=3\n", "2025-07-29T04:11:08.672403Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:08.686458Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0}\n", "2025-07-29T04:11:08.742427Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 565.49, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx processing_time=0.304 project_name=WpIdentifier('santos_data_v3')\n", "\n", "🎉 PLT/K_Val写入完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.models.well_project import WpWellProject\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "\n", "    try:\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_plt_val.wp.xlsx\"\n", "        writer.write(project, output_path,dataset_names=[\"PLT\"],apply_formatting=True)\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_core_k_val.wp.xlsx\"\n", "        writer.write(project, output_path,dataset_names=[\"K_Val\"],apply_formatting=True)\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ PLT/K_Val写入失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 PLT/K_Val写入完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过PLT/K_Val数据集生成\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}