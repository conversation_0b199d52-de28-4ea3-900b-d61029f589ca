from __future__ import annotations

"""logwp.models.service.curve_extraction - 曲线提取服务层

实现WpWellProject的曲线提取功能，支持条件查询和曲线选择。

Architecture
------------
层次/依赖: models层服务，依赖curve、datasets、exceptions
设计原则: 服务层模式、无状态设计、类型安全
性能特征: 内存优化、异步支持、批量处理

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_DDS_logwp_曲线提取.md》§3 - 服务层设计
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
"""

from typing import TYPE_CHECKING, Optional, Dict
import pandas as pd
import numpy as np

from logwp.models.utils import CaseInsensitiveDict
from logwp.models.constants import WpStandardColumn

from logwp.models.exceptions import (
    WpDataError, WpDatasetNotFoundError, WpCurveMetadataError
)
from logwp.infra.exceptions import ErrorContext
from logwp.models.constants import WpStatisticsKeys
from logwp.models.curve import CurveExpansionMode, CurveMetadata
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.well_project import WpWellProject
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from logwp.models.datasets.bundle import WpArrayBundle, WpDataFrameBundle

# 导入数据集类型用于智能类型判断
from logwp.models.datasets.continuous import WpContinuousDataset
from logwp.models.datasets.discrete import WpDiscreteDataset

logger = get_logger(__name__)


def extract_curves(
    project: WpWellProject,
    source_dataset: str,
    target_dataset: str,
    curve_names: list[str] | None = None,
    query_condition: str | None = None
) -> WpDepthIndexedDatasetBase:
    """曲线提取服务函数（支持智能类型判断）。

    从源数据集中提取指定曲线，生成新的数据集。支持智能数据集类型判断：
    - 如果源数据集是Interval类型，保持Interval类型
    - 如果源数据集是Continuous/Discrete类型，根据深度采样特征智能判断：
      * 等间隔采样 → 生成Continuous类型数据集
      * 非等间隔采样 → 生成Discrete类型数据集

    Args:
        project: WpWellProject项目对象
        source_dataset: 源数据集名称
        target_dataset: 目标数据集名称
        curve_names: 要提取的曲线名称列表。如果为 `None`，则提取所有数据曲线
            （即排除井名、深度等系统曲线）。
        query_condition: 查询条件（可选）

    Returns:
        WpDepthIndexedDatabaseBase: 提取生成的新数据集，类型根据深度采样特征智能判断

    Raises:
        WpDatasetNotFoundError: 源数据集不存在
        WpCurveMetadataError: 曲线名称无效或查询条件语法错误
        ValueError: 参数无效

    Note:
        智能类型判断逻辑：
        1. 检查源数据集深度参考曲线数量
        2. 如果不是单一深度参考（如Interval），保持原类型
        3. 如果是单一深度参考，使用check_uniform_depth_sampling()检查采样间隔
        4. 根据采样间隔特征选择Continuous或Discrete类型

    References:
        《SCAPE_DDS_logwp_曲线提取.md》§4.2 - 服务层完整处理流程
        《SCAPE_DDS_logwp_曲线提取.md》§4.3 - 智能类型判断
    """
    logger.info(
        "开始曲线提取",
        operation="extract_curves",
        source_dataset=source_dataset,
        target_dataset=target_dataset,
        curve_count=len(curve_names) if curve_names is not None else "all",
        has_query=query_condition is not None
    )

    try:
        # 1. 参数验证
        _validate_extraction_parameters(source_dataset, target_dataset, curve_names)

        # 2. 获取源数据集
        source_ds = project.get_dataset(source_dataset)
        if source_ds is None:
            raise WpDatasetNotFoundError(
                f"源数据集 '{source_dataset}' 不存在",
                context=ErrorContext(
                    operation="extract_curves",
                    dataset_name=source_dataset,
                    additional_info={"available_datasets": list(project.datasets.keys())}
                )
            )

        # 3. 确定要提取的曲线列表
        if curve_names is None:
            curves_to_extract = source_ds.curve_metadata.get_data_curves()
            logger.info(
                "curve_names为None，将提取所有数据曲线。",
                operation="extract_curves",
                extracted_curve_count=len(curves_to_extract),
                source_dataset=source_dataset
            )
        else:
            curves_to_extract = curve_names

        # 4. 获取必需的井名列和深度列名称
        well_curve_names = source_ds.curve_metadata.get_well_identifier_curves()
        depth_curve_names = source_ds.curve_metadata.get_depth_reference_curves()

        # 5. 合并用户指定曲线和必需列（保持原始格式用于元数据提取）
        all_required_curve_names = list(set(curves_to_extract + well_curve_names + depth_curve_names))

        # 6. 提取曲线元数据（使用原始格式，保持二维组合曲线完整性！）
        filtered_metadata = source_ds.curve_metadata.extract_metadata(all_required_curve_names)

        # 7. 获取DataFrame友好的列名（关键修复！）
        dataframe_column_names = source_ds.curve_metadata.expand_curve_names(
            curves_to_extract,
            CurveExpansionMode.DATAFRAME
        )

        # 8. 获取必需的井名列和深度列
        well_columns, depth_columns = _get_required_columns(source_ds)

        # 9. 构建完整的列名列表（DataFrame友好列名 + 必需列）
        all_columns = _build_column_list_with_dataframe_names(
            dataframe_column_names, well_columns, depth_columns
        )
        # 10. 应用查询条件过滤数据
        if query_condition:
            filtered_df = _apply_query_filter(source_ds.df, source_ds.curve_metadata, query_condition)
        else:
            filtered_df = source_ds.df.copy()

        # 11. 选择指定列（使用DataFrame友好列名）
        selected_df = filtered_df[all_columns].copy()

        # 12. 重置索引确保使用RangeIndex
        selected_df = selected_df.reset_index(drop=True)

        # 13. 智能数据集类型判断和创建
        target_ds = _create_dataset_with_smart_type_detection(
            source_ds, target_dataset, selected_df, filtered_metadata
        )

        logger.info(
            "曲线提取完成",
            operation="extract_curves",
            source_rows=len(source_ds.df),
            target_rows=len(selected_df),
            selected_columns=len(all_columns),
            dataframe_columns=all_columns  # 记录实际的DataFrame列名
        )

        # 14. 返回生成的数据集
        return target_ds

    except Exception as e:
        logger.error(
            "曲线提取失败",
            operation="extract_curves",
            source_dataset=source_dataset,
            target_dataset=target_dataset,
            error_type=type(e).__name__,
            error_message=str(e)
        )
        raise


def extract_curve_dataframe_bundle(
    dataset: 'WpDepthIndexedDatasetBase',
    curve_names: list[str] | None,
    *,
    include_system_columns: bool = False,
    validate_existence: bool = True,
    query_condition: Optional[str] = None,
    rename_map: Optional[CaseInsensitiveDict[str, str]] = None
) -> 'WpDataFrameBundle':
    """创建曲线DataFrame数据包（轻量级DTO封装）。

    返回包含DataFrame数据和便捷元数据访问接口的Bundle对象，
    专为需要进一步处理DataFrame的场景设计。

    Args:
        dataset: 源数据集对象
        curve_names: 要提取的曲线名称列表（指现有曲线名称）。如果为 `None`，
            则提取所有数据曲线（即排除井名、深度等系统曲线）。
        include_system_columns: 是否包含系统列（井名+深度），默认False
        validate_existence: 是否验证曲线存在性，默认True
        query_condition: 可选的查询条件，用于数据过滤
        rename_map: 可选的曲线重命名映射表, 格式为 {'原始名称': '新名称'}

    Returns:
        tuple[pd.DataFrame, dict[str, list[str]]]:
            - selected_dataframe: 选择的数据DataFrame
            - curve_to_columns_mapping: 曲线名到DataFrame列名的映射

    Raises:
        WpCurveMetadataError: 曲线不存在或名称无效
        WpDatasetError: 数据集操作失败

    Examples:
        >>> # 基本使用
        >>> df, mapping = extract_curve_dataframe_bundle(
        ...     dataset=my_dataset,
        ...     curve_names=["PHIT", "T2_VALUE"],
        ...     include_system_columns=False
        ... )
        >>> print(mapping)  # {"PHIT": ["PHIT"], "T2_VALUE": ["T2_VALUE[1]", "T2_VALUE[2]", ...]}

        >>> # 包含系统列
        >>> df, mapping = extract_curve_dataframe_bundle(
        ...     dataset=my_dataset,
        ...     curve_names=["GR"],
        ...     include_system_columns=True
        ... )
        >>> # df将包含井名列、深度列和GR列

    References:
        设计背景：为SCAPE Stage-II等算法提供高效的数据提取接口
        重构计划：未来将extract_curves和extract_curve_dataframe_bundle合并为统一的服务层
    """
    try:
        from logwp.models.datasets.bundle import WpDataFrameBundle

        # 步骤0: 确定要提取的曲线列表
        if curve_names is None:
            curves_to_extract = dataset.curve_metadata.get_data_curves()
            logger.info(
                "curve_names为None，将提取所有数据曲线。",
                operation="extract_curve_dataframe_bundle",
                extracted_curve_count=len(curves_to_extract)
            )
        else:
            curves_to_extract = curve_names

        # 【修复】根据 include_system_columns 标志强制过滤系统曲线
        # 无论 curve_names 中是否包含系统曲线，此参数都应作为最终决定因素。
        if not include_system_columns:
            system_curves_set = set(dataset.curve_metadata.get_system_curves())

            # 检查用户请求的曲线中是否包含系统曲线
            requested_system_curves = [
                c for c in curves_to_extract if c in system_curves_set
            ]

            if requested_system_curves:
                # 如果包含，则从待提取列表中过滤掉它们
                curves_to_extract = [c for c in curves_to_extract if c not in system_curves_set]
                logger.info(
                    "include_system_columns=False，已从请求中过滤掉系统曲线。",
                    operation="extract_curve_dataframe_bundle",
                    filtered_curves=requested_system_curves,
                )

        logger.debug(
            "开始核心曲线数据提取",
            operation="extract_curve_dataframe_bundle",
            curve_names=curve_names,
            include_system_columns=include_system_columns,
            validate_existence=validate_existence,
            dataset_shape=dataset.df.shape
        )

        # 步骤0：初始化系统曲线相关变量，避免NameError
        well_original_names, depth_original_names = [], []
        well_columns, depth_columns = [], []

        # 步骤1：曲线存在性验证（重用extract_curves逻辑）
        if validate_existence:
            dataset.curve_metadata.validate_curves_exist(curves_to_extract)

        # 步骤2：曲线名称展开（重用extract_curves核心逻辑）
        from logwp.models.curve.metadata import CurveExpansionMode

        dataframe_column_names = dataset.curve_metadata.expand_curve_names(
            curves_to_extract, CurveExpansionMode.DATAFRAME
        )

        # 步骤3：构建曲线到列名的映射
        curve_to_columns = {}
        for curve_name in curves_to_extract:
            df_expand_columns = dataset.curve_metadata.expand_curve_names(
                [curve_name], CurveExpansionMode.DATAFRAME
            )
            curve_to_columns[curve_name] = df_expand_columns

        # 步骤4：处理系统列（重用extract_curves逻辑）
        all_columns = dataframe_column_names.copy()
        if include_system_columns:
            # 这行代码保持不变，获取DataFrame列名
            well_columns, depth_columns = _get_required_columns(dataset)

            # 【新增】获取系统曲线的原始名称，用于元数据提取
            well_original_names = dataset.curve_metadata.get_well_identifier_curves()
            depth_original_names = dataset.curve_metadata.get_depth_reference_curves()

            # 这行代码保持不变，构建用于从DataFrame中选择列的完整列表
            all_columns = _build_column_list_with_dataframe_names(
                dataframe_column_names, well_columns, depth_columns
            )

            # 【修复】将系统曲线添加到 curve_to_columns 映射中，确保Bundle内部一致性
            for curve_name in well_original_names + depth_original_names:
                curve_attrs = dataset.curve_metadata.get_curve(curve_name)
                if curve_attrs:
                    # 系统曲线通常是一维的
                    curve_to_columns[curve_name] = [curve_attrs.dataframe_column_name]

        # 步骤5：应用查询条件过滤（重用extract_curves逻辑）
        if query_condition:
            filtered_df = _apply_query_filter(dataset.df, dataset.curve_metadata, query_condition)
        else:
            filtered_df = dataset.df

        # 步骤6：选择指定列
        selected_df = filtered_df[all_columns].copy()

        # 步骤A: 确定实际提取出的所有原始曲线名（用户曲线+系统曲线）
        all_required_original_names = list(set(curves_to_extract + well_original_names + depth_original_names))

        # 步骤B: 创建一个只包含所需曲线的、干净的元数据副本
        final_metadata = dataset.curve_metadata.extract_metadata(all_required_original_names)

        # 步骤C: 如果提供了重命名映射，则执行重命名
        if rename_map:
            logger.info("应用曲线重命名映射...", rename_map=rename_map)

            column_rename_map = final_metadata.rename_curves_for_ml(rename_map)

            selected_df.rename(columns=column_rename_map, inplace=True)
            logger.info("曲线重命名完成。")

            # 【新增】同步更新 curve_to_columns_map 以反映重命名
            updated_curve_to_columns = {}
            for old_curve, old_cols in curve_to_columns.items():
                # 获取新的曲线名，如果未重命名则保持原样
                new_curve = rename_map.get(old_curve, old_curve)
                # 获取新的DataFrame列名列表
                new_cols = [column_rename_map.get(col, col) for col in old_cols]
                updated_curve_to_columns[new_curve] = new_cols

            # 将旧的映射替换为更新后的映射
            curve_to_columns = updated_curve_to_columns
            logger.debug("curve_to_columns_map 已同步更新以匹配重命名后的曲线。")


        bundle = WpDataFrameBundle(
            name=dataset.name,
            curve_metadata=final_metadata,
            data=selected_df,
            curve_to_columns_map=curve_to_columns
        )

        logger.debug(
            "DataFrame Bundle创建完成",
            operation="extract_curve_dataframe_bundle",
            bundle_name=bundle.name,
            original_rows=len(dataset.df),
            selected_rows=len(selected_df),
            selected_columns=len(all_columns),
            is_interval_bundle=bundle.is_interval_bundle,
            well_curves_count=len(bundle.well_curve_map),
            curve_to_columns_count={name: len(cols) for name, cols in curve_to_columns.items()}
        )

        return bundle

    except Exception as e:
        logger.error(
            "核心曲线数据提取失败",
            operation="extract_curve_dataframe_bundle",
            curve_names=curve_names,
            error_message=str(e),
            error_type=type(e).__name__
        )
        raise


def extract_curve_array_bundle(
    dataset: 'WpDepthIndexedDatasetBase',
    curve_names: list[str],
    *,
    include_system_columns: bool = False,
    validate_existence: bool = True,
    query_condition: Optional[str] = None,
    rename_map: Optional[CaseInsensitiveDict[str, str]] = None
) -> 'WpArrayBundle':
    """创建曲线NumPy数组数据包（算法友好DTO封装）。

    返回包含numpy数组数据和便捷元数据访问接口的Bundle对象，
    专为算法计算和机器学习场景设计。

    Args:
        dataset: 源数据集对象
        curve_names: 要提取的曲线名称列表
            例如: ["PHIT", "GR", "T2_VALUE"]
        include_system_columns: 是否包含系统列（井名+深度），默认False
        validate_existence: 是否验证曲线存在性，默认True
        query_condition: 可选的查询条件，用于数据过滤
        rename_map: 可选的曲线重命名映射表, 格式为 {'原始名称': '新名称'}

    Returns:
        Dict[str, np.ndarray]: 曲线名到numpy数组的映射
        - 一维曲线返回形状为(n_depths,)的数组
        - 二维组合曲线返回形状为(n_depths, n_bins)的数组
        - 如果include_system_columns=True，额外包含'well_name'和'depth'键

    Raises:
        WpDataError: 数据集操作失败
        WpCurveMetadataError: 曲线不存在或名称无效

    Examples:
        >>> # 基本使用：提取一维曲线
        >>> arrays = extract_curve_array_bundle(
        ...     dataset=my_dataset,
        ...     curve_names=["PHIT", "GR", "RHOB"]
        ... )
        >>> assert arrays["PHIT"].shape == (n_depths,)  # 一维数组

        >>> # 提取二维组合曲线
        >>> arrays = extract_curve_array_bundle(
        ...     dataset=my_dataset,
        ...     curve_names=["T2_VALUE"]
        ... )
        >>> assert arrays["T2_VALUE"].shape == (n_depths, n_bins)  # 二维数组

        >>> # 包含系统列
        >>> arrays = extract_curve_array_bundle(
        ...     dataset=my_dataset,
        ...     curve_names=["PHIT"],
        ...     include_system_columns=True
        ... )
        >>> assert "well_name" in arrays
        >>> assert "depth" in arrays

    Performance Notes:
        - 重用extract_curve_dataframe_bundle的优化逻辑
        - 直接返回numpy数组，避免中间对象创建开销
        - 支持大规模数据集的高效处理

    Architecture Notes:
        与extract_curve_dataframe_bundle形成互补：
        - extract_curve_dataframe_bundle: 返回DataFrame，适合数据预处理
        - extract_curve_array_bundle: 返回numpy数组，适合算法计算

        保持业务无关性：不包含任何特定算法的逻辑（如T2轴验证等），
        这些业务逻辑应该在算法层实现。
    """
    try:
        from logwp.models.datasets.bundle import WpArrayBundle

        logger.debug(
            "开始曲线数组数据包提取",
            operation="extract_curve_array_bundle",
            curve_names=curve_names,
            dataset_shape=dataset.df.shape,
            include_system_columns=include_system_columns
        )

        # 转发给extract_curve_dataframe_bundle获取DataFrame数据包
        df_bundle = extract_curve_dataframe_bundle(
            dataset=dataset,
            curve_names=curve_names,
            include_system_columns=include_system_columns,
            validate_existence=validate_existence,
            query_condition=query_condition,
            rename_map=rename_map
        )

        # 转换为numpy数组格式
        result = CaseInsensitiveDict()

        # 关键修复：迭代更新后的曲线名（df_bundle.curve_to_columns_map.keys()），
        # 而不是原始的curve_names。这可以正确处理重命名后的情况。
        # 同时，这也确保了如果用户请求了系统列，它们也会被包含在最终的数组包中。
        all_curves_to_extract = list(df_bundle.curve_to_columns_map.keys())
        for curve_name in all_curves_to_extract:
            column_names = df_bundle.curve_to_columns_map[curve_name]

            if len(column_names) == 1:
                # 一维曲线：直接提取单列
                result[curve_name] = df_bundle.data[column_names[0]].values
                logger.debug(
                    f"提取一维曲线: {curve_name}",
                    column_name=column_names[0],
                    data_shape=result[curve_name].shape
                )
            else:
                # 二维组合曲线：提取多列组成矩阵
                result[curve_name] = df_bundle.data[column_names].values
                logger.debug(
                    f"提取二维组合曲线: {curve_name}",
                    column_count=len(column_names),
                    data_shape=result[curve_name].shape
                )

        bundle = WpArrayBundle(
            name=dataset.name,
            curve_metadata=df_bundle.curve_metadata,
            data=result
        )

        logger.info(
            "Array Bundle创建完成",
            operation="extract_curve_array_bundle",
            bundle_name=bundle.name,
            extracted_keys=list(result.keys()),
            data_shapes={key: arr.shape for key, arr in result.items()},
            total_memory_mb=sum(arr.nbytes for arr in result.values()) / 1024 / 1024,
            is_interval_bundle=bundle.is_interval_bundle,
            well_curves_count=len(bundle.well_curve_map)
        )

        return bundle

    except Exception as e:
        logger.error(
            "曲线数组数据包提取失败",
            operation="extract_curve_array_bundle",
            curve_names=curve_names,
            error_message=str(e),
            error_type=type(e).__name__
        )

        # 重新抛出已知异常，包装未知异常
        if isinstance(e, (WpDataError, WpCurveMetadataError)):
            raise
        else:
            raise WpDataError(
                f"曲线数组数据包提取失败: {e}",
                context=ErrorContext(
                    operation="extract_curve_array_bundle",
                    additional_info={
                        "curve_names": curve_names,
                        "dataset_shape": dataset.df.shape if hasattr(dataset, 'df') else None,
                        "original_error": str(e)
                    }
                )
            ) from e


def _validate_extraction_parameters(
    source_dataset: str,
    target_dataset: str,
    curve_names: list[str] | None
) -> None:
    """验证曲线提取参数。

    Args:
        source_dataset: 源数据集名称
        target_dataset: 目标数据集名称
        curve_names: 要提取的曲线名称列表

    Raises:
        ValueError: 参数无效
    """
    # 验证基本参数
    if not source_dataset or not isinstance(source_dataset, str):
        raise ValueError("源数据集名称必须是非空字符串")

    if not target_dataset or not isinstance(target_dataset, str):
        raise ValueError("目标数据集名称必须是非空字符串")

    if curve_names is not None and (not curve_names or not isinstance(curve_names, list)):
        raise ValueError("曲线名称列表必须是非空列表")

    if curve_names is not None and not all(isinstance(name, str) and name.strip() for name in curve_names):
        raise ValueError("曲线名称列表中的所有元素必须是非空字符串")


def _get_required_columns(source_dataset: WpDepthIndexedDatasetBase) -> tuple[list[str], list[str]]:
    """获取必需的井名列和深度列。

    Args:
        source_dataset: 源数据集

    Returns:
        tuple[list[str], list[str]]: (井名列名列表, 深度列名列表)

    References:
        《SCAPE_DDS_logwp_曲线提取.md》§8.2 - 列名处理逻辑
    """
    # 获取井名列
    well_curve_names = source_dataset.curve_metadata.get_well_identifier_curves()
    well_columns = []
    for curve_name in well_curve_names:
        curve = source_dataset.curve_metadata.get_curve(curve_name)
        if curve:
            well_columns.append(curve.dataframe_column_name)

    # 获取深度列
    depth_curve_names = source_dataset.curve_metadata.get_depth_reference_curves()
    depth_columns = []
    for curve_name in depth_curve_names:
        curve = source_dataset.curve_metadata.get_curve(curve_name)
        if curve:
            depth_columns.append(curve.dataframe_column_name)

    return well_columns, depth_columns


def _build_column_list_with_dataframe_names(
    dataframe_column_names: list[str],
    well_columns: list[str],
    depth_columns: list[str]
) -> list[str]:
    """构建完整的DataFrame列名列表。

    Args:
        dataframe_column_names: DataFrame友好的曲线列名列表
        well_columns: 井名列名列表
        depth_columns: 深度列名列表

    Returns:
        list[str]: 完整的DataFrame列名列表

    Note:
        此函数已简化，因为输入的dataframe_column_names已经是DataFrame友好格式

    References:
        《SCAPE_DDS_logwp_曲线提取.md》§8.3 - DataFrame列名构建
    """
    # 直接使用DataFrame友好的列名，无需转换
    user_columns = dataframe_column_names

    # 合并并去重，保持顺序：井名列 + 深度列 + 用户指定列
    all_columns = []
    seen = set()

    for col_list in [well_columns, depth_columns, user_columns]:
        for col in col_list:
            if col not in seen:
                all_columns.append(col)
                seen.add(col)

    return all_columns


def _apply_query_filter(
    df: pd.DataFrame,
    curve_metadata: CurveMetadata,
    query_condition: str
) -> pd.DataFrame:
    """应用查询条件过滤数据。

    Args:
        df: 源DataFrame
        curve_metadata: 曲线元数据
        query_condition: 查询条件

    Returns:
        pd.DataFrame: 过滤后的DataFrame

    Raises:
        WpCurveMetadataError: 查询条件语法错误或曲线名称无效

    References:
        《SCAPE_DDS_logwp_曲线提取.md》§7 - 查询条件处理
    """
    try:
        # 转换查询条件为DataFrame友好格式
        translated_query = curve_metadata.translate_query_for_dataframe(query_condition)

        logger.debug(
            "应用查询条件",
            operation="apply_query_filter",
            original_query=query_condition,
            translated_query=translated_query,
            df_shape=df.shape
        )

        # 应用查询过滤
        filtered_df = df.query(translated_query)

        logger.debug(
            "查询过滤完成",
            operation="apply_query_filter",
            original_rows=len(df),
            filtered_rows=len(filtered_df),
            filter_ratio=len(filtered_df) / len(df) if len(df) > 0 else 0
        )

        return filtered_df

    except Exception as e:
        raise WpCurveMetadataError(
            f"查询条件应用失败: {str(e)}",
            context=ErrorContext(
                operation="apply_query_filter",
                additional_info={
                    "original_query": query_condition,
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
            )
        ) from e


def _create_dataset_with_smart_type_detection(
    source_ds: WpDepthIndexedDatasetBase,
    target_dataset: str,
    selected_df: pd.DataFrame,
    filtered_metadata: CurveMetadata
) -> WpDepthIndexedDatasetBase:
    """智能数据集类型检测和创建。

    根据源数据集类型和深度采样特征，智能判断目标数据集的类型：
    - 如果源数据集是Interval类型，保持Interval类型
    - 如果源数据集是Continuous/Discrete类型，根据深度采样间隔判断：
      * 等间隔采样 → Continuous类型
      * 非等间隔采样 → Discrete类型

    Args:
        source_ds: 源数据集
        target_dataset: 目标数据集名称
        selected_df: 选择的DataFrame数据
        filtered_metadata: 过滤后的曲线元数据

    Returns:
        WpDepthIndexedDatabaseBase: 创建的目标数据集

    References:
        《SCAPE_DDS_logwp_曲线提取.md》§4.3 - 智能类型判断
    """
    # 如果源数据集不是单一深度参考，保持原类型
    if source_ds.get_depth_reference_count() != 1:
        logger.info(
            "源数据集非单一深度参考，保持原类型",
            operation="smart_type_detection",
            source_type=type(source_ds).__name__,
            depth_reference_count=source_ds.get_depth_reference_count(),
            target_dataset=target_dataset
        )
        return type(source_ds).create_with_data(target_dataset, selected_df, filtered_metadata)

    # 创建临时数据集用于深度采样检查
    temp_ds = type(source_ds).create_with_data(f"temp_{target_dataset}", selected_df, filtered_metadata)

    try:
        # 检查深度采样间隔
        is_uniform, sampling_interval = temp_ds.check_uniform_depth_sampling()

        if is_uniform:
            # 等间隔采样 → Continuous类型
            target_type = WpContinuousDataset
            type_reason = f"等间隔采样，间隔={sampling_interval}"
        else:
            # 非等间隔采样 → Discrete类型
            target_type = WpDiscreteDataset
            type_reason = "非等间隔采样"

        logger.info(
            "智能类型检测完成",
            operation="smart_type_detection",
            source_type=type(source_ds).__name__,
            target_type=target_type.__name__,
            is_uniform_sampling=is_uniform,
            sampling_interval=sampling_interval,
            type_reason=type_reason,
            target_dataset=target_dataset
        )

        # 创建目标类型的数据集，传递采样间隔（仅WpContinuousDataset使用）
        if target_type == WpContinuousDataset and sampling_interval is not None:
            return target_type.create_with_data(
                target_dataset, selected_df, filtered_metadata,
                depth_sampling_rate=sampling_interval
            )
        else:
            return target_type.create_with_data(target_dataset, selected_df, filtered_metadata)

    except Exception as e:
        # 如果深度采样检查失败，回退到原类型
        logger.warning(
            "深度采样检查失败，回退到原类型",
            operation="smart_type_detection",
            source_type=type(source_ds).__name__,
            target_dataset=target_dataset,
            error_type=type(e).__name__,
            error_message=str(e)
        )
        return type(source_ds).create_with_data(target_dataset, selected_df, filtered_metadata)
