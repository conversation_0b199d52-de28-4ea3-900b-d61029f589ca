"""logwp.extras.backend.internal.factory - 后端服务工厂

实现创建后端服务实例的工厂函数。
"""

from __future__ import annotations

from typing import Any

import numpy as np
from ..service import BackendService
from .cupy_service import CupyService, _CUPY_AVAILABLE, _CUPY_MODULE
from .numpy_service import NumpyService


def create_backend_service_from_data(*arrays: Any) -> BackendService:
    """根据输入数组类型自动检测并创建相应的后端服务实例。

    此工厂函数是后端服务的唯一入口。它会检查传入的数组，如果任何
    一个是CuPy数组，则返回一个CupyService实例；否则，返回一个
    NumpyService实例。

    Args:
        *arrays: 要检查的可变数量的数组。

    Returns:
        一个实现了BackendService协议的服务实例。
    """
    if _CUPY_AVAILABLE and _CUPY_MODULE is not None:
        backend_module = _CUPY_MODULE.get_array_module(*arrays)
        if backend_module is _CUPY_MODULE:
            return CupyService()

    return NumpyService()


def create_backend_service_by_name(backend_name: str) -> BackendService:
    """根据名称明确创建后端服务实例。

    Args:
        backend_name (str): 后端名称，必须是 'cpu' 或 'gpu'。

    Returns:
        一个实现了BackendService协议的服务实例。

    Raises:
        ValueError: 如果后端名称无效。
        ImportError: 如果请求了 'gpu' 但CuPy不可用。
    """
    normalized_name = backend_name.lower()
    if normalized_name == 'gpu':
        # CupyService的占位符实现会在cupy不可用时自动抛出ImportError
        return CupyService()
    if normalized_name == 'cpu':
        return NumpyService()

    raise ValueError(f"无效的后端名称: '{backend_name}'. 支持的名称是 'cpu' 或 'gpu'。")


def create_backend_service_from_module(backend_module: Any) -> BackendService:
    """根据后端模块（numpy或cupy）创建相应的后端服务实例。

    Args:
        backend_module (Any): numpy或cupy模块。

    Returns:
        一个实现了BackendService协议的服务实例。

    Raises:
        ValueError: 如果传入的不是有效的numpy或cupy模块。
    """
    if _CUPY_AVAILABLE and backend_module is _CUPY_MODULE:
        return CupyService()
    if backend_module is np:
        return NumpyService()

    raise ValueError(f"无效的后端模块: {backend_module}. 只支持 numpy 或 cupy 模块。")


def is_gpu_available() -> bool:
    """检查系统中是否有可用的GPU后端（即CuPy是否已安装并能成功导入）。"""
    return _CUPY_AVAILABLE
