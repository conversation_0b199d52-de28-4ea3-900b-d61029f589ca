#!/usr/bin/env python3
"""插值相关的通用工具函数。

提供可被多个服务层模块复用的、与插值相关的底层功能。
"""

import numpy as np


def apply_interpolation_distance_limit(
    interpolated_values: np.ndarray,
    target_depths: np.ndarray,
    source_depths: np.ndarray,
    max_distance: float
) -> np.ndarray:
    """
    对插值结果应用最大距离限制。超出距离阈值的点将被填充为NaN或空字符串。

    Args:
        interpolated_values: 初始插值结果数组。
        target_depths: 目标深度点数组。
        source_depths: 源数据深度点数组。
        max_distance: 最大插值距离。

    Returns:
        np.ndarray: 应用距离限制后的结果数组。
    """
    if len(source_depths) == 0:
        return interpolated_values

    min_distances = np.min(np.abs(source_depths - target_depths[:, np.newaxis]), axis=1)
    exceeds_limit_mask = min_distances > max_distance

    if interpolated_values.dtype == 'object':
        # 确保数组是可写的
        if not interpolated_values.flags.writeable:
            interpolated_values = interpolated_values.copy()
        interpolated_values[exceeds_limit_mask] = ''
    else:
        # 确保数组是可写的浮点数类型
        if not np.issubdtype(interpolated_values.dtype, np.floating):
            interpolated_values = interpolated_values.astype(float)
        elif not interpolated_values.flags.writeable:
            interpolated_values = interpolated_values.copy()
        interpolated_values[exceeds_limit_mask] = np.nan

    return interpolated_values
