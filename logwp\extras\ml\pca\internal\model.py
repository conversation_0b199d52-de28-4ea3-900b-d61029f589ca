"""PCA模型管理服务。

管理PCA模型参数、元数据维护、模型验证等功能。
定义PCAParameters数据结构和相关操作。

Architecture
------------
层次/依赖: PCA内部服务层，模型管理
设计原则: 类型安全、元数据一致、模型验证
性能特征: 轻量级容器、快速访问、内存友好
"""

from __future__ import annotations

from typing import TYPE_CHECKING, TypedDict
import numpy as np

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from logwp.models.curve.metadata import CurveMetadata

# =============================================================================
# PCA模型数据结构定义
# =============================================================================

class PCAParameters(TypedDict):
    """PCA模型参数结构。

    Architecture
    ------------
    层次/依赖: PCA模型数据容器，类型安全结构
    设计原则: 结构化数据、类型安全、序列化友好
    性能特征: 轻量级容器、快速访问、内存友好

    Attributes:
        components: 主成分矩阵 (n_components, n_features)
        mean: 原始数据均值向量 (n_features,)
        scale: 标准化标准差向量 (n_features,)
        explained_variance_ratio: 方差解释比例 (n_components,)
        n_components: 主成分数量
        n_features: 原始特征数量
        curve_metadata: 原始曲线元数据，用于逆变换
        feature_names: 原始特征名称列表
        pca_curve_name: PCA结果曲线基础名称
    """
    components: np.ndarray
    mean: np.ndarray
    scale: np.ndarray
    explained_variance_ratio: np.ndarray
    n_components: int
    n_features: int
    curve_metadata: CurveMetadata
    feature_names: list[str]
    pca_curve_name: str


# =============================================================================
# 模型管理函数
# =============================================================================

def create_pca_model(
    components: np.ndarray,
    mean: np.ndarray,
    scale: np.ndarray,
    explained_variance_ratio: np.ndarray,
    curve_metadata: CurveMetadata,
    feature_names: list[str],
    pca_curve_name: str = "PC"
) -> PCAParameters:
    """创建PCA模型参数。

    Architecture
    ------------
    层次/依赖: PCA模型构造器，确保数据一致性
    设计原则: 参数验证、类型安全、完整性检查
    性能特征: 快速构造、内存优化、错误检查

    Args:
        components: 主成分矩阵
        mean: 数据均值向量
        scale: 标准差向量
        explained_variance_ratio: 方差解释比例
        curve_metadata: 原始曲线元数据
        feature_names: 特征名称列表
        pca_curve_name: PCA曲线基础名称

    Returns:
        PCA模型参数

    Raises:
        WpPcaModelError: 模型参数不一致
    """
    # 1. 参数维度一致性检查
    n_components, n_features = components.shape

    if len(mean) != n_features:
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError(f"均值向量维度({len(mean)})与特征数量({n_features})不匹配")

    if len(scale) != n_features:
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError(f"标准差向量维度({len(scale)})与特征数量({n_features})不匹配")

    if len(explained_variance_ratio) != n_components:
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError(f"方差解释比例维度({len(explained_variance_ratio)})与主成分数量({n_components})不匹配")

    if len(feature_names) != n_features:
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError(f"特征名称数量({len(feature_names)})与特征数量({n_features})不匹配")

    # 2. 数值有效性验证
    if not np.all(np.isfinite(components)):
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError("主成分矩阵包含无效值(NaN或Inf)")

    if not np.all(np.isfinite(mean)):
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError("均值向量包含无效值(NaN或Inf)")

    if not np.all(np.isfinite(scale)):
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError("标准差向量包含无效值(NaN或Inf)")

    if not np.all(np.isfinite(explained_variance_ratio)):
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError("方差解释比例包含无效值(NaN或Inf)")

    # 检查方差解释比例的合理性
    if np.any(explained_variance_ratio < 0) or np.any(explained_variance_ratio > 1):
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError("方差解释比例必须在[0,1]范围内")

    # 3. 创建PCA模型参数
    pca_model: PCAParameters = {
        "components": components,
        "mean": mean,
        "scale": scale,
        "explained_variance_ratio": explained_variance_ratio,
        "n_components": n_components,
        "n_features": n_features,
        "curve_metadata": curve_metadata,
        "feature_names": feature_names,
        "pca_curve_name": pca_curve_name
    }

    return pca_model


def validate_pca_parameters(params: PCAParameters) -> None:
    """验证PCA参数的一致性和有效性。

    Architecture
    ------------
    层次/依赖: PCA模型验证器，确保模型正确性
    设计原则: 全面检查、快速验证、详细报告
    性能特征: 轻量级检查、快速执行、精确诊断

    Args:
        params: PCA模型参数

    Raises:
        WpPcaModelError: 模型参数无效
    """
    from ..exceptions import WpPcaModelError

    # 1. 基本参数存在性检查
    required_keys = {
        "components", "mean", "scale", "explained_variance_ratio",
        "n_components", "n_features", "curve_metadata",
        "feature_names", "pca_curve_name"
    }

    missing_keys = required_keys - set(params.keys())
    if missing_keys:
        raise WpPcaModelError(f"PCA模型缺少必需参数: {missing_keys}")

    # 2. 维度一致性检查
    components = params["components"]
    mean = params["mean"]
    scale = params["scale"]
    explained_variance_ratio = params["explained_variance_ratio"]
    n_components = params["n_components"]
    n_features = params["n_features"]
    feature_names = params["feature_names"]

    # 检查主成分矩阵维度
    if components.shape != (n_components, n_features):
        raise WpPcaModelError(
            f"主成分矩阵维度({components.shape})与声明的维度({n_components}, {n_features})不匹配"
        )

    # 检查均值向量维度
    if len(mean) != n_features:
        raise WpPcaModelError(f"均值向量长度({len(mean)})与特征数量({n_features})不匹配")

    # 检查标准差向量维度
    if len(scale) != n_features:
        raise WpPcaModelError(f"标准差向量长度({len(scale)})与特征数量({n_features})不匹配")

    # 检查方差解释比例维度
    if len(explained_variance_ratio) != n_components:
        raise WpPcaModelError(
            f"方差解释比例长度({len(explained_variance_ratio)})与主成分数量({n_components})不匹配"
        )

    # 检查特征名称数量
    if len(feature_names) != n_features:
        raise WpPcaModelError(f"特征名称数量({len(feature_names)})与特征数量({n_features})不匹配")

    # 3. 数值有效性检查
    if not np.all(np.isfinite(components)):
        raise WpPcaModelError("主成分矩阵包含无效值(NaN或Inf)")

    if not np.all(np.isfinite(mean)):
        raise WpPcaModelError("均值向量包含无效值(NaN或Inf)")

    if not np.all(np.isfinite(scale)):
        raise WpPcaModelError("标准差向量包含无效值(NaN或Inf)")

    if not np.all(np.isfinite(explained_variance_ratio)):
        raise WpPcaModelError("方差解释比例包含无效值(NaN或Inf)")

    # 检查方差解释比例范围
    if np.any(explained_variance_ratio < 0) or np.any(explained_variance_ratio > 1):
        raise WpPcaModelError("方差解释比例必须在[0,1]范围内")

    # 检查标准差为正值
    if np.any(scale <= 0):
        raise WpPcaModelError("标准差向量必须为正值")

    # 4. 元数据完整性检查
    curve_metadata = params["curve_metadata"]
    if curve_metadata is None:
        raise WpPcaModelError("曲线元数据不能为None")

    pca_curve_name = params["pca_curve_name"]
    if not pca_curve_name or not isinstance(pca_curve_name, str):
        raise WpPcaModelError("PCA曲线名称必须是非空字符串")


def select_principal_components(
    pca_model: PCAParameters,
    pca_data_all: WpDepthIndexedDatasetBase,
    n_components: int
) -> WpDepthIndexedDatasetBase:
    """从所有主成分中选择指定数量的主成分。

    Architecture
    ------------
    层次/依赖: PCA主成分选择器，元数据同步更新
    设计原则: 主成分选择、元数据维护、数据一致性
    性能特征: 轻量级操作、快速选择、内存优化

    Args:
        pca_model: PCA模型参数
        pca_data_all: 包含所有主成分的数据
        n_components: 要选择的主成分数量

    Returns:
        包含选定主成分的数据集

    Raises:
        WpPcaModelError: 主成分选择异常
    """
    from ..exceptions import WpPcaModelError
    import structlog

    logger = structlog.get_logger(__name__)

    try:
        # 1. 主成分数量验证
        max_components = pca_model["n_components"]
        if n_components <= 0:
            raise WpPcaModelError(f"选择的主成分数量必须大于0，当前值: {n_components}")

        if n_components > max_components:
            raise WpPcaModelError(
                f"选择的主成分数量({n_components})超过可用数量({max_components})"
            )

        # 2. 获取系统曲线和PCA曲线信息
        system_curves = pca_data_all.curve_metadata.get_system_curves()
        system_columns = pca_data_all.curve_metadata.get_dataframe_columns_for_curves(system_curves)

        pca_curve_name = pca_model["pca_curve_name"]
        pca_columns = pca_data_all.curve_metadata.get_dataframe_columns_for_curves([pca_curve_name])

        # 3. 选择前n_components个主成分数据
        selected_pca_columns = pca_columns[:n_components]

        # 4. 构建新的DataFrame
        selected_df = pca_data_all.df[system_columns + selected_pca_columns].copy()

        # 5. 更新曲线元数据
        new_curve_metadata = copy.deepcopy(pca_data_all.curve_metadata)

        # 获取原PCA曲线属性
        original_pca_curve = new_curve_metadata.get_curve(pca_curve_name)
        if original_pca_curve is None:
            raise WpPcaModelError(f"找不到PCA曲线: {pca_curve_name}")

        # 创建新的PCA曲线属性（减少元素数量）
        new_element_names = [f"{pca_curve_name}[{i+1}]" for i in range(n_components)]
        new_dataframe_element_names = [f"{pca_curve_name}_{i+1}" for i in range(n_components)]

        updated_pca_curve = CurveBasicAttributes.create_2d_composite_curve(
            name=pca_curve_name,
            element_count=n_components,
            unit=original_pca_curve.unit,
            category=original_pca_curve.category,
            description=f"PCA主成分分析结果，包含前{n_components}个主成分",
            element_names=new_element_names,
            dataframe_element_names=new_dataframe_element_names
        )

        # 更新元数据
        new_curve_metadata.curves[pca_curve_name] = updated_pca_curve

        # 6. 创建新的数据集
        dataset_class = type(pca_data_all)
        selected_dataset = dataset_class(
            name=f"{pca_data_all.name}_selected_{n_components}",
            df=selected_df,
            curve_metadata=new_curve_metadata,
            ext_attr_manager=copy.deepcopy(pca_data_all.ext_attr_manager) if pca_data_all.ext_attr_manager else None
        )

        logger.info(
            "主成分选择完成",
            original_dataset=pca_data_all.name,
            selected_dataset=selected_dataset.name,
            original_components=max_components,
            selected_components=n_components,
            pca_curve_name=pca_curve_name
        )

        return selected_dataset

    except Exception as e:
        if isinstance(e, WpPcaModelError):
            raise
        else:
            raise WpPcaModelError(f"主成分选择过程中发生错误: {str(e)}") from e


def compute_explained_variance(pca_model: PCAParameters) -> tuple[np.ndarray, np.ndarray]:
    """计算方差解释比例。

    Architecture
    ------------
    层次/依赖: PCA方差分析器，决策支持
    设计原则: 方差分析、累积计算、决策支持
    性能特征: 快速计算、轻量级操作

    Args:
        pca_model: PCA模型参数

    Returns:
        tuple: (方差解释比例, 累积方差解释比例)
    """
    # 1. 方差解释比例提取
    explained_variance_ratio = pca_model["explained_variance_ratio"]

    # 2. 累积方差计算
    cumulative_variance_ratio = np.cumsum(explained_variance_ratio)

    return explained_variance_ratio, cumulative_variance_ratio


def build_pca_composite_curve(
    pca_data: np.ndarray,
    curve_name: str,
    n_components: int,
    original_metadata: CurveMetadata
) -> tuple[CurveMetadata, dict[str, str]]:
    """构建PCA二维组合曲线元数据。

    根据WFS规范构建符合标准的二维组合曲线。

    Architecture
    ------------
    层次/依赖: PCA曲线构造器，WFS规范兼容
    设计原则: 规范兼容、元数据完整、类型安全
    性能特征: 快速构造、内存优化、标准兼容

    Args:
        pca_data: PCA结果数据
        curve_name: 曲线基础名称
        n_components: 主成分数量
        original_metadata: 原始曲线元数据

    Returns:
        tuple: (更新后的曲线元数据, DataFrame列名映射)
    """
    try:
        # 1. 创建新的曲线元数据（复制原始元数据）
        new_metadata = copy.deepcopy(original_metadata)

        # 2. 构建PCA二维组合曲线元数据
        # 按WFS规范：元素名称使用方括号，DataFrame列名使用下划线
        element_names = [f"{curve_name}[{i+1}]" for i in range(n_components)]
        dataframe_element_names = [f"{curve_name}_{i+1}" for i in range(n_components)]

        # 3. 创建PCA曲线属性
        pca_curve_attrs = CurveBasicAttributes.create_2d_composite_curve(
            name=curve_name,
            element_count=n_components,
            unit="dimensionless",  # PCA结果为无量纲
            category=WpCurveCategory.COMPUTED,
            description=f"PCA主成分分析结果，包含{n_components}个主成分",
            element_names=element_names,
            dataframe_element_names=dataframe_element_names
        )

        # 4. 移除原始数据曲线，只保留系统曲线
        system_curves = new_metadata.get_system_curves()
        curves_to_remove = []
        for curve_name_key in new_metadata.curves.keys():
            if curve_name_key not in system_curves:
                curves_to_remove.append(curve_name_key)

        for curve_name_key in curves_to_remove:
            del new_metadata.curves[curve_name_key]

        # 5. 添加PCA曲线
        new_metadata.add_curve(pca_curve_attrs)

        # 6. 生成DataFrame列名映射
        column_mapping = {}
        for i, element_name in enumerate(element_names):
            column_mapping[element_name] = dataframe_element_names[i]

        return new_metadata, column_mapping

    except Exception as e:
        from ..exceptions import WpPcaModelError
        raise WpPcaModelError(f"构建PCA二维组合曲线时发生错误: {str(e)}") from e
