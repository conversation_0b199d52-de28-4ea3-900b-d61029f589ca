# 指南：在 Windows 上通过 WSL2 配置 TensorFlow GPU 开发环境

> **版本**: 1.0
> **目标**: 在 Windows 系统上，利用 WSL2 (Windows Subsystem for Linux) 搭建一个支持最新版 TensorFlow (2.11+) GPU 加速的开发环境。
> **适用场景**: 解决 TensorFlow 2.11 及以上版本不再支持原生 Windows GPU 的问题。

---

## 简介：为何选择 WSL2？

自 TensorFlow 2.11 版本起，官方停止了对原生 Windows 环境的 GPU 支持。WSL2 是微软官方提供的解决方案，它在 Windows 系统内部运行一个完整的、真实的 Linux 内核，具备以下优势：

- **原生性能**: 提供了近乎原生的 Linux 性能，特别是在文件 I/O 和 GPU 计算方面。
- **完全兼容**: 可以在 Windows 上无缝运行为 Linux 构建的 GPU 加速软件（如 TensorFlow, PyTorch, CuPy 等）。
- **无缝集成**: 与 VS Code 等开发工具完美集成，提供统一的开发体验。

本指南将引导您完成从零到一的完整配置过程。

---

## 关键概念：“驾驶舱”与“引擎”的分离

在开始之前，理解 WSL2 的工作模式至关重要。您**不需要**放弃熟悉的 Windows 环境。可以将整个系统想象成一辆高性能汽车：

- **您的“驾驶舱” (Windows + VS Code):** 这是您工作的地方。您使用 Windows 的图形界面、文件资源管理器，以及您最常用的、**安装在 Windows 上的 VS Code**。

- **您的“引擎” (WSL2 - Linux):** 这是提供动力的地方。它在后台运行，负责编译、执行 Python 代码、管理 Conda 环境，并直接与 GPU 通信。

- **连接桥梁 (VS Code WSL 插件):** 这是让“驾驶舱”能够无缝控制“引擎”的魔法。

**结论：您在 Windows 的舒适驾驶舱里进行所有操作，但享受的是 Linux 引擎带来的全部性能和兼容性。**

> 您无需在黑色的 Linux 终端窗口中进行日常开发。所有工作都可以在 VS Code 这个统一的界面中完成。

---

## 第一步：系统要求与准备

1.  **操作系统**: 确保您的系统是 **Windows 10 (21H2 或更高版本)** 或 **Windows 11**。
2.  **硬件虚拟化**: 确保在您电脑的 BIOS/UEFI 中已开启硬件虚拟化。
    *   **如何检查**: 打开 **任务管理器** -> **性能** 选项卡 -> 点击 **CPU** -> 查看右下角的 **“虚拟化”** 是否显示 **“已启用”**。

---

## 第二步：一键安装 WSL2 和 Ubuntu

微软已将安装过程极大简化。您只需一个命令即可完成所有基础设置。

1.  以 **管理员身份** 打开 **PowerShell** 或 **Command Prompt**。
    *   在开始菜单搜索 `PowerShell`，右键点击，选择“以管理员身份运行”。

2.  执行以下命令：
    ```powershell
    wsl --install
    ```
    > **说明**: 此命令会自动完成以下所有操作：
    > 1. 启用 WSL 和虚拟机平台所需的所有 Windows 功能。
    > 2. 下载并安装最新的 Linux 内核。
    > 3. 将 WSL2 设置为默认版本。
    > 4. 下载并安装 **Ubuntu** 作为您的默认 Linux 发行版。

3.  **重启电脑**: 根据提示，完成安装后请重启您的计算机。

4.  **初始化 Ubuntu**: 重启后，Ubuntu 终端窗口会自动启动。您需要根据提示为您的 Linux 环境创建一个**新的用户名和密码**。
    *   **注意**: 这个用户名和密码与您的 Windows 账户无关，请牢记。

---

## 第三步：安装 NVIDIA 驱动 for WSL

这是让 WSL2 中的 Linux 系统能够访问您物理 GPU 的关键一步。

1.  **确认驱动**: 您提供的 `nvidia-smi` 输出显示您的驱动版本为 `576.57`，这是一个非常新的版本，**已经原生支持 WSL2**。通常情况下，您无需任何操作。

2.  **验证驱动**: 在 **WSL (Ubuntu) 终端** 中（而不是 Windows 的 CMD 或 PowerShell），运行以下命令：
    ```bash
    nvidia-smi
    ```
    如果能看到和在 Windows 中完全一样的 GPU 信息输出，则证明驱动配置成功。

---

## 第四步：在 WSL2 中配置 Conda 和项目环境

现在，所有的操作都在 **WSL (Ubuntu) 终端** 中进行。

1.  **安装 Miniconda for Linux**:
    ```bash
    # 更新包列表
    sudo apt update && sudo apt upgrade -y

    # 下载 Miniconda 安装脚本
    wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh

    # 运行安装脚本
    bash Miniconda3-latest-Linux-x86_64.sh
    ```
    *   在安装过程中，请按 `Enter` 和输入 `yes` 来同意许可协议。
    *   当询问是否要运行 `conda init` 时，务必输入 `yes`。

2.  **重启终端**: 关闭当前的 WSL 终端，然后重新打开一个新的。您会看到命令行前面出现了 `(base)`，表示 Conda 已被激活。

3.  **访问项目文件**:
    WSL2 会自动将您的 Windows 盘符挂载到 `/mnt/` 目录下。例如，您的 `F:` 盘在 WSL 中对应的路径是 `/mnt/f/`。
    ```bash
    # 进入您的项目目录 (请根据您的实际路径调整)
    cd /mnt/f/X/20250924-26.IFEDC202519960/30.dev/V2/scape_project
    ```

4.  **创建并激活 Conda 环境**:
    ```bash
    # 根据项目要求创建环境
    conda create --name scape_env python=3.12 -y

    # 激活环境
    conda activate scape_env
    ```

5.  **安装项目依赖 (目标: TensorFlow 2.19)**:
    由于现在处于 Linux 环境 (`sys_platform != 'win32'`)，`pyproject.toml` 中的平台标记会自动选择安装支持 GPU 的 TensorFlow 2.19.x 版本。
    ```bash
    # 安装机器学习相关的依赖
    # pip会根据pyproject.toml的规则，在WSL2中安装tensorflow>=2.19.0
    pip install -e .[ml]
    ```

6.  **最终验证**: 在激活了 `scape_env` 的 WSL 终端中，运行我们的检查脚本。
    ```bash
    python scripts/gpu/tf_gpu_check.py
    ```
    此时，您应该能看到脚本成功检测到您的 Quadro RTX 4000 GPU，并能成功执行矩阵乘法！

---

## 第五步：与 VS Code 无缝集成

这是提升开发体验最重要的一步，它能让您像在 Windows 上一样方便地在 WSL2 中进行开发。

1.  **安装 VS Code 插件**:
    在您的 **Windows 上的 VS Code** 中，打开扩展市场，搜索并安装由 Microsoft 官方发布的 **“WSL”** 插件。

2.  **连接到 WSL**:
    安装插件后，VS Code 左下角会出现一个绿色的 `><` 图标。
    *   点击该图标。
    *   在弹出的菜单中选择 **“Connect to WSL”**。

3.  **在 WSL 中打开项目**:
    *   VS Code 会打开一个新窗口，此窗口已完全运行在 WSL 环境中。
    *   在新窗口中，通过菜单 `File > Open Folder...` 打开您的项目。
    *   **重要**: 路径请使用 WSL 的路径，即 `/mnt/f/X/.../scape_project`。

4.  **选择 Python 解释器**:
    *   打开项目后，VS Code 可能会提示您选择 Python 解释器。
    *   点击右下角的解释器版本号，或按 `Ctrl+Shift+P` 并搜索 `Python: Select Interpreter`。
    *   从列表中选择带有 `('scape_env':conda)` 标记的解释器。

5.  **开始开发**:
    *   现在，当您在 VS Code 中打开一个新终端 (`Ctrl+`` `) 时，它将是一个**自动激活了 `scape_env` 环境的 WSL 终端**。
    *   您可以直接运行、调试代码，所有操作都将利用 WSL2 中的 GPU 环境，体验与原生 Windows 开发完全一致。

---

## 总结

您已成功搭建了一套现代、高效且功能强大的深度学习开发环境。现在，您可以：
- 在 Windows 上享受 Linux 的全部生态和性能。
- 使用最新版的 TensorFlow (2.19+) 并获得完整的 GPU 加速支持。
- 通过 VS Code + WSL 插件获得无缝、统一的开发体验。

祝您编码愉快！

---

## 附录：常见问题解答 (FAQ)

### Q: 为什么不直接改用 PyTorch？它在原生 Windows 上不是有更好的 GPU 支持吗？

**A:** 这是一个非常好的问题。是的，PyTorch 确实为原生 Windows 提供了优秀的 GPU 支持，避免了配置 WSL2 的步骤。

然而，对于 **SCAPE 项目** 而言，我们选择坚持使用 TensorFlow + WSL2 的方案，原因如下：

1.  **保留核心设计**: 本项目的核心算法 `OBMIQ` 已经围绕 TensorFlow/Keras 的函数式 API 进行了详细的设计和文档化。切换到 PyTorch 意味着需要**从零开始重写整个模型**，工作量巨大。

2.  **兼容现有工具链**: 我们的超参数寻优方案基于 `KerasTuner`，它与 TensorFlow/Keras 无缝集成。切换框架将导致整个工具链的更换和重写。

3.  **保护文档资产**: 项目所有的方法说明、设计文档和伪代码均基于 TensorFlow。保持技术栈的一致性是对这些宝贵资产的最大保护。

**结论**: 虽然 WSL2 需要一次性的环境配置，但这个成本远低于因更换深度学习框架而导致的整个项目核心代码和文档的重构。因此，**TensorFlow + WSL2 是本项目在当前阶段最高效、最稳妥的技术路径**。
