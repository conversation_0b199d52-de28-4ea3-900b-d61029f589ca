"""
logwp.attr.predefined - 预定义属性管理

提供T2_Axis等标准属性的简化实现，专注核心功能。

Architecture
------------
层次/依赖: attr层预定义属性管理，依赖types、constants
设计原则: 简化设计、实用主义、核心功能优先
性能特征: 高效计算、内存优化、类型安全

Core Components
---------------
- **t2_axis**: T2_Axis预定义属性处理器
- **t2_axis_domain**: T2轴领域对象

Examples
--------
>>> from logwp.attr.predefined import T2AxisProcessor, T2AxisLog10, T2AxisExp2
>>>
>>> # 使用T2轴工厂创建领域对象
>>> processor = T2AxisProcessor()
>>> json_data = {"Axis_Type": "log10", "T2_Start": {...}, ...}  # 来自WP文件的JSON数据
>>> t2_axis = processor.create_domain_object(json_data)
>>> assert isinstance(t2_axis, T2AxisLog10)
>>> t2_values = t2_axis.calculate_values()

References
----------
- 《SCAPE_SAD_软件架构设计.md》FAP-1到FAP-10 - 格式无关原则
- 《SCAPE_CCG_编码与通用规范.md》CS-2 - 包前缀命名规范
"""

from __future__ import annotations

# T2_Axis预定义属性
from .t2_axis import T2AxisProcessor
from .t2_axis_domain import T2AxisLog10, T2AxisExp2

__all__ = [
    # T2_Axis处理器
    "T2AxisProcessor",

    # T2轴领域对象
    "T2AxisLog10",
    "T2AxisExp2",
]
