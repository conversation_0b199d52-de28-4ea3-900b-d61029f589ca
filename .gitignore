# =============================================================================
# SCAPE 现代化项目 .gitignore 文件
# 基于现代化技术栈：Python 3.11+, ruff, structlog, pydantic v2, GPU计算
# =============================================================================

# =============================================================================
# Python 核心文件
# =============================================================================

# 编译文件
*.pyc
*.pyo
*.pyd
__pycache__/
*.so
*.dll
*.dylib

# SCAPE
# 忽略 scape_case/jupyter/ 目录下任何深度的 output* 文件夹内的所有内容
scape_case/jupyter/**/output*/

# Python 环境和现代包管理器
venv/
env/
ENV/
env.bak/
venv.bak/
.Python
.venv/
venv_new/
pyvenv.cfg

# 现代包管理器缓存和配置
.pdm-python
.pdm-build/
__pypackages__/
.uv/
.uv-cache/
.poetry/
poetry.lock
pdm.lock
uv.lock

# pip 相关
pip-log.txt
pip-delete-this-directory.txt
pip-wheel-metadata/

# 构建和分发
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# =============================================================================
# 现代化开发工具缓存
# =============================================================================

# ruff (现代化代码质量工具)
.ruff_cache/

# mypy (类型检查)
.mypy_cache/
.dmypy.json
dmypy.json

# pytest (测试框架)
.pytest_cache/
.coverage
.coverage.*
coverage.xml
*.cover
*.py,cover
htmlcov/
.hypothesis/
.tox/
.nox/

# 移除的老工具缓存 (已被ruff替代)
# .black_cache/     # 已被ruff替代
# .isort_cache/     # 已被ruff替代
# .flake8_cache/    # 已被ruff替代
# .pylint.d/        # 已被ruff替代

# =============================================================================
# GPU 计算和现代化机器学习
# =============================================================================

# GPU 计算缓存 (cupy, cudf, numba)
.cupy/
.cudf_cache/
.numba_cache/
__numba_cache__/
.cuda_cache/
.gpu_cache/

# 现代化机器学习工具
.joblib/
joblib_cache/
.optuna/
optuna-storage.db
optuna.db
.xgboost_cache/
.lightgbm_cache/

# GPU 性能分析
*.nvprof
*.nvvp
*.qdrep
gpu_profile_*.txt
cuda_profile_*.log

# =============================================================================
# 异步 I/O 和性能监控
# =============================================================================

# 异步 I/O 缓存
.aiofiles_cache/
.asyncio_cache/
async_temp/

# 性能分析文件
.prof
*.prof
profile_*.txt
memory_profiler_*.dat
.memory_profiler/
py-spy-*.svg
py-spy-*.txt
.py-spy/
mprofile_*.dat
*.lprof
.line_profiler/

# =============================================================================
# 现代化日志和配置
# =============================================================================

# structlog 日志文件
*.log
*.out
*.err
logs/
.structlog/

# 现代化配置文件 (TOML优先)
config.toml.local
config.yaml.local
config.yml.local
local_config.toml
local_config.yaml
local_config.yml
settings.toml.local
settings.yaml.local
settings.yml.local
pyproject.toml.bak

# 环境变量文件
.env
.env.local
.env.*.local
.envrc

# 移除的老日志工具配置 (已被structlog替代)
# .loguru/          # 已被structlog替代
# .rich_console/    # 保留rich用于CLI美化

# =============================================================================
# IDE 和编辑器
# =============================================================================

# Visual Studio Code
.vscode/
.history/

# PyCharm
.idea/

# Cursor AI
.cursor/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim/Neovim
*.swp
*.swo
.netrwhist

# Emacs
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统文件
# =============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride
Icon

# Windows
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*~
.nfs*

# =============================================================================
# SCAPE 项目特定文件
# =============================================================================

# 输出目录和报告
reports/
scape_study_cases/*/output/
scape_study_cases/*/results/
scape_case/*/output/
scape_case/*/results/

# 临时文件
*.tmp
*.temp
*.bak
*.backup
temp_data/
scratch/
playground/
sandbox/

# 性能基准测试结果
benchmark_results/
performance_logs/

# =============================================================================
# 数据文件
# =============================================================================

# 大型数据文件
*.xlsx
*.xls
*.csv
!tests/test_data/*.xlsx
!tests/test_data/*.xls
!tests/test_data/*.csv
!docs/examples/*.xlsx
!docs/examples/*.csv

# 机器学习模型和序列化文件
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.npy
*.npz
*.model
*.ckpt
*.pt
*.pth
*.xgb
*.lgb

# 模型检查点和权重
checkpoints/
weights/
saved_models/
model_artifacts/

# =============================================================================
# 可视化输出
# =============================================================================

# 图像文件
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.pdf
!docs/images/*.png
!docs/images/*.jpg
!docs/images/*.svg
!tests/test_data/images/*

# Plotly 输出
*.html
!docs/*.html
.plotly/
plotly-cache/

# Matplotlib 配置
matplotlibrc
.matplotlib/

# =============================================================================
# 文档生成
# =============================================================================

# Sphinx 文档
_build/
.doctrees/
docs/_build/
docs/build/

# 其他文档工具
.pdoc/
.pdoc_cache/
api_docs/

# =============================================================================
# Jupyter Notebook
# =============================================================================

.ipynb_checkpoints
*/.ipynb_checkpoints/*
profile_default/
ipython_config.py
.jupyter/
.local/share/jupyter/
.jupyterlab-settings/
.ipython/

# =============================================================================
# 安全和敏感信息
# =============================================================================

# 密钥和证书
*.pem
*.key
*.crt
*.cert
secrets.json
config.json

# API 密钥
api_keys.txt
.api_keys
credentials.json

# =============================================================================
# 其他工具和缓存
# =============================================================================

# 版本控制
.hg/
.svn/

# 压缩文件
*.7z
*.rar
*.zip
*.tar.gz
*.tgz

# 系统监控
psutil_*.log
system_monitor.log

# 实验和临时数据
experiments/
temp_results/
debug_output/

# =============================================================================
# 现代化技术栈特定忽略
# =============================================================================

# pydantic v2 缓存
.pydantic_cache/
__pydantic_cache__/

# structlog 配置和缓存
.structlog_cache/
structlog_config.json.local

# GPU 计算临时文件
gpu_temp/
cuda_temp/
.gpu_memory_pool/

# 异步 I/O 临时文件
async_temp/
.async_cache/

# 现代化包管理器特定
.pdm.toml.bak
poetry.toml.bak
uv.toml.bak

# Feature Flag 配置
feature_flags.json.local
.feature_flags/

# 性能监控输出
performance_*.json
memory_usage_*.log
gpu_usage_*.log

# 开发模式临时文件
dev_temp/
debug_temp/
test_temp/
