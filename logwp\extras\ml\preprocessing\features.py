"""
特征工程函数，提供如深度对齐等功能。
"""
from typing import Tuple
import numpy as np
import pandas as pd

try:
    from scipy.signal import correlate
    from scipy.interpolate import interp1d
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False


def depth_aligner(
    reference_series: pd.Series,
    target_series: pd.Series,
    *,
    max_shift: float,
    interpolation_method: str = 'linear'
) -> Tuple[pd.Series, float, float]:
    """
    使用互相关算法，将目标测井曲线与参考曲线进行深度对齐。

    此函数通过寻找能使两条曲线形态最匹配的深度偏移量，来校正由于
    电缆伸缩或测量起点不同导致的深度不一致问题。

    Args:
        reference_series (pd.Series): 参考曲线。这是一个Pandas Series，
                                      其索引(index)为深度，值(values)为曲线值。
        target_series (pd.Series):    需要对齐的目标曲线。这也是一个Pandas Series，
                                      其索引为深度，值为曲线值。
        max_shift (float):            允许的最大深度偏移量（正负均可）。
                                      函数将在此范围内搜索最佳匹配。单位与深度索引一致。
        interpolation_method (str):   在移动目标曲线后，用于在新的深度点上
                                      生成值的插值方法，默认为 'linear'。

    Returns:
        Tuple[pd.Series, float, float]: 一个包含三项的元组：
        - aligned_series (pd.Series): 已经对齐到参考曲线深度的新目标曲线。
        - best_shift (float):         计算出的最佳深度偏移量。正值表示目标曲线
                                      需要向下移动，负值表示向上移动。
        - max_correlation (float):    在最佳偏移位置上，两条曲线的归一化互相关系数，
                                      可作为对齐质量的评估指标（范围-1到1）。
    """
    if not SCIPY_AVAILABLE:
        raise ImportError("此功能需要 'scipy' 库。请运行 'pip install scipy' 进行安装。")

    # 1. 输入验证和准备
    if not isinstance(reference_series, pd.Series) or not isinstance(target_series, pd.Series):
        raise TypeError("输入必须是 pandas.Series 对象。")
    if reference_series.empty or target_series.empty:
        raise ValueError("输入 Series 不能为空。")

    ref = reference_series.dropna()
    tar = target_series.dropna()

    # 2. 确定公共深度网格用于互相关
    ref_interval = np.mean(np.diff(ref.index)) if len(ref.index) > 1 else 0.1
    tar_interval = np.mean(np.diff(tar.index)) if len(tar.index) > 1 else 0.1
    resample_interval = min(ref_interval, tar_interval) / 2.0

    start_depth = max(ref.index.min(), tar.index.min())
    end_depth = min(ref.index.max(), tar.index.max())

    if start_depth >= end_depth:
        raise ValueError("参考曲线和目标曲线没有重叠的深度范围。")

    grid_depths = np.arange(start_depth, end_depth, resample_interval)

    ref_interp = np.interp(grid_depths, ref.index, ref.values)
    tar_interp = np.interp(grid_depths, tar.index, tar.values)

    # 标准化（z-score），以便互相关不受幅值影响
    ref_norm = (ref_interp - np.mean(ref_interp)) / (np.std(ref_interp) or 1)
    tar_norm = (tar_interp - np.mean(tar_interp)) / (np.std(tar_interp) or 1)

    # 3. 计算互相关
    correlation = correlate(ref_norm, tar_norm, mode='full')
    lags = np.arange(-len(tar_norm) + 1, len(ref_norm))
    max_shift_samples = int(abs(max_shift) / resample_interval)

    search_mask = (lags >= -max_shift_samples) & (lags <= max_shift_samples)
    if not np.any(search_mask):
        raise ValueError(f"最大偏移量 {max_shift} 太小，无法在重采样网格上进行搜索。")

    lags_in_range = lags[search_mask]
    correlation_in_range = correlation[search_mask]

    best_lag_index = np.argmax(correlation_in_range)
    best_lag = lags_in_range[best_lag_index]
    max_corr_value = correlation_in_range[best_lag_index] / len(grid_depths)
    best_shift = -best_lag * resample_interval

    # 4. 应用偏移并插值到原始参考深度
    shifted_target_index = target_series.index + best_shift
    interp_func = interp1d(shifted_target_index, target_series.values, kind=interpolation_method, bounds_error=False, fill_value=np.nan)
    aligned_values = interp_func(reference_series.index)
    aligned_series = pd.Series(aligned_values, index=reference_series.index, name=target_series.name + "_aligned")

    return aligned_series, best_shift, max_corr_value
