"""scape.core.obmiq_baselines - OBMIQ Baselines Component

This package provides a classical machine learning approach (Scikit-learn based)
to quantify oil-based mud invasion, serving as a baseline model to the
PyTorch-based `scape.core.obmiq` component.

It follows the standard multi-step component architecture, offering distinct
training and prediction steps.
"""
from __future__ import annotations

# Import plot profiles to ensure they are registered in the global registry
# when this package is imported.
from . import plot_profiles

# Import the public API for the training step
from .training_facade import run_obmiq_baselines_training_step
from .config import ObmiqBaselinesTrainingConfig
from .constants import ObmiqBaselinesTrainingArtifacts

# Import the public API for the prediction step
from .prediction_facade import run_obmiq_baselines_prediction_step
from .config import ObmiqBaselinesPredictionConfig
from .constants import ObmiqBaselinesPredictionArtifacts

# Import shared utilities
from .artifact_handler import ObmiqBaselinesArtifactHandler
from .constants import ObmiqBaselinesPlotProfiles

# Define the public API of the package
__all__ = [
    "run_obmiq_baselines_training_step", "ObmiqBaselinesTrainingConfig", "ObmiqBaselinesTrainingArtifacts", "run_obmiq_baselines_prediction_step", "ObmiqBaselinesPredictionConfig", "ObmiqBaselinesPredictionArtifacts", "ObmiqBaselinesArtifactHandler", "ObmiqBaselinesPlotProfiles", "plot_profiles"
]
