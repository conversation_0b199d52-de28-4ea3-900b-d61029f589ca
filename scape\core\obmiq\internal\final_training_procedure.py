"""scape.core.obmiq.internal.final_training_procedure - OBMIQ最终模型训练规程

本模块负责使用找到的最佳超参数，在全部数据上训练最终的生产模型。

Architecture
------------
层次/依赖: scape/core/obmiq/internal层，被training_facade调用
设计原则: 封装最终训练逻辑，包括早停、模型保存和产物打包

Functions:
    train_final_model: 执行最终模型训练并返回模型资产包。

References:
    - 《scape_core_obmiq_pytorch版开发计划.md》§2.5, §5
"""
from __future__ import annotations

import copy
from typing import Any, Dict, Optional, Tuple

import numpy as np
import pandas as pd
import torch
from logwp.infra import get_logger
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

from ..config import ObmiqTrainingConfig
from .data_handler import OBMIQDataset, prepare_final_preprocessors
from .model_builder import OBMIQPyTorchModel, AdaptiveLossModule
from .tuning_procedure import _set_seed

logger = get_logger()


class _GraphTracerWrapper(torch.nn.Module):
    """一个包装器，用于适配TensorBoard的add_graph对字典输入模型的追踪。

    它接收一个模型和输入键的列表，其forward方法接收*args，然后在内部
    将它们重新组装成字典，再传递给原始模型。
    """
    def __init__(self, model: OBMIQPyTorchModel, input_keys: list[str]):
        super().__init__()
        self.model = model
        self.input_keys = input_keys

    def forward(self, *inputs) -> torch.Tensor:
        input_dict = {key: val for key, val in zip(self.input_keys, inputs)}
        return self.model(input_dict)
class _TensorBoardLogger:
    """一个简单的TensorBoard日志记录器包装类。"""

    def __init__(self, log_dir: Optional[str] = None):
        self.writer = SummaryWriter(log_dir=log_dir) if log_dir else None

    def log_scalars(self, main_tag: str, tag_scalar_dict: Dict[str, float], step: int):
        if self.writer:
            self.writer.add_scalars(main_tag, tag_scalar_dict, step)

    def log_graph(self, model: torch.nn.Module, input_to_model: Dict[str, torch.Tensor]):
        if self.writer:
            # JIT tracer需要一个接受*args的forward方法。我们创建一个包装器来
            # 适配我们模型那样的接受字典输入的forward方法。
            wrapper = _GraphTracerWrapper(model, list(input_to_model.keys()))
            # 使用包装后的模型和解包后的输入张量列表进行追踪
            self.writer.add_graph(wrapper, list(input_to_model.values()))

    def close(self):
        if self.writer:
            self.writer.close()

def _create_loader_from_preprocessed(
    df: pd.DataFrame,
    config: ObmiqTrainingConfig,
    feature_selectors: Dict[str, Any],
    preprocessors: Dict[str, Any],
    shuffle: bool,
) -> DataLoader:
    """使用已拟合的预处理器创建DataLoader。"""
    seq_cols = feature_selectors["sequence_cols"]
    norm_col = feature_selectors["normalization_col"]
    tab_cols = feature_selectors["tabular_cols"]
    target_cols = feature_selectors["target_cols"]

    # 1. 转换数据
    df_processed = df.copy()
    df_processed[tab_cols] = preprocessors["tabular_scaler"].transform(df[tab_cols])

    # 2. 提取数组
    sequence_values = df_processed[seq_cols].values
    norm_values = df_processed[norm_col].values[:, np.newaxis]
    norm_values[norm_values == 0] = 1e-6
    normalized_sequence = sequence_values / norm_values

    tabular_values = df_processed[tab_cols].values
    target_values = df_processed[target_cols].values

    # 3. 创建Dataset和DataLoader
    dataset = OBMIQDataset(normalized_sequence, tabular_values, target_values)
    return DataLoader(dataset, batch_size=config.batch_size, shuffle=shuffle, num_workers=0)


def train_final_model(
    train_df: pd.DataFrame,
    best_hps: Dict[str, Any],
    config: ObmiqTrainingConfig,
    feature_selectors: Dict[str, Any],
    data_shapes: Dict[str, int],
    metadata: Dict[str, Any],
    tb_log_dir: Optional[str] = None,
) -> Tuple[Dict[str, Any], pd.DataFrame, pd.DataFrame]:
    """
    使用最佳超参数在全部数据上训练最终模型。

    Args:
        train_df: 包含所有训练井的完整DataFrame。
        best_hps: 在交叉验证中找到的最佳超参数字典。
        config: 训练配置。
        feature_selectors: 包含特征列名的字典。
        data_shapes: 包含数据维度信息的字典。
        metadata: 需要与模型一同保存的元数据字典。
        tb_log_dir: TensorBoard日志的保存目录。

    Returns:
        一个元组 (model_assets, history_df, final_eval_df)，其中 final_eval_df
        是包含最终模型在全部训练数据上评估结果的DataFrame。
    """
    _set_seed(config.random_seed)
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Final model training started on device: {device}")
    tb_logger = _TensorBoardLogger(tb_log_dir)

    # 1. 在全部数据上拟合最终的预处理器
    final_preprocessors = prepare_final_preprocessors(
        train_df, feature_selectors["tabular_cols"]
    )

    # 2. 从全量数据中划分一小部分作为验证集，用于早停
    train_sub_df, val_sub_df = train_test_split(
        train_df, test_size=config.val_split_ratio, random_state=config.random_seed
    )

    # 3. 使用已拟合的预处理器创建DataLoader
    train_loader = _create_loader_from_preprocessed(
        train_sub_df, config, feature_selectors, final_preprocessors, shuffle=True
    )
    val_loader = _create_loader_from_preprocessed(
        val_sub_df, config, feature_selectors, final_preprocessors, shuffle=False
    )

    # 4. 使用最佳超参数构建模型
    model = OBMIQPyTorchModel(best_hps, data_shapes).to(device)
    criterion = AdaptiveLossModule().to(device)
    optimizer = torch.optim.AdamW(
        list(model.parameters()) + list(criterion.parameters()),
        lr=best_hps["learning_rate"],
        weight_decay=best_hps["weight_decay"],
    )

    # 将模型图写入TensorBoard
    sample_batch = next(iter(train_loader))
    sample_inputs = {k: v.to(device) for k, v in sample_batch.items() if k != "target"}
    tb_logger.log_graph(model, sample_inputs)

    # 5. 训练循环与早停
    best_val_loss = float("inf")
    patience_counter = 0
    best_model_state = None
    history = []

    for epoch in range(config.final_train_epochs):
        model.train()
        total_train_loss = 0
        for batch in train_loader:
            inputs = {k: v.to(device) for k, v in batch.items() if k != "target"}
            targets = batch["target"].to(device)
            optimizer.zero_grad()
            predictions = model(inputs)
            loss = criterion(predictions, targets)
            loss.backward()
            optimizer.step()
            total_train_loss += loss.item()

        model.eval()
        total_val_loss = 0
        val_predictions = []
        val_targets = []
        with torch.no_grad():
            for batch in val_loader:
                inputs = {k: v.to(device) for k, v in batch.items() if k != "target"}
                targets = batch["target"].to(device)
                predictions = model(inputs)
                val_predictions.append(predictions)
                val_targets.append(targets)
                total_val_loss += criterion(predictions, targets).item()

        avg_val_loss = total_val_loss / len(val_loader)
        avg_train_loss = total_train_loss / len(train_loader)
        logger.info(
            f"Epoch {epoch+1}/{config.final_train_epochs}, "
            f"Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}"
        )

        # --- 新增：计算并记录详细的监控指标 ---
        epoch_metrics = {"epoch": epoch + 1, "loss": avg_train_loss, "val_loss": avg_val_loss}

        # 计算RMSE
        all_preds = torch.cat(val_predictions).cpu().numpy()
        all_targets = torch.cat(val_targets).cpu().numpy()
        target_cols = feature_selectors["target_cols"]
        rmse_metrics = {}
        for i, col_name in enumerate(target_cols):
            rmse = np.sqrt(np.mean((all_preds[:, i] - all_targets[:, i]) ** 2))
            rmse_key = f"rmse_val_{col_name}"
            epoch_metrics[rmse_key] = rmse
            rmse_metrics[col_name] = rmse
        tb_logger.log_scalars("RMSE_val", rmse_metrics, epoch + 1)

        # 记录自适应损失权重
        log_vars_metrics = {}
        for i, log_var in enumerate(criterion.log_vars):
            log_vars_metrics[f"target_{i}"] = log_var.item()
        tb_logger.log_scalars("Log_Vars", log_vars_metrics, epoch + 1)

        # 记录到历史记录和TensorBoard
        history.append(epoch_metrics)
        tb_logger.log_scalars(
            "Loss", {"train": avg_train_loss, "val": avg_val_loss}, epoch + 1
        )
        # --- 监控指标记录结束 ---

        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            best_model_state = copy.deepcopy(model.state_dict())
            patience_counter = 0
        else:
            patience_counter += 1

        if patience_counter >= config.patience:
            logger.info(f"Early stopping triggered at epoch {epoch+1}.")
            break

    # 6. 加载性能最佳的模型权重并打包资产
    if best_model_state:
        tb_logger.writer.add_hparams(best_hps, {"hparam/best_val_loss": best_val_loss})
        model.load_state_dict(best_model_state)
        logger.info(f"Loaded best model state with validation loss: {best_val_loss:.4f}")

    # 7. 在全部数据上进行最终评估，以生成绘图所需的数据快照
    logger.info("Performing final evaluation on the full training dataset...")
    full_train_loader = _create_loader_from_preprocessed(
        train_df, config, feature_selectors, final_preprocessors, shuffle=False
    )
    all_predictions = []
    model.eval()  # 确保模型处于评估模式，这对于Dropout和BatchNorm等层很重要
    with torch.no_grad():
        for batch in full_train_loader:
            inputs = {k: v.to(device) for k, v in batch.items() if k != "target"}
            predictions = model(inputs)
            all_predictions.append(predictions.cpu().numpy())

    predictions_np = np.concatenate(all_predictions, axis=0)

    # 创建评估DataFrame
    final_eval_df = train_df.copy()
    target_cols = feature_selectors["target_cols"]
    pred_cols = [f"{col}_pred" for col in target_cols]
    final_eval_df[pred_cols] = predictions_np

    # 计算残差
    for i, col in enumerate(target_cols):
        final_eval_df[f"{col}_residual"] = final_eval_df[col] - final_eval_df[pred_cols[i]]

    # 8. 打包最终产物
    # 在所有基于GPU的计算（如评估）完成后，再将模型移至CPU以进行序列化
    model_assets = {
        "model_state_dict": model.cpu().state_dict(),
        "model_hyperparameters": best_hps,
        "preprocessors": final_preprocessors,
        "data_shapes": data_shapes,
        "metadata": metadata,
    }
    history_df = pd.DataFrame(history)
    tb_logger.close()

    return model_assets, history_df, final_eval_df
