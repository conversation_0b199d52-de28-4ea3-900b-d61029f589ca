"""可复现的绘图逻辑。

此模块包含所有从数据快照复现图表的函数。
遵循《可追踪机器学习组件开发框架》§4.2的规范，每个函数都接收一个
数据快照路径和一个PlotProfile对象作为输入，以确保100%的可复现性。
"""

from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING, Any, Dict, Optional

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from logwp.extras.plotting import apply_profile, save_figure

if TYPE_CHECKING:
    from logwp.extras.plotting import PlotProfile


def replot_perm_corr_crossplot_from_snapshot(
    snapshot_path: Path,
    plot_profile: PlotProfile,
    output_path: Path,
    metrics: Optional[Dict[str, Any]] = None,
) -> None:
    """从数据快照重新生成渗透率相关性交会图。

    此函数读取一个包含对齐后渗透率数据的CSV文件，并根据提供的
    PlotProfile配置生成交会图。

    Args:
        snapshot_path (Path): 数据快照文件路径 (CSV格式)。
            必须包含 'TRUE_VALUE' 和 'PRED_VALUE' 列。
        plot_profile (PlotProfile): 用于控制图表样式的配置对象。
        output_path (Path): 生成的图表文件的保存路径。
        metrics (Optional[Dict[str, Any]]): 包含要在图上标注的指标的字典。
            期望的结构: `{"correlations": {...}, "hit_rates": {...}}`
    """
    # 1. 加载数据快照
    data = pd.read_csv(snapshot_path)
    if data.empty:
        fig, ax = plt.subplots()
        apply_profile(ax, plot_profile)
        ax.text(0.5, 0.5, "无数据可绘制", ha="center", va="center", transform=ax.transAxes)
        save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
        plt.close(fig)
        return

    # 2. 创建图表并应用基础样式
    fig, ax = plt.subplots()
    apply_profile(ax, plot_profile)

    # 3. 绘制核心数据点
    ax.scatter(
        data["TRUE_VALUE"],
        data["PRED_VALUE"],
        **plot_profile.artist_props.get("scatter", {}),
    )

    # 4. 绘制辅助线 (1:1线, 误差边界线)
    min_val = min(ax.get_xlim()[0], ax.get_ylim()[0])
    max_val = max(ax.get_xlim()[1], ax.get_ylim()[1])
    line_range = np.logspace(np.log10(min_val), np.log10(max_val), 100)

    ax.plot(line_range, line_range, **plot_profile.artist_props.get("line_1x", {}))
    ax.plot(line_range, line_range * 3, **plot_profile.artist_props.get("line_3x", {}))
    ax.plot(line_range, line_range / 3, **plot_profile.artist_props.get("line_3x", {}))
    ax.plot(line_range, line_range * 5, **plot_profile.artist_props.get("line_5x", {}))
    ax.plot(line_range, line_range / 5, **plot_profile.artist_props.get("line_5x", {}))
    ax.plot(line_range, line_range * 10, **plot_profile.artist_props.get("line_10x", {}))
    ax.plot(line_range, line_range / 10, **plot_profile.artist_props.get("line_10x", {}))

    # 5. 添加指标文本标注
    if metrics:
        correlations = metrics.get("correlations", {})
        hit_rates = metrics.get("hit_rates", {})

        corr_text = (
            f"Pearson R (log): {correlations.get('pearson_r', np.nan):.3f}\n"
            f"Spearman ρ: {correlations.get('spearman_rho', np.nan):.3f}\n"
            f"R-squared: {correlations.get('r_squared', np.nan):.3f}"
        )
        # 对hit_rates的键进行排序，以确保文本框中的顺序一致
        hit_rate_text = "\n".join(
            [f"{key.upper()}: {value:.1f}%" for key, value in sorted(hit_rates.items())]
        )
        full_text = f"{corr_text}\n\nHit Rates:\n{hit_rate_text}"

        text_props = plot_profile.artist_props.get("text_box", {})
        ax.text(
            0.05, 0.95, full_text, transform=ax.transAxes,
            verticalalignment='top', **text_props
        )

    # 6. 保存图表
    save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_plt_capture_curve_from_snapshot(
    snapshot_path: Path,
    plot_profile: PlotProfile,
    output_path: Path,
    metrics: Optional[Dict[str, Any]] = None,
) -> None:
    """从数据快照重新生成PLT捕获曲线图。

    此函数读取一个包含捕获曲线坐标的CSV文件，并根据提供的
    PlotProfile配置生成图表。

    Args:
        snapshot_path (Path): 数据快照文件路径 (CSV格式)。
            必须包含 'x' 和 'y' 列。
        plot_profile (PlotProfile): 用于控制图表样式的配置对象。
        output_path (Path): 生成的图表文件的保存路径。
        metrics (Optional[Dict[str, Any]]): 包含要在图上标注的指标的字典。
            期望的结构: `{"auc": ..., "gini": ..., "capture_at_30_percent": ..., "lift_at_30_percent": ...}`
    """
    # 1. 加载数据快照
    data = pd.read_csv(snapshot_path)
    if data.empty:
        fig, ax = plt.subplots()
        apply_profile(ax, plot_profile)
        ax.text(0.5, 0.5, "无数据可绘制", ha="center", va="center", transform=ax.transAxes)
        save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
        plt.close(fig)
        return

    # 2. 创建图表并应用基础样式
    fig, ax = plt.subplots()
    apply_profile(ax, plot_profile)

    # 3. 绘制捕获曲线
    ax.plot(data["x"], data["y"], **plot_profile.artist_props.get("capture_curve", {}))

    # 4. 绘制随机参考线 (1:1对角线)
    ax.plot([0, 1], [0, 1], **plot_profile.artist_props.get("random_line", {}))

    # 5. 添加指标文本标注
    if metrics:
        text = (
            f"AUC: {metrics.get('auc', np.nan):.3f}\n"
            f"Gini: {metrics.get('gini', np.nan):.3f}\n"
            f"Capture@30%: {metrics.get('capture_at_30_percent', np.nan):.1%}\n"
            f"Lift@30%: {metrics.get('lift_at_30_percent', np.nan):.2f}x"
        )

        text_props = plot_profile.artist_props.get("text_box", {})
        ax.text(
            0.5, 0.2, text, transform=ax.transAxes,
            verticalalignment='bottom', **text_props
        )

    # 6. 保存图表
    save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_plt_lorenz_curve_from_snapshot(
    snapshot_path: Path,
    plot_profile: PlotProfile,
    output_path: Path,
    metrics: Optional[Dict[str, Any]] = None,
) -> None:
    """从数据快照重新生成PLT洛伦兹曲线图。

    此函数读取一个包含洛伦兹曲线坐标的CSV文件，并根据提供的
    PlotProfile配置生成图表。

    Args:
        snapshot_path (Path): 数据快照文件路径 (CSV格式)。
            必须包含 'x' 和 'y' 列。
        plot_profile (PlotProfile): 用于控制图表样式的配置对象。
        output_path (Path): 生成的图表文件的保存路径。
        metrics (Optional[Dict[str, Any]]): 包含要在图上标注的指标的字典。
            期望的结构: `{"gini": ...}`
    """
    # 1. 加载数据快照
    data = pd.read_csv(snapshot_path)
    if data.empty:
        fig, ax = plt.subplots()
        apply_profile(ax, plot_profile)
        ax.text(0.5, 0.5, "无数据可绘制", ha="center", va="center", transform=ax.transAxes)
        save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
        plt.close(fig)
        return

    # 2. 创建图表并应用基础样式
    fig, ax = plt.subplots()
    apply_profile(ax, plot_profile)

    # 3. 绘制洛伦兹曲线
    ax.plot(data["x"], data["y"], **plot_profile.artist_props.get("lorenz_curve", {}))

    # 4. 绘制完美平均线 (1:1对角线)
    ax.plot([0, 1], [0, 1], **plot_profile.artist_props.get("equality_line", {}))

    # 5. 添加指标文本标注
    if metrics:
        text = f"Gini: {metrics.get('gini', np.nan):.3f}"

        text_props = plot_profile.artist_props.get("text_box", {})
        ax.text(
            0.5, 0.2, text, transform=ax.transAxes,
            verticalalignment='bottom', **text_props
        )

    # 6. 保存图表
    save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_plt_contribution_crossplot_from_snapshot(
    snapshot_path: Path,
    plot_profile: PlotProfile,
    output_path: Path,
    metrics: Optional[Dict[str, Any]] = None,
) -> None:
    """从数据快照重新生成PLT贡献率交会图。

    此函数读取一个包含各层段预测贡献率和实际贡献率的CSV文件，
    并根据提供的PlotProfile配置生成交会图。

    Args:
        snapshot_path (Path): 数据快照文件路径 (CSV格式)。
            必须包含 'r_plt' 和 'r_pred' 列。
        plot_profile (PlotProfile): 用于控制图表样式的配置对象。
        output_path (Path): 生成的图表文件的保存路径。
        metrics (Optional[Dict[str, Any]]): 包含要在图上标注的指标的字典。
            期望的结构: `{"correlations": {...}}`
    """
    # 1. 加载数据快照
    data = pd.read_csv(snapshot_path)
    if data.empty:
        fig, ax = plt.subplots()
        apply_profile(ax, plot_profile)
        ax.text(0.5, 0.5, "无数据可绘制", ha="center", va="center", transform=ax.transAxes)
        save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
        plt.close(fig)
        return

    # 2. 创建图表并应用基础样式
    fig, ax = plt.subplots()
    apply_profile(ax, plot_profile)

    # 3. 绘制核心数据点
    ax.scatter(
        data["r_pred"],  # X轴：预测贡献率
        data["r_plt"],   # Y轴：实际贡献率
        **plot_profile.artist_props.get("scatter", {}),
    )

    # 4. 绘制1:1对角线
    min_val = min(ax.get_xlim()[0], ax.get_ylim()[0])
    max_val = max(ax.get_xlim()[1], ax.get_ylim()[1])
    ax.plot([min_val, max_val], [min_val, max_val], **plot_profile.artist_props.get("line_1x", {}))

    # 5. 添加指标文本标注
    if metrics:
        correlations = metrics.get("correlations", {})
        text = (
            f"Spearman ρ: {correlations.get('spearman_rho', np.nan):.3f}\n"
            f"Pearson R: {correlations.get('pearson_r', np.nan):.3f}"
        )

        text_props = plot_profile.artist_props.get("text_box", {})
        ax.text(
            0.05, 0.95, text, transform=ax.transAxes,
            verticalalignment='top', **text_props
        )

    # 6. 保存图表
    save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)
