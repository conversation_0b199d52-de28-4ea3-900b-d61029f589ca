"""scape.core.swift_pso.plotting - SWIFT-PSO绘图复现功能

提供从数据快照重新生成图表的功能，支持t-SNE可视化的完全可复现性。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO绘图层
设计原则: 数据快照复现、PlotProfile集成、职责单一
性能特征: 延迟加载、内存优化、格式支持

遵循CCG规范：
- SC-4: 单位一致性
- PF-1: 内存控制
- TS-1: 完整类型注解

References
----------
- 《logwp/extras/tracking/机器学习组件开发框架》§4.2 - 绘图复现与配置
- 《logwp/extras/plotting/README.md》- PlotProfile系统使用指南
"""

from __future__ import annotations

from pathlib import Path
from typing import Any, Optional, Union

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.spatial import ConvexHull
from matplotlib.patches import Polygon

from logwp.extras.plotting import PlotProfile, apply_profile, save_figure
from logwp.infra import get_logger
from logwp.io.exceptions import WpIOError
from logwp.infra.exceptions import ErrorContext

logger = get_logger(__name__)


def replot_tsne_from_snapshot(
    snapshot_path: Path,
    plot_profile: PlotProfile,
    output_path: Path
) -> None:
    """从数据快照重新生成t-SNE可视化图表。

    此函数负责从已保存的t-SNE降维数据快照重新生成图表，
    使用指定的PlotProfile配置，确保绘图的完全可复现性。

    Args:
        snapshot_path: t-SNE数据快照文件路径（CSV格式）
        plot_profile: 绘图配置对象
        output_path: 输出图表文件路径

    Raises:
        FileNotFoundError: 数据快照文件不存在时抛出
        ValueError: 数据格式错误时抛出

    Note:
        此函数只负责从已有产物复现图表，不包含任何业务计算或配置创建逻辑。

    References:
        迁移并重构自 scape/core/swift_pso_backup/internal/tsne_plotter.py
    """
    logger.info(
        "开始从数据快照重新生成t-SNE图表",
        operation="tsne_replot",
        snapshot_path=str(snapshot_path),
        output_path=str(output_path),
        profile_name=plot_profile.name
    )

    # 1. 加载数据快照
    try:
        tsne_data = pd.read_csv(snapshot_path)
    except Exception as e:
        logger.error(
            "加载t-SNE数据快照失败",
            operation="tsne_replot",
            snapshot_path=str(snapshot_path),
            error=str(e)
        )
        raise

    # 2. 验证数据格式
    # 验证数据快照是否包含绘图所需的核心列。
    # 其他列（如iteration, bootstrap_run）是可选的，
    # _draw_tsne_plot_content函数会优雅地处理它们不存在的情况。
    required_columns = ['tsne_x', 'tsne_y', 'cluster_label']
    missing_columns = [col for col in required_columns if col not in tsne_data.columns]
    if missing_columns:
        raise ValueError(f"数据快照缺少必需的列: {missing_columns}")

    if tsne_data.empty:
        raise ValueError("数据快照为空")

    try:
        # 3. 设置seaborn主题并创建图表（参照老版本模式）
        sns.set_theme(style="whitegrid")

        fig, ax = plt.subplots()
        apply_profile(ax, plot_profile)

        # 4. 绘制图表内容
        _draw_tsne_plot_content(ax, tsne_data, plot_profile)

        # 5. 保存图表
        if plot_profile.save_config:
            saved_paths = save_figure(
                fig,
                plot_profile.save_config,
                output_path.parent,
                output_path.stem
            )
            logger.info(
                "t-SNE图表保存成功",
                operation="tsne_replot",
                saved_paths=[str(p) for p in saved_paths]
            )
        else:
            # 回退到简单保存
            fig.savefig(output_path, dpi=300, bbox_inches="tight")
            logger.info(
                "t-SNE图表保存成功（简单模式）",
                operation="tsne_replot",
                path=str(output_path)
            )

    except Exception as e:
        context = ErrorContext(
            operation="tsne_replot",
            file_path=str(output_path)
        )
        raise WpIOError(f"生成t-SNE图表失败: {e}", context=context) from e
    finally:
        plt.close(fig)


def _draw_tsne_plot_content(
    ax: Any,
    tsne_data: pd.DataFrame,
    plot_profile: PlotProfile
) -> None:
    """绘制t-SNE图表的具体内容。

    Args:
        ax: matplotlib Axes对象
        tsne_data: 包含t-SNE结果的DataFrame
        plot_profile: 绘图配置模板

    Note:
        参照老版本 scape/core/swift_pso_backup/internal/tsne_plotter.py 的实现模式
    """
    artist_props = plot_profile.artist_props

    # 检查实际的列名（兼容不同的列名格式）
    tsne_x_col = 'tsne_x' if 'tsne_x' in tsne_data.columns else 'tsne_1'
    tsne_y_col = 'tsne_y' if 'tsne_y' in tsne_data.columns else 'tsne_2'
    cluster_col = 'cluster_label'
    bootstrap_col = 'bootstrap_run'
    lowo_fold_col = 'lowo_fold'
    iteration_col = 'iteration'

    # 步骤1: 绘制收敛轨迹线（如果有多个迭代点）
    trajectory_props = artist_props.get("trajectory", {})
    if iteration_col in tsne_data.columns:
        # 轨迹线按 (bootstrap_run, lowo_fold) 分组
        group_cols = [col for col in [bootstrap_col, lowo_fold_col] if col in tsne_data.columns]
        if not group_cols:
            group_cols = [bootstrap_col]  # Fallback

        for _, group_data in tsne_data.groupby(group_cols):
            sorted_group = group_data.sort_values(iteration_col)
            if len(sorted_group) > 1:
                ax.plot(
                    sorted_group[tsne_x_col],
                    sorted_group[tsne_y_col],
                    **trajectory_props
                )

    # 步骤2: 绘制散点图（按聚类标签着色）
    scatter_props = artist_props.get("scatter", {}).copy()

    # --- 增强：为DBSCAN和K-means动态生成调色板 ---
    unique_labels = sorted(tsne_data[cluster_col].unique())
    is_dbscan_result = -1 in unique_labels
    palette_config = artist_props.get("palette", {})
    palette_name = palette_config.get("name", "viridis")

    if is_dbscan_result:
        # DBSCAN: 噪声点为灰色，其他聚类使用标准调色板
        n_clusters_actual = len(unique_labels) - 1
        colors = sns.color_palette(palette_name, n_colors=max(n_clusters_actual, 1))
        cluster_palette = {
            label: colors[i] for i, label in enumerate([l for l in unique_labels if l != -1])
        }
        cluster_palette[-1] = palette_config.get("noise_color", "#B0B0B0")  # 灰色
    else:
        # K-means: 标准调色板
        n_clusters = len(unique_labels)
        cluster_palette = sns.color_palette(palette_name, n_colors=max(n_clusters, 1))

    sns.scatterplot(
        data=tsne_data,
        x=tsne_x_col,
        y=tsne_y_col,
        hue=cluster_col,
        palette=cluster_palette,  # 使用动态生成的调色板
        ax=ax,
        **scatter_props
    )

    # 新增：步骤2.5: 绘制凸包（如果配置中存在）
    convex_hull_props = artist_props.get("convex_hull")
    if convex_hull_props:
        # 遍历每个聚类
        for i, cluster_id in enumerate(unique_labels):
            # 新增：跳过为噪声点绘制凸包
            if cluster_id == -1:
                continue

            cluster_data = tsne_data[tsne_data[cluster_col] == cluster_id]

            # 凸包至少需要3个点
            if len(cluster_data) >= 3:
                points = cluster_data[[tsne_x_col, tsne_y_col]].values
                try:
                    hull = ConvexHull(points)
                    # 使用与散点图相同的颜色
                    hull_style = convex_hull_props.copy()
                    # 从调色板字典或列表中获取颜色
                    hull_style['facecolor'] = cluster_palette[cluster_id]
                    # 创建并添加多边形
                    polygon = Polygon(points[hull.vertices, :], **hull_style)
                    ax.add_patch(polygon)
                except Exception as e:
                    # 如果点共线，ConvexHull会失败，记录警告但不中断
                    logger.warning(f"为聚类 {cluster_id} 计算凸包失败: {e}", operation="tsne_replot")

    # 步骤3: 标记起点和终点（如果有迭代信息）
    if iteration_col in tsne_data.columns:
        group_cols = [col for col in [bootstrap_col, lowo_fold_col] if col in tsne_data.columns]
        if not group_cols:
            group_cols = [bootstrap_col]  # Fallback

        # 绘制起点
        start_points = tsne_data.loc[
            tsne_data.groupby(group_cols)[iteration_col].idxmin()
        ]
        start_props = artist_props.get("start_point", {}).copy()
        start_label = start_props.pop("label", "Start Point")
        if not start_points.empty:
            ax.scatter(start_points[tsne_x_col], start_points[tsne_y_col], label=start_label, **start_props)

        # 绘制终点
        end_points = tsne_data.loc[
            tsne_data.groupby(group_cols)[iteration_col].idxmax()
        ]
        end_props = artist_props.get("end_point", {}).copy()
        end_label = end_props.pop("label", "End Point")
        if not end_points.empty:
            ax.scatter(end_points[tsne_x_col], end_points[tsne_y_col], label=end_label, **end_props)

    # 调整图例
    _adjust_legend(ax, artist_props.get("legend", {}))


def _adjust_legend(ax: Any, legend_config: dict) -> None:
    """调整图例显示。

    Args:
        ax: matplotlib Axes对象
        legend_config: 图例配置字典

    Note:
        参照老版本的图例调整逻辑，将起点和终点移到最后
    """
    handles, labels = ax.get_legend_handles_labels()

    # 将'Start Point'和'End Point'的图例移到最后
    start_point_items = []
    end_point_items = []
    other_items = []

    for handle, label in zip(handles, labels):
        if label == 'Start Point':
            start_point_items.append((handle, label))
        elif label == 'End Point':
            end_point_items.append((handle, label))
        else:
            # 增强：格式化聚类标签，特别处理DBSCAN的噪声点
            if label == '-1':
                other_items.append((handle, "Noise"))
            else:
                try:
                    cluster_num = int(label)
                    other_items.append((handle, f"Cluster {cluster_num}"))
                except (ValueError, TypeError):
                    other_items.append((handle, label))  # 保持原样

    # 重新排列：其他项 + 起点 + 终点
    reordered_handles = []
    reordered_labels = []

    for handle, label in other_items + start_point_items + end_point_items:
        reordered_handles.append(handle)
        reordered_labels.append(label)

    # 应用图例配置
    legend_props = legend_config.copy()
    ax.legend(
        handles=reordered_handles,
        labels=reordered_labels,
        **legend_props
    )
