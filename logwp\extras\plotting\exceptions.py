"""logwp.extras.plotting.exceptions - 绘图配置服务专属异常

定义绘图配置系统的异常层次结构，遵循SCAPE项目异常处理规范。

Architecture
------------
层次/依赖: logwp.extras.plotting包异常层
设计原则: 结构化异常信息、异常链保持、分层异常树
性能特征: 轻量级异常对象、延迟格式化

遵循CCG规范：
- EH-1: Exception Groups支持（Python 3.11+）
- EH-2: 结构化异常上下文信息
- EH-3: 分层异常树设计
- EH-4: 异常链保持完整性

References
----------
- 《SCAPE_CCG_编码与通用规范》§EH - 现代化异常处理
"""

from __future__ import annotations

from typing import Any, Dict, Optional

from logwp.infra.exceptions import WpError, ErrorContext


class WpPlottingError(WpError):
    """绘图配置服务的基础异常。

    所有plotting包相关的异常都应该继承自这个基类，
    形成清晰的异常层次结构。

    Examples:
        >>> try:
        ...     registry.get("nonexistent_profile")
        ... except WpPlottingError as e:
        ...     logger.error("绘图配置错误", error=str(e))
    """
    pass


class ProfileNotFoundError(WpPlottingError):
    """当找不到指定的绘图配置模板时抛出。

    这是最常见的plotting异常，通常发生在：
    1. 请求不存在的profile名称
    2. 模块级基础模板未注册
    3. 配置文件加载失败

    Attributes:
        profile_name (str): 请求的配置模板名称
        available_profiles (list[str]): 当前可用的配置模板列表

    Examples:
        >>> raise ProfileNotFoundError(
        ...     "Profile 'unknown.profile' not found",
        ...     profile_name="unknown.profile",
        ...     available_profiles=["base", "plt_analyzer.base"]
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        profile_name: Optional[str] = None,
        available_profiles: Optional[list[str]] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.profile_name = profile_name
        self.available_profiles = available_profiles or []


class ProfileRegistrationError(WpPlottingError):
    """配置模板注册过程中发生错误时抛出。

    常见场景：
    1. 重复注册同名profile且overwrite=False
    2. 基础模板循环依赖
    3. 配置模板验证失败

    Attributes:
        profile_name (str): 尝试注册的配置模板名称
        operation (str): 失败的操作类型（register/register_base）

    Examples:
        >>> raise ProfileRegistrationError(
        ...     "Profile 'test.profile' already exists",
        ...     profile_name="test.profile",
        ...     operation="register"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        profile_name: Optional[str] = None,
        operation: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.profile_name = profile_name
        self.operation = operation


class ProfileMergeError(WpPlottingError):
    """配置模板合并过程中发生错误时抛出。

    常见场景：
    1. 深度合并字典时类型冲突
    2. 基础模板链过长导致递归
    3. 配置属性验证失败

    Attributes:
        base_profile_name (str): 基础模板名称
        target_profile_name (str): 目标模板名称
        merge_stage (str): 失败的合并阶段

    Examples:
        >>> raise ProfileMergeError(
        ...     "Cannot merge incompatible types in rc_params",
        ...     base_profile_name="base",
        ...     target_profile_name="test.profile",
        ...     merge_stage="rc_params_merge"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        base_profile_name: Optional[str] = None,
        target_profile_name: Optional[str] = None,
        merge_stage: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.base_profile_name = base_profile_name
        self.target_profile_name = target_profile_name
        self.merge_stage = merge_stage


class ProfileIOError(WpPlottingError):
    """配置模板文件I/O操作失败时抛出。

    常见场景：
    1. JSON配置文件格式错误
    2. 配置目录权限不足
    3. 文件系统错误

    Attributes:
        file_path (str): 相关的文件路径
        operation (str): 失败的I/O操作（load/save/scan）

    Examples:
        >>> raise ProfileIOError(
        ...     "Failed to parse JSON configuration file",
        ...     file_path="/config/plot_profiles/custom.json",
        ...     operation="load"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        file_path: Optional[str] = None,
        operation: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.file_path = file_path
        self.operation = operation


class StyleApplicationError(WpPlottingError):
    """样式应用到matplotlib对象时发生错误。

    常见场景：
    1. matplotlib版本不兼容
    2. 字体不存在
    3. 配置参数无效

    Attributes:
        profile_name (str): 应用的配置模板名称
        matplotlib_object (str): 目标matplotlib对象类型
        failed_property (str): 失败的属性设置

    Examples:
        >>> raise StyleApplicationError(
        ...     "Font 'CustomFont' not available",
        ...     profile_name="custom.profile",
        ...     matplotlib_object="Axes",
        ...     failed_property="font.family"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        profile_name: Optional[str] = None,
        matplotlib_object: Optional[str] = None,
        failed_property: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.profile_name = profile_name
        self.matplotlib_object = matplotlib_object
        self.failed_property = failed_property
