{"cells": [{"cell_type": "markdown", "id": "0556fffe", "metadata": {}, "source": ["# 三角形图版分析"]}, {"cell_type": "code", "execution_count": 1, "id": "c88aa011", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-08-03T16:08:42.293632Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 128.57, 'cpu_percent': 0.0}\n", "2025-08-03T16:08:43.389032Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 352.48, 'cpu_percent': 0.0} operation=registry_init\n", "2025-08-03T16:08:43.397090Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 352.5, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-08-03T16:08:43.397090Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 352.5, 'cpu_percent': 0.0} profile_name=base\n", "2025-08-03T16:08:43.490129Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 357.6, 'cpu_percent': 0.0} operation=register_base_profile profile_name=petroplot.nmr_ternary.base\n", "2025-08-03T16:08:43.497565Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 357.61, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=petroplot.nmr_ternary.default\n", "===库导入完成!\n"]}], "source": ["from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "from logwp.extras.petroplot.common import *\n", "from logwp.extras.petroplot.nmr_ternary import *\n", "\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"===库导入完成!\")"]}, {"cell_type": "markdown", "id": "07998a31", "metadata": {}, "source": ["## 加载数据"]}, {"cell_type": "code", "execution_count": 2, "id": "e733775a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-03T16:08:43.547213Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 358.86, 'cpu_percent': 0.0} file_path=nmr_ternary.wp.xlsx\n", "2025-08-03T16:08:43.562927Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 359.02, 'cpu_percent': 0.0} file_path=nmr_ternary.wp.xlsx file_size_mb=0.63 sheet_count=1\n", "2025-08-03T16:08:43.570939Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 359.04, 'cpu_percent': 0.0} project_name=nmr_ternary\n", "2025-08-03T16:08:43.576448Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 359.04, 'cpu_percent': 0.0} default_depth_unit=m project_name=nmr_ternary\n", "2025-08-03T16:08:43.585168Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 359.45, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-03T16:08:43.607594Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 359.48, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=8 well_curves=1\n", "2025-08-03T16:08:44.515918Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.05, 'cpu_percent': 0.0} shape=(14282, 8) sheet_name=nmr_ternary\n", "2025-08-03T16:08:44.546132Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.11, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_ternary')\n", "2025-08-03T16:08:44.556607Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.11, 'cpu_percent': 0.0} curve_count=8 dataset_name=nmr_ternary dataset_type=Continuous df_shape=(14282, 8) processing_time=0.973\n", "2025-08-03T16:08:44.614432Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.11, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-08-03T16:08:44.619270Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.11, 'cpu_percent': 0.0} dataset_count=1\n", "2025-08-03T16:08:44.634899Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.11, 'cpu_percent': 0.0} dataset_count=1 file_path=nmr_ternary.wp.xlsx processing_time=1.088 project_name=WpIdentifier('nmr_ternary')\n", "✅ 成功读取WL数据: ./nmr_ternary.wp.xlsx\n", "2025-08-03T16:08:44.634899Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.11, 'cpu_percent': 0.0} extracted_curve_count=6 operation=extract_curve_dataframe_bundle\n", "2025-08-03T16:08:44.680453Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.91, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=8 input_curves=['WELL_NO', 'VMESO', 'PZI', 'VMICRO', 'K_LABEL', 'VMACRO', 'MD', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=8 output_curves=['WELL_NO', 'VMESO', 'PZI', 'VMICRO', 'K_LABEL', 'VMACRO', 'MD', 'K_LABEL_TYPE']\n", "===WL Bundle:   WELL_NO         MD    VMACRO    VMICRO     VMESO  K_LABEL K_LABEL_TYPE   PZI\n", "0     C-1  6335.1156  0.024924  0.008790  0.093361      NaN         None  <NA>\n", "1     C-1  6335.2680  0.028043  0.012756  0.097468    0.205          MDT     1\n", "2     C-1  6335.4204  0.028520  0.015218  0.084699      NaN         None  <NA>\n", "3     C-1  6335.5728  0.032635  0.015021  0.066208      NaN         None  <NA>\n", "4     C-1  6335.7252  0.035453  0.012595  0.057045      NaN         None  <NA>\n", "2025-08-03T16:08:44.735978Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.96, 'cpu_percent': 0.0} file_path=lwd_nmr_ternary.wp.xlsx\n", "2025-08-03T16:08:44.735978Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.96, 'cpu_percent': 0.0} file_path=lwd_nmr_ternary.wp.xlsx file_size_mb=0.12 sheet_count=1\n", "2025-08-03T16:08:44.759435Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.96, 'cpu_percent': 0.0} project_name=lwd_nmr_ternary\n", "2025-08-03T16:08:44.769456Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.96, 'cpu_percent': 0.0} default_depth_unit=m project_name=lwd_nmr_ternary\n", "2025-08-03T16:08:44.774956Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.97, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-03T16:08:44.796292Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.97, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=8 well_curves=1\n", "2025-08-03T16:08:44.952580Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} shape=(2442, 8) sheet_name=lwd_nmr_ternary\n", "2025-08-03T16:08:44.968428Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} dataset_name=WpIdentifier('lwd_nmr_ternary')\n", "2025-08-03T16:08:44.968428Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} curve_count=8 dataset_name=lwd_nmr_ternary dataset_type=Continuous df_shape=(2442, 8) processing_time=0.193\n", "2025-08-03T16:08:45.034595Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-08-03T16:08:45.038729Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} dataset_count=1\n", "2025-08-03T16:08:45.051840Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} dataset_count=1 file_path=lwd_nmr_ternary.wp.xlsx processing_time=0.316 project_name=WpIdentifier('lwd_nmr_ternary')\n", "✅ 成功读取LWD数据: ./lwd_nmr_ternary.wp.xlsx\n", "2025-08-03T16:08:45.051840Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} extracted_curve_count=6 operation=extract_curve_dataframe_bundle\n", "2025-08-03T16:08:45.069935Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=8 input_curves=['VMACRO_LWD', 'VMICRO_LWD', 'WELL_NO', 'PZI', 'VMESO_LWD', 'K_LABEL', 'MD', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=8 output_curves=['VMACRO_LWD', 'VMICRO_LWD', 'WELL_NO', 'PZI', 'VMESO_LWD', 'K_LABEL', 'MD', 'K_LABEL_TYPE']\n", "===LWD Bundle:   WELL_NO         MD  VMICRO_LWD  VMACRO_LWD  VMESO_LWD  K_LABEL K_LABEL_TYPE  \\\n", "0     C-1  6335.1156    0.006392    0.022646   0.075426      NaN         None   \n", "1     C-1  6335.2680    0.007271    0.020203   0.086992    0.205          MDT   \n", "2     C-1  6335.4204    0.009093    0.020320   0.091553      NaN         None   \n", "3     C-1  6335.5728    0.007958    0.016707   0.093995      NaN         None   \n", "4     C-1  6335.7252    0.005236    0.013193   0.091279      NaN         None   \n", "\n", "    PZI  \n", "0  <NA>  \n", "1     1  \n", "2  <NA>  \n", "3  <NA>  \n", "4  <NA>  \n"]}], "source": ["# --- 加载WL数据 ---\n", "from logwp.models.constants import WpDepthRole\n", "\n", "\n", "wl_data_file_path = \"./nmr_ternary.wp.xlsx\"\n", "reader = WpExcelReader()\n", "wl_project = reader.read(wl_data_file_path)\n", "print(f\"✅ 成功读取WL数据: {wl_data_file_path}\")\n", "\n", "wl_bundle = wl_project.get_dataset(\"nmr_ternary\").extract_curve_dataframe_bundle(\n", "    include_system_columns=True\n", ")\n", "wl_well_name = wl_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "wl_depth_name = wl_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "print (f\"===WL Bundle: {wl_bundle.data.head()}\")\n", "\n", "# --- 加载LWD数据 ---\n", "lwd_data_file_path = \"./lwd_nmr_ternary.wp.xlsx\"\n", "lwd_project = reader.read(lwd_data_file_path)\n", "print(f\"✅ 成功读取LWD数据: {lwd_data_file_path}\")\n", "\n", "lwd_bundle = lwd_project.get_dataset(\"lwd_nmr_ternary\").extract_curve_dataframe_bundle(\n", "    include_system_columns=True\n", ")\n", "lwd_well_name = lwd_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "lwd_depth_name = lwd_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "print (f\"===LWD Bundle: {lwd_bundle.data.head()}\")"]}, {"cell_type": "markdown", "id": "7548d0c3", "metadata": {}, "source": ["## 初始化RunContext"]}, {"cell_type": "code", "execution_count": 3, "id": "7a6c75c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-03T16:08:45.097141Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 369.98, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\plot_20250804_000845 run_id=20250803-160845-76b0053c\n", "实验运行已初始化，所有产物将保存至: X:\\03.Project\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\02_preanalysis_plot\\case01_nmr_ternary\\output01\\plot_20250804_000845\n"]}], "source": ["# 初始化RunContext\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"plot\")\n", "run_context = RunContext(output_dir / run_dir_name,overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")"]}, {"cell_type": "markdown", "id": "df8f2779", "metadata": {}, "source": ["## WL绘图"]}, {"cell_type": "code", "execution_count": 4, "id": "ab9b8ec5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-03T16:08:45.125706Z [warning  ] 在对列 'K_LABEL' 进行对数转换时，发现 200 个非正数值。这些值将被替换为下限值 1e-06 以进行计算。 [logwp.extras.petroplot.nmr_ternary.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 370.07, 'cpu_percent': 0.0}\n", "2025-08-03T16:08:45.249139Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=WL.petroplot_nmr_ternary.configs.logic_config artifact_path=WL_nmr_ternary\\logic_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 379.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n", "2025-08-03T16:08:45.324789Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=WL.petroplot_nmr_ternary.data_snapshots.main_plot_data artifact_path=WL_nmr_ternary\\plot_snapshot.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 382.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n", "2025-08-03T16:08:46.086998Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=WL.petroplot_nmr_ternary.plots.ternary_plot_main_html artifact_path=WL_nmr_ternary\\ternary_plot_main.html context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 384.52, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n", "Chromium init'ed with kwargs {}\n", "Found chromium path: C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\n", "Temp directory created: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmppf5aqhgd.\n", "Opening browser.              \n", "Temp directory created: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp0jw7mw8t.\n", "Temporary directory at: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp0jw7mw8t\n", "Conforming 1 to file:///C:/Users/<USER>/AppData/Local/Temp/tmppf5aqhgd/index.html\n", "Waiting on all navigates      \n", "All navigates done, putting them all in queue.\n", "Getting tab from queue (has 1)\n", "Got 45C0                      \n", "Processing fig.png            \n", "Sending big command for fig.png.\n", "Sent big command for fig.png. \n", "Reloading tab 45C0 before return.\n", "Putting tab 45C0 back (queue size: 0).\n", "Waiting for all cleanups to finish.\n", "Exiting Kaleido               \n", "TemporaryDirectory.cleanup() worked.\n", "shutil.rmtree worked.         \n", "Cancelling tasks.             \n", "Exiting Kaleido/Choreo        \n", "Closing browser.              \n", "TemporaryDirectory.cleanup() worked.\n", "shutil.rmtree worked.         \n", "Cancelling tasks.             \n", "Exiting Kaleido/Choreo        \n", "Closing browser.              \n", "TemporaryDirectory.cleanup() worked.\n", "shutil.rmtree worked.         \n", "2025-08-03T16:08:48.516336Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=WL.petroplot_nmr_ternary.plots.ternary_plot_main_png artifact_path=WL_nmr_ternary\\ternary_plot_main.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 385.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n"]}], "source": ["plot_config, plot_profile = create_publication_ready_perm_config(\n", "    enable_background=True,\n", "    colorscale='Plasma',\n", "    font_size_pt=20,\n", "    tick_font_size_pt=20,\n", "    marker_size=18,\n", "    legend_position='right')\n", "\n", "wl_selectors = NmrTernaryDataSelectors(\n", "    macro_curve=\"VMACRO\",\n", "    micro_curve=\"VMICRO\",\n", "    meso_curve=\"VMESO\",\n", "    color_curve=\"K_LABEL\",\n", "    hover_extra_curves=[wl_well_name, wl_depth_name]\n", ")\n", "\n", "wl_results = run_nmr_ternary_plot_step(\n", "    config=plot_config,\n", "    selectors=wl_selectors,\n", "    ctx=run_context,\n", "    bundle=wl_bundle,\n", "    plot_profile=plot_profile,\n", "    prefix=\"WL\"\n", ")"]}, {"cell_type": "markdown", "id": "b2dbb1ee", "metadata": {}, "source": ["## LWD绘图"]}, {"cell_type": "code", "execution_count": 5, "id": "8e3d8b01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-03T16:08:48.542822Z [warning  ] 在对列 'K_LABEL' 进行对数转换时，发现 67 个非正数值。这些值将被替换为下限值 1e-06 以进行计算。 [logwp.extras.petroplot.nmr_ternary.facade] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 385.21, 'cpu_percent': 0.0}\n", "TemporaryDirectory.cleanup() worked.\n", "shutil.rmtree worked.         \n", "2025-08-03T16:08:48.597917Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=LWD.petroplot_nmr_ternary.configs.logic_config artifact_path=LWD_nmr_ternary\\logic_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 385.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n", "2025-08-03T16:08:48.630986Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=LWD.petroplot_nmr_ternary.data_snapshots.main_plot_data artifact_path=LWD_nmr_ternary\\plot_snapshot.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 385.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n", "2025-08-03T16:08:49.416264Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=LWD.petroplot_nmr_ternary.plots.ternary_plot_main_html artifact_path=LWD_nmr_ternary\\ternary_plot_main.html context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 385.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n", "Chromium init'ed with kwargs {}\n", "Found chromium path: C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\n", "Temp directory created: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_wqta13f.\n", "Opening browser.              \n", "Temp directory created: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpinujgxjr.\n", "Temporary directory at: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpinujgxjr\n", "Conforming 1 to file:///C:/Users/<USER>/AppData/Local/Temp/tmp_wqta13f/index.html\n", "Waiting on all navigates      \n", "All navigates done, putting them all in queue.\n", "Getting tab from queue (has 1)\n", "Got F9B2                      \n", "Processing fig.png            \n", "Sending big command for fig.png.\n", "Sent big command for fig.png. \n", "Reloading tab F9B2 before return.\n", "Putting tab F9B2 back (queue size: 0).\n", "Waiting for all cleanups to finish.\n", "Exiting Kaleido               \n", "TemporaryDirectory.cleanup() worked.\n", "shutil.rmtree worked.         \n", "Cancelling tasks.             \n", "Exiting Kaleido/Choreo        \n", "Closing browser.              \n", "TemporaryDirectory.cleanup() worked.\n", "shutil.rmtree worked.         \n", "Cancelling tasks.             \n", "Exiting Kaleido/Choreo        \n", "Closing browser.              \n", "TemporaryDirectory.cleanup() worked.\n", "shutil.rmtree worked.         \n", "2025-08-03T16:08:51.643080Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=LWD.petroplot_nmr_ternary.plots.ternary_plot_main_png artifact_path=LWD_nmr_ternary\\ternary_plot_main.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 385.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250803-160845-76b0053c\n"]}], "source": ["lwd_selectors = NmrTernaryDataSelectors(\n", "    macro_curve=\"VMACRO_LWD\",\n", "    micro_curve=\"VMICRO_LWD\",\n", "    meso_curve=\"VMESO_LWD\",\n", "    color_curve=\"K_LABEL\",\n", "    hover_extra_curves=[wl_well_name, wl_depth_name]\n", ")\n", "\n", "lwd_results = run_nmr_ternary_plot_step(\n", "    config=plot_config,\n", "    selectors=lwd_selectors,\n", "    ctx=run_context,\n", "    bundle=lwd_bundle,\n", "    plot_profile=plot_profile,\n", "    prefix=\"LWD\"\n", ")"]}, {"cell_type": "markdown", "id": "41bba618", "metadata": {}, "source": ["## 结束"]}, {"cell_type": "code", "execution_count": 6, "id": "fd4e8cc9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-03T16:08:51.665284Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 385.21, 'cpu_percent': 0.0} duration_seconds=6.563 manifest_path=output01\\plot_20250804_000845\\manifest.json operation=finalize run_id=20250803-160845-76b0053c status=RUNNING\n"]}], "source": ["run_context.finalize()"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}