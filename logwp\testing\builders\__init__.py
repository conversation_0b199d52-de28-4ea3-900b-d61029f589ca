"""logwp.testing.builders - 测试数据构建器

提供流畅的API来构建测试用的logwp对象。

Examples
--------
>>> from logwp.testing.builders import DatasetBuilder, ProjectBuilder
>>>
>>> # 构建测试数据集
>>> dataset = DatasetBuilder.continuous("test_logs") \
...     .with_wells(["W-1", "W-2"]) \
...     .with_depth_range(2500, 2510, 0.5) \
...     .with_curves({"GR": 50, "PHIT": 0.15}) \
...     .build()
>>>
>>> # 构建测试项目
>>> project = ProjectBuilder("Santos_Test") \
...     .with_dataset("logs", dataset) \
...     .with_head_attribute("version", "1.0") \
...     .build()
"""

from .dataset_builder import DatasetBuilder
from .project_builder import ProjectBuilder
from .curve_builder import CurveBuilder

__all__ = [
    "DatasetBuilder",
    "ProjectBuilder", 
    "CurveBuilder",
]
