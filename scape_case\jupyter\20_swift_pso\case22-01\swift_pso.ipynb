{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a9e0a2e7", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "1e86095f", "metadata": {}, "source": ["# SWIFT-PSO 案例\n", "- 固定随机种子\n", "- 新增`收敛轨迹与聚类分析图`以及各Cluster参数统计\n", "- 12个优化参数,t2lm_exp、vmacro_b、vmacro_min为固定\n", "- t2_cutoff_long参数搜索边界修改\n", "- random_seed = 2000\n", "- t-SNE可视化最终收敛点聚类分析 (Cluster Analysis)聚类方法：kmeans"]}, {"cell_type": "markdown", "id": "4266395e", "metadata": {}, "source": ["## 1. 初始化与导入"]}, {"cell_type": "code", "execution_count": 2, "id": "3e9b170c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-29T05:32:55.133970Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 128.12, 'cpu_percent': 0.0}\n", "2025-07-29T05:32:57.622838Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.92, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-29T05:32:57.638584Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.93, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-29T05:32:57.669938Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.94, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-29T05:32:57.701688Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 356.39, 'cpu_percent': 0.0} operation=register_base_profile profile_name=swift_pso.base\n", "2025-07-29T05:32:57.733263Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 356.42, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_convergence\n", "2025-07-29T05:32:57.744438Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 356.44, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T05:32:59.276445Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.24, 'cpu_percent': 0.0} operation=register_base_profile profile_name=validation.plt.base\n", "2025-07-29T05:32:59.308078Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T05:32:59.326283Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.29, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.capture_curve\n", "2025-07-29T05:32:59.339489Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.31, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.lorenz_curve\n", "2025-07-29T05:32:59.370845Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.33, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.perm_corr.permeability_crossplot\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "# 导入 swift_pso 组件\n", "from scape.core.swift_pso import (\n", "    run_swift_pso_training_step,\n", "    run_swift_pso_prediction_step,\n", "    run_tsne_visualization_step,\n", "    SwiftPsoTrainingConfig,\n", "    SwiftPsoPredictionConfig,\n", "    TsneVisualConfig,\n", "    SwiftPsoTrainingArtifacts,\n", "    SwiftPsoPredictionArtifacts,\n", "    TsneVisualArtifacts,\n", "    TsnePlotProfiles\n", ")\n", "\n", "# 导入 validation 组件\n", "from scape.core.validation import (\n", "    run_plt_analysis_step,\n", "    run_perm_correlation_step,\n", "    PltAnalysisConfig,\n", "    PermCorrelationConfig,\n", "    PltAnalysisArtifacts,\n", "    PermCorrelationArtifacts,\n", "    PltPlotTypes,\n", "    PltAnalysisPlotProfiles,\n", "    PermCorrelationPlotProfiles\n", ")\n", "\n", "import scape.core.swift_pso.plot_profiles"]}, {"cell_type": "markdown", "id": "f519543e", "metadata": {}, "source": ["## 2. 实验设置与数据加载\n", "\n", "使用 `RunContext` 初始化一个实验，所有后续操作都将在此上下文中进行，确保所有产物和日志都保存在一个独立的运行目录中。"]}, {"cell_type": "code", "execution_count": 3, "id": "e446002f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T05:32:59.526865Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.15, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\swift_pso_run_20250729_133259 run_id=20250729-053259-a669311d\n", "实验运行已初始化，所有产物将保存至: F:\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\20_swift_pso\\case22-01\\output01\\swift_pso_run_20250729_133259\n", "2025-07-29T05:32:59.547907Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.15, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T05:32:59.578605Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.41, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx file_size_mb=0.13 sheet_count=1\n", "2025-07-29T05:32:59.609992Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.44, 'cpu_percent': 0.0} project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T05:32:59.627203Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.46, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T05:32:59.657023Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.94, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T05:32:59.711103Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 405.12, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=11 well_curves=1\n", "2025-07-29T05:33:00.071227Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.88, 'cpu_percent': 0.0} shape=(297, 74) sheet_name=swift_pso_train_cleaned\n", "2025-07-29T05:33:00.102767Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned')\n", "2025-07-29T05:33:00.129268Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} curve_count=11 dataset_name=swift_pso_train_cleaned dataset_type=Point df_shape=(297, 74) processing_time=0.488\n", "2025-07-29T05:33:00.174303Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T05:33:00.205601Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T05:33:00.221525Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=0.674 project_name=WpIdentifier('scape_swift_pso_train_cleaned')\n", "✅ 成功读取训练数据: ./scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T05:33:00.267602Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.16, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=11 input_curves=['DT2_P50', 'PHIT_NMR', 'K_LABEL', 'MD', 'PHI_T2_DIST', 'T2LM', 'T2_P50', 'WELL_NO', 'DPHIT_NMR', 'PZI', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=11 output_curves=['DT2_P50', 'PHIT_NMR', 'K_LABEL', 'MD', 'PHI_T2_DIST', 'T2LM', 'T2_P50', 'WELL_NO', 'DPHIT_NMR', 'PZI', 'K_LABEL_TYPE']\n", "2025-07-29T05:33:00.308160Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.16, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T05:33:00.328890Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.16, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T05:33:00.336679Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.16, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T05:33:00.352694Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.16, 'cpu_percent': 0.0}\n", "===train_bundle:   WELL_NO       MD  PHIT_NMR      T2LM   DT2_P50    T2_P50  DPHIT_NMR  \\\n", "0     C-1  6319.40  0.105551   785.428  0.316622   997.666   0.079668   \n", "1     C-1  6335.20  0.138267   746.054  0.111100   871.799   0.023800   \n", "2     C-1  6334.52  0.137828  3960.371  1.190176  6131.592   0.060766   \n", "3     C-1  6337.00  0.100931   527.925  0.102427   663.328   0.004202   \n", "4     C-1  6349.70  0.047791   111.798 -0.399350   203.148  -0.029715   \n", "\n", "     T2_VALUE_1    T2_VALUE_2    T2_VALUE_3  ...  T2_VALUE_58  T2_VALUE_59  \\\n", "0  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003882     0.003707   \n", "1  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.004239     0.003638   \n", "2  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.010259     0.012969   \n", "3  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003190     0.003105   \n", "4  1.506000e-05  3.392000e-05  6.404000e-05  ...     0.000000     0.000000   \n", "\n", "   T2_VALUE_60  T2_VALUE_61  T2_VALUE_62  T2_VALUE_63  T2_VALUE_64  K_LABEL  \\\n", "0     0.003506     0.003268     0.002990     0.002676     0.002339    0.888   \n", "1     0.003084     0.002577     0.002114     0.001696     0.001326    0.205   \n", "2     0.015308     0.017210     0.018582     0.019324     0.019364  153.000   \n", "3     0.003001     0.002867     0.002698     0.002490     0.002247    6.130   \n", "4     0.000000     0.000000     0.000000     0.000000     0.000000    0.263   \n", "\n", "   K_LABEL_TYPE  PZI  \n", "0          CORE    1  \n", "1           MDT    1  \n", "2          CORE    1  \n", "3          CORE    1  \n", "4          CORE    1  \n", "\n", "[5 rows x 74 columns]\n", "2025-07-29T05:33:00.387852Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.21, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE']\n", "===train_label_all_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T05:33:00.435418Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.24, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE']\n", "===train_label_pz_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T05:33:00.514249Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.42, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE']\n", "===train_label_core_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "2025-07-29T05:33:00.579181Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.42, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL', 'WELL_NO', 'MD', 'PZI', 'K_LABEL_TYPE']\n", "===train_label_core_pz_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "  - T2轴信息: T2AxisLog10(t2_start=0.1, t2_end=10000, n=64, unit='ms')\n"]}], "source": ["# --- 实验设置 ---\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"swift_pso_run\")\n", "run_context = RunContext(output_dir / run_dir_name,overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")\n", "\n", "# --- 加载训练数据 ---\n", "data_file_path = \"./scape_swift_pso_train_cleaned.wp.xlsx\"\n", "reader = WpExcelReader()\n", "train_project = reader.read(data_file_path)\n", "print(f\"✅ 成功读取训练数据: {data_file_path}\")\n", "\n", "train_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',\n", "        'PHI_T2_DIST', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_bundle: {train_bundle.data.head()}\")\n", "\n", "# 训练集岩心检验数据准备\n", "train_label_all_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_all_bundle: {train_label_all_bundle.data.head()}\")\n", "\n", "train_label_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_pz_bundle: {train_label_pz_bundle.data.head()}\")\n", "\n", "train_label_core_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE'\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_bundle: {train_label_core_bundle.data.head()}\")\n", "\n", "train_label_core_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE' and PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_pz_bundle: {train_label_core_pz_bundle.data.head()}\")\n", "\n", "# --- 准备数据依赖参数 ---\n", "t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "t2_time_array = t2_axis_info.calculate_values()\n", "print(f\"  - T2轴信息: {t2_axis_info}\")"]}, {"cell_type": "markdown", "id": "e5b87190", "metadata": {}, "source": ["## 3. 步骤一：SWIFT-PSO 训练\n", "\n", "调用 `run_swift_pso_training_step` 执行训练。我们首先使用 `SwiftPsoTrainingConfig.create_default()` 创建一个默认配置对象，然后将数据依赖的参数（如参考值、T2轴）注入其中。"]}, {"cell_type": "code", "execution_count": 4, "id": "76495147", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\n", "2025-07-29T05:33:00.725064Z [info     ] 开始SWIFT-PSO训练步骤                [scape.core.swift_pso.training_facade] backend=gpu bootstrap_iterations=20 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.53, 'cpu_percent': 0.0} operation=swift_pso_training_step run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:00.761611Z [info     ] 成功替换Bundle中的曲线                 [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.54, 'cpu_percent': 0.0} curve_name=K_LABEL_TYPE new_data_type=INT new_shape=(297,) operation=replace_curve\n", "2025-07-29T05:33:00.788859Z [info     ] 必需曲线验证通过                       [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') checked_curves_count=9 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.54, 'cpu_percent': 0.0} operation=validate_required_curves\n", "2025-07-29T05:33:00.815550Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=gpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.54, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T05:33:00.844353Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.configs.training_config artifact_path=swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.59, 'cpu_percent': 0.0} description=本次训练步骤的完整Pydantic配置快照，确保可复现性。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:00.871136Z [info     ] 训练配置已保存为产物                     [scape.core.swift_pso.training_facade] config_path=output01\\swift_pso_run_20250729_133259\\swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.59, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:00.882358Z [info     ] 调用内部PSO优化器                     [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.59, 'cpu_percent': 0.0} operation=swift_pso_training_step\n", "2025-07-29T05:33:00.905751Z [info     ] 开始按井拆分DataFrame Bundle         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.59, 'cpu_percent': 0.0} operation=to_all_wells_data\n", "2025-07-29T05:33:00.929190Z [info     ] DataFrame Bundle按井拆分完成         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.71, 'cpu_percent': 0.0} operation=to_all_wells_data wells_found=['C-1', 'C-2', 'T-1']\n", "--- Bootstrap-<PERSON>de 1/20 ---\n", "2025-07-29T05:33:00.960698Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 408.96, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 101 代触发。\n", "2025-07-29T05:33:02.472984Z [info     ] PSO 优化完成。最终迭代次数: 101, 最优损失: 6.038098 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.79, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:02.499670Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.79, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:02.539142Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.82, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:02.566902Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.89, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(8.999999999999999e-08, 0.08350502) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T05:33:02.599291Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.89, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 121 代触发。\n", "2025-07-29T05:33:04.327065Z [info     ] PSO 优化完成。最终迭代次数: 121, 最优损失: 4.771758 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.92, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:04.356500Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.92, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:04.395802Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.92, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:04.429776Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.92, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(6.999999999999999e-08, 0.047916730000000005) v_micro_range=(3.9000000000000024e-07, 0.05727889000000002)\n", "2025-07-29T05:33:04.456821Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.92, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 81 代触发。\n", "2025-07-29T05:33:05.610165Z [info     ] PSO 优化完成。最终迭代次数: 81, 最优损失: 5.939414 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.99, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:05.636491Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.99, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:05.680625Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:05.714810Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.005826999999999999, 0.09788412) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 2/20 ---\n", "2025-07-29T05:33:05.752115Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 109 代触发。\n", "2025-07-29T05:33:07.239982Z [info     ] PSO 优化完成。最终迭代次数: 109, 最优损失: 7.086279 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:07.272162Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:07.315285Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:07.346007Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(1.2e-07, 0.10925294999999999) v_micro_range=(3.500000000000002e-07, 0.04422076000000001)\n", "2025-07-29T05:33:07.371475Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 176 代触发。\n", "2025-07-29T05:33:09.766554Z [info     ] PSO 优化完成。最终迭代次数: 176, 最优损失: 3.893256 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:09.791979Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.0, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:09.839639Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.01, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:09.864079Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.01, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.05600393000000002)\n", "2025-07-29T05:33:09.912132Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.01, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 118 代触发。\n", "2025-07-29T05:33:11.657153Z [info     ] PSO 优化完成。最终迭代次数: 118, 最优损失: 5.324765 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:11.691050Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:11.729624Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:11.766911Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.005208419999999999, 0.05667839) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 3/20 ---\n", "2025-07-29T05:33:11.798978Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 89 代触发。\n", "2025-07-29T05:33:13.097744Z [info     ] PSO 优化完成。最终迭代次数: 89, 最优损失: 6.308926 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:13.124312Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:13.159415Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:13.180400Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.03, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08579144000000001) v_meso_range=(1.5000000000000002e-07, 0.13464968) v_micro_range=(3.300000000000002e-07, 0.042200610000000006)\n", "2025-07-29T05:33:13.221992Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.03, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 192 代触发。\n", "2025-07-29T05:33:15.858589Z [info     ] PSO 优化完成。最终迭代次数: 192, 最优损失: 4.396595 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.03, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:15.886469Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.03, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:15.925352Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:15.956126Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(8.999999999999999e-08, 0.051865060000000004) v_micro_range=(3.700000000000002e-07, 0.05703878000000002)\n", "2025-07-29T05:33:15.992178Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 78 代触发。\n", "2025-07-29T05:33:17.101525Z [info     ] PSO 优化完成。最终迭代次数: 78, 最优损失: 5.975922 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:17.120149Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:17.152405Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:17.186519Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.006245559999999999, 0.056678400000000004) v_micro_range=(0.0019181899999999989, 0.045421560000000014)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 4/20 ---\n", "2025-07-29T05:33:17.222400Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 106 代触发。\n", "2025-07-29T05:33:18.608366Z [info     ] PSO 优化完成。最终迭代次数: 106, 最优损失: 5.707199 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:18.630998Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:18.663397Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:18.700739Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(1.3e-07, 0.09118348) v_micro_range=(3.300000000000002e-07, 0.042200610000000006)\n", "2025-07-29T05:33:18.748605Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 134 代触发。\n", "2025-07-29T05:33:20.637183Z [info     ] PSO 优化完成。最终迭代次数: 134, 最优损失: 4.287835 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:20.661736Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:20.698483Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:20.721687Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(3.0000000000000004e-08, 0.030009409999999997) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T05:33:20.759626Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 104 代触发。\n", "2025-07-29T05:33:22.282002Z [info     ] PSO 优化完成。最终迭代次数: 104, 最优损失: 6.146123 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.11, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:22.300993Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:22.348966Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:22.374982Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.11628647) v_meso_range=(0.006965299999999999, 0.06800901000000001) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 5/20 ---\n", "2025-07-29T05:33:22.412516Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 131 代触发。\n", "2025-07-29T05:33:24.208508Z [info     ] PSO 优化完成。最终迭代次数: 131, 最优损失: 4.914926 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:24.236775Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:24.278555Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:24.305683Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(2e-08, 0.04073197) v_micro_range=(0.006484919999999999, 0.09614386999999998)\n", "2025-07-29T05:33:24.343498Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 133 代触发。\n", "2025-07-29T05:33:26.209234Z [info     ] PSO 优化完成。最终迭代次数: 133, 最优损失: 4.340692 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:26.236148Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:26.278302Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:26.303848Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(6.999999999999999e-08, 0.047916730000000005) v_micro_range=(3.9000000000000024e-07, 0.05727889000000002)\n", "2025-07-29T05:33:26.336355Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 75 代触发。\n", "2025-07-29T05:33:27.415461Z [info     ] PSO 优化完成。最终迭代次数: 75, 最优损失: 6.037435 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:27.437157Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:27.477490Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:27.508224Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.005208419999999999, 0.05667839) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 6/20 ---\n", "2025-07-29T05:33:27.543538Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 128 代触发。\n", "2025-07-29T05:33:29.243100Z [info     ] PSO 优化完成。最终迭代次数: 128, 最优损失: 7.919949 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:29.263655Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:29.320496Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:29.348986Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.14, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08579144000000001) v_meso_range=(5e-08, 0.10082788) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T05:33:29.383709Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.14, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 100 代触发。\n", "2025-07-29T05:33:30.770943Z [info     ] PSO 优化完成。最终迭代次数: 100, 最优损失: 4.787547 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:30.789371Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:30.824521Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:30.857103Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(6.999999999999999e-08, 0.047916730000000005) v_micro_range=(3.9000000000000024e-07, 0.05727889000000002)\n", "2025-07-29T05:33:30.895517Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 75 代触发。\n", "2025-07-29T05:33:31.997002Z [info     ] PSO 优化完成。最终迭代次数: 75, 最优损失: 6.614970 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:32.026061Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:32.063623Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:32.093669Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.005208409999999999, 0.0450249) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 7/20 ---\n", "2025-07-29T05:33:32.143732Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 76 代触发。\n", "2025-07-29T05:33:33.171766Z [info     ] PSO 优化完成。最终迭代次数: 76, 最优损失: 6.152456 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:33.194241Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:33.230434Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:33.251615Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08579144000000001) v_meso_range=(8.999999999999999e-08, 0.12591607) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T05:33:33.298693Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 86 代触发。\n", "2025-07-29T05:33:34.519518Z [info     ] PSO 优化完成。最终迭代次数: 86, 最优损失: 4.991175 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:34.545765Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:34.576442Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:34.616420Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.05583256999999999) v_meso_range=(1.3e-07, 0.07810117999999999) v_micro_range=(3.700000000000002e-07, 0.05703878000000002)\n", "2025-07-29T05:33:34.650465Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 117 代触发。\n", "2025-07-29T05:33:36.279593Z [info     ] PSO 优化完成。最终迭代次数: 117, 最优损失: 5.562583 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:36.316489Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:36.358495Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:36.387397Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.00041007, 0.03927055) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 8/20 ---\n", "2025-07-29T05:33:36.419957Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 82 代触发。\n", "2025-07-29T05:33:37.546747Z [info     ] PSO 优化完成。最终迭代次数: 82, 最优损失: 6.404488 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.17, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:37.566062Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.17, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:37.604179Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.17, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:37.630233Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.17, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(3.0000000000000004e-08, 0.05623814) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T05:33:37.658724Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.17, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 172 代触发。\n", "2025-07-29T05:33:39.960904Z [info     ] PSO 优化完成。最终迭代次数: 172, 最优损失: 4.724137 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.17, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:39.983992Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.17, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:40.022911Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:40.053220Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(1.2e-07, 0.05368192) v_micro_range=(3.400000000000002e-07, 0.05189064000000002)\n", "2025-07-29T05:33:40.089372Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 104 代触发。\n", "2025-07-29T05:33:41.580528Z [info     ] PSO 优化完成。最终迭代次数: 104, 最优损失: 5.260871 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:41.600775Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:41.641656Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:41.670806Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.005208409999999999, 0.0450249) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 9/20 ---\n", "2025-07-29T05:33:41.712677Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 156 代触发。\n", "2025-07-29T05:33:43.790756Z [info     ] PSO 优化完成。最终迭代次数: 156, 最优损失: 5.572683 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:43.819390Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:43.856224Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:43.886659Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(6e-08, 0.07921933) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T05:33:43.913052Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 114 代触发。\n", "2025-07-29T05:33:45.523044Z [info     ] PSO 优化完成。最终迭代次数: 114, 最优损失: 5.160275 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:45.558102Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:45.594973Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:45.622900Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(6e-08, 0.04632677) v_micro_range=(4.0000000000000025e-07, 0.05743926000000002)\n", "2025-07-29T05:33:45.659401Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 134 代触发。\n", "2025-07-29T05:33:47.537242Z [info     ] PSO 优化完成。最终迭代次数: 134, 最优损失: 5.859484 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:47.557169Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:47.592395Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:47.620230Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(1.7300000000000004e-05, 0.03194019) v_micro_range=(0.00422468, 0.05513255000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 10/20 ---\n", "2025-07-29T05:33:47.657228Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 87 代触发。\n", "2025-07-29T05:33:48.817635Z [info     ] PSO 优化完成。最终迭代次数: 87, 最优损失: 6.165631 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:48.843227Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:48.885300Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.3, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:48.913088Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(1.0999999999999998e-07, 0.10750565999999998) v_micro_range=(3.600000000000002e-07, 0.04908202000000001)\n", "2025-07-29T05:33:48.940573Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 155 代触发。\n", "2025-07-29T05:33:51.031804Z [info     ] PSO 优化完成。最终迭代次数: 155, 最优损失: 4.309749 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:51.058077Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:51.091288Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:51.122517Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(6e-08, 0.04632677) v_micro_range=(4.0000000000000025e-07, 0.05743926000000002)\n", "2025-07-29T05:33:51.162871Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 85 代触发。\n", "2025-07-29T05:33:52.403475Z [info     ] PSO 优化完成。最终迭代次数: 85, 最优损失: 6.124402 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.47, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:52.447849Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.47, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:52.491345Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:52.517525Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006965279999999999, 0.04502494) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11/20 ---\n", "2025-07-29T05:33:52.562363Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 93 代触发。\n", "2025-07-29T05:33:53.806268Z [info     ] PSO 优化完成。最终迭代次数: 93, 最优损失: 6.788811 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:53.824778Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:53.860532Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:53.891496Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(1.2e-07, 0.08848487) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T05:33:53.925617Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 159 代触发。\n", "2025-07-29T05:33:56.167072Z [info     ] PSO 优化完成。最终迭代次数: 159, 最优损失: 3.437559 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:56.180247Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:56.226385Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:56.263463Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(5e-08, 0.04419616) v_micro_range=(0.0008690600000000001, 0.06493764)\n", "2025-07-29T05:33:56.302694Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 131 代触发。\n", "2025-07-29T05:33:58.181204Z [info     ] PSO 优化完成。最终迭代次数: 131, 最优损失: 5.114238 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.49, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:58.211319Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.49, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:58.249972Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.49, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:58.277585Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.49, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006965279999999999, 0.04502494) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 12/20 ---\n", "2025-07-29T05:33:58.314600Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.49, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 94 代触发。\n", "2025-07-29T05:33:59.581659Z [info     ] PSO 优化完成。最终迭代次数: 94, 最优损失: 6.907637 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0}\n", "2025-07-29T05:33:59.607320Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:59.639621Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:33:59.673967Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(7.999999999999999e-08, 0.10393886) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T05:33:59.716113Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 200 代触发。\n", "2025-07-29T05:34:02.462733Z [info     ] PSO 优化完成。最终迭代次数: 200, 最优损失: 3.832925 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:02.490534Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:02.530587Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:02.556113Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(3.0000000000000004e-08, 0.030009409999999997) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T05:34:02.590045Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T05:34:03.734258Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 5.994266 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.58, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:03.770109Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.58, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:03.811492Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.58, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:03.847854Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.59, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006891039999999999, 0.04502492) v_micro_range=(0.0005744599999999999, 0.044776070000000015)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 13/20 ---\n", "2025-07-29T05:34:03.894326Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 75 代触发。\n", "2025-07-29T05:34:04.971900Z [info     ] PSO 优化完成。最终迭代次数: 75, 最优损失: 5.629627 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.59, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:04.983598Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:05.039814Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:05.063485Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(4e-08, 0.06761289000000001) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T05:34:05.131527Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 97 代触发。\n", "2025-07-29T05:34:06.517424Z [info     ] PSO 优化完成。最终迭代次数: 97, 最优损失: 4.018811 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:06.530759Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:06.556469Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:06.580961Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(4e-08, 0.03894591) v_micro_range=(0.005783170000000001, 0.07943931)\n", "2025-07-29T05:34:06.616598Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 160 代触发。\n", "2025-07-29T05:34:08.825138Z [info     ] PSO 优化完成。最终迭代次数: 160, 最优损失: 4.846450 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:08.861084Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:08.904984Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:08.918262Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.003995059999999999, 0.045024890000000005) v_micro_range=(0.00268106, 0.047672050000000014)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 14/20 ---\n", "2025-07-29T05:34:08.959536Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 96 代触发。\n", "2025-07-29T05:34:10.225914Z [info     ] PSO 优化完成。最终迭代次数: 96, 最优损失: 7.471955 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:10.238915Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:10.282128Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:10.316354Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.63, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(9.999999999999998e-08, 0.08489312999999998) v_micro_range=(3.600000000000002e-07, 0.04908202000000001)\n", "2025-07-29T05:34:10.362472Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.63, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 118 代触发。\n", "2025-07-29T05:34:12.013167Z [info     ] PSO 优化完成。最终迭代次数: 118, 最优损失: 3.972454 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:12.042135Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:12.078553Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:12.109524Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(3.0000000000000004e-08, 0.030009409999999997) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T05:34:12.156264Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T05:34:13.313106Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 4.991401 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:13.339521Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:13.382243Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:13.415420Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006891039999999999, 0.04502492) v_micro_range=(0.0005744599999999999, 0.044776070000000015)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 15/20 ---\n", "2025-07-29T05:34:13.449002Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 114 代触发。\n", "2025-07-29T05:34:14.960917Z [info     ] PSO 优化完成。最终迭代次数: 114, 最优损失: 6.288398 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:14.999681Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:15.042001Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:15.072217Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(7.999999999999999e-08, 0.08247362999999999) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T05:34:15.108045Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 161 代触发。\n", "2025-07-29T05:34:17.348446Z [info     ] PSO 优化完成。最终迭代次数: 161, 最优损失: 3.774875 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:17.369916Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:17.415446Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:17.453891Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(6e-08, 0.04632677) v_micro_range=(4.0000000000000025e-07, 0.05743926000000002)\n", "2025-07-29T05:34:17.498002Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 69 代触发。\n", "2025-07-29T05:34:18.535460Z [info     ] PSO 优化完成。最终迭代次数: 69, 最优损失: 6.751432 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:18.560972Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:18.598309Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:18.642189Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.11628647) v_meso_range=(0.00041009, 0.062254620000000004) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 16/20 ---\n", "2025-07-29T05:34:18.684528Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 104 代触发。\n", "2025-07-29T05:34:20.058458Z [info     ] PSO 优化完成。最终迭代次数: 104, 最优损失: 6.472717 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:20.076361Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:20.126295Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:20.152007Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(6.999999999999999e-08, 0.10183186) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T05:34:20.190319Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 105 代触发。\n", "2025-07-29T05:34:21.644867Z [info     ] PSO 优化完成。最终迭代次数: 105, 最优损失: 4.090662 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.67, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:21.657747Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.67, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:21.702823Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.67, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:21.729520Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.67, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(4e-08, 0.03894591) v_micro_range=(0.005783170000000001, 0.07943931)\n", "2025-07-29T05:34:21.759943Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.67, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 81 代触发。\n", "2025-07-29T05:34:22.924163Z [info     ] PSO 优化完成。最终迭代次数: 81, 最优损失: 5.033058 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.68, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:22.953610Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.68, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:22.991432Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.68, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:23.012880Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.68, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.11628647) v_meso_range=(0.006245569999999999, 0.06800898) v_micro_range=(0.0019181899999999989, 0.045421560000000014)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 17/20 ---\n", "2025-07-29T05:34:23.060128Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.68, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 173 代触发。\n", "2025-07-29T05:34:25.391464Z [info     ] PSO 优化完成。最终迭代次数: 173, 最优损失: 5.478646 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.68, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:25.404682Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.68, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:25.448270Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.69, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:25.484591Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.69, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(6e-08, 0.07921933) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T05:34:25.523604Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.69, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 188 代触发。\n", "2025-07-29T05:34:28.112540Z [info     ] PSO 优化完成。最终迭代次数: 188, 最优损失: 3.724771 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:28.135952Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:28.172940Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:28.208861Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(3.0000000000000004e-08, 0.030009409999999997) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T05:34:28.249550Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 150 代触发。\n", "2025-07-29T05:34:30.339895Z [info     ] PSO 优化完成。最终迭代次数: 150, 最优损失: 6.219765 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:30.361450Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:30.398905Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:30.435175Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.005208409999999999, 0.0450249) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 18/20 ---\n", "2025-07-29T05:34:30.503002Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 87 代触发。\n", "2025-07-29T05:34:31.701387Z [info     ] PSO 优化完成。最终迭代次数: 87, 最优损失: 8.080391 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:31.731326Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:31.784452Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:31.821512Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(6.999999999999999e-08, 0.10183186) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T05:34:31.855716Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 85 代触发。\n", "2025-07-29T05:34:33.076350Z [info     ] PSO 优化完成。最终迭代次数: 85, 最优损失: 3.841680 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:33.105267Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:33.144690Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:33.183913Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08404873) v_meso_range=(5e-08, 0.04652142) v_micro_range=(0.005783170000000001, 0.07943931)\n", "2025-07-29T05:34:33.225223Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 118 代触发。\n", "2025-07-29T05:34:34.914867Z [info     ] PSO 优化完成。最终迭代次数: 118, 最优损失: 5.802783 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:34.934467Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:34.971809Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:35.011064Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.005208419999999999, 0.05667839) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 19/20 ---\n", "2025-07-29T05:34:35.046085Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 93 代触发。\n", "2025-07-29T05:34:36.315502Z [info     ] PSO 优化完成。最终迭代次数: 93, 最优损失: 6.985953 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:36.353133Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:36.394480Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:36.422185Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08579144000000001) v_meso_range=(9.999999999999998e-08, 0.12706336999999998) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T05:34:36.457164Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.71, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 191 代触发。\n", "2025-07-29T05:34:39.063090Z [info     ] PSO 优化完成。最终迭代次数: 191, 最优损失: 3.858797 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.72, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:39.093398Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.72, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:39.129136Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:39.164336Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(5e-08, 0.04419616) v_micro_range=(0.0008690600000000001, 0.06493764)\n", "2025-07-29T05:34:39.207469Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 127 代触发。\n", "2025-07-29T05:34:40.957261Z [info     ] PSO 优化完成。最终迭代次数: 127, 最优损失: 5.285059 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:40.983958Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:41.032110Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:41.064790Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006965289999999999, 0.045915580000000004) v_micro_range=(0.0003839099999999999, 0.04470182000000001)\n", "--- Boots<PERSON><PERSON>-<PERSON><PERSON> 20/20 ---\n", "2025-07-29T05:34:41.109334Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 101 代触发。\n", "2025-07-29T05:34:42.477888Z [info     ] PSO 优化完成。最终迭代次数: 101, 最优损失: 6.240416 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:42.509471Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:42.556681Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:42.587629Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(2e-08, 0.04073197) v_micro_range=(0.006484919999999999, 0.09614386999999998)\n", "2025-07-29T05:34:42.636723Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T05:34:43.784834Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 4.039125 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:43.810136Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:43.854688Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:43.885621Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.05600393000000002)\n", "2025-07-29T05:34:43.936672Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 100 代触发。\n", "2025-07-29T05:34:45.390175Z [info     ] PSO 优化完成。最终迭代次数: 100, 最优损失: 5.798185 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:45.419024Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:45.467770Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:45.505527Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.73, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.11628647) v_meso_range=(0.003995079999999999, 0.06800896000000001) v_micro_range=(0.00268106, 0.047672050000000014)\n", "2025-07-29T05:34:45.546703Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 25 代触发。\n", "2025-07-29T05:34:45.827208Z [info     ] PSO 优化完成。最终迭代次数: 25, 最优损失: 4.827003 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.7, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:45.861659Z [info     ] Fine-Tuning阶段完成，最终损失: 4.827003 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.7, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:45.881093Z [info     ] SWIFT-PSO 优化完成。                [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.7, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:45.904628Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.models.final_parameters artifact_path=swift_pso_training\\final_parameters.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.7, 'cpu_percent': 0.0} description=最终优化后的模型参数及上下文，可直接用于预测步骤。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:45.945789Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.datasets.all_parameters_from_lowo artifact_path=swift_pso_training\\all_parameters_from_lowo.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.34, 'cpu_percent': 0.0} description=所有Bootstrap+LOWO迭代产生的优化参数集，用于t-SNE可视化分析。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:45.971219Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.convergence_history_finetune artifact_path=swift_pso_training\\convergence_history_finetune.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.34, 'cpu_percent': 0.0} description=Fine-Tuning阶段的损失函数收敛历史。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:45.985677Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.bootstrap_summary artifact_path=swift_pso_training\\summary_bootstrap_mu_rmse.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.34, 'cpu_percent': 0.0} description=每轮Bootstrap的平均验证误差（mu_rmse），用于确定最佳轮次b*。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:46.022837Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.params_warm_start artifact_path=swift_pso_training\\params_warm_start.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.34, 'cpu_percent': 0.0} description=用于启动Fine-Tuning阶段的热启动参数（theta_hat_star）。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:46.051289Z [info     ] SWIFT-PSO训练步骤完成                [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.34, 'cpu_percent': 0.0} final_loss=4.827003212826861 operation=swift_pso_training_step\n", "✅ [Step 1/5] 训练完成！ 结果: {'status': 'completed', 'final_loss': 4.827003212826861}\n", "   - 最终模型参数已保存为产物: swift_pso_training.models.final_parameters\n", "   - t-SNE源数据已保存为产物: swift_pso_training.datasets.all_parameters_from_lowo\n"]}], "source": ["# 1. 使用 Pydantic 模型创建配置\n", "training_config = SwiftPsoTrainingConfig.create_default()\n", "\n", "training_config.enable_fold_diagnostics = True\n", "training_config.optimization_params=[\n", "            'log10_KSDR_A', 'PHIT_EXP', 'RHO_NMR', 'log10_KMACRO_A',\n", "            'log10_T2cutoff_short', 'log10_T2cutoff_long',\n", "            'beta_1', 'beta_2', 'delta_MDT'\n", "        ]\n", "\n", "training_config.fixed_params['T2LM_EXP'] = 2.0\n", "training_config.fixed_params['KMACRO_B'] = 2.0\n", "training_config.fixed_params['Vmacro_min'] = 0.01\n", "\n", "# 2. 注入数据依赖参数\n", "t2_p50_ref = train_bundle.data['T2_P50'].median()\n", "phit_nmr_ref = train_bundle.data['PHIT_NMR'].median()\n", "\n", "training_config.random_seed = 2000\n", "training_config.pso_config_lowo['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_lowo['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_lowo['t2_time'] = t2_time_array\n", "training_config.pso_config_lowo['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_lowo['t2_range_min'] = 0.1\n", "training_config.pso_config_lowo['t2_range_max'] = 8000\n", "training_config.pso_config_lowo['parameters_boundaries']['log10_T2cutoff_long'] = (2.6, 2.65, 3.45, 3.5)\n", "\n", "training_config.pso_config_finetune['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_finetune['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_finetune['t2_time'] = t2_time_array\n", "training_config.pso_config_finetune['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_finetune['t2_range_min'] = 0.1\n", "training_config.pso_config_finetune['t2_range_max'] = 8000\n", "training_config.pso_config_finetune['parameters_boundaries']['log10_T2cutoff_long'] = (2.6, 2.65, 3.45, 3.5)\n", "\n", "# 3. 调整执行参数\n", "training_config.bootstrap_iterations = 20\n", "training_config.pso_config_lowo[\"max_iterations\"] = 400\n", "training_config.pso_config_lowo[\"n_particles\"] = 150\n", "training_config.pso_config_finetune[\"max_iterations\"] = 200\n", "training_config.pso_config_finetune[\"n_particles\"] = 100\n", "\n", "# 4. 执行训练步骤\n", "print(\"🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\")\n", "training_result = run_swift_pso_training_step(\n", "    config=training_config,\n", "    ctx=run_context, # 直接传递上下文对象\n", "    train_bundle=train_bundle,\n", "    backend='gpu'  # 执行层参数直接传入\n", ")\n", "\n", "print(f\"✅ [Step 1/5] 训练完成！ 结果: {training_result}\")\n", "print(f\"   - 最终模型参数已保存为产物: {SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value}\")\n", "print(f\"   - t-SNE源数据已保存为产物: {SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value}\")"]}, {"cell_type": "markdown", "id": "b1c2d3e4", "metadata": {}, "source": ["## 4. 步骤二：模型预测\n", "\n", "使用 `run_swift_pso_prediction_step` 对训练集和应用集进行预测。我们首先从 `RunContext` 中显式加载上一步训练产出的模型，然后将其作为参数传入预测函数。"]}, {"cell_type": "code", "execution_count": 5, "id": "f5g6h7i8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T05:34:46.185688Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.34, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T05:34:46.225958Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.35, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx file_size_mb=1.88 sheet_count=1\n", "2025-07-29T05:34:46.251991Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.35, 'cpu_percent': 0.0} project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T05:34:46.280674Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.35, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T05:34:46.309583Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.35, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T05:34:46.356751Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.35, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=8 well_curves=1\n", "2025-07-29T05:34:50.212624Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.0, 'cpu_percent': 0.0} shape=(4689, 71) sheet_name=swift_pso_apply_cleaned\n", "2025-07-29T05:34:50.276283Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.0, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned')\n", "2025-07-29T05:34:50.292365Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.0, 'cpu_percent': 0.0} curve_count=8 dataset_name=swift_pso_apply_cleaned dataset_type=Continuous df_shape=(4689, 71) processing_time=3.983\n", "2025-07-29T05:34:50.323696Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.0, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T05:34:50.355115Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.0, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T05:34:50.370807Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.0, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=4.185 project_name=WpIdentifier('scape_swift_pso_apply_cleaned')\n", "✅ 成功读取应用数据: ./scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T05:34:50.402212Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.0, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=8 input_curves=['DT2_P50', 'PHIT_NMR', 'MD', 'PHI_T2_DIST', 'T2LM', 'T2_P50', 'WELL_NO', 'DPHIT_NMR'] operation=extract_metadata output_curve_count=8 output_curves=['DT2_P50', 'PHIT_NMR', 'MD', 'PHI_T2_DIST', 'T2LM', 'T2_P50', 'WELL_NO', 'DPHIT_NMR']\n", "2025-07-29T05:34:50.433565Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.0, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T05:34:50.449513Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.0, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T05:34:50.488704Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.0, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T05:34:50.519988Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.0, 'cpu_percent': 0.0}\n"]}], "source": ["# --- 加载应用数据 ---\n", "apply_data_path = \"./scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "apply_project = reader.read(apply_data_path)\n", "print(f\"✅ 成功读取应用数据: {apply_data_path}\")\n", "\n", "apply_bundle = apply_project.get_dataset(\"swift_pso_apply_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR', 'PHI_T2_DIST'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "\n", "# --- 准备预测配置 (输出曲线名将作为facade函数的直接参数传入) ---\n", "prediction_config = SwiftPsoPredictionConfig.create_default()"]}, {"cell_type": "code", "execution_count": 6, "id": "j9k0l1m2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 2.1/5] 开始对训练集进行预测...\n", "\n", "================================================================================\n", "                            SWIFT-PSO 模型产物摘要\n", "================================================================================\n", "\n", "--- 优化参数 (Optimized Parameters) ---\n", "                 参数名      数值    线性域数值\n", "        log10_KSDR_A -1.7658   0.0171\n", "            PHIT_EXP  4.7669      N/A\n", "             RHO_NMR  5.0182      N/A\n", "      log10_KMACRO_A -0.4038   0.3946\n", "log10_T2cutoff_short  1.8899  77.6036\n", " log10_T2cutoff_long  2.6492 445.8950\n", "              beta_1  0.3008      N/A\n", "              beta_2  0.8432      N/A\n", "           delta_MDT -0.2924      N/A\n", "\n", "--- 固定参数 (Fixed Parameters) ---\n", "       参数名     数值 线性域数值\n", "  T2LM_EXP 2.0000   N/A\n", "  KMACRO_B 2.0000   N/A\n", "Vmacro_min 0.0100   N/A\n", "\n", "--- 上下文 (Context) ---\n", "  - t2_p50_ref: 331.6580\n", "  - phit_nmr_ref: 0.0685\n", "\n", "================================================================================\n", "2025-07-29T05:34:50.684847Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.01, 'cpu_percent': 0.0} data_rows=297 operation=swift_pso_prediction_step run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:50.700488Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.01, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T05:34:50.731882Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.01, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T05:34:50.747797Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.01, 'cpu_percent': 0.0} data_rows=297 operation=swift_pso_prediction\n", "2025-07-29T05:34:50.763437Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.02, 'cpu_percent': 0.0} n_bins=64 n_depths=297 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.14300481) v_meso_range=(8.999999999999999e-08, 0.08350502) v_micro_range=(3.700000000000002e-07, 0.05703878000000002)\n", "2025-07-29T05:34:50.803488Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.02, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(3.323477719005793e-08, 35.348596763621366) result_rows=297\n", "2025-07-29T05:34:50.834942Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.03, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T05:34:50.866566Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.03, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T05:34:50.897176Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.03, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T05:34:50.919989Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.03, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T05:34:50.951586Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.03, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T05:34:51.002518Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:51.034192Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=297\n", "✅ 训练集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n", "🚀 [Step 2.2/5] 开始对应用集进行预测...\n", "2025-07-29T05:34:51.049846Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction_step run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:51.081242Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T05:34:51.128396Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T05:34:51.159839Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction\n", "2025-07-29T05:34:51.207108Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.33, 'cpu_percent': 0.0} n_bins=64 n_depths=4689 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.15384649999999997) v_meso_range=(8.999999999999999e-08, 0.09347911999999999) v_micro_range=(3.700000000000002e-07, 0.2788430499999999)\n", "2025-07-29T05:34:51.253576Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(5.27584812357841e-12, 35.348596763621366) result_rows=4689\n", "2025-07-29T05:34:51.298084Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T05:34:51.329827Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T05:34:51.361233Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T05:34:51.377164Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T05:34:51.413540Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T05:34:51.474239Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:34:51.521561Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=4689\n", "2025-07-29T05:34:51.553133Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} copy_data=False new_dataset_name=train_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T05:34:51.600198Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T05:34:51.631876Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=train_apply dataset_type=Point operation=dataset_initialization\n", "2025-07-29T05:34:51.659821Z [info     ] 成功添加 'train_apply' (WpDiscreteDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T05:34:51.663297Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} copy_data=False new_dataset_name=pred_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T05:34:51.694777Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:34:51.726526Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=pred_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T05:34:51.746925Z [info     ] 成功添加 'pred_apply' (WpContinuousDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T05:34:51.774269Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} dataset_count=2 file_path=output01\\swift_pso_run_20250729_133259\\swift_pso_apply_result.wp.xlsx project_name=WpIdentifier('swift_pso_apply_result') save_head_info=True save_well_map=True\n", "2025-07-29T05:34:51.821676Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.07, 'cpu_percent': 0.0} curve_count=15 dataset_name=WpIdentifier('train_apply') dataset_type=Point df_shape=(297, 78)\n", "2025-07-29T05:34:52.044014Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.33, 'cpu_percent': 0.0} dataset_name=WpIdentifier('train_apply') processing_time=0.238\n", "2025-07-29T05:34:52.059824Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.33, 'cpu_percent': 0.0} curve_count=12 dataset_name=WpIdentifier('pred_apply') dataset_type=Continuous df_shape=(4689, 75)\n", "2025-07-29T05:34:53.619679Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.59, 'cpu_percent': 0.0} dataset_name=WpIdentifier('pred_apply') processing_time=1.56\n", "2025-07-29T05:34:53.660329Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 552.95, 'cpu_percent': 0.0} dataset_count=2 head_info=False total_sheets=2 well_map=False\n", "2025-07-29T05:34:53.700150Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 552.95, 'cpu_percent': 0.0}\n", "2025-07-29T05:34:53.714889Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 552.96, 'cpu_percent': 0.0} workbook_sheets=2\n", "2025-07-29T05:35:24.120096Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.59, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:24.141064Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.6, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:30.109224Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.92, 'cpu_percent': 0.0} file_path=output01\\swift_pso_run_20250729_133259\\swift_pso_apply_result.wp.xlsx processing_time=38.335 project_name=WpIdentifier('swift_pso_apply_result')\n", "✅ 应用集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n"]}], "source": ["# --- 对训练集进行预测 ---\n", "print(\"🚀 [Step 2.1/5] 开始对训练集进行预测...\")\n", "# 1. 实例化产物处理器\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "# 2. 从RunContext加载上一步训练产出的模型参数\n", "model_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.FINAL_PARAMETERS)\n", "model_assets = handler.load_parameters(model_params_path)\n", "handler.print_model_assets_human_readable(model_assets)\n", "\n", "# 3. 调用预测步骤，并传入加载的模型\n", "train_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets,  # 传入加载的模型\n", "    prediction_bundle=train_bundle, # 使用训练数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu' # 预测通常在CPU上进行\n", ")\n", "\n", "# 获取带预测结果的bundle，以备后续步骤使用\n", "train_bundle_with_pred = train_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 训练集预测完成！新增曲线: {train_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")\n", "\n", "# --- 对应用集进行预测 ---\n", "print(\"🚀 [Step 2.2/5] 开始对应用集进行预测...\")\n", "# 模型参数只需加载一次，可复用\n", "apply_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets, # 传入加载的模型\n", "    prediction_bundle=apply_bundle, # 使用应用数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu'\n", ")\n", "\n", "temp_project = WpWellProject(name=\"swift_pso_apply_result\")\n", "temp_project.add_dataframe_bundle(\"train_apply\",train_bundle)\n", "temp_project.add_dataframe_bundle(\"pred_apply\",apply_bundle)\n", "\n", "writer = WpExcelWriter()\n", "apply_result_path = output_dir / run_dir_name / \"swift_pso_apply_result.wp.xlsx\"\n", "writer.write(temp_project, apply_result_path, apply_formatting=True)\n", "\n", "apply_bundle_with_pred = apply_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 应用集预测完成！新增曲线: {apply_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")"]}, {"cell_type": "markdown", "id": "1af960c4", "metadata": {}, "source": ["## 训练集上渗透率交会图"]}, {"cell_type": "code", "execution_count": 7, "id": "e07ee165", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T05:35:30.215664Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.93, 'cpu_percent': 0.0} step_name=train_all_perm_corr_analysis\n", "2025-07-29T05:35:30.255763Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.94, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:30.267939Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.94, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T05:35:30.282344Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.14, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:30.343759Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.14, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:30.362309Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.14, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:30.401853Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.14, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:30.468147Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.52, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:30.496629Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_133259\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.57, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:31.976599Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.89, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T05:35:32.013260Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.89, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:32.029831Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.89, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T05:35:32.069663Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.91, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:32.109668Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.91, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:32.122416Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.91, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:32.162485Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.92, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:32.202369Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 611.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:32.228667Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_133259\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 611.07, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:33.563856Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.32, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T05:35:33.602655Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:33.630159Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.33, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T05:35:33.670449Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.35, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:33.696873Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.36, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:33.723570Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.37, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:33.776857Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.38, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:33.856487Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:33.910366Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_133259\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.4, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:35.297743Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.38, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T05:35:35.326897Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.38, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:35.363768Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.38, 'cpu_percent': 0.0}\n", "  - TRAIN_ALL 模型验证完成。\n", "\n", "2025-07-29T05:35:35.377863Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.38, 'cpu_percent': 0.0} step_name=train_pz_perm_corr_analysis\n", "2025-07-29T05:35:35.403735Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.39, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:35.417276Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.39, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T05:35:35.457028Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.48, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:35.499197Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.48, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:35.527436Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:35.564204Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.49, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:35.617217Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.85, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:35.643831Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_133259\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.91, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:37.018336Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.05, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T05:35:37.071446Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.05, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:37.111529Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.05, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T05:35:37.151680Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.06, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:37.191267Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.06, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:37.218247Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:37.244419Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:37.311262Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:37.338305Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_133259\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.41, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:39.112676Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.6, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T05:35:39.152507Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.6, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:39.192443Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.6, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T05:35:39.245286Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.6, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:39.285090Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.6, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:39.312206Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.6, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:39.352280Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.6, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:39.392164Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.61, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:39.431355Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_133259\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.61, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:40.848165Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.12, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T05:35:40.886450Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:40.913114Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.13, 'cpu_percent': 0.0}\n", "  - TRAIN_PZ 模型验证完成。\n", "\n", "2025-07-29T05:35:40.939188Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.13, 'cpu_percent': 0.0} step_name=train_core_perm_corr_analysis\n", "2025-07-29T05:35:40.965927Z [info     ] 找到 1 口共同井进行分析: ['C-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.14, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:40.992486Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.14, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T05:35:41.034191Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.14, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:41.079592Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.14, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:41.099682Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.14, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:41.159905Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:41.219726Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.44, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:41.259582Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_133259\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.56, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:42.607239Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.72, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T05:35:42.643665Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.72, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:42.660291Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.72, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE 模型验证完成。\n", "\n", "2025-07-29T05:35:42.687042Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.72, 'cpu_percent': 0.0} step_name=train_core_pz_perm_corr_analysis\n", "2025-07-29T05:35:42.717229Z [info     ] 找到 1 口共同井进行分析: ['C-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.72, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:42.726946Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.72, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T05:35:42.767812Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.74, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:42.793747Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.74, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:42.819904Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.74, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:42.847067Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 520.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:42.900341Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 521.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:35:42.940459Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_133259\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 521.0, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:44.314469Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 524.8, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_133259\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T05:35:44.353685Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 524.8, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:44.380865Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 524.8, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE_PZ 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"train_all\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.415528414185691,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 24.0% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.6% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6937601083693103,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 17.6% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 26.4% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6970127189934551,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 38.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 58.1% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.3957340858706719,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 41.8% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 71.4% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.12654757501256778,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 38.5% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 53.9% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6360978636992338,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 47.6% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 71.4% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.3757681979473653,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 26.2% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.8% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.3757681979473653,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 26.2% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.8% <= 90%\\u3002\"\n", "    }\n", "  }\n", "}\n"]}], "source": ["models_to_perm_corr = {\n", "    \"train_all\": train_label_all_bundle,\n", "    \"train_pz\": train_label_pz_bundle,\n", "    \"train_core\": train_label_core_bundle,\n", "    \"train_core_pz\": train_label_core_bundle,\n", "}\n", "\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_results = {}\n", "for model_prefix, label_bundle in models_to_perm_corr.items():\n", "    perm_corr_config = PermCorrelationConfig()\n", "    perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": f\"Core Perm vs. Predicted Perm ({model_prefix.upper()})\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", "    )\n", "    perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=train_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=label_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=model_prefix\n", "    )\n", "    perm_corr_results[model_prefix] = perm_corr_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(perm_corr_results, indent=2))"]}, {"cell_type": "markdown", "id": "n3o4p5q6", "metadata": {}, "source": ["## 5. 步骤三：t-SNE 可视化\n", "\n", "调用 `run_tsne_visualization_step` 对训练过程中的参数演化进行可视化。此步骤会自动消费训练步骤产出的 `ALL_OPTIMIZED_PARAMETERS` 产物。\n", "\n", "我们采用 **Get -> Modify -> Pass** 模式来定制图表：\n", "1.  **Get**: 从全局注册表 `plot_registry` 获取一个默认的 `PlotProfile` 模板。\n", "2.  **Modify**: 在运行时动态修改模板的属性（如标题）。\n", "3.  **Pass**: 将修改后的 `PlotProfile` 对象传入 `facade` 函数。"]}, {"cell_type": "code", "execution_count": 8, "id": "r7s8t9u0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 3/5] 开始执行 t-SNE 可视化...\n", "2025-07-29T05:35:45.034765Z [info     ] 开始t-SNE可视化步骤                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.75, 'cpu_percent': 0.0} custom_profiles=['swift_pso.tsne_convergence', 'swift_pso.tsne_cluster_analysis'] operation=tsne_visualization_step run_id=20250729-053259-a669311d source_data_rows=720\n", "2025-07-29T05:35:45.074486Z [info     ] 开始执行收敛轨迹分析                     [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.75, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T05:35:45.087806Z [info     ] 开始t-SNE降维计算                    [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.75, 'cpu_percent': 0.0} data_rows=720 operation=tsne_computation perplexity=15\n", "2025-07-29T05:35:45.127773Z [info     ] 执行t-SNE降维                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.78, 'cpu_percent': 0.0} init=pca learning_rate=200.0 max_iter=2000 n_components=2 operation=tsne_computation perplexity=15 random_state=42 verbose=0\n", "2025-07-29T05:35:54.811787Z [info     ] 在原始高维空间上执行K-means聚类            [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.85, 'cpu_percent': 0.0} n_clusters=4 operation=tsne_computation\n", "2025-07-29T05:35:55.025175Z [info     ] 聚类轮廓系数 (Silhouette Score): 0.1796 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 505.29, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:55.038287Z [info     ] t-SNE降维和聚类计算完成                 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 505.29, 'cpu_percent': 0.0} operation=tsne_computation result_rows=720 tsne_x_range=(-874.1135864257812, 891.814208984375) tsne_y_range=(-918.5004272460938, 996.2288818359375)\n", "2025-07-29T05:35:55.091561Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 506.07, 'cpu_percent': 0.0} description=t-SNE收敛轨迹的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:55.126104Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 506.07, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization\\tsne_convergence_trajectory_profile.json profile_name=swift_pso.tsne_convergence\n", "2025-07-29T05:35:55.145037Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 506.07, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization\\tsne_convergence_trajectory_plot.png profile_name=swift_pso.tsne_convergence snapshot_path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization\\tsne_convergence_trajectory_data.csv\n", "2025-07-29T05:35:55.200421Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 506.86, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_convergence\n", "2025-07-29T05:35:55.345440Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_convergence_trajectory_plot base_path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.14, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:56.999033Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.38, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T05:35:57.025783Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.38, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T05:35:57.057507Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.38, 'cpu_percent': 0.0} description=SWIFT-PSO参数演化轨迹的t-SNE可视化图表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:57.075086Z [info     ] 开始执行最终收敛点聚类分析                  [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.38, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T05:35:57.086106Z [info     ] 开始最终收敛点的聚类分析                   [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.38, 'cpu_percent': 0.0} operation=cluster_analysis_computation total_points=720\n", "2025-07-29T05:35:57.105253Z [info     ] 筛选出 60 个最终收敛点进行分析。             [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.38, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:57.586143Z [info     ] 执行聚类分析，方法: kmeans              [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.75, 'cpu_percent': 0.0} operation=cluster_analysis_computation\n", "2025-07-29T05:35:57.666514Z [info     ] 聚类分析计算完成。                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.91, 'cpu_percent': 0.0}\n", "2025-07-29T05:35:57.678655Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.91, 'cpu_percent': 0.0} description=最终收敛点聚类分析的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:57.718648Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.91, 'cpu_percent': 0.0} description=聚类分析的总体量化指标报告，包括轮廓系数、簇心等。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:57.745370Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_statistics artifact_path=swift_pso_visualization\\tsne_cluster_statistics_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.91, 'cpu_percent': 0.0} description=每个簇内部所有参数的详细统计信息（均值、标准差等）。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:57.786083Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_summary_table artifact_path=swift_pso_visualization\\tsne_cluster_summary_table.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.95, 'cpu_percent': 0.0} description=各簇参数均值和标准差的对比摘要表，便于物理意义解释。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:57.813671Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.95, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization\\tsne_cluster_analysis_profile.json profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T05:35:57.834780Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.95, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization\\tsne_cluster_analysis_plot.png profile_name=swift_pso.tsne_cluster_analysis snapshot_path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization\\tsne_cluster_analysis_data.csv\n", "2025-07-29T05:35:57.878916Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 574.95, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T05:35:57.945132Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_cluster_analysis_plot base_path=output01\\swift_pso_run_20250729_133259\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 575.33, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:35:59.386950Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T05:35:59.418917Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_133259\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T05:35:59.465547Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} description=SWIFT-PSO最终收敛点的t-SNE聚类分析图表（带凸包）。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:35:59.493290Z [info     ] t-SNE可视化步骤完成                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "✅ [Step 3/5] t-SNE 可视化完成！结果: {'status': 'completed', 'clusters_found': 4}\n"]}], "source": ["# 1. 创建可视化配置 (保持不变)\n", "tsne_config = TsneVisualConfig(\n", "    perplexity=15,\n", "    n_iter=2000,\n", "    random_state=42,\n", "    cluster_method=\"kmeans\",\n", "    n_clusters=4,\n", "    dbscan_eps=1,\n", "    dbscan_min_samples=5\n", ")\n", "\n", "# 2. 获取并修改两个绘图配置，然后打包成字典\n", "# 2.1 收敛轨迹图的配置\n", "trajectory_profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Parameter Evolution (Trajectory)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.2 聚类分析图的配置\n", "cluster_profile = plot_registry.get(TsnePlotProfiles.CLUSTER_ANALYSIS).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Final Parameters (Cluster Analysis)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.3 打包成字典\n", "custom_plot_profiles = {\n", "    TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value: trajectory_profile,\n", "    TsnePlotProfiles.CLUSTER_ANALYSIS.value: cluster_profile\n", "}\n", "\n", "# 3. 执行可视化步骤 (加载数据的部分保持不变)\n", "print(\"🚀 [Step 3/5] 开始执行 t-SNE 可视化...\")\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "tsne_source_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)\n", "tsne_source_data = handler.load_dataframe(tsne_source_path)\n", "\n", "# 3.1 更新函数调用\n", "tsne_result = run_tsne_visualization_step(\n", "    config=tsne_config,\n", "    ctx=run_context,\n", "    tsne_source_data=tsne_source_data,\n", "    plot_profiles=custom_plot_profiles # <--- 修改此处\n", ")\n", "\n", "# 4. 更新打印的产物信息\n", "print(f\"✅ [Step 3/5] t-SNE 可视化完成！结果: {tsne_result}\")"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["## 6. 步骤四：PLT 盲井检验\n", "\n", "调用 `run_plt_analysis_step` 对模型的预测结果进行PLT盲井检验。此步骤会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物（包含在 `apply_bundle_with_pred` 中）。\n", "\n", "我们为不同的图表（贡献率交会图、捕获率曲线、洛伦兹曲线）分别获取并定制 `PlotProfile`，然后将它们打包成一个字典传入。"]}, {"cell_type": "code", "execution_count": 9, "id": "w5x6y7z8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T05:35:59.619297Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx\n", "2025-07-29T05:35:59.655656Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T05:35:59.693263Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} project_name=scape_plt_val\n", "2025-07-29T05:35:59.719086Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_plt_val\n", "2025-07-29T05:35:59.733533Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T05:35:59.746230Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T05:35:59.762089Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T05:35:59.772875Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T05:35:59.819442Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T05:35:59.852860Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} shape=(14, 4) sheet_name=PLT\n", "2025-07-29T05:35:59.884075Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T05:35:59.906456Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(14, 4) processing_time=0.134\n", "2025-07-29T05:35:59.945752Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T05:35:59.972405Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T05:35:59.986752Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx processing_time=0.367 project_name=WpIdentifier('scape_plt_val')\n", "2025-07-29T05:36:00.020448Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} extracted_curve_count=1 operation=extract_curve_dataframe_bundle\n", "2025-07-29T05:36:00.040183Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['QOZI', 'WELL_NO', 'MD_Bottom', 'MD_Top'] operation=extract_metadata output_curve_count=4 output_curves=['QOZI', 'WELL_NO', 'MD_Bottom', 'MD_Top']\n", "✅ 成功读取PLT验证数据: ./scape_plt_val.wp.xlsx\n", "🚀 [Step 4/5] 开始执行 PLT 盲井检验...\n", "2025-07-29T05:36:00.080227Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} step_name=plt_analysis\n", "2025-07-29T05:36:00.093422Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0}\n", "2025-07-29T05:36:00.106909Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.1, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T05:36:00.147015Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:00.186981Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:00.222944Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:00.253414Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:00.293268Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:00.346079Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:00.373560Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.11, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:00.427253Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.12, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:00.467032Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.12, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T05:36:00.506971Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:00.559334Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:00.600376Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T05:36:00.627096Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\swift_pso_run_20250729_133259\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 643.12, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T05:36:00.879712Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.87, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T05:36:00.907311Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.87, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:00.938054Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.87, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T05:36:00.960003Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.87, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:00.986645Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.87, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:01.054047Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.87, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T05:36:01.094004Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\swift_pso_run_20250729_133259\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.87, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T05:36:01.374298Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 648.64, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T05:36:01.400859Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 648.64, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:01.440596Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 648.64, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T05:36:01.467197Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 648.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:01.507229Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 648.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:01.547423Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 648.64, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T05:36:01.574106Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\swift_pso_run_20250729_133259\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 648.64, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T05:36:01.801069Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T05:36:01.827563Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:01.867463Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T05:36:01.907008Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:01.946956Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:01.987574Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:02.014211Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:02.053782Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:02.107225Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:02.147665Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T05:36:02.201083Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:02.227695Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T05:36:02.254289Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:02.281057Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:02.333912Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T05:36:02.360831Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\swift_pso_run_20250729_133259\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 651.38, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T05:36:02.601479Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.41, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T05:36:02.641312Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.41, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:02.667839Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.41, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T05:36:02.694396Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:02.721243Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:02.765620Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.47, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T05:36:02.787369Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\swift_pso_run_20250729_133259\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.47, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T05:36:03.054850Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.22, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T05:36:03.094707Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.22, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:03.134596Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.22, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T05:36:03.161296Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:03.187648Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:03.231264Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.22, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T05:36:03.267597Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\swift_pso_run_20250729_133259\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.22, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T05:36:03.534846Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.18, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T05:36:03.574720Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.18, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:03.601325Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.18, 'cpu_percent': 0.0}\n", "✅ [Step 4/5] PLT 检验完成！结果: {'C-1': {'spearman_rho': 0.42857142857142866, 'gini_capture': 0.39536310102506333, 'gini_lorenz': 0.20299736199446605}, 'C-2': {'spearman_rho': 0.39285714285714296, 'gini_capture': -0.050207321282104145, 'gini_lorenz': 0.36374540446551595}}\n", "   - 分析报告已保存为产物: plt_analysis.reports.analyzed_layers_* \n"]}], "source": ["# --- 加载PLT验证数据 ---\n", "plt_val_path = \"./scape_plt_val.wp.xlsx\"\n", "plt_val_project = reader.read(plt_val_path)\n", "plt_val_bundle = plt_val_project.get_dataset(\"PLT\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取PLT验证数据: {plt_val_path}\")\n", "\n", "# 1. 创建PLT分析配置 (曲线名将作为facade函数的直接参数传入)\n", "plt_config = PltAnalysisConfig()\n", "\n", "# 2. 获取并修改多个绘图配置 (Get -> Modify -> Pass)\n", "contribution_profile = plot_registry.get(PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT).with_updates(\n", "    title_props={\"label\": \"Flow Contribution Crossplot\"}\n", ")\n", "\n", "capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE).with_updates(\n", "    title_props={\"label\": \"Permeability Capture Curve\"}\n", ")\n", "\n", "lorenz_profile = plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE).with_updates(\n", "    title_props={\"label\": \"Lorenz Curve Analysis\"}\n", ")\n", "\n", "plt_plot_profiles = {\n", "    PltPlotTypes.CONTRIBUTION_CROSSPLOT: contribution_profile,\n", "    PltPlotTypes.CAPTURE_CURVE: capture_profile,\n", "    PltPlotTypes.LORENZ_CURVE: lorenz_profile\n", "}\n", "\n", "# 3. 执行PLT分析步骤\n", "print(\"🚀 [Step 4/5] 开始执行 PLT 盲井检验...\")\n", "plt_result = run_plt_analysis_step(\n", "    config=plt_config,\n", "    ctx=run_context,\n", "    prediction_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    plt_bundle=plt_val_bundle,\n", "    permeability_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    flow_rate_curve=\"QOZI\",           # 数据选择器参数\n", "    plot_profiles=plt_plot_profiles\n", ")\n", "\n", "print(f\"✅ [Step 4/5] PLT 检验完成！结果: {plt_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PltAnalysisArtifacts.ANALYZED_LAYERS_TABLE_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["## 7. 步骤五：岩心井渗透率(CT)相关性分析\n", "\n", "调用 `run_perm_correlation_step` 对模型的预测结果进行岩心井检验。此步骤同样会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物。"]}, {"cell_type": "code", "execution_count": 10, "id": "e5f6g7h8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T05:36:03.721577Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.21, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx\n", "2025-07-29T05:36:03.775229Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.21, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T05:36:03.801791Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.21, 'cpu_percent': 0.0} project_name=scape_core_k_val\n", "2025-07-29T05:36:03.828310Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.21, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_core_k_val\n", "2025-07-29T05:36:03.854973Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.21, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T05:36:03.881582Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.21, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T05:36:03.908251Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.21, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T05:36:03.948126Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.46, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-29T05:36:03.981047Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.46, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T05:36:04.014607Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} shape=(44, 9) sheet_name=K_Val\n", "2025-07-29T05:36:04.041224Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-29T05:36:04.054891Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(44, 9) processing_time=0.12\n", "2025-07-29T05:36:04.081745Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T05:36:04.108311Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T05:36:04.121606Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val.wp.xlsx processing_time=0.4 project_name=WpIdentifier('scape_core_k_val')\n", "2025-07-29T05:36:04.148432Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} extracted_curve_count=7 operation=extract_curve_dataframe_bundle\n", "2025-07-29T05:36:04.161749Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.49, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['K_LABEL', 'POR', 'PERM', 'PERM_LT_001_FLAG', 'Lithology', 'WELL_NO', 'SAMPLE_TYPE', 'MD', 'K_LABEL_TYPE'] operation=extract_metadata output_curve_count=9 output_curves=['K_LABEL', 'POR', 'PERM', 'PERM_LT_001_FLAG', 'Lithology', 'WELL_NO', 'SAMPLE_TYPE', 'MD', 'K_LABEL_TYPE']\n", "✅ 成功读取岩心验证数据: ./scape_core_k_val.wp.xlsx\n", "🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\n", "2025-07-29T05:36:04.201737Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.5, 'cpu_percent': 0.0} step_name=ct_perm_corr_analysis\n", "2025-07-29T05:36:04.230952Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.5, 'cpu_percent': 0.0}\n", "2025-07-29T05:36:04.241237Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.5, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T05:36:04.267327Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.39, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:04.294975Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.39, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_133259\\ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:36:04.321151Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:04.348113Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:04.388368Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.48, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T05:36:04.415044Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_133259\\ct_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.48, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T05:36:06.109412Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 513.12, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_133259\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_133259\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T05:36:06.142441Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 513.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-053259-a669311d\n", "2025-07-29T05:36:06.175753Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 513.12, 'cpu_percent': 0.0}\n", "✅ [Step 5/5] 岩心井检验完成！结果: {'T-1': {'spearman_rho': 0.5325608751840004, 'conclusion': \"Fail: 5x符合率 9.8% <= 50% (未满足井 'T-1' 的放宽标准)。\"}}\n", "   - 分析报告已保存为产物: perm_corr_analysis.plots.crossplot_* \n"]}], "source": ["# --- 加载岩心验证数据 ---\n", "core_val_path = \"./scape_core_k_val.wp.xlsx\"\n", "core_val_project = reader.read(core_val_path)\n", "core_val_bundle = core_val_project.get_dataset(\"K_Val\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取岩心验证数据: {core_val_path}\")\n", "\n", "# 1. 创建渗透率相关性分析配置\n", "perm_corr_config = PermCorrelationConfig(\n", "    relaxed_wells=[\"T-1\"]     # 对T-1井使用放宽的深度对齐标准\n", ")\n", "\n", "# 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": \"Core Perm vs. Predicted Perm\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "\n", "# 3. 执行渗透率相关性分析步骤\n", "print(\"🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\")\n", "perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=core_val_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"PERM\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=\"ct\"\n", ")\n", "\n", "print(f\"✅ [Step 5/5] 岩心井检验完成！结果: {perm_corr_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PermCorrelationArtifacts.CROSSPLOT_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "i9j0k1l2", "metadata": {}, "source": ["## 8. 总结\n", "\n", "🎉 **SWIFT-PSO Case 02 (重构版) 工作流执行完毕！**\n", "\n", "本次重构展示了如何使用新的组件化框架来构建一个清晰、可维护、可追踪的机器学习工作流。所有步骤的输入、输出和配置都得到了规范化管理。\n", "\n", "**关键亮点:**\n", "1.  **统一的运行上下文 (`RunContext`)**: 所有的步骤都在同一个 `RunContext` 中执行，确保了所有产物（模型、数据集、图表、报告）和日志都被集中管理在一个独立的运行目录中：\n", "    - **输出目录**: `./output02/swift_pso_workflow_run`\n", "2.  **清晰的步骤划分**: 每个核心任务（训练、预测、可视化、验证）都被封装成一个独立的 `run_*_step` 函数，职责明确。\n", "3.  **类型安全的配置 (Pydantic)**: 使用 `SwiftPsoTrainingConfig`, `TsneVisualConfig`, `PltAnalysisConfig` 等Pydantic模型替代了易出错的字典，提供了自动验证和清晰的文档。\n", "4.  **自动化的产物管理**: `facade` 函数内部处理了产物的保存和注册，使得工作流代码更简洁。下游步骤可以通过 `RunContext` 自动加载上游产物，无需手动传递文件路径。\n", "5.  **灵活的绘图系统 (`PlotProfile`)**: 通过 **Get -> Modify -> Pass** 模式，我们可以在不修改组件源码的情况下，轻松地定制图表的每一个细节，同时享受高质量的默认模板。"]}, {"cell_type": "markdown", "id": "j8k9l0m1", "metadata": {}, "source": ["## 9. 最终化运行\n", "\n", "在所有步骤完成后，手动调用 `finalize()` 来结束本次运行。这将确保所有日志被刷新，并且运行清单 `manifest.json` 被正确写入。"]}, {"cell_type": "code", "execution_count": 11, "id": "n2o3p4q5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T05:36:06.295992Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 513.12, 'cpu_percent': 0.0} duration_seconds=186.768 manifest_path=output01\\swift_pso_run_20250729_133259\\manifest.json operation=finalize run_id=20250729-053259-a669311d status=RUNNING\n", "✅ 运行已最终化，所有记录已保存。\n"]}], "source": ["run_context.finalize()\n", "print(\"✅ 运行已最终化，所有记录已保存。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}