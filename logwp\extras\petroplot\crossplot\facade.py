"""logwp.extras.petroplot.crossplot.facade - 交会图组件门面

本模块提供了交会图组件的公共API，包括用于工作流的步骤门面
和用于独立调用的普通门面。

Architecture
------------
层次/依赖: petroplot/crossplot的公共API层，被外部工作流或脚本调用
设计原则: 遵循《可追踪机器学习组件开发框架》的门面模式，负责编排绘图流程
"""

from typing import Dict, Optional, Any, List
import json
import copy
from pathlib import Path
import pandas as pd
import plotly.graph_objects as go
from logwp.infra import get_logger
from logwp.extras.tracking import RunContext
from logwp.models.constants import WpDepthRole
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.extras.plotting import PlotProfile, registry, save_figure
from logwp.models.curve import CurveExpansionMode
from logwp.extras.petroplot.common import save_and_register_plots

from .config import CrossPlotConfig, CrossPlotColumnSelectors, SeriesColumnSelectors, SeriesConfig
from .internal.plotter import draw_crossplot
from .constants import CrossPlotProfiles, CrossPlotArtifacts

logger = get_logger(__name__)


def _resolve_selectors(
    config: CrossPlotConfig,
    bundles: Dict[str, WpDataFrameBundle]
) -> CrossPlotColumnSelectors:
    """
    将用户配置中的逻辑曲线名解析为物理列名。

    这是“翻译官”模式的核心实现。它迭代配置中的每个系列，
    根据系列指定的 `bundle_name` 找到对应的数据源，
    然后使用该数据源的元数据来解析曲线名。

    Args:
        config: 包含多个系列配置的CrossPlotConfig对象。
        bundles: 一个数据源字典，键是逻辑名，值是WpDataFrameBundle。

    Returns:
        一个包含已解析的物理列名的CrossPlotColumnSelectors对象。
    """
    resolved_series_selectors = []
    for series_cfg in config.series:
        bundle_name = series_cfg.bundle_name
        if bundle_name not in bundles:
            logger.warning(f"系列 '{series_cfg.name}' 的数据源 '{bundle_name}' 未在传入的bundles中找到，将跳过。")
            continue

        bundle = bundles[bundle_name]
        meta = bundle.curve_metadata

        def _resolve(curve_name: Optional[str]) -> Optional[str]:
            if not curve_name:
                return None
            try:
                # expand_curve_names 返回一个列表，我们期望一个列
                return meta.expand_curve_names([curve_name], mode=CurveExpansionMode.DATAFRAME)[0]
            except (ValueError, IndexError) as e:
                logger.warning(f"无法在数据源 '{bundle_name}' 中解析曲线 '{curve_name}' (用于系列 '{series_cfg.name}'): {e}。此字段将被忽略。")
                return None

        def _resolve_list(curve_names: Optional[List[str]]) -> Optional[List[str]]:
            if not curve_names:
                return None
            resolved = [c for c in (_resolve(cn) for cn in curve_names) if c]
            return resolved if resolved else None

        well_curves = meta.get_well_identifier_curves()
        if not well_curves:
            raise ValueError("无法在数据Bundle的元数据中找到井名标识曲线。")

        depth_curves = meta.get_curves_by_depth_role(WpDepthRole.SINGLE)
        if not depth_curves:
            raise ValueError("无法在数据Bundle的元数据中找到单深度参考曲线。")

        # 创建一个包含已解析物理名称的SeriesColumnSelectors对象
        series_selector = SeriesColumnSelectors(
            name=series_cfg.name,
            bundle_name=series_cfg.bundle_name,
            plot_type=series_cfg.plot_type,
            x_col=_resolve(series_cfg.x_curve),
            y_col=_resolve(series_cfg.y_curve),
            color_col=_resolve(series_cfg.color_curve),
            symbol_col=_resolve(series_cfg.symbol_curve),
            split_col=_resolve(series_cfg.split_by_curve),
            well_col=_resolve(well_curves[0]),
            depth_col=_resolve(depth_curves[0]),
            hover_extra_cols=_resolve_list(series_cfg.hover_extra_curves),
            color_mapping=series_cfg.color_mapping,
            symbol_mapping=series_cfg.symbol_mapping,
            line_style=series_cfg.line_style,
            null_marker_style=series_cfg.null_marker_style,
            distinguish_null_color=series_cfg.distinguish_null_color,
            include_in_marginals=series_cfg.include_in_marginals,
        )
        resolved_series_selectors.append(series_selector)

    return CrossPlotColumnSelectors(series=resolved_series_selectors)


def generate_crossplot(
    config: CrossPlotConfig,
    selectors: CrossPlotColumnSelectors,
    data_dict: Dict[str, pd.DataFrame],
    plot_profile: PlotProfile,
) -> Dict[str, go.Figure]:
    """
    根据已解析的选择器和数据字典生成一个或多个交会图。

    如果配置了拆分列，此函数将为每个唯一值生成一个图表。
    否则，只生成一个名为 "main" 的图表。

    Args:
        config: 绘图逻辑配置。
        selectors: 包含物理列名的选择器。
        data_dict: 以bundle_name为键的数据字典。
        plot_profile: 绘图样式模板。

    Returns:
        一个以拆分值（或"main"）为键，Plotly Figure对象为值的字典。
    """
    # 1. 查找拆分配置
    split_col: Optional[str] = None
    split_bundle_name: Optional[str] = None
    for s in selectors.series:
        if s.split_col:
            split_col = s.split_col
            split_bundle_name = s.bundle_name
            logger.info(f"绘图拆分已激活，将根据数据源 '{split_bundle_name}' 的列 '{split_col}' 进行拆分。")
            break  # 使用找到的第一个拆分配置

    # 2. 如果没有拆分，则绘制单个图表
    if not split_col or not split_bundle_name:
        fig = draw_crossplot(config=config, selectors=selectors, data_dict=data_dict, plot_profile=plot_profile)
        return {"main": fig}

    # 3. 如果有拆分，则为每个唯一值绘制一个图表
    figs_dict = {}
    split_df = data_dict.get(split_bundle_name)
    if split_df is None or split_col not in split_df.columns:
        logger.warning(f"用于拆分的列 '{split_col}' 在数据源 '{split_bundle_name}' 中未找到，将绘制单个图表。")
        fig = draw_crossplot(config=config, selectors=selectors, data_dict=data_dict, plot_profile=plot_profile)
        return {"main": fig}

    unique_values = sorted(split_df[split_col].dropna().unique())
    if len(unique_values) > config.max_split_plots:
        logger.warning(f"拆分值的数量 ({len(unique_values)}) 超过了最大限制 ({config.max_split_plots})，将只绘制前 {config.max_split_plots} 个值的图表。")
        unique_values = unique_values[:config.max_split_plots]

    for value in unique_values:
        logger.debug(f"正在为拆分值 '{value}' 生成图表...")
        split_data_dict = {}
        is_valid_split = True
        for bundle_name, df in data_dict.items():
            # 深度拷贝以避免修改原始数据
            df_copy = df.copy()
            if split_col in df_copy.columns:
                filtered_df = df_copy[df_copy[split_col] == value]
                if filtered_df.empty:
                    logger.debug(f"数据源 '{bundle_name}' 在按 '{split_col}=={value}' 筛选后为空。")
                split_data_dict[bundle_name] = filtered_df
            else:
                # 如果某个数据源没有拆分列，则将其完整地用于所有子图
                split_data_dict[bundle_name] = df_copy

        subtitle = config.split_subtitle_template.format(split_col=split_col, value=value)
        fig = draw_crossplot(
            config=config,
            selectors=selectors,
            data_dict=split_data_dict,
            plot_profile=plot_profile,
            subtitle=subtitle
        )
        figs_dict[str(value)] = fig

    return figs_dict


def run_crossplot_step(
    config: CrossPlotConfig,
    ctx: RunContext,
    bundles: Dict[str, WpDataFrameBundle],
    *,
    prefix: str,
    plot_profile: Optional[PlotProfile] = None
) -> Dict[str, Any]:
    """
    执行交会图绘制步骤，支持多系列、多数据源和绘图拆分。

    Args:
        config: 包含绘图逻辑和系列定义的配置对象。
        ctx: 当前运行的上下文，用于追踪。
        bundles: 一个数据源字典，键是逻辑名，值是WpDataFrameBundle。
        prefix: 产物输出目录的前缀。
        plot_profile: (可选) 自定义的绘图样式模板。

    Returns:
        一个包含状态和产物信息的字典。
    """
    logger.info("===== Crossplot Step Started =====")
    step_dir = ctx.get_step_dir(f"{prefix}_crossplot")

    # 1. 将用户配置中的逻辑曲线名解析为物理列名
    logger.info("Resolving logical curve names to physical column names for all series...")
    column_selectors = _resolve_selectors(config, bundles)

    if not column_selectors.series:
        logger.warning("没有可绘制的系列，步骤提前结束。")
        return {"status": "skipped", "message": "No valid series to plot."}

    # 2. 准备数据字典和绘图模板
    data_dict = {name: b.data for name, b in bundles.items()}
    profile = plot_profile or registry.get(CrossPlotProfiles.DEFAULT.value)

    # 3. 调用核心绘图逻辑 (支持拆分)
    logger.info("Generating plot(s)...")
    figs_dict = generate_crossplot(
        config=config,
        selectors=column_selectors,
        data_dict=data_dict,
        plot_profile=profile
    )

    # 4. 保存并注册绘图产物
    logger.info(f"Saving {len(figs_dict)} plot(s) and artifacts...")
    plot_paths = save_and_register_plots(
        figs_dict=figs_dict,
        ctx=ctx,
        output_dir=step_dir,
        base_artifact_name=CrossPlotArtifacts.PLOT_PREFIX.value,
        formats=config.output_formats,
        plot_profile=profile
    )

    # 5. 保存逻辑配置和数据快照以实现可复现性
    # 5a. 保存数据快照 (每个数据源一个csv)
    snapshot_dir = step_dir / CrossPlotArtifacts.DATA_SNAPSHOT_DIR.value.split('.')[-1]
    snapshot_dir.mkdir(exist_ok=True)
    for bundle_name, df in data_dict.items():
        df.to_csv(snapshot_dir / f"{bundle_name}.csv", index=False)

    ctx.register_artifact(
        snapshot_dir.relative_to(ctx.run_dir),
        CrossPlotArtifacts.DATA_SNAPSHOT_DIR.value,
        description="用于生成图表的所有数据源的数据快照目录。"
    )

    # 5b. 保存逻辑快照
    logic_config = {
        "config": config.model_dump(mode='json'),
        "selectors": column_selectors.model_dump(mode='json')
    }
    logic_config_path = step_dir / f"{CrossPlotArtifacts.LOGIC_CONFIG.value.split('.')[-1]}.json"
    with open(logic_config_path, 'w', encoding='utf-8') as f:
        json.dump(logic_config, f, indent=2)

    ctx.register_artifact(
        logic_config_path.relative_to(ctx.run_dir),
        CrossPlotArtifacts.LOGIC_CONFIG.value,
        description="用于生成图表的完整逻辑配置（包括已解析的列名）。"
    )

    logger.info("===== Crossplot Step Finished =====")
    return {"status": "completed", "plots": plot_paths}


def replot_crossplot_from_snapshot(
    snapshot_dir: Path,
    logic_config_path: Path,
    plot_profile: PlotProfile,
    output_path: Path,
) -> None:
    """从快照文件精确复现交会图。"""
    # 1. 加载逻辑配置
    with open(logic_config_path, 'r', encoding='utf-8') as f:
        logic_config_data = json.load(f)
    config = CrossPlotConfig(**logic_config_data['config'])
    selectors = CrossPlotColumnSelectors(**logic_config_data['selectors'])

    # 2. 从快照目录加载数据
    data_dict = {}
    for series in selectors.series:
        bundle_name = series.bundle_name
        if bundle_name not in data_dict: # 避免重复加载
            csv_path = snapshot_dir / f"{bundle_name}.csv"
            if csv_path.exists():
                data_dict[bundle_name] = pd.read_csv(csv_path)
            else:
                logger.warning(f"在快照目录中未找到数据文件: {csv_path}，可能会导致绘图错误。")

    # 3. 调用核心绘图逻辑 (注意：复现时不支持拆分，总是绘制单个图)
    fig = draw_crossplot(
        config=config,
        selectors=selectors,
        data_dict=data_dict,
        plot_profile=plot_profile
    )

    # 4. 保存最终图像
    save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
    logger.info(f"图表已从快照成功复现并保存至: {output_path}")
