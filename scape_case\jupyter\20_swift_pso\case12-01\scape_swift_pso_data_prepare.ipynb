{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SCAPE数据准备,数据源:`santos_data_v2.wp.xlsx`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-29T03:31:51.080361Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 127.76, 'cpu_percent': 0.0}\n", "库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:31:53.713485Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 356.18, 'cpu_percent': 0.0} file_path=santos_data_v2.wp.xlsx\n", "2025-07-29T03:31:53.785682Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.34, 'cpu_percent': 0.0} file_path=santos_data_v2.wp.xlsx file_size_mb=27.29 sheet_count=8\n", "2025-07-29T03:31:53.822241Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.34, 'cpu_percent': 0.0} project_name=santos_data_v2\n", "2025-07-29T03:31:53.840903Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.34, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data_v2\n", "2025-07-29T03:31:53.856429Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.34, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:31:53.880641Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.34, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:31:53.905471Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.34, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-07-29T03:31:53.921870Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.34, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T03:31:53.962182Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.64, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:31:54.027152Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 358.09, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=62 well_curves=1\n", "2025-07-29T03:32:32.537922Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 433.68, 'cpu_percent': 0.0} shape=(16303, 251) sheet_name=Logs\n", "2025-07-29T03:32:32.615728Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 432.99, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-07-29T03:32:32.629083Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 432.99, 'cpu_percent': 0.0} curve_count=62 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 251) processing_time=38.681\n", "2025-07-29T03:32:32.646026Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 433.04, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:32.697981Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 433.04, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=91 well_curves=1\n", "2025-07-29T03:32:37.264212Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.23, 'cpu_percent': 0.0} shape=(4502, 91) sheet_name=OBMIQ_Pred\n", "2025-07-29T03:32:37.311797Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.23, 'cpu_percent': 0.0} dataset_name=WpIdentifier('OBMIQ_Pred')\n", "2025-07-29T03:32:37.327902Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.23, 'cpu_percent': 0.0} curve_count=91 dataset_name=OBMIQ_Pred dataset_type=Continuous df_shape=(4502, 91) processing_time=4.682\n", "2025-07-29T03:32:37.375890Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.23, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:37.391736Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.23, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T03:32:37.471137Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-07-29T03:32:37.503240Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-07-29T03:32:37.519325Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.155\n", "2025-07-29T03:32:37.551429Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T03:32:37.587634Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T03:32:37.603406Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} shape=(15, 4) sheet_name=PLT\n", "2025-07-29T03:32:37.621082Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T03:32:37.621082Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(15, 4) processing_time=0.07\n", "2025-07-29T03:32:37.649922Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:37.659679Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.34, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T03:32:37.689311Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.38, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-07-29T03:32:37.705285Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.38, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-29T03:32:37.721129Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.38, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.08\n", "2025-07-29T03:32:37.737183Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.38, 'cpu_percent': 0.0} dataset_count=5 has_head_info=True has_well_map=True\n", "2025-07-29T03:32:37.753184Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.38, 'cpu_percent': 0.0} dataset_count=5\n", "2025-07-29T03:32:37.770758Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.38, 'cpu_percent': 0.0} dataset_count=5 file_path=santos_data_v2.wp.xlsx processing_time=44.057 project_name=WpIdentifier('santos_data_v2')\n", "2025-07-29T03:32:37.784866Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.38, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data_v2'}\n", "2025-07-29T03:32:37.832643Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.46, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 5}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data_v2\n", "📅 数据集: dict_keys(['Logs', 'OBMIQ_Pred', 'K_Label', 'PLT', 'K_<PERSON>'])\n"]}], "source": ["data_file_path = \"./santos_data_v2.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 数据集: {project.datasets.keys()}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 合并K_Label和Logs数据集生成SCAPE训练数据"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 定义K_Label数据集的曲线列表\n", "k_label_curves = [\n", "     'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "]\n", "\n", "# 定义Logs数据集的曲线列表\n", "logs_curves = [\n", "    'T2LM',\n", "    'T2_P50',\n", "    'PHIT_NMR',\n", "    'PHI_T2_DIST'\n", "]\n", "\n", "obmiq_pred_curves = [\n", "    'DT2_P50',\n", "    'DPHIT_NMR',\n", "]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始合并K_Label和Logs数据集...\n", "\n", "📍 执行左对齐合并...\n", "2025-07-29T03:32:38.019287Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.53, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] left_dataset=K_Label operation=merge_datasets_left_aligned right_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST'] right_dataset=Logs\n", "2025-07-29T03:32:38.060865Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.53, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=K_Label target_dataset=K_Label_left_temp\n", "2025-07-29T03:32:38.110762Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.54, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['WELL_NO', 'K_LABEL', 'MD', 'K_LABEL_TYPE', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['WELL_NO', 'K_LABEL', 'MD', 'K_LABEL_TYPE', 'PZI']\n", "2025-07-29T03:32:38.149280Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.68, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_K_Label_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:38.178717Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.76, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T03:32:38.202462Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.76, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=K_Label_left_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-07-29T03:32:38.232408Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.76, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:38.253230Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.76, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=5 source_rows=506 target_rows=506\n", "2025-07-29T03:32:38.274959Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.76, 'cpu_percent': 0.0} curve_count=4 has_query=False operation=extract_curves source_dataset=Logs target_dataset=Logs_right_temp\n", "2025-07-29T03:32:38.298653Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 434.76, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=6 input_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'WELL_NO', 'PHI_T2_DIST', 'MD'] operation=extract_metadata output_curve_count=6 output_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'WELL_NO', 'PHI_T2_DIST', 'MD']\n", "2025-07-29T03:32:38.389963Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 473.41, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_Logs_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:38.431870Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.23, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-29T03:32:38.468908Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.23, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=Logs_right_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-29T03:32:38.497100Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.23, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:38.539311Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 474.23, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] operation=extract_curves selected_columns=69 source_rows=16303 target_rows=16303\n", "2025-07-29T03:32:38.578632Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.03, 'cpu_percent': 0.0} left_dataset_type=WpDiscreteDataset left_sampling_interval=0.010000000000218279 operation=merge_datasets_left_aligned\n", "2025-07-29T03:32:38.592738Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.03, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'WELL_NO', 'PHI_T2_DIST', 'MD']\n", "2025-07-29T03:32:38.607519Z [info     ] 开始连续型数据集深度重采样                  [logwp.models.datasets.internal.continuous_resample] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.03, 'cpu_percent': 0.0} custom_depth_range=False dataset_name=Logs_right_temp interpolation_method=nearest new_sampling_interval=0.010000000000218279 operation=resample_continuous_dataset original_sampling_interval=0.03809999999975844 out_of_range_fill_value=nan\n", "2025-07-29T03:32:39.257861Z [info     ] 连续型数据集深度重采样完成                  [logwp.models.datasets.internal.continuous_resample] actual_depth_range=(6310.27000013774, 6962.020000151966) context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 506.11, 'cpu_percent': 0.0} dataset_name=Logs_right_temp depth_points=per_well_calculated operation=resample_continuous_dataset original_rows=16303 resampled_rows=118298\n", "2025-07-29T03:32:39.297735Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 506.11, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_right_temp_converted_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:39.705752Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 523.66, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_right_temp_converted_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:39.748383Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 510.53, 'cpu_percent': 0.0} left_rows=506 operation=_merge_dataframes_left_aligned right_data_columns=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] right_rows=506\n", "2025-07-29T03:32:39.764475Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 510.53, 'cpu_percent': 0.0} merged_columns=72 merged_rows=506 operation=_merge_dataframes_left_aligned\n", "2025-07-29T03:32:39.788284Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 510.53, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=k_logs dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:39.803465Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 510.53, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('k_logs') result_dataset_type=WpDiscreteDataset result_shape=(506, 72)\n", "2025-07-29T03:32:39.830469Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.6, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] left_dataset=k_logs operation=merge_datasets_left_aligned right_curves=['DT2_P50', 'DPHIT_NMR'] right_dataset=OBMIQ_Pred\n", "2025-07-29T03:32:39.859876Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.6, 'cpu_percent': 0.0} curve_count=7 has_query=False operation=extract_curves source_dataset=k_logs target_dataset=k_logs_left_temp\n", "2025-07-29T03:32:39.881831Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.6, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'WELL_NO', 'PHI_T2_DIST', 'K_LABEL', 'MD', 'K_LABEL_TYPE', 'PZI'] operation=extract_metadata output_curve_count=9 output_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'WELL_NO', 'PHI_T2_DIST', 'K_LABEL', 'MD', 'K_LABEL_TYPE', 'PZI']\n", "2025-07-29T03:32:39.922944Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_k_logs_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:39.936525Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T03:32:39.965982Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=k_logs_left_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-07-29T03:32:40.011915Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=k_logs_left_temp dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:40.030635Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=72 source_rows=506 target_rows=506\n", "2025-07-29T03:32:40.053511Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} curve_count=2 has_query=False operation=extract_curves source_dataset=OBMIQ_Pred target_dataset=OBMIQ_Pred_right_temp\n", "2025-07-29T03:32:40.070152Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['DT2_P50', 'DPHIT_NMR', 'MD', 'WELL_NO'] operation=extract_metadata output_curve_count=4 output_curves=['DT2_P50', 'DPHIT_NMR', 'MD', 'WELL_NO']\n", "2025-07-29T03:32:40.108415Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.8, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:40.135532Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.8, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:32:40.164880Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.8, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=OBMIQ_Pred_right_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-29T03:32:40.199184Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.8, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:40.218267Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.8, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=4 source_rows=4502 target_rows=4502\n", "2025-07-29T03:32:40.249855Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.71, 'cpu_percent': 0.0} left_dataset_type=WpDiscreteDataset left_sampling_interval=0.010000000000218279 operation=merge_datasets_left_aligned\n", "2025-07-29T03:32:40.271931Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.71, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['DT2_P50', 'DPHIT_NMR', 'MD', 'WELL_NO']\n", "2025-07-29T03:32:40.282779Z [info     ] 开始连续型数据集深度重采样                  [logwp.models.datasets.internal.continuous_resample] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.71, 'cpu_percent': 0.0} custom_depth_range=False dataset_name=OBMIQ_Pred_right_temp interpolation_method=nearest new_sampling_interval=0.010000000000218279 operation=resample_continuous_dataset original_sampling_interval=0.15239999999994325 out_of_range_fill_value=nan\n", "2025-07-29T03:32:40.343631Z [info     ] 连续型数据集深度重采样完成                  [logwp.models.datasets.internal.continuous_resample] actual_depth_range=(6310.570000137746, 6733.490000146978) context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.84, 'cpu_percent': 0.0} dataset_name=OBMIQ_Pred_right_temp depth_points=per_well_calculated operation=resample_continuous_dataset original_rows=4502 resampled_rows=70248\n", "2025-07-29T03:32:40.364685Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.84, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:40.436445Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.18, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:40.445331Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} left_rows=506 operation=_merge_dataframes_left_aligned right_data_columns=['DT2_P50', 'DPHIT_NMR'] right_rows=506\n", "2025-07-29T03:32:40.474418Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} merged_columns=74 merged_rows=506 operation=_merge_dataframes_left_aligned\n", "2025-07-29T03:32:40.496402Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=k_logs_obmiq dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:40.528933Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.19, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64', 'K_LABEL', 'K_LABEL_TYPE', 'PZI', 'DT2_P50', 'DPHIT_NMR'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('k_logs_obmiq') result_dataset_type=WpDiscreteDataset result_shape=(506, 74)\n", "✅ 数据集合并完成\n", "   数据形状: (506, 74)\n", "\n", "💾 保存SCAPE训练数据集...\n", "2025-07-29T03:32:40.561932Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.11, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train.wp.xlsx project_name=WpIdentifier('swift_pso_train') save_head_info=True save_well_map=True\n", "2025-07-29T03:32:40.581263Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.11, 'cpu_percent': 0.0} curve_count=11 dataset_name=WpIdentifier('k_logs_obmiq') dataset_type=Point df_shape=(506, 74)\n", "2025-07-29T03:32:40.841345Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.5, 'cpu_percent': 0.0} dataset_name=WpIdentifier('k_logs_obmiq') processing_time=0.26\n", "2025-07-29T03:32:40.861559Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.5, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-29T03:32:40.887570Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.5, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:40.897809Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.5, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-29T03:32:42.088385Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.5, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:42.096116Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.5, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:42.788607Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.52, 'cpu_percent': 0.0} file_path=scape_swift_pso_train.wp.xlsx processing_time=2.227 project_name=WpIdentifier('swift_pso_train')\n", "2025-07-29T03:32:42.817657Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.52, 'cpu_percent': 0.0} format=markdown project_name=swift_pso_train\n", "2025-07-29T03:32:43.680631Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.89, 'cpu_percent': 0.0} file_size=35680 format=markdown output_path=scape_swift_pso_train_report.md\n", "2025-07-29T03:32:43.711164Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=9 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.89, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-29T03:32:43.727961Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.89, 'cpu_percent': 0.0} curve_count=9 dropna_how=any new_dataset=swift_pso_train_cleaned operation=dropna_dataset source_dataset=swift_pso_train\n", "2025-07-29T03:32:43.751968Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=409 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.9, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=506 removed_rows=97\n", "2025-07-29T03:32:43.773010Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.9, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:32:43.808958Z [info     ] 源数据集类型为 WpDiscreteDataset, 新数据集类型保持不变。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.9, 'cpu_percent': 0.0} operation=dropna_dataset\n", "✅ 合并后的数据集去空值处理并完成\n", "   数据形状: (409, 74)\n", "   数据集类型: WpDiscreteDataset\n", "2025-07-29T03:32:43.832816Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.9, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx project_name=WpIdentifier('Santos_SCAPE_train_cleaned') save_head_info=True save_well_map=True\n", "2025-07-29T03:32:43.867743Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.9, 'cpu_percent': 0.0} curve_count=11 dataset_name=WpIdentifier('swift_pso_train_cleaned') dataset_type=Point df_shape=(409, 74)\n", "2025-07-29T03:32:44.011475Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned') processing_time=0.144\n", "2025-07-29T03:32:44.017251Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-29T03:32:44.066230Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:44.077373Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-29T03:32:45.100972Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.91, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:45.122019Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.91, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:45.640018Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.91, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=1.807 project_name=WpIdentifier('Santos_SCAPE_train_cleaned')\n", "2025-07-29T03:32:45.677950Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.91, 'cpu_percent': 0.0} format=markdown project_name=Santos_SCAPE_train_cleaned\n", "2025-07-29T03:32:46.338722Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.89, 'cpu_percent': 0.0} file_size=29956 format=markdown output_path=scape_swift_pso_train_cleaned_report.md\n", "\n", "🎉 SWIFT-PSO 训练数据集生成完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.models.well_project import WpWellProject\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "\n", "    print(\"🔧 开始合并K_Label和Logs数据集...\")\n", "\n", "    try:\n", "        # 使用左对齐合并方式\n", "        print(\"\\n📍 执行左对齐合并...\")\n", "        la1_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"K_Label\",\n", "            left_curves=k_label_curves,\n", "            right_dataset=\"Logs\",\n", "            right_curves=logs_curves,\n", "            new_dataset_name=\"k_logs\"\n", "        )\n", "        project.add_dataset(\"k_logs\", la1_ds)\n", "\n", "        la2_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"k_logs\",\n", "            left_curves= logs_curves + k_label_curves,\n", "            right_dataset=\"OBMIQ_Pred\",\n", "            right_curves=obmiq_pred_curves,\n", "            new_dataset_name=\"k_logs_obmiq\"\n", "        )\n", "        project.add_dataset(\"swift_pso_train\", la2_ds)\n", "\n", "        print(f\"✅ 数据集合并完成\")\n", "        print(f\"   数据形状: {la2_ds.df.shape}\")\n", "\n", "        # =============================\n", "\n", "        # 创建临时项目并保存\n", "        print(\"\\n💾 保存SCAPE训练数据集...\")\n", "        temp_project = WpWellProject(name=\"swift_pso_train\")\n", "        temp_project.add_dataset(\"swift_pso_train\", la2_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_swift_pso_train.wp.xlsx\"\n", "        writer.write(temp_project, output_path, apply_formatting=True)\n", "        report_path = temp_project.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"scape_swift_pso_train_report.md\"\n", "        )\n", "\n", "        # =============================\n", "\n", "        swift_pso_train_cleaned_ds = project.dropna_dataset(\n", "            source_dataset_name=\"swift_pso_train\",\n", "            curve_names=[],\n", "            new_dataset_name=\"swift_pso_train_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        print(f\"✅ 合并后的数据集去空值处理并完成\")\n", "        print(f\"   数据形状: {swift_pso_train_cleaned_ds.df.shape}\")\n", "        print(f\"   数据集类型: {type(swift_pso_train_cleaned_ds).__name__}\")\n", "\n", "        # =============================\n", "\n", "        temp_project2 = WpWellProject(name=\"Santos_SCAPE_train_cleaned\")\n", "        temp_project2.add_dataset(\"swift_pso_train_cleaned\", swift_pso_train_cleaned_ds)\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_swift_pso_train_cleaned.wp.xlsx\"\n", "        writer.write(temp_project2, output_path, apply_formatting=True)\n", "        report_path = temp_project2.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"scape_swift_pso_train_cleaned_report.md\"\n", "        )\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 数据集合并/清理失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 SWIFT-PSO 训练数据集生成完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过SWIFT-PSO训练数据集生成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 生成预测数据集"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:32:46.432192Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.89, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST'] left_dataset=Logs operation=merge_datasets_left_aligned right_curves=['DT2_P50', 'DPHIT_NMR'] right_dataset=OBMIQ_Pred\n", "2025-07-29T03:32:46.454925Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.89, 'cpu_percent': 0.0} curve_count=4 has_query=False operation=extract_curves source_dataset=Logs target_dataset=Logs_left_temp\n", "2025-07-29T03:32:46.487678Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.89, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=6 input_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'WELL_NO', 'PHI_T2_DIST', 'MD'] operation=extract_metadata output_curve_count=6 output_curves=['T2LM', 'T2_P50', 'PHIT_NMR', 'WELL_NO', 'PHI_T2_DIST', 'MD']\n", "2025-07-29T03:32:46.580153Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 471.19, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_Logs_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:46.609418Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 472.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-29T03:32:46.635989Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 472.27, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=Logs_left_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-29T03:32:46.660391Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 472.27, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:46.692084Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 472.27, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64'] operation=extract_curves selected_columns=69 source_rows=16303 target_rows=16303\n", "2025-07-29T03:32:46.712205Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.67, 'cpu_percent': 0.0} curve_count=2 has_query=False operation=extract_curves source_dataset=OBMIQ_Pred target_dataset=OBMIQ_Pred_right_temp\n", "2025-07-29T03:32:46.721456Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.67, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['DT2_P50', 'DPHIT_NMR', 'MD', 'WELL_NO'] operation=extract_metadata output_curve_count=4 output_curves=['DT2_P50', 'DPHIT_NMR', 'MD', 'WELL_NO']\n", "2025-07-29T03:32:46.777766Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.05, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:46.804435Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.06, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:32:46.820533Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.06, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=OBMIQ_Pred_right_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-29T03:32:46.836511Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.06, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:46.852632Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.06, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=4 source_rows=4502 target_rows=4502\n", "2025-07-29T03:32:46.868336Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.96, 'cpu_percent': 0.0} left_dataset_type=WpContinuousDataset left_sampling_interval=0.03809999999975844 operation=merge_datasets_left_aligned\n", "2025-07-29T03:32:46.887843Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.97, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['DT2_P50', 'DPHIT_NMR', 'MD', 'WELL_NO']\n", "2025-07-29T03:32:46.925701Z [info     ] 开始连续型数据集深度重采样                  [logwp.models.datasets.internal.continuous_resample] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.97, 'cpu_percent': 0.0} custom_depth_range=False dataset_name=OBMIQ_Pred_right_temp interpolation_method=nearest new_sampling_interval=0.03809999999975844 operation=resample_continuous_dataset original_sampling_interval=0.15239999999994325 out_of_range_fill_value=nan\n", "2025-07-29T03:32:46.981179Z [info     ] 连续型数据集深度重采样完成                  [logwp.models.datasets.internal.continuous_resample] actual_depth_range=(6310.57919995999, 6733.527299957308) context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.11, 'cpu_percent': 0.0} dataset_name=OBMIQ_Pred_right_temp depth_points=per_well_calculated operation=resample_continuous_dataset original_rows=4502 resampled_rows=18442\n", "2025-07-29T03:32:47.004670Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.11, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:47.079704Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 444.45, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred_right_temp_converted_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:47.093272Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 444.19, 'cpu_percent': 0.0} left_rows=16303 operation=_merge_dataframes_left_aligned right_data_columns=['DT2_P50', 'DPHIT_NMR'] right_rows=16303\n", "2025-07-29T03:32:47.126820Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 453.15, 'cpu_percent': 0.0} merged_columns=71 merged_rows=16303 operation=_merge_dataframes_left_aligned\n", "2025-07-29T03:32:47.142739Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 453.15, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:47.158825Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 453.15, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'T2LM', 'T2_P50', 'PHIT_NMR', 'PHI_T2_DIST_1', 'PHI_T2_DIST_2', 'PHI_T2_DIST_3', 'PHI_T2_DIST_4', 'PHI_T2_DIST_5', 'PHI_T2_DIST_6', 'PHI_T2_DIST_7', 'PHI_T2_DIST_8', 'PHI_T2_DIST_9', 'PHI_T2_DIST_10', 'PHI_T2_DIST_11', 'PHI_T2_DIST_12', 'PHI_T2_DIST_13', 'PHI_T2_DIST_14', 'PHI_T2_DIST_15', 'PHI_T2_DIST_16', 'PHI_T2_DIST_17', 'PHI_T2_DIST_18', 'PHI_T2_DIST_19', 'PHI_T2_DIST_20', 'PHI_T2_DIST_21', 'PHI_T2_DIST_22', 'PHI_T2_DIST_23', 'PHI_T2_DIST_24', 'PHI_T2_DIST_25', 'PHI_T2_DIST_26', 'PHI_T2_DIST_27', 'PHI_T2_DIST_28', 'PHI_T2_DIST_29', 'PHI_T2_DIST_30', 'PHI_T2_DIST_31', 'PHI_T2_DIST_32', 'PHI_T2_DIST_33', 'PHI_T2_DIST_34', 'PHI_T2_DIST_35', 'PHI_T2_DIST_36', 'PHI_T2_DIST_37', 'PHI_T2_DIST_38', 'PHI_T2_DIST_39', 'PHI_T2_DIST_40', 'PHI_T2_DIST_41', 'PHI_T2_DIST_42', 'PHI_T2_DIST_43', 'PHI_T2_DIST_44', 'PHI_T2_DIST_45', 'PHI_T2_DIST_46', 'PHI_T2_DIST_47', 'PHI_T2_DIST_48', 'PHI_T2_DIST_49', 'PHI_T2_DIST_50', 'PHI_T2_DIST_51', 'PHI_T2_DIST_52', 'PHI_T2_DIST_53', 'PHI_T2_DIST_54', 'PHI_T2_DIST_55', 'PHI_T2_DIST_56', 'PHI_T2_DIST_57', 'PHI_T2_DIST_58', 'PHI_T2_DIST_59', 'PHI_T2_DIST_60', 'PHI_T2_DIST_61', 'PHI_T2_DIST_62', 'PHI_T2_DIST_63', 'PHI_T2_DIST_64', 'DT2_P50', 'DPHIT_NMR'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('swift_pso_apply') result_dataset_type=WpContinuousDataset result_shape=(16303, 71)\n", "2025-07-29T03:32:47.190241Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=6 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.32, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-29T03:32:47.206047Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.32, 'cpu_percent': 0.0} curve_count=6 dropna_how=any new_dataset=swift_pso_apply_cleaned operation=dropna_dataset source_dataset=swift_pso_apply\n", "2025-07-29T03:32:47.238233Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=4690 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.83, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=11613\n", "2025-07-29T03:32:47.271231Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.84, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:32:47.289761Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.84, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:32:47.311291Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.84, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-29T03:32:47.324609Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.84, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx project_name=WpIdentifier('swift_pso_apply_cleaned') save_head_info=True save_well_map=True\n", "2025-07-29T03:32:47.353642Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.84, 'cpu_percent': 0.0} curve_count=8 dataset_name=WpIdentifier('swift_pso_apply_cleaned') dataset_type=Continuous df_shape=(4690, 71)\n", "2025-07-29T03:32:48.778077Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.88, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned') processing_time=1.424\n", "2025-07-29T03:32:48.809782Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.39, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-29T03:32:48.857406Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.39, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:48.873419Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.4, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-29T03:32:58.515394Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 532.52, 'cpu_percent': 0.0}\n", "2025-07-29T03:32:58.529796Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 532.52, 'cpu_percent': 0.0}\n", "2025-07-29T03:33:04.745186Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.75, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=17.421 project_name=WpIdentifier('swift_pso_apply_cleaned')\n"]}], "source": ["ds_apply = project.merge_datasets_left_aligned(\n", "    left_dataset=\"Logs\",\n", "    left_curves=logs_curves,\n", "    right_dataset=\"OBMIQ_Pred\",\n", "    right_curves=obmiq_pred_curves,\n", "    new_dataset_name=\"swift_pso_apply\"\n", ")\n", "\n", "project.add_dataset(\"swift_pso_apply\",ds_apply)\n", "\n", "ds_apply_cleaned=project.dropna_dataset(\n", "    source_dataset_name=\"swift_pso_apply\",\n", "    curve_names=[],\n", "    new_dataset_name=\"swift_pso_apply_cleaned\",\n", "    dropna_how=\"any\"\n", ")\n", "temp_project = WpWellProject(name=\"swift_pso_apply_cleaned\")\n", "temp_project.add_dataset(\"swift_pso_apply_cleaned\", ds_apply_cleaned)\n", "\n", "writer = WpExcelWriter()\n", "output_path = \"scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "writer.write(temp_project, output_path, apply_formatting=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. PLT & Core_K"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:33:04.843989Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.75, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx project_name=WpIdentifier('santos_data_v2') save_head_info=True save_well_map=True\n", "2025-07-29T03:33:04.865489Z [info     ] 开始写入井头信息表单                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.75, 'cpu_percent': 0.0} total_attributes=4\n", "2025-07-29T03:33:04.881375Z [info     ] 井头信息表单写入完成                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.76, 'cpu_percent': 0.0} processing_time=0.016 total_attributes=4\n", "2025-07-29T03:33:04.897224Z [info     ] 开始写入井名映射表单                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.76, 'cpu_percent': 0.0} total_mappings=4\n", "2025-07-29T03:33:04.897224Z [info     ] 井名映射表单写入完成                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.76, 'cpu_percent': 0.0} processing_time=0.0 total_mappings=4\n", "2025-07-29T03:33:04.917675Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.76, 'cpu_percent': 0.0} curve_count=4 dataset_name=WpIdentifier('PLT') dataset_type=Interval df_shape=(15, 4)\n", "2025-07-29T03:33:04.933343Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT') processing_time=0.016\n", "2025-07-29T03:33:04.949406Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} dataset_count=1 head_info=True total_sheets=3 well_map=True\n", "2025-07-29T03:33:04.965392Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0}\n", "2025-07-29T03:33:05.000523Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} workbook_sheets=3\n", "2025-07-29T03:33:05.026928Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0}\n", "2025-07-29T03:33:05.026928Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0}\n", "2025-07-29T03:33:05.109335Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx processing_time=0.265 project_name=WpIdentifier('santos_data_v2')\n", "2025-07-29T03:33:05.146788Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val.wp.xlsx project_name=WpIdentifier('santos_data_v2') save_head_info=True save_well_map=True\n", "2025-07-29T03:33:05.173127Z [info     ] 开始写入井头信息表单                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} total_attributes=4\n", "2025-07-29T03:33:05.188780Z [info     ] 井头信息表单写入完成                     [logwp.io.wp_excel.internal.head_info_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} processing_time=0.016 total_attributes=4\n", "2025-07-29T03:33:05.220538Z [info     ] 开始写入井名映射表单                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} total_mappings=4\n", "2025-07-29T03:33:05.241329Z [info     ] 井名映射表单写入完成                     [logwp.io.wp_excel.internal.well_map_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} processing_time=0.016 total_mappings=4\n", "2025-07-29T03:33:05.252185Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} curve_count=9 dataset_name=WpIdentifier('K_Val') dataset_type=Point df_shape=(45, 9)\n", "2025-07-29T03:33:05.283726Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val') processing_time=0.032\n", "2025-07-29T03:33:05.283726Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} dataset_count=1 head_info=True total_sheets=3 well_map=True\n", "2025-07-29T03:33:05.315848Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0}\n", "2025-07-29T03:33:05.323871Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} workbook_sheets=3\n", "2025-07-29T03:33:05.389648Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0}\n", "2025-07-29T03:33:05.412232Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0}\n", "2025-07-29T03:33:05.466869Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 534.77, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx processing_time=0.32 project_name=WpIdentifier('santos_data_v2')\n", "\n", "🎉 PLT/K_Val写入完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.models.well_project import WpWellProject\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "\n", "    try:\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_plt_val.wp.xlsx\"\n", "        writer.write(project, output_path,dataset_names=[\"PLT\"],apply_formatting=True)\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_core_k_val.wp.xlsx\"\n", "        writer.write(project, output_path,dataset_names=[\"K_Val\"],apply_formatting=True)\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ PLT/K_Val写入失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 PLT/K_Val写入完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过PLT/K_Val数据集生成\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}