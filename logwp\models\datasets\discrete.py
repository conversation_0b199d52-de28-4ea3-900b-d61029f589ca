from __future__ import annotations

"""logwp.models.datasets.discrete - 离散型数据集

WpDiscreteDataset处理离散型测井数据，支持不等间距采样的点数据。

Architecture
------------
层次/依赖: datasets层具体实现，继承WpDepthIndexedDatabaseBase
设计原则: 离散数据特化、点数据索引、稀疏数据优化
性能特征: 内存优化、快速查找、GPU支持

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- SC-1: 算法正确性，包含物理约束验证

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§3.2.3 - WpDiscreteDataset设计
- 《SCAPE_WFS_WP文件规范.md》A.3.2 - 点型数据集规范
"""

from typing import Any
import pandas as pd
import numpy as np
import copy
from datetime import datetime

from logwp.models.constants import WpDsType, WpStandardColumn, WpApiKeys, WpStatisticsKeys, WpDepthRole
from logwp.models.exceptions import (
    WpValidationError, WpDataError,
    ErrorContext
)
from logwp.models.types import WpDataDict, WpDepthValue, WpIdentifier, WpDatasetName
from logwp.infra import get_logger
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata
from logwp.models.datasets.base import WpDepthIndexedDatasetBase

logger = get_logger(__name__)


class WpDiscreteDataset(WpDepthIndexedDatasetBase):
    """离散型数据集。

    处理离散型测井数据，如岩心分析、MDT测试等不等间距采样的点数据。
    支持稀疏数据索引、点数据验证和数据完整性检查。

    DataFrame约定：
    - MD（测量深度）列作为普通列存储，不作为索引
    - 用户可调用 create_depth_index() 方法创建深度索引

    **WFS规范映射说明**：
    - logwp数据模型：WpDiscreteDataset（离散型，强调数学特性）
    - WFS文件格式：Point类型（点型，WFS v1.0规范术语）
    - 枚举值映射：dataset_type返回WpDsType.POINT
    - 设计原理：保持格式无关的业务模型命名，通过枚举值与具体格式规范对应

    Architecture
    ------------
    层次/依赖: datasets层离散型数据集，继承WpDepthIndexedDatabaseBase
    设计原则: 离散数据特化、点数据索引、稀疏优化
    性能特征: 内存优化、快速查找、GPU支持

    Discrete Dataset Requirements（基于WFS A.3.2节Point类型）：
    - **深度列**: 必须包含MD（测量深度）或TVD（垂直深度）列
    - **不等间距**: 深度采样间隔不固定，支持稀疏采样
    - **点数据**: 每行代表一个测量点，通常数据量较小
    - **数据完整性**: 基础数据结构和格式验证

    Examples:
        >>> df = pd.DataFrame({
        ...     "MD": [2500.0, 2505.5, 2512.3],
        ...     "PERM_CORE": [12.5, 8.9, 15.2],
        ...     "PHIT_CORE": [0.15, 0.12, 0.18],
        ...     "WELL": ["C-1", "C-1", "C-1"]
        ... })
        >>> dataset = WpDiscreteDataset(name="K_Label", df=df)
        >>> assert dataset.validate()
        >>> assert dataset.dataset_type == WpDsType.POINT  # 对应WFS Point类型
        >>>
        >>> # 深度范围查询
        >>> subset = dataset.filter_depth_range(2500.0, 2510.0)
        >>> assert len(subset.df) == 2

    References:
        《SCAPE_WFS_WP文件规范.md》A.3.2 - 点型数据集规范（WFS Point类型）
    """

    @property
    def dataset_type(self) -> WpDsType:
        """数据集类型。

        Returns:
            WpDsType: 离散型数据集类型（对应WFS规范中的Point类型）

        Note:
            虽然类名为Discrete，但返回WpDsType.POINT以符合WFS v1.0规范。
            这体现了logwp格式无关设计：业务模型使用通用术语，
            通过枚举值与具体格式规范建立映射关系。
        """
        return WpDsType.POINT


    @classmethod
    def create_empty(cls, name: str) -> WpDiscreteDataset:
        """创建空的离散型数据集（io层调用）。

        Args:
            name: 数据集名称

        Returns:
            WpDiscreteDataset: 空的离散型数据集实例

        Examples:
            >>> dataset = WpDiscreteDataset.create_empty("K_Label")
            >>> assert dataset.dataset_type == WpDsType.POINT
            >>> assert dataset.df.empty

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.2 - 创建空数据集
        """
        return cls(name=WpIdentifier(name))

    @classmethod
    def create_with_data(
        cls,
        name: WpDatasetName | WpIdentifier,
        df: pd.DataFrame,
        curve_metadata: CurveMetadata,
        *,
        depth_sampling_rate: float = 0
    ) -> WpDiscreteDataset:
        """创建离散型数据集并附加数据。

        工厂方法，创建新的离散型数据集实例并同时附加DataFrame和曲线元数据。

        Args:
            name: 数据集名称
            df: DataFrame数据体
            curve_metadata: 曲线元数据
            depth_sampling_rate: 深度采样间隔，离散型数据集忽略此参数

        Returns:
            WpDiscreteDataset: 创建的离散型数据集实例

        Examples:
            >>> df = pd.DataFrame({'WELL': ['C-1'], 'MD': [2500.0], 'FACIES': ['SAND']})
            >>> metadata = CurveMetadata()
            >>> # ... 配置metadata ...
            >>> dataset = WpDiscreteDataset.create_with_data("facies_logs", df, metadata)
            >>> assert dataset.name == "facies_logs"
            >>> assert dataset.dataset_type == WpDsType.POINT

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.3 - 工厂方法模式
        """
        # 创建空数据集实例
        instance = cls.create_empty(name)

        # 附加数据和元数据
        instance.direct_attach_curves_data(df, curve_metadata)

        return instance

    def clone_dataset(self) -> WpDiscreteDataset:
        """克隆离散型数据集，返回新的数据集实例。

        创建当前数据集的深拷贝，包括：
        - 数据体（DataFrame）的完整拷贝
        - 曲线元数据的完整拷贝
        - 所有属性的拷贝（除时间戳外）

        Returns:
            WpDiscreteDataset: 克隆后的新离散型数据集实例

        Note:
            - 克隆后的数据集具有新的时间戳（created_at, modified_at）
            - 数据体和元数据完全独立，修改不会相互影响

        Examples:
            >>> original = dataset
            >>> cloned = dataset.clone_dataset()
            >>> assert cloned.name == original.name
            >>> assert cloned.df.equals(original.df)
            >>> assert cloned is not original  # 不同的对象
            >>> assert cloned.df is not original.df  # 不同的DataFrame
            >>> assert cloned.dataset_type == WpDsType.POINT
        """
        # 深拷贝DataFrame
        cloned_df = self.df.copy(deep=True)

        # 深拷贝曲线元数据
        cloned_curve_metadata = copy.deepcopy(self.curve_metadata)

        # 创建新的数据集实例，使用新的时间戳
        now = datetime.now()
        cloned = WpDiscreteDataset(
            name=self.name,  # WpIdentifier是不可变的，可以共享
            df=cloned_df,
            curve_metadata=cloned_curve_metadata,
            created_at=now,
            modified_at=now
        )

        return cloned

    # ------------------------------------------------------------
    # 深度参考曲线便捷服务方法实现
    # ------------------------------------------------------------

    def get_depth_reference_count(self) -> int:
        """获取离散型数据集深度参考曲线的条数。

        Returns:
            int: 深度参考曲线数量，离散型数据集固定返回1

        Examples:
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> assert discrete_dataset.get_depth_reference_count() == 1
        """
        return 1

    def get_single_depth_reference_curve(self) -> CurveBasicAttributes:
        """获取离散型数据集的单个深度曲线。

        Returns:
            CurveBasicAttributes: 深度参考曲线的基本属性

        Raises:
            WpDataError: 当没有找到深度参考曲线时抛出

        Note:
            - 利用CurveMetadata.get_depth_reference_curves()方法
            - WFS规范保证返回列表的第一个元素是离散型数据集的深度参考曲线

        Examples:
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> depth_curve = discrete_dataset.get_single_depth_reference_curve()
            >>> assert depth_curve.depth_role == WpDepthRole.SINGLE
        """
        depth_curve_names = self.curve_metadata.get_depth_reference_curves()

        if not depth_curve_names:
            raise WpDataError(
                "离散型数据集缺少深度参考曲线",
                context=ErrorContext(
                    operation="get_single_depth_reference_curve",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "available_curves": list(self.curve_metadata.curves.keys())
                    }
                )
            )

        # WFS规范保证第一个是离散型数据集的深度参考曲线
        depth_curve_name = depth_curve_names[0]
        return self.curve_metadata.get_curve(depth_curve_name)

    def get_interval_depth_reference_curves(self) -> tuple[CurveBasicAttributes, CurveBasicAttributes]:
        """离散型数据集不支持区间深度索引。

        Raises:
            WpDataError: 离散型数据集不支持此操作

        Note:
            - 离散型数据集只有单一深度索引
            - 应使用get_single_depth_reference_curve()方法
        """
        raise WpDataError(
            "离散型数据集不支持区间深度索引操作",
            context=ErrorContext(
                operation="get_interval_depth_reference_curves",
                dataset_name=str(self.name),
                additional_info={
                    "dataset_type": self.dataset_type.value,
                    "supported_operation": "get_single_depth_reference_curve",
                    "reason": "discrete_dataset_single_depth_only"
                }
            )
        )

    def get_wells_depth_ranges(self) -> dict[str, tuple[float, float]]:
        """获取每口井的深度范围。

        Returns:
            dict[str, tuple[float, float]]: {井名: (最小深度, 最大深度)}

        Note:
            - 基于深度参考曲线的最小值和最大值
            - 空数据集返回空字典
            - 按井名分组计算每口井的深度范围

        Examples:
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> wells_ranges = discrete_dataset.get_wells_depth_ranges()
            >>> # {'C-1': (2500.0, 2600.0), 'C-2': (2550.0, 2650.0)}
        """
        if self.df.empty:
            return {}

        try:
            depth_curve = self.get_single_depth_reference_curve()
            depth_column = depth_curve.dataframe_column_name

            if depth_column not in self.df.columns:
                return {}

            # 获取井名曲线
            well_curves = self.curve_metadata.get_well_identifier_curves()
            if not well_curves or well_curves[0] not in self.df.columns:
                # 没有井名曲线，视为单井数据
                depth_series = self.df[depth_column].dropna()
                if depth_series.empty:
                    return {}
                return {"Unknown": (float(depth_series.min()), float(depth_series.max()))}

            well_column = well_curves[0]
            wells_ranges = {}

            # 按井分组计算深度范围
            for well_name in self.df[well_column].dropna().unique():
                well_data = self.df[self.df[well_column] == well_name]
                well_depths = well_data[depth_column].dropna()

                if not well_depths.empty:
                    wells_ranges[str(well_name)] = (
                        float(well_depths.min()),
                        float(well_depths.max())
                    )

            return wells_ranges

        except WpDataError:
            # 没有深度曲线时返回空字典
            return {}

    def get_depth_range_for_well(self, well_name: str) -> tuple[float, float]:
        """获取指定井的深度范围。

        Args:
            well_name: 井名

        Returns:
            tuple[float, float]: (最小深度, 最大深度)

        Raises:
            WpDataError: 当指定井不存在时抛出

        Examples:
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> min_depth, max_depth = discrete_dataset.get_depth_range_for_well("C-1")
            >>> assert min_depth <= max_depth
        """
        wells_ranges = self.get_wells_depth_ranges()

        if well_name not in wells_ranges:
            available_wells = list(wells_ranges.keys())
            raise WpDataError(
                f"井 '{well_name}' 不存在于数据集中",
                context=ErrorContext(
                    operation="get_depth_range_for_well",
                    dataset_name=str(self.name),
                    additional_info={
                        "requested_well": well_name,
                        "available_wells": available_wells,
                        "total_wells": len(available_wells)
                    }
                )
            )

        return wells_ranges[well_name]

    def get_depth_reference_unit(self) -> str | None:
        """获取离散型数据集深度参考曲线的单位。

        Returns:
            str | None: 深度曲线的单位，如果没有单位则返回None

        Note:
            - 利用get_single_depth_reference_curve()获取深度曲线
            - 返回深度曲线的unit属性

        Examples:
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> unit = discrete_dataset.get_depth_reference_unit()
            >>> assert unit == "m"  # 或其他深度单位
        """
        try:
            depth_curve = self.get_single_depth_reference_curve()
            return depth_curve.unit
        except WpDataError:
            # 没有深度曲线时返回None
            return None

    def calculate_depth_sampling_rate(self) -> float:
        """动态计算离散型数据集的最小深度间隔。

        Returns:
            float: 计算得到的最小深度间隔

        Raises:
            WpDataError: 当数据集为空或无法计算最小间隔时抛出

        Note:
            - 动态计算深度曲线间隔的最小值
            - 这代表数据集中最密集的采样间隔
            - 适用于不等间距采样的点数据

        Examples:
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> min_interval = discrete_dataset.calculate_depth_sampling_rate()
            >>> assert min_interval > 0  # 最小深度间隔
        """
        if self.df.empty:
            raise WpDataError(
                "空数据集无法计算最小深度间隔",
                context=ErrorContext(
                    operation="calculate_depth_sampling_rate",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "reason": "empty_dataset"
                    }
                )
            )

        try:
            depth_curve = self.get_single_depth_reference_curve()
            depth_column = depth_curve.dataframe_column_name

            if depth_column not in self.df.columns:
                raise WpDataError(
                    "深度曲线列不存在于DataFrame中",
                    context=ErrorContext(
                        operation="calculate_depth_sampling_rate",
                        dataset_name=str(self.name),
                        column_name=depth_column,
                        additional_info={
                            "available_columns": list(self.df.columns),
                            "expected_column": depth_column
                        }
                    )
                )

            depth_series = self.df[depth_column].dropna().sort_values()

            if len(depth_series) < 2:
                raise WpDataError(
                    "深度数据点不足，无法计算最小间隔",
                    context=ErrorContext(
                        operation="calculate_depth_sampling_rate",
                        dataset_name=str(self.name),
                        additional_info={
                            "valid_depth_points": len(depth_series),
                            "minimum_required": 2
                        }
                    )
                )

            # 计算相邻点之间的间隔
            intervals = depth_series.diff().dropna()

            # 过滤掉非正间隔（可能的数据错误）
            positive_intervals = intervals[intervals > 0]

            if positive_intervals.empty:
                raise WpDataError(
                    "未找到有效的正深度间隔",
                    context=ErrorContext(
                        operation="calculate_depth_sampling_rate",
                        dataset_name=str(self.name),
                        additional_info={
                            "total_intervals": len(intervals),
                            "positive_intervals": len(positive_intervals)
                        }
                    )
                )

            # 对于离散型数据集，返回最小间隔
            min_interval = float(positive_intervals.min())

            return min_interval

        except WpDataError:
            # 重新抛出WpDataError
            raise
        except Exception as e:
            raise WpDataError(
                "计算最小深度间隔时发生错误",
                context=ErrorContext(
                    operation="calculate_depth_sampling_rate",
                    dataset_name=str(self.name),
                    additional_info={
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    }
                )
            ) from e

    def to_continuous_dataset(
        self,
        sampling_interval: float,
        *,
        depth_range: tuple[float, float] | None = None,
        interpolation_method: str = "nearest",
        out_of_range_fill_value: Any = np.nan,
        new_dataset_name: str | None = None
    ) -> WpContinuousDataset:
        """转换为连续型数据集。

        将当前离散型数据集重采样为连续型数据集，支持多种插值方法和自定义深度范围。

        Args:
            sampling_interval: 深度采样间隔（必须 > 0）
            depth_range: 输出深度范围，None表示使用原数据集范围
            interpolation_method: 默认插值方法，可被曲线属性覆盖，默认"nearest"
            out_of_range_fill_value: 超出原数据集范围的填充值
            new_dataset_name: 新数据集名称，None表示自动生成

        Returns:
            WpContinuousDataset: 转换后的连续型数据集

        Raises:
            WpValidationError: 当参数验证失败时抛出
            WpDataError: 当数据集状态异常时抛出

        Note:
            - 转换后的深度曲线名称为WpStandardColumn.DEPTH
            - 保留原有的井名曲线，使用WpStandardColumn.WELL_NAME
            - 使用pandas插值方法进行数据重采样
            - 深度范围会按采样间隔对齐
            - 插值方法优先级：强制方法 > 数据类型约束 > 用户指定方法
            - 至少需要2个数据点进行插值

        Examples:
            >>> # 基本转换
            >>> continuous_dataset = discrete_dataset.to_continuous_dataset(
            ...     sampling_interval=0.5
            ... )
            >>> assert continuous_dataset.dataset_type == WpDsType.CONTINUOUS
            >>> assert "depth" in continuous_dataset.df.columns
            >>> assert "well_name" in continuous_dataset.df.columns

            >>> # 线性插值转换
            >>> linear_dataset = discrete_dataset.to_continuous_dataset(
            ...     sampling_interval=1.0,
            ...     interpolation_method="linear"
            ... )

            >>> # 自定义深度范围转换
            >>> extended_dataset = discrete_dataset.to_continuous_dataset(
            ...     sampling_interval=0.25,
            ...     depth_range=(2490.0, 2530.0),  # 扩展范围
            ...     interpolation_method="spline",
            ...     out_of_range_fill_value=-999,  # 扩展区域填充-999
            ...     new_dataset_name="extended_core_data"
            ... )

        References:
            《SCAPE_DDS_logwp_离散转连续数据集.md》§2.2 - WpDiscreteDataset API方法
        """
        # 导入放在方法内部避免循环导入
        from logwp.models.datasets.continuous import WpContinuousDataset
        from logwp.models.datasets.internal.discrete_to_continuous import (
            convert_discrete_to_continuous,
        )

        logger.info(
            "开始离散型数据集转连续型数据集",
            operation="to_continuous_dataset",
            dataset_name=str(self.name),
            sampling_interval=sampling_interval,
            custom_depth_range=depth_range is not None,
            interpolation_method=interpolation_method,
            custom_dataset_name=new_dataset_name is not None
        )

        # 调用服务层进行转换
        converted_df, converted_metadata, actual_depth_range = convert_discrete_to_continuous(
            self,
            sampling_interval,
            depth_range=depth_range,
            interpolation_method=interpolation_method,
            out_of_range_fill_value=out_of_range_fill_value
        )

        # 确定新数据集名称
        if new_dataset_name is None:
            new_dataset_name = f"{self.name}_continuous"

        # 创建连续型数据集
        continuous_dataset = WpContinuousDataset.create_with_data(
            name=new_dataset_name,
            df=converted_df,
            curve_metadata=converted_metadata
        )

        # 设置深度采样间隔
        continuous_dataset.depth_sampling_rate = sampling_interval

        logger.info(
            "离散型数据集转连续型数据集完成",
            operation="to_continuous_dataset",
            original_dataset=str(self.name),
            new_dataset=str(continuous_dataset.name),
            original_rows=len(self.df),
            converted_rows=len(converted_df),
            actual_depth_range=actual_depth_range,
            sampling_interval=sampling_interval
        )

        return continuous_dataset
