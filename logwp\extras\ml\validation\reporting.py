"""
提供模型评估报告生成功能。
"""
from __future__ import annotations

from typing import Dict, Any
import numpy as np
import pandas as pd

try:
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    # 定义虚拟函数以允许在没有sklearn的情况下导入
    def mean_absolute_error(*args, **kwargs): raise ImportError("scikit-learn is required.")
    def mean_squared_error(*args, **kwargs): raise ImportError("scikit-learn is required.")
    def r2_score(*args, **kwargs): raise ImportError("scikit-learn is required.")


def regression_report(
    y_true: np.ndarray | pd.Series,
    y_pred: np.ndarray | pd.Series,
    groups: pd.Series | None = None
) -> Dict[str, Any]:
    """
    生成一个详细的回归模型评估报告。

    此函数计算整体的回归指标，并且如果提供了`groups`（如井名），
    还会按组计算指标，这对于评估模型在不同井上的表现非常有用。

    Args:
        y_true (np.ndarray | pd.Series): 真实的标签值。
        y_pred (np.ndarray | pd.Series): 模型预测的标签值。
        groups (pd.Series | None, optional): 用于分组的Series，例如井名。
                                              索引必须与y_true和y_pred对齐。
                                              默认为 None。

    Returns:
        Dict[str, Any]: 一个包含评估指标的字典。结构如下：
        {
            'overall': {
                'mae': float, 'mse': float, 'rmse': float, 'r2': float, 'count': int
            },
            'by_group': {
                'group_name_1': {'mae': ..., 'count': ...},
                'group_name_2': {'mae': ..., 'count': ...},
                ...
            }
        }
    """
    if not SKLEARN_AVAILABLE:
        raise ImportError("此功能需要 'scikit-learn' 库。请运行 'pip install scikit-learn' 进行安装。")

    y_true_arr, y_pred_arr = np.asarray(y_true), np.asarray(y_pred)
    if y_true_arr.shape != y_pred_arr.shape:
        raise ValueError(f"y_true 和 y_pred 的形状必须一致，但收到了 {y_true_arr.shape} 和 {y_pred_arr.shape}")

    report: Dict[str, Any] = {}

    # 1. 计算整体指标
    report['overall'] = {
        'mae': float(mean_absolute_error(y_true_arr, y_pred_arr)),
        'mse': float(mean_squared_error(y_true_arr, y_pred_arr)),
        'rmse': float(np.sqrt(mean_squared_error(y_true_arr, y_pred_arr))),
        'r2': float(r2_score(y_true_arr, y_pred_arr)),
        'count': len(y_true_arr)
    }

    # 2. 按组计算指标
    if groups is not None:
        if not isinstance(groups, pd.Series):
            raise TypeError("'groups' 参数必须是 pandas.Series。")
        if len(groups) != len(y_true_arr):
            raise ValueError(f"groups 的长度 ({len(groups)}) 必须与 y_true 的长度 ({len(y_true_arr)}) 一致。")

        df = pd.DataFrame({'y_true': y_true_arr, 'y_pred': y_pred_arr, 'group': groups})
        by_group_metrics = {}
        for group_name, group_df in df.groupby('group'):
            g_true, g_pred = group_df['y_true'], group_df['y_pred']
            r2 = r2_score(g_true, g_pred) if len(g_true) > 1 else np.nan
            by_group_metrics[str(group_name)] = {
                'mae': float(mean_absolute_error(g_true, g_pred)),
                'mse': float(mean_squared_error(g_true, g_pred)),
                'rmse': float(np.sqrt(mean_squared_error(g_true, g_pred))),
                'r2': float(r2),
                'count': len(g_true)
            }
        report['by_group'] = by_group_metrics

    return report
