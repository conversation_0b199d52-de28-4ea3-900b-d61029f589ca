"""
提供经典的特征重要性可视化功能。
"""
from __future__ import annotations

from typing import Any, Optional, TYPE_CHECKING
import pandas as pd
import numpy as np

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

if TYPE_CHECKING:
    from matplotlib.axes import Axes


def plot_feature_importance(
    model_or_shap_values: Any,
    X: pd.DataFrame,
    *,
    method: str = 'auto',
    top_n: Optional[int] = 20,
    ax: "Axes | None" = None,
    **kwargs: Any
) -> "Axes":
    """
    绘制一个条形图来显示特征的重要性。

    此函数可以从两种来源生成特征重要性：
    1.  **经典方法**: 直接使用树模型（如RandomForest, XGBoost）的 `feature_importances_` 属性。
    2.  **SHAP方法**: 通过计算每个特征的平均绝对SHAP值。

    Args:
        model_or_shap_values (Any): 可以是一个已训练的机器学习模型对象，
                                    也可以是一个SHAP值数组 (来自 `calculate_shap_values`)。
        X (pd.DataFrame): 用于解释的原始特征矩阵，用于获取特征名称。
        method (str, optional): 使用的方法。可选值为 'auto', 'classic', 'shap'。
                                'auto'会根据输入类型自动选择。默认为 'auto'。
        top_n (int | None, optional): 要显示的最重要特征的数量。如果为None，则显示所有特征。
                                      默认为 20。
        ax (matplotlib.axes.Axes | None, optional): 要在其上绘图的现有matplotlib轴对象。
                                                    如果为None，将创建一个新的图和轴。
                                                    默认为 None。
        **kwargs: 其他传递给 `seaborn.barplot` 的关键字参数。

    Returns:
        matplotlib.axes.Axes: 包含了条形图的matplotlib轴对象。
    """
    if not MATPLOTLIB_AVAILABLE:
        raise ImportError("此功能需要 'matplotlib' 和 'seaborn' 库。请运行 'pip install matplotlib seaborn' 进行安装。")

    if not isinstance(X, pd.DataFrame):
        raise TypeError("输入数据 X 必须是 pandas DataFrame。")

    importance_df = None

    # --- 1. 计算特征重要性 ---
    if method == 'auto':
        if hasattr(model_or_shap_values, 'feature_importances_'):
            method_used = 'classic'
        elif isinstance(model_or_shap_values, np.ndarray):
            method_used = 'shap'
        else:
            raise TypeError("在 'auto' 模式下，输入必须是带有 'feature_importances_' 属性的模型或一个NumPy SHAP值数组。")
    else:
        method_used = method

    if method_used == 'classic':
        if not hasattr(model_or_shap_values, 'feature_importances_'):
            raise TypeError("当 method='classic' 时，输入必须是一个带有 'feature_importances_' 属性的模型。")
        importances = model_or_shap_values.feature_importances_
        importance_df = pd.DataFrame({'Feature': X.columns, 'Importance': importances})
        title = "Feature Importance (from model)"

    elif method_used == 'shap':
        if not isinstance(model_or_shap_values, np.ndarray):
             raise TypeError("当 method='shap' 时，输入必须是一个SHAP值数组。")
        shap_values = model_or_shap_values
        # 对于多分类，shap_values可能是列表，这里我们只处理回归或二分类的情况
        if isinstance(shap_values, list):
            shap_values = shap_values[0]
        importances = np.abs(shap_values).mean(axis=0)
        importance_df = pd.DataFrame({'Feature': X.columns, 'Importance': importances})
        title = "Feature Importance (Mean Absolute SHAP)"
    else:
        raise ValueError(f"不支持的方法: '{method}'。支持的值为 'auto', 'classic', 'shap'。")

    # --- 2. 准备绘图数据 ---
    importance_df = importance_df.sort_values(by='Importance', ascending=False)
    if top_n is not None and top_n > 0:
        importance_df = importance_df.head(top_n)

    # --- 3. 绘图 ---
    if ax is None:
        height = max(6, len(importance_df) * 0.4)
        fig, ax = plt.subplots(figsize=kwargs.pop('figsize', (10, height)))
    else:
        fig = ax.get_figure()

    sns.barplot(x='Importance', y='Feature', data=importance_df, ax=ax, **kwargs)

    ax.set_title(title)
    ax.set_xlabel("Importance Score")
    ax.set_ylabel("Features")
    fig.tight_layout()

    return ax
