#!/usr/bin/env python3
"""区间型数据集转连续型数据集服务模块。

提供WpIntervalDataset转换为WpContinuousDataset的核心转换逻辑。

Architecture
------------
层次/依赖: datasets/service层，区间数据转换业务逻辑
设计原则: Service Layer模式、向量化操作、类型安全
性能特征: 高效区间查找、内存优化、批量处理

Examples:
    >>> # 基本转换
    >>> df, metadata, depth_range = convert_interval_to_continuous(
    ...     interval_dataset, sampling_interval=0.5
    ... )

    >>> # 自定义深度范围转换
    >>> df, metadata, depth_range = convert_interval_to_continuous(
    ...     interval_dataset,
    ...     sampling_interval=1.0,
    ...     depth_range=(2490.0, 2520.0),
    ...     fill_value=0.0,
    ...     out_of_range_fill_value=-999
    ... )

References:
    《SCAPE_DDS_logwp_层段转连续数据集.md》§3 - 转换算法设计
"""

from __future__ import annotations

import copy
from datetime import datetime
from typing import TYPE_CHECKING, Any

import numpy as np
import pandas as pd

from logwp.models.constants import WpCurveCategory, WpDepthRole, WpStandardColumn
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata
from logwp.models.exceptions import WpDataError, WpValidationError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.datasets.interval import WpIntervalDataset

logger = get_logger(__name__)


def convert_interval_to_continuous(
    interval_dataset: WpIntervalDataset,
    sampling_interval: float,
    *,
    depth_range: tuple[float, float] | None = None,
    fill_value: Any = np.nan,
    out_of_range_fill_value: Any = np.nan
) -> tuple[pd.DataFrame, CurveMetadata, tuple[float, float]]:
    """将区间型数据集转换为连续型数据集格式。

    实现区间数据到连续数据的转换，支持自定义深度采样间隔和填充策略。

    Args:
        interval_dataset: 源区间型数据集
        sampling_interval: 深度采样间隔（必须 > 0）
        depth_range: 输出深度范围，None表示使用原数据集范围
        fill_value: 原数据集未覆盖区域的填充值
        out_of_range_fill_value: 超出原数据集范围的填充值

    Returns:
        tuple[pd.DataFrame, CurveMetadata, tuple[float, float]]:
            (转换后的DataFrame, 新的曲线元数据, 实际深度范围)

    Raises:
        WpValidationError: 当参数验证失败时抛出
        WpDataError: 当数据集状态异常时抛出

    Note:
        - 转换后的深度曲线名称为WpStandardColumn.DEPTH
        - 保留原有的井名曲线
        - 区间值不进行重采样，直接使用原始层段值
        - 深度范围会按采样间隔对齐

    Examples:
        >>> # 基本转换
        >>> df, metadata, depth_range = convert_interval_to_continuous(
        ...     interval_dataset, sampling_interval=0.5
        ... )
        >>> assert WpStandardColumn.DEPTH in df.columns
        >>> assert len(df) == int((depth_range[1] - depth_range[0]) / 0.5) + 1

        >>> # 自定义范围转换
        >>> df, metadata, depth_range = convert_interval_to_continuous(
        ...     interval_dataset,
        ...     sampling_interval=1.0,
        ...     depth_range=(2490.0, 2520.0),
        ...     fill_value=0.0,
        ...     out_of_range_fill_value=-999
        ... )

    References:
        《SCAPE_DDS_logwp_层段转连续数据集.md》§3.1 - 深度序列生成
        《SCAPE_DDS_logwp_层段转连续数据集.md》§3.2 - 值填充策略
    """
    logger.info(
        "开始区间型数据集转连续型数据集转换",
        operation="convert_interval_to_continuous",
        dataset_name=str(interval_dataset.name),
        sampling_interval=sampling_interval,
        custom_depth_range=depth_range is not None,
        fill_value=str(fill_value),
        out_of_range_fill_value=str(out_of_range_fill_value)
    )

    # 1. 参数验证
    _validate_conversion_parameters(interval_dataset, sampling_interval, depth_range)

    # 2. 获取源数据集信息
    source_df = interval_dataset.df
    source_metadata = interval_dataset.curve_metadata

    # 3. 获取深度和井名曲线信息
    top_curve, bottom_curve = interval_dataset.get_interval_depth_reference_curves()
    well_curves = source_metadata.get_well_identifier_curves()

    # 4. 按井分别处理或使用指定深度范围
    if depth_range is not None:
        # 用户指定了深度范围，使用传统逻辑
        target_depth_range = depth_range
        depth_sequence = _generate_depth_sequence(target_depth_range, sampling_interval)
        actual_depth_range = (depth_sequence[0], depth_sequence[-1])

        new_df = _create_continuous_dataframe_with_fixed_range(
            source_df, depth_sequence, top_curve, bottom_curve, well_curves,
            target_depth_range, fill_value, out_of_range_fill_value
        )
    else:
        # 按井分别处理，避免跨井的无意义深度范围
        new_df, actual_depth_range = _create_continuous_dataframe_per_well(
            source_df, top_curve, bottom_curve, well_curves,
            sampling_interval, fill_value, out_of_range_fill_value
        )

    # 7. 创建新的曲线元数据
    # interval转continuous使用标准深度列名（与DataFrame创建逻辑保持一致）
    depth_column_name = "DEPTH"

    new_metadata = _create_continuous_metadata(
        source_metadata, top_curve, bottom_curve, sampling_interval, depth_column_name
    )

    logger.info(
        "区间型数据集转连续型数据集转换完成",
        operation="convert_interval_to_continuous",
        dataset_name=str(interval_dataset.name),
        original_rows=len(source_df),
        converted_rows=len(new_df),
        actual_depth_range=actual_depth_range
    )

    return new_df, new_metadata, actual_depth_range


def _validate_conversion_parameters(
    interval_dataset: "WpIntervalDataset",
    sampling_interval: float,
    depth_range: tuple[float, float] | None
) -> None:
    """验证转换参数的有效性。

    Args:
        interval_dataset: 区间型数据集
        sampling_interval: 深度采样间隔
        depth_range: 深度范围

    Raises:
        WpValidationError: 当参数无效时抛出
        WpDataError: 当数据集状态异常时抛出
    """
    # 验证采样间隔
    if sampling_interval <= 0:
        raise WpValidationError(
            "深度采样间隔必须为正数",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(interval_dataset.name),
                additional_info={
                    "sampling_interval": sampling_interval,
                    "requirement": "sampling_interval > 0"
                }
            )
        )

    # 验证数据集状态
    if interval_dataset.df.empty:
        raise WpDataError(
            "区间型数据集为空，无法进行转换",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(interval_dataset.name),
                additional_info={"reason": "empty_dataset"}
            )
        )

    # 验证深度曲线
    try:
        interval_dataset.get_interval_depth_reference_curves()
    except WpDataError as e:
        raise WpDataError(
            "区间型数据集缺少有效的深度参考曲线",
            context=ErrorContext(
                operation="validate_conversion_parameters",
                dataset_name=str(interval_dataset.name),
                additional_info={"original_error": str(e)}
            )
        ) from e

    # 验证深度范围
    if depth_range is not None:
        if len(depth_range) != 2 or depth_range[0] >= depth_range[1]:
            raise WpValidationError(
                "深度范围格式无效",
                context=ErrorContext(
                    operation="validate_conversion_parameters",
                    dataset_name=str(interval_dataset.name),
                    additional_info={
                        "depth_range": depth_range,
                        "requirement": "tuple[float, float] with start < end"
                    }
                )
            )

        # 验证深度范围是否包含原数据集范围（按井验证）
        wells_ranges = interval_dataset.get_wells_depth_ranges()
        for well, well_range in wells_ranges.items():
            if (depth_range[0] > well_range[0] or depth_range[1] < well_range[1]):
                raise WpValidationError(
                    f"指定的深度范围必须包含所有井的深度范围，井{well}超出范围",
                    context=ErrorContext(
                        operation="validate_conversion_parameters",
                        dataset_name=str(interval_dataset.name),
                        additional_info={
                            "specified_range": depth_range,
                            "well_range": well_range,
                            "well_name": well,
                            "requirement": "specified_range must contain all wells' ranges"
                    }
                )
            )


def _generate_depth_sequence(
    depth_range: tuple[float, float],
    sampling_interval: float
) -> np.ndarray:
    """生成对齐的深度序列。

    Args:
        depth_range: 深度范围
        sampling_interval: 采样间隔

    Returns:
        np.ndarray: 对齐的深度序列
    """
    depth_start, depth_end = depth_range

    # 深度边界对齐
    aligned_start = np.floor(depth_start / sampling_interval) * sampling_interval
    aligned_end = np.ceil(depth_end / sampling_interval) * sampling_interval

    # 生成深度序列
    depth_sequence = np.arange(aligned_start, aligned_end + sampling_interval, sampling_interval)

    logger.debug(
        "深度序列生成完成",
        operation="generate_depth_sequence",
        original_range=depth_range,
        aligned_range=(aligned_start, aligned_end),
        sampling_interval=sampling_interval,
        depth_points=len(depth_sequence)
    )

    return depth_sequence


def _create_continuous_dataframe(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    top_curve: CurveBasicAttributes,
    bottom_curve: CurveBasicAttributes,
    well_curves: list[str],
    original_depth_range: tuple[float, float],
    fill_value: Any,
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """创建连续型数据集的DataFrame。

    🚨 **重要修复**：现在支持多井数据的正确处理。

    修复说明：
    - 检测输入数据是否包含多口井
    - 单井数据：使用原有的高效逻辑
    - 多井数据：按井分组处理，确保所有井的数据都被正确保留
    - 支持部分曲线为空的情况（保留NaN值）

    Args:
        source_df: 源区间型数据集的DataFrame
        depth_sequence: 深度序列
        top_curve: 顶界深度曲线
        bottom_curve: 底界深度曲线
        well_curves: 井名曲线列表
        original_depth_range: 原数据集深度范围
        fill_value: 原数据集未覆盖区域的填充值
        out_of_range_fill_value: 超出原数据集范围的填充值

    Returns:
        pd.DataFrame: 转换后的连续型DataFrame
    """
    # 🚨 关键修复：检测并正确处理多井数据
    #
    # 修复逻辑：
    # 1. 检查是否包含多口井的数据
    # 2. 单井数据：使用原有高效逻辑
    # 3. 多井数据：按井分组处理，确保所有井数据都被保留

    # 检测井的数量
    unique_wells = []
    if well_curves and well_curves[0] in source_df.columns:
        unique_wells = source_df[well_curves[0]].dropna().unique().tolist()

    if len(unique_wells) <= 1:
        # 🟢 单井数据：使用原有的高效逻辑
        logger.debug(
            "单井数据转换",
            operation="create_continuous_dataframe",
            wells_count=len(unique_wells),
            well_name=unique_wells[0] if unique_wells else "unknown"
        )
        return _create_single_well_interval_continuous_dataframe(
            source_df, depth_sequence, top_curve, bottom_curve, well_curves,
            original_depth_range, fill_value, out_of_range_fill_value
        )
    else:
        # 🔧 多井数据：按井分组处理，然后合并结果
        logger.debug(
            "多井数据转换",
            operation="create_continuous_dataframe",
            wells_count=len(unique_wells),
            wells=unique_wells
        )
        return _create_multi_well_interval_continuous_dataframe(
            source_df, depth_sequence, top_curve, bottom_curve, well_curves,
            original_depth_range, fill_value, out_of_range_fill_value
        )


def _create_single_well_interval_continuous_dataframe(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    top_curve: CurveBasicAttributes,
    bottom_curve: CurveBasicAttributes,
    well_curves: list[str],
    original_depth_range: tuple[float, float],
    fill_value: Any,
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """为单井数据创建连续型DataFrame（原有逻辑）。"""
    # 创建基础DataFrame（interval转continuous使用标准深度列名）
    depth_column_name = "DEPTH"
    new_df = pd.DataFrame({depth_column_name: depth_sequence})

    # 获取深度列名
    top_column = top_curve.dataframe_column_name
    bottom_column = bottom_curve.dataframe_column_name

    # 获取井名（单井数据，取第一个井名即可）
    well_name = None
    if well_curves and well_curves[0] in source_df.columns:
        well_name_series = source_df[well_curves[0]].dropna()
        if not well_name_series.empty:
            well_name = well_name_series.iloc[0]

    # 添加井名列
    if well_name is not None and well_curves:
        new_df[well_curves[0]] = well_name

    # 获取需要转换的数据曲线
    exclude_columns = {top_column, bottom_column}
    if well_curves:
        exclude_columns.update(well_curves)
    data_columns = [col for col in source_df.columns if col not in exclude_columns]

    # 为每个数据曲线进行值填充
    for column in data_columns:
        new_df[column] = _fill_curve_values(
            source_df, depth_sequence, top_column, bottom_column, column,
            original_depth_range, fill_value, out_of_range_fill_value
        )

    return new_df


def _create_multi_well_interval_continuous_dataframe(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    top_curve: CurveBasicAttributes,
    bottom_curve: CurveBasicAttributes,
    well_curves: list[str],
    original_depth_range: tuple[float, float],
    fill_value: Any,
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """为多井数据创建连续型DataFrame（新增逻辑）。

    🚨 **关键修复**：正确处理多井数据的转换。
    """
    if not well_curves or well_curves[0] not in source_df.columns:
        raise ValueError("多井数据转换需要井名曲线")

    well_column = well_curves[0]
    unique_wells = source_df[well_column].dropna().unique()
    top_column = top_curve.dataframe_column_name
    bottom_column = bottom_curve.dataframe_column_name

    # 获取需要转换的数据曲线
    exclude_columns = {top_column, bottom_column}
    if well_curves:
        exclude_columns.update(well_curves)
    data_columns = [col for col in source_df.columns if col not in exclude_columns]

    # 存储每口井的转换结果
    well_dataframes = []

    for well_name in unique_wells:
        # 提取当前井的数据
        well_data = source_df[source_df[well_column] == well_name].copy()
        if well_data.empty:
            continue

        # 为当前井创建连续型DataFrame
        well_continuous_df = pd.DataFrame({"DEPTH": depth_sequence})
        well_continuous_df[well_column] = well_name

        # 对当前井的每个数据曲线进行值填充
        for column in data_columns:
            if column not in well_data.columns:
                well_continuous_df[column] = fill_value
                continue

            # 为当前井的当前曲线进行值填充
            try:
                well_continuous_df[column] = _fill_curve_values(
                    well_data, depth_sequence, top_column, bottom_column, column,
                    original_depth_range, fill_value, out_of_range_fill_value
                )
            except Exception as e:
                logger.warning(
                    "井曲线值填充失败，使用默认填充值",
                    operation="create_multi_well_interval_continuous_dataframe",
                    well_name=well_name,
                    column=column,
                    error=str(e)
                )
                well_continuous_df[column] = fill_value

        well_dataframes.append(well_continuous_df)

    # 合并所有井的转换结果
    if not well_dataframes:
        return pd.DataFrame()

    result_df = pd.concat(well_dataframes, ignore_index=True)
    return result_df


def _fill_curve_values(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    top_column: str,
    bottom_column: str,
    data_column: str,
    original_depth_range: tuple[float, float],
    fill_value: Any,
    out_of_range_fill_value: Any
) -> pd.Series:
    """为单个曲线填充值。

    Args:
        source_df: 源DataFrame
        depth_sequence: 深度序列
        top_column: 顶界深度列名
        bottom_column: 底界深度列名
        data_column: 数据列名
        original_depth_range: 原数据集深度范围
        fill_value: 原数据集未覆盖区域的填充值
        out_of_range_fill_value: 超出原数据集范围的填充值

    Returns:
        pd.Series: 填充后的数据序列
    """
    # 初始化结果序列
    result = pd.Series(index=range(len(depth_sequence)), dtype=object)

    # 过滤有效的区间数据
    valid_intervals = source_df[
        (source_df[top_column].notna()) &
        (source_df[bottom_column].notna()) &
        (source_df[data_column].notna())
    ].copy()

    if valid_intervals.empty:
        # 没有有效区间，全部填充默认值
        for i, depth in enumerate(depth_sequence):
            if original_depth_range[0] <= depth <= original_depth_range[1]:
                result.iloc[i] = fill_value
            else:
                result.iloc[i] = out_of_range_fill_value
        return result

    # 使用向量化方法进行区间查找和值填充
    for i, depth in enumerate(depth_sequence):
        # 查找包含当前深度的区间
        matching_intervals = valid_intervals[
            (valid_intervals[top_column] <= depth) &
            (valid_intervals[bottom_column] > depth)
        ]

        if not matching_intervals.empty:
            # 找到匹配区间，使用第一个匹配的值
            result.iloc[i] = matching_intervals[data_column].iloc[0]
        elif original_depth_range[0] <= depth <= original_depth_range[1]:
            # 在原数据集范围内但未被区间覆盖
            result.iloc[i] = fill_value
        else:
            # 超出原数据集范围
            result.iloc[i] = out_of_range_fill_value

    return result


def _create_continuous_metadata(
    source_metadata: CurveMetadata,
    top_curve: CurveBasicAttributes,
    bottom_curve: CurveBasicAttributes,
    sampling_interval: float,
    depth_column_name: str
) -> CurveMetadata:
    """创建连续型数据集的曲线元数据。

    Args:
        source_metadata: 源曲线元数据
        top_curve: 顶界深度曲线
        bottom_curve: 底界深度曲线
        sampling_interval: 采样间隔

    Returns:
        CurveMetadata: 新的曲线元数据
    """
    # 深拷贝源元数据
    new_metadata = copy.deepcopy(source_metadata)

    # 移除原有的深度曲线
    if new_metadata.has_curve(top_curve.name):
        new_metadata.remove_curve(top_curve.name)
    if new_metadata.has_curve(bottom_curve.name):
        new_metadata.remove_curve(bottom_curve.name)

    # 创建新的深度曲线（使用推导的深度列名）
    depth_curve = CurveBasicAttributes.create_1d_curve(
        name=depth_column_name,  # 使用推导的深度列名
        unit=top_curve.unit,  # 使用顶界深度曲线的单位
        category=WpCurveCategory.IDENTIFIER,
        depth_role=WpDepthRole.SINGLE,
        description=f"连续深度曲线，采样间隔{sampling_interval}{top_curve.unit}"
    )
    # 设置DataFrame列名（保持一致的DataFrame列名）
    object.__setattr__(depth_curve, 'dataframe_column_name', depth_column_name)

    # 添加新的深度曲线
    new_metadata.add_curve(depth_curve)

    # 井名曲线保持原始名称，无需修改dataframe_column_name

    # 更新时间戳
    new_metadata.modified_at = datetime.now()

    logger.debug(
        "连续型曲线元数据创建完成",
        operation="create_continuous_metadata",
        total_curves=new_metadata.get_curve_count(),
        depth_curve=depth_column_name,
        sampling_interval=sampling_interval,
        well_curves=len(new_metadata.get_well_identifier_curves())
    )

    return new_metadata


def _create_continuous_dataframe_with_fixed_range(
    source_df: pd.DataFrame,
    depth_sequence: np.ndarray,
    top_curve: CurveBasicAttributes,
    bottom_curve: CurveBasicAttributes,
    well_curves: list[str],
    original_depth_range: tuple[float, float],
    fill_value: Any,
    out_of_range_fill_value: Any
) -> pd.DataFrame:
    """使用固定深度范围创建连续型DataFrame（用户指定深度范围时）。"""
    return _create_continuous_dataframe(
        source_df, depth_sequence, top_curve, bottom_curve, well_curves,
        original_depth_range, fill_value, out_of_range_fill_value
    )


def _create_continuous_dataframe_per_well(
    source_df: pd.DataFrame,
    top_curve: CurveBasicAttributes,
    bottom_curve: CurveBasicAttributes,
    well_curves: list[str],
    sampling_interval: float,
    fill_value: Any,
    out_of_range_fill_value: Any
) -> tuple[pd.DataFrame, tuple[float, float]]:
    """按井分别创建连续型DataFrame，避免跨井的无意义深度范围。

    Returns:
        tuple[pd.DataFrame, tuple[float, float]]: (转换后的DataFrame, 实际深度范围)
    """
    # 检测井的数量
    unique_wells = []
    if well_curves and well_curves[0] in source_df.columns:
        unique_wells = source_df[well_curves[0]].dropna().unique().tolist()

    top_column = top_curve.dataframe_column_name
    bottom_column = bottom_curve.dataframe_column_name

    if len(unique_wells) <= 1:
        # 单井数据：使用井自己的深度范围
        well_data = source_df
        if unique_wells:
            well_data = source_df[source_df[well_curves[0]] == unique_wells[0]]

        # 计算井的深度范围（顶界最小值到底界最大值）
        top_depths = well_data[top_column].dropna()
        bottom_depths = well_data[bottom_column].dropna()

        if top_depths.empty or bottom_depths.empty:
            return pd.DataFrame(), (0.0, 0.0)

        well_depth_range = (float(top_depths.min()), float(bottom_depths.max()))
        depth_sequence = _generate_depth_sequence(well_depth_range, sampling_interval)

        new_df = _create_continuous_dataframe(
            source_df, depth_sequence, top_curve, bottom_curve, well_curves,
            well_depth_range, fill_value, out_of_range_fill_value
        )

        actual_depth_range = (depth_sequence[0], depth_sequence[-1])
        return new_df, actual_depth_range

    else:
        # 多井数据：按井分别处理，然后合并
        well_dataframes = []
        all_depth_ranges = []

        for well in unique_wells:
            well_data = source_df[source_df[well_curves[0]] == well]

            # 计算当前井的深度范围
            top_depths = well_data[top_column].dropna()
            bottom_depths = well_data[bottom_column].dropna()

            if top_depths.empty or bottom_depths.empty:
                continue

            well_depth_range = (float(top_depths.min()), float(bottom_depths.max()))
            well_depth_sequence = _generate_depth_sequence(well_depth_range, sampling_interval)

            well_df = _create_continuous_dataframe(
                well_data, well_depth_sequence, top_curve, bottom_curve, well_curves,
                well_depth_range, fill_value, out_of_range_fill_value
            )

            if not well_df.empty:
                well_dataframes.append(well_df)
                all_depth_ranges.extend([well_depth_sequence[0], well_depth_sequence[-1]])

        if not well_dataframes:
            return pd.DataFrame(), (0.0, 0.0)

        # 合并所有井的数据
        combined_df = pd.concat(well_dataframes, ignore_index=True)

        # 计算实际深度范围
        if all_depth_ranges:
            actual_depth_range = (min(all_depth_ranges), max(all_depth_ranges))
        else:
            actual_depth_range = (0.0, 0.0)

        return combined_df, actual_depth_range
