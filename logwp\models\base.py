"""
logwp.models.base - 模型基类定义

提供WP项目中各种模型的抽象基类。

Architecture
------------
层次/依赖: models层基础抽象类
设计原则: 组合模式、领域驱动设计、最小接口
性能特征: 轻量级抽象、零运行时开销

Classes
-------
- WpProjectComponent: WP项目组件抽象基类

Examples
--------
>>> # 此模块提供抽象基类，不直接实例化
>>> # 由WpHead、WpWellMap、WpDatabaseBase等继承使用

References
----------
- 《SCAPE_SAD_软件架构设计.md》组合模式设计
- 《SCAPE_CCG_编码与通用规范.md》CS-2 - 包前缀命名规范
"""

from __future__ import annotations
from dataclasses import dataclass


@dataclass
class WpProjectComponent:
    """WP项目组件抽象基类。
    
    代表WpWellProject组合根中管理的核心对象。
    
    Architecture
    ------------
    层次/依赖: models层抽象基类
    设计原则: 组合模式、领域驱动设计
    性能特征: 轻量级抽象、最小接口
    
    Design Philosophy
    -----------------
    - **组合根管理**: 作为WpWellProject组合根中管理的对象
    - **统一抽象**: 为不同类型的项目组件提供统一的抽象
    - **最小接口**: 目前保持最小化，避免大规模重构
    - **扩展性**: 为未来添加共同方法或属性预留空间
    
    Inheritance Hierarchy
    ---------------------
    - WpHead: 项目头信息组件
    - WpWellMap: 井映射组件
    - WpDatabaseBase: 数据集组件基类
    
    Examples
    --------
    >>> # 此类为抽象基类，不直接实例化
    >>> # 由具体的项目组件类继承
    >>> from logwp.models.head import WpHead
    >>> head = WpHead()
    >>> assert isinstance(head, WpProjectComponent)
    
    References
    ----------
    - 《SCAPE_SAD_软件架构设计.md》组合模式设计原则
    - 《SCAPE_DDS_详细设计_logwp.md》§3.2 - 模型层设计
    """
    pass  # 目前保持最小化，避免大规模重构
