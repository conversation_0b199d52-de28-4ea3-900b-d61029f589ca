# `logwp.extras.petroplot.common` - 可复用绘图组件基础包

**版本**: 2.0 (重构后)
**状态**: 生产就绪

## 1. 摘要 (Abstract)

`logwp.extras.petroplot.common` 是 `petroplot` 绘图框架的**核心基础包**。它遵循**“关注点分离”**和**“组合优于继承”**的设计原则，为所有绘图组件提供了可复用的、分层的模块。

本包为新绘图组件的开发提供了坚实的脚手架，旨在：
- **定义标准**: 通过抽象基类定义所有绘图器的统一接口和行为。
- **加速开发**: 提供通用的配置模型和可复用的高级功能（如模块化布局）。
- **保证一致性**: 确保所有 `petroplot` 组件在核心行为上保持一致。
- **提升可维护性**: 将通用逻辑集中管理，便于未来的统一升级和维护。

---

## 2. 核心组件详解

本包主要由以下几个核心模块构成：

### 2.1. `plotter_abc.py` - 绘图器抽象基类

这是 `petroplot` 框架的**顶层设计**。该模块定义了所有绘图器必须遵循的、**与具体绘图库无关的**抽象基类（ABC）。

- **`PetroPlotter`**: 所有绘图器的顶层基类，采用**模板方法模式**定义了标准的绘图流程（`plot()` 方法），并声明了子类必须实现的抽象方法（如 `_setup_figure`, `_add_data_traces` 等）。
- **`CartesianPlotter` & `TernaryPlotter`**: 分别为笛卡尔坐标系图表和三元图表定义的二级抽象基类，增加了更具体的抽象方法（如 `_setup_cartesian_axes`）。

**使用方法**: 所有具体的绘图器（如 `CrossPlotter`）**必须**继承自这些ABC中的一个，以确保它们遵循统一的接口和绘图流程。

```python
# 在新的组件中 (e.g., logwp/extras/petroplot/crossplot/internal/plotter.py)
from logwp.extras.petroplot.common import CartesianPlotter

class CrossPlotter(CartesianPlotter):
    def _setup_figure(self) -> None:
        # 实现Figure的初始化
        ...

    def _add_data_traces(self) -> None:
        # 实现数据轨迹的添加
        ...

    # ... 实现其他所有抽象方法 ...
```

### 2.2. `config.py` - 通用配置模型

该模块定义了一系列所有绘图组件都可能用到的、基于Pydantic的配置模型。

- **`ContinuousColorConfig` & `CategoricalColorConfig`**: 为连续数值（如渗透率）和分类数据（如岩性）提供了标准化的颜色映射配置方案。
- **`SymbolConfig`**: 定义了如何将分类数据映射到不同的标记符号。
- **`LegendConfig`**: 提供了对图例位置、标题等通用属性的控制。

**使用方法**: 在开发新的绘图组件时，其专属的 `config.py` 文件应直接从本模块导入并使用这些通用模型，而不是重新定义它们。

### 2.3. `layout_manager.py` & `legend_manager.py` - 模块化布局辅助工具

这两个模块是新架构的核心，它们是**纯净的、职责单一的布局“零件供应商”**。

- **`LayoutManager`**: **仅**负责应用通用的、与图例和坐标轴无关的布局属性（标题、全局字体、基础边距、画布背景色）。它**不**处理图表尺寸。
- **`LegendManager`**: **仅**负责生成标准图例（`legend`对象）的配置。它**不**处理颜色轴（Colorbar）。

**使用方法**: 遵循**组合优于继承**和**“总指挥”模式**。具体的绘图器在其 `_apply_layout` 方法中扮演“总指挥”的角色，负责组装布局：

```python
# 在具体绘图器的 _apply_layout 方法中

# 1. 绘图器自己准备“个性化”的布局片段 (如坐标轴、特殊domain设置)
axes_config = self._setup_my_special_axes()
if self._needs_special_layout():
    axes_config['my_axis']['domain'] = [0, 0.9] # 绘图器自己负责特殊逻辑

# 2. 从“通用”管理器获取“标准”布局零件
layout_mgr = LayoutManager(self.config, self.plot_profile, self.subtitle)
base_layout = layout_mgr.build_base_layout()

legend_mgr = LegendManager(self.config, self.plot_profile)
legend_layout = legend_mgr.build_legend_layout(
    has_standard_legend=self._has_standard_legend_items()
)

# 3. 作为“总指挥”，明确设定画布尺寸并按顺序合并所有布局片段
final_layout = {}
final_layout.update(base_layout)
final_layout.update({
    'width': self.plot_profile.figure_props.get("width", 800),
    'height': self.plot_profile.figure_props.get("height", 700),
})
final_layout.update(axes_config)
final_layout.update(legend_layout)

# 4. 一次性应用最终布局
self.fig.update_layout(**final_layout)
```

### 2.4. `facade_utils.py` - 门面辅助函数

该模块提供了一系列旨在简化各组件 `facade.py` 中重复性工作的辅助函数。

- **`save_and_register_plots`**: 封装了图表保存和注册的完整逻辑。

### 2.5. `exceptions.py` - 通用异常

定义了 `PetroPlotError` 基类，供所有 `petroplot` 组件抛出特定于本库的异常时使用。

---

## 3. 目录结构

```text
logwp/extras/petroplot/common/
├── __init__.py           # 导出公共API
├── README.md             # 本文档
├── plotter_abc.py        # 绘图器抽象基类
├── config.py             # 通用配置模型
├── layout_manager.py     # 【新】通用布局管理器
├── legend_manager.py     # 【新】标准图例管理器
├── facade_utils.py       # 通用门面辅助函数
└── exceptions.py         # 通用异常
```
