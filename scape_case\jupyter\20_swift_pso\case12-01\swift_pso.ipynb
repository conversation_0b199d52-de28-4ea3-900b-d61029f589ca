{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a9e0a2e7", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "1e86095f", "metadata": {}, "source": ["# SWIFT-PSO 案例\n", "- 固定随机种子\n", "- 新增`收敛轨迹与聚类分析图`以及各Cluster参数统计\n", "- 12个优化参数,t2lm_exp、vmacro_b、vmacro_min为固定\n", "- t2_cutoff_long参数搜索边界修改\n", "- random_seed = 2000\n", "- t-SNE可视化最终收敛点聚类分析 (Cluster Analysis)聚类方法：kmeans"]}, {"cell_type": "markdown", "id": "4266395e", "metadata": {}, "source": ["## 1. 初始化与导入"]}, {"cell_type": "code", "execution_count": 2, "id": "3e9b170c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-29T03:53:55.873036Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 128.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:53:58.355102Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.46, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-29T03:53:58.370849Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.47, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-29T03:53:58.386600Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.48, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-29T03:53:58.434499Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.98, 'cpu_percent': 0.0} operation=register_base_profile profile_name=swift_pso.base\n", "2025-07-29T03:53:58.466329Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 356.01, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_convergence\n", "2025-07-29T03:53:58.481553Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 356.04, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T03:54:00.049401Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.04, 'cpu_percent': 0.0} operation=register_base_profile profile_name=validation.plt.base\n", "2025-07-29T03:54:00.065254Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.06, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:54:00.097190Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.09, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.capture_curve\n", "2025-07-29T03:54:00.113011Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.1, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:54:00.145094Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.13, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.perm_corr.permeability_crossplot\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "# 导入 swift_pso 组件\n", "from scape.core.swift_pso import (\n", "    run_swift_pso_training_step,\n", "    run_swift_pso_prediction_step,\n", "    run_tsne_visualization_step,\n", "    SwiftPsoTrainingConfig,\n", "    SwiftPsoPredictionConfig,\n", "    TsneVisualConfig,\n", "    SwiftPsoTrainingArtifacts,\n", "    SwiftPsoPredictionArtifacts,\n", "    TsneVisualArtifacts,\n", "    TsnePlotProfiles\n", ")\n", "\n", "# 导入 validation 组件\n", "from scape.core.validation import (\n", "    run_plt_analysis_step,\n", "    run_perm_correlation_step,\n", "    PltAnalysisConfig,\n", "    PermCorrelationConfig,\n", "    PltAnalysisArtifacts,\n", "    PermCorrelationArtifacts,\n", "    PltPlotTypes,\n", "    PltAnalysisPlotProfiles,\n", "    PermCorrelationPlotProfiles\n", ")\n", "\n", "import scape.core.swift_pso.plot_profiles"]}, {"cell_type": "markdown", "id": "f519543e", "metadata": {}, "source": ["## 2. 实验设置与数据加载\n", "\n", "使用 `RunContext` 初始化一个实验，所有后续操作都将在此上下文中进行，确保所有产物和日志都保存在一个独立的运行目录中。"]}, {"cell_type": "code", "execution_count": 3, "id": "e446002f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:54:00.311698Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.84, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\swift_pso_run_20250729_115400 run_id=20250729-035400-3b1f6242\n", "实验运行已初始化，所有产物将保存至: F:\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\20_swift_pso\\case13\\output01\\swift_pso_run_20250729_115400\n", "2025-07-29T03:54:00.319378Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.84, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T03:54:00.352542Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.11, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx file_size_mb=0.17 sheet_count=1\n", "2025-07-29T03:54:00.368649Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.14, 'cpu_percent': 0.0} project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T03:54:00.384670Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.15, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T03:54:00.400485Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.53, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:54:00.432467Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.76, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=11 well_curves=1\n", "2025-07-29T03:54:00.872846Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.06, 'cpu_percent': 0.0} shape=(408, 74) sheet_name=swift_pso_train_cleaned\n", "2025-07-29T03:54:00.888636Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.07, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned')\n", "2025-07-29T03:54:00.908226Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.07, 'cpu_percent': 0.0} curve_count=11 dataset_name=swift_pso_train_cleaned dataset_type=Point df_shape=(408, 74) processing_time=0.524\n", "2025-07-29T03:54:00.936123Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.07, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:54:00.951849Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.07, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:54:00.968009Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.07, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=0.649 project_name=WpIdentifier('scape_swift_pso_train_cleaned')\n", "✅ 成功读取训练数据: ./scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T03:54:01.015630Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.34, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=11 input_curves=['DPHIT_NMR', 'PHIT_NMR', 'K_LABEL_TYPE', 'PHI_T2_DIST', 'K_LABEL', 'MD', 'T2LM', 'T2_P50', 'WELL_NO', 'DT2_P50', 'PZI'] operation=extract_metadata output_curve_count=11 output_curves=['DPHIT_NMR', 'PHIT_NMR', 'K_LABEL_TYPE', 'PHI_T2_DIST', 'K_LABEL', 'MD', 'T2LM', 'T2_P50', 'WELL_NO', 'DT2_P50', 'PZI']\n", "2025-07-29T03:54:01.047577Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.34, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T03:54:01.063587Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.34, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T03:54:01.069080Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.34, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T03:54:01.085022Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.34, 'cpu_percent': 0.0}\n", "===train_bundle:   WELL_NO       MD  PHIT_NMR      T2LM   DT2_P50    T2_P50  DPHIT_NMR  \\\n", "0     C-1  6319.40  0.105551   785.428  0.316622   997.666   0.079668   \n", "1     C-1  6335.20  0.138267   746.054  0.111100   871.799   0.023800   \n", "2     C-1  6334.52  0.137828  3960.371  1.190176  6131.592   0.060766   \n", "3     C-1  6337.00  0.100931   527.925  0.102427   663.328   0.004202   \n", "4     C-1  6349.70  0.047791   111.798 -0.399350   203.148  -0.029715   \n", "\n", "     T2_VALUE_1    T2_VALUE_2    T2_VALUE_3  ...  T2_VALUE_58  T2_VALUE_59  \\\n", "0  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003882     0.003707   \n", "1  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.004239     0.003638   \n", "2  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.010259     0.012969   \n", "3  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003190     0.003105   \n", "4  1.506000e-05  3.392000e-05  6.404000e-05  ...     0.000000     0.000000   \n", "\n", "   T2_VALUE_60  T2_VALUE_61  T2_VALUE_62  T2_VALUE_63  T2_VALUE_64  K_LABEL  \\\n", "0     0.003506     0.003268     0.002990     0.002676     0.002339    0.888   \n", "1     0.003084     0.002577     0.002114     0.001696     0.001326    0.205   \n", "2     0.015308     0.017210     0.018582     0.019324     0.019364  153.000   \n", "3     0.003001     0.002867     0.002698     0.002490     0.002247    6.130   \n", "4     0.000000     0.000000     0.000000     0.000000     0.000000    0.263   \n", "\n", "   K_LABEL_TYPE  PZI  \n", "0          CORE    1  \n", "1           MDT    1  \n", "2          CORE    1  \n", "3          CORE    1  \n", "4          CORE    1  \n", "\n", "[5 rows x 74 columns]\n", "2025-07-29T03:54:01.116853Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.39, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI']\n", "===train_label_all_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T03:54:01.181022Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.41, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI']\n", "===train_label_pz_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T03:54:01.276028Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.46, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI']\n", "===train_label_core_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "2025-07-29T03:54:01.374106Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.46, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['K_LABEL_TYPE', 'MD', 'WELL_NO', 'K_LABEL', 'PZI']\n", "===train_label_core_pz_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "  - T2轴信息: T2AxisLog10(t2_start=0.1, t2_end=10000, n=64, unit='ms')\n"]}], "source": ["# --- 实验设置 ---\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"swift_pso_run\")\n", "run_context = RunContext(output_dir / run_dir_name,overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")\n", "\n", "# --- 加载训练数据 ---\n", "data_file_path = \"./scape_swift_pso_train_cleaned.wp.xlsx\"\n", "reader = WpExcelReader()\n", "train_project = reader.read(data_file_path)\n", "print(f\"✅ 成功读取训练数据: {data_file_path}\")\n", "\n", "train_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',\n", "        'PHI_T2_DIST', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_bundle: {train_bundle.data.head()}\")\n", "\n", "# 训练集岩心检验数据准备\n", "train_label_all_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_all_bundle: {train_label_all_bundle.data.head()}\")\n", "\n", "train_label_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_pz_bundle: {train_label_pz_bundle.data.head()}\")\n", "\n", "train_label_core_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE'\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_bundle: {train_label_core_bundle.data.head()}\")\n", "\n", "train_label_core_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE' and PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_pz_bundle: {train_label_core_pz_bundle.data.head()}\")\n", "\n", "# --- 准备数据依赖参数 ---\n", "t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "t2_time_array = t2_axis_info.calculate_values()\n", "print(f\"  - T2轴信息: {t2_axis_info}\")"]}, {"cell_type": "markdown", "id": "e5b87190", "metadata": {}, "source": ["## 3. 步骤一：SWIFT-PSO 训练\n", "\n", "调用 `run_swift_pso_training_step` 执行训练。我们首先使用 `SwiftPsoTrainingConfig.create_default()` 创建一个默认配置对象，然后将数据依赖的参数（如参考值、T2轴）注入其中。"]}, {"cell_type": "code", "execution_count": 4, "id": "76495147", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\n", "2025-07-29T03:54:01.500485Z [info     ] 开始SWIFT-PSO训练步骤                [scape.core.swift_pso.training_facade] backend=gpu bootstrap_iterations=20 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.68, 'cpu_percent': 0.0} operation=swift_pso_training_step run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:01.536940Z [info     ] 成功替换Bundle中的曲线                 [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.68, 'cpu_percent': 0.0} curve_name=K_LABEL_TYPE new_data_type=INT new_shape=(408,) operation=replace_curve\n", "2025-07-29T03:54:01.566654Z [info     ] 必需曲线验证通过                       [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') checked_curves_count=9 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.68, 'cpu_percent': 0.0} operation=validate_required_curves\n", "2025-07-29T03:54:01.580665Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=gpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.69, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T03:54:01.605955Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.configs.training_config artifact_path=swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.74, 'cpu_percent': 0.0} description=本次训练步骤的完整Pydantic配置快照，确保可复现性。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:01.621570Z [info     ] 训练配置已保存为产物                     [scape.core.swift_pso.training_facade] config_path=output01\\swift_pso_run_20250729_115400\\swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.74, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:01.647065Z [info     ] 调用内部PSO优化器                     [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.74, 'cpu_percent': 0.0} operation=swift_pso_training_step\n", "2025-07-29T03:54:01.660275Z [info     ] 开始按井拆分DataFrame Bundle         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.74, 'cpu_percent': 0.0} operation=to_all_wells_data\n", "2025-07-29T03:54:01.675400Z [info     ] DataFrame Bundle按井拆分完成         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 408.17, 'cpu_percent': 0.0} operation=to_all_wells_data wells_found=['C-1', 'C-2', 'T-1']\n", "--- Bootstrap-<PERSON>de 1/20 ---\n", "2025-07-29T03:54:01.699595Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 409.47, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 120 代触发。\n", "2025-07-29T03:54:03.588340Z [info     ] PSO 优化完成。最终迭代次数: 120, 最优损失: 5.725784 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.83, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:03.616817Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.84, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:03.665989Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.9, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:03.695164Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.92, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(8.999999999999999e-08, 0.08350502) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T03:54:03.735138Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.92, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 148 代触发。\n", "2025-07-29T03:54:05.772297Z [info     ] PSO 优化完成。最终迭代次数: 148, 最优损失: 4.077808 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.92, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:05.802173Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.92, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:05.840978Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:05.866553Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(7.999999999999999e-08, 0.05063158) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T03:54:05.907485Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 130 代触发。\n", "2025-07-29T03:54:07.834087Z [info     ] PSO 优化完成。最终迭代次数: 130, 最优损失: 5.184395 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:07.863625Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:07.903222Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:07.936815Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.0012945999999999997, 0.05541687000000001) v_micro_range=(0.0026810799999999997, 0.05037252000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 2/20 ---\n", "2025-07-29T03:54:07.970889Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 167 代触发。\n", "2025-07-29T03:54:10.232487Z [info     ] PSO 优化完成。最终迭代次数: 167, 最优损失: 5.229554 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:10.253939Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:10.289857Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.93, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:10.326775Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.94, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(6e-08, 0.09763175999999998) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T03:54:10.355067Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.94, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 138 代触发。\n", "2025-07-29T03:54:12.277808Z [info     ] PSO 优化完成。最终迭代次数: 138, 最优损失: 4.423777 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.94, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:12.297742Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.94, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:12.330841Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.94, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:12.358245Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.94, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:54:12.393288Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.94, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 148 代触发。\n", "2025-07-29T03:54:14.607169Z [info     ] PSO 优化完成。最终迭代次数: 148, 最优损失: 5.362391 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:14.636686Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:14.664969Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:14.691178Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.00041007, 0.03927055) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 3/20 ---\n", "2025-07-29T03:54:14.726075Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 131 代触发。\n", "2025-07-29T03:54:16.499324Z [info     ] PSO 优化完成。最终迭代次数: 131, 最优损失: 5.374678 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:16.523364Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:16.555823Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:16.574570Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(1.0999999999999998e-07, 0.10750565999999998) v_micro_range=(3.600000000000002e-07, 0.04908202000000001)\n", "2025-07-29T03:54:16.613795Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 146 代触发。\n", "2025-07-29T03:54:18.623080Z [info     ] PSO 优化完成。最终迭代次数: 146, 最优损失: 4.024223 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:18.654727Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:18.705401Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:18.732875Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(3.0000000000000004e-08, 0.030009409999999997) v_micro_range=(0.00393, 0.10102375999999999)\n", "2025-07-29T03:54:18.769998Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.95, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 75 代触发。\n", "2025-07-29T03:54:19.918816Z [info     ] PSO 优化完成。最终迭代次数: 75, 最优损失: 5.296764 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:19.945712Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:19.985386Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:20.011013Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.0012945999999999997, 0.05541687000000001) v_micro_range=(0.0026810799999999997, 0.05037252000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 4/20 ---\n", "2025-07-29T03:54:20.047982Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 160 代触发。\n", "2025-07-29T03:54:22.221892Z [info     ] PSO 优化完成。最终迭代次数: 160, 最优损失: 5.675950 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:22.253666Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:22.293260Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.09, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:22.319712Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.1, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(6e-08, 0.07921933) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T03:54:22.359163Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.1, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 138 代触发。\n", "2025-07-29T03:54:24.276904Z [info     ] PSO 优化完成。最终迭代次数: 138, 最优损失: 4.471957 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.1, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:24.301797Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.1, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:24.338301Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.1, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:24.363353Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(4e-08, 0.03894591) v_micro_range=(0.00370749, 0.09978174999999999)\n", "2025-07-29T03:54:24.400593Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 82 代触发。\n", "2025-07-29T03:54:25.668072Z [info     ] PSO 优化完成。最终迭代次数: 82, 最优损失: 5.864759 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:25.695859Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:25.733710Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:25.765041Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.07809129999999999) v_meso_range=(0.006168889999999999, 0.10613273000000001) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 5/20 ---\n", "2025-07-29T03:54:25.805059Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 65 代触发。\n", "2025-07-29T03:54:26.737636Z [info     ] PSO 优化完成。最终迭代次数: 65, 最优损失: 5.133539 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:26.772180Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:26.808644Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:26.838875Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(5e-08, 0.08579142) v_meso_range=(0.00139793, 0.16203889) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T03:54:26.878610Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 156 代触发。\n", "2025-07-29T03:54:29.008270Z [info     ] PSO 优化完成。最终迭代次数: 156, 最优损失: 3.663028 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:29.031167Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:29.069173Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:29.089748Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(7.999999999999999e-08, 0.05063158) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T03:54:29.125011Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 81 代触发。\n", "2025-07-29T03:54:30.407276Z [info     ] PSO 优化完成。最终迭代次数: 81, 最优损失: 6.093817 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:30.428113Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:30.460581Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:30.486649Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006965269999999999, 0.045024930000000005) v_micro_range=(0.0003839299999999999, 0.044701840000000014)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 6/20 ---\n", "2025-07-29T03:54:30.521457Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 72 代触发。\n", "2025-07-29T03:54:31.508420Z [info     ] PSO 优化完成。最终迭代次数: 72, 最优损失: 6.241548 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:31.518748Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:31.558886Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:31.579716Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08579144000000001) v_meso_range=(8.999999999999999e-08, 0.12591607) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T03:54:31.628335Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T03:54:32.710734Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 4.412106 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:32.735399Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:32.779565Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:32.803881Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(1.0999999999999998e-07, 0.053681910000000006) v_micro_range=(3.500000000000002e-07, 0.09835934000000002)\n", "2025-07-29T03:54:32.837795Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 137 代触发。\n", "2025-07-29T03:54:34.847512Z [info     ] PSO 优化完成。最终迭代次数: 137, 最优损失: 5.489872 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:34.879301Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:34.923351Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:34.940873Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.005208419999999999, 0.05667839) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 7/20 ---\n", "2025-07-29T03:54:34.977046Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 93 代触发。\n", "2025-07-29T03:54:36.258266Z [info     ] PSO 优化完成。最终迭代次数: 93, 最优损失: 4.750394 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:36.288995Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:36.336945Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:36.362223Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(6e-08, 0.09763175999999998) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T03:54:36.391038Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 93 代触发。\n", "2025-07-29T03:54:37.631581Z [info     ] PSO 优化完成。最终迭代次数: 93, 最优损失: 4.121881 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:37.671679Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:37.711866Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:37.744492Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:54:37.779414Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 151 代触发。\n", "2025-07-29T03:54:39.979781Z [info     ] PSO 优化完成。最终迭代次数: 151, 最优损失: 4.898758 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:40.017580Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:40.063833Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:40.091498Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.0012945899999999998, 0.043763380000000004) v_micro_range=(0.0026810799999999997, 0.05037252000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 8/20 ---\n", "2025-07-29T03:54:40.125690Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 137 代触发。\n", "2025-07-29T03:54:41.997436Z [info     ] PSO 优化完成。最终迭代次数: 137, 最优损失: 4.767613 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:42.013197Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:42.050446Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:42.081142Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(9.999999999999998e-08, 0.10611755) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T03:54:42.100178Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 77 代触发。\n", "2025-07-29T03:54:43.150807Z [info     ] PSO 优化完成。最终迭代次数: 77, 最优损失: 4.616794 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:43.176293Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:43.227468Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:43.266639Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.0999999999999998e-07, 0.08015219999999999) v_micro_range=(4.0000000000000025e-07, 0.09835938999999999)\n", "2025-07-29T03:54:43.309321Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 77 代触发。\n", "2025-07-29T03:54:44.501475Z [info     ] PSO 优化完成。最终迭代次数: 77, 最优损失: 5.400461 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:44.530372Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:44.571801Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:44.594534Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.11628647) v_meso_range=(0.003995079999999999, 0.06800896000000001) v_micro_range=(0.00268106, 0.047672050000000014)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 9/20 ---\n", "2025-07-29T03:54:44.634425Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 74 代触发。\n", "2025-07-29T03:54:45.659153Z [info     ] PSO 优化完成。最终迭代次数: 74, 最优损失: 6.694501 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:45.684668Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:45.713267Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:45.743192Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(3.0000000000000004e-08, 0.05623814) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T03:54:45.779349Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 114 代触发。\n", "2025-07-29T03:54:47.364239Z [info     ] PSO 优化完成。最终迭代次数: 114, 最优损失: 4.330780 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:47.379187Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:47.410723Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:47.424040Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08404873) v_meso_range=(8.999999999999999e-08, 0.05501709999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T03:54:47.463833Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T03:54:48.659959Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 5.474014 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:48.694770Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:48.730887Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:48.760508Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006891039999999999, 0.04502492) v_micro_range=(0.0005744599999999999, 0.044776070000000015)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 10/20 ---\n", "2025-07-29T03:54:48.799009Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 84 代触发。\n", "2025-07-29T03:54:49.940476Z [info     ] PSO 优化完成。最终迭代次数: 84, 最优损失: 6.121073 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:49.975566Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:50.012530Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:50.036011Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(6.999999999999999e-08, 0.10183186) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T03:54:50.081607Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T03:54:51.162713Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 4.484158 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:51.198919Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:51.230654Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:51.256302Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:54:51.289382Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 127 代触发。\n", "2025-07-29T03:54:53.094795Z [info     ] PSO 优化完成。最终迭代次数: 127, 最优损失: 5.419400 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:53.114693Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:53.146275Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:53.172931Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.006965279999999999, 0.05667842000000001) v_micro_range=(0.0003839299999999999, 0.044701840000000014)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11/20 ---\n", "2025-07-29T03:54:53.213981Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 153 代触发。\n", "2025-07-29T03:54:55.302394Z [info     ] PSO 优化完成。最终迭代次数: 153, 最优损失: 5.537617 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:55.322006Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:55.348575Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:55.384792Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(6.999999999999999e-08, 0.08132633) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T03:54:55.410670Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 167 代触发。\n", "2025-07-29T03:54:57.679072Z [info     ] PSO 优化完成。最终迭代次数: 167, 最优损失: 4.363230 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:57.694964Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:57.749807Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:57.777723Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(8.999999999999999e-08, 0.051865060000000004) v_micro_range=(3.700000000000002e-07, 0.09835936)\n", "2025-07-29T03:54:57.808176Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 122 代触发。\n", "2025-07-29T03:54:59.602870Z [info     ] PSO 优化完成。最终迭代次数: 122, 最优损失: 5.940511 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.29, 'cpu_percent': 0.0}\n", "2025-07-29T03:54:59.625085Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:59.656099Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:54:59.681539Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.29, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.003995059999999999, 0.045024890000000005) v_micro_range=(0.00268106, 0.047672050000000014)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 12/20 ---\n", "2025-07-29T03:54:59.722274Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.29, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 68 代触发。\n", "2025-07-29T03:55:00.679307Z [info     ] PSO 优化完成。最终迭代次数: 68, 最优损失: 5.490396 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:00.683332Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:00.727130Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:00.748700Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(1.2e-07, 0.10925294999999999) v_micro_range=(3.500000000000002e-07, 0.04422076000000001)\n", "2025-07-29T03:55:00.790104Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 188 代触发。\n", "2025-07-29T03:55:03.300159Z [info     ] PSO 优化完成。最终迭代次数: 188, 最优损失: 4.068739 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:03.329110Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:03.359042Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:03.386700Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:55:03.421770Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 97 代触发。\n", "2025-07-29T03:55:04.919016Z [info     ] PSO 优化完成。最终迭代次数: 97, 最优损失: 5.405527 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:04.944812Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:04.991435Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:05.021414Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006965289999999999, 0.045915580000000004) v_micro_range=(0.0003839099999999999, 0.04470182000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 13/20 ---\n", "2025-07-29T03:55:05.060311Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.3, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 137 代触发。\n", "2025-07-29T03:55:06.893363Z [info     ] PSO 优化完成。最终迭代次数: 137, 最优损失: 6.341154 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.31, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:06.931486Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:06.976892Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:07.015044Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.31, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(7.999999999999999e-08, 0.08247362999999999) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T03:55:07.050314Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.31, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 191 代触发。\n", "2025-07-29T03:55:09.596247Z [info     ] PSO 优化完成。最终迭代次数: 191, 最优损失: 4.399849 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:09.630491Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:09.674016Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:09.702245Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(6e-08, 0.04632677) v_micro_range=(4.0000000000000025e-07, 0.09835938999999999)\n", "2025-07-29T03:55:09.721883Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 78 代触发。\n", "2025-07-29T03:55:10.910302Z [info     ] PSO 优化完成。最终迭代次数: 78, 最优损失: 5.342497 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:10.931407Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:10.975197Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:10.991926Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.0012945999999999997, 0.05541687000000001) v_micro_range=(0.0026810799999999997, 0.05037252000000002)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 14/20 ---\n", "2025-07-29T03:55:11.022521Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 88 代触发。\n", "2025-07-29T03:55:12.252108Z [info     ] PSO 优化完成。最终迭代次数: 88, 最优损失: 6.313128 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:12.280195Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:12.329976Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:12.360275Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(1.0999999999999998e-07, 0.10750565999999998) v_micro_range=(3.600000000000002e-07, 0.04908202000000001)\n", "2025-07-29T03:55:12.393918Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T03:55:13.490920Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 4.320776 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:13.531407Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:13.567540Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:13.598352Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:55:13.639863Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 108 代触发。\n", "2025-07-29T03:55:15.280121Z [info     ] PSO 优化完成。最终迭代次数: 108, 最优损失: 5.347908 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:15.314533Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:15.365667Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:15.396041Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.11628647) v_meso_range=(0.006965299999999999, 0.06800901000000001) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 15/20 ---\n", "2025-07-29T03:55:15.419474Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 151 代触发。\n", "2025-07-29T03:55:17.514206Z [info     ] PSO 优化完成。最终迭代次数: 151, 最优损失: 4.468981 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:17.541862Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:17.577762Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:17.602385Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(1.2e-07, 0.08848487) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T03:55:17.635906Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.32, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 115 代触发。\n", "2025-07-29T03:55:19.212235Z [info     ] PSO 优化完成。最终迭代次数: 115, 最优损失: 4.642729 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:19.246582Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:19.278051Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:19.312376Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08404873) v_meso_range=(8.999999999999999e-08, 0.05501709999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T03:55:19.342543Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 100 代触发。\n", "2025-07-29T03:55:20.883610Z [info     ] PSO 优化完成。最终迭代次数: 100, 最优损失: 5.609975 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:20.898886Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:20.944932Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:20.967666Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.11628647) v_meso_range=(4e-08, 0.045430700000000004) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 16/20 ---\n", "2025-07-29T03:55:21.011369Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 118 代触发。\n", "2025-07-29T03:55:22.605658Z [info     ] PSO 优化完成。最终迭代次数: 118, 最优损失: 5.217432 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:22.638496Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.33, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:22.676735Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.34, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:22.708627Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.35, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08579144000000001) v_meso_range=(6.999999999999999e-08, 0.11960896999999998) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T03:55:22.754931Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.4, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 166 代触发。\n", "2025-07-29T03:55:25.062548Z [info     ] PSO 优化完成。最终迭代次数: 166, 最优损失: 4.458371 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.4, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:25.090728Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.4, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:25.131298Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:25.165196Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(8.999999999999999e-08, 0.051865060000000004) v_micro_range=(3.700000000000002e-07, 0.09835936)\n", "2025-07-29T03:55:25.199984Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 150 代触发。\n", "2025-07-29T03:55:27.440480Z [info     ] PSO 优化完成。最终迭代次数: 150, 最优损失: 5.901531 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:27.456437Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:27.502385Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:27.533818Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.0012945899999999998, 0.043763380000000004) v_micro_range=(0.0026810799999999997, 0.05037252000000002)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 17/20 ---\n", "2025-07-29T03:55:27.572984Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 126 代触发。\n", "2025-07-29T03:55:29.291048Z [info     ] PSO 优化完成。最终迭代次数: 126, 最优损失: 5.886233 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:29.306901Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:29.361802Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:29.386140Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(3.0000000000000004e-08, 0.05623814) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T03:55:29.415666Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.42, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 206 代触发。\n", "2025-07-29T03:55:32.204270Z [info     ] PSO 优化完成。最终迭代次数: 206, 最优损失: 4.264366 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.43, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:32.239786Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:32.279614Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:32.309128Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.46, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(7.999999999999999e-08, 0.05063158) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T03:55:32.334234Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.46, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 148 代触发。\n", "2025-07-29T03:55:34.602741Z [info     ] PSO 优化完成。最终迭代次数: 148, 最优损失: 4.945202 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.56, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:34.608779Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.56, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:34.653884Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.56, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:34.696999Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.56, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.00041007, 0.03927055) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 18/20 ---\n", "2025-07-29T03:55:34.752684Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.56, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 116 代触发。\n", "2025-07-29T03:55:36.363060Z [info     ] PSO 优化完成。最终迭代次数: 116, 最优损失: 6.132910 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:36.392115Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:36.430932Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:36.461723Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08579144000000001) v_meso_range=(8.999999999999999e-08, 0.12591607) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T03:55:36.494380Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 207 代触发。\n", "2025-07-29T03:55:39.356994Z [info     ] PSO 优化完成。最终迭代次数: 207, 最优损失: 3.519957 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:39.388758Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:39.429896Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:39.467138Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(9.999999999999998e-08, 0.05337395) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:55:39.495202Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 97 代触发。\n", "2025-07-29T03:55:40.988725Z [info     ] PSO 优化完成。最终迭代次数: 97, 最优损失: 5.375620 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:41.005174Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:41.050310Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:41.076151Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006965289999999999, 0.045915580000000004) v_micro_range=(0.0003839099999999999, 0.04470182000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 19/20 ---\n", "2025-07-29T03:55:41.109921Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 108 代触发。\n", "2025-07-29T03:55:42.635780Z [info     ] PSO 优化完成。最终迭代次数: 108, 最优损失: 5.100515 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:42.660854Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:42.688719Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:42.713283Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.10089872999999999) v_meso_range=(4e-08, 0.06761289000000001) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T03:55:42.748226Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T03:55:43.846375Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 4.595543 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:43.877740Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:43.923119Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:43.954039Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.0952323) v_meso_range=(1.0999999999999998e-07, 0.053681910000000006) v_micro_range=(3.500000000000002e-07, 0.09835934000000002)\n", "2025-07-29T03:55:43.989620Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T03:55:45.253039Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 5.736599 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:45.267917Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:45.315020Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:45.348085Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(8.999999999999999e-08, 0.13400318) v_meso_range=(0.006891039999999999, 0.04502492) v_micro_range=(0.0005744599999999999, 0.044776070000000015)\n", "--- Boots<PERSON><PERSON>-<PERSON><PERSON> 20/20 ---\n", "2025-07-29T03:55:45.391142Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 129 代触发。\n", "2025-07-29T03:55:47.143269Z [info     ] PSO 优化完成。最终迭代次数: 129, 最优损失: 6.564090 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:47.179340Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:47.217620Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:47.245798Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.08757133) v_meso_range=(1.2e-07, 0.10925294999999999) v_micro_range=(3.500000000000002e-07, 0.04422076000000001)\n", "2025-07-29T03:55:47.297688Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 90 代触发。\n", "2025-07-29T03:55:48.588742Z [info     ] PSO 优化完成。最终迭代次数: 90, 最优损失: 5.106599 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:48.611054Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:48.643710Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:48.685009Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.08404873) v_meso_range=(6e-08, 0.049380650000000005) v_micro_range=(0.0008690600000000001, 0.09878561)\n", "2025-07-29T03:55:48.725297Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 103 代触发。\n", "2025-07-29T03:55:50.336624Z [info     ] PSO 优化完成。最终迭代次数: 103, 最优损失: 5.247555 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:50.367057Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:50.402187Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.67, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:50.436853Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.67, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(7.999999999999999e-08, 0.12541036) v_meso_range=(0.0012945999999999997, 0.05541687000000001) v_micro_range=(0.0026810799999999997, 0.05037252000000002)\n", "2025-07-29T03:55:50.475543Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 443.11, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 25 代触发。\n", "2025-07-29T03:55:50.751866Z [info     ] PSO 优化完成。最终迭代次数: 25, 最优损失: 4.910222 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.33, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:50.775203Z [info     ] Fine-Tuning阶段完成，最终损失: 4.910222 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.33, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:50.797314Z [info     ] SWIFT-PSO 优化完成。                [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.33, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:50.817898Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.models.final_parameters artifact_path=swift_pso_training\\final_parameters.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.33, 'cpu_percent': 0.0} description=最终优化后的模型参数及上下文，可直接用于预测步骤。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:50.856902Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.datasets.all_parameters_from_lowo artifact_path=swift_pso_training\\all_parameters_from_lowo.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} description=所有Bootstrap+LOWO迭代产生的优化参数集，用于t-SNE可视化分析。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:50.875733Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.convergence_history_finetune artifact_path=swift_pso_training\\convergence_history_finetune.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} description=Fine-Tuning阶段的损失函数收敛历史。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:50.913437Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.bootstrap_summary artifact_path=swift_pso_training\\summary_bootstrap_mu_rmse.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} description=每轮Bootstrap的平均验证误差（mu_rmse），用于确定最佳轮次b*。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:50.942644Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.params_warm_start artifact_path=swift_pso_training\\params_warm_start.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} description=用于启动Fine-Tuning阶段的热启动参数（theta_hat_star）。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:50.967933Z [info     ] SWIFT-PSO训练步骤完成                [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} final_loss=4.91022200919841 operation=swift_pso_training_step\n", "✅ [Step 1/5] 训练完成！ 结果: {'status': 'completed', 'final_loss': 4.91022200919841}\n", "   - 最终模型参数已保存为产物: swift_pso_training.models.final_parameters\n", "   - t-SNE源数据已保存为产物: swift_pso_training.datasets.all_parameters_from_lowo\n"]}], "source": ["# 1. 使用 Pydantic 模型创建配置\n", "training_config = SwiftPsoTrainingConfig.create_default()\n", "\n", "training_config.enable_fold_diagnostics = True\n", "training_config.optimization_params=[\n", "            'log10_KSDR_A', 'PHIT_EXP', 'RHO_NMR', 'log10_KMACRO_A',\n", "            'log10_T2cutoff_short', 'log10_T2cutoff_long',\n", "            'beta_1', 'beta_2', 'delta_MDT'\n", "        ]\n", "\n", "training_config.fixed_params['T2LM_EXP'] = 2.0\n", "training_config.fixed_params['KMACRO_B'] = 2.0\n", "training_config.fixed_params['Vmacro_min'] = 0.01\n", "\n", "# 2. 注入数据依赖参数\n", "t2_p50_ref = train_bundle.data['T2_P50'].median()\n", "phit_nmr_ref = train_bundle.data['PHIT_NMR'].median()\n", "\n", "training_config.random_seed = 2000\n", "training_config.pso_config_lowo['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_lowo['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_lowo['t2_time'] = t2_time_array\n", "training_config.pso_config_lowo['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_lowo['t2_range_min'] = 0.1\n", "training_config.pso_config_lowo['t2_range_max'] = 8000\n", "training_config.pso_config_lowo['parameters_boundaries']['log10_T2cutoff_long'] = (2.6, 2.65, 3.45, 3.5)\n", "\n", "training_config.pso_config_finetune['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_finetune['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_finetune['t2_time'] = t2_time_array\n", "training_config.pso_config_finetune['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_finetune['t2_range_min'] = 0.1\n", "training_config.pso_config_finetune['t2_range_max'] = 8000\n", "training_config.pso_config_finetune['parameters_boundaries']['log10_T2cutoff_long'] = (2.6, 2.65, 3.45, 3.5)\n", "\n", "# 3. 调整执行参数\n", "training_config.bootstrap_iterations = 20\n", "training_config.pso_config_lowo[\"max_iterations\"] = 400\n", "training_config.pso_config_lowo[\"n_particles\"] = 150\n", "training_config.pso_config_finetune[\"max_iterations\"] = 200\n", "training_config.pso_config_finetune[\"n_particles\"] = 100\n", "\n", "# 4. 执行训练步骤\n", "print(\"🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\")\n", "training_result = run_swift_pso_training_step(\n", "    config=training_config,\n", "    ctx=run_context, # 直接传递上下文对象\n", "    train_bundle=train_bundle,\n", "    backend='gpu'  # 执行层参数直接传入\n", ")\n", "\n", "print(f\"✅ [Step 1/5] 训练完成！ 结果: {training_result}\")\n", "print(f\"   - 最终模型参数已保存为产物: {SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value}\")\n", "print(f\"   - t-SNE源数据已保存为产物: {SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value}\")"]}, {"cell_type": "markdown", "id": "b1c2d3e4", "metadata": {}, "source": ["## 4. 步骤二：模型预测\n", "\n", "使用 `run_swift_pso_prediction_step` 对训练集和应用集进行预测。我们首先从 `RunContext` 中显式加载上一步训练产出的模型，然后将其作为参数传入预测函数。"]}, {"cell_type": "code", "execution_count": 5, "id": "f5g6h7i8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:55:51.084509Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T03:55:51.106195Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx file_size_mb=1.88 sheet_count=1\n", "2025-07-29T03:55:51.124966Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T03:55:51.143171Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T03:55:51.174375Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.2, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:55:51.201488Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.21, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=8 well_curves=1\n", "2025-07-29T03:55:55.113737Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 461.09, 'cpu_percent': 0.0} shape=(4689, 71) sheet_name=swift_pso_apply_cleaned\n", "2025-07-29T03:55:55.152271Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 461.09, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned')\n", "2025-07-29T03:55:55.168112Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 461.09, 'cpu_percent': 0.0} curve_count=8 dataset_name=swift_pso_apply_cleaned dataset_type=Continuous df_shape=(4689, 71) processing_time=4.006\n", "2025-07-29T03:55:55.200050Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 461.09, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:55:55.215946Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 461.09, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:55:55.231686Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 461.09, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=4.147 project_name=WpIdentifier('scape_swift_pso_apply_cleaned')\n", "✅ 成功读取应用数据: ./scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T03:55:55.263701Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.06, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=8 input_curves=['DPHIT_NMR', 'PHIT_NMR', 'PHI_T2_DIST', 'MD', 'T2LM', 'T2_P50', 'WELL_NO', 'DT2_P50'] operation=extract_metadata output_curve_count=8 output_curves=['DPHIT_NMR', 'PHIT_NMR', 'PHI_T2_DIST', 'MD', 'T2LM', 'T2_P50', 'WELL_NO', 'DT2_P50']\n", "2025-07-29T03:55:55.291759Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.06, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T03:55:55.307551Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.06, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T03:55:55.326580Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.06, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T03:55:55.342215Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.06, 'cpu_percent': 0.0}\n"]}], "source": ["# --- 加载应用数据 ---\n", "apply_data_path = \"./scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "apply_project = reader.read(apply_data_path)\n", "print(f\"✅ 成功读取应用数据: {apply_data_path}\")\n", "\n", "apply_bundle = apply_project.get_dataset(\"swift_pso_apply_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR', 'PHI_T2_DIST'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "\n", "# --- 准备预测配置 (输出曲线名将作为facade函数的直接参数传入) ---\n", "prediction_config = SwiftPsoPredictionConfig.create_default()"]}, {"cell_type": "code", "execution_count": 6, "id": "j9k0l1m2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 2.1/5] 开始对训练集进行预测...\n", "\n", "================================================================================\n", "                            SWIFT-PSO 模型产物摘要\n", "================================================================================\n", "\n", "--- 优化参数 (Optimized Parameters) ---\n", "                 参数名      数值    线性域数值\n", "        log10_KSDR_A -1.7718   0.0169\n", "            PHIT_EXP  4.7401      N/A\n", "             RHO_NMR  5.0737      N/A\n", "      log10_KMACRO_A -1.1792   0.0662\n", "log10_T2cutoff_short  2.1119 129.4024\n", " log10_T2cutoff_long  2.6821 480.9968\n", "              beta_1  0.3857      N/A\n", "              beta_2  0.6990      N/A\n", "           delta_MDT -0.4120      N/A\n", "\n", "--- 固定参数 (Fixed Parameters) ---\n", "       参数名     数值 线性域数值\n", "  T2LM_EXP 2.0000   N/A\n", "  KMACRO_B 2.0000   N/A\n", "Vmacro_min 0.0100   N/A\n", "\n", "--- 上下文 (Context) ---\n", "  - t2_p50_ref: 309.2900\n", "  - phit_nmr_ref: 0.0683\n", "\n", "================================================================================\n", "2025-07-29T03:55:55.462458Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.07, 'cpu_percent': 0.0} data_rows=408 operation=swift_pso_prediction_step run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:55.494169Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.07, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T03:55:55.514737Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.07, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:55:55.525770Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.07, 'cpu_percent': 0.0} data_rows=408 operation=swift_pso_prediction\n", "2025-07-29T03:55:55.541545Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.07, 'cpu_percent': 0.0} n_bins=64 n_depths=408 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.13441199) v_meso_range=(6.999999999999999e-08, 0.10183186) v_micro_range=(4.0000000000000025e-07, 0.09835938999999999)\n", "2025-07-29T03:55:55.582869Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.07, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(3.682456622882737e-08, 83.88916901619496) result_rows=408\n", "2025-07-29T03:55:55.607480Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:55:55.625138Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:55:55.647469Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:55:55.657009Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:55:55.687847Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:55:55.705021Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:55.728164Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=408\n", "✅ 训练集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n", "🚀 [Step 2.2/5] 开始对应用集进行预测...\n", "2025-07-29T03:55:55.763371Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction_step run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:55.790936Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T03:55:55.805507Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:55:55.815432Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction\n", "2025-07-29T03:55:55.832142Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 468.38, 'cpu_percent': 0.0} n_bins=64 n_depths=4689 operation=nmr_compute stage=complete v_macro_range=(6.999999999999999e-08, 0.14151097) v_meso_range=(6.999999999999999e-08, 0.10397020999999998) v_micro_range=(4.0000000000000025e-07, 0.2788430799999999)\n", "2025-07-29T03:55:55.863800Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(6.4172319475373715e-12, 33.114879550600584) result_rows=4689\n", "2025-07-29T03:55:55.879658Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:55:55.895582Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:55:55.927321Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:55:55.943060Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:55:55.958913Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.08, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:55:56.066854Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:55:56.085947Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.16, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=4689\n", "2025-07-29T03:55:56.101881Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.16, 'cpu_percent': 0.0} copy_data=False new_dataset_name=train_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:55:56.117909Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T03:55:56.149602Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=train_apply dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:55:56.173453Z [info     ] 成功添加 'train_apply' (WpDiscreteDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:55:56.191258Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} copy_data=False new_dataset_name=pred_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:55:56.205656Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:55:56.221312Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=pred_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:55:56.237374Z [info     ] 成功添加 'pred_apply' (WpContinuousDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:55:56.269147Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} dataset_count=2 file_path=output01\\swift_pso_run_20250729_115400\\swift_pso_apply_result.wp.xlsx project_name=WpIdentifier('swift_pso_apply_result') save_head_info=True save_well_map=True\n", "2025-07-29T03:55:56.301005Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.2, 'cpu_percent': 0.0} curve_count=15 dataset_name=WpIdentifier('train_apply') dataset_type=Point df_shape=(408, 78)\n", "2025-07-29T03:55:56.539987Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.46, 'cpu_percent': 0.0} dataset_name=WpIdentifier('train_apply') processing_time=0.239\n", "2025-07-29T03:55:56.571498Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.46, 'cpu_percent': 0.0} curve_count=12 dataset_name=WpIdentifier('pred_apply') dataset_type=Continuous df_shape=(4689, 75)\n", "2025-07-29T03:55:58.190067Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.45, 'cpu_percent': 0.0} dataset_name=WpIdentifier('pred_apply') processing_time=1.619\n", "2025-07-29T03:55:58.225417Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.81, 'cpu_percent': 0.0} dataset_count=2 head_info=False total_sheets=2 well_map=False\n", "2025-07-29T03:55:58.237304Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.82, 'cpu_percent': 0.0}\n", "2025-07-29T03:55:58.253126Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.82, 'cpu_percent': 0.0} workbook_sheets=2\n", "2025-07-29T03:56:29.926620Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.53, 'cpu_percent': 0.0}\n", "2025-07-29T03:56:29.952143Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.54, 'cpu_percent': 0.0}\n", "2025-07-29T03:56:35.951788Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.89, 'cpu_percent': 0.0} file_path=output01\\swift_pso_run_20250729_115400\\swift_pso_apply_result.wp.xlsx processing_time=39.683 project_name=WpIdentifier('swift_pso_apply_result')\n", "✅ 应用集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n"]}], "source": ["# --- 对训练集进行预测 ---\n", "print(\"🚀 [Step 2.1/5] 开始对训练集进行预测...\")\n", "# 1. 实例化产物处理器\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "# 2. 从RunContext加载上一步训练产出的模型参数\n", "model_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.FINAL_PARAMETERS)\n", "model_assets = handler.load_parameters(model_params_path)\n", "handler.print_model_assets_human_readable(model_assets)\n", "\n", "# 3. 调用预测步骤，并传入加载的模型\n", "train_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets,  # 传入加载的模型\n", "    prediction_bundle=train_bundle, # 使用训练数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu' # 预测通常在CPU上进行\n", ")\n", "\n", "# 获取带预测结果的bundle，以备后续步骤使用\n", "train_bundle_with_pred = train_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 训练集预测完成！新增曲线: {train_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")\n", "\n", "# --- 对应用集进行预测 ---\n", "print(\"🚀 [Step 2.2/5] 开始对应用集进行预测...\")\n", "# 模型参数只需加载一次，可复用\n", "apply_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets, # 传入加载的模型\n", "    prediction_bundle=apply_bundle, # 使用应用数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu'\n", ")\n", "\n", "temp_project = WpWellProject(name=\"swift_pso_apply_result\")\n", "temp_project.add_dataframe_bundle(\"train_apply\",train_bundle)\n", "temp_project.add_dataframe_bundle(\"pred_apply\",apply_bundle)\n", "\n", "writer = WpExcelWriter()\n", "apply_result_path = output_dir / run_dir_name / \"swift_pso_apply_result.wp.xlsx\"\n", "writer.write(temp_project, apply_result_path, apply_formatting=True)\n", "\n", "apply_bundle_with_pred = apply_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 应用集预测完成！新增曲线: {apply_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")"]}, {"cell_type": "markdown", "id": "1af960c4", "metadata": {}, "source": ["## 训练集上渗透率交会图"]}, {"cell_type": "code", "execution_count": 7, "id": "e07ee165", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:56:36.061869Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.89, 'cpu_percent': 0.0} step_name=train_all_perm_corr_analysis\n", "2025-07-29T03:56:36.084777Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.9, 'cpu_percent': 0.0}\n", "2025-07-29T03:56:36.087956Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.9, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:56:36.114634Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.1, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:36.141673Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.1, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:36.159143Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.1, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:36.181736Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.1, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:36.236027Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.5, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:36.261496Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_115400\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.55, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:37.840898Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.04, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:56:37.864179Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:37.901404Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.04, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:56:37.925027Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.05, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:37.951130Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.05, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:37.966423Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.05, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:37.997165Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.05, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:38.040665Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:38.064746Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_115400\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 614.17, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:39.463814Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.27, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:56:39.497938Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:39.531466Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.27, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T03:56:39.558567Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.27, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:39.571820Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.27, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:39.598294Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:39.612569Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:39.659959Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.33, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:39.693779Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_115400\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.4, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:41.072314Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.63, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T03:56:41.104688Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.63, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:41.135748Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.64, 'cpu_percent': 0.0}\n", "  - TRAIN_ALL 模型验证完成。\n", "\n", "2025-07-29T03:56:41.151672Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.64, 'cpu_percent': 0.0} step_name=train_pz_perm_corr_analysis\n", "2025-07-29T03:56:41.167390Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.65, 'cpu_percent': 0.0}\n", "2025-07-29T03:56:41.192596Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.65, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:56:41.218703Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.68, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:41.230564Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.69, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:41.251749Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:41.275874Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.71, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:41.307434Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.74, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:41.342757Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_115400\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.74, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:43.093718Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.62, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:56:43.125350Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.62, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:43.146120Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.62, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:56:43.177643Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.63, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:43.212045Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.64, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:43.240732Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:43.263413Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:43.302993Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.64, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:43.312386Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_115400\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.64, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:44.732610Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:56:44.758252Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:44.783923Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T03:56:44.797278Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:44.828990Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:44.844642Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:44.871657Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:44.900179Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:44.932002Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_115400\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.78, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:46.294102Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T03:56:46.341852Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:46.357679Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0}\n", "  - TRAIN_PZ 模型验证完成。\n", "\n", "2025-07-29T03:56:46.373714Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} step_name=train_core_perm_corr_analysis\n", "2025-07-29T03:56:46.389542Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0}\n", "2025-07-29T03:56:46.405481Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:56:46.436038Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:46.470639Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:46.484312Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:46.516012Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:46.550575Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.52, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:46.572382Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_115400\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.52, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:48.095063Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.64, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:56:48.142555Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:48.158335Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.64, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:56:48.190358Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.64, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:48.218515Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.64, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:48.242363Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:48.269809Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:48.305293Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.65, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:48.321060Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_115400\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.65, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:49.728450Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:56:49.765650Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:49.799238Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE 模型验证完成。\n", "\n", "2025-07-29T03:56:49.825810Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} step_name=train_core_pz_perm_corr_analysis\n", "2025-07-29T03:56:49.839031Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0}\n", "2025-07-29T03:56:49.854680Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:56:49.870351Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:49.886209Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:49.902110Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:49.937374Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.9, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:49.972603Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.94, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:50.003997Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_115400\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.94, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:51.342475Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.24, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:56:51.357823Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:51.389314Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.24, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:56:51.422595Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.26, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:51.453112Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.26, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:51.466462Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:51.498158Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:51.529868Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.28, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:56:51.545773Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_115400\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.28, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:56:52.911264Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.86, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_115400\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:56:52.949100Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:56:52.978948Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.86, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE_PZ 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"train_all\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.41866212814178727,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 24.0% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 42.9% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6046855960760017,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 14.4% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 29.7% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.7059487794933711,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 35.5% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 58.1% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.4017402437035714,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 41.8% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 70.4% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6437977960049188,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 24.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 54.3% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6465043727168083,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 42.9% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 71.4% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.37779210773058924,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 26.2% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.1% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6166586557100192,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 13.5% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 33.3% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.37779210773058924,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 26.2% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.1% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6166586557100192,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 13.5% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 33.3% <= 90%\\u3002\"\n", "    }\n", "  }\n", "}\n"]}], "source": ["models_to_perm_corr = {\n", "    \"train_all\": train_label_all_bundle,\n", "    \"train_pz\": train_label_pz_bundle,\n", "    \"train_core\": train_label_core_bundle,\n", "    \"train_core_pz\": train_label_core_bundle,\n", "}\n", "\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_results = {}\n", "for model_prefix, label_bundle in models_to_perm_corr.items():\n", "    perm_corr_config = PermCorrelationConfig()\n", "    perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": f\"Core Perm vs. Predicted Perm ({model_prefix.upper()})\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", "    )\n", "    perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=train_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=label_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=model_prefix\n", "    )\n", "    perm_corr_results[model_prefix] = perm_corr_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(perm_corr_results, indent=2))"]}, {"cell_type": "markdown", "id": "n3o4p5q6", "metadata": {}, "source": ["## 5. 步骤三：t-SNE 可视化\n", "\n", "调用 `run_tsne_visualization_step` 对训练过程中的参数演化进行可视化。此步骤会自动消费训练步骤产出的 `ALL_OPTIMIZED_PARAMETERS` 产物。\n", "\n", "我们采用 **Get -> Modify -> Pass** 模式来定制图表：\n", "1.  **Get**: 从全局注册表 `plot_registry` 获取一个默认的 `PlotProfile` 模板。\n", "2.  **Modify**: 在运行时动态修改模板的属性（如标题）。\n", "3.  **Pass**: 将修改后的 `PlotProfile` 对象传入 `facade` 函数。"]}, {"cell_type": "code", "execution_count": 8, "id": "r7s8t9u0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 3/5] 开始执行 t-SNE 可视化...\n", "2025-07-29T03:56:53.434999Z [info     ] 开始t-SNE可视化步骤                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 542.15, 'cpu_percent': 0.0} custom_profiles=['swift_pso.tsne_convergence', 'swift_pso.tsne_cluster_analysis'] operation=tsne_visualization_step run_id=20250729-035400-3b1f6242 source_data_rows=734\n", "2025-07-29T03:56:53.466623Z [info     ] 开始执行收敛轨迹分析                     [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 542.17, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T03:56:53.482285Z [info     ] 开始t-SNE降维计算                    [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 542.18, 'cpu_percent': 0.0} data_rows=734 operation=tsne_computation perplexity=15\n", "2025-07-29T03:56:53.800174Z [info     ] 执行t-SNE降维                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.94, 'cpu_percent': 0.0} init=pca learning_rate=200.0 max_iter=2000 n_components=2 operation=tsne_computation perplexity=15 random_state=42 verbose=0\n", "2025-07-29T03:57:03.604293Z [info     ] 在原始高维空间上执行K-means聚类            [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 506.94, 'cpu_percent': 0.0} n_clusters=4 operation=tsne_computation\n", "2025-07-29T03:57:03.830703Z [info     ] 聚类轮廓系数 (Silhouette Score): 0.2312 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.5, 'cpu_percent': 0.0}\n", "2025-07-29T03:57:03.848558Z [info     ] t-SNE降维和聚类计算完成                 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.5, 'cpu_percent': 0.0} operation=tsne_computation result_rows=734 tsne_x_range=(-856.01123046875, 891.700439453125) tsne_y_range=(-915.1416015625, 807.824951171875)\n", "2025-07-29T03:57:03.891128Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.31, 'cpu_percent': 0.0} description=t-SNE收敛轨迹的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:03.916075Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.31, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization\\tsne_convergence_trajectory_profile.json profile_name=swift_pso.tsne_convergence\n", "2025-07-29T03:57:03.924506Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 508.31, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization\\tsne_convergence_trajectory_plot.png profile_name=swift_pso.tsne_convergence snapshot_path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization\\tsne_convergence_trajectory_data.csv\n", "2025-07-29T03:57:03.977897Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.43, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_convergence\n", "2025-07-29T03:57:04.072923Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_convergence_trajectory_plot base_path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.7, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:57:05.774344Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 576.79, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T03:57:05.806074Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 576.79, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T03:57:05.837903Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 576.79, 'cpu_percent': 0.0} description=SWIFT-PSO参数演化轨迹的t-SNE可视化图表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:05.869327Z [info     ] 开始执行最终收敛点聚类分析                  [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 576.79, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T03:57:05.886535Z [info     ] 开始最终收敛点的聚类分析                   [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 576.79, 'cpu_percent': 0.0} operation=cluster_analysis_computation total_points=734\n", "2025-07-29T03:57:05.916585Z [info     ] 筛选出 60 个最终收敛点进行分析。             [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 576.79, 'cpu_percent': 0.0}\n", "2025-07-29T03:57:06.418852Z [info     ] 执行聚类分析，方法: kmeans              [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.25, 'cpu_percent': 0.0} operation=cluster_analysis_computation\n", "2025-07-29T03:57:06.477441Z [info     ] 聚类分析计算完成。                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.35, 'cpu_percent': 0.0}\n", "2025-07-29T03:57:06.501075Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.35, 'cpu_percent': 0.0} description=最终收敛点聚类分析的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:06.524865Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.35, 'cpu_percent': 0.0} description=聚类分析的总体量化指标报告，包括轮廓系数、簇心等。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:06.554950Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_statistics artifact_path=swift_pso_visualization\\tsne_cluster_statistics_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.35, 'cpu_percent': 0.0} description=每个簇内部所有参数的详细统计信息（均值、标准差等）。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:06.586818Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_summary_table artifact_path=swift_pso_visualization\\tsne_cluster_summary_table.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.39, 'cpu_percent': 0.0} description=各簇参数均值和标准差的对比摘要表，便于物理意义解释。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:06.618747Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.39, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization\\tsne_cluster_analysis_profile.json profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T03:57:06.649928Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.39, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization\\tsne_cluster_analysis_plot.png profile_name=swift_pso.tsne_cluster_analysis snapshot_path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization\\tsne_cluster_analysis_data.csv\n", "2025-07-29T03:57:06.681822Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.45, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T03:57:06.729497Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_cluster_analysis_plot base_path=output01\\swift_pso_run_20250729_115400\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.83, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:57:08.193214Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.09, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T03:57:08.224240Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.09, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_115400\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T03:57:08.255682Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.09, 'cpu_percent': 0.0} description=SWIFT-PSO最终收敛点的t-SNE聚类分析图表（带凸包）。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:08.287528Z [info     ] t-SNE可视化步骤完成                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.09, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "✅ [Step 3/5] t-SNE 可视化完成！结果: {'status': 'completed', 'clusters_found': 4}\n"]}], "source": ["# 1. 创建可视化配置 (保持不变)\n", "tsne_config = TsneVisualConfig(\n", "    perplexity=15,\n", "    n_iter=2000,\n", "    random_state=42,\n", "    cluster_method=\"kmeans\",\n", "    n_clusters=4,\n", "    dbscan_eps=1,\n", "    dbscan_min_samples=5\n", ")\n", "\n", "# 2. 获取并修改两个绘图配置，然后打包成字典\n", "# 2.1 收敛轨迹图的配置\n", "trajectory_profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Parameter Evolution (Trajectory)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.2 聚类分析图的配置\n", "cluster_profile = plot_registry.get(TsnePlotProfiles.CLUSTER_ANALYSIS).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Final Parameters (Cluster Analysis)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.3 打包成字典\n", "custom_plot_profiles = {\n", "    TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value: trajectory_profile,\n", "    TsnePlotProfiles.CLUSTER_ANALYSIS.value: cluster_profile\n", "}\n", "\n", "# 3. 执行可视化步骤 (加载数据的部分保持不变)\n", "print(\"🚀 [Step 3/5] 开始执行 t-SNE 可视化...\")\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "tsne_source_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)\n", "tsne_source_data = handler.load_dataframe(tsne_source_path)\n", "\n", "# 3.1 更新函数调用\n", "tsne_result = run_tsne_visualization_step(\n", "    config=tsne_config,\n", "    ctx=run_context,\n", "    tsne_source_data=tsne_source_data,\n", "    plot_profiles=custom_plot_profiles # <--- 修改此处\n", ")\n", "\n", "# 4. 更新打印的产物信息\n", "print(f\"✅ [Step 3/5] t-SNE 可视化完成！结果: {tsne_result}\")"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["## 6. 步骤四：PLT 盲井检验\n", "\n", "调用 `run_plt_analysis_step` 对模型的预测结果进行PLT盲井检验。此步骤会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物（包含在 `apply_bundle_with_pred` 中）。\n", "\n", "我们为不同的图表（贡献率交会图、捕获率曲线、洛伦兹曲线）分别获取并定制 `PlotProfile`，然后将它们打包成一个字典传入。"]}, {"cell_type": "code", "execution_count": 9, "id": "w5x6y7z8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:57:08.397019Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.11, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx\n", "2025-07-29T03:57:08.434129Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.11, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T03:57:08.464573Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.11, 'cpu_percent': 0.0} project_name=scape_plt_val\n", "2025-07-29T03:57:08.480225Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.11, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_plt_val\n", "2025-07-29T03:57:08.512132Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.11, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T03:57:08.528121Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.11, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:57:08.567926Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.11, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:57:08.600976Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T03:57:08.615694Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T03:57:08.632810Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} shape=(14, 4) sheet_name=PLT\n", "2025-07-29T03:57:08.648766Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T03:57:08.664836Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(14, 4) processing_time=0.081\n", "2025-07-29T03:57:08.687545Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:57:08.703472Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:57:08.719591Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx processing_time=0.323 project_name=WpIdentifier('scape_plt_val')\n", "2025-07-29T03:57:08.735315Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} extracted_curve_count=1 operation=extract_curve_dataframe_bundle\n", "2025-07-29T03:57:08.766920Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['MD_Bottom', 'QOZI', 'WELL_NO', 'MD_Top'] operation=extract_metadata output_curve_count=4 output_curves=['MD_Bottom', 'QOZI', 'WELL_NO', 'MD_Top']\n", "✅ 成功读取PLT验证数据: ./scape_plt_val.wp.xlsx\n", "🚀 [Step 4/5] 开始执行 PLT 盲井检验...\n", "2025-07-29T03:57:08.797902Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} step_name=plt_analysis\n", "2025-07-29T03:57:08.813912Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:57:08.813912Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:57:08.833035Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:08.862354Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:08.893880Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:08.931816Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:08.959270Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:08.998256Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:09.026090Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:09.063450Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.13, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.086733Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.13, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:57:09.101648Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.132992Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.180394Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.13, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:57:09.211555Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\swift_pso_run_20250729_115400\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 645.13, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:57:09.469153Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.88, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T03:57:09.500927Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.88, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.532719Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.88, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T03:57:09.548358Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.579783Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.627398Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.89, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T03:57:09.643366Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\swift_pso_run_20250729_115400\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.89, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:57:09.886217Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 650.66, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T03:57:09.913956Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 650.66, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.944975Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 650.66, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:57:09.961897Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 650.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:09.993033Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 650.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:10.038226Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 650.66, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:57:10.049978Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\swift_pso_run_20250729_115400\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 650.66, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:57:10.271580Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.47, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T03:57:10.315577Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.47, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:10.351003Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.47, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:57:10.366749Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:10.398317Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:10.445844Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:10.477429Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:10.502742Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:10.526896Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:10.574592Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:57:10.622265Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:10.660322Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:57:10.676223Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:10.692017Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:10.723829Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:57:10.739611Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\swift_pso_run_20250729_115400\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.5, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:57:11.026225Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.25, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T03:57:11.058044Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.25, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:11.087215Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.25, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T03:57:11.100456Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:11.132465Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:11.180165Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T03:57:11.196281Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\swift_pso_run_20250729_115400\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.25, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:57:11.450710Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.02, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T03:57:11.482232Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.02, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:11.513606Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.02, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:57:11.529326Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:11.561355Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:11.609110Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.02, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:57:11.624883Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\swift_pso_run_20250729_115400\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.02, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:57:11.843848Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.82, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T03:57:11.886961Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.82, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:11.907495Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.82, 'cpu_percent': 0.0}\n", "✅ [Step 4/5] PLT 检验完成！结果: {'C-1': {'spearman_rho': 0.5714285714285715, 'gini_capture': 0.39536310102506333, 'gini_lorenz': 0.15414245736862664}, 'C-2': {'spearman_rho': 0.3571428571428572, 'gini_capture': -0.050207321282104145, 'gini_lorenz': 0.35010741292632197}}\n", "   - 分析报告已保存为产物: plt_analysis.reports.analyzed_layers_* \n"]}], "source": ["# --- 加载PLT验证数据 ---\n", "plt_val_path = \"./scape_plt_val.wp.xlsx\"\n", "plt_val_project = reader.read(plt_val_path)\n", "plt_val_bundle = plt_val_project.get_dataset(\"PLT\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取PLT验证数据: {plt_val_path}\")\n", "\n", "# 1. 创建PLT分析配置 (曲线名将作为facade函数的直接参数传入)\n", "plt_config = PltAnalysisConfig()\n", "\n", "# 2. 获取并修改多个绘图配置 (Get -> Modify -> Pass)\n", "contribution_profile = plot_registry.get(PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT).with_updates(\n", "    title_props={\"label\": \"Flow Contribution Crossplot\"}\n", ")\n", "\n", "capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE).with_updates(\n", "    title_props={\"label\": \"Permeability Capture Curve\"}\n", ")\n", "\n", "lorenz_profile = plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE).with_updates(\n", "    title_props={\"label\": \"Lorenz Curve Analysis\"}\n", ")\n", "\n", "plt_plot_profiles = {\n", "    PltPlotTypes.CONTRIBUTION_CROSSPLOT: contribution_profile,\n", "    PltPlotTypes.CAPTURE_CURVE: capture_profile,\n", "    PltPlotTypes.LORENZ_CURVE: lorenz_profile\n", "}\n", "\n", "# 3. 执行PLT分析步骤\n", "print(\"🚀 [Step 4/5] 开始执行 PLT 盲井检验...\")\n", "plt_result = run_plt_analysis_step(\n", "    config=plt_config,\n", "    ctx=run_context,\n", "    prediction_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    plt_bundle=plt_val_bundle,\n", "    permeability_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    flow_rate_curve=\"QOZI\",           # 数据选择器参数\n", "    plot_profiles=plt_plot_profiles\n", ")\n", "\n", "print(f\"✅ [Step 4/5] PLT 检验完成！结果: {plt_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PltAnalysisArtifacts.ANALYZED_LAYERS_TABLE_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["## 7. 步骤五：岩心井渗透率(CT)相关性分析\n", "\n", "调用 `run_perm_correlation_step` 对模型的预测结果进行岩心井检验。此步骤同样会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物。"]}, {"cell_type": "code", "execution_count": 10, "id": "e5f6g7h8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:57:12.034984Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.82, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx\n", "2025-07-29T03:57:12.062623Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.84, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T03:57:12.094295Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.84, 'cpu_percent': 0.0} project_name=scape_core_k_val\n", "2025-07-29T03:57:12.110167Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.84, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_core_k_val\n", "2025-07-29T03:57:12.126050Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.85, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T03:57:12.141928Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.85, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:57:12.157740Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 661.85, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:57:12.183339Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.1, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:57:12.214683Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.11, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T03:57:12.230488Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} shape=(44, 9) sheet_name=K_Val\n", "2025-07-29T03:57:12.246586Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-29T03:57:12.274440Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(44, 9) processing_time=0.091\n", "2025-07-29T03:57:12.292193Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:57:12.307668Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:57:12.322798Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val.wp.xlsx processing_time=0.288 project_name=WpIdentifier('scape_core_k_val')\n", "2025-07-29T03:57:12.338611Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} extracted_curve_count=7 operation=extract_curve_dataframe_bundle\n", "2025-07-29T03:57:12.354666Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['PERM_LT_001_FLAG', 'SAMPLE_TYPE', 'K_LABEL_TYPE', 'POR', 'Lithology', 'MD', 'PERM', 'WELL_NO', 'K_LABEL'] operation=extract_metadata output_curve_count=9 output_curves=['PERM_LT_001_FLAG', 'SAMPLE_TYPE', 'K_LABEL_TYPE', 'POR', 'Lithology', 'MD', 'PERM', 'WELL_NO', 'K_LABEL']\n", "✅ 成功读取岩心验证数据: ./scape_core_k_val.wp.xlsx\n", "🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\n", "2025-07-29T03:57:12.380941Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.14, 'cpu_percent': 0.0} step_name=ct_perm_corr_analysis\n", "2025-07-29T03:57:12.396844Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:57:12.412858Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 659.15, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T03:57:12.428526Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.04, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:12.463670Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.04, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_115400\\ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:57:12.479377Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:12.495078Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:12.527001Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.04, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:57:12.560748Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_115400\\ct_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.04, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:57:14.106306Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.11, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_115400\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_115400\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T03:57:14.137811Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-035400-3b1f6242\n", "2025-07-29T03:57:14.169160Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.11, 'cpu_percent': 0.0}\n", "✅ [Step 5/5] 岩心井检验完成！结果: {'T-1': {'spearman_rho': 0.5297730544730748, 'conclusion': \"Fail: 5x符合率 9.8% <= 50% (未满足井 'T-1' 的放宽标准)。\"}}\n", "   - 分析报告已保存为产物: perm_corr_analysis.plots.crossplot_* \n"]}], "source": ["# --- 加载岩心验证数据 ---\n", "core_val_path = \"./scape_core_k_val.wp.xlsx\"\n", "core_val_project = reader.read(core_val_path)\n", "core_val_bundle = core_val_project.get_dataset(\"K_Val\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取岩心验证数据: {core_val_path}\")\n", "\n", "# 1. 创建渗透率相关性分析配置\n", "perm_corr_config = PermCorrelationConfig(\n", "    relaxed_wells=[\"T-1\"]     # 对T-1井使用放宽的深度对齐标准\n", ")\n", "\n", "# 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": \"Core Perm vs. Predicted Perm\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "\n", "# 3. 执行渗透率相关性分析步骤\n", "print(\"🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\")\n", "perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=core_val_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"PERM\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=\"ct\"\n", ")\n", "\n", "print(f\"✅ [Step 5/5] 岩心井检验完成！结果: {perm_corr_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PermCorrelationArtifacts.CROSSPLOT_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "i9j0k1l2", "metadata": {}, "source": ["## 8. 总结\n", "\n", "🎉 **SWIFT-PSO Case 02 (重构版) 工作流执行完毕！**\n", "\n", "本次重构展示了如何使用新的组件化框架来构建一个清晰、可维护、可追踪的机器学习工作流。所有步骤的输入、输出和配置都得到了规范化管理。\n", "\n", "**关键亮点:**\n", "1.  **统一的运行上下文 (`RunContext`)**: 所有的步骤都在同一个 `RunContext` 中执行，确保了所有产物（模型、数据集、图表、报告）和日志都被集中管理在一个独立的运行目录中：\n", "    - **输出目录**: `./output02/swift_pso_workflow_run`\n", "2.  **清晰的步骤划分**: 每个核心任务（训练、预测、可视化、验证）都被封装成一个独立的 `run_*_step` 函数，职责明确。\n", "3.  **类型安全的配置 (Pydantic)**: 使用 `SwiftPsoTrainingConfig`, `TsneVisualConfig`, `PltAnalysisConfig` 等Pydantic模型替代了易出错的字典，提供了自动验证和清晰的文档。\n", "4.  **自动化的产物管理**: `facade` 函数内部处理了产物的保存和注册，使得工作流代码更简洁。下游步骤可以通过 `RunContext` 自动加载上游产物，无需手动传递文件路径。\n", "5.  **灵活的绘图系统 (`PlotProfile`)**: 通过 **Get -> Modify -> Pass** 模式，我们可以在不修改组件源码的情况下，轻松地定制图表的每一个细节，同时享受高质量的默认模板。"]}, {"cell_type": "markdown", "id": "j8k9l0m1", "metadata": {}, "source": ["## 9. 最终化运行\n", "\n", "在所有步骤完成后，手动调用 `finalize()` 来结束本次运行。这将确保所有日志被刷新，并且运行清单 `manifest.json` 被正确写入。"]}, {"cell_type": "code", "execution_count": 11, "id": "n2o3p4q5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:57:14.264630Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.11, 'cpu_percent': 0.0} duration_seconds=193.953 manifest_path=output01\\swift_pso_run_20250729_115400\\manifest.json operation=finalize run_id=20250729-035400-3b1f6242 status=RUNNING\n", "✅ 运行已最终化，所有记录已保存。\n"]}], "source": ["run_context.finalize()\n", "print(\"✅ 运行已最终化，所有记录已保存。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}