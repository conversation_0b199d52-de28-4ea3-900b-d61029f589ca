"""PCA内部服务层模块。

包含PCA功能的所有核心业务逻辑实现，遵循SCAPE项目的内部服务层模式。
外部API层只做参数验证和请求转发，所有具体实现都在此目录下。

Architecture
------------
层次/依赖: PCA内部服务层，被外部API层调用
设计原则: 业务逻辑分离、模块化设计、高内聚低耦合
性能特征: GPU加速、内存优化、异步支持

Modules
-------
preprocess: 数据预处理服务
compute: PCA计算服务（GPU/CPU）
model: 模型管理服务
validation: 数据验证服务
persistence: 模型持久化服务
visualization: 可视化服务

Notes
-----
本目录下的模块仅供PCA包内部使用，不对外暴露API。
所有公共接口都通过上级目录的API层提供。
"""

from __future__ import annotations

# 内部服务层模块不对外导出任何API
# 所有功能都通过上级API层访问

__all__: list[str] = []
