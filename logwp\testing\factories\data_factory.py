"""测试项目工厂 - 测试专用

提供预定义的、完整的测试项目（WpWellProject）创建方法。

Examples
--------
>>> # 创建演示项目
>>> project = TestDataFactory.santos_demo_project()
"""

from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.models import WpWellProject


class TestDataFactory:
    """测试项目工厂类。

    提供预定义的测试项目（WpWellProject）创建方法，用于快速生成标准化的、可复用的测试项目。
    """

    @staticmethod
    def santos_demo_project() -> 'WpWellProject':
        """创建Santos油田演示项目。

        Returns:
            WpWellProject: Santos演示项目

        Note:
            需要完整的logwp环境才能工作。
        """
        try:
            from logwp.testing.builders import DatasetBuilder
            from logwp.models import WpWellProject

            # 创建测试数据集
            logs_dataset = DatasetBuilder.quick_continuous_dataset(
                name="Santos_logs",
                well_name="C-1",
                depth_range=(2500.0, 2520.0),
                interval=0.5,
                curves={
                    "GR": lambda d: 50 + 10 * ((d - 2500) / 20),
                    "PHIT_NMR": lambda d: 0.15 + 0.05 * ((d - 2510) / 10),
                    "T2LM": lambda d: 100 + 20 * ((d - 2500) / 20),
                    "PERM_CORE": lambda d: 10 + 5 * ((d - 2500) / 20)
                }
            )

            core_dataset = DatasetBuilder.quick_discrete_dataset(
                name="Santos_core",
                well_name="C-1",
                depths=[2502.5, 2507.3, 2512.1, 2517.8],
                curves={
                    "PERM_CORE": [12.5, 8.9, 15.2, 11.3],
                    "PHIT_CORE": [0.16, 0.14, 0.18, 0.15],
                    "FACIES": ["砂岩", "泥岩", "砂岩", "砂岩"]
                }
            )

            # 创建项目
            return WpWellProject.create_with_datasets(
                name="Santos_Demo_Project",
                datasets={
                    "Santos_logs": logs_dataset,
                    "Santos_core": core_dataset
                },
                head_attributes={
                    "version": "1.0",
                    "field": "Santos_Basin",
                    "block": "BM-S-11",
                    "operator": "Petrobras",
                    "description": "Santos Basin demo project for testing"
                },
                well_mappings={
                    "C-1A-RJS": "C-1",
                    "C-1B-RJS": "C-1"
                }
            )

        except ImportError:
            raise RuntimeError(
                "TestDataFactory.santos_demo_project() requires full logwp environment."
            )

    @staticmethod
    def minimal_project(name: str = "minimal_project") -> 'WpWellProject':
        """创建最小化测试项目。

        Args:
            name: 项目名称

        Returns:
            WpWellProject: 最小化项目
        """
        try:
            from logwp.testing.builders import DatasetBuilder
            from logwp.models import WpWellProject

            # 创建最小数据集
            minimal_dataset = DatasetBuilder.quick_continuous_dataset(
                name="minimal_logs",
                well_name="MIN-1",
                depth_range=(2500.0, 2505.0),
                interval=1.0,
                curves={
                    "GR": 50.0,
                    "PHIT": 0.15
                }
            )

            return WpWellProject.create_with_datasets(
                name=name,
                datasets={"minimal_logs": minimal_dataset}
            )

        except ImportError:
            raise RuntimeError(
                "TestDataFactory.minimal_project() requires full logwp environment."
            )
