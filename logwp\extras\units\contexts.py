"""
Defines Contexts for handling non-linear unit conversions, such as temperature.
"""
from typing import Callable, NamedTuple

# Forward declaration for type hinting
class Unit:
    pass

class Context(NamedTuple):
    """
    A context defines a set of transformation rules for a specific dimension.
    """
    name: str
    # func(value, unit_from) -> base_value
    to_base: Callable[[float, Unit], float]
    # func(base_value, unit_to) -> value
    from_base: Callable[[float, Unit], float]


# --- Temperature Context Implementation ---

def _temp_to_kelvin(value: float, unit: "Unit") -> float:
    """Converts a temperature value to the base unit (Kelvin)."""
    return value * unit.scale + unit.offset

def _temp_from_kelvin(value: float, unit: "Unit") -> float:
    """Converts a temperature value from the base unit (Kelvin)."""
    return (value - unit.offset) / unit.scale

# Create the specific context object
temperature_context = Context(
    name="temperature",
    to_base=_temp_to_kelvin,
    from_base=_temp_from_kelvin,
)
