# SCAPE 项目编码与通用规范（Coding & Conventions Guide，CCG）

> **版本**: 5.0  
> **更新日期**: 2025-07-01  
> **适用范围**: SCAPE 现代化技术栈项目  
> **Python版本**: 3.11+  

---

## 1. 概述

本文档为 **SCAPE（Santos Carbonate Adaptive Permeability Estimator）** 项目的现代化编码规范，基于 Python 3.11+ 和现代化技术栈制定。

### 1.1 设计原则

**SCAPE 项目特点：**
- **科学研究导向**：内部科研项目，重点关注算法准确性和研究复现性
- **现代化技术栈**：采用 Python 3.11+、ruff、structlog、pydantic v2、GPU 计算等现代技术
- **实用主义**：避免过度工程化，重点解决实际问题
- **类型安全**：100% 类型注解覆盖，Protocol 接口定义

**核心规范原则：**
1. **现代化优先**：优先使用现代 Python 特性和工具
2. **类型安全**：严格类型检查，支持 Protocol 和 TypedDict
3. **性能导向**：GPU 加速、异步 I/O、向量化计算
4. **可维护性**：清晰的架构分层、统一的代码风格
5. **科学严谨**：确保算法实现的正确性和可复现性

### 1.2 技术栈要求

**强制要求：**
- **Python 3.11+**：支持 Exception Groups、性能优化、最新语法特性
- **ruff ≥ 0.1.8**：统一代码质量工具，替代 black+isort+flake8+pylint
- **mypy ≥ 1.8.0**：严格类型检查
- **structlog ≥ 23.0.0**：结构化日志
- **pydantic ≥ 2.5.0**：数据验证与序列化

**可选组件：**
- **GPU 计算**：cupy、cudf、numba（`scape[gpu]`）
- **机器学习**：scikit-learn、xgboost、optuna（`scape[ml]`）
- **可视化**：plotly、matplotlib（`scape[viz]`）

---

## 2. 代码风格规范

### 2.1 基础风格

**遵循标准：**
- **PEP 8**：Python 官方代码风格指南
- **PEP 484/526**：类型注解规范
- **ruff 配置**：项目统一的代码质量标准

**关键配置：**
```toml
[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "W", "F", "I", "B", "C4", "UP", "RUF"]
```

### 2.2 命名约定

#### 包级别命名
- **包名**：全小写，下划线分隔
  - ✅ `logwp`, `scape_core`, `logwp_extras`
  - ❌ `LogWP`, `scapeCore`, `scape-study`

#### 类命名
- **普通类**：PascalCase，描述性名词
  - ✅ `WpWellProject`, `ObmiqTrainer`, `ExcelReader`
  - ❌ `wellProject`, `dataset_manager`

#### 函数和方法命名
- **函数/方法**：snake_case，动词开头
  - ✅ `read_wp_excel()`, `validate_dataset()`, `calculate_porosity()`
  - ❌ `ReadWpExcel()`, `ValidateDataset()`

#### 常量命名
- **常量**：UPPER_SNAKE_CASE，枚举类使用包前缀
  - ✅ `DEFAULT_CHUNK_SIZE`, `WpDsType.CONTINUOUS`
  - ❌ `defaultChunkSize`, `DsType.continuous`

#### 公共 API 命名前缀
所有对外暴露的类、函数、异常必须使用包前缀：

- **logwp 包**：`Wp` 前缀
  - ✅ `WpWellProject`, `WpDataset`, `WpFileFormatError`
- **scape/core 包**：`Scape` 前缀  
  - ✅ `ScapeObmiqTrainer`, `ScapeSwiftPso`, `ScapeModelError`
- **scape/study 包**：`Study` 前缀
  - ✅ `StudyRunner`, `StudyConfig`, `StudyReporter`

---

## 3. 现代化类型系统

### 3.1 类型注解要求

**100% 类型注解覆盖：**
```python
from __future__ import annotations

from typing import Protocol, TypedDict, Any
from pathlib import Path
import pandas as pd

# 函数必须有完整类型注解
def read_wp_excel(
    path: Path | str,
    *,
    chunk_size: int = 10_000,
    validate: bool = True
) -> WpWellProject:
    """读取 WP Excel 文件。"""
    ...

# 类属性必须有类型注解
class WpDataset:
    name: str
    df: pd.DataFrame
    metadata: dict[str, Any]
    
    def __init__(self, name: str, df: pd.DataFrame) -> None:
        self.name = name
        self.df = df
        self.metadata = {}
```

### 3.2 Protocol 接口定义

**使用 Protocol 定义类型安全接口：**
```python
from typing import Protocol, runtime_checkable

@runtime_checkable
class ReaderProtocol(Protocol):
    """文件读取器协议。"""
    
    def read(self, path: Path, **kwargs: Any) -> WpWellProject:
        """读取文件并返回 WpWellProject。"""
        ...
    
    def validate_format(self, path: Path) -> bool:
        """验证文件格式是否支持。"""
        ...

@runtime_checkable  
class FeatureStrategyProtocol(Protocol):
    """特征生成策略协议。"""
    
    def generate(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成特征矩阵。"""
        ...
    
    def get_feature_names(self) -> list[str]:
        """获取特征名称列表。"""
        ...
```

### 3.3 TypedDict 数据结构

**使用 TypedDict 定义结构化数据：**
```python
from typing import TypedDict, NotRequired

class WpMetadata(TypedDict):
    """WP 文件元数据。"""
    version: str
    created_at: str
    file_path: str
    well_count: int
    dataset_count: int
    # 可选字段使用 NotRequired
    description: NotRequired[str]
    tags: NotRequired[list[str]]

class GpuInfo(TypedDict):
    """GPU 信息。"""
    device_id: int
    device_name: str
    memory_total: int
    memory_available: int
    cuda_version: str
```

---

## 4. 现代化异常处理

### 4.1 Exception Groups (Python 3.11+)

**批量异常处理：**
```python
def process_multiple_files(files: list[Path]) -> None:
    """处理多个文件，支持批量异常处理。"""
    errors: list[Exception] = []
    
    for file_path in files:
        try:
            process_file(file_path)
        except WpError as e:
            errors.append(e)
    
    if errors:
        raise ExceptionGroup("Multiple file processing errors", errors)

# 异常处理
try:
    process_multiple_files(file_list)
except* WpFileFormatError as eg:
    for error in eg.exceptions:
        logger.error("文件格式错误: %s", error)
except* WpDataError as eg:
    for error in eg.exceptions:
        logger.error("数据处理错误: %s", error)
```

### 4.2 结构化异常信息

**使用结构化上下文：**
```python
from typing import NamedTuple

class ErrorContext(NamedTuple):
    """异常上下文信息。"""
    operation: str
    file_path: str | None = None
    dataset_name: str | None = None
    line_number: int | None = None
    additional_info: dict[str, Any] | None = None

class WpError(Exception):
    """logwp 根异常。"""
    
    def __init__(
        self, 
        message: str, 
        *, 
        context: ErrorContext | None = None
    ) -> None:
        super().__init__(message)
        self.context = context
```

---

## 5. 现代化日志系统

### 5.1 structlog 配置

**结构化日志配置：**
```python
import structlog
from structlog.typing import FilteringBoundLogger

# 配置 structlog
structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.dev.ConsoleRenderer() if DEBUG else structlog.processors.JSONRenderer(),
    ],
    wrapper_class=structlog.make_filtering_bound_logger(20),  # INFO level
    logger_factory=structlog.WriteLoggerFactory(),
    cache_logger_on_first_use=True,
)

# 使用结构化日志
logger: FilteringBoundLogger = structlog.get_logger(__name__)

def process_dataset(dataset_name: str, row_count: int) -> None:
    """处理数据集。"""
    logger.info(
        "开始处理数据集",
        dataset_name=dataset_name,
        row_count=row_count,
        operation="process_dataset",
        stage="start"
    )
    
    try:
        # 处理逻辑
        pass
    except Exception as e:
        logger.error(
            "数据集处理失败",
            dataset_name=dataset_name,
            error_type=type(e).__name__,
            error_message=str(e),
            operation="process_dataset",
            stage="error",
            exc_info=True
        )
        raise
    finally:
        logger.info(
            "数据集处理完成",
            dataset_name=dataset_name,
            operation="process_dataset", 
            stage="complete"
        )
```

### 5.2 日志级别约定

| 级别 | 使用场景 | 处理策略 |
|------|----------|----------|
| **DEBUG** | I/O chunk 读取进度、详细诊断信息 | 仅在 `--log_level debug` 输出 |
| **INFO** | 算法阶段节点、关键操作确认 | 默认输出 |
| **WARNING** | 可恢复异常、潜在问题预警 | 继续流程并计数 |
| **ERROR** | 严重错误、功能无法完成 | 立即抛出异常 |
| **CRITICAL** | 系统级错误、程序无法继续 | 程序终止 |

---

## 6. 现代化数据验证

### 6.1 pydantic v2 数据模型

**使用 pydantic v2 定义数据模型：**
```python
from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Literal
import pandas as pd

class WpDatasetConfig(BaseModel):
    """数据集配置模型。"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        use_enum_values=True,
        frozen=True  # 不可变模型
    )

    name: str = Field(..., min_length=1, description="数据集名称")
    ds_type: Literal["Continuous", "Point", "Interval"] = Field(..., description="数据集类型")
    chunk_size: int = Field(10_000, gt=0, le=100_000, description="分块大小")
    validate_schema: bool = Field(True, description="是否验证Schema")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        """验证数据集名称。"""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError("数据集名称只能包含字母、数字、下划线和连字符")
        return v

class StudyConfig(BaseModel):
    """研究配置模型。"""
    model_config = ConfigDict(validate_assignment=True)

    # 基础配置
    study_name: str = Field(..., description="研究名称")
    random_seed: int = Field(42, ge=0, description="随机种子")

    # GPU配置
    gpu_enabled: bool = Field(False, description="是否启用GPU")
    gpu_device_id: int = Field(0, ge=0, description="GPU设备ID")

    # 性能配置
    num_workers: int = Field(4, ge=1, le=32, description="并行工作线程数")
    memory_limit_gb: float = Field(4.0, gt=0, description="内存限制(GB)")

    @field_validator('study_name')
    @classmethod
    def validate_study_name(cls, v: str) -> str:
        """验证研究名称。"""
        if len(v.strip()) < 3:
            raise ValueError("研究名称至少3个字符")
        return v.strip()
```

### 6.2 配置管理

**使用 pydantic-settings 管理配置：**
```python
from pydantic_settings import BaseSettings, SettingsConfigDict

class ScapeSettings(BaseSettings):
    """SCAPE 项目设置。"""
    model_config = SettingsConfigDict(
        env_prefix="SCAPE_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )

    # 基础设置
    debug_mode: bool = False
    log_level: str = "INFO"
    config_file: str | None = None

    # 性能设置
    max_cpu_memory_gb: float = 4.0
    max_gpu_memory_gb: float = 8.0
    chunk_size: int = 10_000
    num_workers: int = 4

    # GPU设置
    gpu_enabled: bool = False
    gpu_device_id: int = 0
    gpu_memory_fraction: float = 0.8

    # 异步I/O设置
    async_io_enabled: bool = True
    async_io_workers: int = 2
    io_timeout_sec: int = 300

# 全局设置实例
settings = ScapeSettings()
```

---

## 7. GPU 计算规范

### 7.1 GPU 环境检测

**自动检测和回退机制：**
```python
from typing import Any
import logging

logger = logging.getLogger(__name__)

def detect_gpu_environment() -> dict[str, Any]:
    """检测GPU环境。"""
    gpu_info = {
        "available": False,
        "device_count": 0,
        "devices": [],
        "cuda_version": None
    }

    try:
        import cupy as cp
        gpu_info["available"] = True
        gpu_info["device_count"] = cp.cuda.runtime.getDeviceCount()
        gpu_info["cuda_version"] = cp.cuda.runtime.runtimeGetVersion()

        for i in range(gpu_info["device_count"]):
            with cp.cuda.Device(i):
                props = cp.cuda.runtime.getDeviceProperties(i)
                gpu_info["devices"].append({
                    "id": i,
                    "name": props["name"].decode(),
                    "memory": props["totalGlobalMem"],
                    "compute_capability": f"{props['major']}.{props['minor']}"
                })

    except ImportError:
        logger.warning("cupy 未安装，GPU计算不可用")
    except Exception as e:
        logger.warning("GPU检测失败: %s", e)

    return gpu_info

def get_compute_backend() -> str:
    """获取计算后端。"""
    if settings.gpu_enabled and detect_gpu_environment()["available"]:
        return "gpu"
    return "cpu"

# 条件导入GPU库
def import_gpu_libraries() -> tuple[Any, Any, Any]:
    """条件导入GPU库。"""
    try:
        import cupy as cp
        import cudf
        import numba.cuda as cuda
        return cp, cudf, cuda
    except ImportError as e:
        if settings.gpu_enabled:
            raise ImportError(
                f"GPU库导入失败: {e}. "
                "请安装: pip install scape[gpu]"
            ) from e
        return None, None, None
```

### 7.2 GPU 计算模式

**CPU/GPU 统一接口：**
```python
from typing import Union
import numpy as np

ArrayLike = Union[np.ndarray, "cp.ndarray"]

class ComputeEngine:
    """统一计算引擎。"""

    def __init__(self, backend: str = "auto") -> None:
        self.backend = backend if backend != "auto" else get_compute_backend()
        self.cp, self.cudf, self.cuda = import_gpu_libraries()

    def array(self, data: Any) -> ArrayLike:
        """创建数组。"""
        if self.backend == "gpu" and self.cp is not None:
            return self.cp.asarray(data)
        return np.asarray(data)

    def to_cpu(self, arr: ArrayLike) -> np.ndarray:
        """转换到CPU。"""
        if self.backend == "gpu" and hasattr(arr, "get"):
            return arr.get()  # cupy array
        return np.asarray(arr)

    def matmul(self, a: ArrayLike, b: ArrayLike) -> ArrayLike:
        """矩阵乘法。"""
        if self.backend == "gpu" and self.cp is not None:
            return self.cp.matmul(a, b)
        return np.matmul(a, b)

# 全局计算引擎
compute = ComputeEngine()
```

---

## 8. 异步 I/O 规范

### 8.1 异步文件操作

**大文件异步读取：**
```python
import asyncio
import aiofiles
from pathlib import Path
from typing import AsyncGenerator

async def read_large_file_async(
    file_path: Path,
    chunk_size: int = 8192
) -> AsyncGenerator[str, None]:
    """异步读取大文件。"""
    async with aiofiles.open(file_path, mode='r', encoding='utf-8') as f:
        while chunk := await f.read(chunk_size):
            yield chunk

async def process_files_concurrently(
    file_paths: list[Path],
    max_concurrent: int = 5
) -> list[Any]:
    """并发处理多个文件。"""
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_single_file(path: Path) -> Any:
        async with semaphore:
            # 处理单个文件
            return await process_file_async(path)

    tasks = [process_single_file(path) for path in file_paths]
    return await asyncio.gather(*tasks, return_exceptions=True)

# 同步包装器
def run_async_operation(coro: Any) -> Any:
    """运行异步操作的同步包装器。"""
    try:
        loop = asyncio.get_running_loop()
        # 如果已在事件循环中，使用 create_task
        return loop.create_task(coro)
    except RuntimeError:
        # 如果没有运行的事件循环，创建新的
        return asyncio.run(coro)
```

---

## 9. 常量管理规范

### 9.1 分层常量体系

**按功能域组织常量：**
```python
from enum import Enum

# 基础层常量
class WpSystemDefaults(Enum):
    """系统默认配置。"""
    CHUNK_SIZE = 10_000
    MAX_MEMORY_GB = 4.0
    DEFAULT_ENCODING = "utf-8"
    NUM_WORKERS = 4
    ASYNC_IO_WORKERS = 2

# 业务层常量
class WpDsType(str, Enum):
    """数据集类型枚举。"""
    CONTINUOUS = "Continuous"
    POINT = "Point"
    INTERVAL = "Interval"
    TIME_SERIES = "TimeSeries"

class WpKeywords(str, Enum):
    """测井曲线关键字。"""
    MD = "MD"
    WELL_NO = "WELL_NO"
    PHIT_NMR = "PHIT_NMR"
    T2_P50 = "T2_P50"
    DPHIT_NMR = "DPHIT_NMR"
    K_LABEL = "K_LABEL"

# GPU计算常量
class WpGpuDefaults(Enum):
    """GPU计算默认配置。"""
    DEVICE_ID = 0
    MEMORY_FRACTION = 0.8
    BATCH_SIZE_AUTO = True
    ENABLE_MIXED_PRECISION = True
    FALLBACK_TO_CPU = True

# 环境变量常量
class WpEnvironmentVars(str, Enum):
    """环境变量名称。"""
    DEBUG_MODE = "SCAPE_DEBUG_MODE"
    GPU_ENABLED = "SCAPE_GPU_ENABLED"
    LOG_LEVEL = "SCAPE_LOG_LEVEL"
    MAX_CPU_MEMORY_GB = "SCAPE_MAX_CPU_MEMORY_GB"
    MAX_GPU_MEMORY_GB = "SCAPE_MAX_GPU_MEMORY_GB"
```

### 9.2 常量使用规范

**禁止硬编码，统一引用：**
```python
# ❌ 错误：硬编码字符串
result = {"dataset_type": "continuous", "curve_count": 10}

# ✅ 正确：使用枚举常量
from logwp.constants import WpDsType, WpApiKeys

result = {
    WpApiKeys.DATA: {
        "dataset_type": WpDsType.CONTINUOUS,
        "curve_count": 10
    }
}

# ❌ 错误：硬编码配置值
chunk_size = 10000
max_memory = 4.0

# ✅ 正确：使用配置常量
from logwp.constants import WpSystemDefaults

chunk_size = WpSystemDefaults.CHUNK_SIZE.value
max_memory = WpSystemDefaults.MAX_MEMORY_GB.value
```

---

## 10. 性能优化规范

### 10.1 内存管理

**智能内存控制：**
```python
import psutil
from typing import Generator

def get_available_memory() -> float:
    """获取可用内存(GB)。"""
    return psutil.virtual_memory().available / (1024**3)

def adaptive_chunk_size(
    total_rows: int,
    base_chunk_size: int = 10_000,
    memory_limit_gb: float = 2.0
) -> int:
    """自适应分块大小。"""
    available_memory = get_available_memory()
    max_memory = min(memory_limit_gb, available_memory * 0.8)

    # 估算每行内存使用
    estimated_memory_per_row = 0.001  # 1KB per row
    max_rows = int(max_memory * 1024 / estimated_memory_per_row)

    return min(base_chunk_size, max_rows, total_rows)

def memory_efficient_processing(
    data: pd.DataFrame,
    process_func: callable,
    chunk_size: int | None = None
) -> Generator[Any, None, None]:
    """内存高效的数据处理。"""
    if chunk_size is None:
        chunk_size = adaptive_chunk_size(len(data))

    for start_idx in range(0, len(data), chunk_size):
        end_idx = min(start_idx + chunk_size, len(data))
        chunk = data.iloc[start_idx:end_idx]

        try:
            result = process_func(chunk)
            yield result
        finally:
            # 显式删除chunk以释放内存
            del chunk
```

### 10.2 性能监控

**性能指标收集：**
```python
import time
import functools
from typing import Callable, Any

def performance_monitor(func: Callable) -> Callable:
    """性能监控装饰器。"""
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        start_time = time.perf_counter()
        start_memory = psutil.Process().memory_info().rss / (1024**2)  # MB

        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.perf_counter()
            end_memory = psutil.Process().memory_info().rss / (1024**2)  # MB

            logger.info(
                "性能监控",
                function=func.__name__,
                execution_time_sec=round(end_time - start_time, 3),
                memory_usage_mb=round(end_memory - start_memory, 2),
                peak_memory_mb=round(end_memory, 2)
            )

    return wrapper

@performance_monitor
def process_large_dataset(df: pd.DataFrame) -> pd.DataFrame:
    """处理大数据集。"""
    # 数据处理逻辑
    return df.copy()
```

---

## 11. 文档规范

### 11.1 docstring 规范

**Google 风格 docstring：**
```python
def read_wp_excel(
    path: Path | str,
    *,
    chunk_size: int = 10_000,
    validate: bool = True,
    gpu_enabled: bool = False
) -> WpWellProject:
    """读取 WP Excel 文件并返回 WpWellProject 对象。

    支持现代化特性：异步I/O、GPU加速、自动验证等。

    Args:
        path: Excel 文件路径，支持 .wp.xlsx 格式
        chunk_size: 分块读取大小，默认 10,000 行
        validate: 是否执行数据验证，默认 True
        gpu_enabled: 是否启用 GPU 加速处理，默认 False

    Returns:
        WpWellProject: 包含所有数据集的井工程项目对象

    Raises:
        WpFileFormatError: 文件格式不符合 WP 规范
        WpValidationError: 数据验证失败
        WpGpuError: GPU 计算错误（仅在 gpu_enabled=True 时）

    Examples:
        >>> # 基本使用
        >>> project = read_wp_excel("data.wp.xlsx")
        >>> print(f"包含 {len(project.datasets)} 个数据集")

        >>> # GPU 加速读取
        >>> project = read_wp_excel(
        ...     "large_data.wp.xlsx",
        ...     chunk_size=50_000,
        ...     gpu_enabled=True
        ... )

    Note:
        - 文件必须符合 WP 格式规范（见 SCAPE_MS 方法说明书）
        - GPU 加速需要安装 cupy: `pip install scape[gpu]`
        - 大文件建议使用异步模式以提高性能
    """
    ...
```

### 11.2 类型注解文档

**完整的类型信息：**
```python
from typing import TypeAlias, NewType, Final

# 类型别名
WellName: TypeAlias = str
DepthValue: TypeAlias = float
CurveName: TypeAlias = str

# 新类型
DatasetName = NewType('DatasetName', str)
ChunkSize = NewType('ChunkSize', int)

# 常量类型
DEFAULT_CHUNK_SIZE: Final[int] = 10_000
SUPPORTED_FORMATS: Final[tuple[str, ...]] = (".wp.xlsx", ".las", ".dlis")

class WpWellProject:
    """井工程项目类。

    Type Information:
        - datasets: 数据集字典，键为数据集名称，值为数据集对象
        - metadata: 项目元数据，包含版本、创建时间等信息
        - well_names: 项目中所有井名的集合
    """

    datasets: dict[DatasetName, WpDataset]
    metadata: WpMetadata
    well_names: set[WellName]
```

---

## 12. 代码审查清单

### 12.1 提交前检查

**必须通过的检查项：**

- [ ] **类型检查**：`mypy --strict` 无错误
- [ ] **代码质量**：`ruff check` 无错误
- [ ] **格式化**：`ruff format` 已执行
- [ ] **测试要求**：遵循 STG 测试文档规范
- [ ] **文档完整**：所有公共 API 有 docstring
- [ ] **类型注解**：100% 类型注解覆盖
- [ ] **常量使用**：无硬编码字符串/数字
- [ ] **异常处理**：适当的异常捕获和传播
- [ ] **日志记录**：关键操作有结构化日志
- [ ] **性能考虑**：大数据处理使用分块/异步

### 12.2 代码审查要点

**重点关注：**
1. **架构一致性**：是否符合五层架构设计
2. **现代化特性**：是否正确使用 Python 3.11+ 特性
3. **GPU 兼容性**：是否支持 CPU/GPU 自动切换
4. **错误处理**：是否使用 Exception Groups 和结构化异常
5. **性能优化**：是否考虑内存使用和计算效率
6. **类型安全**：是否充分利用类型系统
7. **测试友好性**：是否遵循 STG 文档的测试规范

---

## 13. 总结

本规范基于 SCAPE 项目的现代化技术栈要求制定，重点关注：

1. **现代化技术栈**：Python 3.11+、ruff、structlog、pydantic v2
2. **类型安全**：Protocol、TypedDict、严格类型检查
3. **性能优化**：GPU 计算、异步 I/O、内存管理
4. **科学严谨**：算法正确性、实验可复现性
5. **工程质量**：统一规范、自动化工具、完整测试

所有开发人员（包括 AI 编程助手）必须严格遵守本规范，确保代码质量和项目的长期可维护性。

---

**参考文档：**
- [SCAPE_SAD_软件架构设计.md](./SCAPE_SAD_软件架构设计.md)
- [SCAPE_MS_方法说明书.md](./SCAPE_MS_方法说明书.md)
- [PDM_STG_测试文档模板.md](./PDM_STG_测试文档模板.md) - 测试规范详见此文档
- [pyproject.toml](../pyproject.toml)
- [.cursorrules](../.cursorrules)
