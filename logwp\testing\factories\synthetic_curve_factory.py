"""合成曲线工厂 - 测试专用

提供能够生成具有物理意义或特定统计特征的合成曲线数据的方法。

Examples
--------
>>> # 生成双峰T2分布
>>> t2_dist = SyntheticCurveFactory.nmr_t2_distribution(
...     depth_points=100,
...     t2_bins=64,
...     peaks=[(3, 1, 10), (200, 2, 50)] # (T2位置, 振幅, 宽度)
... )
>>>
>>> # 生成与岩相关联的GR曲线
>>> facies = ["sand"] * 50 + ["shale"] * 50
>>> gr_curve = SyntheticCurveFactory.facies_based_curve(
...     facies_log=facies,
...     mapping={"sand": (30, 5), "shale": (90, 8)} # (均值, 标准差)
... )
"""
from __future__ import annotations

import numpy as np
from typing import Literal


class SyntheticCurveFactory:
    """合成曲线工厂类。

    提供用于生成模拟真实物理过程或具有特定统计特征的曲线数据的方法。
    """

    @staticmethod
    def nmr_t2_distribution(
        depth_points: int,
        t2_bins: int,
        peaks: list[tuple[float, float, float]],
        t2_axis: np.ndarray | None = None
    ) -> np.ndarray:
        """生成模拟NMR T2谱分布。

        Args:
            depth_points: 深度点数量。
            t2_bins: T2谱的bin数量。
            peaks: 峰值定义列表，每个元素为 (t2_ms, amplitude, width) 元组。
            t2_axis: T2轴，如果为None则自动生成对数间隔轴。

        Returns:
            np.ndarray: 形状为 (depth_points, t2_bins) 的T2谱数据。
        """
        if t2_axis is None:
            t2_axis = np.logspace(np.log10(0.1), np.log10(10000), t2_bins)

        # 首先创建单个平均T2分布
        base_t2_dist = np.zeros((1, t2_bins))
        for t2_ms, amplitude, width in peaks:
            # 使用高斯函数模拟峰
            peak_shape = amplitude * np.exp(-((np.log(t2_axis) - np.log(t2_ms)) ** 2) / (2 * (width / 10)**2))
            base_t2_dist += peak_shape

        # 为所有深度点复制基础分布
        t2_dist = np.tile(base_t2_dist, (depth_points, 1))

        # 为每个深度点增加与信号幅度相关的随机扰动
        noise = np.random.normal(0, 0.02 * np.max(base_t2_dist), size=t2_dist.shape)
        return np.abs(t2_dist + noise)

    @staticmethod
    def facies_based_curve(
        facies_log: list[str],
        mapping: dict[str, tuple[float, float]]
    ) -> np.ndarray:
        """根据岩相序列生成测井曲线。

        Args:
            facies_log: 岩相名称列表。
            mapping: 岩相到 (均值, 标准差) 的映射字典。

        Returns:
            np.ndarray: 生成的测井曲线值。
        """
        values = []
        for facies in facies_log:
            mean, std = mapping.get(facies, (0, 0))
            values.append(np.random.normal(mean, std))
        return np.array(values)

    @staticmethod
    def create_correlated_curves(
        n_points: int,
        means: list[float],
        cov_matrix: list[list[float]],
        curve_names: list[str] | None = None,
    ) -> dict[str, np.ndarray]:
        """生成具有指定相关性的多条曲线。

        Args:
            n_points: 数据点数量。
            means: 均值列表，长度应与曲线数量一致。
            cov_matrix: 协方差矩阵，定义了曲线间的相关性。
            curve_names: 曲线名称列表。如果为None，则自动命名为 "CURVE_0", "CURVE_1", ...

        Returns:
            dict[str, np.ndarray]: 曲线名称到数据数组的字典。

        Raises:
            ValueError: 如果参数维度不匹配。
        """
        num_curves = len(means)
        if np.shape(cov_matrix) != (num_curves, num_curves):
            raise ValueError("协方差矩阵的维度与均值列表的长度不匹配。")
        if curve_names and len(curve_names) != num_curves:
            raise ValueError("曲线名称列表的长度与均值列表的长度不匹配。")

        if curve_names is None:
            curve_names = [f"CURVE_{i}" for i in range(num_curves)]

        data = np.random.multivariate_normal(mean=means, cov=cov_matrix, size=n_points)

        return {name: data[:, i] for i, name in enumerate(curve_names)}

    @staticmethod
    def create_log_with_noise(
        ideal_curve: np.ndarray,
        noise_type: Literal["gaussian"] = "gaussian",
        scale: float = 0.05,
    ) -> np.ndarray:
        """为理想曲线添加噪声。

        Args:
            ideal_curve: 理想的、无噪声的曲线数据。
            noise_type: 噪声类型。目前仅支持 'gaussian'。
            scale: 噪声尺度。对于高斯噪声，这是标准差相对于理想曲线幅度的比例。

        Returns:
            np.ndarray: 添加了噪声的曲线数据。
        """
        if noise_type == "gaussian":
            noise_std = scale * (np.max(ideal_curve) - np.min(ideal_curve))
            noise = np.random.normal(0, noise_std, size=ideal_curve.shape)
            return ideal_curve + noise
        else:
            raise NotImplementedError(f"噪声类型 '{noise_type}' 尚未实现。")
