{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ数据准备 \n", "\n", "- 表格数据使用更多的输入曲线\n", "  - DEN, CN, DT, RD_LOG10, RS_LOG10, DRES\n", "  - T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10\n", "  - PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR\n", "  - VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO\n", "  - LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-30T02:42:48.171743Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 128.95, 'cpu_percent': 0.0}\n", "库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T02:42:57.822384Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 356.57, 'cpu_percent': 0.0} file_path=santos_data_v1.wp.xlsx\n", "2025-07-30T02:42:57.903247Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.77, 'cpu_percent': 0.0} file_path=santos_data_v1.wp.xlsx file_size_mb=28.86 sheet_count=8\n", "2025-07-30T02:42:57.929387Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.77, 'cpu_percent': 0.0} project_name=santos_data_v1\n", "2025-07-30T02:42:57.948190Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.78, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data_v1\n", "2025-07-30T02:42:57.966328Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.79, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-30T02:42:57.986195Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.79, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-30T02:42:57.999580Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.79, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-07-30T02:42:58.016160Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.79, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-30T02:42:58.051876Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 357.92, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:42:58.098762Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 358.12, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=74 well_curves=1\n", "2025-07-30T02:43:38.670646Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 435.87, 'cpu_percent': 0.0} shape=(16303, 263) sheet_name=Logs\n", "2025-07-30T02:43:38.755736Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 436.2, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-07-30T02:43:38.771828Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 436.2, 'cpu_percent': 0.0} curve_count=74 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 263) processing_time=40.726\n", "2025-07-30T02:43:38.819685Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 436.23, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:38.859390Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 436.25, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=91 well_curves=1\n", "2025-07-30T02:43:43.494505Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.04, 'cpu_percent': 0.0} shape=(4502, 91) sheet_name=OBMIQ_Pred\n", "2025-07-30T02:43:43.557781Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.04, 'cpu_percent': 0.0} dataset_name=WpIdentifier('OBMIQ_Pred')\n", "2025-07-30T02:43:43.557781Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.04, 'cpu_percent': 0.0} curve_count=91 dataset_name=OBMIQ_Pred dataset_type=Continuous df_shape=(4502, 91) processing_time=4.752\n", "2025-07-30T02:43:43.589133Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.05, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-07-30T02:43:43.626607Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.05, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-30T02:43:43.708062Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-07-30T02:43:43.730031Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-07-30T02:43:43.753921Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.165\n", "2025-07-30T02:43:43.787082Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-30T02:43:43.808144Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-30T02:43:43.831266Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} shape=(15, 4) sheet_name=PLT\n", "2025-07-30T02:43:43.853584Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-30T02:43:43.853584Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(15, 4) processing_time=0.071\n", "2025-07-30T02:43:43.894235Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-30T02:43:43.916234Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.15, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-30T02:43:43.963227Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.19, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-07-30T02:43:43.978954Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.19, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-30T02:43:43.995056Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.19, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.11\n", "2025-07-30T02:43:44.030382Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.19, 'cpu_percent': 0.0} dataset_count=5 has_head_info=True has_well_map=True\n", "2025-07-30T02:43:44.042260Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.19, 'cpu_percent': 0.0} dataset_count=5\n", "2025-07-30T02:43:44.064968Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.19, 'cpu_percent': 0.0} dataset_count=5 file_path=santos_data_v1.wp.xlsx processing_time=46.243 project_name=WpIdentifier('santos_data_v1')\n", "2025-07-30T02:43:44.089408Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.19, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data_v1'}\n", "2025-07-30T02:43:44.152535Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.28, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 5}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data_v1\n", "📅 创建时间: 2025-07-30 10:42:57.948190\n", "🔧 默认深度单位: WpDepthUnit.METER\n"]}], "source": ["data_file_path = \"./santos_data_v1.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 创建时间: {project.created_at}\")\n", "    print(f\"🔧 默认深度单位: {project.default_depth_reference_unit}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 生成测井数据概况报告"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 提取OBMIQ累积分布数据集"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 准备提取OBMIQ相关曲线，共31条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR, VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO, LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY, PHI_T2_DIST_CUM, DT2_P50, DPHIT_NMR\n", "📊 准备提取OBMIQ相关曲线（dropna），共29条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR, VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO, LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY, PHI_T2_DIST_CUM\n"]}], "source": ["# 定义要提取的曲线列表\n", "# obmiq_curves = [\n", "#    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "#    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',\n", "#    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',\n", "#    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "#]\n", "\n", "#log_scout分析结果\n", "obmiq_curves = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "]\n", "\n", "obmiq_curves_dropna = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM'\n", "]\n", "\n", "print(f\"📊 准备提取OBMIQ相关曲线，共{len(obmiq_curves)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves)}\")\n", "print(f\"📊 准备提取OBMIQ相关曲线（dropna），共{len(obmiq_curves_dropna)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves_dropna)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始提取OBMIQ数据集(训练)...\n", "\n", "📍 提取C-1井数据(训练)...\n", "2025-07-30T02:43:44.401935Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.41, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T02:43:44.468956Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.43, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR']\n", "2025-07-30T02:43:44.765914Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.67, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:44.798015Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.75, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T02:43:44.821556Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.75, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-30T02:43:44.848969Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.75, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:44.869817Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.75, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=1085\n", "2025-07-30T02:43:44.910613Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.75, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_c_1\n", "2025-07-30T02:43:44.940451Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1056 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.77, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1085 removed_rows=29\n", "2025-07-30T02:43:44.962094Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.77, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T02:43:44.987332Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.77, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:45.011945Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.77, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T02:43:45.027212Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.77, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_c1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_C1') save_head_info=True save_well_map=True\n", "2025-07-30T02:43:45.058268Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.78, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1056, 96)\n", "2025-07-30T02:43:46.532080Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 432.84, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=1.474\n", "2025-07-30T02:43:46.555770Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 432.84, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T02:43:46.712910Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 432.86, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_c1.wp.xlsx processing_time=1.696 project_name=WpIdentifier('Santos_OBMIQ_C1')\n", "2025-07-30T02:43:46.753010Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 432.86, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_C1\n", "2025-07-30T02:43:47.216086Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 433.28, 'cpu_percent': 0.0} file_size=15014 format=markdown output_path=santos_obmiq_cum_c1_report.md\n", "✅ C-1井数据已保存: santos_obmiq_cum_c1.wp.xlsx\n", "   数据形状: (1056, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取T-1井数据(训练)...\n", "2025-07-30T02:43:47.261226Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 433.28, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T02:43:47.306651Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 433.28, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR']\n", "2025-07-30T02:43:47.583712Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.14, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:47.624099Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.14, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T02:43:47.653674Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.14, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-30T02:43:47.687926Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.14, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:47.702036Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.14, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=1661\n", "2025-07-30T02:43:47.715192Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 437.14, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_t_1\n", "2025-07-30T02:43:47.755540Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1596 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.3, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1661 removed_rows=65\n", "2025-07-30T02:43:47.778965Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.3, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T02:43:47.799858Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.3, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:47.815989Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.3, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T02:43:47.829129Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.3, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_t1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_T1') save_head_info=True save_well_map=True\n", "2025-07-30T02:43:47.855317Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.3, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1596, 96)\n", "2025-07-30T02:43:49.893623Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.5, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=2.038\n", "2025-07-30T02:43:49.927437Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.34, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T02:43:50.139894Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.35, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_t1.wp.xlsx processing_time=2.311 project_name=WpIdentifier('Santos_OBMIQ_T1')\n", "2025-07-30T02:43:50.173058Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.35, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_T1\n", "2025-07-30T02:43:50.479102Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.45, 'cpu_percent': 0.0} file_size=14989 format=markdown output_path=santos_obmiq_cum_t1_report.md\n", "✅ T-1井数据已保存: santos_obmiq_cum_t1.wp.xlsx\n", "   数据形状: (1596, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取所有井数据(训练)...\n", "2025-07-30T02:43:50.531337Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.45, 'cpu_percent': 0.0} curve_count=31 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T02:43:50.563756Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 438.45, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR']\n", "2025-07-30T02:43:50.666677Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 479.82, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:50.696439Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.18, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-30T02:43:50.713305Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.18, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-30T02:43:50.743256Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.18, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:50.773634Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 480.18, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=16303\n", "2025-07-30T02:43:50.801540Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 448.09, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all\n", "2025-07-30T02:43:50.843882Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=2652 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.72, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=13651\n", "2025-07-30T02:43:50.867548Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.78, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T02:43:50.893974Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.78, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:43:50.905989Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.78, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T02:43:50.929094Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.78, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All') save_head_info=True save_well_map=True\n", "2025-07-30T02:43:50.937868Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.78, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(2652, 96)\n", "2025-07-30T02:43:52.088221Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 484.7, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=1.15\n", "2025-07-30T02:43:52.138931Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 482.79, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T02:43:52.168325Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 482.79, 'cpu_percent': 0.0}\n", "2025-07-30T02:43:52.183354Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 482.79, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-30T02:43:59.754034Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.37, 'cpu_percent': 0.0}\n", "2025-07-30T02:43:59.771799Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.37, 'cpu_percent': 0.0}\n", "2025-07-30T02:44:03.675407Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.79, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx processing_time=12.746 project_name=WpIdentifier('Santos_OBMIQ_All')\n", "2025-07-30T02:44:03.712151Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.79, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All\n", "2025-07-30T02:44:04.425586Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 517.58, 'cpu_percent': 0.0} file_size=32530 format=markdown output_path=santos_obmiq_cum_all_report.md\n", "✅ 所有井数据已保存: santos_obmiq_cum_all.wp.xlsx\n", "   数据形状: (2652, 96)\n", "   数据集类型: WpContinuousDataset\n", "   井名分布: {'T-1': 1596, 'C-1': 1056}\n", "\n", "📍 提取所有井数据(预测)...\n", "2025-07-30T02:44:04.467851Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 517.58, 'cpu_percent': 0.0} curve_count=31 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq_all_apply\n", "2025-07-30T02:44:04.492998Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 517.58, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['SFF_NMR', 'PHIT_NMR', 'FFV_NMR', 'T2LM_LOG10', 'DRES', 'RS_LOG10', 'PHI_T2_DIST_CUM', 'T2_P50_LOG10', 'SMESO', 'SMICRO', 'LT2STDDEV_FFI', 'T2_P20_LOG10', 'VMESO', 'BVI_NMR', 'RD_LOG10', 'T2LM_LONG_LOG10', 'VMACRO', 'WELL_NO', 'DPHIT_NMR', 'SWB_NMR', 'LSKEW_FFI', 'SDR_PROXY', 'CN', 'MD', 'DT', 'DEN', 'SWI_NMR', 'VMICRO', 'PHIE_NMR', 'DT2_P50', 'SMACRO', 'LKURT_FFI', 'BFV_NMR']\n", "2025-07-30T02:44:04.595788Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 559.38, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:44:04.635591Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 560.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-30T02:44:04.664225Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 559.75, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq_all_apply target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-30T02:44:04.693807Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 559.75, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:44:04.715689Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 559.75, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=16303\n", "2025-07-30T02:44:04.743360Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.66, 'cpu_percent': 0.0} curve_count=29 dropna_how=any new_dataset=nmr_obmiq_all_apply_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all_apply\n", "2025-07-30T02:44:04.781571Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=4504 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 531.06, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=11799\n", "2025-07-30T02:44:04.803113Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 531.09, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T02:44:04.828095Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 531.09, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:44:04.846333Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 531.09, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T02:44:04.858245Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 531.09, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All_Apply') save_head_info=True save_well_map=True\n", "2025-07-30T02:44:04.897592Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 531.09, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') dataset_type=Continuous df_shape=(4504, 96)\n", "2025-07-30T02:44:06.756570Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 549.01, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') processing_time=1.859\n", "2025-07-30T02:44:06.784986Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 545.76, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T02:44:06.817222Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 545.76, 'cpu_percent': 0.0}\n", "2025-07-30T02:44:06.829851Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 545.76, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-30T02:44:19.361327Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.16, 'cpu_percent': 0.0}\n", "2025-07-30T02:44:19.374617Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.17, 'cpu_percent': 0.0}\n", "2025-07-30T02:44:25.843610Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.12, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=20.985 project_name=WpIdentifier('Santos_OBMIQ_All_Apply')\n", "2025-07-30T02:44:25.881062Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.12, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All_Apply\n", "2025-07-30T02:44:26.764100Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.04, 'cpu_percent': 0.0} file_size=41421 format=markdown output_path=santos_obmiq_cum_all_apply_report.md\n", "✅ 所有井数据(预测）已保存: santos_obmiq_cum_all_apply.wp.xlsx\n", "   数据形状: (4504, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "🎉 OBMIQ数据集提取完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "    from logwp.models.well_project import WpWellProject\n", "\n", "    print(\"🔧 开始提取OBMIQ数据集(训练)...\")\n", "\n", "    try:\n", "        # 1. 提取C-1井的数据\n", "        print(\"\\n📍 提取C-1井数据(训练)...\")\n", "        c1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'C-1'\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_c_1\", c1_dataset)\n", "        c1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_c_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_c1 = WpWellProject(name=\"Santos_OBMIQ_C1\")\n", "        temp_project_c1.add_dataset(\"nmr_obmiq\", c1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        c1_path = \"santos_obmiq_cum_c1.wp.xlsx\"\n", "        writer.write(temp_project_c1, c1_path, apply_formatting=False)\n", "        report_path = temp_project_c1.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_c1_report.md\"\n", "        )\n", "\n", "        print(f\"✅ C-1井数据已保存: {c1_path}\")\n", "        print(f\"   数据形状: {c1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(c1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ C-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 2. 提取T-1井的数据\n", "        print(\"\\n📍 提取T-1井数据(训练)...\")\n", "        t1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'T-1'\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_t_1\", t1_dataset)\n", "        t1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_t_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_t1 = WpWellProject(name=\"Santos_OBMIQ_T1\")\n", "        temp_project_t1.add_dataset(\"nmr_obmc\", t1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        t1_path = \"santos_obmiq_cum_t1.wp.xlsx\"\n", "        writer.write(temp_project_t1, t1_path, apply_formatting=False)\n", "        report_path = temp_project_t1.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_t1_report.md\"\n", "        )\n", "\n", "        print(f\"✅ T-1井数据已保存: {t1_path}\")\n", "        print(f\"   数据形状: {t1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(t1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ T-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 3. 提取所有井的数据\n", "        print(\"\\n📍 提取所有井数据(训练)...\")\n", "        all_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all\", all_dataset)\n", "        all_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq\", all_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_dataset).__name__}\")\n", "\n", "        # 显示井名统计\n", "        if 'WELL_NO' in all_dataset.df.columns:\n", "            well_counts = all_dataset.df['WELL_NO'].value_counts()\n", "            print(f\"   井名分布: {dict(well_counts)}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "\n", "    try:\n", "        # 4. 提取所有井的数据(预测)\n", "        print(\"\\n📍 提取所有井数据(预测)...\")\n", "        all_apply_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves #需要包含真值，这样方便对比\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all_apply\", all_apply_dataset)\n", "        all_apply_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves_dropna, # dropna时不考虑真值\n", "            new_dataset_name=\"nmr_obmiq_all_apply_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All_Apply\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq_apply\", all_apply_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_apply_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据(预测）已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_apply_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_apply_dataset).__name__}\")\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据(预测）提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 OBMIQ数据集提取完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过OBMIQ数据集提取\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}