"""
提供专为测井数据设计的交叉验证分割器。
"""
from __future__ import annotations

from typing import Iterator, Tuple
import numpy as np
import pandas as pd

try:
    from sklearn.model_selection import BaseCrossValidator
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    # 定义一个虚拟基类以允许代码在没有sklearn的情况下导入
    BaseCrossValidator = object


class LeaveOneWellOut(BaseCrossValidator):
    """
    留一井交叉验证分割器 (Leave-One-Well-Out Cross-Validator)。

    这是一种专门为测井数据设计的交叉验证策略。在每一次迭代中，它会选择一口井
    作为测试集，而所有其他井则作为训练集。这可以有效地防止因同一口井的数据
    同时出现在训练集和测试集中而导致的“信息泄露”，从而提供对模型泛化能力
    更真实、更可靠的评估。

    此分割器与 scikit-learn 的交叉验证工具（如 `cross_val_score`）完全兼容。
    使用时，必须将包含井名的 Series 作为 `groups` 参数传入。
    """

    def __init__(self):
        if not SKLEARN_AVAILABLE:
            raise ImportError("此功能需要 'scikit-learn' 库。请运行 'pip install scikit-learn' 进行安装。")

    def get_n_splits(self, X: pd.DataFrame = None, y=None, groups: pd.Series = None) -> int:
        """
        返回交叉验证的折数（即唯一井的数量）。

        Args:
            X: 特征数据，未使用。
            y: 目标数据，未使用。
            groups (pd.Series): 一个包含每行对应井名的Pandas Series。

        Returns:
            int: 唯一井的数量。
        """
        if groups is None:
            raise ValueError("必须提供 'groups' 参数，其应为包含井名的Pandas Series。")
        return groups.nunique()

    def split(self, X: pd.DataFrame, y=None, groups: pd.Series = None) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        生成训练集/测试集的索引。

        Args:
            X (pd.DataFrame): 特征数据，其索引必须与groups的索引一致。
            y: 目标数据，未使用。
            groups (pd.Series): 一个包含每行对应井名的Pandas Series。

        Yields:
            Tuple[np.ndarray, np.ndarray]: (train_indices, test_indices) 元组。
        """
        if groups is None:
            raise ValueError("必须提供 'groups' 参数，其应为包含井名的Pandas Series。")
        if not isinstance(groups, pd.Series):
            raise TypeError("'groups' 参数必须是 pandas.Series。")
        if not X.index.equals(groups.index):
            raise ValueError("X 和 groups 的索引必须相同。")

        unique_wells = groups.unique()
        all_indices = np.arange(len(X))

        for well in unique_wells:
            test_mask = (groups == well)
            test_indices = all_indices[test_mask]
            train_indices = all_indices[~test_mask]
            yield train_indices, test_indices


class SpatialKFold(BaseCrossValidator):
    """
    空间K折交叉验证器 (Spatial K-Fold Cross-Validator)。

    此交叉验证策略专为具有空间（或时间）结构的数据（如测井深度序列）设计。
    标准的K-Fold会将相邻的数据点分到训练集和测试集中，由于测井数据的
    强自相关性，这会导致模型性能被严重高估。

    SpatialKFold通过在每个测试集折叠的两侧移除一个指定大小的“间隔”
    (gap)来解决这个问题，确保训练集和测试集在空间上是分离的。

    注意：此分割器假设输入数据 `X` 是按深度（或空间）顺序排列的。
    它不处理多井分组，应在单井数据上使用。

    Args:
        n_splits (int, optional): 折叠数量。必须至少为2。默认为5。
        gap (int, optional): 在每个测试集两侧要排除的样本数量。默认为0。
    """
    def __init__(self, n_splits: int = 5, gap: int = 0):
        if not SKLEARN_AVAILABLE:
            raise ImportError("此功能需要 'scikit-learn' 库。请运行 'pip install scikit-learn' 进行安装。")
        if not isinstance(n_splits, int) or n_splits <= 1:
            raise ValueError("n_splits 必须是大于1的整数。")
        if not isinstance(gap, int) or gap < 0:
            raise ValueError("gap 必须是非负整数。")
        self.n_splits = n_splits
        self.gap = gap

    def get_n_splits(self, X=None, y=None, groups=None) -> int:
        """返回交叉验证的折数。"""
        return self.n_splits

    def split(self, X, y=None, groups=None) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        生成训练集/测试集的索引。

        Args:
            X: 特征数据。其行数代表样本总数。
            y: 目标数据，未使用。
            groups: 组信息，未使用。

        Yields:
            Tuple[np.ndarray, np.ndarray]: (train_indices, test_indices) 元组。
        """
        n_samples = len(X)
        indices = np.arange(n_samples)

        fold_sizes = np.full(self.n_splits, n_samples // self.n_splits, dtype=int)
        fold_sizes[:n_samples % self.n_splits] += 1

        current = 0
        for fold_size in fold_sizes:
            start, stop = current, current + fold_size
            test_indices = indices[start:stop]

            remove_start = max(0, start - self.gap)
            remove_stop = min(n_samples, stop + self.gap)

            train_mask = np.ones(n_samples, dtype=bool)
            train_mask[remove_start:remove_stop] = False
            train_indices = indices[train_mask]

            yield train_indices, test_indices
            current = stop
