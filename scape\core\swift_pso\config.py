"""scape.core.swift_pso.config - SWIFT-PSO配置模型定义

为三个步骤分别定义类型安全的Pydantic配置模型，严格遵循关注点分离原则。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO配置层
设计原则: 类型安全、关注点分离、数据验证
性能特征: Pydantic v2性能优化、字段验证、序列化

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- DV-1: pydantic v2模型
- DV-2: 字段验证和约束

References
----------
- 《SCAPE_MS_方法说明书》§4 - SWIFT-PSO算法参数定义
- 《logwp/extras/tracking/机器学习组件开发框架》§3.1 - 配置模型规范
"""

from __future__ import annotations

from typing import Any, Dict
from pydantic import BaseModel, Field, model_validator


class SwiftPsoTrainingConfig(BaseModel):
    """SWIFT-PSO训练步骤的配置模型。

    包含所有影响训练计算结果和科学逻辑的模型/算法参数。
    执行层参数（如backend）不包含在此配置中，而是作为facade函数的直接参数。

    Architecture
    ------------
    层次/依赖: scape/core层，SWIFT-PSO训练配置
    设计原则: 类型安全、数据验证、可复现性
    性能特征: Pydantic v2优化、字段验证

    References
    ----------
    - 《SCAPE_MS_方法说明书》§4.1-4.3 - SWIFT-PSO训练流程参数定义
    """

    # Bootstrap阶段参数
    bootstrap_iterations: int = Field(
        20,
        description="Bootstrap迭代次数",
        gt=0,
        le=100
    )
    narrow_window_factor: float = Field(
        0.3,
        description="Fine-Tuning阶段的窄窗口缩放因子",
        gt=0,
        le=1
    )
    bootstrap_sample_ratio: float = Field(
        0.8,
        description="井内Bootstrap抽样比例",
        gt=0,
        le=1
    )

    # 诊断性功能开关
    enable_fold_diagnostics: bool = Field(
        True,
        description="是否为每次Bootstrap+LOWO运行保存详细的诊断日志"
    )

    random_seed: int | None = Field(
        2025,
        description="用于所有随机操作的全局种子，以确保结果的可复现性。设置为None则不固定种子。"
    )

    optimization_params: list[str] = Field(
        default_factory=lambda: [
            'log10_KSDR_A', 'PHIT_EXP', 'T2LM_EXP', 'RHO_NMR',
            'log10_KMACRO_A', 'KMACRO_B', 'Vmacro_min',
            'log10_T2cutoff_short', 'log10_T2cutoff_long', 'beta_1', 'beta_2',
            'delta_MDT'
        ],
        description="需要优化的参数列表。列表外的参数将被视为固定参数。"
    )

    fixed_params: dict[str, float] = Field(
        default_factory=dict,
        description="固定参数及其值。此处的参数不应出现在 optimization_params 中。"
    )

    # PSO算法参数 - Bootstrap+LOWO阶段
    pso_config_lowo: Dict[str, Any] = Field(
        default_factory=lambda: {
            "n_particles": 100,
            "max_iterations": 300,
            "w_strategy": ("linear_decay", 0.9, 0.4),
            "c1": 1.8,
            "c2": 1.8,
            "loss_function_mode": "bootstrap",
            "boundary_strategy": "hard_reflection",
            "enable_early_stopping": True,
            "early_stopping_mode": "lowo",
            # 新增：非产层惩罚项超参数
            "lambda_penalty": 0.5,
            "k_penalty_thresh": 1.0,
            # 新增：PZI样本加权超参数
            "w_prod": 1.0,
            "w_nonprod": 0.1,
            "parameters_boundaries": {
                'log10_KSDR_A':         (-2.0, -1.6, 1.6, 2.0),
                'PHIT_EXP':             (1.0, 1.4, 4.6, 5.0),
                'T2LM_EXP':             (1.0, 1.4, 4.6, 5.0),
                'RHO_NMR':              (0.1, 5.09, 45.01, 50.0),
                'log10_KMACRO_A':       (-2.0, -1.6, 1.6, 2.0),
                'KMACRO_B':             (1.0, 1.4, 4.6, 5.0),
                'Vmacro_min':           (0.01, 0.033, 0.137, 0.15),
                'log10_T2cutoff_short': (1.5, 1.6, 2.4, 2.5),
                'log10_T2cutoff_long':  (3, 3.05, 3.45, 3.5),
                'beta_1':               (0.02, 0.068, 0.452, 0.5),
                'beta_2':               (0.5, 0.75, 2.75, 3.0),
                'delta_MDT':            (-0.5, -0.35, 0.85, 1.0)
            },
            # 数据依赖参数占位符，由工作流脚本动态填充
            "t2_time": None,
            "t2_range_min": None,
            "t2_range_max": None,
            "curve_to_columns_map": None,
            "t2_p50_ref": None,
            "phit_nmr_ref": None
        },
        description="Bootstrap+LOWO阶段的PSO配置字典"
    )

    # PSO算法参数 - Fine-Tuning阶段
    pso_config_finetune: Dict[str, Any] = Field(
        default_factory=lambda: {
            "n_particles": 60,
            "max_iterations": 100,
            "w_strategy": ("linear_decay", 0.8, 0.3),
            "c1": 1.6,
            "c2": 1.6,
            "loss_function_mode": "finetune",
            "boundary_strategy": "boundary_jitter",
            "enable_early_stopping": True,
            "early_stopping_mode": "finetune",
            # 新增：非产层惩罚项超参数
            "lambda_penalty": 0.5,
            "k_penalty_thresh": 1.0,
            # 新增：PZI样本加权超参数
            "w_prod": 1.0,
            "w_nonprod": 0.1,
            "parameters_boundaries": {
                'log10_KSDR_A':         (-2.0, -1.6, 1.6, 2.0),
                'PHIT_EXP':             (1.0, 1.4, 4.6, 5.0),
                'T2LM_EXP':             (1.0, 1.4, 4.6, 5.0),
                'RHO_NMR':              (0.1, 5.09, 45.01, 50.0),
                'log10_KMACRO_A':       (-2.0, -1.6, 1.6, 2.0),
                'KMACRO_B':             (1.0, 1.4, 4.6, 5.0),
                'Vmacro_min':           (0.01, 0.033, 0.137, 0.15),
                'log10_T2cutoff_short': (1.5, 1.6, 2.4, 2.5),
                'log10_T2cutoff_long':  (3, 3.05, 3.45, 3.5),
                'beta_1':               (0.02, 0.068, 0.452, 0.5),
                'beta_2':               (0.5, 0.75, 2.75, 3.0),
                'delta_MDT':            (-0.5, -0.35, 0.85, 1.0)
            },
            # 数据依赖参数占位符，由工作流脚本动态填充
            "t2_time": None,
            "t2_range_min": None,
            "t2_range_max": None,
            "curve_to_columns_map": None,
            "t2_p50_ref": None,
            "phit_nmr_ref": None
        },
        description="Fine-Tuning阶段的PSO配置字典"
    )

    @classmethod
    def create_default(cls) -> "SwiftPsoTrainingConfig":
        """创建默认的训练配置。

        Returns:
            SwiftPsoTrainingConfig: 包含默认参数的配置实例

        Note:
            数据依赖参数（t2_time, t2_p50_ref等）需要由工作流脚本动态注入
        """
        # 现在此方法非常简洁，只依赖于字段的默认值/工厂
        return cls()

    @model_validator(mode='after')
    def check_params_exclusive(self) -> "SwiftPsoTrainingConfig":
        """验证优化参数和固定参数没有重叠。"""
        if self.optimization_params and self.fixed_params:
            overlap = set(self.optimization_params) & set(self.fixed_params.keys())
            if overlap:
                raise ValueError(f"参数不能同时出现在 'optimization_params' 和 'fixed_params' 中: {overlap}")
        return self


class SwiftPsoPredictionConfig(BaseModel):
    """SWIFT-PSO预测步骤的配置模型。

    包含影响预测计算结果的模型参数。数据选择器参数（如曲线名称）
    和执行层参数（如backend）不包含在此配置中。

    Architecture
    ------------
    层次/依赖: scape/core层，SWIFT-PSO预测配置
    设计原则: 类型安全、数据验证、可复现性
    性能特征: Pydantic v2优化、字段验证

    References
    ----------
    - 《SCAPE_MS_方法说明书》§4.4 - FOSTER-NMR预测应用参数定义
    """

    # 计算范围参数
    t2_range_min: float | None = Field(
        None,
        description="T2计算范围最小值（ms）",
        gt=0
    )
    t2_range_max: float | None = Field(
        None,
        description="T2计算范围最大值（ms）",
        gt=0
    )



    @classmethod
    def create_default(cls) -> "SwiftPsoPredictionConfig":
        """创建默认的预测配置。

        Returns:
            SwiftPsoPredictionConfig: 包含默认参数的配置实例
        """
        return cls()


class TsneVisualConfig(BaseModel):
    """t-SNE可视化步骤的配置模型。

    包含影响t-SNE计算结果的算法参数。绘图样式参数（如plot_profile_name）
    不包含在此配置中，而是作为facade函数的直接参数。

    Architecture
    ------------
    层次/依赖: scape/core层，t-SNE可视化配置
    设计原则: 类型安全、数据验证、可复现性
    性能特征: Pydantic v2优化、字段验证

    References
    ----------
    - 《SCAPE_MS_方法说明书》§4.4.5 - t-SNE可视化参数定义
    """

    # t-SNE算法参数
    perplexity: int = Field(
        30,
        description="t-SNE算法的Perplexity参数",
        gt=0,
        le=100
    )
    learning_rate: float = Field(
        200.0,
        description="t-SNE算法的学习率",
        gt=0
    )
    n_iter: int = Field(
        1000,
        description="t-SNE的优化迭代次数",
        gt=250,
        le=5000
    )
    init: str = Field(
        "pca",
        description="t-SNE初始化方法",
        pattern="^(pca|random)$"
    )
    random_state: int | None = Field(
        42,
        description="随机种子，确保可复现性"
    )

    # 聚类算法选择
    cluster_method: str = Field(
        "kmeans",
        description="聚类算法选择，'kmeans' 或 'dbscan'",
        pattern="^(kmeans|dbscan)$"
    )

    # K-means 专用参数
    n_clusters: int = Field(
        4,
        description="K-means聚类的簇数量",
        gt=1
    )

    # DBSCAN 专用参数
    dbscan_eps: float = Field(0.5, description="DBSCAN的eps参数", gt=0)
    dbscan_min_samples: int = Field(5, description="DBSCAN的min_samples参数", gt=0)

    @classmethod
    def create_default(cls) -> "TsneVisualConfig":
        """创建默认的t-SNE可视化配置。

        Returns:
            TsneVisualConfig: 包含默认参数的配置实例
        """
        return cls()
