@echo off
echo ============================================
echo ? Cleaning Python project cache (Windows CMD)
echo ============================================

:: �0�1�1�7�1�7 pyc �1�7�0�4�1�7�1�7�1�7�1�7�1�7�0�8�1�7�1�7
echo ? Deleting *.pyc files...
for /r %%i in (*.pyc) do (
    if exist "%%i" del /f /q "%%i"
)

:: �0�1�1�7�1�7 __pycache__ �1�7�0�4�1�7�1�7�1�7
echo ? Deleting __pycache__ folders...
for /d /r %%i in (__pycache__) do (
    if exist "%%i" rd /s /q "%%i"
)

:: �0�1�1�7�1�7 .pytest_cache
if exist ".pytest_cache" (
    echo ? Deleting .pytest_cache...
    rd /s /q ".pytest_cache"
)

:: �0�1�1�7�1�7 .mypy_cache
if exist ".mypy_cache" (
    echo ? Deleting .mypy_cache...
    rd /s /q ".mypy_cache"
)

:: �0�1�1�7�1�7 .ipynb_checkpoints
echo ? Deleting .ipynb_checkpoints...
for /d /r %%i in (.ipynb_checkpoints) do (
    if exist "%%i" rd /s /q "%%i"
)

:: �0�1�1�7�1�7 htmlcov
if exist "htmlcov" (
    echo ? Deleting htmlcov...
    rd /s /q "htmlcov"
)

:: �0�1�1�7�1�7 .coverage
if exist ".coverage" (
    echo ? Deleting .coverage...
    del /f /q ".coverage"
)

:: �0�1�1�7�1�7 VS Code �1�7�1�7 .python-language-server �1�7�1�7�1�7���1�7�1�7�0�5�1�7�1�7
if exist ".vscode\.python-language-server" (
    echo ? Deleting VS Code Python language server cache...
    rd /s /q ".vscode\.python-language-server"
)

echo.
echo ? Clean complete.
