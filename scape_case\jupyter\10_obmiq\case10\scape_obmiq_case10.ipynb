{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ 端到端工作流 (PyTorch版)\n", "\n", "- 表格数据使用更多的输入曲线\n", "  - DEN, CN, DT, RD_LOG10, RS_LOG10, DRES\n", "  - T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10\n", "  - PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR\n", "  - VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO\n", "  - LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY\n", "- MLP架构改为两层"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境设置与导入完成。\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "from pathlib import Path\n", "\n", "# 导入 SCAPE 和 LOGWP 的核心组件\n", "from logwp.io import WpExcelReader\n", "from logwp.extras.tracking import RunContext\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "\n", "# 导入新版OBMIQ组件\n", "import scape.core.obmiq.plot_profiles # 导入以注册绘图模板\n", "from scape.core.obmiq import (\n", "    run_obmiq_training_step,\n", "    run_obmiq_prediction_step,\n", "    ObmiqTrainingConfig,\n", "    ObmiqPredictionConfig,\n", "    ObmiqTrainingArtifacts,\n", "    ObmiqArtifactHandler\n", ")\n", "from logwp.models.constants import WpDepthRole\n", "\n", "# 设置\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', 50)\n", "\n", "print(\"环境设置与导入完成。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T05:50:54.338913Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2550.75, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx\n", "2025-07-30T05:50:54.386367Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2550.75, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx file_size_mb=1.28 sheet_count=1\n", "2025-07-30T05:50:54.408498Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2550.75, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all\n", "2025-07-30T05:50:54.421550Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2550.75, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all\n", "2025-07-30T05:50:54.437281Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2550.77, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:50:54.485895Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2550.77, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=33 well_curves=1\n", "2025-07-30T05:50:56.798514Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2554.05, 'cpu_percent': 0.0} shape=(2159, 96) sheet_name=nmr_obmiq\n", "2025-07-30T05:50:56.830064Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2554.06, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq')\n", "2025-07-30T05:50:56.845847Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2554.06, 'cpu_percent': 0.0} curve_count=33 dataset_name=nmr_obmiq dataset_type=Continuous df_shape=(2159, 96) processing_time=2.409\n", "2025-07-30T05:50:56.877755Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2554.06, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-30T05:50:56.909237Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2554.06, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-30T05:50:56.925259Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2554.06, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx processing_time=2.587 project_name=WpIdentifier('santos_obmiq_cum_all')\n", "2025-07-30T05:50:56.948841Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2554.06, 'cpu_percent': 0.0} extracted_curve_count=31 operation=extract_curve_dataframe_bundle\n", "2025-07-30T05:50:56.987273Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2556.84, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['WELL_NO', 'RS_LOG10', 'SWI_NMR', 'DT', 'T2_P50_LOG10', 'T2_P20_LOG10', 'SMESO', 'BFV_NMR', 'SWB_NMR', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'DEN', 'T2LM_LOG10', 'PHIT_NMR', 'MD', 'CN', 'DRES', 'LT2STDDEV_FFI', 'RD_LOG10', 'DT2_P50', 'VMESO', 'LSKEW_FFI', 'DPHIT_NMR', 'FFV_NMR', 'VMICRO', 'SFF_NMR', 'BVI_NMR', 'LKURT_FFI', 'SMACRO', 'T2LM_LONG_LOG10', 'PHIE_NMR', 'VMACRO', 'SMICRO'] operation=extract_metadata output_curve_count=33 output_curves=['WELL_NO', 'RS_LOG10', 'SWI_NMR', 'DT', 'T2_P50_LOG10', 'T2_P20_LOG10', 'SMESO', 'BFV_NMR', 'SWB_NMR', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'DEN', 'T2LM_LOG10', 'PHIT_NMR', 'MD', 'CN', 'DRES', 'LT2STDDEV_FFI', 'RD_LOG10', 'DT2_P50', 'VMESO', 'LSKEW_FFI', 'DPHIT_NMR', 'FFV_NMR', 'VMICRO', 'SFF_NMR', 'BVI_NMR', 'LKURT_FFI', 'SMACRO', 'T2LM_LONG_LOG10', 'PHIE_NMR', 'VMACRO', 'SMICRO']\n", "2025-07-30T05:50:57.002925Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2556.84, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx\n", "2025-07-30T05:50:57.034448Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2556.84, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx file_size_mb=2.56 sheet_count=1\n", "2025-07-30T05:50:57.050232Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2556.84, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all_apply\n", "2025-07-30T05:50:57.069968Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2556.84, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all_apply\n", "2025-07-30T05:50:57.090012Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2556.84, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:50:57.132189Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2556.84, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=33 well_curves=1\n", "2025-07-30T05:51:02.592619Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1605.73, 'cpu_percent': 0.0} shape=(4503, 96) sheet_name=nmr_obmiq_apply\n", "2025-07-30T05:51:02.632502Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1605.75, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_apply')\n", "2025-07-30T05:51:02.640147Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1605.75, 'cpu_percent': 0.0} curve_count=33 dataset_name=nmr_obmiq_apply dataset_type=Continuous df_shape=(4503, 96) processing_time=5.55\n", "2025-07-30T05:51:02.687745Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1605.75, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-30T05:51:02.710413Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1605.75, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-30T05:51:02.719690Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1605.75, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=5.717 project_name=WpIdentifier('santos_obmiq_cum_all_apply')\n", "2025-07-30T05:51:02.755099Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1604.76, 'cpu_percent': 0.0} extracted_curve_count=31 operation=extract_curve_dataframe_bundle\n", "2025-07-30T05:51:02.800639Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1612.55, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['WELL_NO', 'RS_LOG10', 'SWI_NMR', 'DT', 'T2_P50_LOG10', 'T2_P20_LOG10', 'SMESO', 'BFV_NMR', 'SWB_NMR', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'DEN', 'T2LM_LOG10', 'PHIT_NMR', 'MD', 'CN', 'DRES', 'LT2STDDEV_FFI', 'RD_LOG10', 'DT2_P50', 'VMESO', 'LSKEW_FFI', 'DPHIT_NMR', 'FFV_NMR', 'VMICRO', 'SFF_NMR', 'BVI_NMR', 'LKURT_FFI', 'SMACRO', 'T2LM_LONG_LOG10', 'PHIE_NMR', 'VMACRO', 'SMICRO'] operation=extract_metadata output_curve_count=33 output_curves=['WELL_NO', 'RS_LOG10', 'SWI_NMR', 'DT', 'T2_P50_LOG10', 'T2_P20_LOG10', 'SMESO', 'BFV_NMR', 'SWB_NMR', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'DEN', 'T2LM_LOG10', 'PHIT_NMR', 'MD', 'CN', 'DRES', 'LT2STDDEV_FFI', 'RD_LOG10', 'DT2_P50', 'VMESO', 'LSKEW_FFI', 'DPHIT_NMR', 'FFV_NMR', 'VMICRO', 'SFF_NMR', 'BVI_NMR', 'LKURT_FFI', 'SMACRO', 'T2LM_LONG_LOG10', 'PHIE_NMR', 'VMACRO', 'SMICRO']\n", "数据加载成功。\n", "训练数据束: nmr_obmiq, 形状: (2159, 96), 井名: WELL_NO\n", "预测数据束: nmr_obmiq_apply, 形状: (4503, 96)\n", "T2时间轴长度: 64\n"]}], "source": ["# --- 请在此处填写您的数据路径 ---\n", "\n", "\n", "TRAIN_WP_FILE_PATH = \"santos_obmiq_cum_all.wp.xlsx\"\n", "TRAIN_DATASET_NAME = \"nmr_obmiq\"\n", "\n", "PRED_WP_FILE_PATH = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "PREDICTION_DATASET_NAME = \"nmr_obmiq_apply\"\n", "# ---------------------------------\n", "\n", "# 加载整个工区文件\n", "try:\n", "    # 获取训练和预测数据束\n", "    reader = WpExcelReader()\n", "    project = reader.read(TRAIN_WP_FILE_PATH)\n", "    train_ds = project.get_dataset(TRAIN_DATASET_NAME)\n", "    train_bundle = train_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "    well_no_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "    depth_name = train_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "    pred_reader = WpExcelReader()\n", "    pred_project = pred_reader.read(PRED_WP_FILE_PATH)\n", "    prediction_ds = pred_project.get_dataset(PREDICTION_DATASET_NAME)\n", "    prediction_bundle = prediction_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "\n", "    # 从 head_info 获取 T2 时间轴\n", "    # 假设所有井共享一个T2轴定义\n", "\n", "    t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "    t2_time_array = t2_axis_info.calculate_values()\n", "\n", "    print(\"数据加载成功。\")\n", "    print(f\"训练数据束: {train_ds.name}, 形状: {train_ds.df.shape}, 井名: {well_no_name}\")\n", "    print(f\"预测数据束: {prediction_ds.name}, 形状: {prediction_ds.df.shape}\")\n", "    print(f\"T2时间轴长度: {len(t2_time_array)}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 数据加载失败: {e}\")\n", "    project = None\n", "    pred_project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 运行 OBMIQ 工作流\n", "\n", "我们使用 `RunContext` 来包裹整个实验流程，以确保所有参数、指标和产物都被追踪。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T05:51:02.846946Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1609.39, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\obmiq_run_pytorch_20250730_135102 run_id=20250730-055102-85cdf887\n", "--- 开始 OBMIQ 训练步骤 (PyTorch) ---\n", "2025-07-30T05:51:02.876342Z [info     ] ===== OBMIQ Training Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1609.39, 'cpu_percent': 0.0}\n", "2025-07-30T05:51:02.881982Z [info     ] 曲线名已成功解析为DataFrame列名。          [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1609.39, 'cpu_percent': 0.0}\n", "2025-07-30T05:51:02.899235Z [info     ] --- Stage 0: Saving Configuration Snapshot --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1609.39, 'cpu_percent': 0.0}\n", "2025-07-30T05:51:02.915002Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.configs.training_config_snapshot artifact_path=obmiq_training_pytorch\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1609.39, 'cpu_percent': 0.0} description=Snapshot of the training configuration used for this run. operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T05:51:02.928851Z [info     ] --- Stage 2: Hyperparameter Tuning using LOWO-CV --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1609.39, 'cpu_percent': 0.0}\n", "2025-07-30T05:51:02.944875Z [info     ] Tuning procedure started on device: cuda [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1609.39, 'cpu_percent': 0.0}\n", "2025-07-30T05:51:02.972412Z [info     ] --- Starting CV Fold 1/2 ---   [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1611.19, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-30 13:51:03,005] A new study created in memory with name: no-name-36b4e6d6-fa91-4a0f-8cc6-2f21e33887b7\n", "[I 2025-07-30 13:51:24,649] Trial 0 finished with value: -6.193633675575256 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.3640138491216332, 'learning_rate': 0.0016788684518215286, 'weight_decay': 2.030586856695154e-05}. Best is trial 0 with value: -6.193633675575256.\n", "[I 2025-07-30 13:51:33,886] Trial 1 finished with value: -0.8521706461906433 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 8, 'dropout_rate': 0.33448960605907596, 'learning_rate': 0.0006804236435200252, 'weight_decay': 1.2251585474088342e-05}. Best is trial 0 with value: -6.193633675575256.\n", "[I 2025-07-30 13:51:55,935] Trial 2 finished with value: -0.8054924458265305 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 8, 'dropout_rate': 0.43318694482187037, 'learning_rate': 0.00018936881691275753, 'weight_decay': 1.3306039678217327e-05}. Best is trial 0 with value: -6.193633675575256.\n", "[I 2025-07-30 13:52:18,894] Trial 3 finished with value: -1.2187440395355225 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units_1': 32, 'mlp_units_2': 32, 'dropout_rate': 0.3269246458099887, 'learning_rate': 0.000268418638152583, 'weight_decay': 1.1732851241717957e-05}. Best is trial 0 with value: -6.193633675575256.\n", "[I 2025-07-30 13:52:40,985] Trial 4 finished with value: -10.066782474517822 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.26153311301720694, 'learning_rate': 0.0072679819926030715, 'weight_decay': 1.2669324808116801e-05}. Best is trial 4 with value: -10.066782474517822.\n", "[I 2025-07-30 13:52:41,691] Trial 5 pruned. \n", "[I 2025-07-30 13:52:43,172] Trial 6 pruned. \n", "[I 2025-07-30 13:52:45,519] Trial 7 pruned. \n", "[I 2025-07-30 13:53:08,615] Trial 8 finished with value: -9.304112672805786 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units_1': 16, 'mlp_units_2': 16, 'dropout_rate': 0.4537784761430098, 'learning_rate': 0.005621899519306341, 'weight_decay': 8.911163132701357e-05}. Best is trial 4 with value: -10.066782474517822.\n", "[I 2025-07-30 13:53:08,807] Trial 9 pruned. \n", "[I 2025-07-30 13:53:31,350] Trial 10 finished with value: -10.023076057434082 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.20084457146306064, 'learning_rate': 0.007331924468642218, 'weight_decay': 0.0006123467466753696}. Best is trial 4 with value: -10.066782474517822.\n", "[I 2025-07-30 13:53:47,876] Trial 11 finished with value: -9.978509664535522 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.20083524871899652, 'learning_rate': 0.009813186893095379, 'weight_decay': 0.0004916042664025377}. Best is trial 4 with value: -10.066782474517822.\n", "[I 2025-07-30 13:53:48,052] Trial 12 pruned. \n", "[I 2025-07-30 13:53:48,226] Trial 13 pruned. \n", "[I 2025-07-30 13:54:02,748] Trial 14 finished with value: -9.352648735046387 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.5149930479160383, 'learning_rate': 0.008886246355822209, 'weight_decay': 3.693480795794046e-05}. Best is trial 4 with value: -10.066782474517822.\n", "[I 2025-07-30 13:54:02,980] Trial 15 pruned. \n", "[I 2025-07-30 13:54:03,575] Trial 16 pruned. \n", "[I 2025-07-30 13:54:03,829] Trial 17 pruned. \n", "[I 2025-07-30 13:54:26,424] Trial 18 finished with value: -10.140472650527954 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.2283498520707152, 'learning_rate': 0.006589324317070449, 'weight_decay': 2.9219960179714146e-05}. Best is trial 18 with value: -10.140472650527954.\n", "[I 2025-07-30 13:54:26,610] Trial 19 pruned. \n", "[I 2025-07-30 13:54:26,784] Trial 20 pruned. \n", "[I 2025-07-30 13:54:47,552] Trial 21 finished with value: -10.068859338760376 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.2201245404075145, 'learning_rate': 0.007044429516613875, 'weight_decay': 7.313644403062133e-05}. Best is trial 18 with value: -10.140472650527954.\n", "[I 2025-07-30 13:54:47,752] Trial 22 pruned. \n", "[I 2025-07-30 13:54:47,952] Trial 23 pruned. \n", "[I 2025-07-30 13:54:48,101] Trial 24 pruned. \n", "[I 2025-07-30 13:54:48,516] Trial 25 pruned. \n", "[I 2025-07-30 13:54:48,672] Trial 26 pruned. \n", "[I 2025-07-30 13:54:59,315] Trial 27 pruned. \n", "[I 2025-07-30 13:54:59,489] Trial 28 pruned. \n", "[I 2025-07-30 13:54:59,703] Trial 29 pruned. \n", "[I 2025-07-30 13:54:59,917] Trial 30 pruned. \n", "[I 2025-07-30 13:55:22,668] Trial 31 finished with value: -10.131622076034546 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.21831716999374037, 'learning_rate': 0.007070114557101376, 'weight_decay': 1.672334742278924e-05}. Best is trial 18 with value: -10.140472650527954.\n", "[I 2025-07-30 13:55:23,405] Trial 32 pruned. \n", "[I 2025-07-30 13:55:23,579] Trial 33 pruned. \n", "[I 2025-07-30 13:55:23,792] Trial 34 pruned. \n", "[I 2025-07-30 13:55:23,965] Trial 35 pruned. \n", "[I 2025-07-30 13:55:24,125] Trial 36 pruned. \n", "[I 2025-07-30 13:55:24,308] Trial 37 pruned. \n", "[I 2025-07-30 13:55:24,508] Trial 38 pruned. \n", "[I 2025-07-30 13:55:35,784] Trial 39 finished with value: -9.403120040893555 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 32, 'dropout_rate': 0.34878112776939174, 'learning_rate': 0.009958544651036828, 'weight_decay': 1.4413496046538283e-05}. Best is trial 18 with value: -10.140472650527954.\n", "[I 2025-07-30 13:55:35,943] Trial 40 pruned. \n", "[I 2025-07-30 13:55:36,106] Trial 41 pruned. \n", "[I 2025-07-30 13:55:37,309] Trial 42 pruned. \n", "[I 2025-07-30 13:55:37,477] Trial 43 pruned. \n", "[I 2025-07-30 13:55:37,650] Trial 44 pruned. \n", "[I 2025-07-30 13:55:37,850] Trial 45 pruned. \n", "[I 2025-07-30 13:55:38,091] Trial 46 pruned. \n", "[I 2025-07-30 13:55:49,387] Trial 47 pruned. \n", "[I 2025-07-30 13:55:49,548] Trial 48 pruned. \n", "[I 2025-07-30 13:55:49,708] Trial 49 pruned. \n", "[I 2025-07-30 13:55:49,889] Trial 50 pruned. \n", "[I 2025-07-30 13:56:06,075] Trial 51 pruned. \n", "[I 2025-07-30 13:56:20,013] Trial 52 finished with value: -9.548341274261475 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.25365924973069165, 'learning_rate': 0.008409582244097644, 'weight_decay': 0.0008139120202062906}. Best is trial 18 with value: -10.140472650527954.\n", "[I 2025-07-30 13:56:23,307] Trial 53 pruned. \n", "[I 2025-07-30 13:56:23,521] Trial 54 pruned. \n", "[I 2025-07-30 13:56:23,734] Trial 55 pruned. \n", "[I 2025-07-30 13:56:23,934] Trial 56 pruned. \n", "[I 2025-07-30 13:56:24,134] Trial 57 pruned. \n", "[I 2025-07-30 13:56:24,350] Trial 58 pruned. \n", "[I 2025-07-30 13:56:24,561] Trial 59 pruned. \n", "[I 2025-07-30 13:56:24,788] Trial 60 pruned. \n", "[I 2025-07-30 13:56:42,071] Trial 61 pruned. \n", "[I 2025-07-30 13:56:42,221] Trial 62 pruned. \n", "[I 2025-07-30 13:57:04,906] Trial 63 finished with value: -10.106955528259277 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.2333554629443304, 'learning_rate': 0.008074582472627021, 'weight_decay': 0.0003220859791594353}. Best is trial 18 with value: -10.140472650527954.\n", "[I 2025-07-30 13:57:05,069] Trial 64 pruned. \n", "[I 2025-07-30 13:57:05,297] Trial 65 pruned. \n", "[I 2025-07-30 13:57:29,466] Trial 66 finished with value: -10.166960716247559 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.23228664220914955, 'learning_rate': 0.007332273636674879, 'weight_decay': 0.00015025702839655604}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 13:57:29,991] Trial 67 pruned. \n", "[I 2025-07-30 13:57:30,192] Trial 68 pruned. \n", "[I 2025-07-30 13:57:30,405] Trial 69 pruned. \n", "[I 2025-07-30 13:57:30,600] Trial 70 pruned. \n", "[I 2025-07-30 13:57:30,840] Trial 71 pruned. \n", "[I 2025-07-30 13:57:37,255] Trial 72 pruned. \n", "[I 2025-07-30 13:57:37,562] Trial 73 pruned. \n", "[I 2025-07-30 13:58:00,051] Trial 74 finished with value: -10.13603687286377 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.21097791246660114, 'learning_rate': 0.009025175887560624, 'weight_decay': 0.0003833839146571172}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 13:58:00,272] Trial 75 pruned. \n", "[I 2025-07-30 13:58:14,139] Trial 76 pruned. \n", "[I 2025-07-30 13:58:14,316] Trial 77 pruned. \n", "[I 2025-07-30 13:58:14,528] Trial 78 pruned. \n", "[I 2025-07-30 13:58:14,729] Trial 79 pruned. \n", "[I 2025-07-30 13:58:14,921] Trial 80 pruned. \n", "[I 2025-07-30 13:58:28,480] Trial 81 finished with value: -9.686470985412598 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.21054830807728997, 'learning_rate': 0.00908626410650083, 'weight_decay': 0.0002715662687346797}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 13:58:28,806] Trial 82 pruned. \n", "[I 2025-07-30 13:58:28,991] Trial 83 pruned. \n", "[I 2025-07-30 13:58:39,771] Trial 84 pruned. \n", "[I 2025-07-30 13:58:39,998] Trial 85 pruned. \n", "[I 2025-07-30 13:58:40,216] Trial 86 pruned. \n", "[I 2025-07-30 13:58:41,468] Trial 87 pruned. \n", "[I 2025-07-30 13:58:41,697] Trial 88 pruned. \n", "[I 2025-07-30 13:58:55,565] Trial 89 pruned. \n", "[I 2025-07-30 13:58:55,751] Trial 90 pruned. \n", "[I 2025-07-30 13:58:55,965] Trial 91 pruned. \n", "[I 2025-07-30 13:59:15,780] Trial 92 finished with value: -9.994929075241089 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.21365428908013498, 'learning_rate': 0.00911047370159306, 'weight_decay': 0.00036846762440638785}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 13:59:15,976] Trial 93 pruned. \n", "[I 2025-07-30 13:59:16,152] Trial 94 pruned. \n", "[I 2025-07-30 13:59:16,382] Trial 95 pruned. \n", "[I 2025-07-30 13:59:16,549] Trial 96 pruned. \n", "[I 2025-07-30 13:59:29,258] Trial 97 pruned. \n", "[I 2025-07-30 13:59:29,415] Trial 98 pruned. \n", "[I 2025-07-30 13:59:41,081] Trial 99 finished with value: -9.5794038772583 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 16, 'mlp_units_2': 16, 'dropout_rate': 0.21348191560856095, 'learning_rate': 0.009712882547096211, 'weight_decay': 3.7085264330044637e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 13:59:41,241] Trial 100 pruned. \n", "[I 2025-07-30 13:59:41,575] Trial 101 pruned. \n", "[I 2025-07-30 13:59:57,731] Trial 102 finished with value: -9.94186782836914 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.2402542659647739, 'learning_rate': 0.008978821023099898, 'weight_decay': 0.0002543613115108786}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 13:59:57,924] Trial 103 pruned. \n", "[I 2025-07-30 13:59:58,104] Trial 104 pruned. \n", "[I 2025-07-30 13:59:58,306] Trial 105 pruned. \n", "[I 2025-07-30 13:59:58,487] Trial 106 pruned. \n", "[I 2025-07-30 14:00:13,067] Trial 107 finished with value: -9.840802431106567 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.24770111768823053, 'learning_rate': 0.009199039087067306, 'weight_decay': 0.00019278902637613588}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:00:13,246] Trial 108 pruned. \n", "[I 2025-07-30 14:00:13,598] Trial 109 pruned. \n", "[I 2025-07-30 14:00:13,764] Trial 110 pruned. \n", "[I 2025-07-30 14:00:24,499] Trial 111 pruned. \n", "[I 2025-07-30 14:00:24,687] Trial 112 pruned. \n", "[I 2025-07-30 14:00:24,887] Trial 113 pruned. \n", "[I 2025-07-30 14:00:25,188] Trial 114 pruned. \n", "[I 2025-07-30 14:00:25,349] Trial 115 pruned. \n", "[I 2025-07-30 14:00:38,323] Trial 116 finished with value: -9.8240065574646 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.24719692286515604, 'learning_rate': 0.009954769408885308, 'weight_decay': 1.4422847010021982e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:00:38,476] Trial 117 pruned. \n", "[I 2025-07-30 14:00:38,789] Trial 118 pruned. \n", "[I 2025-07-30 14:00:39,021] Trial 119 pruned. \n", "[I 2025-07-30 14:00:39,205] Trial 120 pruned. \n", "[I 2025-07-30 14:00:57,677] Trial 121 finished with value: -9.946431875228882 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.24365016375534204, 'learning_rate': 0.009834990850068075, 'weight_decay': 1.020561211046741e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:01:20,046] Trial 122 finished with value: -9.954909086227417 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.23944932663898985, 'learning_rate': 0.008756495995793546, 'weight_decay': 1.1194224141906124e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:01:20,739] Trial 123 pruned. \n", "[I 2025-07-30 14:01:20,906] Trial 124 pruned. \n", "[I 2025-07-30 14:01:21,088] Trial 125 pruned. \n", "[I 2025-07-30 14:01:38,316] Trial 126 finished with value: -9.95624327659607 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.21681812663647615, 'learning_rate': 0.009969712614030696, 'weight_decay': 1.310311975526059e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:01:53,981] Trial 127 pruned. \n", "[I 2025-07-30 14:01:54,167] Trial 128 pruned. \n", "[I 2025-07-30 14:01:54,355] Trial 129 pruned. \n", "[I 2025-07-30 14:01:54,731] Trial 130 pruned. \n", "[I 2025-07-30 14:02:03,335] Trial 131 pruned. \n", "[I 2025-07-30 14:02:03,545] Trial 132 pruned. \n", "[I 2025-07-30 14:02:03,743] Trial 133 pruned. \n", "[I 2025-07-30 14:02:22,348] Trial 134 finished with value: -10.057591199874878 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.21787822727603565, 'learning_rate': 0.009968997159893358, 'weight_decay': 1.0142774293354362e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:02:22,500] Trial 135 pruned. \n", "[I 2025-07-30 14:02:22,659] Trial 136 pruned. \n", "[I 2025-07-30 14:02:22,856] Trial 137 pruned. \n", "[I 2025-07-30 14:02:23,020] Trial 138 pruned. \n", "[I 2025-07-30 14:02:23,184] Trial 139 pruned. \n", "[I 2025-07-30 14:02:23,342] Trial 140 pruned. \n", "[I 2025-07-30 14:02:24,411] Trial 141 pruned. \n", "[I 2025-07-30 14:02:24,749] Trial 142 pruned. \n", "[I 2025-07-30 14:02:42,330] Trial 143 finished with value: -10.005010604858398 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.2585736898700819, 'learning_rate': 0.009235237356317502, 'weight_decay': 9.467988668063516e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:02:42,486] Trial 144 pruned. \n", "[I 2025-07-30 14:02:53,601] Trial 145 finished with value: -9.591697931289673 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.2090563019751738, 'learning_rate': 0.00987624303021934, 'weight_decay': 0.000750736236830117}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:02:53,754] Trial 146 pruned. \n", "[I 2025-07-30 14:02:53,951] Trial 147 pruned. \n", "[I 2025-07-30 14:02:54,121] Trial 148 pruned. \n", "[I 2025-07-30 14:02:54,326] Trial 149 pruned. \n", "[I 2025-07-30 14:02:54,532] Trial 150 pruned. \n", "[I 2025-07-30 14:03:06,689] Trial 151 finished with value: -9.863729238510132 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.24367416898835287, 'learning_rate': 0.009218014008276551, 'weight_decay': 7.692895465137185e-05}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:03:06,866] Trial 152 pruned. \n", "[I 2025-07-30 14:03:07,037] Trial 153 pruned. \n", "[I 2025-07-30 14:03:07,209] Trial 154 pruned. \n", "[I 2025-07-30 14:03:10,388] Trial 155 pruned. \n", "[I 2025-07-30 14:03:10,563] Trial 156 pruned. \n", "[I 2025-07-30 14:03:11,481] Trial 157 pruned. \n", "[I 2025-07-30 14:03:11,654] Trial 158 pruned. \n", "[I 2025-07-30 14:03:11,967] Trial 159 pruned. \n", "[I 2025-07-30 14:03:12,149] Trial 160 pruned. \n", "[I 2025-07-30 14:03:12,317] Trial 161 pruned. \n", "[I 2025-07-30 14:03:12,480] Trial 162 pruned. \n", "[I 2025-07-30 14:03:12,688] Trial 163 pruned. \n", "[I 2025-07-30 14:03:18,689] Trial 164 pruned. \n", "[I 2025-07-30 14:03:19,016] Trial 165 pruned. \n", "[I 2025-07-30 14:03:19,366] Trial 166 pruned. \n", "[I 2025-07-30 14:03:19,506] Trial 167 pruned. \n", "[I 2025-07-30 14:03:19,661] Trial 168 pruned. \n", "[I 2025-07-30 14:03:20,663] Trial 169 pruned. \n", "[I 2025-07-30 14:03:21,143] Trial 170 pruned. \n", "[I 2025-07-30 14:03:23,664] Trial 171 pruned. \n", "[I 2025-07-30 14:03:25,674] Trial 172 pruned. \n", "[I 2025-07-30 14:03:25,970] Trial 173 pruned. \n", "[I 2025-07-30 14:03:26,166] Trial 174 pruned. \n", "[I 2025-07-30 14:03:26,347] Trial 175 pruned. \n", "[I 2025-07-30 14:03:26,511] Trial 176 pruned. \n", "[I 2025-07-30 14:03:26,694] Trial 177 pruned. \n", "[I 2025-07-30 14:03:26,860] Trial 178 pruned. \n", "[I 2025-07-30 14:03:27,037] Trial 179 pruned. \n", "[I 2025-07-30 14:03:27,240] Trial 180 pruned. \n", "[I 2025-07-30 14:03:28,246] Trial 181 pruned. \n", "[I 2025-07-30 14:03:38,672] Trial 182 pruned. \n", "[I 2025-07-30 14:03:39,300] Trial 183 pruned. \n", "[I 2025-07-30 14:03:39,617] Trial 184 pruned. \n", "[I 2025-07-30 14:03:39,804] Trial 185 pruned. \n", "[I 2025-07-30 14:03:39,977] Trial 186 pruned. \n", "[I 2025-07-30 14:03:40,165] Trial 187 pruned. \n", "[I 2025-07-30 14:03:40,370] Trial 188 pruned. \n", "[I 2025-07-30 14:03:40,536] Trial 189 pruned. \n", "[I 2025-07-30 14:03:40,730] Trial 190 pruned. \n", "[I 2025-07-30 14:03:47,955] Trial 191 pruned. \n", "[I 2025-07-30 14:03:48,154] Trial 192 pruned. \n", "[I 2025-07-30 14:03:48,328] Trial 193 pruned. \n", "[I 2025-07-30 14:03:59,926] Trial 194 finished with value: -9.65259599685669 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.21721245412862658, 'learning_rate': 0.00998882770705386, 'weight_decay': 0.00028666156814171305}. Best is trial 66 with value: -10.166960716247559.\n", "[I 2025-07-30 14:04:00,127] Trial 195 pruned. \n", "[I 2025-07-30 14:04:00,307] Trial 196 pruned. \n", "[I 2025-07-30 14:04:00,652] Trial 197 pruned. \n", "[I 2025-07-30 14:04:00,858] Trial 198 pruned. \n", "[I 2025-07-30 14:04:15,389] Trial 199 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-30T06:04:15.390784Z [info     ] Fold 1 best trial: value=-10.1670, params={'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 16, 'dropout_rate': 0.23228664220914955, 'learning_rate': 0.007332273636674879, 'weight_decay': 0.00015025702839655604} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1613.87, 'cpu_percent': 0.0}\n", "2025-07-30T06:04:15.396704Z [info     ] --- Performing blind test for Fold 1 on well 'C-1' --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1613.87, 'cpu_percent': 0.0}\n", "2025-07-30T06:04:34.862434Z [info     ] --- Starting CV Fold 2/2 ---   [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1615.48, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-30 14:04:34,898] A new study created in memory with name: no-name-a001ef41-e429-43b4-b4ab-d4a580eaf3ef\n", "[I 2025-07-30 14:04:50,583] Trial 0 finished with value: -2.514164129892985 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units_1': 64, 'mlp_units_2': 8, 'dropout_rate': 0.5028945033886965, 'learning_rate': 0.0007827245712295317, 'weight_decay': 7.833500260625396e-05}. Best is trial 0 with value: -2.514164129892985.\n", "[I 2025-07-30 14:05:06,707] Trial 1 finished with value: -4.6238077481587725 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 16, 'mlp_units_2': 8, 'dropout_rate': 0.33966235502337727, 'learning_rate': 0.0015413350297065025, 'weight_decay': 0.00026420504051392733}. Best is trial 1 with value: -4.6238077481587725.\n", "[I 2025-07-30 14:05:23,280] Trial 2 finished with value: -0.37314630548159283 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units_1': 16, 'mlp_units_2': 32, 'dropout_rate': 0.31082270184921645, 'learning_rate': 0.00011907770139467673, 'weight_decay': 0.00033779161770569404}. Best is trial 1 with value: -4.6238077481587725.\n", "[I 2025-07-30 14:05:39,483] Trial 3 finished with value: -9.11586888631185 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 16, 'mlp_units_2': 32, 'dropout_rate': 0.35206926427932594, 'learning_rate': 0.004417808876063746, 'weight_decay': 0.0003333281522189799}. Best is trial 3 with value: -9.11586888631185.\n", "[I 2025-07-30 14:05:54,789] Trial 4 finished with value: -1.511494755744934 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.5334471631761895, 'learning_rate': 0.0004477682019094783, 'weight_decay': 1.1799480857825056e-05}. Best is trial 3 with value: -9.11586888631185.\n", "[I 2025-07-30 14:06:06,924] Trial 5 finished with value: -9.181781133015951 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 8, 'dropout_rate': 0.32294609850458267, 'learning_rate': 0.009830129400920444, 'weight_decay': 0.00031360185421025716}. Best is trial 5 with value: -9.181781133015951.\n", "[I 2025-07-30 14:06:21,761] Trial 6 finished with value: -7.296759128570557 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 8, 'dropout_rate': 0.22456992159673816, 'learning_rate': 0.002921723321879054, 'weight_decay': 1.715135306245087e-05}. Best is trial 5 with value: -9.181781133015951.\n", "[I 2025-07-30 14:06:21,853] Trial 7 pruned. \n", "[I 2025-07-30 14:06:21,976] Trial 8 pruned. \n", "[I 2025-07-30 14:06:36,788] Trial 9 finished with value: -5.615155061086019 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 16, 'mlp_units_2': 8, 'dropout_rate': 0.5267324522757009, 'learning_rate': 0.0023038333227559547, 'weight_decay': 1.5951187099914795e-05}. Best is trial 5 with value: -9.181781133015951.\n", "[I 2025-07-30 14:06:47,220] Trial 10 finished with value: -9.462326685587565 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.20572164583874977, 'learning_rate': 0.009726127795676812, 'weight_decay': 0.0008703832343281112}. Best is trial 10 with value: -9.462326685587565.\n", "[I 2025-07-30 14:07:02,598] Trial 11 finished with value: -10.208691596984863 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.20246944246391413, 'learning_rate': 0.009946662429694905, 'weight_decay': 0.0008943898731147392}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:07:17,893] Trial 12 finished with value: -10.059106826782227 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.21965570187790276, 'learning_rate': 0.009242244825125761, 'weight_decay': 0.0009559100322576959}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:07:33,629] Trial 13 finished with value: -9.254072189331055 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.25469127350885085, 'learning_rate': 0.004628811025626882, 'weight_decay': 0.0009597020339340273}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:07:44,349] Trial 14 finished with value: -9.492643038431803 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.262297616112424, 'learning_rate': 0.008200948867621155, 'weight_decay': 5.865895121386455e-05}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:07:50,627] Trial 15 pruned. \n", "[I 2025-07-30 14:07:50,804] Trial 16 pruned. \n", "[I 2025-07-30 14:08:08,125] Trial 17 finished with value: -9.853293736775717 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.20418411154692356, 'learning_rate': 0.0057467745949961666, 'weight_decay': 0.0005715354168931116}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:08:08,283] Trial 18 pruned. \n", "[I 2025-07-30 14:08:08,437] Trial 19 pruned. \n", "[I 2025-07-30 14:08:24,148] Trial 20 finished with value: -9.628183046976725 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.2812404332498697, 'learning_rate': 0.006378543374457024, 'weight_decay': 0.0001499366136730933}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:08:39,011] Trial 21 finished with value: -10.112883885701498 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.20959803396670845, 'learning_rate': 0.00614834607035618, 'weight_decay': 0.0005712580802835869}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:08:39,165] Trial 22 pruned. \n", "[I 2025-07-30 14:08:52,284] Trial 23 finished with value: -9.215770721435547 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.2884575923924609, 'learning_rate': 0.00696550117629437, 'weight_decay': 0.00043860025288110693}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:09:07,771] Trial 24 finished with value: -10.047094345092773 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.20202431402692522, 'learning_rate': 0.0071653467107137625, 'weight_decay': 0.0008592024192610909}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:09:07,911] Trial 25 pruned. \n", "[I 2025-07-30 14:09:23,596] Trial 26 finished with value: -10.01866340637207 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.23134002605463272, 'learning_rate': 0.009886217619826197, 'weight_decay': 0.00022234123532256472}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:09:23,757] Trial 27 pruned. \n", "[I 2025-07-30 14:09:24,027] Trial 28 pruned. \n", "[I 2025-07-30 14:09:24,172] Trial 29 pruned. \n", "[I 2025-07-30 14:09:24,343] Trial 30 pruned. \n", "[I 2025-07-30 14:09:40,414] Trial 31 finished with value: -9.882371584574381 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.20275177374366088, 'learning_rate': 0.007187739329222561, 'weight_decay': 0.0009377293592454367}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:09:40,639] Trial 32 pruned. \n", "[I 2025-07-30 14:09:55,760] Trial 33 finished with value: -10.129664103190104 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 32, 'mlp_units_2': 16, 'dropout_rate': 0.2029446116021391, 'learning_rate': 0.00815169547449489, 'weight_decay': 0.0009349142445635752}. Best is trial 11 with value: -10.208691596984863.\n", "[I 2025-07-30 14:09:55,898] Trial 34 pruned. \n", "[I 2025-07-30 14:10:12,825] Trial 35 finished with value: -10.26297664642334 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.22794374247440297, 'learning_rate': 0.008170676340277907, 'weight_decay': 0.0002571509619598771}. Best is trial 35 with value: -10.26297664642334.\n", "[I 2025-07-30 14:10:12,992] Trial 36 pruned. \n", "[I 2025-07-30 14:10:13,149] Trial 37 pruned. \n", "[I 2025-07-30 14:10:13,314] Trial 38 pruned. \n", "[I 2025-07-30 14:10:28,017] Trial 39 finished with value: -9.546998977661133 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.22513534427490312, 'learning_rate': 0.007864705514758754, 'weight_decay': 0.0002858835729054192}. Best is trial 35 with value: -10.26297664642334.\n", "[I 2025-07-30 14:10:28,177] Trial 40 pruned. \n", "[I 2025-07-30 14:10:46,559] Trial 41 finished with value: -10.283796946207682 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.22068831680123407, 'learning_rate': 0.00965703986635454, 'weight_decay': 0.000772759776583257}. Best is trial 41 with value: -10.283796946207682.\n", "[I 2025-07-30 14:11:01,349] Trial 42 finished with value: -9.978967348734537 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21992331514381322, 'learning_rate': 0.009983155700468356, 'weight_decay': 0.0006603958722394635}. Best is trial 41 with value: -10.283796946207682.\n", "[I 2025-07-30 14:11:17,778] Trial 43 finished with value: -10.289791742960611 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2485229498233518, 'learning_rate': 0.0078076930367754, 'weight_decay': 0.0007333286577057493}. Best is trial 43 with value: -10.289791742960611.\n", "[I 2025-07-30 14:11:34,710] Trial 44 finished with value: -10.051831563313803 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.24520986748412227, 'learning_rate': 0.008216872470535604, 'weight_decay': 0.0007778999086806272}. Best is trial 43 with value: -10.289791742960611.\n", "[I 2025-07-30 14:11:34,895] Trial 45 pruned. \n", "[I 2025-07-30 14:11:51,078] Trial 46 finished with value: -10.345141092936197 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2330332682126531, 'learning_rate': 0.008138480867174388, 'weight_decay': 0.0008216111324319233}. Best is trial 46 with value: -10.345141092936197.\n", "[I 2025-07-30 14:11:51,207] Trial 47 pruned. \n", "[I 2025-07-30 14:12:03,625] Trial 48 finished with value: -9.533581733703613 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.3227505078958287, 'learning_rate': 0.008307673671989957, 'weight_decay': 0.00016452831102988665}. Best is trial 46 with value: -10.345141092936197.\n", "[I 2025-07-30 14:12:03,752] Trial 49 pruned. \n", "[I 2025-07-30 14:12:03,881] Trial 50 pruned. \n", "[I 2025-07-30 14:12:20,012] Trial 51 finished with value: -10.417779286702475 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.23279378804067086, 'learning_rate': 0.008015013173405678, 'weight_decay': 0.0007751277981057726}. Best is trial 51 with value: -10.417779286702475.\n", "[I 2025-07-30 14:12:20,167] Trial 52 pruned. \n", "[I 2025-07-30 14:12:36,105] Trial 53 finished with value: -10.321826616923014 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2585704113741239, 'learning_rate': 0.009182583241706012, 'weight_decay': 0.000612766705407983}. Best is trial 51 with value: -10.417779286702475.\n", "[I 2025-07-30 14:12:36,257] Trial 54 pruned. \n", "[I 2025-07-30 14:12:52,892] Trial 55 finished with value: -10.551994323730469 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.23040798490635395, 'learning_rate': 0.008819625641269922, 'weight_decay': 0.000497732523302981}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:12:53,024] Trial 56 pruned. \n", "[I 2025-07-30 14:12:53,161] Trial 57 pruned. \n", "[I 2025-07-30 14:13:03,935] Trial 58 pruned. \n", "[I 2025-07-30 14:13:04,089] Trial 59 pruned. \n", "[I 2025-07-30 14:13:04,256] Trial 60 pruned. \n", "[I 2025-07-30 14:13:04,424] Trial 61 pruned. \n", "[I 2025-07-30 14:13:17,891] Trial 62 pruned. \n", "[I 2025-07-30 14:13:34,632] Trial 63 finished with value: -10.31852658589681 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.23689442250982853, 'learning_rate': 0.009839032926431044, 'weight_decay': 0.00026275243171071275}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:13:34,766] Trial 64 pruned. \n", "[I 2025-07-30 14:13:44,261] Trial 65 pruned. \n", "[I 2025-07-30 14:13:44,447] Trial 66 pruned. \n", "[I 2025-07-30 14:13:44,578] Trial 67 pruned. \n", "[I 2025-07-30 14:13:44,729] Trial 68 pruned. \n", "[I 2025-07-30 14:13:44,881] Trial 69 pruned. \n", "[I 2025-07-30 14:13:53,674] Trial 70 pruned. \n", "[I 2025-07-30 14:13:53,916] Trial 71 pruned. \n", "[I 2025-07-30 14:13:54,074] Trial 72 pruned. \n", "[I 2025-07-30 14:14:10,387] Trial 73 finished with value: -10.525096893310547 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2094407537747163, 'learning_rate': 0.009060649345811963, 'weight_decay': 1.0109948327228864e-05}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:14:10,547] Trial 74 pruned. \n", "[I 2025-07-30 14:14:24,646] Trial 75 finished with value: -10.226194063822428 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.23843935613287456, 'learning_rate': 0.009067849853256602, 'weight_decay': 2.3301562337733093e-05}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:14:24,846] Trial 76 pruned. \n", "[I 2025-07-30 14:14:37,611] Trial 77 pruned. \n", "[I 2025-07-30 14:14:37,869] Trial 78 pruned. \n", "[I 2025-07-30 14:14:38,011] Trial 79 pruned. \n", "[I 2025-07-30 14:14:38,144] Trial 80 pruned. \n", "[I 2025-07-30 14:14:38,397] Trial 81 pruned. \n", "[I 2025-07-30 14:14:51,877] Trial 82 finished with value: -9.595592816670736 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.22412512875919371, 'learning_rate': 0.008850627006665841, 'weight_decay': 0.0004228322141121332}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:15:04,643] Trial 83 finished with value: -9.81735610961914 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.20073624592382372, 'learning_rate': 0.008797674831723024, 'weight_decay': 0.00013407176073150238}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:15:04,795] Trial 84 pruned. \n", "[I 2025-07-30 14:15:04,928] Trial 85 pruned. \n", "[I 2025-07-30 14:15:05,088] Trial 86 pruned. \n", "[I 2025-07-30 14:15:05,244] Trial 87 pruned. \n", "[I 2025-07-30 14:15:12,236] Trial 88 pruned. \n", "[I 2025-07-30 14:15:28,750] Trial 89 finished with value: -10.100289344787598 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2919999474682325, 'learning_rate': 0.008678789255022483, 'weight_decay': 0.00030634890116115467}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:15:28,884] Trial 90 pruned. \n", "[I 2025-07-30 14:15:42,475] Trial 91 finished with value: -9.608993848164877 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2359354401958376, 'learning_rate': 0.008667853316042844, 'weight_decay': 2.9388138304588028e-05}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:15:42,608] Trial 92 pruned. \n", "[I 2025-07-30 14:15:59,725] Trial 93 finished with value: -10.469317436218262 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.20723073640854345, 'learning_rate': 0.008155534589692794, 'weight_decay': 2.1853331398885665e-05}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:15:59,869] Trial 94 pruned. \n", "[I 2025-07-30 14:16:00,015] Trial 95 pruned. \n", "[I 2025-07-30 14:16:00,172] Trial 96 pruned. \n", "[I 2025-07-30 14:16:00,325] Trial 97 pruned. \n", "[I 2025-07-30 14:16:00,465] Trial 98 pruned. \n", "[I 2025-07-30 14:16:04,390] Trial 99 pruned. \n", "[I 2025-07-30 14:16:04,563] Trial 100 pruned. \n", "[I 2025-07-30 14:16:04,745] Trial 101 pruned. \n", "[I 2025-07-30 14:16:21,262] Trial 102 finished with value: -10.379383087158203 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.23197710778300765, 'learning_rate': 0.00913989854013779, 'weight_decay': 0.0006735687935210973}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:16:21,424] Trial 103 pruned. \n", "[I 2025-07-30 14:16:21,564] Trial 104 pruned. \n", "[I 2025-07-30 14:16:33,975] Trial 105 finished with value: -9.731837272644043 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.20964281608314622, 'learning_rate': 0.009124750222537855, 'weight_decay': 0.000740636178921771}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:16:34,142] Trial 106 pruned. \n", "[I 2025-07-30 14:16:48,505] Trial 107 finished with value: -9.781979242960611 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.25125089497379355, 'learning_rate': 0.00942416314993625, 'weight_decay': 0.0004804046963661942}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:17:05,687] Trial 108 finished with value: -10.224908828735352 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2329623036956749, 'learning_rate': 0.008420104796430274, 'weight_decay': 0.0005456399061425732}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:17:05,856] Trial 109 pruned. \n", "[I 2025-07-30 14:17:06,040] Trial 110 pruned. \n", "[I 2025-07-30 14:17:20,993] Trial 111 finished with value: -10.395934422810873 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21909819958949928, 'learning_rate': 0.009161763150529564, 'weight_decay': 6.632536919133659e-05}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:17:21,148] Trial 112 pruned. \n", "[I 2025-07-30 14:17:31,573] Trial 113 finished with value: -9.3770751953125 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21305624241350685, 'learning_rate': 0.009930800620885446, 'weight_decay': 0.00012160977530383076}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:17:31,717] Trial 114 pruned. \n", "[I 2025-07-30 14:17:48,478] Trial 115 finished with value: -10.527757326761881 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2083370645727251, 'learning_rate': 0.009038898349204492, 'weight_decay': 0.0008105120505475324}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:18:04,243] Trial 116 finished with value: -10.271207809448242 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.20887473412937946, 'learning_rate': 0.009370531591469234, 'weight_decay': 0.0008367635577424107}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:18:04,421] Trial 117 pruned. \n", "[I 2025-07-30 14:18:08,366] Trial 118 pruned. \n", "[I 2025-07-30 14:18:08,529] Trial 119 pruned. \n", "[I 2025-07-30 14:18:23,006] Trial 120 pruned. \n", "[I 2025-07-30 14:18:39,813] Trial 121 finished with value: -10.528666496276855 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21349674491434548, 'learning_rate': 0.009272843959031931, 'weight_decay': 0.0008075348059987213}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:18:55,252] Trial 122 finished with value: -10.440448443094889 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.22588452776526502, 'learning_rate': 0.009990325169403793, 'weight_decay': 0.0007864179558885095}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:18:55,507] Trial 123 pruned. \n", "[I 2025-07-30 14:19:10,695] Trial 124 finished with value: -10.437060356140137 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21526567512858974, 'learning_rate': 0.009231392529238299, 'weight_decay': 0.0007902245732329174}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:19:27,346] Trial 125 finished with value: -10.495750745137533 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21347256769139475, 'learning_rate': 0.009088955215013744, 'weight_decay': 0.0008189020797002155}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:19:44,187] Trial 126 finished with value: -10.543375015258789 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21390342063983048, 'learning_rate': 0.00905326503763141, 'weight_decay': 0.0007873833070715081}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:19:44,366] Trial 127 pruned. \n", "[I 2025-07-30 14:19:55,092] Trial 128 finished with value: -9.597903887430826 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.22363477662415093, 'learning_rate': 0.00998925096035919, 'weight_decay': 0.0009749310096281206}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:19:55,249] Trial 129 pruned. \n", "[I 2025-07-30 14:19:55,394] Trial 130 pruned. \n", "[I 2025-07-30 14:19:59,331] Trial 131 pruned. \n", "[I 2025-07-30 14:19:59,505] Trial 132 pruned. \n", "[I 2025-07-30 14:20:18,244] Trial 133 finished with value: -10.383530934651693 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21508655425697348, 'learning_rate': 0.009103154294316499, 'weight_decay': 0.0006440737001944602}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:20:35,916] Trial 134 finished with value: -10.308075586954752 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21300960153316867, 'learning_rate': 0.0091828435293853, 'weight_decay': 0.0006698048893912925}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:20:36,135] Trial 135 pruned. \n", "[I 2025-07-30 14:20:36,289] Trial 136 pruned. \n", "[I 2025-07-30 14:20:36,539] Trial 137 pruned. \n", "[I 2025-07-30 14:20:36,676] Trial 138 pruned. \n", "[I 2025-07-30 14:20:52,908] Trial 139 finished with value: -10.24694856007894 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.24265554981093113, 'learning_rate': 0.00911789403190286, 'weight_decay': 0.0009595184431303547}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:20:53,069] Trial 140 pruned. \n", "[I 2025-07-30 14:21:09,068] Trial 141 pruned. \n", "[I 2025-07-30 14:21:09,345] Trial 142 pruned. \n", "[I 2025-07-30 14:21:09,478] Trial 143 pruned. \n", "[I 2025-07-30 14:21:11,078] Trial 144 pruned. \n", "[I 2025-07-30 14:21:11,239] Trial 145 pruned. \n", "[I 2025-07-30 14:21:11,450] Trial 146 pruned. \n", "[I 2025-07-30 14:21:14,693] Trial 147 pruned. \n", "[I 2025-07-30 14:21:30,318] Trial 148 finished with value: -10.40918223063151 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.2091199834512584, 'learning_rate': 0.009976737357323715, 'weight_decay': 0.0007424450724395561}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:21:30,457] Trial 149 pruned. \n", "[I 2025-07-30 14:21:30,611] Trial 150 pruned. \n", "[I 2025-07-30 14:21:41,570] Trial 151 finished with value: -9.543874740600586 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21968186323743974, 'learning_rate': 0.00980777331288525, 'weight_decay': 0.0006668795611224162}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:21:41,826] Trial 152 pruned. \n", "[I 2025-07-30 14:21:58,549] Trial 153 finished with value: -10.345753987630209 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.20823834062594096, 'learning_rate': 0.009910693937484531, 'weight_decay': 0.0007162259765321222}. Best is trial 55 with value: -10.551994323730469.\n", "[I 2025-07-30 14:21:58,697] Trial 154 pruned. \n", "[I 2025-07-30 14:22:11,829] Trial 155 pruned. \n", "[I 2025-07-30 14:22:12,025] Trial 156 pruned. \n", "[I 2025-07-30 14:22:12,181] Trial 157 pruned. \n", "[I 2025-07-30 14:22:12,525] Trial 158 pruned. \n", "[I 2025-07-30 14:22:25,358] Trial 159 pruned. \n", "[I 2025-07-30 14:22:25,520] Trial 160 pruned. \n", "[I 2025-07-30 14:22:25,688] Trial 161 pruned. \n", "[I 2025-07-30 14:22:28,618] Trial 162 pruned. \n", "[I 2025-07-30 14:22:28,815] Trial 163 pruned. \n", "[I 2025-07-30 14:22:28,995] Trial 164 pruned. \n", "[I 2025-07-30 14:22:45,538] Trial 165 finished with value: -10.584539731343588 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21928172284277292, 'learning_rate': 0.00996965728769989, 'weight_decay': 0.0006422019994295115}. Best is trial 165 with value: -10.584539731343588.\n", "[I 2025-07-30 14:22:45,690] Trial 166 pruned. \n", "[I 2025-07-30 14:22:56,002] Trial 167 pruned. \n", "[I 2025-07-30 14:22:56,173] Trial 168 pruned. \n", "[I 2025-07-30 14:23:06,974] Trial 169 pruned. \n", "[I 2025-07-30 14:23:07,133] Trial 170 pruned. \n", "[I 2025-07-30 14:23:07,281] Trial 171 pruned. \n", "[I 2025-07-30 14:23:17,995] Trial 172 finished with value: -9.550816535949707 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.23849211630202835, 'learning_rate': 0.009974146535994445, 'weight_decay': 0.0006124512870627697}. Best is trial 165 with value: -10.584539731343588.\n", "[I 2025-07-30 14:23:18,344] Trial 173 pruned. \n", "[I 2025-07-30 14:23:18,508] Trial 174 pruned. \n", "[I 2025-07-30 14:23:18,734] Trial 175 pruned. \n", "[I 2025-07-30 14:23:19,383] Trial 176 pruned. \n", "[I 2025-07-30 14:23:19,535] Trial 177 pruned. \n", "[I 2025-07-30 14:23:23,760] Trial 178 pruned. \n", "[I 2025-07-30 14:23:23,924] Trial 179 pruned. \n", "[I 2025-07-30 14:23:30,360] Trial 180 pruned. \n", "[I 2025-07-30 14:23:32,068] Trial 181 pruned. \n", "[I 2025-07-30 14:23:32,224] Trial 182 pruned. \n", "[I 2025-07-30 14:23:42,117] Trial 183 pruned. \n", "[I 2025-07-30 14:23:42,287] Trial 184 pruned. \n", "[I 2025-07-30 14:23:42,447] Trial 185 pruned. \n", "[I 2025-07-30 14:23:42,576] Trial 186 pruned. \n", "[I 2025-07-30 14:23:46,139] Trial 187 pruned. \n", "[I 2025-07-30 14:23:46,324] Trial 188 pruned. \n", "[I 2025-07-30 14:23:46,522] Trial 189 pruned. \n", "[I 2025-07-30 14:23:46,656] Trial 190 pruned. \n", "[I 2025-07-30 14:24:02,047] Trial 191 finished with value: -10.433380444844564 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21436085197233318, 'learning_rate': 0.009979198001491526, 'weight_decay': 0.0006346810324461821}. Best is trial 165 with value: -10.584539731343588.\n", "[I 2025-07-30 14:24:17,282] Trial 192 finished with value: -10.393300692240397 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21728360819856213, 'learning_rate': 0.009991504594799715, 'weight_decay': 0.0006327445602701325}. Best is trial 165 with value: -10.584539731343588.\n", "[I 2025-07-30 14:24:18,744] Trial 193 pruned. \n", "[I 2025-07-30 14:24:29,888] Trial 194 pruned. \n", "[I 2025-07-30 14:24:30,028] Trial 195 pruned. \n", "[I 2025-07-30 14:24:31,520] Trial 196 pruned. \n", "[I 2025-07-30 14:24:31,700] Trial 197 pruned. \n", "[I 2025-07-30 14:24:31,869] Trial 198 pruned. \n", "[I 2025-07-30 14:24:32,011] Trial 199 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-30T06:24:32.013487Z [info     ] Fold 2 best trial: value=-10.5845, params={'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units_1': 64, 'mlp_units_2': 32, 'dropout_rate': 0.21928172284277292, 'learning_rate': 0.00996965728769989, 'weight_decay': 0.0006422019994295115} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:32.022101Z [info     ] --- Performing blind test for Fold 2 on well 'T-1' --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.06, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.212795Z [info     ] Aggregating results from all CV folds... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 444.46, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.239563Z [info     ] 成功从交叉验证流程中收集到泛化能力评估数据。         [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.84, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.250882Z [info     ] Best hyperparameters found: {'cnn_filters': 64.0, 'cnn_kernel_size': 3.0, 'dropout_rate': 0.21928172284277292, 'learning_rate': 0.00996965728769989, 'mlp_units_1': 64.0, 'mlp_units_2': 32.0, 'weight_decay': 0.0006422019994295115} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.84, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.270740Z [info     ] --- Stage 2 Artifacts: Saving CV and Tuning Reports --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 447.85, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.284925Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.cv_performance artifact_path=obmiq_training_pytorch\\cv_performance_report.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 449.34, 'cpu_percent': 0.0} description=LOWO-CV中每一折的最佳验证损失和对应的超参数。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:24:48.313179Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.hyperparameter_tuning artifact_path=obmiq_training_pytorch\\hyperparameter_tuning_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 449.45, 'cpu_percent': 0.0} description=在所有CV折中聚合得到的全局最佳超参数组合。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:24:48.336228Z [info     ] --- Stage 2 Artifacts: Generating Generalization Evaluation Artifacts --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 449.62, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.363836Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.lowo_cv_performance_summary artifact_path=obmiq_training_pytorch\\lowo_cv_performance_summary.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 449.99, 'cpu_percent': 0.0} description=LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:24:48.392867Z [info     ] 成功生成并保存了泛化能力性能评估表: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\lowo_cv_performance_summary.csv [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 450.0, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.641971Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.lowo_cv_predictions artifact_path=obmiq_training_pytorch\\lowo_cv_predictions.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.87, 'cpu_percent': 0.0} description=LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:24:48.673473Z [info     ] 成功保存了泛化能力评估的数据快照: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\lowo_cv_predictions.csv [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.87, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:48.763942Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 463.91, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T06:24:48.805613Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.33, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:24:49.634686Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.86, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.svg']\n", "2025-07-30T06:24:49.677182Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 490.26, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dt2 P50 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:24:49.778213Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.03, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T06:24:49.827885Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.15, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:24:50.634554Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.64, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.svg']\n", "2025-07-30T06:24:50.681722Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.04, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dphit Nmr operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:24:50.705974Z [info     ] 成功生成并保存了泛化能力交叉图。               [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:50.723491Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.11, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\tensorboard_logs' operation=register_artifact\n", "2025-07-30T06:24:50.744874Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.logs.tensorboard artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.11, 'cpu_percent': 0.0} description=用于TensorBoard可视化的日志文件目录。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:24:50.768161Z [info     ] --- Stage 3: Final Model Training --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.11, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:50.782681Z [info     ] Final model training started on device: cuda [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.79, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:51.290300Z [info     ] Epoch 1/300, Train Loss: -0.1541, Val Loss: -0.4646 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 525.92, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:51.564299Z [info     ] Epoch 2/300, Train Loss: -0.7092, Val Loss: -1.0015 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.02, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:51.824534Z [info     ] Epoch 3/300, Train Loss: -1.2316, Val Loss: -1.5163 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.03, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:52.096372Z [info     ] Epoch 4/300, Train Loss: -1.7435, Val Loss: -2.0191 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:52.431904Z [info     ] Epoch 5/300, Train Loss: -2.2303, Val Loss: -2.4869 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:52.711394Z [info     ] Epoch 6/300, Train Loss: -2.6948, Val Loss: -2.9686 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:53.024691Z [info     ] Epoch 7/300, Train Loss: -3.1644, Val Loss: -3.4461 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:53.277845Z [info     ] Epoch 8/300, Train Loss: -3.5748, Val Loss: -3.8463 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:53.539737Z [info     ] Epoch 9/300, Train Loss: -3.9641, Val Loss: -4.1794 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:53.783742Z [info     ] Epoch 10/300, Train Loss: -4.3290, Val Loss: -4.5304 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:54.070191Z [info     ] Epoch 11/300, Train Loss: -4.6877, Val Loss: -4.8915 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:54.385927Z [info     ] Epoch 12/300, Train Loss: -4.9842, Val Loss: -5.0444 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:54.675098Z [info     ] Epoch 13/300, Train Loss: -5.3036, Val Loss: -5.6250 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:55.026861Z [info     ] Epoch 14/300, Train Loss: -5.5828, Val Loss: -5.9097 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:55.284558Z [info     ] Epoch 15/300, Train Loss: -5.9314, Val Loss: -6.2476 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:55.538786Z [info     ] Epoch 16/300, Train Loss: -6.2337, Val Loss: -6.4975 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:55.804638Z [info     ] Epoch 17/300, Train Loss: -6.5039, Val Loss: -6.7905 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:56.136900Z [info     ] Epoch 18/300, Train Loss: -6.7494, Val Loss: -6.9619 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:56.487162Z [info     ] Epoch 19/300, Train Loss: -7.0234, Val Loss: -7.2634 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:56.811643Z [info     ] Epoch 20/300, Train Loss: -7.2158, Val Loss: -7.5427 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:57.092834Z [info     ] Epoch 21/300, Train Loss: -7.4523, Val Loss: -7.7321 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:57.363015Z [info     ] Epoch 22/300, Train Loss: -7.6935, Val Loss: -7.9558 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:57.619964Z [info     ] Epoch 23/300, Train Loss: -7.9719, Val Loss: -8.1884 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:57.885407Z [info     ] Epoch 24/300, Train Loss: -8.1261, Val Loss: -8.3386 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:58.190420Z [info     ] Epoch 25/300, Train Loss: -8.2384, Val Loss: -8.4921 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:58.511794Z [info     ] Epoch 26/300, Train Loss: -8.4063, Val Loss: -8.6880 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:58.800882Z [info     ] Epoch 27/300, Train Loss: -8.6129, Val Loss: -8.8721 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:59.092615Z [info     ] Epoch 28/300, Train Loss: -8.6612, Val Loss: -8.9344 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:59.363762Z [info     ] Epoch 29/300, Train Loss: -8.8548, Val Loss: -9.1467 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:59.659411Z [info     ] Epoch 30/300, Train Loss: -8.8366, Val Loss: -9.1470 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:24:59.933466Z [info     ] Epoch 31/300, Train Loss: -8.8860, Val Loss: -9.2126 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:00.248773Z [info     ] Epoch 32/300, Train Loss: -8.8502, Val Loss: -9.2803 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:00.523053Z [info     ] Epoch 33/300, Train Loss: -8.9895, Val Loss: -9.2499 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:00.830017Z [info     ] Epoch 34/300, Train Loss: -9.0770, Val Loss: -9.3019 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:01.169475Z [info     ] Epoch 35/300, Train Loss: -9.0496, Val Loss: -9.4013 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.04, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:01.518448Z [info     ] Epoch 36/300, Train Loss: -9.0976, Val Loss: -9.4042 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:01.793052Z [info     ] Epoch 37/300, Train Loss: -9.1098, Val Loss: 2.5730 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:02.038611Z [info     ] Epoch 38/300, Train Loss: -9.1155, Val Loss: -9.4184 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:02.303956Z [info     ] Epoch 39/300, Train Loss: -9.0265, Val Loss: -9.3104 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:02.562842Z [info     ] Epoch 40/300, Train Loss: -9.1061, Val Loss: -9.3674 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:02.816680Z [info     ] Epoch 41/300, Train Loss: -9.1092, Val Loss: -9.2762 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:03.131086Z [info     ] Epoch 42/300, Train Loss: -9.1559, Val Loss: -9.4641 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:03.442701Z [info     ] Epoch 43/300, Train Loss: -9.1226, Val Loss: -9.4373 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:03.733663Z [info     ] Epoch 44/300, Train Loss: -9.1323, Val Loss: -9.3682 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:04.073681Z [info     ] Epoch 45/300, Train Loss: -9.1580, Val Loss: -9.3619 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:04.430544Z [info     ] Epoch 46/300, Train Loss: -9.2106, Val Loss: -9.4225 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:04.729042Z [info     ] Epoch 47/300, Train Loss: -9.2321, Val Loss: -9.6035 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:05.032196Z [info     ] Epoch 48/300, Train Loss: -9.2150, Val Loss: -9.5437 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:05.299328Z [info     ] Epoch 49/300, Train Loss: -9.1850, Val Loss: -9.5765 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:05.577595Z [info     ] Epoch 50/300, Train Loss: -9.1213, Val Loss: -9.6134 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:05.847283Z [info     ] Epoch 51/300, Train Loss: -9.2815, Val Loss: -9.5407 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:06.152156Z [info     ] Epoch 52/300, Train Loss: -9.2873, Val Loss: -9.6879 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:06.438289Z [info     ] Epoch 53/300, Train Loss: -9.3525, Val Loss: -9.7465 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:06.740390Z [info     ] Epoch 54/300, Train Loss: -9.2913, Val Loss: -9.7710 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:07.115346Z [info     ] Epoch 55/300, Train Loss: -9.3751, Val Loss: -9.3723 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:07.418230Z [info     ] Epoch 56/300, Train Loss: -9.3629, Val Loss: -9.6683 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:07.677172Z [info     ] Epoch 57/300, Train Loss: -9.3275, Val Loss: -9.7591 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:07.953588Z [info     ] Epoch 58/300, Train Loss: -9.3220, Val Loss: -9.2926 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:08.232283Z [info     ] Epoch 59/300, Train Loss: -9.1340, Val Loss: -9.6206 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:08.489434Z [info     ] Epoch 60/300, Train Loss: -9.3609, Val Loss: -9.8378 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:08.783733Z [info     ] Epoch 61/300, Train Loss: -9.4529, Val Loss: -9.8180 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:09.037383Z [info     ] Epoch 62/300, Train Loss: -9.4783, Val Loss: -9.9293 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:09.290447Z [info     ] Epoch 63/300, Train Loss: -9.4629, Val Loss: -9.7041 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:09.571942Z [info     ] Epoch 64/300, Train Loss: -9.4441, Val Loss: -9.8682 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:09.876456Z [info     ] Epoch 65/300, Train Loss: -9.4623, Val Loss: -9.9217 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:10.170513Z [info     ] Epoch 66/300, Train Loss: -9.4411, Val Loss: -9.3679 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:10.474043Z [info     ] Epoch 67/300, Train Loss: -9.3349, Val Loss: -9.6934 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:10.761313Z [info     ] Epoch 68/300, Train Loss: -9.4903, Val Loss: -9.7944 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:11.034810Z [info     ] Epoch 69/300, Train Loss: -9.4637, Val Loss: -9.9286 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:11.326106Z [info     ] Epoch 70/300, Train Loss: -9.5411, Val Loss: -9.8774 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:11.598263Z [info     ] Epoch 71/300, Train Loss: -9.4268, Val Loss: -9.9259 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:11.847950Z [info     ] Epoch 72/300, Train Loss: -9.5015, Val Loss: -9.6457 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:12.131334Z [info     ] Epoch 73/300, Train Loss: -9.4179, Val Loss: -9.9901 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:12.397796Z [info     ] Epoch 74/300, Train Loss: -9.4754, Val Loss: -9.9762 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:12.704602Z [info     ] Epoch 75/300, Train Loss: -9.4565, Val Loss: -9.9292 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:12.995518Z [info     ] Epoch 76/300, Train Loss: -9.5029, Val Loss: -9.9050 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:13.252769Z [info     ] Epoch 77/300, Train Loss: -9.5353, Val Loss: -9.9035 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:13.543275Z [info     ] Epoch 78/300, Train Loss: -9.4518, Val Loss: -9.9818 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:13.910518Z [info     ] Epoch 79/300, Train Loss: -9.5934, Val Loss: -9.8400 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:14.249373Z [info     ] Epoch 80/300, Train Loss: -9.5155, Val Loss: -9.9654 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:14.552826Z [info     ] Epoch 81/300, Train Loss: -9.3042, Val Loss: -9.7449 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:14.832681Z [info     ] Epoch 82/300, Train Loss: -9.3980, Val Loss: -9.8409 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:15.089606Z [info     ] Epoch 83/300, Train Loss: -9.5402, Val Loss: -9.6203 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:15.384550Z [info     ] Epoch 84/300, Train Loss: -9.5476, Val Loss: -9.8457 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:15.649973Z [info     ] Epoch 85/300, Train Loss: -9.5561, Val Loss: -9.4074 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:15.943345Z [info     ] Epoch 86/300, Train Loss: -9.5604, Val Loss: -9.9467 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:16.242857Z [info     ] Epoch 87/300, Train Loss: -9.6090, Val Loss: -9.9994 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:16.539345Z [info     ] Epoch 88/300, Train Loss: -9.5124, Val Loss: -9.9595 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:16.841145Z [info     ] Epoch 89/300, Train Loss: -9.5030, Val Loss: -9.8628 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:17.163025Z [info     ] Epoch 90/300, Train Loss: -9.5852, Val Loss: -9.8498 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:17.470465Z [info     ] Epoch 91/300, Train Loss: -9.6394, Val Loss: -9.9859 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:17.741452Z [info     ] Epoch 92/300, Train Loss: -9.5949, Val Loss: -10.0884 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:17.997357Z [info     ] Epoch 93/300, Train Loss: -9.5942, Val Loss: -10.0568 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:18.270658Z [info     ] Epoch 94/300, Train Loss: -9.6411, Val Loss: -9.8775 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:18.577234Z [info     ] Epoch 95/300, Train Loss: -9.4146, Val Loss: -9.9160 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:18.863358Z [info     ] Epoch 96/300, Train Loss: -9.5753, Val Loss: -9.8253 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:19.094876Z [info     ] Epoch 97/300, Train Loss: -9.5936, Val Loss: -9.9645 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:19.428311Z [info     ] Epoch 98/300, Train Loss: -9.6090, Val Loss: -10.0587 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:19.713939Z [info     ] Epoch 99/300, Train Loss: -9.5933, Val Loss: -9.9827 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:19.964167Z [info     ] Epoch 100/300, Train Loss: -9.5883, Val Loss: -9.9598 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:20.251542Z [info     ] Epoch 101/300, Train Loss: -9.5910, Val Loss: -9.9887 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:20.543577Z [info     ] Epoch 102/300, Train Loss: -9.6318, Val Loss: -9.9530 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:20.813668Z [info     ] Epoch 103/300, Train Loss: -9.4762, Val Loss: -9.7601 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:21.130917Z [info     ] Epoch 104/300, Train Loss: -9.5784, Val Loss: -10.0006 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:21.457643Z [info     ] Epoch 105/300, Train Loss: -9.6640, Val Loss: -9.8852 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:21.727584Z [info     ] Epoch 106/300, Train Loss: -9.4986, Val Loss: -9.8583 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:21.998138Z [info     ] Epoch 107/300, Train Loss: -9.6447, Val Loss: -10.0244 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:22.268495Z [info     ] Epoch 108/300, Train Loss: -9.5518, Val Loss: -9.9869 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:22.586511Z [info     ] Epoch 109/300, Train Loss: -9.6523, Val Loss: -9.9211 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:22.884926Z [info     ] Epoch 110/300, Train Loss: -9.5149, Val Loss: -10.0415 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.170618Z [info     ] Epoch 111/300, Train Loss: -9.6258, Val Loss: -9.9623 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.458278Z [info     ] Epoch 112/300, Train Loss: -9.6240, Val Loss: -9.8418 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.474933Z [info     ] Early stopping triggered at epoch 112. [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.05, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.503156Z [info     ] Loaded best model state with validation loss: -10.0884 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.63, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.512594Z [info     ] Performing final evaluation on the full training dataset... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.63, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.654021Z [info     ] Final model training completed. [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 528.63, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.670356Z [info     ] --- Stage 3 Artifacts: Saving Final Model Assets --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 528.59, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.700331Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.assets_pytorch artifact_path=obmiq_training_pytorch\\model_assets_pytorch.pkl context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 529.23, 'cpu_percent': 0.0} description=包含模型权重、超参数和预处理器的PyTorch模型资产包。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:23.746221Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 529.25, 'cpu_percent': 0.0} description=最终模型训练过程中的损失变化历史。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:23.772978Z [info     ] Plotting final training history... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 529.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:23.810945Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 529.38, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.training_history\n", "2025-07-30T06:25:23.833916Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=final_training_history base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 529.44, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:24.610193Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.77, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\final_training_history.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\final_training_history.svg']\n", "2025-07-30T06:25:24.639864Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.77, 'cpu_percent': 0.0} description=最终模型训练的损失曲线图。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:24.664027Z [info     ] Saving final model evaluation data and generating plots... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.78, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:24.947803Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_model_evaluation artifact_path=obmiq_training_pytorch\\final_model_evaluation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.79, 'cpu_percent': 0.0} description=最终模型在全部训练数据上的预测和残差结果。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:24.981649Z [info     ] Generating evaluation plots... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.79, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:25.047648Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 558.15, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T06:25:25.072158Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 558.21, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:25.919193Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 581.35, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.svg']\n", "2025-07-30T06:25:25.955228Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_crossplot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.72, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:26.046476Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 581.73, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T06:25:26.075435Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 581.82, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:26.970828Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 605.2, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.svg']\n", "2025-07-30T06:25:27.005288Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_crossplot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.57, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:27.094780Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.01, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T06:25:27.142972Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.07, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:28.095552Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.49, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.svg']\n", "2025-07-30T06:25:28.136530Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 629.86, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:28.214035Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.2, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T06:25:28.235097Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.23, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:29.088123Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 657.6, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.svg']\n", "2025-07-30T06:25:29.124973Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 655.96, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:29.229024Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.05, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T06:25:29.348789Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 660.9, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:30.235920Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 686.36, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.svg']\n", "2025-07-30T06:25:30.280093Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 684.73, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:30.370910Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 686.82, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T06:25:30.538327Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 687.03, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:31.331655Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 712.19, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.svg']\n", "2025-07-30T06:25:31.376440Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.56, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:31.397893Z [info     ] --- Stage 3: Model Interpretability Analysis (Captum) --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.56, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:31.422498Z [info     ] Running Captum analysis for target: DT2_P50... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.63, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:37.233391Z [info     ] Running Captum analysis for target: DPHIT_NMR... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1025.75, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:41.753133Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1010.11, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-30T06:25:41.766218Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1010.11, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-30T06:25:41.788870Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1010.11, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:41.915338Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1010.15, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50) operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:41.978045Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1010.95, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-30T06:25:42.013686Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1010.97, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:43.212681Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1035.91, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.svg']\n", "2025-07-30T06:25:43.243868Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1035.92, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-30T06:25:43.263732Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1035.92, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-30T06:25:43.293630Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1035.92, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:43.399191Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1035.96, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR) operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:25:43.455913Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1035.99, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-30T06:25:43.495230Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1036.07, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:44.433742Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.21, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.svg']\n", "2025-07-30T06:25:44.474108Z [warning  ] 井名列 'WELL_NO' 不是字符串类型，将进行区分大小写的精确匹配。这可能导致因大小写或空格问题而找不到井。 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.23, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.492619Z [info     ] 找到样本: 井='c-1', 目标深度=6311.73, 实际深度=6335.12 (索引=0) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.506875Z [info     ] 找到样本: 井='c-1', 目标深度=6313.38, 实际深度=6335.12 (索引=0) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.520308Z [info     ] 找到样本: 井='c-1', 目标深度=6318.8, 实际深度=6335.12 (索引=0) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.533868Z [info     ] 找到样本: 井='c-1', 目标深度=6334.55, 实际深度=6335.12 (索引=0) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.547465Z [info     ] 找到样本: 井='c-1', 目标深度=6409.94, 实际深度=6409.94 (索引=483) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.560181Z [info     ] 找到样本: 井='c-1', 目标深度=6426.71, 实际深度=6426.71 (索引=593) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.573490Z [info     ] 找到样本: 井='c-1', 目标深度=6440.34, 实际深度=6440.27 (索引=682) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.586353Z [info     ] 找到样本: 井='t-1', 目标深度=6426.51, 实际深度=6426.56 (索引=920) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.597906Z [info     ] 找到样本: 井='t-1', 目标深度=6471.0, 实际深度=6471.06 (索引=1152) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.609518Z [info     ] 找到样本: 井='t-1', 目标深度=6552.36, 实际深度=6552.29 (索引=1648) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.25, 'cpu_percent': 0.0}\n", "2025-07-30T06:25:44.636474Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.3, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:25:44.720224Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1061.55, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:50.923313Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1112.48, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.svg']\n", "2025-07-30T06:25:50.983111Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1112.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:25:51.072403Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1112.76, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:25:56.923097Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1163.81, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.svg']\n", "2025-07-30T06:25:56.982536Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1164.1, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:25:57.065365Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1164.32, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:03.088719Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1215.89, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.svg']\n", "2025-07-30T06:26:03.132693Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1215.9, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:03.217476Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1216.38, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:09.752455Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1171.19, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.svg']\n", "2025-07-30T06:26:09.804735Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1171.45, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:09.904840Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1171.71, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:16.659687Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1221.52, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.svg']\n", "2025-07-30T06:26:16.712605Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1221.53, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:16.796618Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1221.71, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:23.315597Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1272.1, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.svg']\n", "2025-07-30T06:26:23.362695Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1272.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:23.459976Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1272.68, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:29.984220Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1323.71, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DT2_P50.svg']\n", "2025-07-30T06:26:30.038605Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1323.99, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:30.150802Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6335.12_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1324.25, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:36.379614Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1374.56, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6335.12_target_DPHIT_NMR.svg']\n", "2025-07-30T06:26:36.427875Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1374.64, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:36.522615Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1374.96, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:42.656782Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1306.37, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.svg']\n", "2025-07-30T06:26:42.727867Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1306.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:42.815870Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1306.39, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:48.525823Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1355.96, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.svg']\n", "2025-07-30T06:26:48.579406Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1355.96, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:48.667968Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1356.15, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:26:54.301291Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1406.68, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.svg']\n", "2025-07-30T06:26:54.355818Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1406.72, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:26:54.450352Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1407.25, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:00.063448Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1457.51, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.svg']\n", "2025-07-30T06:27:00.104448Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1457.56, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:00.190139Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1457.86, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:05.918026Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1508.4, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.svg']\n", "2025-07-30T06:27:05.993078Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1508.45, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:06.103682Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1508.85, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:11.746486Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1561.28, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.svg']\n", "2025-07-30T06:27:11.791409Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1561.34, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:12.285380Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1441.53, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:17.985531Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1491.52, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.svg']\n", "2025-07-30T06:27:18.035030Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1491.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:18.130122Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1491.8, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:23.859688Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1541.89, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.svg']\n", "2025-07-30T06:27:23.903821Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1542.03, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:23.997465Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1542.21, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:29.792024Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1593.46, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.svg']\n", "2025-07-30T06:27:29.862159Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1593.5, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:29.950531Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1594.36, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:35.756716Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1646.09, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.svg']\n", "2025-07-30T06:27:35.808924Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1646.14, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:35.894595Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1646.9, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:41.585440Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1699.52, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.svg']\n", "2025-07-30T06:27:41.651137Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1699.57, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T06:27:41.746583Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1700.57, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:47.655063Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1751.86, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.svg']\n", "2025-07-30T06:27:47.667982Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1751.86, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_sequence_attributions_dir' operation=register_artifact\n", "2025-07-30T06:27:47.697115Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_sequence_attributions_dir artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1751.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:47.726099Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1751.86, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir' operation=register_artifact\n", "2025-07-30T06:27:47.750707Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_saliency_examples_dir artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1751.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:47.777941Z [info     ] --- Stage 5: Exporting Model to ONNX --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1751.86, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:47.859714Z [info     ] Model successfully exported to ONNX format at: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_training_pytorch\\obmiq_model.onnx [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.71, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:47.873302Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.onnx_model artifact_path=obmiq_training_pytorch\\obmiq_model.onnx context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.71, 'cpu_percent': 0.0} description=可用于跨平台部署的ONNX格式模型。 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:47.901452Z [info     ] ===== OBMIQ Training Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.71, 'cpu_percent': 0.0}\n", "训练步骤完成。最佳超参数: {'cnn_filters': 64.0, 'cnn_kernel_size': 3.0, 'dropout_rate': 0.21928172284277292, 'learning_rate': 0.00996965728769989, 'mlp_units_1': 64.0, 'mlp_units_2': 32.0, 'weight_decay': 0.0006422019994295115}\n", "\n", "--- 开始 OBMIQ 预测步骤 (PyTorch) ---\n", "2025-07-30T06:27:47.936448Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.77, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:47.936977Z [info     ] Validating prediction inputs... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.77, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:47.950278Z [info     ] Loading model assets and reconstructing model... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.77, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:47.986793Z [info     ] Preprocessing prediction data... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.79, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:47.998543Z [info     ] 成功将模型所需的逻辑曲线名解析为预测数据的物理列名。     [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1752.79, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:48.018963Z [info     ] Performing inference on device: cuda... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1759.0, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:48.135626Z [info     ] Formatting predictions and saving artifacts... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1760.36, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:48.601020Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.datasets.predictions artifact_path=obmiq_prediction_pytorch\\predictions.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1769.61, 'cpu_percent': 0.0} description=包含原始输入和模型预测结果的数据集 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:48.644986Z [info     ] Ground truth found in prediction data. Generating evaluation plots... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1769.61, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:49.175082Z [info     ] Updated prediction snapshot with residual columns at: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch\\predictions.csv [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1769.66, 'cpu_percent': 0.0}\n", "2025-07-30T06:27:49.269303Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1774.79, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T06:27:49.305145Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1774.89, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:50.217874Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.54, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.svg']\n", "2025-07-30T06:27:50.254668Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\crossplot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1794.14, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:50.371618Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1798.08, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T06:27:50.394127Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1798.14, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:51.285156Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1820.8, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.svg']\n", "2025-07-30T06:27:51.311596Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\crossplot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1817.4, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:51.415470Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1821.67, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T06:27:51.449925Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1821.81, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:52.838411Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1585.35, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.svg']\n", "2025-07-30T06:27:52.864688Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_plot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1581.95, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:52.978758Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1586.57, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T06:27:53.008062Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1586.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:53.944558Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1611.32, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.svg']\n", "2025-07-30T06:27:53.994000Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_plot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1607.92, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:54.135119Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1611.32, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T06:27:54.256672Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1611.33, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:54.967158Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1636.07, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.svg']\n", "2025-07-30T06:27:55.004987Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_hist_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1632.67, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50 operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:55.124041Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1636.1, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T06:27:55.248686Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1636.11, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T06:27:56.056623Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1660.81, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_135102\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.svg']\n", "2025-07-30T06:27:56.089413Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_hist_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1657.41, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr operation=register_artifact run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:56.119902Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1657.41, 'cpu_percent': 0.0}\n", "预测步骤完成。\n", "\n", "预测结果已保存至: output01\\obmiq_run_pytorch_20250730_135102\\obmiq_prediction_pytorch\\predictions.csv\n", "\n", "预测结果预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL_NO</th>\n", "      <th>MD</th>\n", "      <th>DT2_P50</th>\n", "      <th>DT2_P50_PRED</th>\n", "      <th>DPHIT_NMR</th>\n", "      <th>DPHIT_NMR_PRED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C-1</td>\n", "      <td>6310.4268</td>\n", "      <td>0.104076</td>\n", "      <td>-0.175228</td>\n", "      <td>0.065477</td>\n", "      <td>-0.009569</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C-1</td>\n", "      <td>6310.5792</td>\n", "      <td>-0.148789</td>\n", "      <td>-0.306248</td>\n", "      <td>0.061988</td>\n", "      <td>-0.001890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C-1</td>\n", "      <td>6310.7316</td>\n", "      <td>-0.211292</td>\n", "      <td>-0.233130</td>\n", "      <td>0.052548</td>\n", "      <td>0.007302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C-1</td>\n", "      <td>6310.8840</td>\n", "      <td>-0.245636</td>\n", "      <td>-0.191008</td>\n", "      <td>0.038409</td>\n", "      <td>0.002607</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C-1</td>\n", "      <td>6311.0364</td>\n", "      <td>0.112414</td>\n", "      <td>-0.033128</td>\n", "      <td>0.051449</td>\n", "      <td>0.008310</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  WELL_NO         MD   DT2_P50  DT2_P50_PRED  DPHIT_NMR  DPHIT_NMR_PRED\n", "0     C-1  6310.4268  0.104076     -0.175228   0.065477       -0.009569\n", "1     C-1  6310.5792 -0.148789     -0.306248   0.061988       -0.001890\n", "2     C-1  6310.7316 -0.211292     -0.233130   0.052548        0.007302\n", "3     C-1  6310.8840 -0.245636     -0.191008   0.038409        0.002607\n", "4     C-1  6311.0364  0.112414     -0.033128   0.051449        0.008310"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-30T06:27:56.230411Z [info     ] Run marked as successful       [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1656.5, 'cpu_percent': 0.0} operation=mark_success run_id=20250730-055102-85cdf887\n", "2025-07-30T06:27:56.257075Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1656.53, 'cpu_percent': 0.0} duration_seconds=2213.406 manifest_path=output01\\obmiq_run_pytorch_20250730_135102\\manifest.json operation=finalize run_id=20250730-055102-85cdf887 status=COMPLETED\n"]}], "source": ["if project:\n", "    # 定义输出目录\n", "    output_dir = Path(\"./output01\") # 使用新的输出目录\n", "    output_dir.mkdir(exist_ok=True)\n", "\n", "    run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"obmiq_run_pytorch\")\n", "    # 使用 RunContext 包裹整个工作流\n", "    with RunContext(run_dir=output_dir / run_dir_name, overwrite=True) as ctx:\n", "\n", "        # --- 1. 训练步骤 ---\n", "        print(\"--- 开始 OBMIQ 训练步骤 (PyTorch) ---\")\n", "\n", "        # a. 定义训练配置\n", "        training_config = ObmiqTrainingConfig(\n", "            n_trials=200,\n", "            max_epochs_per_trial=150,\n", "            final_train_epochs=300,\n", "            patience=20,\n", "            batch_size=64\n", "        )\n", "\n", "        # b. 定义facade函数的关键字参数\n", "        training_kwargs = {\n", "            \"sequence_feature\": \"PHI_T2_DIST_CUM\",\n", "            \"normalization_feature\": \"PHIT_NMR\",\n", "            \"tabular_features\": ['DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "                                'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "                                'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "                                'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "                                'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY'],\n", "            \"target_features\": [\"DT2_P50\", \"DPHIT_NMR\"],\n", "            \"grouping_feature\": well_no_name, # 使用新的参数名\n", "            \"t2_time_axis\": t2_time_array,\n", "        }\n", "\n", "        saliency_samples = [(\"c-1\",6311.73),\n", "                            (\"c-1\",6313.38),\n", "                            (\"c-1\",6318.8),\n", "                            (\"c-1\",6334.55),\n", "                            (\"c-1\",6409.94),\n", "                            (\"c-1\",6426.71),\n", "                            (\"c-1\",6440.34),\n", "                            (\"t-1\",6426.51),\n", "                            (\"t-1\",6471.0),\n", "                            (\"t-1\",6552.36)]\n", "\n", "        # c. 执行训练步骤\n", "        # 新版facade直接接收WpDataFrameBundle，无需手动准备X, y\n", "        training_results = run_obmiq_training_step(\n", "            config=training_config,\n", "            ctx=ctx,\n", "            train_bundle=train_bundle,\n", "            depth_feature=depth_name,\n", "            saliency_samples=saliency_samples,\n", "            **training_kwargs\n", "        )\n", "\n", "        print(f\"训练步骤完成。最佳超参数: {training_results.get('best_hyperparameters')}\")\n", "\n", "        # --- 2. 预测步骤 ---\n", "        print(\"\\n--- 开始 OBMIQ 预测步骤 (PyTorch) ---\")\n", "\n", "        # a. 从上下文中获取模型资产的路径\n", "        model_assets_path = ctx.get_artifact_path(ObmiqTrainingArtifacts.MODEL_ASSETS.value)\n", "\n", "        # b. 使用产物处理器加载模型资产\n", "        handler = ObmiqArtifactHandler()\n", "        model_assets = handler.load_model_assets(model_assets_path)\n", "\n", "        # c. 定义预测配置\n", "        prediction_config = ObmiqPredictionConfig()\n", "\n", "        # d. 定义facade函数的关键字参数\n", "        prediction_kwargs = {\n", "            \"source_t2_time_axis\": t2_time_array, # 假设预测数据与训练数据T2轴相同\n", "            \"output_curve_names\": (\"DT2_P50_PRED\", \"DPHIT_NMR_PRED\")\n", "        }\n", "\n", "        # e. 执行预测步骤\n", "        prediction_results = run_obmiq_prediction_step(\n", "            config=prediction_config,\n", "            ctx=ctx,\n", "            model_assets=model_assets,\n", "            prediction_bundle=prediction_bundle,\n", "            **prediction_kwargs\n", "        )\n", "\n", "        print(\"预测步骤完成。\")\n", "\n", "        # --- 3. 结果检查 ---\n", "        # 新版facade返回的是预测结果文件的路径\n", "        prediction_path = Path(prediction_results[\"output_path\"])\n", "        if prediction_path.exists():\n", "            print(f\"\\n预测结果已保存至: {prediction_path}\")\n", "            # 从CSV加载结果进行预览\n", "            predicted_df = pd.read_csv(prediction_path)\n", "            print(\"\\n预测结果预览:\")\n", "            display(predicted_df[[\"WELL_NO\", \"MD\", \"DT2_P50\", \"DT2_P50_PRED\", \"DPHIT_NMR\", \"DPHIT_NMR_PRED\"]].head())\n", "        else:\n", "            print(f\"❌ 预测结果文件未找到: {prediction_path}\")\n", "\n", "else:\n", "    print(\"因数据加载失败，跳过工作流执行。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}