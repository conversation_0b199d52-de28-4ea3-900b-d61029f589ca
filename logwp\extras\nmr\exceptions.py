"""NMR相关异常定义。

定义NMR模块专用的异常类，提供详细的错误信息和上下文。

Architecture
------------
层次/依赖: NMR异常层，继承自logwp.extras.exceptions
设计原则: 分层异常、结构化信息、易于调试
性能特征: 轻量级异常、快速创建、详细上下文
"""

from __future__ import annotations

from typing import Any, Optional, Dict
from logwp.infra.exceptions import ErrorContext
from logwp.extras.exceptions import WpExtrasError


class WpNmrError(WpExtrasError):
    """NMR分析异常基类。

    所有NMR相关异常的基类，提供统一的异常处理接口。

    Architecture
    ------------
    层次/依赖: NMR异常根类，继承自WpExtrasError
    设计原则: 统一异常接口、结构化上下文、易于处理
    性能特征: 轻量级创建、详细诊断信息

    Attributes:
        message: 异常消息
        context: 错误上下文信息
        nmr_operation: NMR操作类型
        nmr_component: NMR组件名称
    """

    def __init__(
        self,
        message: str,
        *,
        context: Optional[ErrorContext] = None,
        nmr_operation: Optional[str] = None,
        nmr_component: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化NMR异常。

        Args:
            message: 异常消息
            context: 错误上下文信息
            nmr_operation: NMR操作类型
            nmr_component: NMR组件名称
            **kwargs: 其他上下文信息
        """
        # 构建增强的上下文信息
        enhanced_context = context or ErrorContext(operation="nmr_operation")

        # 合并额外的上下文信息到additional_info
        additional_info = enhanced_context.additional_info or {}

        if nmr_operation:
            additional_info["nmr_operation"] = nmr_operation

        if nmr_component:
            additional_info["nmr_component"] = nmr_component

        # 添加其他上下文信息
        if kwargs:
            additional_info.update(kwargs)

        # 使用_replace更新NamedTuple
        enhanced_context = enhanced_context._replace(additional_info=additional_info)

        super().__init__(message, context=enhanced_context)

        self.nmr_operation = nmr_operation
        self.nmr_component = nmr_component


class WpNmrDataError(WpNmrError):
    """NMR数据异常。

    当NMR数据格式、类型或内容不符合要求时抛出。

    Architecture
    ------------
    层次/依赖: NMR数据验证异常，专门处理数据问题
    设计原则: 数据验证、类型检查、格式校验
    性能特征: 快速验证、详细错误信息

    Examples:
        >>> raise WpNmrDataError(
        ...     "T2谱数据必须为一维数组",
        ...     data_shape=(64, 2),
        ...     expected_shape="(n,)"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        data_type: Optional[str] = None,
        data_shape: Optional[tuple] = None,
        expected_shape: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化NMR数据异常。

        Args:
            message: 异常消息
            data_type: 数据类型
            data_shape: 数据形状
            expected_shape: 期望的数据形状
            **kwargs: 其他上下文信息
        """
        context_data = {}
        if data_type:
            context_data["data_type"] = data_type
        if data_shape:
            context_data["data_shape"] = data_shape
        if expected_shape:
            context_data["expected_shape"] = expected_shape

        context_data.update(kwargs)

        # 创建ErrorContext，确保operation参数存在
        error_context = ErrorContext(
            operation=context_data.get("operation", "data_validation"),
            additional_info=context_data
        )

        super().__init__(
            message,
            context=error_context,
            nmr_component="data_validation"
        )


class WpNmrComputationError(WpNmrError):
    """NMR计算异常。

    当NMR计算过程中发生数值错误或算法失败时抛出。

    Architecture
    ------------
    层次/依赖: NMR计算异常，专门处理计算问题
    设计原则: 数值稳定、算法可靠、错误恢复
    性能特征: 快速诊断、详细计算状态

    Examples:
        >>> raise WpNmrComputationError(
        ...     "积分计算失败",
        ...     algorithm="trapezoid",
        ...     array_size=1024,
        ...     numerical_error="division_by_zero"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        algorithm: Optional[str] = None,
        array_size: Optional[int] = None,
        numerical_error: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化NMR计算异常。

        Args:
            message: 异常消息
            algorithm: 计算算法
            array_size: 数组大小
            numerical_error: 数值错误类型
            **kwargs: 其他上下文信息
        """
        context_data = {}
        if algorithm:
            context_data["algorithm"] = algorithm
        if array_size:
            context_data["array_size"] = array_size
        if numerical_error:
            context_data["numerical_error"] = numerical_error

        context_data.update(kwargs)

        # 创建ErrorContext，确保operation参数存在
        error_context = ErrorContext(
            operation=context_data.get("operation", "computation"),
            additional_info=context_data
        )

        super().__init__(
            message,
            context=error_context,
            nmr_component="computation"
        )


class WpNmrPhysicsError(WpNmrError):
    """NMR物理约束异常。

    当计算结果违反物理约束条件时抛出。

    Architecture
    ------------
    层次/依赖: NMR物理验证异常，专门处理物理约束
    设计原则: 物理合理、约束检查、结果验证
    性能特征: 快速验证、物理意义明确

    Examples:
        >>> raise WpNmrPhysicsError(
        ...     "孔隙度不能为负数",
        ...     porosity_value=-0.1,
        ...     porosity_type="micro",
        ...     physical_constraint="porosity >= 0"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        porosity_value: Optional[float] = None,
        porosity_type: Optional[str] = None,
        physical_constraint: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化NMR物理约束异常。

        Args:
            message: 异常消息
            porosity_value: 孔隙度值
            porosity_type: 孔隙度类型
            physical_constraint: 物理约束描述
            **kwargs: 其他上下文信息
        """
        context_data = {}
        if porosity_value is not None:
            context_data["porosity_value"] = porosity_value
        if porosity_type:
            context_data["porosity_type"] = porosity_type
        if physical_constraint:
            context_data["physical_constraint"] = physical_constraint

        context_data.update(kwargs)

        # 创建ErrorContext，确保operation参数存在
        error_context = ErrorContext(
            operation=context_data.get("operation", "physics_validation"),
            additional_info=context_data
        )

        super().__init__(
            message,
            context=error_context,
            nmr_component="physics_validation"
        )


class WpNmrParameterError(WpNmrError):
    """NMR参数异常。

    当输入参数不符合要求时抛出。

    Architecture
    ------------
    层次/依赖: NMR参数验证异常，专门处理参数问题
    设计原则: 参数验证、类型检查、范围校验
    性能特征: 快速验证、详细参数信息

    Examples:
        >>> raise WpNmrParameterError(
        ...     "T2截止值顺序错误",
        ...     t2cutoff_short=33.0,
        ...     t2cutoff_long=3.0,
        ...     parameter_constraint="t2cutoff_short < t2cutoff_long"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        parameter_name: Optional[str] = None,
        parameter_value: Optional[Any] = None,
        parameter_constraint: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """初始化NMR参数异常。

        Args:
            message: 异常消息
            parameter_name: 参数名称
            parameter_value: 参数值
            parameter_constraint: 参数约束描述
            **kwargs: 其他上下文信息
        """
        context_data = {}
        if parameter_name:
            context_data["parameter_name"] = parameter_name
        if parameter_value is not None:
            context_data["parameter_value"] = parameter_value
        if parameter_constraint:
            context_data["parameter_constraint"] = parameter_constraint

        context_data.update(kwargs)

        # 创建ErrorContext，确保operation参数存在
        # 只传递ErrorContext接受的参数
        error_context = ErrorContext(
            operation=context_data.get("operation", "parameter_validation"),
            additional_info=context_data
        )

        super().__init__(
            message,
            context=error_context
        )
        self.nmr_component = "parameter_validation"


class WpNmrConfigurationError(WpNmrError):
    """NMR配置异常。

    当NMR模块配置不正确时抛出。

    Architecture
    ------------
    层次/依赖: NMR配置异常，专门处理配置问题
    设计原则: 配置验证、环境检查、依赖校验
    性能特征: 启动时检查、详细配置信息

    Examples:
        >>> raise WpNmrConfigurationError(
        ...     "GPU计算环境不可用",
        ...     gpu_available=False,
        ...     cuda_version=None,
        ...     fallback_mode="cpu"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        config_section: Optional[str] = None,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        **kwargs: Any
    ) -> None:
        """初始化NMR配置异常。

        Args:
            message: 异常消息
            config_section: 配置节
            config_key: 配置键
            config_value: 配置值
            **kwargs: 其他上下文信息
        """
        context_data = {}
        if config_section:
            context_data["config_section"] = config_section
        if config_key:
            context_data["config_key"] = config_key
        if config_value is not None:
            context_data["config_value"] = config_value

        context_data.update(kwargs)

        # 创建ErrorContext，确保operation参数存在
        error_context = ErrorContext(
            operation=context_data.get("operation", "configuration"),
            additional_info=context_data
        )

        super().__init__(
            message,
            context=error_context,
            nmr_component="configuration"
        )
