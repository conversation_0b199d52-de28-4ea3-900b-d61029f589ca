"""测试配置注册表 (registry.py)。

测试PlotProfileRegistry的功能，包括：
- 配置注册和获取
- 两级基础模板继承体系
- 层叠合并逻辑
- 配置文件I/O
- 错误处理
"""

import json
import pytest
from pathlib import Path

from logwp.extras.plotting import PlotProfile, PlotProfileRegistry
from logwp.extras.plotting.exceptions import (
    ProfileNotFoundError,
    ProfileRegistrationError,
    ProfileIOError
)


class TestPlotProfileRegistry:
    """测试PlotProfileRegistry类。"""

    def test_initialization(self, clean_registry):
        """测试注册表初始化。"""
        registry = clean_registry

        assert len(registry.list_profiles()) == 0
        assert len(registry.list_base_profiles()) == 0

    def test_register_profile(self, clean_registry, sample_plot_profile):
        """测试注册配置模板。"""
        registry = clean_registry
        profile = sample_plot_profile

        # 注册配置
        registry.register(profile)

        # 验证注册成功
        profiles = registry.list_profiles()
        assert "test.sample" in profiles
        assert len(profiles) == 1

    def test_register_profile_overwrite(self, clean_registry):
        """测试配置覆盖。"""
        registry = clean_registry

        # 注册第一个配置
        profile1 = PlotProfile(name="test.profile", rc_params={"font.size": 10})
        registry.register(profile1)

        # 尝试注册同名配置（不允许覆盖）
        profile2 = PlotProfile(name="test.profile", rc_params={"font.size": 12})

        with pytest.raises(ProfileRegistrationError) as exc_info:
            registry.register(profile2, overwrite=False)

        assert exc_info.value.profile_name == "test.profile"
        assert exc_info.value.operation == "register"

        # 允许覆盖
        registry.register(profile2, overwrite=True)
        retrieved = registry.get("test.profile")
        assert retrieved.rc_params["font.size"] == 12

    def test_register_base_profile(self, clean_registry, global_base_profile):
        """测试注册基础模板。"""
        registry = clean_registry
        base = global_base_profile

        # 注册基础模板
        registry.register_base(base)

        # 验证注册成功
        base_profiles = registry.list_base_profiles()
        assert "base" in base_profiles
        assert len(base_profiles) == 1

    def test_register_base_invalid_name(self, clean_registry):
        """测试基础模板名称验证。"""
        registry = clean_registry

        # 无效的基础模板名称
        invalid_profile = PlotProfile(name="invalid_base_name")

        with pytest.raises(ProfileRegistrationError) as exc_info:
            registry.register_base(invalid_profile)

        assert "基础模板名称必须为 'base' 或以 '.base' 结尾" in str(exc_info.value)
        assert exc_info.value.profile_name == "invalid_base_name"

    def test_get_profile_not_found(self, clean_registry):
        """测试获取不存在的配置。"""
        registry = clean_registry

        with pytest.raises(ProfileNotFoundError) as exc_info:
            registry.get("nonexistent.profile")

        assert exc_info.value.profile_name == "nonexistent.profile"
        assert exc_info.value.available_profiles == []

    def test_get_profile_simple(self, clean_registry, sample_plot_profile):
        """测试获取简单配置（无继承）。"""
        registry = clean_registry
        profile = sample_plot_profile

        registry.register(profile)
        retrieved = registry.get("test.sample")

        assert retrieved.name == "test.sample"
        assert retrieved.rc_params["font.size"] == 12
        assert retrieved.figure_props["figsize"] == (10, 8)

    def test_get_profile_clone(self, clean_registry, sample_plot_profile):
        """测试配置克隆。"""
        registry = clean_registry
        profile = sample_plot_profile

        registry.register(profile)

        # 默认克隆
        retrieved1 = registry.get("test.sample", clone=True)
        retrieved2 = registry.get("test.sample", clone=True)

        # 应该是不同的对象
        assert retrieved1 is not retrieved2
        assert retrieved1.rc_params is not retrieved2.rc_params

        # 但内容相同
        assert retrieved1.name == retrieved2.name
        assert retrieved1.rc_params == retrieved2.rc_params

        # 不克隆
        retrieved3 = registry.get("test.sample", clone=False)
        retrieved4 = registry.get("test.sample", clone=False)

        # 应该是相同的对象（注意：这里实际上还是会创建新对象，因为有合并过程）
        assert retrieved3.name == retrieved4.name

    def test_two_level_inheritance(self, clean_registry):
        """测试两级继承体系。"""
        registry = clean_registry

        # 1. 注册全局基础模板
        global_base = PlotProfile(
            name="base",
            rc_params={"font.family": "Arial", "font.size": 10},
            figure_props={"dpi": 150}
        )
        registry.register_base(global_base)

        # 2. 注册模块级基础模板
        module_base = PlotProfile(
            name="test_module.base",
            rc_params={"font.family": "serif", "axes.grid": True},
            figure_props={"figsize": (8, 8)}
        )
        registry.register_base(module_base)

        # 3. 注册具体配置
        specific = PlotProfile(
            name="test_module.specific_chart",
            rc_params={"font.size": 14},
            title_props={"label": "Specific Chart"}
        )
        registry.register(specific)

        # 4. 获取合并后的配置
        merged = registry.get("test_module.specific_chart")

        # 验证三级继承链：global base -> module base -> specific
        assert merged.rc_params["font.family"] == "serif"      # 模块base覆盖全局base
        assert merged.rc_params["font.size"] == 14             # specific覆盖全局base
        assert merged.rc_params["axes.grid"] is True           # 模块base新增
        assert merged.figure_props["dpi"] == 150               # 全局base
        assert merged.figure_props["figsize"] == (8, 8)        # 模块base
        assert merged.title_props["label"] == "Specific Chart" # specific新增

    def test_extract_module_base_name(self, clean_registry):
        """测试模块基础模板名称提取。"""
        registry = clean_registry

        # 测试正常情况
        assert registry._extract_module_base_name("plt_analyzer.capture_curve") == "plt_analyzer.base"
        assert registry._extract_module_base_name("swift_pso.tsne_convergence") == "swift_pso.base"

        # 测试边界情况
        assert registry._extract_module_base_name("simple_name") is None
        assert registry._extract_module_base_name("") is None

    def test_load_from_dir_empty(self, clean_registry, temp_dir):
        """测试从空目录加载。"""
        registry = clean_registry

        # 空目录
        registry.load_from_dir(temp_dir)
        assert len(registry.list_profiles()) == 0
        assert len(registry.list_base_profiles()) == 0

        # 不存在的目录
        nonexistent_dir = temp_dir / "nonexistent"
        registry.load_from_dir(nonexistent_dir)  # 应该不报错

    def test_load_from_dir_with_files(self, clean_registry, temp_dir, sample_json_config):
        """测试从包含文件的目录加载。"""
        registry = clean_registry

        # 创建测试JSON文件
        profile_file = temp_dir / "test_profile.json"
        with open(profile_file, 'w', encoding='utf-8') as f:
            json.dump(sample_json_config, f, indent=2)

        # 创建基础模板文件
        base_config = {
            "name": "test_module.base",
            "rc_params": {"font.family": "serif"},
            "figure_props": {"figsize": [8, 8]}
        }
        base_file = temp_dir / "test_module_base.json"
        with open(base_file, 'w', encoding='utf-8') as f:
            json.dump(base_config, f, indent=2)

        # 创建无效JSON文件
        invalid_file = temp_dir / "invalid.json"
        with open(invalid_file, 'w') as f:
            f.write("invalid json")

        # 创建非JSON文件
        text_file = temp_dir / "readme.txt"
        with open(text_file, 'w') as f:
            f.write("This is not a JSON file")

        # 加载目录
        registry.load_from_dir(temp_dir)

        # 验证加载结果
        profiles = registry.list_profiles()
        base_profiles = registry.list_base_profiles()

        assert "json_test.profile" in profiles
        assert "test_module.base" in base_profiles

        # 验证配置内容
        loaded_profile = registry.get("json_test.profile")
        assert loaded_profile.rc_params["font.size"] == 14

    def test_save_to_dir(self, clean_registry, temp_dir):
        """测试保存到目录。"""
        registry = clean_registry

        # 注册一些配置
        base = PlotProfile(name="base", rc_params={"font.size": 10})
        module_base = PlotProfile(name="test.base", figure_props={"figsize": (8, 8)})
        specific = PlotProfile(name="test.chart", title_props={"label": "Test"})

        registry.register_base(base)
        registry.register_base(module_base)
        registry.register(specific)

        # 保存到目录
        registry.save_to_dir(temp_dir)

        # 验证文件存在
        assert (temp_dir / "base.json").exists()
        assert (temp_dir / "test.base.json").exists()
        assert (temp_dir / "test.chart.json").exists()

        # 验证文件内容
        with open(temp_dir / "test.chart.json", 'r', encoding='utf-8') as f:
            data = json.load(f)

        assert data["name"] == "test.chart"
        assert data["title_props"]["label"] == "Test"

    def test_save_to_dir_error(self, clean_registry):
        """测试保存目录错误处理。"""
        registry = clean_registry

        # 注册一个配置
        profile = PlotProfile(name="test", rc_params={"font.size": 10})
        registry.register(profile)

        # 在Windows上使用一个真正无效的路径
        import platform
        if platform.system() == "Windows":
            # 使用保留设备名作为无效路径
            invalid_path = Path("CON:/invalid/path")
        else:
            invalid_path = Path("/proc/invalid/readonly/path")

        with pytest.raises(ProfileIOError) as exc_info:
            registry.save_to_dir(invalid_path)

        assert exc_info.value.operation == "save_dir"

    def test_cascade_merge_complex(self, clean_registry):
        """测试复杂的层叠合并场景。"""
        registry = clean_registry

        # 创建复杂的继承链
        global_base = PlotProfile(
            name="base",
            rc_params={
                "font.family": "Arial",
                "font.size": 10,
                "axes.grid": False
            },
            figure_props={
                "dpi": 150,
                "facecolor": "white"
            },
            artist_props={
                "default": {"color": "black", "linewidth": 1}
            }
        )

        module_base = PlotProfile(
            name="complex.base",
            rc_params={
                "font.family": "serif",  # 覆盖全局
                "axes.grid": True,       # 覆盖全局
                "grid.alpha": 0.5        # 新增
            },
            figure_props={
                "figsize": (10, 8)       # 新增
            },
            artist_props={
                "default": {"linewidth": 2},  # 部分覆盖
                "special": {"marker": "o"}     # 新增
            }
        )

        specific = PlotProfile(
            name="complex.specific",
            rc_params={
                "font.size": 14          # 覆盖全局
            },
            title_props={
                "label": "Complex Chart"  # 新增
            },
            artist_props={
                "default": {"color": "blue"},  # 覆盖全局
                "highlight": {"alpha": 0.8}    # 新增
            }
        )

        # 注册配置
        registry.register_base(global_base)
        registry.register_base(module_base)
        registry.register(specific)

        # 获取合并结果
        merged = registry.get("complex.specific")

        # 验证复杂合并结果
        # rc_params合并
        assert merged.rc_params["font.family"] == "serif"    # 模块覆盖全局
        assert merged.rc_params["font.size"] == 14           # specific覆盖全局
        assert merged.rc_params["axes.grid"] is True         # 模块覆盖全局
        assert merged.rc_params["grid.alpha"] == 0.5         # 模块新增

        # figure_props合并
        assert merged.figure_props["dpi"] == 150             # 全局
        assert merged.figure_props["facecolor"] == "white"   # 全局
        assert merged.figure_props["figsize"] == (10, 8)     # 模块新增

        # artist_props深度合并
        assert merged.artist_props["default"]["color"] == "blue"      # specific覆盖全局
        assert merged.artist_props["default"]["linewidth"] == 2       # 模块覆盖全局
        assert merged.artist_props["special"]["marker"] == "o"        # 模块新增
        assert merged.artist_props["highlight"]["alpha"] == 0.8       # specific新增

        # title_props新增
        assert merged.title_props["label"] == "Complex Chart"
