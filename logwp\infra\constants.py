from __future__ import annotations

from enum import Enum


class WpGpuResponseKeys(str, Enum):
    """GPU计算响应键名。

    Architecture
    ------------
    层次/依赖: GPU计算层专用响应格式
    设计原则: GPU状态监控、性能追踪
    性能特征: 实时GPU指标、内存使用监控
    """

    # GPU设备信息
    DEVICE_ID = "device_id"
    DEVICE_NAME = "device_name"
    COMPUTE_CAPABILITY = "compute_capability"
    CUDA_VERSION = "cuda_version"

    # GPU内存指标
    MEMORY_USED_MB = "memory_used_mb"
    MEMORY_TOTAL_MB = "memory_total_mb"
    MEMORY_UTILIZATION = "memory_utilization"
    MEMORY_PEAK_MB = "memory_peak_mb"

    # GPU计算指标
    COMPUTATION_TIME_SEC = "computation_time_sec"
    GPU_UTILIZATION = "gpu_utilization"
    SPEEDUP_RATIO = "speedup_ratio"
    BATCH_SIZE_USED = "batch_size_used"

    # GPU状态
    IS_AVAILABLE = "is_available"
    FALLBACK_TO_CPU = "fallback_to_cpu"
    ERROR_MESSAGE = "error_message"


class WpGpuDefaults(Enum):
    """GPU计算默认配置。

    Architecture
    ------------
    层次/依赖: GPU计算层专用配置
    设计原则: GPU优先、CPU回退、内存管理
    性能特征: 自动设备检测、内存优化
    """

    # GPU设备配置
    PREFERRED_DEVICE_ID = 0             # 首选GPU设备ID
    MIN_COMPUTE_CAPABILITY = "7.0"      # 最低计算能力要求

    # GPU内存管理
    MEMORY_POOL_FRACTION = 0.8          # GPU内存池使用比例
    BATCH_SIZE_AUTO_ADJUST = True       # 自动调整batch_size
    MIN_BATCH_SIZE = 100                # 最小batch_size
    MAX_BATCH_SIZE = 10000              # 最大batch_size

    # GPU计算配置
    ENABLE_MIXED_PRECISION = True       # 启用混合精度计算
    CUDA_ASYNC_MEMORY = True            # 异步内存分配

    # 回退策略
    AUTO_FALLBACK_TO_CPU = True         # GPU不可用时自动回退CPU
    FALLBACK_THRESHOLD_SEC = 5.0        # GPU初始化超时阈值


class WpComputeBackend(str, Enum):
    """计算后端枚举。

    Architecture
    ------------
    层次/依赖: 计算引擎后端选择
    设计原则: 后端抽象、自动回退、性能优化
    性能特征: GPU/CPU自适应、内存优化
    """

    CPU = "cpu"
    GPU = "gpu"
    AUTO = "auto"  # 自动选择最优后端

    @classmethod
    def available_backends(cls) -> list[str]:
        """返回可用的计算后端。"""
        return [cls.CPU.value, cls.GPU.value, cls.AUTO.value]

