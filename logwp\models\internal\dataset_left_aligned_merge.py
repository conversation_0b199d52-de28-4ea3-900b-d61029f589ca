#!/usr/bin/env python3
"""依左侧数据集深度索引进行数据集合并服务层模块。

提供WpWellProject的依左侧对齐数据集合并功能，以左数据集的深度索引作为基准进行合并。

Architecture
------------
层次/依赖: models/service层，数据集合并业务逻辑
设计原则: Service Layer模式、无状态设计、类型安全
性能特征: 复用现有服务、精确插值、左连接语义

核心特征：
- 以左数据集深度序列为基准
- 保持左数据集原始深度值
- 左连接合并方式
- 与左数据集类型相同的结果

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-1: Exception Groups异常处理
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_DDS_logwp_依左侧进行数据集合并.md》§3 - 核心算法流程
- 《SCAPE_DDS_logwp_数据集合并.md》- 现有合并功能参考
"""

from __future__ import annotations

import copy
import pandas as pd
import numpy as np
from typing import TYPE_CHECKING

from logwp.infra import get_logger
from logwp.infra.depth_utils import standardize_depth_unit, are_depth_units_equivalent
from logwp.models.exceptions import (
    WpDataError, WpDatasetNotFoundError, WpValidationError,
    WpDatasetTypeError
)
from logwp.infra.exceptions import ErrorContext
from logwp.models.utils.interpolation_utils import apply_interpolation_distance_limit
from logwp.models.datasets.continuous import WpContinuousDataset
from logwp.models.datasets.discrete import WpDiscreteDataset
from logwp.models.datasets.interval import WpIntervalDataset

if TYPE_CHECKING:
    from logwp.models.well_project import WpWellProject
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from logwp.models.curve.metadata import CurveMetadata, CurveExpansionMode

logger = get_logger(__name__)


def merge_datasets_left_aligned(
    project: WpWellProject,
    left_dataset: str,
    left_curves: list[str],
    left_query: str | None,
    right_dataset: str,
    right_curves: list[str],
    right_query: str | None,
    *,
    max_interpolation_distance: float,
    interpolation_method: str = "nearest",
    new_dataset_name: str | None = None
) -> WpDepthIndexedDatasetBase:
    """依左侧数据集深度索引进行数据集合并服务层实现。

    以左数据集的深度索引作为基准，将右数据集转换为兼容格式后进行合并。
    最终结果保持左数据集的深度结构和类型，适用于需要保持原始深度值的场景。

    实现流程：
    1. 参数验证和数据提取
    2. 深度单位一致性检查
    3. 确定左数据集深度参数
    4. 处理曲线名冲突
    5. 转换右数据集为兼容格式
    6. 插值右数据集到左数据集深度点
    7. 按深度进行左连接合并
    8. 构造最终数据集

    Args:
        project: WpWellProject实例（提供数据集访问）
        left_dataset: 左数据集名称（必须为continuous或discrete类型）
        left_curves: 左数据集要提取的曲线列表（紧凑格式）
        left_query: 左数据集查询条件（可选，使用extract_curves语法）
        right_dataset: 右数据集名称（无类型限制）
        right_curves: 右数据集要提取的曲线列表（紧凑格式）
        right_query: 右数据集查询条件（可选，使用extract_curves语法）
        max_interpolation_distance: 最大插值距离阈值（必须参数），单位与数据集深度单位一致。
                                   对于任何插值方法，如果目标深度点与最近的源数据点距离超过此阈值，
                                   结果将被填充为NaN，以确保插值的可靠性。
        interpolation_method: 插值方法，默认"nearest"
        new_dataset_name: 新数据集名称，None表示自动生成

    Returns:
        WpDepthIndexedDatabaseBase: 合并后的数据集（与左数据集类型相同）

    Raises:
        WpDatasetNotFoundError: 源数据集不存在
        WpValidationError: 深度单位不一致或左数据集类型不支持
        WpDataError: 数据集状态异常或合并失败

    Note:
        - 服务函数不修改输入的project状态
        - 返回新创建的数据集，不添加到project中
        - 复用现有的成熟服务层组件
        - 严格遵循CDP-1规范：返回的数据集DataFrame必须使用默认整数索引（RangeIndex）

    Examples:
        >>> # 基本合并（保持左数据集深度结构）
        >>> merged_dataset = merge_datasets_left_aligned(
        ...     project=project,
        ...     left_dataset="logs",  # continuous类型
        ...     left_curves=["GR", "DEN", "PHIT"],
        ...     left_query=None,
        ...     right_dataset="core",  # discrete类型
        ...     right_curves=["PERM", "GR"],  # GR会被重命名为GR_1
        ...     right_query=None,
        ...     max_interpolation_distance=0.5
        ... )
        >>> # 结果为continuous类型，深度序列与logs数据集完全相同
    """
    logger.info(
        "开始依左侧数据集合并",
        operation="merge_datasets_left_aligned",
        left_dataset=left_dataset,
        right_dataset=right_dataset,
        left_curves=left_curves,
        right_curves=right_curves,
        max_interpolation_distance=max_interpolation_distance,
        interpolation_method=interpolation_method
    )

    # 1. 验证参数
    _validate_left_aligned_merge_parameters(
        project, left_dataset, right_dataset, interpolation_method
    )

    # 2. 提取左右数据集（复用现有服务）
    from logwp.models.datasets.internal import curve_extraction

    left_extracted = curve_extraction.extract_curves(
        project, left_dataset, f"{left_dataset}_left_temp", left_curves, left_query
    )
    right_extracted = curve_extraction.extract_curves(
        project, right_dataset, f"{right_dataset}_right_temp", right_curves, right_query
    )

    logger.debug(
        "数据集提取完成",
        operation="merge_datasets_left_aligned",
        left_extracted_shape=left_extracted.df.shape,
        right_extracted_shape=right_extracted.df.shape,
        left_type=type(left_extracted).__name__,
        right_type=type(right_extracted).__name__
    )

    # 3. 验证左数据集类型
    _validate_left_dataset_type(left_extracted)

    # 4. 检查深度单位一致性
    _validate_depth_unit_consistency(left_extracted, right_extracted)

    # 5. 确定左数据集的采样间隔
    left_sampling_interval = _determine_left_dataset_sampling_interval(left_extracted)

    logger.info(
        "左数据集深度参数确定",
        operation="merge_datasets_left_aligned",
        left_sampling_interval=left_sampling_interval,
        left_dataset_type=type(left_extracted).__name__
    )

    # 6. 处理曲线名冲突
    right_curves_renamed, rename_mapping = _resolve_curve_name_conflicts(
        [curve.name for curve in left_extracted.curve_metadata.list_curves()],
        [curve.name for curve in right_extracted.curve_metadata.list_curves()],
        left_extracted.curve_metadata,
        right_extracted.curve_metadata
    )

    logger.info(
        "曲线名冲突处理完成",
        operation="merge_datasets_left_aligned",
        rename_mapping=rename_mapping,
        renamed_right_curves=right_curves_renamed
    )

    # 7. 转换右数据集为与左数据集兼容的格式
    right_converted = _convert_right_dataset_to_compatible_format(
        right_extracted, left_extracted, left_sampling_interval,
        interpolation_method, rename_mapping
    )

    # 8. 将右数据集插值到左数据集的深度点
    right_interpolated = _interpolate_right_to_left_depths(
        left_extracted, right_converted, interpolation_method, max_interpolation_distance
    )

    # 9. 按深度进行左连接合并
    merged_df = _merge_dataframes_left_aligned(
        left_extracted.df, left_extracted.curve_metadata,
        right_interpolated.df, right_interpolated.curve_metadata
    )

    # 10. 确保合并后DataFrame符合CDP-1索引规范
    merged_df = _ensure_dataframe_index_compliance(merged_df)

    # 11. 合并曲线元数据
    merged_metadata = _merge_curve_metadata_left_aligned(
        left_extracted.curve_metadata,
        right_interpolated.curve_metadata,
        rename_mapping
    )

    # 12. 构造最终数据集（与左数据集类型相同）
    final_dataset_name = new_dataset_name or f"{left_dataset}_merged_with_{right_dataset}"
    result_dataset = _create_final_dataset_same_type_as_left(
        merged_df, merged_metadata, left_extracted, final_dataset_name
    )

    logger.info(
        "依左侧数据集合并完成",
        operation="merge_datasets_left_aligned",
        result_dataset_name=result_dataset.name,
        result_dataset_type=type(result_dataset).__name__,
        result_shape=result_dataset.df.shape,
        final_columns=list(result_dataset.df.columns)
    )

    return result_dataset



def _apply_dropna_to_curves_left_aligned(
    df: pd.DataFrame,
    curve_columns: list[str],
    dropna_how: str
) -> pd.DataFrame:
    """对指定的曲线列应用dropna操作（左对齐合并专用）。

    Args:
        df: 输入DataFrame
        curve_columns: 需要检查NaN值的曲线列名列表
        dropna_how: dropna方式（"any"或"all"）

    Returns:
        pd.DataFrame: 清理后的DataFrame

    Note:
        - 只对用户指定的曲线列进行NaN检查
        - 井名列和深度列不参与NaN检查
        - 保持DataFrame的默认整数索引
    """
    # 过滤出实际存在的曲线列
    existing_curve_columns = [col for col in curve_columns if col in df.columns]

    if not existing_curve_columns:
        logger.warning(
            "没有找到有效的曲线列，跳过dropna操作",
            operation="apply_dropna_to_curves_left_aligned",
            requested_columns=curve_columns,
            available_columns=list(df.columns)
        )
        return df

    logger.debug(
        "开始对曲线列应用dropna操作（左对齐合并）",
        operation="apply_dropna_to_curves_left_aligned",
        curve_columns=existing_curve_columns,
        dropna_how=dropna_how,
        original_rows=len(df)
    )

    # 对指定的曲线列应用dropna
    cleaned_df = df.dropna(subset=existing_curve_columns, how=dropna_how)

    logger.debug(
        "dropna操作完成（左对齐合并）",
        operation="apply_dropna_to_curves_left_aligned",
        original_rows=len(df),
        cleaned_rows=len(cleaned_df),
        removed_rows=len(df) - len(cleaned_df)
    )

    return cleaned_df



def _validate_left_aligned_merge_parameters(
    project: WpWellProject,
    left_dataset: str,
    right_dataset: str,
    interpolation_method: str
) -> None:
    """验证依左侧合并的参数。

    Args:
        project: WpWellProject实例
        left_dataset: 左数据集名称
        right_dataset: 右数据集名称
        interpolation_method: 插值方法

    Raises:
        WpDatasetNotFoundError: 数据集不存在
        WpValidationError: 参数无效
    """
    # 验证数据集存在性
    if left_dataset not in project.datasets:
        raise WpDatasetNotFoundError(
            f"左数据集 '{left_dataset}' 不存在",
            context=ErrorContext(
                operation="validate_left_aligned_merge_parameters",
                additional_info={
                    "left_dataset": left_dataset,
                    "available_datasets": list(project.datasets.keys())
                }
            )
        )

    if right_dataset not in project.datasets:
        raise WpDatasetNotFoundError(
            f"右数据集 '{right_dataset}' 不存在",
            context=ErrorContext(
                operation="validate_left_aligned_merge_parameters",
                additional_info={
                    "right_dataset": right_dataset,
                    "available_datasets": list(project.datasets.keys())
                }
            )
        )

    # 验证插值方法
    if not isinstance(interpolation_method, str) or not interpolation_method.strip():
        raise WpValidationError(
            "插值方法必须为非空字符串",
            context=ErrorContext(
                operation="validate_left_aligned_merge_parameters",
                additional_info={
                    "interpolation_method": interpolation_method,
                    "requirement": "non-empty string"
                }
            )
        )


def _validate_left_dataset_type(left_dataset: WpDepthIndexedDatasetBase) -> None:
    """验证左数据集类型。

    Args:
        left_dataset: 左数据集

    Raises:
        WpValidationError: 左数据集类型不支持

    Note:
        - 只支持continuous和discrete类型
        - interval类型不支持，因为无法确定单一深度序列
    """
    if isinstance(left_dataset, WpIntervalDataset):
        raise WpValidationError(
            "左数据集不能为interval类型，请使用continuous或discrete类型",
            context=ErrorContext(
                operation="validate_left_dataset_type",
                additional_info={
                    "left_dataset_name": str(left_dataset.name),
                    "left_dataset_type": str(left_dataset.dataset_type),
                    "supported_types": ["continuous", "discrete"]
                }
            )
        )

    if not isinstance(left_dataset, (WpContinuousDataset, WpDiscreteDataset)):
        raise WpValidationError(
            f"不支持的左数据集类型: {type(left_dataset)}",
            context=ErrorContext(
                operation="validate_left_dataset_type",
                additional_info={
                    "left_dataset_name": str(left_dataset.name),
                    "actual_type": str(type(left_dataset)),
                    "supported_types": ["WpContinuousDataset", "WpDiscreteDataset"]
                }
            )
        )





def _validate_depth_unit_consistency(
    left_dataset: WpDepthIndexedDatasetBase,
    right_dataset: WpDepthIndexedDatasetBase
) -> None:
    """验证左右数据集的深度单位一致性。

    Args:
        left_dataset: 左数据集
        right_dataset: 右数据集

    Raises:
        WpValidationError: 深度单位不一致

    Note:
        - 深度单位必须完全一致才能进行合并
        - 如果任一数据集缺少深度单位信息，发出警告但不阻止合并
    """
    # 获取深度曲线信息
    left_depth_curves = left_dataset.curve_metadata.get_depth_reference_curves()
    right_depth_curves = right_dataset.curve_metadata.get_depth_reference_curves()

    if not left_depth_curves or not right_depth_curves:
        logger.warning(
            "无法获取深度曲线信息，跳过深度单位一致性检查",
            operation="_validate_depth_unit_consistency",
            left_depth_curves=left_depth_curves,
            right_depth_curves=right_depth_curves
        )
        return

    # 获取深度曲线的单位信息
    left_depth_curve = left_dataset.curve_metadata.get_curve(left_depth_curves[0])
    right_depth_curve = right_dataset.curve_metadata.get_curve(right_depth_curves[0])

    if not left_depth_curve or not right_depth_curve:
        logger.warning(
            "无法获取深度曲线属性，跳过深度单位一致性检查",
            operation="_validate_depth_unit_consistency"
        )
        return

    left_unit = getattr(left_depth_curve, 'unit', None)
    right_unit = getattr(right_depth_curve, 'unit', None)

    if left_unit is None or right_unit is None:
        logger.warning(
            "深度曲线缺少单位信息，跳过深度单位一致性检查",
            operation="_validate_depth_unit_consistency",
            left_unit=left_unit,
            right_unit=right_unit
        )
        return

    # 检查单位一致性（使用语义等价比较）
    if not are_depth_units_equivalent(left_unit, right_unit):
        raise WpValidationError(
            f"左右数据集深度单位不一致: 左数据集='{left_unit}', 右数据集='{right_unit}'",
            context=ErrorContext(
                operation="_validate_depth_unit_consistency",
                additional_info={
                    "left_dataset_name": str(left_dataset.name),
                    "right_dataset_name": str(right_dataset.name),
                    "left_depth_unit": left_unit,
                    "right_depth_unit": right_unit,
                    "requirement": "深度单位必须语义等价",
                    "standardized_left": standardize_depth_unit(left_unit),
                    "standardized_right": standardize_depth_unit(right_unit)
                }
            )
        )

    logger.debug(
        "深度单位一致性验证通过",
        operation="_validate_depth_unit_consistency",
        left_unit=left_unit,
        right_unit=right_unit,
        standardized_left=standardize_depth_unit(left_unit),
        standardized_right=standardize_depth_unit(right_unit)
    )


def _determine_left_dataset_sampling_interval(left_dataset: WpDepthIndexedDatasetBase) -> float:
    """确定左数据集的采样间隔。

    Args:
        left_dataset: 左数据集

    Returns:
        float: 采样间隔

    Note:
        - continuous类型：使用depth_sampling_rate
        - discrete类型：使用calculate_depth_sampling_rate()
    """
    if isinstance(left_dataset, WpContinuousDataset):
        return left_dataset.depth_sampling_rate
    elif isinstance(left_dataset, WpDiscreteDataset):
        return left_dataset.calculate_depth_sampling_rate()
    else:
        raise WpDatasetTypeError(f"不支持的左数据集类型: {type(left_dataset)}")


def _resolve_curve_name_conflicts(
    left_curves: list[str],
    right_curves: list[str],
    left_metadata: CurveMetadata,
    right_metadata: CurveMetadata
) -> tuple[list[str], dict[str, str]]:
    """处理曲线名冲突。

    Args:
        left_curves: 左数据集曲线名列表
        right_curves: 右数据集曲线名列表
        left_metadata: 左数据集曲线元数据
        right_metadata: 右数据集曲线元数据

    Returns:
        tuple[list[str], dict[str, str]]: (右数据集重命名后曲线列表, 重命名映射)

    Note:
        - 左数据集曲线名保持不变
        - 右数据集重名曲线按GR→GR_1, GR_1→GR_2的规则重命名
        - 井名和深度曲线不参与重命名，通过元数据属性识别
    """
    # 获取左右数据集的系统曲线（井名和深度曲线）
    left_system_curves = _get_system_curve_names(left_metadata)
    right_system_curves = _get_system_curve_names(right_metadata)

    left_curves_set = set(left_curves)
    rename_mapping = {}
    renamed_right_curves = []

    logger.debug(
        "开始处理曲线名冲突",
        operation="_resolve_curve_name_conflicts",
        left_system_curves=left_system_curves,
        right_system_curves=right_system_curves,
        left_curves=left_curves,
        right_curves=right_curves
    )

    for curve_name in right_curves:
        # 系统曲线（井名和深度曲线）不重命名
        if curve_name in right_system_curves:
            renamed_right_curves.append(curve_name)
            logger.debug(
                "系统曲线保持不变",
                operation="_resolve_curve_name_conflicts",
                curve_name=curve_name,
                curve_type="system"
            )
            continue

        # 检查是否与左数据集冲突
        if curve_name in left_curves_set:
            # 生成新名称
            new_name = _generate_unique_curve_name(curve_name, left_curves_set | set(renamed_right_curves))
            rename_mapping[curve_name] = new_name
            renamed_right_curves.append(new_name)
            logger.debug(
                "数据曲线重命名",
                operation="_resolve_curve_name_conflicts",
                original_name=curve_name,
                new_name=new_name,
                curve_type="data"
            )
        else:
            renamed_right_curves.append(curve_name)
            logger.debug(
                "数据曲线无冲突",
                operation="_resolve_curve_name_conflicts",
                curve_name=curve_name,
                curve_type="data"
            )

    return renamed_right_curves, rename_mapping


def _get_system_curve_names(metadata: CurveMetadata) -> set[str]:
    """获取系统曲线名称（井名和深度曲线）。

    Args:
        metadata: 曲线元数据

    Returns:
        set[str]: 系统曲线名称集合

    Note:
        - 通过is_well_identifier和depth_role属性识别系统曲线
        - 不依赖硬编码的曲线名称
    """
    system_curves = set()

    for curve in metadata.list_curves():
        # 井名曲线
        if curve.is_well_identifier:
            system_curves.add(curve.name)
        # 深度曲线
        elif curve.depth_role is not None:
            system_curves.add(curve.name)

    return system_curves


def _generate_unique_curve_name(base_name: str, existing_names: set[str]) -> str:
    """生成唯一的曲线名称。

    Args:
        base_name: 基础名称
        existing_names: 已存在的名称集合

    Returns:
        str: 唯一的曲线名称

    Note:
        - 按GR→GR_1, GR_1→GR_2的规则生成
        - 确保生成的名称不与已存在的名称冲突
    """
    if base_name not in existing_names:
        return base_name

    counter = 1
    while True:
        new_name = f"{base_name}_{counter}"
        if new_name not in existing_names:
            return new_name
        counter += 1


def _convert_right_dataset_to_compatible_format(
    right_dataset: WpDepthIndexedDatasetBase,
    left_dataset: WpDepthIndexedDatasetBase,
    left_sampling_interval: float,
    interpolation_method: str,
    rename_mapping: dict[str, str] | None = None) -> WpDepthIndexedDatasetBase:
    """将右数据集转换为与左数据集兼容的格式。

    转换规则：
    - WpIntervalDataset: 转换为 WpContinuousDataset，以便进行点插值。
    - WpContinuousDataset / WpDiscreteDataset: 不进行重采样，仅应用曲线重命名。
      这是为了保留原始稀疏数据，让 max_interpolation_distance 参数能正确工作。

    Args:
        right_dataset: 右数据集
        left_dataset: 左数据集（用于确定目标格式）
        left_sampling_interval: 左数据集采样间隔
        interpolation_method: 插值方法
        rename_mapping: 曲线重命名映射

    Returns:
        WpDepthIndexedDatasetBase: 转换后的数据集
    """
    logger.info(
        "开始转换右数据集为兼容格式（左对齐合并）",
        operation="_convert_right_dataset_to_compatible_format",
        right_dataset_type=type(right_dataset).__name__
    )

    # 只有 WpIntervalDataset 需要实际的数据格式转换
    if isinstance(right_dataset, WpIntervalDataset):
        from logwp.models.datasets.internal import interval_to_continuous
        logger.debug("将 Interval 数据集转换为 Continuous 以进行点插值")
        result_df, result_metadata, _ = interval_to_continuous.convert_interval_to_continuous(
            right_dataset, left_sampling_interval,
            depth_range=None  # 使用右数据集自己的深度范围
        )

        # 应用曲线重命名映射（如果提供）
        if rename_mapping:
            result_df, result_metadata = _apply_curve_renaming(result_df, result_metadata, rename_mapping)

        # 创建转换后的连续型数据集（所有转换服务都返回连续型格式）
        return WpContinuousDataset.create_with_data(
            name=f"{right_dataset.name}_converted_temp",
            df=result_df,
            curve_metadata=result_metadata,
            depth_sampling_rate=left_sampling_interval
        )

    # 对于 Continuous 和 Discrete 数据集，我们不希望在这里进行重采样或插值。
    # 这样做会破坏 max_interpolation_distance 的逻辑。
    # 我们只需要应用重命名映射。
    else:
        logger.debug("对 Continuous/Discrete 数据集仅应用重命名，不进行重采样")
        # 深拷贝以避免修改原始提取的数据集
        result_dataset = copy.deepcopy(right_dataset)

        if rename_mapping:
            df, metadata = _apply_curve_renaming(
                result_dataset.df, result_dataset.curve_metadata, rename_mapping
            )
            # 直接更新数据集的内部状态
            object.__setattr__(result_dataset, 'df', df)
            object.__setattr__(result_dataset, 'curve_metadata', metadata)

        return result_dataset


def _apply_curve_renaming(
    df: pd.DataFrame,
    metadata: CurveMetadata,
    rename_mapping: dict[str, str]
) -> tuple[pd.DataFrame, CurveMetadata]:
    """应用曲线重命名映射。

    Args:
        df: 源DataFrame
        metadata: 源曲线元数据
        rename_mapping: 重命名映射 {原名称: 新名称}

    Returns:
        tuple[pd.DataFrame, CurveMetadata]: (重命名后的DataFrame, 重命名后的元数据)

    Note:
        - 同时修改DataFrame列名和CurveBasicAttributes的name、dataframe_column_name
        - 保持井名和深度曲线不变
    """
    # 深拷贝DataFrame和元数据
    new_df = df.copy()
    new_metadata = copy.deepcopy(metadata)

    # 应用DataFrame列名重命名
    df_rename_mapping = {}
    for old_name, new_name in rename_mapping.items():
        # 查找对应的DataFrame列名
        curve_attrs = metadata.get_curve(old_name)
        if curve_attrs:
            old_df_column = curve_attrs.dataframe_column_name
            new_df_column = new_name.replace('[', '_').replace(']', '')  # DataFrame友好格式
            df_rename_mapping[old_df_column] = new_df_column

    new_df = new_df.rename(columns=df_rename_mapping)

    # 应用元数据重命名
    for old_name, new_name in rename_mapping.items():
        curve_attrs = new_metadata.get_curve(old_name)
        if curve_attrs:
            # 更新曲线属性
            object.__setattr__(curve_attrs, 'name', new_name)
            object.__setattr__(curve_attrs, 'dataframe_column_name',
                             new_name.replace('[', '_').replace(']', ''))

            # 在元数据中更新映射
            new_metadata.curves[new_name] = curve_attrs
            if old_name != new_name:
                del new_metadata.curves[old_name]

    return new_df, new_metadata


def _interpolate_right_to_left_depths(
    left_dataset: WpDepthIndexedDatasetBase,
    right_dataset: WpDepthIndexedDatasetBase,
    interpolation_method: str,
    max_interpolation_distance: float
) -> WpContinuousDataset:
    """将右数据集插值到左数据集的深度点。

    这是依左侧对齐合并的核心步骤：将右数据集的数据插值到左数据集的具体深度点。

    Args:
        left_dataset: 左数据集（continuous或discrete）
        right_dataset: 右数据集（已转换为continuous）
        interpolation_method: 插值方法
        max_interpolation_distance: 最大插值距离阈值，对所有插值方法有效。
                                   超出此距离的点将被强制填充为NaN。

    Returns:
        WpContinuousDataset: 插值到左数据集深度点的右数据集

    Note:
        - 获取左数据集的所有深度点（包括所有井的深度点）
        - 将右数据集插值到这些精确的深度点
        - 保持左数据集的深度结构完全不变
    """
    from scipy import interpolate

    # 获取左数据集的深度和井名信息
    left_depth_curves = left_dataset.curve_metadata.get_depth_reference_curves()
    left_well_curves = left_dataset.curve_metadata.get_well_identifier_curves()

    if not left_depth_curves or not left_well_curves:
        raise WpDataError("左数据集缺少必要的深度或井名曲线")

    left_depth_col = left_depth_curves[0]
    left_well_col = left_well_curves[0]

    # 获取右数据集的深度和井名信息
    right_depth_curves = right_dataset.curve_metadata.get_depth_reference_curves()
    right_well_curves = right_dataset.curve_metadata.get_well_identifier_curves()

    # 获取右数据集的深度和井名列名
    right_depth_col = right_depth_curves[0]
    right_well_col = right_well_curves[0]
    right_df_processed = right_dataset.df

    # 创建结果DataFrame，以左数据集的深度结构为基准
    result_df = left_dataset.df[[left_well_col, left_depth_col]].copy()

    # 获取右数据集的数据列（排除井名和深度列）
    system_columns = set([right_well_col, right_depth_col])
    right_data_columns = [col for col in right_df_processed.columns
                         if col not in system_columns]

    # 预先创建所有数据列，避免后续逐列赋值导致的碎片化
    # [修复] 智能初始化列的数据类型，避免FutureWarning
    if right_data_columns:
        data_dict = {}
        for col in right_data_columns:
            source_dtype = right_df_processed[col].dtype
            # 如果源是数值类型（包括整数），目标列可以是浮点型以容纳NaN
            if pd.api.types.is_numeric_dtype(source_dtype):
                data_dict[col] = np.full(len(result_df), np.nan, dtype=np.float64)
            else:
                # 否则，目标列是对象类型，以容纳字符串或混合类型
                data_dict[col] = np.full(len(result_df), None, dtype=object)
        data_df = pd.DataFrame(data_dict, index=result_df.index)
        result_df = pd.concat([result_df, data_df], axis=1)

    # 按井进行插值
    for well_name in result_df[left_well_col].unique():
        # 获取当前井的左数据集深度点
        left_well_mask = result_df[left_well_col] == well_name
        left_depths = result_df.loc[left_well_mask, left_depth_col].values

        # 获取当前井的右数据集数据
        right_well_mask = right_df_processed[right_well_col] == well_name
        right_well_data = right_df_processed[right_well_mask].copy()

        if right_well_data.empty:
            # 右数据集中没有该井的数据，批量填充NaN避免逐列赋值
            if right_data_columns:
                result_df.loc[left_well_mask, right_data_columns] = np.nan
            continue

        right_depths = right_well_data[right_depth_col].values

        # 对每个数据列进行插值
        for col in right_data_columns:
            right_values = right_well_data[col].values

            # [修复] 使用 pd.notna 对所有类型进行稳健的NaN检查 (包括Int64Dtype)
            valid_mask = pd.notna(right_values)
            # 对于对象类型，额外检查空字符串
            if valid_mask.any() and right_values.dtype == 'object':
                valid_mask &= (right_values != '')

            if not valid_mask.any():
                result_df.loc[left_well_mask, col] = np.nan if right_values.dtype != 'object' else ''
                continue

            valid_depths = right_depths[valid_mask]
            valid_values = right_values[valid_mask]

            # [修复] 显式转换为NumPy数组，以兼容scipy/numpy插值函数
            if hasattr(valid_values, 'to_numpy'):
                valid_values = valid_values.to_numpy()


            # 根据曲线元数据确定最佳插值方法
            curve_attrs = right_dataset.curve_metadata.get_curve(col)
            if curve_attrs is not None:
                # 使用CurveBasicAttributes的智能插值方法选择
                actual_interpolation_method = curve_attrs.get_recommended_interpolation_method(
                    interpolation_method
                )
            else:
                # 如果找不到曲线属性，使用默认方法
                actual_interpolation_method = interpolation_method

             # 稳健性增强：如果数据点太少，无法进行线性或三次等插值，则回退到最近邻
            if len(valid_depths) < 2 and actual_interpolation_method not in ["nearest"]:
                logger.debug(
                    f"数据点不足 ({len(valid_depths)}) 无法进行 '{actual_interpolation_method}' 插值，"
                    f"自动回退到 'nearest' 方法。",
                    operation="_interpolate_right_to_left_depths",
                    well_name=well_name,
                    column=col
                    )
                actual_interpolation_method = "nearest"

            # 执行插值（参考现有实现）
            try:
                if actual_interpolation_method == "nearest":
                        interpolated_values = _interpolate_nearest(
                            valid_depths, valid_values, left_depths
                        )

                elif actual_interpolation_method == "linear":
                    # 线性插值（仅适用于数值型）
                    interpolated_values = np.interp(left_depths, valid_depths, valid_values)

                elif actual_interpolation_method == "cubic":
                    # 三次插值（仅适用于数值型）
                    f = interpolate.interp1d(
                        valid_depths, valid_values,
                        kind='cubic', bounds_error=False, fill_value='extrapolate'
                    )
                    interpolated_values = f(left_depths)
                else:
                    # 其他方法使用scipy.interpolate
                    f = interpolate.interp1d(
                        valid_depths, valid_values,
                        kind=actual_interpolation_method, bounds_error=False, fill_value='extrapolate'
                    )
                    interpolated_values = f(left_depths)

            except Exception as e:
                logger.warning(
                    f"插值失败，使用最近邻方法",
                    operation="_interpolate_right_to_left_depths",
                    well_name=well_name,
                    column=col,
                    error=str(e)
                )
                # 回退到最近邻（与现有实现保持一致）
                try:
                    # 统一使用 _interpolate_nearest 进行回退
                    interpolated_values = _interpolate_nearest(
                        valid_depths, valid_values, left_depths
                    )
                except Exception:
                    # 最后的备选：填充第一个有效值
                    interpolated_values = np.full(len(left_depths), valid_values[0])

            # [核心改进] 应用最大插值距离限制
            interpolated_values = apply_interpolation_distance_limit(
                interpolated_values, left_depths, valid_depths, max_interpolation_distance
            )
            result_df.loc[left_well_mask, col] = interpolated_values

    # 去碎片化DataFrame，解决性能警告
    result_df = result_df.copy()

    # 创建插值后的数据集
    interpolated_dataset = WpContinuousDataset.create_with_data(
        name=f"{right_dataset.name}_interpolated_temp",
        df=result_df,
        curve_metadata=right_dataset.curve_metadata,
        depth_sampling_rate=left_dataset.depth_sampling_rate if hasattr(left_dataset, 'depth_sampling_rate') else 0.0
    )

    logger.debug(
        "右数据集插值到左数据集深度点完成",
        operation="_interpolate_right_to_left_depths",
        left_depth_points=len(result_df),
        interpolated_columns=len(right_data_columns)
    )

    return interpolated_dataset


def _interpolate_nearest(
    source_depths: np.ndarray,
    source_values: np.ndarray,
    target_depths: np.ndarray
) -> np.ndarray:
    """最近邻插值（适用于所有数据类型），使用NumPy向量化实现以提高性能。

    Args:
        source_depths: 源深度数组
        source_values: 源数据值数组
        target_depths: 目标深度数组

    Returns:
        np.ndarray: 插值后的数组

    Note:
        - 适用于数值和字符串类型数据
        - 使用NumPy广播和argmin高效计算最近邻索引，避免了低效的Python循环
    """
    # 确保源数据不为空
    if len(source_depths) == 0:
        # 根据源数据类型返回一个合适的空数组或填充数组
        if np.issubdtype(source_values.dtype, np.number):
            return np.full(len(target_depths), np.nan)
        else:
            return np.full(len(target_depths), '', dtype=object)

    # 使用NumPy广播和argmin高效计算最近邻索引
    # 1. 计算每个目标深度点到所有源深度点的距离矩阵
    #    target_depths[:, np.newaxis] -> shape (n_targets, 1)
    #    source_depths -> shape (n_sources,)
    #    distances -> shape (n_targets, n_sources)
    distances = np.abs(target_depths[:, np.newaxis] - source_depths)

    # 2. 沿源深度轴找到最小距离的索引
    nearest_indices = np.argmin(distances, axis=1)

    # 3. 使用高级索引从源值中提取结果
    return source_values[nearest_indices]






def _merge_dataframes_left_aligned(
    left_df: pd.DataFrame,
    left_metadata: CurveMetadata,
    right_df: pd.DataFrame,
    right_metadata: CurveMetadata
) -> pd.DataFrame:
    """按深度进行左连接合并。

    Args:
        left_df: 左数据集DataFrame
        left_metadata: 左数据集曲线元数据
        right_df: 右数据集DataFrame（已插值到左数据集深度点）
        right_metadata: 右数据集曲线元数据

    Returns:
        pd.DataFrame: 合并后的DataFrame

    Note:
        - 右数据集已经插值到左数据集的深度点，深度和井名完全对齐
        - 直接按列合并，无需复杂的join操作
        - 合并后重置为默认整数索引
    """
    # 获取右数据集的数据列（排除井名和深度列）
    right_depth_curves = right_metadata.get_depth_reference_curves()
    right_well_curves = right_metadata.get_well_identifier_curves()

    system_columns = set(right_depth_curves + right_well_curves)
    right_data_columns = [col for col in right_df.columns if col not in system_columns]

    logger.info(
        "开始合并已对齐的数据集",
        operation="_merge_dataframes_left_aligned",
        left_rows=len(left_df),
        right_rows=len(right_df),
        right_data_columns=right_data_columns
    )

    # 创建合并后的DataFrame，使用pd.concat避免逐列赋值导致的碎片化
    if right_data_columns:
        # 提取右数据集的数据列
        right_data_subset = right_df[right_data_columns].copy()
        # 确保索引对齐
        right_data_subset.index = left_df.index
        # 使用pd.concat一次性合并所有列
        merged_df = pd.concat([left_df, right_data_subset], axis=1)
    else:
        # 如果没有右数据列，直接复制左数据集
        merged_df = left_df.copy()

    # 重置为默认整数索引（符合CDP-1规范）
    merged_df = merged_df.reset_index(drop=True)

    logger.info(
        "数据集合并完成",
        operation="_merge_dataframes_left_aligned",
        merged_rows=len(merged_df),
        merged_columns=len(merged_df.columns)
    )

    return merged_df


def _ensure_dataframe_index_compliance(df: pd.DataFrame) -> pd.DataFrame:
    """确保DataFrame符合CDP-1索引规范。

    Args:
        df: 输入DataFrame

    Returns:
        pd.DataFrame: 符合索引规范的DataFrame

    Note:
        - 强制重置为默认整数索引（RangeIndex）
        - 如果原索引包含有用信息，会转换为普通列
        - 严格遵循《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》CDP-1核心原则
    """
    if not isinstance(df.index, pd.RangeIndex):
        # 如果索引不是默认整数索引，重置为RangeIndex
        df = df.reset_index(drop=True)

    return df


def _merge_curve_metadata_left_aligned(
    left_metadata: CurveMetadata,
    right_metadata: CurveMetadata,
    rename_mapping: dict[str, str]
) -> CurveMetadata:
    """合并两个数据集的曲线元数据（依左侧对齐方式）。

    Args:
        left_metadata: 左数据集曲线元数据
        right_metadata: 右数据集曲线元数据
        rename_mapping: 右数据集曲线重命名映射

    Returns:
        CurveMetadata: 合并后的曲线元数据

    Note:
        - 深拷贝左数据集元数据作为基础
        - 添加右数据集的曲线（应用重命名）
        - 更新时间戳
    """
    from datetime import datetime

    # 深拷贝左数据集元数据作为基础
    merged_metadata = copy.deepcopy(left_metadata)

    # 获取右数据集的系统列（井名和深度列），这些不应该添加到合并后的元数据中
    right_depth_curves = right_metadata.get_depth_reference_curves()
    right_well_curves = right_metadata.get_well_identifier_curves()
    system_curve_names = set(right_depth_curves + right_well_curves)

    # 添加右数据集的曲线（应用重命名映射，排除系统列）
    for curve in right_metadata.list_curves():
        curve_name = curve.name

        # 跳过系统列（井名和深度列）
        if curve_name in system_curve_names:
            continue

        # 应用重命名
        final_curve_name = rename_mapping.get(curve_name, curve_name)

        if not merged_metadata.has_curve(final_curve_name):
            curve_attrs = right_metadata.get_curve(curve_name)
            if curve_attrs:
                # 创建新的曲线属性（应用重命名）
                new_curve_attrs = copy.deepcopy(curve_attrs)
                object.__setattr__(new_curve_attrs, 'name', final_curve_name)
                object.__setattr__(new_curve_attrs, 'dataframe_column_name',
                                 final_curve_name.replace('[', '_').replace(']', ''))
                merged_metadata.add_curve(new_curve_attrs)

    # 更新时间戳
    merged_metadata.modified_at = datetime.now()

    logger.debug(
        "曲线元数据合并完成",
        operation="_merge_curve_metadata_left_aligned",
        total_curves=len(merged_metadata.list_curves()),
        added_curves=len(rename_mapping)
    )

    return merged_metadata


def _create_final_dataset_same_type_as_left(
    merged_df: pd.DataFrame,
    merged_metadata: CurveMetadata,
    left_dataset: WpDepthIndexedDatasetBase,
    dataset_name: str
) -> WpDepthIndexedDatasetBase:
    """创建与左数据集类型相同的最终数据集。

    Args:
        merged_df: 合并后的DataFrame
        merged_metadata: 合并后的曲线元数据
        left_dataset: 左数据集（用于确定类型）
        dataset_name: 数据集名称

    Returns:
        WpDepthIndexedDatabaseBase: 最终数据集（与左数据集类型相同）

    Note:
        - continuous→continuous：保持depth_sampling_rate
        - discrete→discrete：不需要depth_sampling_rate
    """
    if isinstance(left_dataset, WpContinuousDataset):
        result_dataset = WpContinuousDataset.create_with_data(
            name=dataset_name,
            df=merged_df,
            curve_metadata=merged_metadata,
            depth_sampling_rate=left_dataset.depth_sampling_rate
        )
    elif isinstance(left_dataset, WpDiscreteDataset):
        result_dataset = WpDiscreteDataset.create_with_data(
            name=dataset_name,
            df=merged_df,
            curve_metadata=merged_metadata
        )
    else:
        raise WpDatasetTypeError(f"不支持的左数据集类型: {type(left_dataset)}")

    return result_dataset
