# Windows 下 **Gemini CLI** 环境配置综合指南（私用版 — 带注释）

> 适用：Windows 10/11 —— CMD、PowerShell、Git Bash
> 代理：`192.168.153.200:7890`  API Key：`AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo`
> 注：本版在**每条命令或脚本后都附有一句简短说明**，方便“小白”理解。

---

## 目录

1. 前置条件
2. **临时**环境变量设置
3. **永久**环境变量设置（当前用户）
4. 验证代理是否生效
5. 还原／清除代理设置
6. Gemini CLI 的安装与更新
7. 常见问题与提示
8. Gemini CLI 基本用法与进阶技巧

---

## 1  前置条件

| 必备                 | 说明                       | 为何需要                 |
| ------------------ | ------------------------ | -------------------- |
| **Node.js ≥ 20**   | 从官网或 nvm‑windows 安装      | Gemini CLI 通过 npm 分发 |
| **npm**            | 随 Node 一起安装              | 用来安装/更新 gemini‑cli   |
| **Google 账号**      | 登录 Makersuite 生成 API Key | 访问 Gemini 服务         |
| **Gemini API Key** | 本指南已给出                   | CLI 鉴权               |

---

## 2  临时环境变量设置

> **当前终端窗口有效**，关闭窗口即失效。

### 2.1  Command Prompt (CMD)

```cmd
:: 指定 HTTP/HTTPS/ALL 代理
set http_proxy=http://192.168.153.200:7890
set https_proxy=http://192.168.153.200:7890
set ALL_PROXY=http://192.168.153.200:7890

:: 写入 Gemini API Key
set GEMINI_API_KEY=AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo
set GOOGLE_API_KEY=AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo
```

### 2.2  PowerShell

```powershell
$env:http_proxy  = 'http://192.168.153.200:7890'
$env:https_proxy = 'http://192.168.153.200:7890'
$env:ALL_PROXY   = 'http://192.168.153.200:7890'
$env:GEMINI_API_KEY = 'AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo'
$env:GOOGLE_API_KEY = 'AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo'
```

### 2.3  Git Bash

```bash
export http_proxy=http://192.168.153.200:7890
export https_proxy=http://192.168.153.200:7890
export ALL_PROXY=http://192.168.153.200:7890
export GEMINI_API_KEY=AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo
export GOOGLE_API_KEY=AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo
```

---

## 3  永久环境变量设置（当前用户）

### 3.1 CMD 批处理脚本（变量统一管理）

```bat
@echo off
setlocal
set "PROXY=http://192.168.153.200:7890"
set "APIKEY=AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo"
for %%v in (http_proxy https_proxy ALL_PROXY) do (
    setx %%v "%PROXY%"
)
for %%v in (GEMINI_API_KEY GOOGLE_API_KEY) do (
    setx %%v "%APIKEY%"
)
echo ✅ 完成，重启终端生效。
pause
```

### 3.2 PowerShell 脚本

```powershell
$Proxy  = 'http://192.168.153.200:7890'
$ApiKey = 'AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo'
foreach ($v in 'http_proxy','https_proxy','ALL_PROXY') {
    [Environment]::SetEnvironmentVariable($v, $Proxy,  'User')
}
foreach ($v in 'GEMINI_API_KEY','GOOGLE_API_KEY') {
    [Environment]::SetEnvironmentVariable($v, $ApiKey, 'User')
}
```

### 3.3 Git Bash 配置文件

```bash
echo "export http_proxy=http://192.168.153.200:7890" >> ~/.bashrc
echo "export https_proxy=http://192.168.153.200:7890" >> ~/.bashrc
echo "export ALL_PROXY=http://192.168.153.200:7890" >> ~/.bashrc
echo "export GEMINI_API_KEY=AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo" >> ~/.bashrc
echo "export GOOGLE_API_KEY=AIzaSyDIQ1thuKizqwYzFylem23XvFrNWMhYiQo" >> ~/.bashrc
source ~/.bashrc
```

---

## 4 验证代理是否生效

```bash
curl -I https://www.google.com
```

若能成功连接说明代理生效。

---

## 5 还原／清除代理设置

CMD:

```cmd
setx http_proxy ""
setx https_proxy ""
setx ALL_PROXY ""
setx GEMINI_API_KEY ""
setx GOOGLE_API_KEY ""
```

PowerShell:

```powershell
foreach ($v in 'http_proxy','https_proxy','ALL_PROXY','GEMINI_API_KEY','GOOGLE_API_KEY') {
    [Environment]::SetEnvironmentVariable($v, $null, 'User')
}
```

Git Bash:
手动编辑 `~/.bashrc` 删除相关行，并 `source ~/.bashrc`

---

## 6 Gemini CLI 的安装与更新

```bash
npm install -g @google/gemini-cli    # 安装
npm update -g @google/gemini-cli     # 更新
npm uninstall -g @google/gemini-cli  # 卸载
```

---

## 7 常见问题与提示

* 命令无响应：检查代理端口是否可用
* `gemini` 命令找不到：确保 npm 全局目录已加入 PATH
* 调用超限：每天限 1000 次请求，每分钟 60 次

---

## 8 Gemini CLI 基本用法与进阶技巧

* 查看帮助：`gemini --help`
* 快速提问：`gemini "Explain this code"`
* 交互模式：`gemini -i`
* 使用 proxy 临时参数：`gemini --proxy http://127.0.0.1:7890 "Hello"`
* 文件理解：`gemini explain your_file.py`
* JSON 输出：`gemini "return result as JSON" --format json`

---

> 使用技巧：建议写 alias 简化代理命令，结合 `.bashrc` 或 `.ps1` 脚本批量配置。
