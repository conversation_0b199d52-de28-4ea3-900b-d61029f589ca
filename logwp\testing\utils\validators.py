"""测试验证器 - 测试专用

提供测试数据验证的工具函数。

Examples
--------
>>> from logwp.testing.utils import validate_test_dataset, assert_dataset_structure
>>>
>>> # 验证测试数据集
>>> is_valid = validate_test_dataset(dataset)
>>>
>>> # 断言数据集结构
>>> assert_dataset_structure(dataset, expected_columns=["WELL", "MD", "GR"])
>>>
>>> # 检查曲线一致性
>>> check_curve_consistency(dataset)
"""

from __future__ import annotations

import pandas as pd
import numpy as np
from typing import Any, TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.models.datasets import WpDepthIndexedDatasetBase


def validate_test_dataset(dataset: Any) -> bool:
    """验证测试数据集的基本有效性。

    Args:
        dataset: 要验证的数据集对象

    Returns:
        bool: 验证是否通过

    Examples:
        >>> is_valid = validate_test_dataset(my_dataset)
        >>> assert is_valid, "数据集验证失败"
    """
    try:
        # 检查基本属性
        if not hasattr(dataset, 'name'):
            print("❌ 数据集缺少name属性")
            return False

        if not hasattr(dataset, 'df'):
            print("❌ 数据集缺少df属性")
            return False

        # 检查DataFrame
        df = dataset.df
        if not isinstance(df, pd.DataFrame):
            print("❌ df不是pandas DataFrame")
            return False

        if len(df) == 0:
            print("❌ DataFrame为空")
            return False

        # 检查基本列
        required_columns = ["WELL", "MD"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ 缺少必需列: {missing_columns}")
            return False

        # 检查井名列
        if df["WELL"].isnull().any():
            print("❌ 井名列包含空值")
            return False

        # 检查深度列
        if df["MD"].isnull().any():
            print("❌ 深度列包含空值")
            return False

        if not pd.api.types.is_numeric_dtype(df["MD"]):
            print("❌ 深度列不是数值类型")
            return False

        print("✓ 数据集验证通过")
        return True

    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        return False


def assert_dataset_structure(
    dataset: Any,
    expected_columns: list[str] | None = None,
    min_rows: int = 1,
    max_rows: int | None = None
) -> None:
    """断言数据集结构符合预期。

    Args:
        dataset: 要检查的数据集
        expected_columns: 期望的列名列表
        min_rows: 最小行数
        max_rows: 最大行数（可选）

    Raises:
        AssertionError: 结构不符合预期时抛出

    Examples:
        >>> assert_dataset_structure(
        ...     dataset,
        ...     expected_columns=["WELL", "MD", "GR", "PHIT"],
        ...     min_rows=10,
        ...     max_rows=100
        ... )
    """
    # 检查基本属性
    assert hasattr(dataset, 'name'), "数据集缺少name属性"
    assert hasattr(dataset, 'df'), "数据集缺少df属性"

    df = dataset.df
    assert isinstance(df, pd.DataFrame), "df不是pandas DataFrame"

    # 检查行数
    assert len(df) >= min_rows, f"行数不足，期望至少{min_rows}行，实际{len(df)}行"
    if max_rows is not None:
        assert len(df) <= max_rows, f"行数过多，期望最多{max_rows}行，实际{len(df)}行"

    # 检查列
    if expected_columns:
        missing_columns = [col for col in expected_columns if col not in df.columns]
        assert not missing_columns, f"缺少期望的列: {missing_columns}"

        extra_columns = [col for col in df.columns if col not in expected_columns]
        if extra_columns:
            print(f"⚠️ 发现额外的列: {extra_columns}")


def check_curve_consistency(dataset: Any) -> dict[str, Any]:
    """检查曲线数据的一致性。

    Args:
        dataset: 要检查的数据集

    Returns:
        dict: 检查结果报告

    Examples:
        >>> report = check_curve_consistency(dataset)
        >>> print(f"检查了 {report['total_curves']} 条曲线")
        >>> if report['issues']:
        ...     print(f"发现 {len(report['issues'])} 个问题")
    """
    report = {
        "total_curves": 0,
        "numeric_curves": 0,
        "string_curves": 0,
        "issues": [],
        "statistics": {}
    }

    try:
        df = dataset.df
        report["total_curves"] = len(df.columns)

        for column in df.columns:
            if column in ["WELL"]:
                # 井名列检查
                report["string_curves"] += 1
                unique_wells = df[column].nunique()
                if unique_wells > 5:
                    report["issues"].append(f"井名列 {column} 包含过多不同值: {unique_wells}")

            elif column in ["MD", "MD_Top", "MD_Bottom"] or "DEPTH" in column.upper():
                # 深度列检查
                report["numeric_curves"] += 1
                if df[column].isnull().any():
                    report["issues"].append(f"深度列 {column} 包含空值")

                if not df[column].is_monotonic_increasing:
                    report["issues"].append(f"深度列 {column} 不是单调递增")

                depth_range = df[column].max() - df[column].min()
                report["statistics"][f"{column}_range"] = depth_range

            elif pd.api.types.is_numeric_dtype(df[column]):
                # 数值列检查
                report["numeric_curves"] += 1

                # 检查异常值
                if df[column].isnull().sum() > len(df) * 0.5:
                    report["issues"].append(f"数值列 {column} 空值过多")

                # 检查数值范围
                if column.upper() in ["PHIT", "PHIE", "SW", "SO"]:
                    # 孔隙度和饱和度应该在0-1之间
                    out_of_range = ((df[column] < 0) | (df[column] > 1)).sum()
                    if out_of_range > 0:
                        report["issues"].append(f"孔隙度/饱和度列 {column} 有 {out_of_range} 个值超出[0,1]范围")

                elif column.upper() in ["PERM", "K"]:
                    # 渗透率应该大于0
                    negative_perm = (df[column] < 0).sum()
                    if negative_perm > 0:
                        report["issues"].append(f"渗透率列 {column} 有 {negative_perm} 个负值")

                # 统计信息
                report["statistics"][f"{column}_mean"] = df[column].mean()
                report["statistics"][f"{column}_std"] = df[column].std()

            else:
                # 字符串列检查
                report["string_curves"] += 1
                unique_values = df[column].nunique()
                report["statistics"][f"{column}_unique_count"] = unique_values

                if unique_values > len(df) * 0.8:
                    report["issues"].append(f"字符串列 {column} 唯一值过多，可能不是类别型数据")

        return report

    except Exception as e:
        report["issues"].append(f"检查过程中出错: {e}")
        return report


def validate_curve_metadata(metadata: Any) -> bool:
    """验证曲线元数据的有效性。

    Args:
        metadata: 要验证的曲线元数据

    Returns:
        bool: 验证是否通过
    """
    try:
        # 检查基本属性
        if not hasattr(metadata, 'curves'):
            print("❌ 元数据缺少curves属性")
            return False

        if not hasattr(metadata, 'has_curve'):
            print("❌ 元数据缺少has_curve方法")
            return False

        # 检查必需曲线
        required_curves = ["WELL"]
        for curve_name in required_curves:
            if not metadata.has_curve(curve_name):
                print(f"❌ 缺少必需曲线: {curve_name}")
                return False

        # 检查深度曲线
        depth_curves = ["MD", "DEPTH", "MD_Top", "MD_Bottom"]
        has_depth = any(metadata.has_curve(curve) for curve in depth_curves)
        if not has_depth:
            print("❌ 缺少深度曲线")
            return False

        print("✓ 曲线元数据验证通过")
        return True

    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        return False


def quick_dataset_summary(dataset: Any) -> dict[str, Any]:
    """快速生成数据集摘要信息。

    Args:
        dataset: 要分析的数据集

    Returns:
        dict: 摘要信息
    """
    summary = {
        "name": getattr(dataset, 'name', 'Unknown'),
        "type": type(dataset).__name__,
        "shape": None,
        "columns": [],
        "wells": [],
        "depth_range": None,
        "issues": []
    }

    try:
        df = dataset.df
        summary["shape"] = df.shape
        summary["columns"] = list(df.columns)

        # 井信息
        if "WELL" in df.columns:
            summary["wells"] = df["WELL"].unique().tolist()

        # 深度范围
        depth_columns = [col for col in df.columns if col in ["MD", "DEPTH", "MD_Top", "MD_Bottom"]]
        if depth_columns:
            depth_col = depth_columns[0]
            summary["depth_range"] = (df[depth_col].min(), df[depth_col].max())

        # 快速问题检查
        if df.isnull().any().any():
            summary["issues"].append("包含空值")

        if len(df) == 0:
            summary["issues"].append("数据为空")

    except Exception as e:
        summary["issues"].append(f"分析出错: {e}")

    return summary


def assert_curves_allclose(
    actual: np.ndarray,
    desired: np.ndarray,
    rtol: float = 1e-7,
    atol: float = 0,
    err_msg: str = "",
) -> None:
    """
    断言两条曲线（NumPy数组）在给定容差范围内是否几乎相等。

    这是对 `np.testing.assert_allclose` 的封装，提供了更符合测井业务场景的
    错误信息。

    Args:
        actual: 实际计算得到的曲线数组。
        desired: 期望的曲线数组。
        rtol: 相对容差。
        atol: 绝对容差。
        err_msg: 断言失败时显示的附加错误信息。

    Raises:
        AssertionError: 如果两条曲线不几乎相等。
    """
    try:
        np.testing.assert_allclose(actual, desired, rtol=rtol, atol=atol, err_msg=err_msg)
    except AssertionError as e:
        # Re-raise with a more informative message
        raise AssertionError(
            f"曲线数值验证失败。\n{err_msg}\n详细信息: {e}"
        ) from e


def assert_physical_constraints(
    curve_data: np.ndarray,
    curve_name: str,
    constraints: dict[str, float | None],
) -> None:
    """
    检查曲线是否符合物理约束。

    Args:
        curve_data: 要检查的曲线数据。
        curve_name: 曲线名称，用于在错误信息中标识。
        constraints: 约束字典，键为 'min' 或 'max'，值为约束值。
                     None表示不检查该边界。

    Raises:
        AssertionError: 如果曲线值违反了物理约束。
    """
    min_val = constraints.get("min")
    max_val = constraints.get("max")

    if min_val is not None:
        assert np.all(curve_data >= min_val), (
            f"物理约束验证失败: 曲线 '{curve_name}' 的部分值 "
            f"({np.min(curve_data)}) 小于允许的最小值 ({min_val})。"
        )

    if max_val is not None:
        assert np.all(curve_data <= max_val), (
            f"物理约束验证失败: 曲线 '{curve_name}' 的部分值 "
            f"({np.max(curve_data)}) 大于允许的最大值 ({max_val})。"
        )


def assert_statistical_properties(
    curve_data: np.ndarray,
    curve_name: str,
    expected_mean: float | None = None,
    expected_std: float | None = None,
    tolerance: float = 0.1,
) -> None:
    """
    检查曲线数据是否具有预期的统计属性。

    Args:
        curve_data: 要检查的曲线数据。
        curve_name: 曲线名称。
        expected_mean: 预期的均值。
        expected_std: 预期的标准差。
        tolerance: 比较时的相对容差。

    Raises:
        AssertionError: 如果统计属性不符合预期。
    """
    if expected_mean is not None:
        actual_mean = np.mean(curve_data)
        assert np.isclose(actual_mean, expected_mean, rtol=tolerance), (
            f"统计属性验证失败: 曲线 '{curve_name}' 的均值 ({actual_mean:.4f}) "
            f"与预期均值 ({expected_mean:.4f}) 不符 (容差: {tolerance})。"
        )

    if expected_std is not None:
        actual_std = np.std(curve_data)
        assert np.isclose(actual_std, expected_std, rtol=tolerance), (
            f"统计属性验证失败: 曲线 '{curve_name}' 的标准差 ({actual_std:.4f}) "
            f"与预期标准差 ({expected_std:.4f}) 不符 (容差: {tolerance})。"
        )
