from __future__ import annotations

"""logwp.models.curve.service.metadata_extraction - 曲线元数据提取服务

实现曲线元数据的智能提取功能，支持二维组合曲线元素的降级处理和冲突检测。
遵循SAD文档的内部服务层设计模式（Utility/Helper Pattern）。

Architecture
------------
层次/依赖: curve模块服务层，依赖curve.metadata、constants、exceptions
设计原则: 无状态服务、职责分离、类型安全
性能特征: 高效算法、内存优化、批量处理

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
- 《SCAPE_DDS_详细设计_logwp.md》§4.3 - CurveMetadata元数据提取重构
"""

from typing import TYPE_CHECKING

from logwp.models.constants import WpStatisticsKeys
from logwp.models.exceptions import WpCurveMetadataError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.curve.metadata import (
        CurveMetadata, CurveBasicAttributes, CurveListAnalysis,
        CurveItemType
    )

logger = get_logger(__name__)


def extract_metadata(
    curve_list: list[str],
    source_metadata: 'CurveMetadata',
    analyze_curve_list_func
) -> 'CurveMetadata':
    """根据曲线列表提取曲线元数据子集。

    从源曲线元数据中提取指定曲线的元数据，支持二维组合曲线元素的智能降级处理。
    当曲线列表中包含二维组合曲线元素时，会将其降级为一维曲线。

    Args:
        curve_list: 曲线列表，支持：
            - 一维曲线：["GR", "DEN"] （直接复制）
            - 二维组合曲线基础名称：["T2_VALUE"] （完整复制）
            - 二维组合曲线元素：["T2_VALUE[1]"] （降级为一维曲线）
            - 混合格式：["GR", "T2_VALUE", "T2_TIME[1]"]
        source_metadata: 源曲线元数据对象
        analyze_curve_list_func: 曲线列表分析函数

    Returns:
        CurveMetadata: 提取的曲线元数据子集

    Raises:
        WpCurveMetadataError: 当曲线列表中包含未知曲线或存在二维组合曲线表示冲突时抛出

    Examples:
        >>> # 基本提取
        >>> subset = extract_metadata(["GR", "DEN", "PHIT"], source_metadata, ...)
        >>> assert len(subset.curves) == 3

        >>> # 二维组合曲线完整提取
        >>> subset = extract_metadata(["GR", "T2_VALUE"], source_metadata, ...)
        >>> assert subset.curves["T2_VALUE"].is_2d_composite_curve()

        >>> # 二维组合曲线元素降级
        >>> subset = extract_metadata(["GR", "T2_VALUE[1]", "T2_VALUE[3]"], source_metadata, ...)
        >>> assert len(subset.curves) == 3
        >>> assert not subset.curves["T2_VALUE[1]"].is_2d_composite_curve()  # 降级为一维
        >>> assert not subset.curves["T2_VALUE[3]"].is_2d_composite_curve()  # 降级为一维

    Note:
        - 二维组合曲线元素降级时会继承父曲线的基本属性（单位、数据类型、描述等）
        - 降级后的一维曲线使用元素名称作为曲线名（如 "T2_VALUE[1]"）
        - DataFrame列名会自动转换为友好格式（如 "T2_VALUE_1"）
        - 保持曲线的添加顺序与输入列表顺序一致

    References:
        《SCAPE_DDS_详细设计_logwp.md》§4.3 - CurveMetadata元数据提取
    """
    # 导入需要的类型（避免循环导入）
    from logwp.models.curve.metadata import CurveMetadata, CurveItemType

    # 第一步：分析曲线列表
    analysis = analyze_curve_list_func(curve_list)

    # 第二步：检查冲突
    if analysis.has_conflicts:
        conflict_details = []
        for conflict in analysis.conflicts:
            conflict_details.append(
                f"'{conflict.base_curve_name}': 紧凑形式 '{conflict.compact_form}' "
                f"与元素形式 {conflict.element_forms} 同时存在"
            )

        raise WpCurveMetadataError(
            f"检测到二维组合曲线表示冲突。请选择使用紧凑形式或元素形式，不要混用。冲突详情: {'; '.join(conflict_details)}",
            context=ErrorContext(
                operation=WpStatisticsKeys.OPERATION_EXTRACT_METADATA,
                additional_info={
                    "requested_curve_list": curve_list,
                    "conflicts": [
                        {
                            "base_curve": c.base_curve_name,
                            "compact_form": c.compact_form,
                            "element_forms": c.element_forms
                        } for c in analysis.conflicts
                    ]
                }
            )
        )

    # 第三步：检查不存在的曲线
    if analysis.not_found_curves:
        raise WpCurveMetadataError(
            f"曲线列表中包含不存在的曲线: {analysis.not_found_curves}",
            context=ErrorContext(
                operation=WpStatisticsKeys.OPERATION_EXTRACT_METADATA,
                additional_info={
                    "available_curves": list(source_metadata.curves.keys()),
                    "requested_curve_list": curve_list,
                    "not_found_curves": analysis.not_found_curves
                }
            )
        )

    # 第四步：创建新的曲线元数据
    new_metadata = CurveMetadata()

    # 第五步：处理每个曲线项
    for item in analysis.curve_items:
        if item.item_type == CurveItemType.ONE_DIMENSIONAL:
            # 一维曲线：直接复制
            copied_curve = _copy_curve_attributes(source_metadata.curves[item.curve_name])
            new_metadata.add_curve(copied_curve)

        elif item.item_type == CurveItemType.TWO_DIMENSIONAL:
            # 二维组合曲线：完整复制
            copied_curve = _copy_curve_attributes(source_metadata.curves[item.curve_name])
            new_metadata.add_curve(copied_curve)

        elif item.item_type == CurveItemType.TWO_DIMENSIONAL_ELEMENT:
            # 二维组合曲线元素：降级为一维曲线
            parent_curve = source_metadata.curves[item.parent_curve]
            downgraded_curve = create_downgraded_1d_curve(
                element_name=item.curve_name,
                parent_curve=parent_curve,
                element_index=item.element_index
            )
            new_metadata.add_curve(downgraded_curve)

    logger.info(
        "曲线元数据提取完成",
        operation="extract_metadata",
        input_curve_count=len(curve_list),
        output_curve_count=len(new_metadata.curves),
        input_curves=curve_list,
        output_curves=list(new_metadata.curves.keys()),
        downgraded_curves=[
            item.curve_name for item in analysis.curve_items
            if item.item_type == CurveItemType.TWO_DIMENSIONAL_ELEMENT
        ]
    )

    return new_metadata


def _copy_curve_attributes(original_curve: 'CurveBasicAttributes') -> 'CurveBasicAttributes':
    """创建曲线属性的深拷贝。

    Args:
        original_curve: 原始曲线属性

    Returns:
        CurveBasicAttributes: 复制的曲线属性
    """
    from logwp.models.curve.metadata import CurveBasicAttributes

    # 创建深拷贝以避免修改原始数据
    return CurveBasicAttributes(
        name=original_curve.name,
        unit=original_curve.unit,
        data_type=original_curve.data_type,
        description=original_curve.description,
        category=original_curve.category,
        dimension=original_curve.dimension,
        curve_class=original_curve.curve_class,
        element_names=original_curve.element_names.copy() if original_curve.element_names else None,
        is_well_identifier=original_curve.is_well_identifier,
        depth_role=original_curve.depth_role
    )


def create_downgraded_1d_curve(
    element_name: str,
    parent_curve: 'CurveBasicAttributes',
    element_index: int
) -> 'CurveBasicAttributes':
    """创建从二维组合曲线元素降级而来的一维曲线。

    Args:
        element_name: 元素名称，如 "T2_VALUE[1]"
        parent_curve: 父二维组合曲线属性
        element_index: 元素索引（1-based）

    Returns:
        CurveBasicAttributes: 降级后的一维曲线属性

    References:
        《SCAPE_DDS_详细设计_logwp.md》§4.3 - 二维组合曲线元素降级处理
    """
    from logwp.models.curve.metadata import CurveBasicAttributes

    # 创建一维曲线，继承父曲线的基本属性
    # 注意：element_name（如"T2_VALUE[1]"）会自动生成正确的DataFrame列名（如"T2_VALUE_1"）
    downgraded_curve = CurveBasicAttributes.create_1d_curve(
        name=element_name,  # 使用元素名称作为曲线名
        unit=parent_curve.unit,
        data_type=parent_curve.data_type,
        description=f"{parent_curve.description} - 元素 {element_index}",
        category=parent_curve.category,
        is_well_identifier=False,  # 二维组合曲线元素不能是井名标识符
        depth_role=None   # 二维组合曲线元素不能是深度参考
    )

    logger.debug(
        "二维组合曲线元素降级为一维曲线",
        operation="create_downgraded_1d_curve",
        element_name=element_name,
        parent_curve_name=parent_curve.name,
        element_index=element_index,
        new_curve_name=downgraded_curve.name,
        dataframe_column_name=downgraded_curve.dataframe_column_name,
        inherited_unit=downgraded_curve.unit,
        inherited_data_type=downgraded_curve.data_type.value
    )

    return downgraded_curve
