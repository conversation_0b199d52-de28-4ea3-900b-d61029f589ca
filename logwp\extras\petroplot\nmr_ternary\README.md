# `logwp.extras.petroplot.nmr_ternary` - NMR孔隙度三元图组件

**版本**: 2.0 (重构后)
**状态**: 生产就绪

### 摘要 (Abstract)

本组件 (`nmr_ternary`) 不仅是一个功能实现，更是后续所有 `petroplot` 系列绘图组件开发的**架构蓝图和最佳实践指南**。它基于 `logwp.extras.petroplot.common` 这个经过重构的通用基础包构建，是**“关注点分离”**和**“组合优于继承”**设计原则的典范。在启动任何新绘图组件的开发前，开发者（包括AI编程助手）应首先理解并遵循本组件所确立的核心设计原则，以确保代码质量、可维护性和一致性。

**开发前需明确的关键决策 (Pre-Development Decisions):**

1.  **技术选型 (Technology Selection):** 首要决策是选择绘图后端。本组件选择 **Plotly** 是因为它强大的**交互性**非常适合探索三元图这类复杂数据。**原则：优先选择能满足需求的现代化、交互式库。**
2.  **API分层 (API Layering):** 必须设计满足不同场景的API（如用于工作流的`run_*`，用于独立调用的`generate_*`，用于复现的`replot_*`）。
3.  **配置驱动 (Configuration-Driven):** 必须将**绘图逻辑**（在`Config`模型中定义）与**美学样式**（在`PlotProfile`中定义）彻底分离。
4.  **可复现性设计 (Reproducibility by Design):** 必须将**数据快照**和**逻辑快照**作为与图表同等重要的一等公民产物，以实现完全可复现。

**编程时需遵循的核心实践 (Core Implementation Practices):**

*   **“翻译官”模式 (Translator Pattern):** 必须解耦用户意图（逻辑名）与内部实现（物理列名），`_resolve_selectors`函数是此模式的典范。
*   **组合优于继承 (Composition over Inheritance):** 对于复杂的、可复用的功能（如智能布局），应将其封装为独立的**辅助类**，并在需要时**组合**使用，而不是通过复杂的继承链来获取功能。
*   **操作精确性 (Precision in Operations):** 在操作图表对象时（如`fig.update_traces`），必须使用**精确选择器**（如按名称选择）而非宽泛选择器（如按类型选择），以避免意外副作用。
*   **嵌入领域知识 (Embed Domain Knowledge):** 应通过**便捷预设函数**（如`presets.py`）将行业标准（如出版物图表尺寸、科学色阶）固化为易于调用的配置。
*   **提升用户透明度 (Enhance User Transparency):** 任何可能影响结果的“静默”数据修正（如对数转换时的值裁剪），都**必须通过日志向用户发出明确警告**。

---
## 1. 架构与设计

本组件经过全面重构和代码审核，结论为**代码质量极高，堪称新架构下的典范，无需修改**。它不仅能出色地完成其核心功能，更重要的是，它为后续所有绘图组件的开发树立了一个绝佳的标杆。

### 1.1. 架构与设计亮点 (Architectural Highlights)

1.  **基于分层通用基础包构建 (Built on a Layered Common Foundation)**:
    *   `nmr_ternary` 的开发充分利用了 `logwp.extras.petroplot.common` 这个经过重构的、分工明确的通用基础包。
    *   **核心基础**:
        *   `common.plotter_abc`: 定义了**与绘图库无关的**抽象基类（如`PetroPlotter`, `TernaryPlotter`），它们使用**模板方法模式**定义了所有绘图器的标准流程。
        *   `common.config`: 包含通用的Pydantic配置模型，如 `ContinuousColorConfig`, `LegendConfig` 等，遵循了DRY原则。
        *   `common.layout_manager` 和 `common.legend_manager`: 提供了**纯净的、职责单一的布局“零件”**。`LayoutManager`负责通用外观，`LegendManager`负责标准图例。
    *   **最佳实践**: `NmrTernaryPlotter` **继承**自 `common.plotter_abc.TernaryPlotter` 来获取标准的绘图流程和接口，同时在其 `_apply_layout` 方法中**组合**使用 `LayoutManager` 和 `LegendManager` 来高效地构建最终布局。这完美地展示了新架构的核心思想。

2.  **完美的分层与解耦**:
    *   **三层API (`facade.py`)**: 组件提供了`run_*` (步骤门面)、`generate_*` (普通门面) 和 `replot_*` (重绘函数) 三层API，清晰地满足了工作流集成、独立调用和图表复现这三种不同场景的需求。
    *   **关注点分离**: 通过 `NmrTernaryDataSelectors` (用户逻辑名) 和 `NmrTernaryColumnSelectors` (内部物理列名) 的分离，以及 `_resolve_selectors` “翻译官”函数的实现，彻底解耦了用户意图和内部实现，这是整个设计的基石。
    *   **配置驱动**: 绘图的所有方面——从数据处理逻辑（如归一化）到视觉表现（如标题、颜色），再到美学风格（如字体、线宽）——完全由 `NmrTernaryPlotConfig` 和 `PlotProfile` 对象驱动。核心绘图引擎 `internal/plotter.py` 是一个纯粹的、无业务逻辑的“渲染器”，这使得代码极易维护和扩展。

3.  **卓越的可复现性与可追踪性**:
    *   **数据快照**: `run_nmr_ternary_plot_step` 在生成图表的同时，会自动保存一份用于绘图的 `plot_snapshot.csv` 数据快照。
    *   **逻辑快照**: 同时，它还会保存一份 `logic_config.json`，其中包含了绘图所用的全部配置和列名。
    *   **产物追踪**: 所有产物（图表、数据快照、逻辑快照）都被 `ctx.register_artifact` 注册，并使用 `constants.py` 中定义的规范化名称，完全符合可追踪机器学习组件的标准。

4.  **高级设计模式的最佳实践**:
    *   **模板方法模式 (Template Method Pattern)**: `common.plotter_abc.PetroPlotter` 基类是此模式的完美应用。它定义了绘图的骨架流程（`plot()`方法），同时将特定图表的实现细节（如 `_draw_main_plot`, `_add_data_traces`）委托给子类（如 `NmrTernaryPlotter`）去实现。
    *   **组合优于继承 (Composition over Inheritance)**: 面对复杂的布局需求，`NmrTernaryPlotter` 不再继承一个庞大的布局基类，而是在其 `_apply_layout` 方法中，实例化并调用轻量级的、职责单一的 `LayoutManager` 和 `LegendManager` 辅助类。它扮演着“总指挥”的角色，从这些“零件供应商”处获取标准布局，并结合自身特殊的布局需求（如为颜色轴收缩绘图区域），最终组装出完整的图表布局。这使得代码更灵活、更易于理解和维护。

5.  **自配置模型与声明式API (Self-Configuring Model & Declarative API)**:
    *   **问题**: 最初，布局逻辑（如图例在右侧还是底部）被实现在了`presets.py`的便捷函数中，这导致了配置职责的错位。
    *   **解决方案**: 我们将布局的控制权完全收归到核心配置模型 `NmrTernaryPlotConfig` 中。通过引入一个高层意图属性 `legend_position`，并利用Pydantic的 `@model_validator`，我们赋予了配置模型“自配置”的能力。
    *   **工作机制**: 当用户实例化 `NmrTernaryPlotConfig` 并指定 `legend_position='bottom'` 时，模型内部的验证器会自动计算并设置 `legend` 和 `color_bar` 的所有底层坐标和方向属性。
    *   **架构优势**: 这种设计将复杂的布局计算封装在配置模型内部，使得 `plotter.py` 绘图引擎的逻辑保持纯净，`presets.py` 也回归其“配置组装”的本职。用户只需声明“想要什么”，而无需关心“如何实现”，这是向更高级的声明式API迈出的重要一步。

5.  **领域知识与用户体验的深度融合**:
    *   **嵌入科学标准**: `presets.py` 中的 `create_publication_ready_perm_config` 函数，其默认参数（如 `width_inches=3.5`，`colorscale='Plasma'`）并非随意设定，而是直接嵌入了科学期刊出版的行业标准（单栏图宽度、感知均匀色阶），引导用户产出高质量图表。
    *   **提升用户透明度**: 在 `facade.py` 的对数转换逻辑中，增加了对“静默”数据修正（将非正值替换为下限）的警告日志。这体现了构建可信赖科学计算工具的核心思想：任何可能影响结果的数据修改都应明确告知用户。

6.  **规避底层库缺陷的健壮性设计 (Robust Design that Bypasses Underlying Library Defects)**:
    *   **问题发现**: 在开发过程中，我们发现了一个Plotly库中未在文档中说明的深层次缺陷：全局布局对象 `layout.coloraxis` 与 `layout.ternary` 之间存在致命的布局冲突。当两者共存时，`ternary.domain`（绘图区域）的设置会被完全忽略，导致颜色轴必定会与主绘图区重叠。
    *   **解决方案**: 我们没有等待上游修复，而是设计了一个更健壮、更可靠的解决方案。我们彻底放弃了使用有问题的全局 `layout.coloraxis`，而是回归经典，直接在 `go.Scatterternary` 轨迹（Trace）内部，通过 `marker.colorbar` 属性来定义颜色轴及其所有布局。
    *   **架构优势**: 这一决策不仅解决了布局问题，更将颜色轴的完整定义（数据映射+布局）内聚到了其所属的 `_add_data_traces` 方法中，使得 `NmrTernaryPlotter` 的逻辑更加自洽和独立，也避免了在布局管理器（`LegendManager`）中处理本不属于它的颜色轴布局逻辑。这体现了在面对第三方库的局限性时，通过优秀架构设计来保证自身系统稳定性的能力。

#### 模块化代码审核 (Modular Code Review)
`nmr_ternary` 是一个标准化的、可复现的、可被工作流编排的绘图组件，专门用于绘制基于核磁共振（NMR）孔隙度组分（宏孔、中孔、微孔）的三元图。它支持使用其他测井曲线（如渗透率）进行颜色映射，并内置了标准的岩石物理分区背景。

- **`config.py`**: 使用 Pydantic 模型对配置进行了精细的结构化拆分（如 `AxisStyleConfig`, `BackgroundRegionConfig`），使得复杂的配置项变得清晰易懂。`Union[ContinuousColorConfig, CategoricalColorConfig]` 配合 `discriminator` 的使用，为处理不同类型的颜色映射提供了类型安全的解决方案。

- **`constants.py`**: 将所有魔法字符串（产物名、模板名）常量化，并使用 `Enum` 进行管理，极大地提升了代码的健壮性和可维护性。产物命名遵循 `step_name.<category>.<specific_name>` 的规范，保证了在复杂工作流中的唯一性。

- **`plot_profiles.py`**: 遵循 `logwp.extras.plotting` 的两级继承体系，定义了 `base` 和 `default` 两个模板，实现了项目风格和模块风格的统一。模板中的样式值均是对原始脚本 `snippets` 的高质量提取，提供了开箱即用的美观默认值。

- **`internal/plotter.py`**: `NmrTernaryPlotter` 类遵循了新的抽象基类接口，其 `plot()` 方法编排了完整的绘图流程。`_apply_layout` 方法清晰地展示了如何组合使用 `LayoutManager` 和 `LegendManager` 辅助类。

- **`facade.py`**: `run_nmr_ternary_plot_step` 是一个完美的步骤门面实现，正确地编排了目录创建、名称解析、绘图调用、产物保存和注册的全过程。`generate_nmr_ternary_plot` 中的数据预处理和拆分逻辑清晰健壮。`replot_nmr_ternary_from_snapshot` 准确地实现了“可复现绘图”的原则。

### 1.2. 核心特性

- **三层API**: 提供步骤门面、普通门面和重绘函数，灵活适应不同使用场景。
- **配置驱动**: 通过Pydantic模型将绘图逻辑（如归一化）与样式（如颜色、字体）完全分离。
- **可追踪性**: 与`logwp.extras.tracking`框架深度集成，自动记录图表、数据快照和逻辑配置作为可追踪产物。
- **可复现性**: 支持从数据和逻辑快照精确复现图表，将耗时的上游计算与轻量的绘图调试解耦。
- **可定制化**: 支持通过`logwp.extras.plotting`的`PlotProfile`系统轻松定制图表外观，无需修改代码。

---

## 2. API 使用指南与示例

本组件提供三种不同层次的API，以满足从工作流集成到独立脚本调用的各种需求。

### 2.1. 步骤门面 (Step Facade) - 标准工作流用法

`run_nmr_ternary_plot_step` 是与工作流集成的标准入口。它处理 `logwp` 的核心数据模型，自动解析曲线名，并管理所有产物的保存与注册。

**场景**: 在一个完整的工作流中，作为下游步骤消费上游计算结果。

```python
# 在一个工作流脚本中 (e.g., run_experiment.py)
import pandas as pd
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.extras.petroplot.nmr_ternary import (
    run_nmr_ternary_plot_step,
    NmrTernaryPlotConfig,
    NmrTernaryDataSelectors,
    ContinuousColorConfig
)

# 假设 train_bundle 是上游步骤产出的 WpDataFrameBundle
# train_bundle.data 包含 'VMACRO', 'VMICRO', 'VMESO', 'PERM' 等物理列

with RunContext(run_dir="./my_run") as ctx:
    # 1. 定义数据源 (使用逻辑曲线名)
    selectors = NmrTernaryDataSelectors(
        macro_curve="MACRO_POR", # 逻辑名
        micro_curve="MICRO_POR", # 逻辑名
        meso_curve="MESO_POR",   # 逻辑名
        color_curve="PERM",      # 逻辑名
        hover_extra_curves=["DEPTH"]
    )

    # 2. 定义绘图逻辑与表现
    config = NmrTernaryPlotConfig(
        title="Well-A01 NMR Porosity Ternary Plot",
        color_mapping=ContinuousColorConfig(
            log_transform=True, # 对渗透率取对数
            cmin=-1,
            cmax=4,
            colorbar_title="log10(Permeability) [mD]"
        ),
        output_formats=["html", "png", "svg"],
        legend_position='bottom' # 新增：声明图例布局意图
    )

    # 3. 执行步骤
    results = run_nmr_ternary_plot_step(
        config=config,
        selectors=selectors,
        ctx=ctx,
        bundle=train_bundle, # 传入包含元数据和数据的Bundle
        prefix="final_report" # 保证产物名称唯一
    )

    print("步骤完成，产物已保存在 './my_run/final_report_nmr_ternary' 目录中。")
    # results['figures'] 包含一个图表字典
```

### 2.2. 普通门面 (Normal Facade) - 独立脚本用法

`generate_nmr_ternary_plot` 接收一个标准的 `pandas.DataFrame`，允许在工作流之外独立使用。

**场景**: 快速原型验证、数据探索、或在一个不使用 `logwp.extras.tracking` 的简单脚本中绘图。

```python
import pandas as pd
from logwp.extras.plotting import registry
from logwp.extras.petroplot.nmr_ternary import (
    generate_nmr_ternary_plot,
    NmrTernaryPlotConfig,
    NmrTernaryColumnSelectors # 注意：这里使用物理列名选择器
)

# 1. 准备一个 pandas DataFrame
data = pd.DataFrame({
    'VMACRO': [60, 20, 10],
    'VMICRO': [20, 70, 10],
    'VMESO':  [20, 10, 80],
    'PERM':   [1000, 10, 0.1]
})

# 2. 定义数据源 (直接使用物理列名)
selectors = NmrTernaryColumnSelectors(
    macro_col="VMACRO",
    micro_col="VMICRO",
    meso_col="VMESO",
    color_col="PERM"
)

# 3. 定义绘图逻辑
config = NmrTernaryPlotConfig() # 使用默认配置

# 4. 获取绘图样式模板
profile = registry.get("petroplot.nmr_ternary.default")

# 5. 生成图表
figs_dict, processed_df = generate_nmr_ternary_plot(
    config=config,
    selectors=selectors,
    data=data,
    plot_profile=profile
)

main_fig = figs_dict['main']
main_fig.show()
```

### 2.3. 重绘函数 (Replot Function) - 复现与调试

`replot_nmr_ternary_from_snapshot` 可以从 `run_*` 步骤生成的快照文件中精确地复现图表。

**场景**: 调试图表样式、修改标签或重新生成不同格式的图表，而无需重新运行上游的耗时计算。

```python
from pathlib import Path
from logwp.extras.plotting import registry
from logwp.extras.petroplot.nmr_ternary import replot_nmr_ternary_from_snapshot

# 假设这些文件由 `run_nmr_ternary_plot_step` 生成
snapshot_csv_path = Path("./my_run/final_report_nmr_ternary/plot_snapshot.csv")
logic_config_json_path = Path("./my_run/final_report_nmr_ternary/logic_config.json")

# 1. 获取一个绘图模板 (可以与原始模板不同，以改变样式)
profile = registry.get("petroplot.nmr_ternary.default").with_updates(
    rc_params={"font.family": "Times New Roman"} # 例如，换个字体
)

# 2. 调用重绘函数
replot_nmr_ternary_from_snapshot(
    snapshot_path=snapshot_csv_path,
    logic_config_path=logic_config_json_path,
    plot_profile=profile,
    output_path=Path("./replot_figure.pdf") # 输出为PDF格式
)

print("图表已从快照成功复现并保存为 'replot_figure.pdf'")
```

---

## 3. 配置详解

本组件的灵活性主要来源于其强大的配置系统。

### 3.1. 数据选择器 (`NmrTernaryDataSelectors`)

这是用户在工作流层面与之交互的对象，其所有字段均为**逻辑曲线名**。`run_nmr_ternary_plot_step` 会自动将其“翻译”为物理列名。

```python
selectors = NmrTernaryDataSelectors(
    macro_curve="MACRO_POR",
    micro_curve="MICRO_POR",
    meso_curve="MESO_POR",
    color_curve="PERM",
    symbol_curve="LITHOLOGY", # 可选，用于按岩性区分标记符号
    split_by_curve="ZONE",     # 可选，用于按ZONE生成多个子图
    hover_extra_curves=["DEPTH", "T2LM"] # 可选，悬停时额外显示
)
```

### 3.2. 绘图配置 (`NmrTernaryPlotConfig`)

该模型控制图表的所有逻辑和表现层细节，与数据选择完全解耦。

**常用配置项快速参考:**

| 配置项 | 类型 | 作用 |
| :--- | :--- | :--- |
| `title` | `str` | 设置图表主标题。 |
| `normalize_data` | `bool` | 是否自动归一化三个组分使其和为100。 |
| `color_mapping` | `ContinuousColorConfig` 或 `CategoricalColorConfig` | 控制数据点的颜色映射逻辑。 |
| `symbol_mapping` | `SymbolConfig` | 控制数据点的符号映射逻辑。 |
| `background_regions` | `BackgroundRegionConfig` | 配置岩石物理背景分区的显示、颜色和透明度。 |
| `legend` | `LegendConfig` | 控制图例的显示、位置和标题。 |
| `legend_position` | `Literal['right', 'bottom']` | **布局主控开关**。声明图例和颜色条的整体布局策略，模型会自动计算具体坐标。 |
| `output_formats` | `List[str]` | 指定输出图表的格式，如 `["html", "png"]`。 |

---

## 4. `petroplot` 包目录结构

`nmr_ternary` 组件是 `petroplot` 系列的一部分，其目录结构清晰地反映了通用逻辑与特定实现的关注点分离。

```text
logwp/extras/petroplot/
├── common/                 # 【通用基础包】所有petroplot组件共享的逻辑
│   ├── __init__.py
│   ├── plotter_abc.py      # 绘图器抽象基类 (PetroPlotter, TernaryPlotter)
│   ├── config.py           # 通用配置模型 (颜色、图例、符号等)
│   ├── layout_manager.py   # 【新】通用布局管理器
│   ├── legend_manager.py   # 【新】标准图例管理器
│   └── ...
└── nmr_ternary/            # 【特定实现】NMR三元图组件
    ├── __init__.py         # 导出公共API
    ├── facade.py           # 实现三层API函数
    ├── config.py           # 定义 NMR三元图 特有配置 (如坐标轴、背景分区)
    ├── internal/
    │   └── plotter.py      # 核心绘图引擎 (继承自 TernaryPlotter, 组合使用 Layout/LegendManager)
    └── ...                 # (constants.py, plot_profiles.py, presets.py)
```
