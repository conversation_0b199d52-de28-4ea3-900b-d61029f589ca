from __future__ import annotations

import logging
import sys
from pathlib import Path
from typing import Optional

import structlog

# 导入新的配置模型和解耦的处理器
try:
    # 理想情况下，从全局配置模块导入
    from logwp.config.logging import LoggingConfig
except ImportError:
    # 作为备用，直接使用模型定义
    from typing import Literal
    from pydantic import BaseModel, Field

    class LoggingConfig(BaseModel):
        level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "INFO"
        json_format: bool = False
        log_to_file: bool = False
        log_file_path: str = "logs/scape_project.log"
        include_performance_context: bool = True
        include_gpu_context: bool = True
from .logging_processors import add_gpu_context, add_performance_context

__all__ = [
    "configure_logging",
    "get_logger",
]

_LOGGING_INITIALIZED = False


def configure_logging(log_config: Optional[LoggingConfig] = None) -> None:
    """
    根据提供的配置对象配置结构化日志系统。
    此函数应在应用程序启动时被调用一次，以保证配置的全局一致性。
    在Jupyter环境中，可以在第一个cell调用此函数以自定义日志行为。

    Args:
        log_config: 日志配置对象。如果为None，则使用默认配置。
    """
    global _LOGGING_INITIALIZED
    if _LOGGING_INITIALIZED:
        # 使用 structlog 本身来记录警告，因为它此时应该已经被配置好了
        structlog.get_logger(__name__).warning("日志系统已初始化，跳过重复配置。")
        return

    cfg = log_config or LoggingConfig()

    # 1. 配置标准库 logging handlers
    handlers = [logging.StreamHandler(sys.stdout)]
    if cfg.log_to_file:
        log_path = Path(cfg.log_file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        # 生产环境建议使用 RotatingFileHandler
        handlers.append(logging.FileHandler(log_path, encoding='utf-8'))

    logging.basicConfig(
        format="%(message)s",
        level=cfg.level.upper(),
        handlers=handlers,
    )

    # 2. 构建 structlog 处理器链
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        structlog.processors.TimeStamper(fmt="iso", utc=True),
    ]

    if cfg.include_performance_context:
        processors.append(add_performance_context)
    if cfg.include_gpu_context:
        processors.append(add_gpu_context)

    # 3. 链接 structlog 和标准库 logging
    structlog.configure(
        processors=processors + [
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # 4. 设置最终的渲染器 (Formatter)
    renderer = (
        structlog.processors.JSONRenderer()
        if cfg.json_format
        else structlog.dev.ConsoleRenderer(colors=True)
    )
    formatter = structlog.stdlib.ProcessorFormatter(
        processor=renderer,
        # foreign_pre_chain=processors, # 用于非structlog日志的格式化
    )

    # 将formatter应用到所有handler
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        handler.setFormatter(formatter)

    _LOGGING_INITIALIZED = True
    structlog.get_logger(__name__).info("日志系统配置完成", config=cfg.dict())


def get_logger(name: Optional[str] = None) -> structlog.BoundLogger:
    """获取结构化日志器。

    这是一个高度兼容和便捷的函数。如果日志系统尚未配置，它会自动使用
    一套适合交互式环境（如Jupyter）的默认配置进行初始化。

    Args:
        name: 日志器名称。通常传入 `__name__`。如果为None，
              structlog会自动从调用栈中推断模块名。

    Returns:
        structlog.BoundLogger: 结构化日志器
    """
    global _LOGGING_INITIALIZED
    if not _LOGGING_INITIALIZED:
        # 自动使用默认配置初始化，非常适合Jupyter Notebook环境
        configure_logging()

    return structlog.get_logger(name)
