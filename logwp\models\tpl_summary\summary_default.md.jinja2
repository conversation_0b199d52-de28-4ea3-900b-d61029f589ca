# 测井数据概况报告

**项目名称**: {{ project_info.name }}
**生成时间**: {{ generation_info.generated_at }}
**项目ID**: {{ project_info.project_id }}

## 1. 项目基本信息
- 创建时间: {{ project_info.created_at }}
- 修改时间: {{ project_info.modified_at }}
- 数据集数量: {{ project_info.dataset_count }}
- 默认深度单位: {{ project_info.default_depth_unit }}

## 2. 井头信息
- 总属性数: {{ head_attributes.total_attributes }}
{% if head_attributes.by_wfs_category %}
- 按WFS类别分布:
{% for category, count in head_attributes.by_wfs_category.items() %}
  - {{ category }}: {{ count }} 个属性
{% endfor %}
{% endif %}
{% if head_attributes.attribute_types %}
- 按数据类型分布:
{% for type_name, count in head_attributes.attribute_types.items() %}
  - {{ type_name }}: {{ count }} 个属性
{% endfor %}
{% endif %}

{% if head_attributes.attribute_records %}
- **井头属性详细列表**:

| 序号 | 类别 | 数据集 | 井名 | 曲线 | 属性名 | 数据类型 | 单位 | 值 | 描述 |
|------|------|--------|------|------|--------|----------|------|----|----- |
{% for record in head_attributes.attribute_records %}
| {{ loop.index }} | {{ record.category }} | {{ record.dataset or '-' }} | {{ record.well or '-' }} | {{ record.curve or '-' }} | {{ record.attribute }} | {{ record.data_type }} | {{ record.unit or '-' }} | {{ record.value }} | {{ record.description or '-' }} |
{% endfor %}
{% endif %}

## 3. 井名映射
- 总映射数: {{ well_mappings.total_mappings }}
{% if well_mappings.mappings_list %}
- 映射详情:
{% for mapping in well_mappings.mappings_list %}
  - {{ mapping.source }} → {{ mapping.target }}
{% endfor %}
{% endif %}

## 4. 数据集概况

### 4.1 数据集总览
| 序号 | 数据集名称 | 类型 | 采样间隔 | 曲线数 | 总行数 |
|------|-----------|------|----------|--------|--------|
{% for dataset_name, dataset_info in datasets.datasets.items() %}
{% if dataset_info.error is not defined %}
{% set basic_info = dataset_info.basic_info %}
| {{ loop.index }} | {{ basic_info.name or dataset_name }} | {{ basic_info.type or 'N/A' }} | {{ basic_info.sampling_interval or 'N/A' }} | {{ basic_info.curve_count or 'N/A' }} | {{ basic_info.total_rows or 'N/A' }} |
{% else %}
| {{ loop.index }} | {{ dataset_name }} | ERROR | - | - | - |
{% endif %}
{% endfor %}

### 4.2 数据集详情

{% for dataset_name, dataset_info in datasets.datasets.items() %}
#### 4.2.{{ loop.index }} {{ dataset_name }}

{% if dataset_info.error is defined %}
**错误**: {{ dataset_info.error }}

{% else %}
{% set basic_info = dataset_info.basic_info %}
- **基本信息**: 类型={{ basic_info.type or 'N/A' }}, 采样间隔={{ basic_info.sampling_interval or 'N/A' }}
- **DataFrame结构**: {{ basic_info.total_columns or 'N/A' }} 列, {{ basic_info.total_rows or 'N/A' }} 行

{% if dataset_info.metadata_info %}
{% set metadata_info = dataset_info.metadata_info %}
- **曲线元数据**: {{ metadata_info.total_curves or 0 }} 条曲线
{% if metadata_info.by_category %}
  - 按类别分布:
{% for category, count in metadata_info.by_category.items() %}
{% if count > 0 %}
    - {{ category }}: {{ count }} 条
{% endif %}
{% endfor %}
{% endif %}

{% if metadata_info.curve_attributes %}
- **曲线属性详细列表**:

| 序号 | 曲线名称 | 单位 | 数据类型 | 类别 | 维数 | 曲线类别 | 井标识 | 深度角色 | 元素名称 | DataFrame列名 | DataFrame元素列名 | 描述 |
|------|----------|------|----------|------|------|----------|--------|----------|----------|---------------|-------------------|------|
{% for curve_attr in metadata_info.curve_attributes %}
| {{ loop.index }} | {{ curve_attr.name }} | {{ curve_attr.unit or '-' }} | {{ curve_attr.data_type }} | {{ curve_attr.category }} | {{ curve_attr.dimension }} | {{ curve_attr.curve_class or '-' }} | {{ '是' if curve_attr.is_well_identifier else '否' }} | {{ curve_attr.depth_role or '-' }} | {% if curve_attr.element_names %}{{ curve_attr.element_names|join(', ') }}{% else %}-{% endif %} | {{ curve_attr.dataframe_column_name }} | {% if curve_attr.dataframe_element_names %}{{ curve_attr.dataframe_element_names|join(', ') }}{% else %}-{% endif %} | {{ curve_attr.description or '-' }} |
{% endfor %}
{% endif %}
{% endif %}

{% if dataset_info.well_statistics and dataset_info.well_statistics.error is not defined %}
- **按井统计**:
{% for well_name, stats in dataset_info.well_statistics.items() %}
{% if stats.get('depth_min') is not none and stats.get('depth_max') is not none %}
  - {{ well_name }}: {{ stats.get('row_count', 'N/A') }} 行, 深度范围 {{ "%.2f"|format(stats['depth_min']) }} - {{ "%.2f"|format(stats['depth_max']) }}
{% else %}
  - {{ well_name }}: {{ stats.get('row_count', 'N/A') }} 行, 深度范围 N/A
{% endif %}
{% endfor %}
{% endif %}

{% if dataset_info.curve_statistics and dataset_info.curve_statistics.error is not defined %}
{% set curve_stats = dataset_info.curve_statistics %}

##### 曲线统计分析

{% if curve_stats.summary %}
{% set summary = curve_stats.summary %}
###### 整体统计概况
- **数值型曲线**: {{ summary.numeric_count or 0 }} 条
- **类别型曲线**: {{ summary.categorical_count or 0 }} 条
- **标识型曲线**: {{ summary.identifier_count or 0 }} 条
{% endif %}

{% if curve_stats.numeric_curves %}
###### 数值型曲线详细统计

| 序号 | 曲线名称 | 总数 | 缺失值 | 有效值 | 最小值 | 最大值 | 均值 | 中位数 | 标准差 | 异常值 |
|------|----------|------|--------|--------|--------|--------|------|--------|--------|--------|
{% for curve_name, stats in curve_stats.numeric_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.valid_count or 'N/A' }} | {{ "%.3f"|format(stats.min) if stats.min is not none else 'N/A' }} | {{ "%.3f"|format(stats.max) if stats.max is not none else 'N/A' }} | {{ "%.3f"|format(stats.mean) if stats.mean is not none else 'N/A' }} | {{ "%.3f"|format(stats.median) if stats.median is not none else 'N/A' }} | {{ "%.3f"|format(stats.std) if stats.std is not none else 'N/A' }} | {{ stats.outlier_count or 0 }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if curve_stats.categorical_curves %}
###### 类别型曲线详细统计

| 序号 | 曲线名称 | 总数 | 缺失值 | 唯一值数 | 众数 | 前5个值分布 |
|------|----------|------|--------|----------|------|-------------|
{% for curve_name, stats in curve_stats.categorical_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ stats.mode or 'N/A' }} | {% if stats.top_5_values %}{% for value, count in stats.top_5_values.items() %}{{ value }}({{ count }}){% if not loop.last %}, {% endif %}{% endfor %}{% else %}N/A{% endif %} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if curve_stats.identifier_curves %}
###### 标识型曲线详细统计

| 序号 | 曲线名称 | 总数 | 缺失值 | 唯一值数 | 完整性(%) | 最常见值 | 出现次数 | 标识类型 |
|------|----------|------|--------|----------|-----------|----------|----------|----------|
{% for curve_name, stats in curve_stats.identifier_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ "%.1f"|format(stats.completeness) if stats.completeness is not none else 'N/A' }} | {{ stats.most_common or 'N/A' }} | {{ stats.most_common_count or 'N/A' }} | {{ stats.identifier_type or 'N/A' }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if curve_stats.by_well_statistics %}
##### 按井曲线统计分析

{% for well_name, well_curve_stats in curve_stats.by_well_statistics.items() %}
###### {{ well_name }}井统计

{% if well_curve_stats.error is not defined and well_curve_stats.summary %}
{% set well_summary = well_curve_stats.summary %}
**统计概况**:
- **数值型曲线**: {{ well_summary.numeric_count or 0 }} 条
- **类别型曲线**: {{ well_summary.categorical_count or 0 }} 条
- **标识型曲线**: {{ well_summary.identifier_count or 0 }} 条

{% if well_curve_stats.numeric_curves %}
**数值型曲线统计**:

| 序号 | 曲线名称 | 总数 | 缺失值 | 有效值 | 最小值 | 最大值 | 均值 | 中位数 | 标准差 | 异常值 |
|------|----------|------|--------|--------|--------|--------|------|--------|--------|--------|
{% for curve_name, stats in well_curve_stats.numeric_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.valid_count or 'N/A' }} | {{ "%.3f"|format(stats.min) if stats.min is not none else 'N/A' }} | {{ "%.3f"|format(stats.max) if stats.max is not none else 'N/A' }} | {{ "%.3f"|format(stats.mean) if stats.mean is not none else 'N/A' }} | {{ "%.3f"|format(stats.median) if stats.median is not none else 'N/A' }} | {{ "%.3f"|format(stats.std) if stats.std is not none else 'N/A' }} | {{ stats.outlier_count or 0 }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if well_curve_stats.categorical_curves %}
**类别型曲线统计**:

| 序号 | 曲线名称 | 总数 | 缺失值 | 唯一值数 | 众数 | 前5个值分布 |
|------|----------|------|--------|----------|------|-------------|
{% for curve_name, stats in well_curve_stats.categorical_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ stats.mode or 'N/A' }} | {% if stats.top_5_values %}{% for value, count in stats.top_5_values.items() %}{{ value }}({{ count }}){% if not loop.last %}, {% endif %}{% endfor %}{% else %}N/A{% endif %} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if well_curve_stats.identifier_curves %}
**标识型曲线统计**:

| 序号 | 曲线名称 | 总数 | 缺失值 | 唯一值数 | 完整性(%) | 最常见值 | 出现次数 | 标识类型 |
|------|----------|------|--------|----------|-----------|----------|----------|----------|
{% for curve_name, stats in well_curve_stats.identifier_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ "%.1f"|format(stats.completeness) if stats.completeness is not none else 'N/A' }} | {{ stats.most_common or 'N/A' }} | {{ stats.most_common_count or 'N/A' }} | {{ stats.identifier_type or 'N/A' }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% else %}
**状态**: {{ well_curve_stats.error or '数据异常' }}
{% endif %}

{% endfor %}
{% endif %}
{% endif %}

{% endif %}
{% endfor %}

---

*报告由 {{ generation_info.generator }} v{{ generation_info.version }} 生成*
