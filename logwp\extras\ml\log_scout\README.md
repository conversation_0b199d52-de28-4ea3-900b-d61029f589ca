# LogScout 组件说明文档

**版本: 1.0**

## 1. 引言

### 1.1. 目标与定位

`LogScout` 是一个标准化的、可重用的**通用分析步骤 (Generic Step)**，旨在对任何表格型数据集（尤其是测井数据）进行系统化的探索性分析。

它的核心任务是快速诊断**输入特征之间的关系**、**每个输入特征与目标特征的关系**，并识别潜在的多重共线性。它将生成一套完整的、可交付的**量化报告**和**可视化图表**产物，为后续的机器学习建模提供数据洞察和决策支持。

与项目中面向特定应用（如 `swift_pso`）的组件不同，`LogScout` 不依赖于任何特定的科学模型或业务逻辑，可以被灵活地集成到任何需要进行特征分析的机器学习工作流中。

### 1.2. 实现要点

*   **框架遵从性**: 严格遵循《可追踪机器学习组件开发框架》，是一个模块化、可追踪、可复现的`Step`。
*   **配置驱动**: 通过 `LogScoutConfig` Pydantic模型进行配置，行为可预测。
*   **样式解耦**: 所有图表均通过 `logwp.extras.plotting` 框架生成，支持通过 `PlotProfile` 进行灵活的样式定制。
*   **产物完备**: 生成一套包括定量报告（.csv）和可视化图表（.png）在内的完整产物，并遵循“数据快照”原则，保证所有图表均可复现。

---

## 2. 核心概念对照表

下表将《框架》中的核心概念与`LogScout`组件中的具体实现进行映射：

| 框架概念 | `logwp.extras.ml.log_scout` 中的具体实现 |
| :--- | :--- |
| **单一步骤包** | `logwp.extras.ml.log_scout` 整个包 |
| **步骤 (Step)** | 特征与目标关系诊断步骤 |
| **门面 (Facade)** | `facade.py` |
| **主执行函数** | `run_log_scout_step()` |
| **配置 (Config)** | `config.LogScoutConfig` |
| **内部逻辑 (Internal)** | `internal/computer.py` (定量计算) <br> `internal/plotter.py` (绘图逻辑) |
| **产物常量** | `constants.LogScoutArtifacts` |
| **产物处理器** | `artifact_handler.LogScoutArtifactHandler` |
| **绘图复现** | `plotting.py` (包含多个`replot_*`函数) |
| **绘图配置** | `plot_profiles.py` (注册`PlotProfile`模板) |
| **数据快照** | 所有图表在生成前，都会先保存其绘图所需的数据为`.csv`快照。 |

---

## 3. 组件架构与目录结构

`LogScout` 遵循标准的单一步骤包结构：

```
logwp/extras/ml/log_scout/
├── __init__.py
├── README.md
├── facade.py
├── config.py
├── constants.py
├── artifact_handler.py
├── plotting.py
├── plot_profiles.py
└── internal/
    ├── __init__.py
    ├── computer.py
    └── plotter.py
```

---

## 4. 开发细节与最佳实践

### 4.1. API 与使用

`LogScout` 的唯一公共入口是 `run_log_scout_step` 函数。

```python
def run_log_scout_step(
    config: LogScoutConfig,
    ctx: RunContext,
    data: pd.DataFrame,
    features: List[str],
    target: str,
    *,
    task_type: Literal["regression", "classification"],
    prefix: str,
    plot_profiles: Optional[Dict[str, PlotProfile]] = None,
) -> Dict[str, str]:
```

**参数说明:**
*   `config`: `LogScoutConfig` 的实例，用于配置步骤的行为。
*   `ctx`: `RunContext` 的实例，用于追踪产物和参数。
*   `data`: 包含所有特征和目标列的 Pandas DataFrame。
*   `features`: 需要分析的输入特征列名列表。
*   `target`: 目标特征的列名。
*   `task_type`: 任务类型，`'regression'` 或 `'classification'`，决定了“特征vs目标”图表的类型。
*   `prefix`: 产物输出目录的前缀，用于在同一工作流中多次运行时区分产物。
*   `plot_profiles`: (可选) 一个字典，用于传入自定义的 `PlotProfile` 对象覆盖默认样式。

**使用示例:**

```python
from logwp.extras.tracking import RunContext
from logwp.extras.ml.log_scout import run_log_scout_step, LogScoutConfig
import pandas as pd

# 1. 准备数据
df = pd.read_csv("my_log_data.csv")
features = ['GR', 'RT', 'NPHI', 'RHOB']
target = 'PERM'

# 2. 创建配置和运行上下文
config = LogScoutConfig(pairplot_max_features=8)
with RunContext("my_experiment_run") as ctx:
    # 3. 执行LogScout步骤
    result = run_log_scout_step(
        config=config,
        ctx=ctx,
        data=df,
        features=features,
        target=target,
        task_type="regression",
        prefix="initial_analysis"
    )
    print(result)
```

### 4.2. 配置 (`config.py`)

`LogScout` 的行为通过 `LogScoutConfig` Pydantic模型进行配置。

*   `pairplot_max_features: int`: 定义了自动绘制散点图矩阵 (`pairplot`) 的特征数量上限。如果输入特征的数量超过此阈值，会自动跳过绘图并给出提示，以防止程序因内存消耗过大而崩溃。默认值为 `10`。

### 4.3. 产物清单 (Artifacts)

`LogScout` 会生成一套结构化的产物，保存在 `/[prefix]_log_scout/` 目录下。

```
/[prefix]_log_scout/
├── quantitative_report/
|   ├── pearson_correlation.csv
|   ├── spearman_correlation.csv
|   ├── vif_scores.csv
|   └── mutual_information.csv
└── visual_report/
    ├── feature_correlation/
    |   ├── pearson_heatmap.png
    |   ├── pearson_clustermap.png
    |   ├── spearman_heatmap.png
    |   ├── spearman_clustermap.png
    |   └── pairplot.png
    └── target_relationship/
        ├── regplot_[feature1]_vs_[target].png
        └── boxplot_[feature1]_by_[target].png
```

*   **定量产物 (`quantitative_report/`)**:
    *   `pearson_correlation.csv`: 输入特征间的皮尔逊相关系数矩阵。
    *   `spearman_correlation.csv`: 输入特征间的斯皮尔曼相关系数矩阵。
    *   `vif_scores.csv`: 每个输入特征的方差膨胀因子（VIF）得分。
    *   `mutual_information.csv`: 每个输入特征与目标特征之间的互信息得分。

*   **可视化产物 (`visual_report/`)**:
    *   **特征间关系 (`feature_correlation/`)**:
        *   `pearson_heatmap.png` / `pearson_clustermap.png`: 皮尔逊相关性热力图。
        *   `spearman_heatmap.png` / `spearman_clustermap.png`: 斯皮尔曼相关性热力图。
        *   `pairplot.png`: 输入特征间的散点图矩阵。
    *   **特征与目标关系 (`target_relationship/`)**:
        *   **回归任务**: 为每个输入特征生成一个与目标的散点回归图 (`regplot_...`)。
        *   **分类任务**: 为每个输入特征生成一个按目标类别分组的箱形图 (`boxplot_...`)。

### 4.4. 绘图定制

所有图表均使用 `logwp.extras.plotting` 框架生成，并支持通过 `plot_profiles` 参数进行运行时定制。推荐使用 **"获取 -> 修改 -> 传入"** 模式。

```python
from logwp.extras.plotting import registry as plot_registry
from logwp.extras.ml.log_scout import LogScoutPlotTypes, LogScoutPlotProfiles

# 1. 获取默认模板
reg_profile = plot_registry.get(LogScoutPlotProfiles.REGPLOT.value)

# 2. 在运行时修改
reg_profile.artist_props["line_kws"]["color"] = "green"
reg_profile.title_props["fontsize"] = 20

# 3. 将修改后的配置传入
run_log_scout_step(
    # ... 其他参数 ...
    plot_profiles={
        LogScoutPlotTypes.REGRESSION_PLOT.value: reg_profile
    }
)
```

### 4.5. 绘图复现 (`plotting.py`)

`LogScout` 遵循“数据快照”原则，为每个图表都保存了其源数据。`plotting.py` 模块提供了从这些快照复现图表的功能，方便调试和样式调整。

*   `replot_heatmap_from_snapshot(...)`
*   `replot_clustermap_from_snapshot(...)`
*   `replot_pairplot_from_snapshot(...)`
*   `replot_target_relationship_from_snapshot(...)`

---

## 5. 总结

`LogScout` 是一个强大而灵活的通用特征分析组件。它通过标准化的流程和可交付的产物，极大地加速了机器学习项目早期的探索性数据分析阶段，为后续的建模工作提供了坚实的数据洞察和决策依据。
