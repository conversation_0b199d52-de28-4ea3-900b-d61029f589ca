{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 基础绘图数据准备"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-01T14:12:42.109804Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.76, 'cpu_percent': 0.0} file_path=santos_data_v2.wp.xlsx\n", "2025-08-01T14:12:42.140784Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.89, 'cpu_percent': 0.0} file_path=santos_data_v2.wp.xlsx file_size_mb=21.82 sheet_count=8\n", "2025-08-01T14:12:42.153256Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.89, 'cpu_percent': 0.0} project_name=santos_data_v2\n", "2025-08-01T14:12:42.159150Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.89, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data_v2\n", "2025-08-01T14:12:42.169572Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.9, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-08-01T14:12:42.175811Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.9, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-08-01T14:12:42.184875Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.87, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-08-01T14:12:42.194099Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.87, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-08-01T14:12:42.197994Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.89, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:42.248900Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.96, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=79 well_curves=1\n", "2025-08-01T14:12:55.414731Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 481.52, 'cpu_percent': 0.0} shape=(16303, 268) sheet_name=Logs\n", "2025-08-01T14:12:55.458893Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.83, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-08-01T14:12:55.466019Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.83, 'cpu_percent': 0.0} curve_count=79 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 268) processing_time=13.268\n", "2025-08-01T14:12:55.498690Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.85, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:55.519722Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.85, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=91 well_curves=1\n", "2025-08-01T14:12:57.937856Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.85, 'cpu_percent': 0.0} shape=(4502, 91) sheet_name=OBMIQ_Pred\n", "2025-08-01T14:12:57.954159Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.85, 'cpu_percent': 0.0} dataset_name=WpIdentifier('OBMIQ_Pred')\n", "2025-08-01T14:12:57.959666Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.85, 'cpu_percent': 0.0} curve_count=91 dataset_name=OBMIQ_Pred dataset_type=Continuous df_shape=(4502, 91) processing_time=2.467\n", "2025-08-01T14:12:57.981841Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.88, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-08-01T14:12:57.999659Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.88, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-08-01T14:12:58.070336Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-08-01T14:12:58.082172Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-08-01T14:12:58.087925Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.109\n", "2025-08-01T14:12:58.104056Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-08-01T14:12:58.113760Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-08-01T14:12:58.125638Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} shape=(15, 4) sheet_name=PLT\n", "2025-08-01T14:12:58.157926Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-08-01T14:12:58.159929Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(15, 4) processing_time=0.06\n", "2025-08-01T14:12:58.188914Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-08-01T14:12:58.198647Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-08-01T14:12:58.208156Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-08-01T14:12:58.219539Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-08-01T14:12:58.219539Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.033\n", "2025-08-01T14:12:58.248957Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataset_count=5 has_head_info=True has_well_map=True\n", "2025-08-01T14:12:58.261532Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataset_count=5\n", "2025-08-01T14:12:58.269043Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 482.95, 'cpu_percent': 0.0} dataset_count=5 file_path=santos_data_v2.wp.xlsx processing_time=16.159 project_name=WpIdentifier('santos_data_v2')\n", "2025-08-01T14:12:58.287871Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.03, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data_v2'}\n", "2025-08-01T14:12:58.319167Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.46, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 5}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data_v2\n", "📅 创建时间: 2025-08-01 22:12:42.159150\n", "🔧 默认深度单位: WpDepthUnit.METER\n"]}], "source": ["data_file_path = \"./santos_data_v2.wp.xlsx\" #原始数据，使用精校过的数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 创建时间: {project.created_at}\")\n", "    print(f\"🔧 默认深度单位: {project.default_depth_reference_unit}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 提取数据"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["\n", "# 不含二维\n", "logs_curves = [\n", "    'GR', 'BS', 'CAL', 'DEN', 'CN', 'DT', 'PE',\n", "    'RD', 'RS', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'RD_LWD', 'RS_LWD', 'RD_LWD_LOG10', 'RS_LWD_LOG10', 'DRES_LWD',\n", "    'PHIT_NMR', 'PHIE_NMR', 'CBW_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD',\n", "    'T2CUTOFF', 'T2LM', 'T2LM_LOG10', 'T2LM_LONG', 'T2LM_LONG_LOG10',\n", "    'T2_P20', 'T2_P20_LOG10', 'T2_P50', 'T2_P50_LOG10',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'DT2LM', 'DT2LM_LONG', 'DT2_P50', 'DT2_P20', 'DPHIT_NMR', 'DPHIE_NMR'\n", "]\n", "\n", "k_label_curves = [\n", "     'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-01T14:12:58.382510Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.67, 'cpu_percent': 0.0} curve_count=3 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_ternary\n", "2025-08-01T14:12:58.394138Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.67, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['MD', 'VMACRO', 'VMICRO', 'VMESO', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['MD', 'VMACRO', 'VMICRO', 'VMESO', 'WELL_NO']\n", "2025-08-01T14:12:58.528968Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 472.98, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:58.542289Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.01, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-01T14:12:58.604447Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.01, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_ternary target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-08-01T14:12:58.626447Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.01, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:58.644514Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.01, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO', 'VMESO', 'VMACRO'] operation=extract_curves selected_columns=5 source_rows=16303 target_rows=14285\n", "2025-08-01T14:12:58.658302Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=3 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.67, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-01T14:12:58.667844Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.67, 'cpu_percent': 0.0} curve_count=3 dropna_how=any new_dataset=nmr_ternary_cleaned operation=dropna_dataset source_dataset=nmr_ternary\n", "2025-08-01T14:12:58.681972Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=14283 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.42, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=14285 removed_rows=2\n", "2025-08-01T14:12:58.697459Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.17, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-01T14:12:58.736215Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.71, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:58.748674Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.71, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-01T14:12:58.761389Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.71, 'cpu_percent': 0.0} curve_count=3 has_query=True operation=extract_curves source_dataset=logs target_dataset=lwd_nmr_ternary\n", "2025-08-01T14:12:58.775361Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.71, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['MD', 'VMICRO_LWD', 'VMACRO_LWD', 'VMESO_LWD', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['MD', 'VMICRO_LWD', 'VMACRO_LWD', 'VMESO_LWD', 'WELL_NO']\n", "2025-08-01T14:12:58.915128Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.66, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_lwd_nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:58.932347Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.69, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-01T14:12:58.946556Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.69, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=lwd_nmr_ternary target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-08-01T14:12:58.961376Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.69, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:58.975572Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 473.69, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD'] operation=extract_curves selected_columns=5 source_rows=16303 target_rows=14285\n", "2025-08-01T14:12:58.991873Z [info     ] 曲线列表为空，将对所有数据曲线执行dropna操作。     [logwp.models.datasets.internal.dataset_dropna] all_data_curves_count=3 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.04, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-01T14:12:59.028669Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.05, 'cpu_percent': 0.0} curve_count=3 dropna_how=any new_dataset=lwd_nmr_ternary_cleaned operation=dropna_dataset source_dataset=lwd_nmr_ternary\n", "2025-08-01T14:12:59.051028Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=2443 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.6, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=14285 removed_rows=11842\n", "2025-08-01T14:12:59.064647Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.67, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-08-01T14:12:59.076227Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.67, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.088428Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.67, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-08-01T14:12:59.109864Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.7, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['VMICRO', 'VMESO', 'VMACRO'] left_dataset=nmr_ternary_cleaned max_interpolation_distance=0.075 operation=merge_datasets_left_aligned right_curves=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_dataset=K_Label\n", "2025-08-01T14:12:59.124947Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.7, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=nmr_ternary_cleaned target_dataset=nmr_ternary_cleaned_left_temp\n", "2025-08-01T14:12:59.136480Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 443.7, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['MD', 'VMACRO', 'VMICRO', 'VMESO', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['MD', 'VMACRO', 'VMICRO', 'VMESO', 'WELL_NO']\n", "2025-08-01T14:12:59.154048Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.79, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.168298Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.35, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-08-01T14:12:59.179143Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.35, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_ternary_cleaned_left_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-08-01T14:12:59.193176Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.35, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.204318Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.35, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO', 'VMESO', 'VMACRO'] operation=extract_curves selected_columns=5 source_rows=14283 target_rows=14283\n", "2025-08-01T14:12:59.246036Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.35, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=K_Label target_dataset=K_Label_right_temp\n", "2025-08-01T14:12:59.263780Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.35, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['MD', 'K_LABEL', 'K_LABEL_TYPE', 'WELL_NO', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['MD', 'K_LABEL', 'K_LABEL_TYPE', 'WELL_NO', 'PZI']\n", "2025-08-01T14:12:59.282754Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.41, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-01T14:12:59.297466Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.42, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-08-01T14:12:59.316770Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.42, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=K_Label_right_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-08-01T14:12:59.330558Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.42, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-01T14:12:59.342291Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.42, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=5 source_rows=506 target_rows=506\n", "2025-08-01T14:12:59.356521Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.42, 'cpu_percent': 0.0} left_dataset_type=WpContinuousDataset left_sampling_interval=0.03809999999975844 operation=merge_datasets_left_aligned\n", "2025-08-01T14:12:59.364130Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.42, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['MD', 'K_LABEL', 'K_LABEL_TYPE', 'WELL_NO', 'PZI']\n", "2025-08-01T14:12:59.376439Z [info     ] 开始转换右数据集为兼容格式（左对齐合并）           [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 445.42, 'cpu_percent': 0.0} operation=_convert_right_dataset_to_compatible_format right_dataset_type=WpDiscreteDataset\n", "2025-08-01T14:12:59.431545Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 447.99, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.445366Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 447.77, 'cpu_percent': 0.0} left_rows=14283 operation=_merge_dataframes_left_aligned right_data_columns=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_rows=14283\n", "2025-08-01T14:12:59.488904Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.5, 'cpu_percent': 0.0} merged_columns=8 merged_rows=14283 operation=_merge_dataframes_left_aligned\n", "2025-08-01T14:12:59.505333Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.5, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_ternary_k dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.522067Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.5, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'VMICRO', 'VMESO', 'VMACRO', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('nmr_ternary_k') result_dataset_type=WpContinuousDataset result_shape=(14283, 8)\n", "2025-08-01T14:12:59.537437Z [info     ] 开始依左侧数据集合并                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.5, 'cpu_percent': 0.0} interpolation_method=nearest left_curves=['VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD'] left_dataset=lwd_nmr_ternary_cleaned max_interpolation_distance=0.075 operation=merge_datasets_left_aligned right_curves=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_dataset=K_Label\n", "2025-08-01T14:12:59.554067Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.5, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=lwd_nmr_ternary_cleaned target_dataset=lwd_nmr_ternary_cleaned_left_temp\n", "2025-08-01T14:12:59.572018Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.5, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['MD', 'VMICRO_LWD', 'VMACRO_LWD', 'VMESO_LWD', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['MD', 'VMICRO_LWD', 'VMACRO_LWD', 'VMESO_LWD', 'WELL_NO']\n", "2025-08-01T14:12:59.593335Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.52, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_lwd_nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.608619Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.55, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-08-01T14:12:59.623777Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.55, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=lwd_nmr_ternary_cleaned_left_temp target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-08-01T14:12:59.639587Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.55, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary_cleaned_left_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.652983Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.55, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD'] operation=extract_curves selected_columns=5 source_rows=2443 target_rows=2443\n", "2025-08-01T14:12:59.664164Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.55, 'cpu_percent': 0.0} curve_count=3 has_query=False operation=extract_curves source_dataset=K_Label target_dataset=K_Label_right_temp\n", "2025-08-01T14:12:59.679059Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.55, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['MD', 'K_LABEL', 'K_LABEL_TYPE', 'WELL_NO', 'PZI'] operation=extract_metadata output_curve_count=5 output_curves=['MD', 'K_LABEL', 'K_LABEL_TYPE', 'WELL_NO', 'PZI']\n", "2025-08-01T14:12:59.693945Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.56, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-01T14:12:59.706494Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.57, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-08-01T14:12:59.719981Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.57, 'cpu_percent': 0.0} is_uniform_sampling=False operation=smart_type_detection sampling_interval=None source_type=WpDiscreteDataset target_dataset=K_Label_right_temp target_type=WpDiscreteDataset type_reason=非等间隔采样\n", "2025-08-01T14:12:59.747406Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.57, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp dataset_type=Point operation=dataset_initialization\n", "2025-08-01T14:12:59.767609Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.57, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=extract_curves selected_columns=5 source_rows=506 target_rows=506\n", "2025-08-01T14:12:59.781139Z [info     ] 左数据集深度参数确定                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.57, 'cpu_percent': 0.0} left_dataset_type=WpContinuousDataset left_sampling_interval=0.15239999999994325 operation=merge_datasets_left_aligned\n", "2025-08-01T14:12:59.792046Z [info     ] 曲线名冲突处理完成                      [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.57, 'cpu_percent': 0.0} operation=merge_datasets_left_aligned rename_mapping={} renamed_right_curves=['MD', 'K_LABEL', 'K_LABEL_TYPE', 'WELL_NO', 'PZI']\n", "2025-08-01T14:12:59.802637Z [info     ] 开始转换右数据集为兼容格式（左对齐合并）           [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.57, 'cpu_percent': 0.0} operation=_convert_right_dataset_to_compatible_format right_dataset_type=WpDiscreteDataset\n", "2025-08-01T14:12:59.821882Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.7, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label_right_temp_interpolated_temp dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.832255Z [info     ] 开始合并已对齐的数据集                    [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.7, 'cpu_percent': 0.0} left_rows=2443 operation=_merge_dataframes_left_aligned right_data_columns=['K_LABEL', 'K_LABEL_TYPE', 'PZI'] right_rows=2443\n", "2025-08-01T14:12:59.845790Z [info     ] 数据集合并完成                        [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.7, 'cpu_percent': 0.0} merged_columns=8 merged_rows=2443 operation=_merge_dataframes_left_aligned\n", "2025-08-01T14:12:59.853760Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.7, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=lwd_nmr_ternary_k dataset_type=Continuous operation=dataset_initialization\n", "2025-08-01T14:12:59.867030Z [info     ] 依左侧数据集合并完成                     [logwp.models.internal.dataset_left_aligned_merge] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.7, 'cpu_percent': 0.0} final_columns=['WELL_NO', 'MD', 'VMICRO_LWD', 'VMESO_LWD', 'VMACRO_LWD', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'] operation=merge_datasets_left_aligned result_dataset_name=WpIdentifier('lwd_nmr_ternary_k') result_dataset_type=WpContinuousDataset result_shape=(2443, 8)\n", "2025-08-01T14:12:59.880474Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.7, 'cpu_percent': 0.0} dataset_count=1 file_path=nmr_ternary.wp.xlsx project_name=WpIdentifier('nmr_ternary') save_head_info=True save_well_map=True\n", "2025-08-01T14:12:59.900865Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.7, 'cpu_percent': 0.0} curve_count=8 dataset_name=WpIdentifier('nmr_ternary_k') dataset_type=Continuous df_shape=(14283, 8)\n", "2025-08-01T14:13:00.265721Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 456.52, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_ternary_k') processing_time=0.365\n", "2025-08-01T14:13:00.275934Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 456.52, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-08-01T14:13:00.293251Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 456.52, 'cpu_percent': 0.0}\n", "2025-08-01T14:13:00.303745Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 456.52, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-08-01T14:13:02.364110Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 456.55, 'cpu_percent': 0.0}\n", "2025-08-01T14:13:02.383606Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 456.55, 'cpu_percent': 0.0}\n", "2025-08-01T14:13:03.487984Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 460.23, 'cpu_percent': 0.0} file_path=nmr_ternary.wp.xlsx processing_time=3.608 project_name=WpIdentifier('nmr_ternary')\n", "✅WL数据已保存: nmr_ternary.wp.xlsx\n", "2025-08-01T14:13:03.504729Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 460.23, 'cpu_percent': 0.0} dataset_count=1 file_path=lwd_nmr_ternary.wp.xlsx project_name=WpIdentifier('lwd_nmr_ternary') save_head_info=True save_well_map=True\n", "2025-08-01T14:13:03.526767Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 460.07, 'cpu_percent': 0.0} curve_count=8 dataset_name=WpIdentifier('lwd_nmr_ternary_k') dataset_type=Continuous df_shape=(2443, 8)\n", "2025-08-01T14:13:03.708225Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.32, 'cpu_percent': 0.0} dataset_name=WpIdentifier('lwd_nmr_ternary_k') processing_time=0.181\n", "2025-08-01T14:13:03.720953Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.32, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-08-01T14:13:03.738819Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.32, 'cpu_percent': 0.0}\n", "2025-08-01T14:13:03.747547Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.32, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-08-01T14:13:04.121079Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.32, 'cpu_percent': 0.0}\n", "2025-08-01T14:13:04.128643Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.32, 'cpu_percent': 0.0}\n", "2025-08-01T14:13:04.319658Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 444.38, 'cpu_percent': 0.0} file_path=lwd_nmr_ternary.wp.xlsx processing_time=0.815 project_name=WpIdentifier('lwd_nmr_ternary')\n", "✅LWD数据已保存: lwd_nmr_ternary.wp.xlsx\n"]}], "source": ["if project is not None:\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "    from logwp.models.well_project import WpWellProject\n", "\n", "\n", "    try:\n", "\n", "        # WL数据\n", "        nmr_ternary_ds = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_ternary\",\n", "            curve_names=['VMICRO','VMESO','VMACRO'],\n", "            query_condition=\"DS_F == 1\"\n", "        )\n", "        project.add_dataset(\"nmr_ternary\", nmr_ternary_ds)\n", "        nmr_ternary_ds = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_ternary\",\n", "            curve_names=[],\n", "            new_dataset_name=\"nmr_ternary_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "        project.add_dataset(\"nmr_ternary_cleaned\", nmr_ternary_ds)\n", "\n", "        #LWD数据\n", "        lwd_nmr_ternary_ds = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"lwd_nmr_ternary\",\n", "            curve_names=['VMICRO_LWD','VMESO_LWD','VMACRO_LWD'],\n", "            query_condition=\"DS_F == 1\"\n", "        )\n", "        project.add_dataset(\"lwd_nmr_ternary\", lwd_nmr_ternary_ds)\n", "        lwd_nmr_ternary_ds = project.dropna_dataset(\n", "            source_dataset_name=\"lwd_nmr_ternary\",\n", "            curve_names=[],\n", "            new_dataset_name=\"lwd_nmr_ternary_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "        project.add_dataset(\"lwd_nmr_ternary_cleaned\", lwd_nmr_ternary_ds)\n", "\n", "        # WL数据合并\n", "        merge_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"nmr_ternary_cleaned\",\n", "            left_curves=['VMICRO','VMESO','VMACRO'],\n", "            right_dataset=\"K_Label\",\n", "            right_curves=k_label_curves,\n", "            interpolation_method = \"nearest\",\n", "            max_interpolation_distance=0.075, # 采样间隔的一半\n", "            new_dataset_name=\"nmr_ternary_k\"\n", "        )\n", "\n", "        # LWD数据合并\n", "        lwd_merge_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"lwd_nmr_ternary_cleaned\",\n", "            left_curves=['VMICRO_LWD','VMESO_LWD','VMACRO_LWD'],\n", "            right_dataset=\"K_Label\",\n", "            right_curves=k_label_curves,\n", "            interpolation_method = \"nearest\",\n", "            max_interpolation_distance=0.075, # 采样间隔的一半\n", "            new_dataset_name=\"lwd_nmr_ternary_k\"\n", "        )\n", "\n", "\n", "        # 保存WL\n", "        temp_project = WpWellProject(name=\"nmr_ternary\")\n", "        temp_project.add_dataset(\"nmr_ternary\", merge_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        out_path = \"nmr_ternary.wp.xlsx\"\n", "        writer.write(temp_project, out_path, apply_formatting=True)\n", "\n", "        print(f\"✅WL数据已保存: {out_path}\")\n", "\n", "        # 保存LWD\n", "        temp_project = WpWellProject(name=\"lwd_nmr_ternary\")\n", "        temp_project.add_dataset(\"lwd_nmr_ternary\", lwd_merge_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        out_path = \"lwd_nmr_ternary.wp.xlsx\"\n", "        writer.write(temp_project, out_path, apply_formatting=True)\n", "\n", "        print(f\"✅LWD数据已保存: {out_path}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过数据集提取\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}