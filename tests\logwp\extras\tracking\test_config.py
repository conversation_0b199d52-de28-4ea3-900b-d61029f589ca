"""测试配置模型的功能。

测试BaseRunConfig和TrackingConfig的功能，包括：
- 配置模型的创建和验证
- 序列化和反序列化
- 字段验证和约束
- 继承和扩展
"""

from __future__ import annotations

import json
from typing import Any, Dict

import pytest
from pydantic import ValidationError

from logwp.extras.tracking import BaseRunConfig, TrackingConfig


class TestBaseRunConfig:
    """测试BaseRunConfig的功能。"""

    def test_basic_creation(self):
        """测试基本配置创建。"""
        config = BaseRunConfig(
            project_name="SCAPE",
            run_name="test_run"
        )
        
        assert config.project_name == "SCAPE"
        assert config.run_name == "test_run"
        assert config.description is None
        assert config.tags == []

    def test_full_creation(self):
        """测试完整配置创建。"""
        config = BaseRunConfig(
            project_name="SCAPE",
            run_name="test_run",
            description="测试运行配置",
            tags=["test", "tracking", "baseline"]
        )
        
        assert config.project_name == "SCAPE"
        assert config.run_name == "test_run"
        assert config.description == "测试运行配置"
        assert config.tags == ["test", "tracking", "baseline"]

    def test_field_validation(self):
        """测试字段验证。"""
        # 测试必需字段
        with pytest.raises(ValidationError):
            BaseRunConfig()  # 缺少必需字段

        with pytest.raises(ValidationError):
            BaseRunConfig(project_name="")  # 空字符串

        with pytest.raises(ValidationError):
            BaseRunConfig(
                project_name="SCAPE",
                run_name=""  # 空字符串
            )

        # 测试字符串长度限制
        with pytest.raises(ValidationError):
            BaseRunConfig(
                project_name="A" * 101,  # 超过最大长度
                run_name="test"
            )

        with pytest.raises(ValidationError):
            BaseRunConfig(
                project_name="SCAPE",
                run_name="A" * 201  # 超过最大长度
            )

        with pytest.raises(ValidationError):
            BaseRunConfig(
                project_name="SCAPE",
                run_name="test",
                description="A" * 1001  # 超过最大长度
            )

    def test_string_strip_whitespace(self):
        """测试字符串空白字符自动去除。"""
        config = BaseRunConfig(
            project_name="  SCAPE  ",
            run_name="  test_run  ",
            description="  测试描述  "
        )
        
        assert config.project_name == "SCAPE"
        assert config.run_name == "test_run"
        assert config.description == "测试描述"

    def test_extra_fields_allowed(self):
        """测试允许额外字段。"""
        config = BaseRunConfig(
            project_name="SCAPE",
            run_name="test_run",
            custom_field="custom_value",  # 额外字段
            algorithm="SWIFT-PSO"  # 额外字段
        )
        
        assert config.project_name == "SCAPE"
        assert config.run_name == "test_run"
        # 额外字段应该可以通过model_dump访问
        config_dict = config.model_dump()
        assert config_dict["custom_field"] == "custom_value"
        assert config_dict["algorithm"] == "SWIFT-PSO"

    def test_to_dict(self, base_run_config: BaseRunConfig):
        """测试转换为字典。"""
        config_dict = base_run_config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert config_dict["project_name"] == base_run_config.project_name
        assert config_dict["run_name"] == base_run_config.run_name
        assert config_dict["description"] == base_run_config.description
        assert config_dict["tags"] == base_run_config.tags

    def test_to_json(self, base_run_config: BaseRunConfig):
        """测试转换为JSON。"""
        json_str = base_run_config.to_json()
        
        assert isinstance(json_str, str)
        # 验证可以解析为有效JSON
        parsed = json.loads(json_str)
        assert parsed["project_name"] == base_run_config.project_name

    def test_from_dict(self):
        """测试从字典创建配置。"""
        data = {
            "project_name": "SCAPE",
            "run_name": "test_run",
            "description": "测试配置",
            "tags": ["test"]
        }
        
        config = BaseRunConfig.from_dict(data)
        
        assert config.project_name == "SCAPE"
        assert config.run_name == "test_run"
        assert config.description == "测试配置"
        assert config.tags == ["test"]

    def test_from_json(self):
        """测试从JSON创建配置。"""
        json_data = {
            "project_name": "SCAPE",
            "run_name": "test_run",
            "description": "测试配置",
            "tags": ["test", "json"]
        }
        json_str = json.dumps(json_data)
        
        config = BaseRunConfig.from_json(json_str)
        
        assert config.project_name == "SCAPE"
        assert config.run_name == "test_run"
        assert config.description == "测试配置"
        assert config.tags == ["test", "json"]

    def test_inheritance(self):
        """测试配置继承。"""
        class CustomConfig(BaseRunConfig):
            algorithm: str = "SWIFT-PSO"
            learning_rate: float = 0.01
            bootstrap_iterations: int = 20

        config = CustomConfig(
            project_name="SCAPE",
            run_name="custom_test",
            algorithm="FOSTER-NMR",
            learning_rate=0.005
        )
        
        assert config.project_name == "SCAPE"
        assert config.run_name == "custom_test"
        assert config.algorithm == "FOSTER-NMR"
        assert config.learning_rate == 0.005
        assert config.bootstrap_iterations == 20  # 默认值


class TestTrackingConfig:
    """测试TrackingConfig的功能。"""

    def test_default_creation(self):
        """测试默认配置创建。"""
        config = TrackingConfig()
        
        assert config.manifest_filename == "manifest.json"
        assert config.config_snapshot_filename == "config_snapshot.yaml"
        assert config.auto_create_dirs is True
        assert config.compress_artifacts is False
        assert config.max_artifact_size_mb == 500
        assert config.enable_file_hashing is True
        assert config.hash_algorithm == "sha256"

    def test_custom_creation(self):
        """测试自定义配置创建。"""
        config = TrackingConfig(
            manifest_filename="custom_manifest.json",
            config_snapshot_filename="custom_config.yaml",
            auto_create_dirs=False,
            compress_artifacts=True,
            max_artifact_size_mb=1000,
            enable_file_hashing=False,
            hash_algorithm="md5"
        )
        
        assert config.manifest_filename == "custom_manifest.json"
        assert config.config_snapshot_filename == "custom_config.yaml"
        assert config.auto_create_dirs is False
        assert config.compress_artifacts is True
        assert config.max_artifact_size_mb == 1000
        assert config.enable_file_hashing is False
        assert config.hash_algorithm == "md5"

    def test_validation(self):
        """测试配置验证。"""
        # 测试最大文件大小必须大于0
        with pytest.raises(ValidationError):
            TrackingConfig(max_artifact_size_mb=0)

        with pytest.raises(ValidationError):
            TrackingConfig(max_artifact_size_mb=-100)

    def test_extra_fields_forbidden(self):
        """测试禁止额外字段。"""
        with pytest.raises(ValidationError):
            TrackingConfig(
                manifest_filename="test.json",
                unknown_field="value"  # 不允许的额外字段
            )

    def test_string_strip_whitespace(self):
        """测试字符串空白字符自动去除。"""
        config = TrackingConfig(
            manifest_filename="  manifest.json  ",
            config_snapshot_filename="  config.yaml  ",
            hash_algorithm="  sha256  "
        )
        
        assert config.manifest_filename == "manifest.json"
        assert config.config_snapshot_filename == "config.yaml"
        assert config.hash_algorithm == "sha256"

    def test_validate_assignment(self):
        """测试赋值时验证。"""
        config = TrackingConfig()
        
        # 正常赋值
        config.max_artifact_size_mb = 1000
        assert config.max_artifact_size_mb == 1000
        
        # 无效赋值应该抛出异常
        with pytest.raises(ValidationError):
            config.max_artifact_size_mb = -1
