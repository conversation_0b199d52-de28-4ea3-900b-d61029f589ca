from __future__ import annotations

from pydantic import BaseModel, Field, PositiveInt


class ObmiqBaselinesTrainingConfig(BaseModel):
    """OBMIQ Baselines 训练步骤的流程控制配置。"""

    random_seed: int = Field(2025, description="全局随机种子")

    # Nested CV
    inner_cv_folds: PositiveInt = Field(5, description="内层交叉验证的折数")

    # Randomized Search
    n_iter_random_search: PositiveInt = Field(50, description="随机搜索的迭代次数")

    device: str = Field(
        "cuda",
        description="计算设备，可以是 'cpu' 或 'cuda' (如果可用)。",
        pattern="^(cpu|cuda)$",
    )


class ObmiqBaselinesPredictionConfig(BaseModel):
    """预测步骤配置，目前为空，为未来扩展保留。"""

    pass
