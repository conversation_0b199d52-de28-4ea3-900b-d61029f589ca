"""
提供用于生成模型预测区间的工具。
"""
from __future__ import annotations

from typing import Any, Tuple
import pandas as pd
import numpy as np

try:
    from mapie.regression import MapieRegressor
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class ConformalPredictor:
    """
    一个使用保形预测 (Conformal Prediction) 为任何scikit-learn兼容回归模型
    生成预测区间的封装器。

    保形预测是一种模型无关的技术，它能提供具有理论覆盖率保证的预测区间。
    例如，一个95%的预测区间意味着，在未来的新数据上，真实值有95%的概率
    会落在这个区间内。

    此封装器使用了 `mapie` 库作为后端。

    Attributes:
        base_estimator: 用户提供的基础回归模型。
        mapie_regressor_: 内部使用的 `mapie` 回归器实例。
    """

    def __init__(self, base_estimator: Any):
        """
        初始化 ConformalPredictor。

        Args:
            base_estimator: 一个scikit-learn兼容的回归模型实例
                            （例如, RandomForestRegressor(), XGBRegressor()）。
        """
        if not SKLEARN_AVAILABLE:
            raise ImportError("此功能需要 'scikit-learn' 和 'mapie' 库。请运行 'pip install scikit-learn mapie' 进行安装。")
        self.base_estimator = base_estimator
        self.mapie_regressor_: MapieRegressor | None = None

    def fit(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_calib: pd.DataFrame,
        y_calib: pd.Series
    ) -> "ConformalPredictor":
        """
        训练基础模型并校准预测区间。

        Args:
            X_train (pd.DataFrame): 用于训练基础模型的特征数据。
            y_train (pd.Series): 用于训练基础模型的目标数据。
            X_calib (pd.DataFrame): 用于校准预测区间的特征数据。
            y_calib (pd.Series): 用于校准预测区间的目标数据。

        Returns:
            ConformalPredictor: 拟合好的自身实例。
        """
        # 使用 'cv="prefit"' 策略，先在训练集上训练基础模型
        self.base_estimator.fit(X_train, y_train)

        # 然后使用校准集来拟合MapieRegressor，以学习如何生成区间
        self.mapie_regressor_ = MapieRegressor(self.base_estimator, cv="prefit")
        self.mapie_regressor_.fit(X_calib, y_calib)

        return self

    def predict(
        self,
        X_test: pd.DataFrame,
        alpha: float = 0.05
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        对新数据进行预测，并返回点预测和预测区间。

        Args:
            X_test (pd.DataFrame): 需要预测的特征数据。
            alpha (float, optional): 显著性水平，用于定义置信度。
                                     alpha=0.05 对应 95% 的置信区间。默认为 0.05。

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: 一个元组 (y_pred, y_lower, y_upper)，
            分别包含点预测值、预测区间的下界和上界。
        """
        if self.mapie_regressor_ is None:
            raise RuntimeError("必须在 predict 之前调用 fit 方法。")

        y_pred, y_pis = self.mapie_regressor_.predict(X_test, alpha=alpha)

        # y_pis 是一个 (n_samples, 2, 1) 的数组，我们需要提取下界和上界
        y_lower = y_pis[:, 0, 0]
        y_upper = y_pis[:, 1, 0]

        return y_pred, y_lower, y_upper


class QuantilePredictor:
    """
    一个使用分位数回归 (Quantile Regression) 生成预测区间的封装器。

    分位数回归直接对目标值的条件分位数进行建模，而不是像普通回归那样
    对条件均值建模。通过分别训练预测低分位数（如0.05）和高分位数（如0.95）
    的模型，我们可以直接得到一个预测区间。

    此封装器要求用户提供三个独立的、已配置好用于不同分位数预测的模型实例。
    """

    def __init__(self, estimator_lower: Any, estimator_median: Any, estimator_upper: Any):
        """
        初始化 QuantilePredictor。

        Args:
            estimator_lower: 一个已配置好用于预测下分位数的scikit-learn兼容回归模型。
                             例如: `LGBMRegressor(objective='quantile', alpha=0.05)`
            estimator_median: 一个用于点预测（通常是中位数或均值）的模型。
                              例如: `LGBMRegressor(objective='quantile', alpha=0.5)`
            estimator_upper: 一个已配置好用于预测上分位数的模型。
                             例如: `LGBMRegressor(objective='quantile', alpha=0.95)`
        """
        if not SKLEARN_AVAILABLE:
            raise ImportError("此功能需要 'scikit-learn' 库。请运行 'pip install scikit-learn' 进行安装。")
        self.estimator_lower = estimator_lower
        self.estimator_median = estimator_median
        self.estimator_upper = estimator_upper

    def fit(self, X: pd.DataFrame, y: pd.Series) -> "QuantilePredictor":
        """
        分别训练用于下界、中值和上界预测的三个模型。

        Args:
            X (pd.DataFrame): 训练特征数据。
            y (pd.Series): 训练目标数据。

        Returns:
            QuantilePredictor: 拟合好的自身实例。
        """
        self.estimator_lower.fit(X, y)
        self.estimator_median.fit(X, y)
        self.estimator_upper.fit(X, y)
        return self

    def predict(self, X_test: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        对新数据进行预测，并返回点预测和预测区间。

        Args:
            X_test (pd.DataFrame): 需要预测的特征数据。

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: 一个元组 (y_pred, y_lower, y_upper)，
            分别包含点预测值、预测区间的下界和上界。
        """
        y_lower = self.estimator_lower.predict(X_test)
        y_pred = self.estimator_median.predict(X_test)
        y_upper = self.estimator_upper.predict(X_test)

        # 确保下界总是小于等于上界，以防模型交叉
        y_lower_final = np.minimum(y_lower, y_upper)
        y_upper_final = np.maximum(y_lower, y_upper)

        return y_pred, y_lower_final, y_upper_final
