"""
测井属性管理器（格式无关）。

实现6种属性类别（V、WP、DS、W、C、O）的分层查找机制，基于WFS规范设计但完全格式无关。

Architecture
------------
层次/依赖: attr层属性管理器，依赖constants层（格式无关常量）
设计原则: 格式无关、分层继承、类型安全、WFS规范兼容
性能特征: 高效查找、缓存优化、批量处理

属性类别系统：
- V（版本）：全局唯一版本属性，必须是第一个属性
- WP（工区）：工区全局属性，整个测井工区范围
- DS（数据集）：数据集级别属性，特定数据集范围
- W（井）：井级别属性，支持数据集继承逻辑
- C（曲线）：曲线级别属性，支持复杂继承机制
- O（其它）：其它属性，精确匹配查找，不受标准作用域限制

Classes:
    ExtAttributeManager: 测井属性管理器（格式无关）
    ExtAttributeRecord: 属性记录结构

Examples:
    >>> # 创建属性管理器
    >>> manager = ExtAttributeManager()
    >>>
    >>> # 添加属性
    >>> manager.add_attribute("V", None, None, None, "Version", "STR", None, "1.0")
    >>> manager.add_attribute("WP", None, None, None, "project_name", "STR", None, "Santos")
    >>>
    >>> # 查找属性
    >>> version = manager.get_version()
    >>> assert version == "1.0"
    >>>
    >>> # 作用域查找
    >>> value = manager.get_scoped_attribute_value("T2_AXIS", dataset="NMR_logs")

References:
    - 《SCAPE_WFS_WP文件规范.md》§5.3 - 属性查找机制（设计参考）
    - 《SCAPE_SAD_软件架构设计.md》FAP-1 - 格式无关原则
"""

from __future__ import annotations

from typing import Any, NamedTuple
from dataclasses import dataclass, field
from collections import defaultdict

from logwp.models.constants import WpExtAttributeCategory, WpStandardColumn, WpStatisticsKeys
from logwp.models.exceptions import WpAttributeError
from logwp.models.types import WpExtAttributeName, WpDatasetName, WpWellName, WpCurveName
from logwp.models.utils import CaseInsensitiveDict


class ExtAttributeRecord(NamedTuple):
    """属性记录结构（格式无关）。

    Architecture
    ------------
    层次/依赖: 属性记录封装，轻量级数据容器
    设计原则: 不可变数据、类型安全、格式无关
    性能特征: 轻量级NamedTuple，创建开销极小
    """
    category: WpExtAttributeCategory
    """属性类别（V、WP、DS、W、C、O）"""

    dataset: WpDatasetName | None
    """数据集作用域"""

    well: WpWellName | None
    """井作用域"""

    curve: WpCurveName | None
    """曲线作用域"""

    attribute: WpExtAttributeName
    """属性名称"""

    data_type: str
    """数据类型（INT、FLOAT、STR、BOOL、COMP）"""

    unit: str | None
    """单位"""

    value: Any
    """属性值"""

    description: str | None = None
    """描述信息"""

    def to_summary_dict(self) -> dict[str, Any]:
        """转换为概况字典格式，用于报告生成。

        Returns:
            dict[str, Any]: 包含所有字段的字典

        Examples:
            >>> record = ExtAttributeRecord(...)
            >>> summary = record.to_summary_dict()
            >>> assert "category" in summary
            >>> assert "attribute" in summary
        """
        return {
            "category": self.category.value,
            "dataset": str(self.dataset) if self.dataset is not None else None,
            "well": str(self.well) if self.well is not None else None,
            "curve": str(self.curve) if self.curve is not None else None,
            "attribute": str(self.attribute),
            "data_type": self.data_type,
            "unit": self.unit,
            "value": self._format_value_for_summary(),
            "description": self.description
        }

    def _format_value_for_summary(self) -> str:
        """格式化属性值用于概况显示。

        Returns:
            str: 格式化后的值字符串
        """
        if self.value is None:
            return "None"

        # 对于复杂对象，尝试转换为简洁的字符串表示
        if self.data_type == "COMP":
            if isinstance(self.value, dict):
                # 对于字典类型，显示键的数量
                return f"{{...}} ({len(self.value)} keys)"
            elif isinstance(self.value, list):
                # 对于列表类型，显示元素数量
                return f"[...] ({len(self.value)} items)"
            else:
                # 其他复杂对象，显示类型
                return f"{type(self.value).__name__} object"

        # 对于字符串，如果太长则截断
        value_str = str(self.value)
        if len(value_str) > 50:
            return value_str[:47] + "..."

        return value_str





@dataclass
class ExtAttributeManager:
    """测井属性管理器（格式无关）。

    实现6种属性类别分层查找机制，基于WFS规范设计但完全格式无关。

    Architecture
    ------------
    层次/依赖: attr层属性管理器，使用格式无关常量
    设计原则: 格式无关、分层继承、类型安全
    性能特征: 高效查找、索引优化、缓存友好

    Attributes:
        records: 属性记录列表
        category_index: 按类别索引的属性记录

    Examples:
        >>> manager = ExtAttributeManager()
        >>>
        >>> # 添加版本属性（全局唯一）
        >>> manager.add_attribute("V", None, None, None, "Version", "STR", None, "1.0")
        >>>
        >>> # 添加工区属性
        >>> manager.add_attribute("WP", None, None, None, "project_name", "STR", None, "Santos")
        >>>
        >>> # 添加数据集属性
        >>> manager.add_attribute("DS", "NMR_logs", None, None, "T2_AXIS", "COMP", "ms", t2_config)
        >>>
        >>> # 查找属性
        >>> version = manager.get_version()
        >>> assert version == "1.0"
        >>>
        >>> # 作用域查找
        >>> value = manager.get_scoped_attribute_value("T2_AXIS", dataset="NMR_logs")

    References:
        《SCAPE_WFS_WP文件规范.md》§5.3 - 属性查找机制（设计参考）
    """

    records: list[ExtAttributeRecord] = field(default_factory=list)
    category_index: defaultdict[WpExtAttributeCategory, list[ExtAttributeRecord]] = field(default_factory=lambda: defaultdict(list))

    def add_attribute(
        self,
        category: str | WpExtAttributeCategory,
        dataset: WpDatasetName | None,
        well: WpWellName | None,
        curve: WpCurveName | None,
        attribute: WpExtAttributeName,
        data_type: str,
        unit: str | None,
        value: Any,
        description: str | None = None
    ) -> None:
        """添加属性记录（格式无关）。

        Args:
            category: 属性类别（V、WP、DS、W、C、O）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）
            attribute: 属性名称
            data_type: 数据类型
            unit: 单位（可选）
            value: 属性值
            description: 描述信息（可选）

        Raises:
            WpAttributeError: 属性定义无效

        Examples:
            >>> manager.add_attribute("V", None, None, None, "Version", "STR", None, "1.0")
            >>> manager.add_attribute("DS", "NMR_logs", None, None, "T2_AXIS", "COMP", "ms", config)
        """
        # 转换类别为枚举
        if isinstance(category, str):
            try:
                category_enum = WpExtAttributeCategory(category)
            except ValueError:
                raise WpAttributeError(f"无效的属性类别: {category}")
        else:
            category_enum = category

        # 验证属性定义
        self._validate_attribute_definition(category_enum, dataset, well, curve, attribute)

        # 创建属性记录
        record = ExtAttributeRecord(
            category=category_enum,
            dataset=dataset,
            well=well,
            curve=curve,
            attribute=attribute,
            data_type=data_type,
            unit=unit,
            value=value,
            description=description
        )

        # 添加到记录列表和索引
        self.records.append(record)
        self.category_index[category_enum].append(record)

    def _validate_attribute_definition(
        self,
        category: WpExtAttributeCategory,
        dataset: WpDatasetName | None,
        well: WpWellName | None,
        curve: WpCurveName | None,
        attribute: WpExtAttributeName
    ) -> None:
        """验证属性定义的有效性。

        Args:
            category: 属性类别
            dataset: 数据集作用域
            well: 井作用域
            curve: 曲线作用域
            attribute: 属性名称

        Raises:
            WpAttributeError: 属性定义无效
        """
        # 版本属性验证
        if category == WpExtAttributeCategory.VERSION:
            # 版本属性由io验证
            pass

        # 工区属性验证
        elif category == WpExtAttributeCategory.WORKAREA:
            if any([dataset, well, curve]):
                raise WpAttributeError("工区属性不能有作用域限制")

        # 数据集属性验证
        elif category == WpExtAttributeCategory.DATASET:
            if not dataset:
                raise WpAttributeError("数据集属性必须指定数据集作用域")
            if any([well, curve]):
                raise WpAttributeError("数据集属性不能有井或曲线作用域")

        # 井属性验证
        elif category == WpExtAttributeCategory.WELL:
            if not well:
                raise WpAttributeError("井属性必须指定井作用域")
            if curve:
                raise WpAttributeError("井属性不能有曲线作用域")

        # 曲线属性验证
        elif category == WpExtAttributeCategory.CURVE:
            if not curve:
                raise WpAttributeError("曲线属性必须指定曲线作用域")

        # 其它属性无特殊验证要求

    def get_version(self) -> str | None:
        """获取版本号（V类别专用）。

        只针对V类别，返回属性名为Version的属性值。

        Returns:
            str | None: 版本号字符串，未找到时返回None

        Examples:
            >>> version = manager.get_version()
            >>> assert version == "1.0"
        """
        for record in self.category_index[WpExtAttributeCategory.VERSION]:
            if record.attribute.strip().upper() == WpStandardColumn.VERSION.upper():
                return record.value
        return None

    def find_scoped_attributes(
        self,
        attribute: WpExtAttributeName | None = None,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> list[ExtAttributeRecord]:
        """查找作用域属性（WP、DS、W、C四节）。

        实现分层查找机制或作用域属性查找机制。

        Args:
            attribute: 属性名称（可选）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            list[ExtAttributeRecord]: 匹配的属性记录列表

        Examples:
            >>> # 查找指定属性
            >>> records = manager.find_scoped_attributes("T2_AXIS", dataset="NMR_logs")
            >>>
            >>> # 查找指定作用域的所有属性
            >>> records = manager.find_scoped_attributes(dataset="NMR_logs", well="C-1")
        """
        results = []

        # 按优先级顺序查找：C -> W -> DS -> WP
        if curve:
            # 曲线作用域查找
            curve_records = self._find_curve_scoped_attributes(attribute, dataset, well, curve)
            results.extend(curve_records)
            if attribute and curve_records:  # 如果指定了属性名且找到了，直接返回
                return results

        if well:
            # 井作用域查找
            well_records = self._find_well_scoped_attributes(attribute, dataset, well)
            results.extend(well_records)
            if attribute and well_records:  # 如果指定了属性名且找到了，直接返回
                return results

        if dataset:
            # 数据集作用域查找
            dataset_records = self._find_dataset_scoped_attributes(attribute, dataset)
            results.extend(dataset_records)
            if attribute and dataset_records:  # 如果指定了属性名且找到了，直接返回
                return results

        # 工区全局属性查找
        workarea_records = self._find_workarea_scoped_attributes(attribute)
        results.extend(workarea_records)

        return results

    def get_scoped_attribute_value(
        self,
        attribute: WpExtAttributeName,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> Any:
        """获取作用域属性值（便捷方法）。

        Args:
            attribute: 属性名称（必需）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            Any: 属性值，未找到时返回None

        Examples:
            >>> value = manager.get_scoped_attribute_value("T2_AXIS", dataset="NMR_logs")
        """
        records = self.find_scoped_attributes(attribute, dataset=dataset, well=well, curve=curve)
        return records[0].value if records else None

    def find_attributes_in_category(
        self,
        category: str | WpExtAttributeCategory,
        attribute: WpExtAttributeName | None = None,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> list[ExtAttributeRecord]:
        """在指定类别下精确匹配属性。

        Args:
            category: 属性类别（V、WP、DS、W、C、O）
            attribute: 属性名称（可选）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            list[ExtAttributeRecord]: 匹配的属性记录列表

        Examples:
            >>> records = manager.find_attributes_in_category("DS", "T2_AXIS", dataset="NMR_logs")
        """
        # 转换类别为枚举
        if isinstance(category, str):
            try:
                category_enum = WpExtAttributeCategory(category.strip().upper())
            except ValueError:
                return []
        else:
            category_enum = category

        results = []
        for record in self.category_index[category_enum]:
            # 只对指定的参数进行精确匹配，未指定的参数忽略
            match = True

            # 属性名匹配（如果指定）
            if attribute is not None:
                if record.attribute.strip().upper() != attribute.strip().upper():
                    match = False

            # 数据集匹配（如果指定）
            if match and dataset is not None:
                if record.dataset is None or record.dataset.strip().upper() != dataset.strip().upper():
                    match = False

            # 井名匹配（如果指定）
            if match and well is not None:
                if record.well is None or record.well.strip().upper() != well.strip().upper():
                    match = False

            # 曲线名匹配（如果指定）
            if match and curve is not None:
                if record.curve is None or record.curve.strip().upper() != curve.strip().upper():
                    match = False

            if match:
                results.append(record)

        return results

    def get_all_category_attributes(
        self,
        category: str | WpExtAttributeCategory
    ) -> list[ExtAttributeRecord]:
        """获取指定类别的所有属性。

        Args:
            category: 属性类别（V、WP、DS、W、C、O）

        Returns:
            list[ExtAttributeRecord]: 指定类别的所有属性记录

        Examples:
            >>> records = manager.get_all_category_attributes("DS")
        """
        # 转换类别为枚举
        if isinstance(category, str):
            try:
                category_enum = WpExtAttributeCategory(category.strip().upper())
            except ValueError:
                return []
        else:
            category_enum = category

        return list(self.category_index[category_enum])

    def find_other_attributes(
        self,
        attribute: WpExtAttributeName | None = None,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> list[ExtAttributeRecord]:
        """查找其它属性（O类别专用）。

        针对O类别进行精确匹配查找。

        Args:
            attribute: 属性名称（可选）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            list[ExtAttributeRecord]: 匹配的属性记录列表

        Examples:
            >>> records = manager.find_other_attributes("custom_attr", dataset="test")
            >>> all_other = manager.find_other_attributes()  # 获取所有O类别属性
        """
        results = []
        for record in self.category_index[WpExtAttributeCategory.OTHER]:
            if self._exact_match_record(record, attribute, dataset, well, curve):
                results.append(record)
        return results

    def get_other_attribute_value(
        self,
        attribute: WpExtAttributeName,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> Any:
        """获取其它属性值（便捷方法）。

        Args:
            attribute: 属性名称（必需）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            Any: 属性值，未找到时返回None

        Examples:
            >>> value = manager.get_other_attribute_value("custom_attr", dataset="test")
        """
        records = self.find_other_attributes(attribute, dataset=dataset, well=well, curve=curve)
        return records[0].value if records else None

    def _exact_match_record(
        self,
        record: ExtAttributeRecord,
        attribute: WpExtAttributeName | None,
        dataset: WpDatasetName | None,
        well: WpWellName | None,
        curve: WpCurveName | None
    ) -> bool:
        """检查记录是否与指定参数精确匹配（大小写不敏感）。

        Args:
            record: 属性记录
            attribute: 属性名称（可选）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            bool: 是否精确匹配
        """
        # 属性名匹配（大小写不敏感）
        if attribute is not None:
            if record.attribute.strip().upper() != attribute.strip().upper():
                return False

        # 数据集匹配（大小写不敏感）
        if dataset is not None:
            if record.dataset is None:
                return False
            if record.dataset.strip().upper() != dataset.strip().upper():
                return False
        else:
            if record.dataset is not None:
                return False

        # 井名匹配（大小写不敏感）
        if well is not None:
            if record.well is None:
                return False
            if record.well.strip().upper() != well.strip().upper():
                return False
        else:
            if record.well is not None:
                return False

        # 曲线名匹配（大小写不敏感）
        if curve is not None:
            if record.curve is None:
                return False
            if record.curve.strip().upper() != curve.strip().upper():
                return False
        else:
            if record.curve is not None:
                return False

        return True

    def _find_curve_scoped_attributes(
        self,
        attribute: WpExtAttributeName | None,
        dataset: WpDatasetName | None,
        well: WpWellName | None,
        curve: WpCurveName
    ) -> list[ExtAttributeRecord]:
        """查找曲线作用域属性。"""
        results = []
        for record in self.category_index[WpExtAttributeCategory.CURVE]:
            # 曲线名必须匹配
            if record.curve is None or record.curve.strip().upper() != curve.strip().upper():
                continue

            # 属性名匹配（如果指定）
            if attribute is not None:
                if record.attribute.strip().upper() != attribute.strip().upper():
                    continue

            # 按WFS规范的4种条件匹配
            # 条件1：仅指定curve_name（DATASET、WELL同时为空）
            if not dataset and not well:
                if not record.dataset and not record.well:
                    results.append(record)
                    if attribute:  # 如果指定了属性名，找到第一个就返回
                        break

            # 条件2：指定curve_name和dataset_name（WELL为空）
            elif dataset and not well:
                if (record.dataset and
                    record.dataset.strip().upper() == dataset.strip().upper() and
                    not record.well):
                    results.append(record)
                    if attribute:
                        break

            # 条件3：指定curve_name和well_name（DATASET为空）
            elif not dataset and well:
                if (not record.dataset and
                    record.well and
                    record.well.strip().upper() == well.strip().upper()):
                    results.append(record)
                    if attribute:
                        break

            # 条件4：同时指定curve_name、well_name和dataset_name
            elif dataset and well:
                if (record.dataset and
                    record.dataset.strip().upper() == dataset.strip().upper() and
                    record.well and
                    record.well.strip().upper() == well.strip().upper()):
                    results.append(record)
                    if attribute:
                        break

        return results

    def _find_well_scoped_attributes(
        self,
        attribute: WpExtAttributeName | None,
        dataset: WpDatasetName | None,
        well: WpWellName
    ) -> list[ExtAttributeRecord]:
        """查找井作用域属性。"""
        results = []
        for record in self.category_index[WpExtAttributeCategory.WELL]:
            # 井名必须匹配
            if record.well is None or record.well.strip().upper() != well.strip().upper():
                continue

            # 属性名匹配（如果指定）
            if attribute is not None:
                if record.attribute.strip().upper() != attribute.strip().upper():
                    continue

            # 按WFS规范的2种条件匹配
            # 条件1：仅指定well_name（DATASET为空）
            if not dataset:
                if not record.dataset:
                    results.append(record)
                    if attribute:
                        break

            # 条件2：同时指定dataset_name和well_name
            else:
                if (record.dataset and
                    record.dataset.strip().upper() == dataset.strip().upper()):
                    results.append(record)
                    if attribute:
                        break

        return results

    def _find_dataset_scoped_attributes(
        self,
        attribute: WpExtAttributeName | None,
        dataset: WpDatasetName
    ) -> list[ExtAttributeRecord]:
        """查找数据集作用域属性。"""
        results = []
        for record in self.category_index[WpExtAttributeCategory.DATASET]:
            # 数据集名必须匹配
            if record.dataset is None or record.dataset.strip().upper() != dataset.strip().upper():
                continue

            # 属性名匹配（如果指定）
            if attribute is not None:
                if record.attribute.strip().upper() != attribute.strip().upper():
                    continue

            results.append(record)
            if attribute:  # 如果指定了属性名，找到第一个就返回
                break

        return results

    def _find_workarea_scoped_attributes(
        self,
        attribute: WpExtAttributeName | None
    ) -> list[ExtAttributeRecord]:
        """查找工区作用域属性。"""
        results = []
        for record in self.category_index[WpExtAttributeCategory.WORKAREA]:
            # 属性名匹配（如果指定）
            if attribute is not None:
                if record.attribute.strip().upper() != attribute.strip().upper():
                    continue

            results.append(record)
            if attribute:  # 如果指定了属性名，找到第一个就返回
                break

        return results

    def clear(self) -> None:
        """清空所有属性。

        Examples:
            >>> manager.clear()
            >>> assert len(manager.records) == 0
        """
        self.records.clear()
        self.category_index.clear()



