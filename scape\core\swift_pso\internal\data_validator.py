"""scape.core.swift_pso.internal.data_validator - 数据完整性校验

提供SWIFT-PSO步骤所需的数据完整性校验工具函数。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部工具层
设计原则: 工具函数、异常安全、数据质量保证
性能特征: 快速检查、结构化错误信息

遵循CCG规范：
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 迁移自 scape/core/swift_pso/internal/backend_utils.py
"""

from __future__ import annotations

from logwp.infra import get_logger

logger = get_logger(__name__)


def validate_data_integrity(bundle: any, required_curves: list[str]) -> None:
    """验证输入数据的完整性。

    检查输入数据bundle中的核心曲线是否存在NaN值，确保数据质量。

    Args:
        bundle: 输入数据bundle
        required_curves: 必需的曲线名称列表

    Raises:
        WpDataError: 数据包含NaN值或缺少必需曲线时抛出

    Note:
        此函数是facade层前置检查的重要组成部分，防止无效数据进入计算流程。

    References:
        迁移自 scape/core/swift_pso_backup/pso_experiment.py 中的数据完整性检查逻辑
    """
    from logwp.models.exceptions import WpDataError, ErrorContext

    logger.debug(
        "开始数据完整性检查",
        operation="data_integrity_check",
        required_curves=required_curves,
        bundle_name=getattr(bundle, 'name', 'unknown')
    )

    try:
        # 检查必需曲线是否存在
        # 此方法在验证失败时会抛出 WpDataError
        bundle.validate_required_curves(required_curves=required_curves)

    except WpDataError as e:
        # 重新包装异常，提供更具体的上下文信息
        raise WpDataError(
            f"输入数据Bundle '{getattr(bundle, 'name', 'unknown')}' 的核心曲线不满足要求。 "
            f"请在运行SWIFT-PSO前清理数据。详细信息: {e}",
            context=e.context
        ) from e
