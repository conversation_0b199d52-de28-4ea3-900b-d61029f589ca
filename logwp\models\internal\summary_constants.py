"""数据概况生成相关常量定义。

定义数据概况生成过程中使用的所有字典键名、字段名等常量，
避免硬编码字符串，提高代码维护性和类型安全性。

Architecture
------------
层次/依赖: models/internal常量层
设计原则: 常量集中管理、类型安全、避免硬编码
使用范围: 数据概况生成服务内部

References:
    《SCAPE_CCG_编码与通用规范.md》§CT - 常量管理规范
"""

from __future__ import annotations


class SummaryKeys:
    """数据概况字典键名常量。"""

    # 顶层汇总键
    TOTAL_COUNT = "total_count"
    DATASETS = "datasets"
    ERROR = "error"

    # 井头信息键
    TOTAL_ATTRIBUTES = "total_attributes"
    BY_CATEGORY = "by_category"  # 已移除，保留用于向后兼容
    COMP_ATTRIBUTES = "comp_attributes"  # 已移除，保留用于向后兼容
    ATTRIBUTE_TYPES = "attribute_types"
    BY_WFS_CATEGORY = "by_wfs_category"
    ATTRIBUTE_RECORDS = "attribute_records"

    # 井名映射键
    TOTAL_MAPPINGS = "total_mappings"
    MAPPINGS_LIST = "mappings_list"
    UNIQUE_TARGETS = "unique_targets"  # 已移除，保留用于向后兼容
    TARGET_WELLS = "target_wells"  # 已移除，保留用于向后兼容
    MAPPING_STATISTICS = "mapping_statistics"  # 已移除，保留用于向后兼容
    SOURCE = "source"
    TARGET = "target"

    # 数据集基本信息键
    BASIC_INFO = "basic_info"
    NAME = "name"
    TYPE = "type"
    SAMPLING_INTERVAL = "sampling_interval"
    TOTAL_ROWS = "total_rows"
    TOTAL_COLUMNS = "total_columns"
    CURVE_COUNT = "curve_count"
    CREATED_AT = "created_at"
    MODIFIED_AT = "modified_at"

    # 数据集详细信息键
    METADATA_INFO = "metadata_info"
    DATAFRAME_INFO = "dataframe_info"
    WELL_STATISTICS = "well_statistics"
    CURVE_STATISTICS = "curve_statistics"

    # 曲线元数据键
    TOTAL_CURVES = "total_curves"
    BY_DIMENSION = "by_dimension"
    BY_DATA_TYPE = "by_data_type"
    BY_CURVE_CLASS = "by_curve_class"
    ONE_D_CURVES = "1d_curves"
    TWO_D_CURVES = "2d_curves"
    CATEGORICAL = "categorical"
    NORMAL = "normal"

    # DataFrame信息键
    COLUMNS = "columns"
    DTYPE = "dtype"
    NON_NULL_COUNT = "non_null_count"
    NULL_COUNT = "null_count"
    NULL_PERCENTAGE = "null_percentage"
    INDEX_TYPE = "index_type"
    MEMORY_USAGE = "memory_usage"
    SHAPE = "shape"

    # 按井统计键
    ROW_COUNT = "row_count"
    DEPTH_MIN = "depth_min"
    DEPTH_MAX = "depth_max"
    DEPTH_RANGE = "depth_range"
    DEPTH_POINTS = "depth_points"

    # 曲线统计键
    NUMERIC_CURVES = "numeric_curves"
    CATEGORICAL_CURVES = "categorical_curves"
    IDENTIFIER_CURVES = "identifier_curves"
    SUMMARY = "summary"
    NUMERIC_COUNT = "numeric_count"
    CATEGORICAL_COUNT = "categorical_count"
    IDENTIFIER_COUNT = "identifier_count"
    TOTAL_ANALYZED = "total_analyzed"

    # 数值统计键
    COUNT = "count"
    MISSING = "missing"
    VALID_COUNT = "valid_count"
    MIN = "min"
    MAX = "max"
    MEAN = "mean"
    MEDIAN = "median"
    STD = "std"
    Q25 = "q25"
    Q75 = "q75"
    SKEWNESS = "skewness"
    KURTOSIS = "kurtosis"
    OUTLIER_COUNT = "outlier_count"
    OUTLIER_PERCENTAGE = "outlier_percentage"

    # 类别统计键
    UNIQUE_VALUES = "unique_values"
    MODE = "mode"
    VALUE_DISTRIBUTION = "value_distribution"
    TOP_5_VALUES = "top_5_values"
    VALUE_PERCENTAGES = "value_percentages"

    # 标识符统计键
    COMPLETENESS = "completeness"
    MOST_COMMON = "most_common"
    MOST_COMMON_COUNT = "most_common_count"
    HAS_DUPLICATES = "has_duplicates"
    DUPLICATE_VALUES = "duplicate_values"

    # 映射统计键
    HAS_MAPPINGS = "has_mappings"
    ONE_TO_MANY = "one_to_many"
    MANY_TO_ONE = "many_to_one"
    ONE_TO_MANY_COUNT = "one_to_many_count"
    MANY_TO_ONE_COUNT = "many_to_one_count"


class ReportTemplates:
    """报告模板常量。"""

    # 文件扩展名
    MARKDOWN_EXT = ".md"
    DEFAULT_SUFFIX = "_data_summary"

    # 时间格式
    DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

    # 默认值
    NOT_AVAILABLE = "N/A"
    ERROR_PREFIX = "ERROR"

    # 表格分隔符
    TABLE_SEPARATOR = " | "
    TABLE_ARROW = " → "

    # 数值格式
    DEPTH_DECIMAL_PLACES = 2
    PERCENTAGE_DECIMAL_PLACES = 2


class LogMessages:
    """日志消息常量。"""

    # 操作标识
    OPERATION_GENERATE_SUMMARY = "generate_summary"
    OPERATION_COLLECT_DATASETS = "collect_datasets_summary"
    OPERATION_GENERATE_REPORT = "generate_markdown_report"

    # 成功消息
    MSG_SUMMARY_START = "开始生成数据概况报告"
    MSG_SUMMARY_COMPLETE = "数据概况报告生成完成"
    MSG_HEAD_SUMMARY_START = "开始生成井头信息概况"
    MSG_HEAD_SUMMARY_COMPLETE = "井头信息概况生成完成"
    MSG_WELLMAP_SUMMARY_START = "开始生成井名映射概况"
    MSG_WELLMAP_SUMMARY_COMPLETE = "井名映射概况生成完成"
    MSG_DATASET_SUMMARY_START = "开始生成数据集概况"
    MSG_DATASET_SUMMARY_COMPLETE = "数据集概况生成完成"
    MSG_CURVE_STATS_START = "开始生成曲线统计分析"
    MSG_CURVE_STATS_COMPLETE = "曲线统计分析完成"

    # 错误消息
    MSG_SUMMARY_FAILED = "生成数据概况报告失败"
    MSG_DATASET_SUMMARY_FAILED = "数据集概况生成失败"
    MSG_CURVE_STATS_FAILED = "曲线统计分析失败"
    MSG_WELL_STATS_FAILED = "按井统计生成失败"
    MSG_NUMERIC_STATS_FAILED = "数值型曲线统计失败"
    MSG_CATEGORICAL_STATS_FAILED = "类别型曲线统计失败"
    MSG_IDENTIFIER_STATS_FAILED = "标识型曲线统计失败"

    # 警告消息
    MSG_NO_WELL_CURVES = "未找到井名曲线"
    MSG_NO_DEPTH_CURVES = "未找到深度曲线"
    MSG_WELL_CURVE_NOT_FOUND = "井名曲线不存在于元数据中"
    MSG_WELL_COLUMN_NOT_FOUND = "井名列不存在于DataFrame中"
    MSG_NO_VALID_NUMERIC_DATA = "无有效数值数据"

    # 错误前缀
    ERROR_SUMMARY_FAILED = "概况生成失败"
    ERROR_STATS_FAILED = "统计失败"
    ERROR_NUMERIC_CALC_FAILED = "数值统计计算失败"
    ERROR_CATEGORICAL_CALC_FAILED = "类别统计计算失败"
    ERROR_IDENTIFIER_CALC_FAILED = "标识符统计计算失败"
