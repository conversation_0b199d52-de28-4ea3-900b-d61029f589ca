{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ 端到端工作流 (PyTorch版)\n", "\n", "缓解过拟合现象：调整超参数范围  和 引入权重衰减 。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-24T14:32:43.328708Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 127.8, 'cpu_percent': 0.0}\n", "2025-07-24T14:32:45.722310Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 713.45, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-24T14:32:45.744281Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 713.45, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-24T14:32:45.757748Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 713.45, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-24T14:32:46.548360Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_base_profile profile_name=obmiq.base\n", "2025-07-24T14:32:46.568043Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.training_history\n", "2025-07-24T14:32:46.578527Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.crossplot\n", "2025-07-24T14:32:46.595421Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_plot\n", "2025-07-24T14:32:46.635045Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_hist\n", "2025-07-24T14:32:46.656461Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.shap_summary\n", "2025-07-24T14:32:46.667568Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.captum_ig_summary\n", "2025-07-24T14:32:46.683206Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.88, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.grad_cam\n", "环境设置与导入完成。\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "from pathlib import Path\n", "\n", "# 导入 SCAPE 和 LOGWP 的核心组件\n", "from logwp.io import WpExcelReader\n", "from logwp.extras.tracking import RunContext\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "\n", "# 导入新版OBMIQ组件\n", "import scape.core.obmiq.plot_profiles # 导入以注册绘图模板\n", "from scape.core.obmiq import (\n", "    run_obmiq_training_step,\n", "    run_obmiq_prediction_step,\n", "    ObmiqTrainingConfig,\n", "    ObmiqPredictionConfig,\n", "    ObmiqTrainingArtifacts,\n", "    ObmiqArtifactHandler\n", ")\n", "from logwp.models.constants import WpDepthRole\n", "\n", "# 设置\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', 50)\n", "\n", "print(\"环境设置与导入完成。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据\n", "\n", "在此步骤中，我们从 `.wp.xlsx` 文件中加载训练和预测所需的数据集。\n", "\n", "**请注意**:\n", "- `train_bundle` 应包含所有用于训练的井（如 C-1, C-2）。\n", "- `prediction_bundle` 可以是训练井的一部分（用于验证），也可以是全新的盲井（如 T-1）。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:32:46.751096Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.9, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx\n", "2025-07-24T14:32:46.767519Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.22, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx file_size_mb=1.67 sheet_count=1\n", "2025-07-24T14:32:46.778888Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.23, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all\n", "2025-07-24T14:32:46.786383Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.24, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all\n", "2025-07-24T14:32:46.805528Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.54, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T14:32:46.821537Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 769.54, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=17 well_curves=1\n", "2025-07-24T14:32:48.106500Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 776.07, 'cpu_percent': 0.0} shape=(2667, 80) sheet_name=nmr_obmiq\n", "2025-07-24T14:32:48.140790Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 776.08, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq')\n", "2025-07-24T14:32:48.154795Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 776.08, 'cpu_percent': 0.0} curve_count=17 dataset_name=nmr_obmiq dataset_type=Continuous df_shape=(2667, 80) processing_time=1.354\n", "2025-07-24T14:32:48.178609Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 776.08, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-24T14:32:48.192935Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 776.08, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-24T14:32:48.201926Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 776.08, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx processing_time=1.451 project_name=WpIdentifier('santos_obmiq_cum_all')\n", "2025-07-24T14:32:48.216471Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 776.08, 'cpu_percent': 0.0} extracted_curve_count=15 operation=extract_curve_dataframe_bundle\n", "2025-07-24T14:32:48.257527Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.45, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['DEN', 'CN', 'RS_LOG10', 'DRES', 'RD_LOG10', 'PHI_T2_DIST_CUM', 'BVI_NMR', 'WELL_NO', 'BFV_NMR', 'DT', 'DT2_P50', 'PHIE_NMR', 'PHIT_NMR', 'SDR_PROXY', 'DPHIT_NMR', 'MD', 'SWB_NMR'] operation=extract_metadata output_curve_count=17 output_curves=['DEN', 'CN', 'RS_LOG10', 'DRES', 'RD_LOG10', 'PHI_T2_DIST_CUM', 'BVI_NMR', 'WELL_NO', 'BFV_NMR', 'DT', 'DT2_P50', 'PHIE_NMR', 'PHIT_NMR', 'SDR_PROXY', 'DPHIT_NMR', 'MD', 'SWB_NMR']\n", "2025-07-24T14:32:48.305353Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.45, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx\n", "2025-07-24T14:32:48.317456Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.46, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx file_size_mb=2.11 sheet_count=1\n", "2025-07-24T14:32:48.332449Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.46, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all_apply\n", "2025-07-24T14:32:48.339711Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.46, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all_apply\n", "2025-07-24T14:32:48.352300Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.5, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T14:32:48.364179Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.62, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=17 well_curves=1\n", "2025-07-24T14:32:50.634055Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 790.33, 'cpu_percent': 0.0} shape=(4598, 80) sheet_name=nmr_obmiq_apply\n", "2025-07-24T14:32:50.650761Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 790.33, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_apply')\n", "2025-07-24T14:32:50.659732Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 790.33, 'cpu_percent': 0.0} curve_count=17 dataset_name=nmr_obmiq_apply dataset_type=Continuous df_shape=(4598, 80) processing_time=2.313\n", "2025-07-24T14:32:50.676545Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 790.33, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-24T14:32:50.687030Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 790.33, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-24T14:32:50.698553Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 790.33, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=2.393 project_name=WpIdentifier('santos_obmiq_cum_all_apply')\n", "2025-07-24T14:32:50.712483Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 790.33, 'cpu_percent': 0.0} extracted_curve_count=15 operation=extract_curve_dataframe_bundle\n", "2025-07-24T14:32:50.729482Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 796.83, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['DEN', 'CN', 'RS_LOG10', 'DRES', 'RD_LOG10', 'PHI_T2_DIST_CUM', 'BVI_NMR', 'WELL_NO', 'BFV_NMR', 'DT', 'DT2_P50', 'PHIE_NMR', 'PHIT_NMR', 'SDR_PROXY', 'DPHIT_NMR', 'MD', 'SWB_NMR'] operation=extract_metadata output_curve_count=17 output_curves=['DEN', 'CN', 'RS_LOG10', 'DRES', 'RD_LOG10', 'PHI_T2_DIST_CUM', 'BVI_NMR', 'WELL_NO', 'BFV_NMR', 'DT', 'DT2_P50', 'PHIE_NMR', 'PHIT_NMR', 'SDR_PROXY', 'DPHIT_NMR', 'MD', 'SWB_NMR']\n", "数据加载成功。\n", "训练数据束: nmr_obmiq, 形状: (2667, 80), 井名: WELL_NO\n", "预测数据束: nmr_obmiq_apply, 形状: (4598, 80)\n", "T2时间轴长度: 64\n"]}], "source": ["# --- 请在此处填写您的数据路径 ---\n", "\n", "\n", "TRAIN_WP_FILE_PATH = \"santos_obmiq_cum_all.wp.xlsx\"\n", "TRAIN_DATASET_NAME = \"nmr_obmiq\"\n", "\n", "PRED_WP_FILE_PATH = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "PREDICTION_DATASET_NAME = \"nmr_obmiq_apply\"\n", "# ---------------------------------\n", "\n", "# 加载整个工区文件\n", "try:\n", "    # 获取训练和预测数据束\n", "    reader = WpExcelReader()\n", "    project = reader.read(TRAIN_WP_FILE_PATH)\n", "    train_ds = project.get_dataset(TRAIN_DATASET_NAME)\n", "    train_bundle = train_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "    well_no_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "    depth_name = train_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "    pred_reader = WpExcelReader()\n", "    pred_project = pred_reader.read(PRED_WP_FILE_PATH)\n", "    prediction_ds = pred_project.get_dataset(PREDICTION_DATASET_NAME)\n", "    prediction_bundle = prediction_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "\n", "    # 从 head_info 获取 T2 时间轴\n", "    # 假设所有井共享一个T2轴定义\n", "\n", "    t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "    t2_time_array = t2_axis_info.calculate_values()\n", "\n", "    print(\"数据加载成功。\")\n", "    print(f\"训练数据束: {train_ds.name}, 形状: {train_ds.df.shape}, 井名: {well_no_name}\")\n", "    print(f\"预测数据束: {prediction_ds.name}, 形状: {prediction_ds.df.shape}\")\n", "    print(f\"T2时间轴长度: {len(t2_time_array)}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 数据加载失败: {e}\")\n", "    project = None\n", "    pred_project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 运行 OBMIQ 工作流\n", "\n", "我们使用 `RunContext` 来包裹整个实验流程，以确保所有参数、指标和产物都被追踪。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:32:53.112235Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 796.84, 'cpu_percent': 0.0} operation=init_context run_dir=test\\obmiq_run_pytorch_20250724_223253 run_id=20250724-143253-5d864dfd\n", "--- 开始 OBMIQ 训练步骤 (PyTorch) ---\n", "2025-07-24T14:32:53.128984Z [info     ] ===== OBMIQ Training Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 796.85, 'cpu_percent': 0.0}\n", "2025-07-24T14:32:53.145602Z [info     ] 曲线名已成功解析为DataFrame列名。          [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 796.85, 'cpu_percent': 0.0}\n", "2025-07-24T14:32:53.154085Z [info     ] --- Stage 0: Saving Configuration Snapshot --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 796.85, 'cpu_percent': 0.0}\n", "2025-07-24T14:32:53.164593Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.configs.training_config_snapshot artifact_path=obmiq_training_pytorch\\training_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 796.86, 'cpu_percent': 0.0} description=Snapshot of the training configuration used for this run. operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:32:53.180638Z [info     ] --- Stage 2: Hyperparameter Tuning using LOWO-CV --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 796.86, 'cpu_percent': 0.0}\n", "2025-07-24T14:32:53.190846Z [info     ] Tuning procedure started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.04, 'cpu_percent': 0.0}\n", "2025-07-24T14:32:53.201818Z [info     ] --- Starting CV Fold 1/2 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 797.08, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-24 22:32:53,218] A new study created in memory with name: no-name-5b02140d-6f2d-42f8-951f-7a0831f6ac7f\n", "[I 2025-07-24 22:32:55,702] Trial 0 finished with value: -3.1108631690343223 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.5266758634007341, 'learning_rate': 0.00453235853007937, 'weight_decay': 0.00010750548665229003}. Best is trial 0 with value: -3.1108631690343223.\n", "[I 2025-07-24 22:32:57,356] Trial 1 finished with value: -0.3922964135805766 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.5274604677260942, 'learning_rate': 0.0005915588406710849, 'weight_decay': 7.760102189663687e-05}. Best is trial 0 with value: -3.1108631690343223.\n", "[I 2025-07-24 22:32:59,423] Trial 2 finished with value: -2.8709261417388916 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.4703015467321796, 'learning_rate': 0.0041015875031162686, 'weight_decay': 0.0002496552467762261}. Best is trial 0 with value: -3.1108631690343223.\n", "[I 2025-07-24 22:33:01,028] Trial 3 finished with value: -0.6000117560227712 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2553042664144085, 'learning_rate': 0.0008509958213326108, 'weight_decay': 2.084105363885116e-05}. Best is trial 0 with value: -3.1108631690343223.\n", "[I 2025-07-24 22:33:02,350] Trial 4 finished with value: -3.8316386540730796 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.2461101307023349, 'learning_rate': 0.005706857341560455, 'weight_decay': 0.00010240253664916098}. Best is trial 4 with value: -3.8316386540730796.\n", "[I 2025-07-24 22:33:04,135] Trial 5 finished with value: -4.9540267785390215 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.2126131183829525, 'learning_rate': 0.008580867270896173, 'weight_decay': 0.00018179148547009107}. Best is trial 5 with value: -4.9540267785390215.\n", "[I 2025-07-24 22:33:04,251] Trial 6 pruned. \n", "[I 2025-07-24 22:33:04,462] Trial 7 pruned. \n", "[I 2025-07-24 22:33:04,568] Trial 8 pruned. \n", "[I 2025-07-24 22:33:04,679] Trial 9 pruned. \n", "[I 2025-07-24 22:33:04,796] Trial 10 pruned. \n", "[I 2025-07-24 22:33:06,702] Trial 11 finished with value: -4.605644226074219 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.20651591469124025, 'learning_rate': 0.007823704213363654, 'weight_decay': 0.00022634307463086653}. Best is trial 5 with value: -4.9540267785390215.\n", "[I 2025-07-24 22:33:08,628] Trial 12 finished with value: -5.5660986105601 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.2024533593207251, 'learning_rate': 0.009766917139845187, 'weight_decay': 0.000246503874117811}. Best is trial 12 with value: -5.5660986105601.\n", "[I 2025-07-24 22:33:10,517] Trial 13 finished with value: -5.406639099121094 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.3254756594373091, 'learning_rate': 0.009902663185515937, 'weight_decay': 0.00027909181418849996}. Best is trial 12 with value: -5.5660986105601.\n", "[I 2025-07-24 22:33:10,634] Trial 14 pruned. \n", "[I 2025-07-24 22:33:10,745] Trial 15 pruned. \n", "[I 2025-07-24 22:33:10,864] Trial 16 pruned. \n", "[I 2025-07-24 22:33:12,796] Trial 17 finished with value: -5.293830235799153 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.3985756140962764, 'learning_rate': 0.009091885663403748, 'weight_decay': 5.0724056067879544e-05}. Best is trial 12 with value: -5.5660986105601.\n", "[I 2025-07-24 22:33:12,895] Trial 18 pruned. \n", "[I 2025-07-24 22:33:12,998] Trial 19 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:33:12.999351Z [info     ] Fold 1 best trial: value=-5.5661, params={'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.2024533593207251, 'learning_rate': 0.009766917139845187, 'weight_decay': 0.000246503874117811} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1384.01, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:13.009289Z [info     ] --- Performing blind test for Fold 1 on well 'C-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1384.01, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:14.920377Z [info     ] --- Starting CV Fold 2/2 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1387.79, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-24 22:33:14,935] A new study created in memory with name: no-name-741b2475-d6db-47e6-b444-33a120c00063\n", "[I 2025-07-24 22:33:16,379] Trial 0 finished with value: 0.06774557242169976 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.554410585904266, 'learning_rate': 0.00019497922390379922, 'weight_decay': 0.00025233914629820605}. Best is trial 0 with value: 0.06774557242169976.\n", "[I 2025-07-24 22:33:17,772] Trial 1 finished with value: 0.0710460664704442 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 8, 'dropout_rate': 0.5140174693493123, 'learning_rate': 0.00021056741871118513, 'weight_decay': 0.0002925239534655085}. Best is trial 0 with value: 0.06774557242169976.\n", "[I 2025-07-24 22:33:18,632] Trial 2 finished with value: 0.13178987428545952 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.5469039968145508, 'learning_rate': 0.00010240545939436656, 'weight_decay': 5.510920344760211e-05}. Best is trial 0 with value: 0.06774557242169976.\n", "[I 2025-07-24 22:33:19,408] Trial 3 finished with value: -0.6722568720579147 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.3162893339442612, 'learning_rate': 0.0016004301560370272, 'weight_decay': 9.281465995191339e-05}. Best is trial 3 with value: -0.6722568720579147.\n", "[I 2025-07-24 22:33:20,194] Trial 4 finished with value: 0.09321147296577692 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3296997022283351, 'learning_rate': 0.00014189135022174012, 'weight_decay': 0.0006857197102847907}. Best is trial 3 with value: -0.6722568720579147.\n", "[I 2025-07-24 22:33:21,073] Trial 5 finished with value: -1.2291728556156158 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.27272531346960527, 'learning_rate': 0.002889353362569131, 'weight_decay': 2.4445630971146612e-05}. Best is trial 5 with value: -1.2291728556156158.\n", "[I 2025-07-24 22:33:21,939] Trial 6 finished with value: -1.168241798877716 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.42855604494155497, 'learning_rate': 0.002548756720293042, 'weight_decay': 2.249857352446829e-05}. Best is trial 5 with value: -1.2291728556156158.\n", "[I 2025-07-24 22:33:22,013] Trial 7 pruned. \n", "[I 2025-07-24 22:33:23,365] Trial 8 finished with value: -0.218552153557539 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.3346529463143485, 'learning_rate': 0.0007119873216575877, 'weight_decay': 0.0008307573586029747}. Best is trial 5 with value: -1.2291728556156158.\n", "[I 2025-07-24 22:33:23,435] Trial 9 pruned. \n", "[I 2025-07-24 22:33:24,262] Trial 10 finished with value: -3.4788577556610107 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.20873431983280985, 'learning_rate': 0.009389592022540716, 'weight_decay': 1.644851155262224e-05}. Best is trial 10 with value: -3.4788577556610107.\n", "[I 2025-07-24 22:33:25,093] Trial 11 finished with value: -3.493079721927643 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.20902636522794973, 'learning_rate': 0.008600083066567996, 'weight_decay': 1.0863134066579933e-05}. Best is trial 11 with value: -3.493079721927643.\n", "[I 2025-07-24 22:33:26,191] Trial 12 finished with value: -3.5241622924804688 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.20668094950789087, 'learning_rate': 0.009553400036831706, 'weight_decay': 1.149820758508819e-05}. Best is trial 12 with value: -3.5241622924804688.\n", "[I 2025-07-24 22:33:27,627] Trial 13 finished with value: -3.677181124687195 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.22390534224426914, 'learning_rate': 0.009762592584993285, 'weight_decay': 1.021760362206924e-05}. Best is trial 13 with value: -3.677181124687195.\n", "[I 2025-07-24 22:33:29,080] Trial 14 finished with value: -1.9419970214366913 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.2575522579709567, 'learning_rate': 0.004417166564958152, 'weight_decay': 4.207304909637745e-05}. Best is trial 13 with value: -3.677181124687195.\n", "[I 2025-07-24 22:33:30,539] Trial 15 finished with value: -2.222674548625946 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.26095846142267093, 'learning_rate': 0.005065554414112536, 'weight_decay': 1.4307532658763365e-05}. Best is trial 13 with value: -3.677181124687195.\n", "[I 2025-07-24 22:33:32,000] Trial 16 finished with value: -2.3738457560539246 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.37784664181790645, 'learning_rate': 0.005514602854947451, 'weight_decay': 3.998994266578368e-05}. Best is trial 13 with value: -3.677181124687195.\n", "[I 2025-07-24 22:33:32,139] Trial 17 pruned. \n", "[I 2025-07-24 22:33:32,239] Trial 18 pruned. \n", "[I 2025-07-24 22:33:33,535] Trial 19 finished with value: -2.7684544920921326 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.5978505271829874, 'learning_rate': 0.007202434522335692, 'weight_decay': 8.001396886131107e-05}. Best is trial 13 with value: -3.677181124687195.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:33:33.536296Z [info     ] Fold 2 best trial: value=-3.6772, params={'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.22390534224426914, 'learning_rate': 0.009762592584993285, 'weight_decay': 1.021760362206924e-05} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1407.48, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:33.541637Z [info     ] --- Performing blind test for Fold 2 on well 'T-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1407.48, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.002067Z [info     ] Aggregating results from all CV folds... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1412.45, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.009448Z [info     ] 成功从交叉验证流程中收集到泛化能力评估数据。         [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.21, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.021752Z [info     ] Best hyperparameters found: {'cnn_filters': 8.0, 'cnn_kernel_size': 3.0, 'dropout_rate': 0.2024533593207251, 'learning_rate': 0.009766917139845187, 'mlp_units': 8.0, 'weight_decay': 0.000246503874117811} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.21, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.026111Z [info     ] --- Stage 2 Artifacts: Saving CV and Tuning Reports --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.21, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.053787Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.cv_performance artifact_path=obmiq_training_pytorch\\cv_performance_report.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.25, 'cpu_percent': 0.0} description=LOWO-CV中每一折的最佳验证损失和对应的超参数。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:35.112700Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.hyperparameter_tuning artifact_path=obmiq_training_pytorch\\hyperparameter_tuning_report.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.25, 'cpu_percent': 0.0} description=在所有CV折中聚合得到的全局最佳超参数组合。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:35.135818Z [info     ] --- Stage 2 Artifacts: Generating Generalization Evaluation Artifacts --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.25, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.152777Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.lowo_cv_performance_summary artifact_path=obmiq_training_pytorch\\lowo_cv_performance_summary.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.25, 'cpu_percent': 0.0} description=LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:35.166956Z [info     ] 成功生成并保存了泛化能力性能评估表: test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\lowo_cv_performance_summary.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1414.25, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.306809Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.lowo_cv_predictions artifact_path=obmiq_training_pytorch\\lowo_cv_predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1416.29, 'cpu_percent': 0.0} description=LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:35.323797Z [info     ] 成功保存了泛化能力评估的数据快照: test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\lowo_cv_predictions.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1416.29, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:35.381517Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1418.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T14:33:35.397956Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dt2_p50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1418.45, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:35.817628Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1442.15, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.svg']\n", "2025-07-24T14:33:35.832438Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1440.5, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dt2 P50 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:35.873918Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1442.15, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T14:33:35.889486Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dphit_nmr base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1442.15, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:36.368383Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.8, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.svg']\n", "2025-07-24T14:33:36.385811Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1463.15, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dphit Nmr operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:36.394335Z [info     ] 成功生成并保存了泛化能力交叉图。               [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1463.15, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:36.405226Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1463.15, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\tensorboard_logs' operation=register_artifact\n", "2025-07-24T14:33:36.421181Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.logs.tensorboard artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1463.15, 'cpu_percent': 0.0} description=用于TensorBoard可视化的日志文件目录。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:36.439646Z [info     ] --- Stage 3: Final Model Training --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1463.15, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:36.489328Z [info     ] Final model training started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1463.18, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:36.989971Z [info     ] Epoch 1/30, Train Loss: -0.1425, Val Loss: -0.4423 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1554.12, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:37.163983Z [info     ] Epoch 2/30, Train Loss: -0.7713, Val Loss: -1.0463 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:37.278311Z [info     ] Epoch 3/30, Train Loss: -1.3562, Val Loss: -1.5265 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:37.397434Z [info     ] Epoch 4/30, Train Loss: -1.8911, Val Loss: -1.6542 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:37.510373Z [info     ] Epoch 5/30, Train Loss: -2.3553, Val Loss: -2.2775 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:37.677999Z [info     ] Epoch 6/30, Train Loss: -2.8593, Val Loss: -2.9796 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:37.800221Z [info     ] Epoch 7/30, Train Loss: -3.2321, Val Loss: -3.2020 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:37.907839Z [info     ] Epoch 8/30, Train Loss: -3.6267, Val Loss: -3.3843 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.022431Z [info     ] Epoch 9/30, Train Loss: -4.0151, Val Loss: -3.5346 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.180375Z [info     ] Epoch 10/30, Train Loss: -4.3057, Val Loss: -4.2423 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.289013Z [info     ] Epoch 11/30, Train Loss: -4.6570, Val Loss: -4.5969 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.405749Z [info     ] Epoch 12/30, Train Loss: -5.0034, Val Loss: -5.0091 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.541286Z [info     ] Epoch 13/30, Train Loss: -5.2877, Val Loss: -5.2410 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.702813Z [info     ] Epoch 14/30, Train Loss: -5.6212, Val Loss: -5.5474 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.816993Z [info     ] Epoch 15/30, Train Loss: -5.9221, Val Loss: -2.7730 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:38.943608Z [info     ] Epoch 16/30, Train Loss: -6.2348, Val Loss: -6.1294 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.051241Z [info     ] Epoch 17/30, Train Loss: -6.5344, Val Loss: -6.5501 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.216880Z [info     ] Epoch 18/30, Train Loss: -6.7883, Val Loss: -6.7807 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.341411Z [info     ] Epoch 19/30, Train Loss: -6.9955, Val Loss: -6.8682 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.459135Z [info     ] Epoch 20/30, Train Loss: -7.2506, Val Loss: -7.1470 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.566854Z [info     ] Epoch 21/30, Train Loss: -7.4606, Val Loss: -7.3680 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.727959Z [info     ] Epoch 22/30, Train Loss: -7.6442, Val Loss: -7.4469 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.844548Z [info     ] Epoch 23/30, Train Loss: -7.8381, Val Loss: -7.5187 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:39.962441Z [info     ] Epoch 24/30, Train Loss: -7.8252, Val Loss: -7.7330 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.066817Z [info     ] Epoch 25/30, Train Loss: -7.8727, Val Loss: -7.6192 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.63, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.277980Z [info     ] Epoch 26/30, Train Loss: -8.0278, Val Loss: -7.9545 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.64, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.400168Z [info     ] Epoch 27/30, Train Loss: -8.1028, Val Loss: -7.4349 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.64, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.505733Z [info     ] Epoch 28/30, Train Loss: -8.1358, Val Loss: -7.8962 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.64, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.636677Z [info     ] Epoch 29/30, Train Loss: -8.1372, Val Loss: -7.5147 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.64, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.824038Z [info     ] Epoch 30/30, Train Loss: -8.2208, Val Loss: -8.0190 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1556.64, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.853601Z [info     ] Loaded best model state with validation loss: -8.0190 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.17, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.861154Z [info     ] Performing final evaluation on the full training dataset... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.15, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.920377Z [info     ] Final model training completed. [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.85, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.928175Z [info     ] --- Stage 3 Artifacts: Saving Final Model Assets --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.77, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:40.974883Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.assets_pytorch artifact_path=obmiq_training_pytorch\\model_assets_pytorch.pkl context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.84, 'cpu_percent': 0.0} description=包含模型权重、超参数和预处理器的PyTorch模型资产包。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:41.003367Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.84, 'cpu_percent': 0.0} description=最终模型训练过程中的损失变化历史。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:41.024594Z [info     ] Plotting final training history... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.84, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:41.040504Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.84, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.training_history\n", "2025-07-24T14:33:41.051555Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=final_training_history base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1558.84, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:41.433584Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1583.79, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\final_training_history.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\final_training_history.svg']\n", "2025-07-24T14:33:41.451355Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1583.8, 'cpu_percent': 0.0} description=最终模型训练的损失曲线图。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:41.470243Z [info     ] Saving final model evaluation data and generating plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1583.8, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:41.630890Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_model_evaluation artifact_path=obmiq_training_pytorch\\final_model_evaluation.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1587.16, 'cpu_percent': 0.0} description=最终模型在全部训练数据上的预测和残差结果。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:41.650202Z [info     ] Generating evaluation plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1587.16, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:41.688357Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1588.86, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T14:33:41.732008Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dt2_p50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1588.86, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:42.184652Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1611.53, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.svg']\n", "2025-07-24T14:33:42.205716Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1609.84, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:42.285401Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1612.23, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T14:33:42.312983Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dphit_nmr base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1612.3, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:42.768824Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1634.91, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.svg']\n", "2025-07-24T14:33:42.783343Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1633.22, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:42.875794Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1634.96, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T14:33:42.888855Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dt2_p50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1634.96, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:43.376117Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.05, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.svg']\n", "2025-07-24T14:33:43.391007Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1658.36, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:43.428702Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.8, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T14:33:43.444804Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dphit_nmr base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.8, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:43.970885Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1685.54, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.svg']\n", "2025-07-24T14:33:43.983776Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1683.85, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:44.019574Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1685.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T14:33:44.118378Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dt2_p50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1686.06, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:44.456725Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1710.84, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.svg']\n", "2025-07-24T14:33:44.473451Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1709.15, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:44.558586Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1712.1, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T14:33:44.624822Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dphit_nmr base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1712.1, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:45.064543Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1736.89, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.svg']\n", "2025-07-24T14:33:45.102303Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1735.2, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:45.121364Z [info     ] --- Stage 3: Model Interpretability Analysis (Captum) --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1735.2, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:45.195629Z [info     ] Running Captum analysis for target: DT2_P50... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1736.88, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:47.607095Z [info     ] Running Captum analysis for target: DPHIT_NMR... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1778.89, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:49.847797Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1781.34, 'cpu_percent': 0.0} error=File not found: test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-24T14:33:49.861287Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1781.34, 'cpu_percent': 0.0} error=File not found: test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-24T14:33:49.875016Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1781.34, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:49.964837Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50_data.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1782.96, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50) operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:49.992728Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1784.22, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-24T14:33:50.045800Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1784.23, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:50.418181Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1808.95, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.svg']\n", "2025-07-24T14:33:50.436645Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1808.95, 'cpu_percent': 0.0} error=File not found: test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-24T14:33:50.451873Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1808.95, 'cpu_percent': 0.0} error=File not found: test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-24T14:33:50.460650Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1808.95, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:50.537824Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR_data.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.53, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR) operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:33:50.566035Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-24T14:33:50.583597Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.54, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:50.933908Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1834.24, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.svg']\n", "2025-07-24T14:33:50.968147Z [warning  ] 井名列 'WELL_NO' 不是字符串类型，将进行区分大小写的精确匹配。这可能导致因大小写或空格问题而找不到井。 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1834.24, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:50.985571Z [info     ] 找到样本: 井='c-1', 目标深度=6311.73, 实际深度=6311.80 (索引=9) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1834.93, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:50.995901Z [info     ] 找到样本: 井='c-1', 目标深度=6313.38, 实际深度=6313.32 (索引=19) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.58, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.009140Z [info     ] 找到样本: 井='c-1', 目标深度=6318.8, 实际深度=6318.81 (索引=53) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.58, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.015260Z [info     ] 找到样本: 井='c-1', 目标深度=6334.55, 实际深度=6334.51 (索引=140) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.58, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.024403Z [info     ] 找到样本: 井='c-1', 目标深度=6409.94, 实际深度=6409.94 (索引=635) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.58, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.034372Z [info     ] 找到样本: 井='c-1', 目标深度=6426.71, 实际深度=6426.71 (索引=745) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.58, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.041985Z [info     ] 找到样本: 井='c-1', 目标深度=6440.34, 实际深度=6440.27 (索引=834) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.58, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.085766Z [info     ] 找到样本: 井='t-1', 目标深度=6426.51, 实际深度=6426.56 (索引=1080) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.9, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.097892Z [info     ] 找到样本: 井='t-1', 目标深度=6471.0, 实际深度=6471.06 (索引=1338) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.87, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.109102Z [info     ] 找到样本: 井='t-1', 目标深度=6552.36, 实际深度=6552.29 (索引=1846) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.87, 'cpu_percent': 0.0}\n", "2025-07-24T14:33:51.134975Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1837.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:33:51.207414Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1837.79, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:54.062231Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1889.45, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.svg']\n", "2025-07-24T14:33:54.088353Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1889.51, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:33:54.184123Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1890.2, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:57.101361Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1916.33, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.svg']\n", "2025-07-24T14:33:57.123950Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1916.44, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:33:57.234180Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1917.05, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:33:59.956545Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1970.41, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.svg']\n", "2025-07-24T14:33:59.990381Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1970.45, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:00.101687Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1971.2, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:02.847244Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2025.38, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:02.881404Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2025.44, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:02.987763Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2026.31, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:05.708637Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2079.94, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.svg']\n", "2025-07-24T14:34:05.795036Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2079.98, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:05.844917Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2080.83, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:08.806557Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2014.14, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:08.839437Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2014.36, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:08.895335Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2014.36, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:11.645321Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2064.2, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.svg']\n", "2025-07-24T14:34:11.665911Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2064.2, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:11.762283Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2064.26, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:14.584257Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2115.53, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:14.609239Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2115.56, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:14.722624Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2116.52, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:17.446126Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2169.42, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.svg']\n", "2025-07-24T14:34:17.479855Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2169.49, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:17.586931Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2170.43, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:20.205192Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2223.7, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:20.265857Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2223.94, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:20.328601Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2225.12, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:24.207861Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2158.46, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.svg']\n", "2025-07-24T14:34:24.239094Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2158.73, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:24.325815Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2158.74, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:28.345212Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2208.36, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:28.388148Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2208.36, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:28.479062Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2208.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:31.403815Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2259.87, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.svg']\n", "2025-07-24T14:34:31.442414Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2259.91, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:31.534678Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2260.99, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:34.452614Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2314.41, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:34.497413Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2314.45, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:34.594858Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2315.48, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:37.444871Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2369.08, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.svg']\n", "2025-07-24T14:34:37.475916Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2369.11, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:37.586343Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2369.89, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:40.381820Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2423.18, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:40.439898Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2423.21, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:40.539707Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2424.19, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:43.366140Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2355.08, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.svg']\n", "2025-07-24T14:34:43.439636Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2355.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:43.501711Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2355.26, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:46.285719Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2405.59, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:46.309454Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2405.62, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:46.401876Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DT2_P50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2405.64, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:49.117227Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2457.76, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.svg']\n", "2025-07-24T14:34:49.154810Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2457.82, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T14:34:49.270510Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DPHIT_NMR base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2458.49, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:52.022337Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.64, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.svg']\n", "2025-07-24T14:34:52.043422Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.64, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_sequence_attributions_dir' operation=register_artifact\n", "2025-07-24T14:34:52.140209Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_sequence_attributions_dir artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:52.157300Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.65, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir' operation=register_artifact\n", "2025-07-24T14:34:52.170410Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_saliency_examples_dir artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:52.186632Z [info     ] --- Stage 5: Exporting Model to ONNX --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2511.66, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.261232Z [info     ] Model successfully exported to ONNX format at: test\\obmiq_run_pytorch_20250724_223253\\obmiq_training_pytorch\\obmiq_model.onnx [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.81, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.272386Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.onnx_model artifact_path=obmiq_training_pytorch\\obmiq_model.onnx context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.82, 'cpu_percent': 0.0} description=可用于跨平台部署的ONNX格式模型。 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:52.287468Z [info     ] ===== OBMIQ Training Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.82, 'cpu_percent': 0.0}\n", "训练步骤完成。最佳超参数: {'cnn_filters': 8.0, 'cnn_kernel_size': 3.0, 'dropout_rate': 0.2024533593207251, 'learning_rate': 0.009766917139845187, 'mlp_units': 8.0, 'weight_decay': 0.000246503874117811}\n", "\n", "--- 开始 OBMIQ 预测步骤 (PyTorch) ---\n", "2025-07-24T14:34:52.301531Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.88, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.324210Z [info     ] Validating prediction inputs... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.88, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.340049Z [info     ] Loading model assets and reconstructing model... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.88, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.375193Z [info     ] Preprocessing prediction data... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.88, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.385309Z [info     ] 成功将模型所需的逻辑曲线名解析为预测数据的物理列名。     [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2518.88, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.400027Z [info     ] Performing inference on device: cuda... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2520.82, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.421962Z [info     ] Formatting predictions and saving artifacts... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2520.87, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.638531Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.datasets.predictions artifact_path=obmiq_prediction_pytorch\\predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2529.27, 'cpu_percent': 0.0} description=包含原始输入和模型预测结果的数据集 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:52.659460Z [info     ] Ground truth found in prediction data. Generating evaluation plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2529.27, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.928342Z [info     ] Updated prediction snapshot with residual columns at: test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch\\predictions.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2531.41, 'cpu_percent': 0.0}\n", "2025-07-24T14:34:52.978019Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2536.27, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T14:34:53.001523Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dt2_p50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2536.34, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:53.444971Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2559.0, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.svg']\n", "2025-07-24T14:34:53.514328Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2556.09, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:53.584150Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2559.77, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T14:34:53.600088Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dphit_nmr base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2559.86, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:54.072153Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2582.49, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.svg']\n", "2025-07-24T14:34:54.103785Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2579.57, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:54.191740Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2582.96, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T14:34:54.204969Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dt2_p50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2582.99, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:54.665525Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2607.76, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.svg']\n", "2025-07-24T14:34:54.691049Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_plot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2604.85, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:54.791138Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2608.38, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T14:34:54.804720Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dphit_nmr base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2608.46, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:55.280024Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2633.21, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.svg']\n", "2025-07-24T14:34:55.300400Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_plot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2630.3, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:55.385098Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2633.96, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T14:34:55.449321Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dt2_p50 base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2634.11, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:55.816903Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2658.8, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.svg']\n", "2025-07-24T14:34:55.832124Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_hist_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2655.89, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50 operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:55.913556Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2659.88, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T14:34:55.971964Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dphit_nmr base_path=test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2660.01, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T14:34:56.354958Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2684.71, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.png', 'test\\\\obmiq_run_pytorch_20250724_223253\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.svg']\n", "2025-07-24T14:34:56.373050Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_hist_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2681.8, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr operation=register_artifact run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:56.396540Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2681.8, 'cpu_percent': 0.0}\n", "预测步骤完成。\n", "\n", "预测结果已保存至: test\\obmiq_run_pytorch_20250724_223253\\obmiq_prediction_pytorch\\predictions.csv\n", "\n", "预测结果预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL_NO</th>\n", "      <th>MD</th>\n", "      <th>DT2_P50</th>\n", "      <th>DT2_P50_PRED</th>\n", "      <th>DPHIT_NMR</th>\n", "      <th>DPHIT_NMR_PRED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C-1</td>\n", "      <td>6310.4268</td>\n", "      <td>0.104076</td>\n", "      <td>0.098737</td>\n", "      <td>0.065477</td>\n", "      <td>0.004359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C-1</td>\n", "      <td>6310.5792</td>\n", "      <td>-0.148789</td>\n", "      <td>-0.034583</td>\n", "      <td>0.061988</td>\n", "      <td>0.012552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C-1</td>\n", "      <td>6310.7316</td>\n", "      <td>-0.211292</td>\n", "      <td>-0.048610</td>\n", "      <td>0.052548</td>\n", "      <td>0.013413</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C-1</td>\n", "      <td>6310.8840</td>\n", "      <td>-0.245636</td>\n", "      <td>0.032497</td>\n", "      <td>0.038409</td>\n", "      <td>0.008429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C-1</td>\n", "      <td>6311.0364</td>\n", "      <td>0.112414</td>\n", "      <td>0.115707</td>\n", "      <td>0.051449</td>\n", "      <td>0.022202</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  WELL_NO         MD   DT2_P50  DT2_P50_PRED  DPHIT_NMR  DPHIT_NMR_PRED\n", "0     C-1  6310.4268  0.104076      0.098737   0.065477        0.004359\n", "1     C-1  6310.5792 -0.148789     -0.034583   0.061988        0.012552\n", "2     C-1  6310.7316 -0.211292     -0.048610   0.052548        0.013413\n", "3     C-1  6310.8840 -0.245636      0.032497   0.038409        0.008429\n", "4     C-1  6311.0364  0.112414      0.115707   0.051449        0.022202"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T14:34:56.495035Z [info     ] Run marked as successful       [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2681.6, 'cpu_percent': 0.0} operation=mark_success run_id=20250724-143253-5d864dfd\n", "2025-07-24T14:34:56.514299Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2681.6, 'cpu_percent': 0.0} duration_seconds=123.394 manifest_path=test\\obmiq_run_pytorch_20250724_223253\\manifest.json operation=finalize run_id=20250724-143253-5d864dfd status=COMPLETED\n"]}], "source": ["if project:\n", "    # 定义输出目录\n", "    output_dir = Path(\"./test\") # 使用新的输出目录\n", "    output_dir.mkdir(exist_ok=True)\n", "\n", "    run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"obmiq_run_pytorch\")\n", "    # 使用 RunContext 包裹整个工作流\n", "    with RunContext(run_dir=output_dir / run_dir_name, overwrite=True) as ctx:\n", "\n", "        # --- 1. 训练步骤 ---\n", "        print(\"--- 开始 OBMIQ 训练步骤 (PyTorch) ---\")\n", "\n", "        # a. 定义训练配置\n", "        training_config = ObmiqTrainingConfig(\n", "            n_trials=20,\n", "            max_epochs_per_trial=20,\n", "            final_train_epochs=30,\n", "            patience=20,\n", "            batch_size=64\n", "        )\n", "\n", "        # b. 定义facade函数的关键字参数\n", "        training_kwargs = {\n", "            \"sequence_feature\": \"PHI_T2_DIST_CUM\",\n", "            \"normalization_feature\": \"PHIT_NMR\",\n", "            \"tabular_features\": [\"SWB_NMR\", \"BFV_NMR\", \"PHIE_NMR\", \"RD_LOG10\", \"CN\", \"DRES\", \"DEN\", \"RS_LOG10\", \"DT\", \"BVI_NMR\", \"SDR_PROXY\"],\n", "            \"target_features\": [\"DT2_P50\", \"DPHIT_NMR\"],\n", "            \"grouping_feature\": well_no_name, # 使用新的参数名\n", "            \"t2_time_axis\": t2_time_array,\n", "        }\n", "\n", "        saliency_samples = [(\"c-1\",6311.73),\n", "                            (\"c-1\",6313.38),\n", "                            (\"c-1\",6318.8),\n", "                            (\"c-1\",6334.55),\n", "                            (\"c-1\",6409.94),\n", "                            (\"c-1\",6426.71),\n", "                            (\"c-1\",6440.34),\n", "                            (\"t-1\",6426.51),\n", "                            (\"t-1\",6471.0),\n", "                            (\"t-1\",6552.36)]\n", "\n", "        # c. 执行训练步骤\n", "        # 新版facade直接接收WpDataFrameBundle，无需手动准备X, y\n", "        training_results = run_obmiq_training_step(\n", "            config=training_config,\n", "            ctx=ctx,\n", "            train_bundle=train_bundle,\n", "            depth_feature=depth_name,\n", "            saliency_samples=saliency_samples,\n", "            **training_kwargs\n", "        )\n", "\n", "        print(f\"训练步骤完成。最佳超参数: {training_results.get('best_hyperparameters')}\")\n", "\n", "        # --- 2. 预测步骤 ---\n", "        print(\"\\n--- 开始 OBMIQ 预测步骤 (PyTorch) ---\")\n", "\n", "        # a. 从上下文中获取模型资产的路径\n", "        model_assets_path = ctx.get_artifact_path(ObmiqTrainingArtifacts.MODEL_ASSETS.value)\n", "\n", "        # b. 使用产物处理器加载模型资产\n", "        handler = ObmiqArtifactHandler()\n", "        model_assets = handler.load_model_assets(model_assets_path)\n", "\n", "        # c. 定义预测配置\n", "        prediction_config = ObmiqPredictionConfig()\n", "\n", "        # d. 定义facade函数的关键字参数\n", "        prediction_kwargs = {\n", "            \"source_t2_time_axis\": t2_time_array, # 假设预测数据与训练数据T2轴相同\n", "            \"output_curve_names\": (\"DT2_P50_PRED\", \"DPHIT_NMR_PRED\")\n", "        }\n", "\n", "        # e. 执行预测步骤\n", "        prediction_results = run_obmiq_prediction_step(\n", "            config=prediction_config,\n", "            ctx=ctx,\n", "            model_assets=model_assets,\n", "            prediction_bundle=prediction_bundle,\n", "            **prediction_kwargs\n", "        )\n", "\n", "        print(\"预测步骤完成。\")\n", "\n", "        # --- 3. 结果检查 ---\n", "        # 新版facade返回的是预测结果文件的路径\n", "        prediction_path = Path(prediction_results[\"output_path\"])\n", "        if prediction_path.exists():\n", "            print(f\"\\n预测结果已保存至: {prediction_path}\")\n", "            # 从CSV加载结果进行预览\n", "            predicted_df = pd.read_csv(prediction_path)\n", "            print(\"\\n预测结果预览:\")\n", "            display(predicted_df[[\"WELL_NO\", \"MD\", \"DT2_P50\", \"DT2_P50_PRED\", \"DPHIT_NMR\", \"DPHIT_NMR_PRED\"]].head())\n", "        else:\n", "            print(f\"❌ 预测结果文件未找到: {prediction_path}\")\n", "\n", "else:\n", "    print(\"因数据加载失败，跳过工作流执行。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}