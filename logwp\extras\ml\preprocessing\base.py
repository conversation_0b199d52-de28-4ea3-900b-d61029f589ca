"""
预处理器基类定义
"""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpDataFrameBundle


class BasePreprocessor(ABC):
    """
    所有预处理器的抽象基类。

    定义了一个类似 scikit-learn 的接口，包含 fit 和 transform 方法。
    """

    @abstractmethod
    def fit(self, bundle: WpDataFrameBundle) -> "BasePreprocessor":
        """
        根据输入数据包“学习”预处理所需的参数。
        例如，对于一个缩放器，它会学习每口井的均值和标准差。
        """
        raise NotImplementedError

    @abstractmethod
    def transform(self, bundle: WpDataFrameBundle) -> WpDataFrameBundle:
        """
        使用已学习的参数对输入数据包进行转换。
        """
        raise NotImplementedError
