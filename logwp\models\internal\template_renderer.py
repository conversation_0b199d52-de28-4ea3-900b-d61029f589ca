"""模板渲染器。

提供基于Jinja2的模板渲染功能，支持多种模板和国际化。

Architecture
------------
层次/依赖: models/internal服务层
设计原则: 模板与数据分离、可扩展、缓存优化
模板引擎: Jinja2

Examples:
    >>> renderer = TemplateRenderer()
    >>> content = renderer.render_markdown(data, template="default")
    >>> print(content)

References:
    《SCAPE_DDS_logwp_generate_summary.md》- 模板系统设计
"""

from __future__ import annotations

import os
from pathlib import Path
from typing import Any, Dict

from logwp.infra import get_logger

logger = get_logger(__name__)

# 延迟导入Jinja2，避免强制依赖
try:
    from jinja2 import Environment, FileSystemLoader, Template
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False
    Environment = None
    FileSystemLoader = None
    Template = None


class TemplateRenderer:
    """模板渲染器。

    支持基于Jinja2的模板渲染，提供缓存和错误处理。
    """

    def __init__(self, template_dir: Path | None = None):
        """初始化模板渲染器。

        Args:
            template_dir: 模板目录路径，None表示使用默认目录

        Raises:
            ImportError: Jinja2未安装
        """
        if not JINJA2_AVAILABLE:
            raise ImportError(
                "Jinja2 is required for template rendering. "
                "Install it with: pip install 'scape[templates]'"
            )

        if template_dir is None:
            # 默认模板目录：logwp/models/tpl_summary
            # __file__ 是 logwp/models/internal/template_renderer.py
            # parent.parent 是 logwp/models
            template_dir = Path(__file__).parent.parent / "tpl_summary"

        self.template_dir = Path(template_dir)
        self._env = None
        self._template_cache: Dict[str, Template] = {}

        logger.debug("模板渲染器初始化", template_dir=str(self.template_dir))

    @property
    def env(self) -> Environment:
        """获取Jinja2环境。"""
        if self._env is None:
            self._env = Environment(
                loader=FileSystemLoader(str(self.template_dir)),
                autoescape=False,  # Markdown不需要自动转义
                trim_blocks=True,
                lstrip_blocks=True
            )

            # 添加自定义过滤器
            self._env.filters['format_float'] = self._format_float

        return self._env

    def render_markdown(
        self,
        data: Dict[str, Any],
        template: str = "default"
    ) -> str:
        """渲染Markdown格式报告。

        Args:
            data: 结构化数据
            template: 模板名称（不含扩展名）

        Returns:
            str: 渲染后的Markdown内容

        Raises:
            FileNotFoundError: 模板文件不存在
            Exception: 模板渲染失败

        Examples:
            >>> renderer = TemplateRenderer()
            >>> content = renderer.render_markdown(data, "default")
            >>> print(content)
        """
        template_name = f"summary_{template}.md.jinja2"

        try:
            # 从缓存获取或加载模板
            if template_name not in self._template_cache:
                self._template_cache[template_name] = self.env.get_template(template_name)

            template_obj = self._template_cache[template_name]

            # 调试日志：检查传入模板的数据
            head_attrs = data.get("head_attributes", {})
            logger.debug("模板渲染数据检查",
                        template=template_name,
                        has_head_attributes="head_attributes" in data,
                        has_attribute_records="attribute_records" in head_attrs,
                        attribute_records_count=len(head_attrs.get("attribute_records", [])))

            # 渲染模板
            content = template_obj.render(**data)

            logger.debug("模板渲染成功",
                        template=template_name,
                        content_length=len(content))

            return content

        except Exception as e:
            logger.error("模板渲染失败",
                        template=template_name,
                        error=str(e))
            raise

    def list_available_templates(self) -> list[str]:
        """列出可用的模板。

        Returns:
            list[str]: 可用模板名称列表（不含扩展名）
        """
        templates = []

        if not self.template_dir.exists():
            return templates

        for file_path in self.template_dir.glob("summary_*.md.jinja2"):
            # 提取模板名称：summary_default.md.jinja2 -> default
            name = file_path.stem.replace("summary_", "").replace(".md", "")
            templates.append(name)

        return sorted(templates)

    def template_exists(self, template: str) -> bool:
        """检查模板是否存在。

        Args:
            template: 模板名称

        Returns:
            bool: 模板是否存在
        """
        template_name = f"summary_{template}.md.jinja2"
        template_path = self.template_dir / template_name
        return template_path.exists()

    def clear_cache(self) -> None:
        """清空模板缓存。"""
        self._template_cache.clear()
        logger.debug("模板缓存已清空")

    @staticmethod
    def _format_float(value: Any, precision: int = 2) -> str:
        """格式化浮点数。

        Args:
            value: 要格式化的值
            precision: 小数位数

        Returns:
            str: 格式化后的字符串
        """
        if value is None:
            return "N/A"

        try:
            return f"{float(value):.{precision}f}"
        except (ValueError, TypeError):
            return str(value)


def get_default_renderer() -> TemplateRenderer:
    """获取默认模板渲染器实例。

    Returns:
        TemplateRenderer: 默认渲染器实例
    """
    return TemplateRenderer()
