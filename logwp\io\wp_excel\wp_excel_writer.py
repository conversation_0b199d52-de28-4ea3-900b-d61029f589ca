"""WP Excel文件写入器。

实现WFS v1.0规范的WP Excel文件写入功能，支持完整的测井数据模型保存。
遵循SAD文档的"内部服务层设计（Utility/Helper Pattern）"模式。

Architecture
------------
层次/依赖: I/O层格式特定处理器，依赖models、constants、exceptions
设计原则: 格式解耦、内部服务转发、WFS规范兼容
性能特征: 内存优化、批量写入、Excel格式化

Core Features
-------------
- **WFS v1.0规范**: 严格按照WFS规范生成Excel文件
- **内部服务转发**: 主类只做功能转发，具体逻辑在internal层
- **可配置写入**: 支持选择性保存表头、井名映射和数据集
- **性能/格式分离**: 支持高性能的无格式写入和可选的样式格式化
- **Excel格式化**: 丰富的格式化选项，提升用户体验

Examples
--------
>>> from logwp.io.wp_excel import WpExcelWriter
>>> from logwp.io.wp_excel.config import ExcelFormattingConfig
>>>
>>> # 基本写入
>>> writer = WpExcelWriter()
>>> writer.write(project, "output.wp.xlsx")
>>>
>>> # 高性能无格式写入
>>> writer.write(project, "output.wp.xlsx",
...              save_head_info=True,
...              save_well_map=False,
...              dataset_names=["OBMIQ_logs"],
...              apply_formatting=False)

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》- 详细设计文档
- 《SCAPE_WFS_WP文件规范.md》- WFS v1.0规范
- 《SCAPE_SAD_logwp.md》- 内部服务层设计模式
"""

from __future__ import annotations

import time
from pathlib import Path
from typing import Sequence

import structlog

from logwp.io.exceptions import WpFileFormatError, WpIOError
from logwp.models.exceptions import WpValidationError
from logwp.infra.exceptions import ErrorContext
from logwp.models.well_project import WpWellProject
from .config import ExcelFormattingConfig
from .internal.excel_writer import create_workbook_with_sheets, save_workbook_to_file
from .internal.wp_excel_formatter_service import WpExcelFormatter

logger = structlog.get_logger(__name__)

__all__ = [
    "WpExcelWriter",
]


class WpExcelWriter:
    """WP Excel文件写入器（内部服务转发实现）。

    遵循SAD文档的"内部服务层设计（Utility/Helper Pattern）"模式，
    将具体写入逻辑转发给internal层处理。

    Architecture
    ------------
    层次/依赖: I/O层格式特定处理器，转发到internal层
    设计原则: 转发模式、WFS规范兼容、可配置性
    性能特征: 内存优化、批量写入、异常处理

    Examples:
        >>> writer = WpExcelWriter()
        >>> writer.write(project, "output.wp.xlsx")
        >>>
        >>> # 自定义保存选项
        >>> writer.write(project, "output.wp.xlsx",
        ...              save_head_info=True,
        ...              save_well_map=True,
        ...              dataset_names=["OBMIQ_logs", "NMR_logs"])
        >>>
        >>> # 禁用格式化以提升性能
        >>> writer.write(project, "output.wp.xlsx", apply_formatting=False)

    References:
        《SCAPE_DDS_logwp_io_write_wp_excel.md》§2.1 - 主类设计
        《SCAPE_SAD_logwp.md》§4.2 - 内部服务层模式
    """

    def __init__(self) -> None:
        """初始化写入器。"""
        self._start_time: float | None = None
        self._current_file: Path | None = None

    def write(
        self,
        project: WpWellProject,
        file_path: str | Path,
        *,
        save_head_info: bool = True,
        save_well_map: bool = True,
        dataset_names: Sequence[str] | None = None,
        formatting_config: ExcelFormattingConfig | None = None,
        apply_formatting: bool = True
    ) -> None:
        """主写入流程（转发到internal层）。

        将WpWellProject测井数据模型保存为符合WFS v1.0规范的Excel文件。支持数据写入与
        样式格式化的分离，通过`apply_formatting=False`可实现高性能无格式写入。

        Args:
            project: 要保存的测井项目对象
            file_path: 输出文件路径，建议使用.wp.xlsx扩展名
            save_head_info: 是否保存井头信息表单，默认True
            save_well_map: 是否保存井名映射表单，默认True
            dataset_names: 要保存的数据集名称列表，None表示保存所有数据集
            formatting_config: Excel格式化配置，None使用默认配置
            apply_formatting: 是否应用Excel格式化，默认True。对于大型数据集，
                             设为False可大幅提升写入性能。

        Raises:
            WpValidationError: 项目数据验证失败
            WpFileFormatError: 文件格式生成失败
            WpIOError: 文件写入失败
            PermissionError: 文件写入权限不足

        Examples:
            >>> # 保存所有内容
            >>> writer.write(project, "complete.wp.xlsx")
            >>>
            >>> # 仅保存指定数据集
            >>> writer.write(project, "logs_only.wp.xlsx",
            ...              save_head_info=False,
            ...              save_well_map=False,
            ...              dataset_names=["OBMIQ_logs"])
            >>>
            >>> # 使用自定义格式化
            >>> config = ExcelFormattingConfig(
            ...     default_font_size=10,
            ...     structure_bg_color="FFD700"
            ... )
            >>> writer.write(project, "formatted.wp.xlsx", formatting_config=config)
            >>>
            >>> # 禁用格式化以提升性能
            >>> writer.write(project, "no_format.wp.xlsx", apply_formatting=False)
        """
        # 参数验证和预处理
        file_path = Path(file_path)
        self._start_time = time.time()
        self._current_file = file_path

        # 使用默认格式化配置
        if formatting_config is None:
            formatting_config = ExcelFormattingConfig()

        logger.info(
            "开始写入WP Excel文件",
            file_path=str(file_path),
            project_name=project.name,
            save_head_info=save_head_info,
            save_well_map=save_well_map,
            dataset_count=len(dataset_names) if dataset_names else len(list(project.datasets.keys())),
            apply_formatting=apply_formatting
        )

        try:
            # 1. 参数验证
            self._validate_write_parameters(project, file_path, dataset_names)

            # 2. 转发到internal层创建包含数据的工作簿（无格式）
            workbook = create_workbook_with_sheets(
                project=project,
                save_head_info=save_head_info,
                save_well_map=save_well_map,
                dataset_names=list(dataset_names) if dataset_names else None,
                formatting_config=formatting_config, # config仍需传递给数据转换层
                write_only=not apply_formatting
            )

            # 3. (可选) 应用格式化
            if apply_formatting:
                logger.info("开始应用Excel格式化...")
                formatter = WpExcelFormatter(project, formatting_config)
                formatter.format_workbook(workbook)
                logger.info("Excel格式化应用完成。")

            # 4. 保存工作簿到文件
            save_workbook_to_file(workbook, file_path)

            # 5. 记录完成信息
            processing_time = time.time() - self._start_time
            logger.info(
                "WP Excel文件写入完成",
                file_path=str(file_path),
                project_name=project.name,
                processing_time=round(processing_time, 3)
            )

        except Exception as e:
            processing_time = time.time() - self._start_time if self._start_time else 0
            logger.error(
                "WP Excel文件写入失败",
                file_path=str(file_path),
                project_name=project.name,
                error_type=type(e).__name__,
                error_message=str(e),
                processing_time=round(processing_time, 3)
            )
            raise

    def _validate_write_parameters(
        self,
        project: WpWellProject,
        file_path: Path,
        dataset_names: Sequence[str] | None
    ) -> None:
        """验证写入参数。

        Args:
            project: 测井项目对象
            file_path: 输出文件路径
            dataset_names: 数据集名称列表

        Raises:
            WpValidationError: 参数验证失败
        """
        # 验证项目对象
        if not project.name:
            raise WpValidationError(
                "项目名称不能为空",
                context=ErrorContext(
                    operation="validate_write_parameters",
                    file_path=str(file_path)
                )
            )

        # 验证数据集名称
        if dataset_names is not None:
            available_names = set(project.datasets.keys())
            invalid_names = [name for name in dataset_names if name not in available_names]

            if invalid_names:
                raise WpValidationError(
                    f"指定的数据集不存在: {invalid_names}",
                    context=ErrorContext(
                        operation="validate_write_parameters",
                        file_path=str(file_path),
                        additional_info={
                            "invalid_datasets": invalid_names,
                            "available_datasets": list(available_names)
                        }
                    )
                )

        # 验证文件路径
        try:
            # 确保父目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            raise WpIOError(
                f"无法创建输出目录: {e}",
                context=ErrorContext(
                    operation="validate_write_parameters",
                    file_path=str(file_path)
                )
            ) from e
