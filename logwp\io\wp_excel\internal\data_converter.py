"""数据转换服务。

提供WFS格式到Models层的标准化转换功能，包括深度单位标准化、
数据类型转换、友好名称生成等核心转换能力。
"""

from __future__ import annotations

import re
import warnings
from typing import Any, Dict, List, Iterator

import pandas as pd
import structlog

from logwp.models.constants import WpDataType
from logwp.models.exceptions import WpValidationError
from logwp.io.constants import WpXlsxKey
from logwp.models.curve.metadata import CurveMetadata

logger = structlog.get_logger(__name__)

__all__ = [
    "convert_wfs_depth_unit_to_models",
    "convert_wfs_data_type_to_models",
    "build_dataframe_with_friendly_names",
    "build_dataframe_from_column_indexed_data",
    "build_dataframe_from_iterator",
    "apply_data_type_conversions",
    "validate_dataframe_construction_compliance",
]

def convert_wfs_depth_unit_to_models(wfs_unit: str) -> str:
    """WFS深度单位到Models层标准化。

    WFS规范 → Models层规范：
    - METERS/METER/M → m
    - FT/F → ft
    - 大小写不敏感处理

    Args:
        wfs_unit: WFS深度单位字符串

    Returns:
        str: 标准化的深度单位

    Examples:
        >>> unit = convert_wfs_depth_unit_to_models("METERS")
        >>> assert unit == "m"

    References:
        《SCAPE_WFS_WP文件规范.md》4.5.1节 - 深度单位规范
    """
    if not wfs_unit:
        return ""

    normalized = wfs_unit.strip().upper()

    # 米制单位
    if normalized in {WpXlsxKey.UNIT_METERS.value, WpXlsxKey.UNIT_METER.value, WpXlsxKey.UNIT_M.value}:
        return "m"

    # 英制单位
    if normalized in {WpXlsxKey.UNIT_FT.value, WpXlsxKey.UNIT_F.value, "FEET", "FOOT"}:
        return "ft"

    # 保持原值但小写
    result = wfs_unit.strip().lower()
    logger.debug("深度单位转换", wfs_unit=wfs_unit, result=result)
    return result


def convert_wfs_data_type_to_models(wfs_type: str) -> WpDataType:
    """WFS数据类型到Models层枚举。

    WFS规范 → Models层规范：
    - INT → WpDataType.INT
    - FLOAT → WpDataType.FLOAT
    - STR → WpDataType.STR
    - BOOL → WpDataType.BOOL

    Args:
        wfs_type: WFS数据类型字符串

    Returns:
        WpDataType: Models层数据类型枚举

    Examples:
        >>> data_type = convert_wfs_data_type_to_models("INT")
        >>> assert data_type == WpDataType.INT
    """
    if not wfs_type:
        return WpDataType.FLOAT  # 默认类型

    type_mapping = {
        WpXlsxKey.TYPE_INT: WpDataType.INT,
        WpXlsxKey.TYPE_FLOAT: WpDataType.FLOAT,
        WpXlsxKey.TYPE_STR: WpDataType.STR,
        WpXlsxKey.TYPE_BOOL: WpDataType.BOOL,
    }

    result = type_mapping.get(wfs_type.upper(), WpDataType.FLOAT)
    logger.debug("数据类型转换", wfs_type=wfs_type, result=result.value)
    return result

def build_dataframe_from_iterator(
    curve_metadata: CurveMetadata,
    row_iterator: Iterator[tuple],
    curve_definitions: list[dict[str, Any]],
    sheet_name: str
) -> pd.DataFrame:
    """[新] 从行迭代器高效构造DataFrame，专为流式处理设计。

    此函数直接消费行迭代器，避免将所有数据加载到内存，是性能优化的关键。

    Args:
        curve_metadata: 曲线元数据对象
        row_iterator: 行数据迭代器 (已定位到数据起始行，即第8行)
        curve_definitions: 从表头解析的曲线定义列表，包含原始列索引
        sheet_name: 工作表名称，用于日志记录

    Returns:
        pd.DataFrame: 构造的DataFrame（使用友好名称并转换了数据类型）
    """
    logger.debug("开始从行迭代器构造DataFrame", sheet_name=sheet_name)

    if not curve_definitions:
        logger.warning("无曲线定义，返回空DataFrame", sheet_name=sheet_name)
        return pd.DataFrame()

    # 1. 跳过数据标题行 (第8行，内容为 "Data")
    try:
        data_header_row = next(row_iterator)
        if not data_header_row or str(data_header_row[0] or "").strip().upper() != WpXlsxKey.ROW_TITLE_DATA.upper():
            logger.warning("数据起始行（第8行）标识不正确，但仍将继续处理", sheet_name=sheet_name, expected=WpXlsxKey.ROW_TITLE_DATA, actual=str(data_header_row[0] or ""))
    except StopIteration:
        logger.warning("在数据起始行处已无数据，返回空DataFrame", sheet_name=sheet_name)
        return pd.DataFrame()

    # 2. 准备列名和源数据列索引
    df_columns = []
    source_col_indices = []  # 0-based indices for tuple access

    name_to_curve_obj = curve_metadata.curves
    for c_def in curve_definitions:
        curve_name = c_def['name']
        col_idx = c_def['column_index']  # 1-based

        # 首先尝试直接匹配（一维曲线）
        if curve_name in name_to_curve_obj:
            curve_obj = name_to_curve_obj[curve_name]
            df_columns.append(curve_obj.dataframe_column_name)
            source_col_indices.append(col_idx - 1)
        else:
            # 尝试匹配2D曲线元素
            dataframe_column_name = _find_2d_curve_dataframe_column(curve_name, curve_metadata)
            if dataframe_column_name:
                df_columns.append(dataframe_column_name)
                source_col_indices.append(col_idx - 1)
                logger.debug("匹配2D曲线元素", sheet_name=sheet_name, curve_name=curve_name, dataframe_column=dataframe_column_name)
            else:
                logger.error("曲线定义与元数据不匹配，跳过曲线", sheet_name=sheet_name, curve_name=curve_name)

    if not df_columns:
        logger.warning("未找到有效的曲线列进行映射，返回空DataFrame", sheet_name=sheet_name)
        return pd.DataFrame()

    # 3. 创建一个生成器，从每行中提取所需的数据
    max_needed_index = max(source_col_indices) if source_col_indices else -1
    def data_generator():
        for row in row_iterator:
            padded_row = row + (None,) * (max_needed_index + 1 - len(row)) if len(row) <= max_needed_index else row
            yield [padded_row[i] for i in source_col_indices]

    # 4. 从生成器高效创建DataFrame
    df = pd.DataFrame(data_generator(), columns=df_columns)

    # 5. 应用数据类型转换
    if not df.empty:
        df = apply_data_type_conversions(df, curve_metadata)

    logger.info("DataFrame从行迭代器构造完成", sheet_name=sheet_name, shape=df.shape)

    return df


def _find_2d_curve_dataframe_column(curve_name: str, curve_metadata: CurveMetadata) -> str | None:
    """查找2D曲线元素对应的DataFrame列名。

    Args:
        curve_name: 曲线名称（如"PHI_T2_DIST[1]"）
        curve_metadata: 曲线元数据对象

    Returns:
        str | None: 对应的DataFrame列名，如果找不到则返回None
    """
    # 使用与curve_parser.py相同的模式识别2D曲线元素
    pattern = WpXlsxKey.CURVE_2D_INDEX_PATTERN
    match = re.search(pattern, curve_name)

    if not match:
        return None

    # 提取基础名称和索引
    base_name = curve_name[:match.start()]
    element_index = int(match.group(1))

    # 查找对应的2D组合曲线
    if base_name in curve_metadata.curves:
        curve_obj = curve_metadata.curves[base_name]
        if curve_obj.is_2d_composite_curve() and curve_obj.dataframe_element_names:
            # 检查索引是否在有效范围内（1-based转0-based）
            if 1 <= element_index <= len(curve_obj.dataframe_element_names):
                return curve_obj.dataframe_element_names[element_index - 1]

    return None


def apply_data_type_conversions(df: pd.DataFrame,
                               curve_metadata: CurveMetadata) -> pd.DataFrame:
    """应用数据类型转换。

    Args:
        df: 原始DataFrame
        curve_metadata: 曲线元数据

    Returns:
        pd.DataFrame: 类型转换后的DataFrame

    References:
        《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》DF-3节 - 数据类型一致性
    """
    logger.debug("开始数据类型转换")

    for curve in curve_metadata.curves.values():
        if curve.is_2d_composite_curve():
            # 二维组合曲线：转换所有元素列
            if curve.dataframe_element_names:
                for friendly_name in curve.dataframe_element_names:
                    if friendly_name in df.columns:
                        df[friendly_name] = convert_column_data_type(
                            df[friendly_name], curve.data_type
                        )
                        logger.debug(
                            "转换二维组合曲线元素数据类型",
                            curve_name=curve.name,
                            friendly_name=friendly_name,
                            target_type=curve.data_type.value
                        )
            else:
                logger.warning(f"二维组合曲线 {curve.name} 缺少dataframe_element_names")
        else:
            # 一维曲线：转换单列
            column_name = curve.dataframe_column_name
            if column_name in df.columns:
                df[column_name] = convert_column_data_type(
                    df[column_name], curve.data_type
                )
                logger.debug(
                    "转换一维曲线数据类型",
                    curve_name=curve.name,
                    column_name=column_name,
                    target_type=curve.data_type.value
                )

    logger.debug("数据类型转换完成")
    return df


def convert_column_data_type(series: pd.Series, target_type: WpDataType) -> pd.Series:
    """强制数据类型转换。

    规范要求：
    - 深度曲线强制FLOAT
    - 井名曲线强制STR
    - 布尔值按WFS规范转换
    - 错误值处理

    Args:
        series: 原始数据列
        target_type: 目标数据类型

    Returns:
        pd.Series: 转换后的数据列

    References:
        《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》DF-4节 - WFS数据类型推断
    """
    if target_type == WpDataType.FLOAT:
        return pd.to_numeric(series, errors='coerce')

    elif target_type == WpDataType.INT:
        # 先转换为数值，再转换为整数
        try:
            numeric_series = pd.to_numeric(series, errors='coerce')
            return numeric_series.astype('Int64')  # 支持NaN的整数类型
        except (TypeError, ValueError) as e:
            logger.warning(f"整数类型转换失败，保持原样: {e}")
            return series

    elif target_type == WpDataType.STR:
        return series.astype(str)

    elif target_type == WpDataType.BOOL:
        return convert_boolean_series(series)

    else:
        logger.warning(f"未知数据类型: {target_type}, 保持原样")
        return series


def convert_boolean_series(series: pd.Series) -> pd.Series:
    """转换布尔值序列。

    Args:
        series: 原始数据列

    Returns:
        pd.Series: 布尔值数据列

    References:
        《SCAPE_WFS_WP文件规范.md》4.1.3节 - 布尔值规范
    """
    true_values = WpXlsxKey.bool_true_values()
    true_values_upper = {v.upper() for v in true_values}

    def convert_value(value):
        if pd.isna(value):
            return None
        str_value = str(value).strip().upper()
        return str_value in true_values_upper

    return series.apply(convert_value)


def normalize_curve_names_for_dataframe(curve_names: List[str]) -> Dict[str, str]:
    """标准化曲线名称为DataFrame友好格式。

    Args:
        curve_names: 原始曲线名称列表

    Returns:
        Dict[str, str]: 原始名称到友好名称的映射

    Examples:
        >>> mapping = normalize_curve_names_for_dataframe(["T2_VALUE[0]", "T2_VALUE[1]"])
        >>> print(mapping)  # {"T2_VALUE[0]": "T2_VALUE_0", "T2_VALUE[1]": "T2_VALUE_1"}
    """
    name_mapping = {}

    for name in curve_names:
        # 替换方括号为下划线
        friendly_name = name.replace('[', '_').replace(']', '')

        # 移除其他特殊字符
        friendly_name = ''.join(c if c.isalnum() or c == '_' else '_' for c in friendly_name)

        # 确保不以数字开头
        if friendly_name and friendly_name[0].isdigit():
            friendly_name = 'C_' + friendly_name

        # 确保不为空
        if not friendly_name:
            friendly_name = f'Column_{len(name_mapping)}'

        name_mapping[name] = friendly_name

    logger.debug("曲线名称标准化", mapping_count=len(name_mapping))
    return name_mapping


def validate_curve_data_mapping(curve_metadata: CurveMetadata,
                               raw_data: Dict[str, List[Any]]) -> None:
    """验证曲线数据映射的完整性。

    检查curve_metadata中定义的所有曲线是否在raw_data中都有对应的数据。
    对于二维组合曲线，检查所有元素是否都有数据。

    Args:
        curve_metadata: 曲线元数据对象
        raw_data: 原始数据字典 {曲线名: 数据列表}

    Raises:
        WpValidationError: 数据映射不完整

    References:
        《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》DV章节 - 数据验证规范
    """
    missing_curves = []

    for curve in curve_metadata.curves.values():
        if curve.is_2d_composite_curve():
            # 二维组合曲线：检查所有元素
            if curve.element_names:
                for element_name in curve.element_names:
                    if element_name not in raw_data:
                        missing_curves.append(element_name)
        else:
            # 一维曲线：检查曲线本身
            if curve.name not in raw_data:
                missing_curves.append(curve.name)

    if missing_curves:
        logger.warning(
            "发现缺失的曲线数据",
            missing_count=len(missing_curves),
            missing_curves=missing_curves[:10] + ["..."] if len(missing_curves) > 10 else missing_curves
        )
        # 注意：这里只警告，不抛出异常，因为某些情况下可能允许部分数据缺失
    else:
        logger.debug("曲线数据映射验证通过", total_curves=len(curve_metadata.curves))


def validate_dataframe_construction_compliance(df: pd.DataFrame,
                                             curve_metadata: CurveMetadata) -> None:
    """验证DataFrame构造合规性。

    完整验证清单：
    - CDP-1：DataFrame索引类型检查
    - 友好名称：DataFrame列名与元数据一致性
    - 数据完整性：行数一致性、空值处理

    Args:
        df: 构造的DataFrame
        curve_metadata: 曲线元数据

    Raises:
        WpValidationError: 构造合规性验证失败

    References:
        《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》DV章节 - 数据验证规范
    """
    # CDP-1合规性检查
    if not isinstance(df.index, pd.RangeIndex):
        raise WpValidationError("DataFrame必须使用默认整数索引（CDP-1原则）")

    # 友好名称一致性验证
    expected_columns = set()
    for curve in curve_metadata.curves.values():
        if curve.is_2d_composite_curve():
            expected_columns.update(curve.dataframe_element_names)
        else:
            expected_columns.add(curve.dataframe_column_name)

    actual_columns = set(df.columns)

    if expected_columns != actual_columns:
        missing = expected_columns - actual_columns
        extra = actual_columns - expected_columns
        raise WpValidationError(
            f"DataFrame列名与元数据不一致。缺失: {missing}, 多余: {extra}"
        )

    # 数据完整性检查
    if df.empty:
        logger.warning("DataFrame为空")
    else:
        logger.debug(
            "DataFrame构造合规性验证通过",
            shape=df.shape,
            index_type=type(df.index).__name__
        )
