"""logwp.extras.petroplot.crossplot - 可追踪的二维交会图组件

本组件提供了一个标准化的、可复现的、可被工作流编排的绘图功能，
专门用于绘制通用的二维交会图（散点图）。

核心功能:
- **三层API**: 提供步骤门面、普通门面和重绘函数，适应不同使用场景。
- **多系列支持**: 支持在同一图表上绘制多个数据系列（散点图、折线图），每个系列可来自不同的数据源。
- **配置驱动**: 通过Pydantic模型将绘图逻辑与样式完全分离。
- **可追踪性**: 与`logwp.extras.tracking`框架集成，自动记录产物。
- **可复现性**: 支持从数据和逻辑快照精确复现图表。
- **可定制化**: 支持通过`logwp.extras.plotting`的PlotProfile系统定制外观。

公共API:
- **门面函数**:
  - `run_crossplot_step`: 与工作流集成的步骤门面。
  - `generate_crossplot`: 接收DataFrame的普通门面。
  - `replot_crossplot_from_snapshot`: 从快照复现图表的重绘函数。
- **配置模型**:
  - `CrossPlotConfig`: 绘图的逻辑与表现层配置。
  - `SeriesConfig`: 定义单个数据系列的配置。
  - `AxisConfig`: 坐标轴的详细配置。
- **常量**:
  - `CrossPlotArtifacts`: 产物逻辑名称。
  - `CrossPlotProfiles`: 绘图模板名称。
"""

# 1. 导入 plot_profiles 模块以触发其内部的自动注册机制。
from . import plot_profiles
from . import presets

# 2. 从各模块导出公共API
from .config import CrossPlotConfig, SeriesConfig, AxisConfig
from .constants import CrossPlotArtifacts, CrossPlotProfiles
from .facade import (
    run_crossplot_step,
    generate_crossplot,
    replot_crossplot_from_snapshot,
)
from .presets import create_publication_ready_perm_config

# 3. 定义 __all__ 以便 `from . import *` 使用
__all__ = [
    "run_crossplot_step", "generate_crossplot", "replot_crossplot_from_snapshot",
    "CrossPlotConfig", "SeriesConfig", "AxisConfig",
    "CrossPlotArtifacts", "CrossPlotProfiles",
    "create_publication_ready_perm_config"
]
