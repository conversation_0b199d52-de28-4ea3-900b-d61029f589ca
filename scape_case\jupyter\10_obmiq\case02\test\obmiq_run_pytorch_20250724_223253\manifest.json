{"run_id": "20250724-143253-5d864dfd", "start_time_utc": "2025-07-24T14:32:53.112235Z", "end_time_utc": "2025-07-24T14:34:56.506076Z", "duration_seconds": 123.394, "status": "COMPLETED", "lineage": {"inputs": {}, "code_version": {}}, "config_snapshot_path": null, "parameters": {}, "metrics": {}, "artifacts": {"obmiq_training.configs.training_config_snapshot": {"path": "obmiq_training_pytorch/training_config.json", "type": "data", "metadata": {"hash": "cd204c1409459ec1d05067dc376eb97b7bbfbb0144b5477c0c5cf33e48a8d86f", "hash_algorithm": "sha256", "size_bytes": 509}, "description": "Snapshot of the training configuration used for this run."}, "obmiq_training.reports.cv_performance": {"path": "obmiq_training_pytorch/cv_performance_report.csv", "type": "data", "metadata": {"hash": "01359fcdc4d4756961187ac7f049747f6f01c257965f8ad39617fb14d655206f", "hash_algorithm": "sha256", "size_bytes": 290}, "description": "LOWO-CV中每一折的最佳验证损失和对应的超参数。"}, "obmiq_training.reports.hyperparameter_tuning": {"path": "obmiq_training_pytorch/hyperparameter_tuning_report.json", "type": "data", "metadata": {"hash": "95527e4a799e0ceb474b0b7d978bd2a65c40e0fd14b1d3370857a2a97778b0ed", "hash_algorithm": "sha256", "size_bytes": 196}, "description": "在所有CV折中聚合得到的全局最佳超参数组合。"}, "obmiq_training.reports.lowo_cv_performance_summary": {"path": "obmiq_training_pytorch/lowo_cv_performance_summary.csv", "type": "data", "metadata": {"hash": "e34af4885049fb485407c79da14cf062b4a92b0583a8dce3d22c7cdd161ea4b8", "hash_algorithm": "sha256", "size_bytes": 216}, "description": "LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。"}, "obmiq_training.data_snapshots.lowo_cv_predictions": {"path": "obmiq_training_pytorch/lowo_cv_predictions.csv", "type": "data", "metadata": {"hash": "5defa0ab0cf070f9da052507896d3456e1bc03935e2bed5013801791ff0ee4e2", "hash_algorithm": "sha256", "size_bytes": 2056249}, "description": "LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。"}, "obmiq_training.plots.lowo_cv_crossplot_dt2_p50": {"path": "obmiq_training_pytorch/lowo_cv_crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "f3a6938dc87f92f10da75b6391d44c3c09f34888150d3eca47e1d98836439758", "hash_algorithm": "sha256", "size_bytes": 727113}, "description": "LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dt2 P50"}, "obmiq_training.plots.lowo_cv_crossplot_dphit_nmr": {"path": "obmiq_training_pytorch/lowo_cv_crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "ff86560cbce2a838beaaa6c87ebd2d521e5f36cd01f2e82bf2d5457221486c21", "hash_algorithm": "sha256", "size_bytes": 1005826}, "description": "LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dphit Nmr"}, "obmiq_training.logs.tensorboard": {"path": "obmiq_training_pytorch/tensorboard_logs", "type": "unknown", "metadata": {"size_bytes": 0}, "description": "用于TensorBoard可视化的日志文件目录。"}, "obmiq_training.models.assets_pytorch": {"path": "obmiq_training_pytorch/model_assets_pytorch.pkl", "type": "model", "metadata": {"hash": "7296fc407da96c18a6e6be8fbaf4e6078612a6854943a391cedbd0b6e94480a0", "hash_algorithm": "sha256", "size_bytes": 12355}, "description": "包含模型权重、超参数和预处理器的PyTorch模型资产包。"}, "obmiq_training.data_snapshots.final_training_history": {"path": "obmiq_training_pytorch/final_training_history.csv", "type": "data", "metadata": {"hash": "ef8d0194987ff68099a6807194d365ae1c54039d14d494380f2bee80d58e5bd4", "hash_algorithm": "sha256", "size_bytes": 1992}, "description": "最终模型训练过程中的损失变化历史。"}, "obmiq_training.plots.final_training_history": {"path": "obmiq_training_pytorch/final_training_history.png", "type": "image", "metadata": {"hash": "9cc277a86607e82c5aac85bdbc6cf49e4b17fb5b4aa1aebae7c3ec7d026bdc0a", "hash_algorithm": "sha256", "size_bytes": 216785}, "description": "最终模型训练的损失曲线图。"}, "obmiq_training.data_snapshots.final_model_evaluation": {"path": "obmiq_training_pytorch/final_model_evaluation.csv", "type": "data", "metadata": {"hash": "318e3f42b0a7fec36eafb653038731de0255f547a1e95a2e721225d8e4f9ffd9", "hash_algorithm": "sha256", "size_bytes": 2168848}, "description": "最终模型在全部训练数据上的预测和残差结果。"}, "obmiq_training.plots.eval_crossplot_dt2_p50": {"path": "obmiq_training_pytorch/eval_crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "621243815d6cca0542312e3d52ca312f120e928fe198b623a75efe32cfeddbdf", "hash_algorithm": "sha256", "size_bytes": 606964}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50"}, "obmiq_training.plots.eval_crossplot_dphit_nmr": {"path": "obmiq_training_pytorch/eval_crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "798e942d3ef07a0712b54e4a50ebbc67926b832c19ff721c8bb8b585d3bc28b4", "hash_algorithm": "sha256", "size_bytes": 772255}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr"}, "obmiq_training.plots.eval_residuals_plot_dt2_p50": {"path": "obmiq_training_pytorch/eval_residuals_plot_dt2_p50.png", "type": "image", "metadata": {"hash": "c84f858a2bb6a0e3363b4f8bfa05b0f2f87a71de9f19b6b14bfee4dfccdde0eb", "hash_algorithm": "sha256", "size_bytes": 685686}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50"}, "obmiq_training.plots.eval_residuals_plot_dphit_nmr": {"path": "obmiq_training_pytorch/eval_residuals_plot_dphit_nmr.png", "type": "image", "metadata": {"hash": "fafff683ca65c4013d598571b9708e730bd8b984f967313ed28a970deed260a6", "hash_algorithm": "sha256", "size_bytes": 991645}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr"}, "obmiq_training.plots.eval_residuals_hist_dt2_p50": {"path": "obmiq_training_pytorch/eval_residuals_hist_dt2_p50.png", "type": "image", "metadata": {"hash": "693743cdb5b4f4b6abc93a7a3c92139ebf976ccecb5c0705f44b1cd3115405e3", "hash_algorithm": "sha256", "size_bytes": 170697}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50"}, "obmiq_training.plots.eval_residuals_hist_dphit_nmr": {"path": "obmiq_training_pytorch/eval_residuals_hist_dphit_nmr.png", "type": "image", "metadata": {"hash": "22bcd805c81c00f4ceda482d165e9ee663e3801bbe2f0e80f3639105d6539687", "hash_algorithm": "sha256", "size_bytes": 201155}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr"}, "obmiq_training.plots.captum_ig_summary_DT2_P50": {"path": "obmiq_training_pytorch/captum_ig_summary_DT2_P50.png", "type": "image", "metadata": {}, "description": "Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。"}, "obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50": {"path": "obmiq_training_pytorch/captum_ig_summary_DT2_P50_data.csv", "type": "data", "metadata": {"hash": "571bc8bcfb40abf2e3a6a141d292a124d76620f7bb5018a3fbbef8c2e65c4313", "hash_algorithm": "sha256", "size_bytes": 363590}, "description": "Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50)"}, "obmiq_training.plots.captum_ig_summary_DPHIT_NMR": {"path": "obmiq_training_pytorch/captum_ig_summary_DPHIT_NMR.png", "type": "image", "metadata": {}, "description": "Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。"}, "obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR": {"path": "obmiq_training_pytorch/captum_ig_summary_DPHIT_NMR_data.csv", "type": "data", "metadata": {"hash": "1762b0a26ce066958ddfa73d36a5f80431da4ebc6f95cd81544b4c4d72cd2510", "hash_algorithm": "sha256", "size_bytes": 398416}, "description": "Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR)"}, "obmiq_training.data_snapshots.captum_sequence_attributions_dir": {"path": "obmiq_training_pytorch/captum_sequence_attributions_dir", "type": "unknown", "metadata": {"size_bytes": 4096}}, "obmiq_training.plots.captum_saliency_examples_dir": {"path": "obmiq_training_pytorch/captum_saliency_examples_dir", "type": "unknown", "metadata": {"size_bytes": 12288}}, "obmiq_training.models.onnx_model": {"path": "obmiq_training_pytorch/obmiq_model.onnx", "type": "unknown", "metadata": {"hash": "75b535ac37edc86fced097467b3b1f495d0067047581aca75b9f6e9ab42b696a", "hash_algorithm": "sha256", "size_bytes": 6591}, "description": "可用于跨平台部署的ONNX格式模型。"}, "obmiq_prediction.datasets.predictions": {"path": "obmiq_prediction_pytorch/predictions.csv", "type": "data", "metadata": {"hash": "6dd589fee1caf82c233cbe36c9814893c9592188ac55283f6c2bfe5e237b3872", "hash_algorithm": "sha256", "size_bytes": 3485824}, "description": "包含原始输入和模型预测结果的数据集"}, "obmiq_prediction.plots.crossplot_dt2_p50": {"path": "obmiq_prediction_pytorch/crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "621243815d6cca0542312e3d52ca312f120e928fe198b623a75efe32cfeddbdf", "hash_algorithm": "sha256", "size_bytes": 606964}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50"}, "obmiq_prediction.plots.crossplot_dphit_nmr": {"path": "obmiq_prediction_pytorch/crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "546626a5802e2a808bbd794884fbb9417664afac40b5ecd1fdc97b382425f7fe", "hash_algorithm": "sha256", "size_bytes": 773644}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr"}, "obmiq_prediction.plots.residuals_plot_dt2_p50": {"path": "obmiq_prediction_pytorch/residuals_plot_dt2_p50.png", "type": "image", "metadata": {"hash": "c84f858a2bb6a0e3363b4f8bfa05b0f2f87a71de9f19b6b14bfee4dfccdde0eb", "hash_algorithm": "sha256", "size_bytes": 685686}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50"}, "obmiq_prediction.plots.residuals_plot_dphit_nmr": {"path": "obmiq_prediction_pytorch/residuals_plot_dphit_nmr.png", "type": "image", "metadata": {"hash": "4e0452d43f731bd1f480411038f07bc46ff78e4f65e3cb1e0b6cda52e0d367e4", "hash_algorithm": "sha256", "size_bytes": 993830}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr"}, "obmiq_prediction.plots.residuals_hist_dt2_p50": {"path": "obmiq_prediction_pytorch/residuals_hist_dt2_p50.png", "type": "image", "metadata": {"hash": "6b58f6c0e9d301b0e70dc9a4796f037c08dba32de1f05604382f2ee558756d37", "hash_algorithm": "sha256", "size_bytes": 170727}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50"}, "obmiq_prediction.plots.residuals_hist_dphit_nmr": {"path": "obmiq_prediction_pytorch/residuals_hist_dphit_nmr.png", "type": "image", "metadata": {"hash": "9ceff3117457614ef1a14473a0f03bddb3125a5b9c82fd3e8eb187e8020f59e6", "hash_algorithm": "sha256", "size_bytes": 201512}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr"}}}