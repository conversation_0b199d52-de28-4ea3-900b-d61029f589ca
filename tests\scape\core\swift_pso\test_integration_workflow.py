"""tests.scape.core.swift_pso.test_integration_workflow - SWIFT-PSO集成测试

测试完整的端到端工作流，验证训练、预测、可视化三个步骤的协同工作能力。

Architecture
------------
层次/依赖: tests/scape/core/swift_pso层，集成测试
设计原则: 端到端验证、数据流转模拟、契约测试

References
----------
- `docs/DDS/refactor/swift_pso重构.md` §5.3 - 集成测试详细设计
"""

from __future__ import annotations

import numpy as np
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle

from scape.core.swift_pso import (
    run_swift_pso_prediction_step,
    run_swift_pso_training_step,
    run_tsne_visualization_step,
    SwiftPsoArtifactHandler,
    SwiftPsoPredictionConfig,
    SwiftPsoTrainingConfig,
    SwiftPsoPredictionArtifacts,
    SwiftPsoTrainingArtifacts,
    TsneVisualArtifacts,
    TsneVisualConfig,
	TsnePlotProfiles,
)


def test_full_workflow_success(
    run_context: RunContext,
    swift_pso_train_bundle: WpDataFrameBundle,
    swift_pso_pred_bundle: WpDataFrameBundle,
):
    """测试完整的端到端工作流，验证训练、预测、可视化三个步骤的协同工作能力。

    此测试用例扮演"工作流驱动脚本"的角色，负责：
    1. 运行训练步骤。
    2. 加载训练产物。
    3. 将产物传递给下游的预测和可视化步骤。
    4. 验证所有步骤的产物都已正确生成和注册。
    """
    # ==========================================================================
    # 1. 准备配置和数据
    # ==========================================================================
    handler = SwiftPsoArtifactHandler()

    # 根据方法说明书，动态计算数据依赖性参数
    t2_p50_ref = swift_pso_train_bundle.get_curve_statistic('T2_P50', 'median')
    phit_nmr_ref = swift_pso_train_bundle.get_curve_statistic('DPHIT_NMR', 'median')
    curve_map = swift_pso_train_bundle.curve_to_columns_map
    # 根据方法说明书，显式生成T2时间轴 (0.1ms to 10000ms, 64 bins)
    t2_time = np.logspace(np.log10(0.1), np.log10(10000), 64)

    # 准备共享的数据依赖参数和配置
    # 注意：这里的边界值直接从默认配置中获取，以保持一致性
    parameters_boundaries = SwiftPsoTrainingConfig.create_default().pso_config_lowo["parameters_boundaries"]
    shared_data_params = {
        "t2_p50_ref": t2_p50_ref,
        "phit_nmr_ref": phit_nmr_ref,
        "curve_to_columns_map": curve_map,
        "t2_time": t2_time,
        "t2_range_min": t2_time.min(),
        "t2_range_max": t2_time.max(),
        "parameters_boundaries": parameters_boundaries,
    }

    # 为LOWO和Fine-Tuning阶段创建独立的、具有不同超参数的测试配置
    # 这能更准确地模拟真实工作流
    pso_config_lowo_test = {
        "n_particles": 10,  # 不同的粒子数
        "max_iterations": 5,  # 不同的迭代次数
        "w_strategy": ("linear_decay", 0.9, 0.4),
        "c1": 1.8,
        "c2": 1.8,
        "loss_function_mode": "bootstrap",
        "boundary_strategy": "hard_reflection",
        "enable_early_stopping": True,
        "early_stopping_mode": "lowo",
        **shared_data_params
    }

    pso_config_finetune_test = {
        "n_particles": 8,   # 不同的粒子数
        "max_iterations": 3,   # 不同的迭代次数
        "w_strategy": ("linear_decay", 0.8, 0.3),
        "c1": 1.6,
        "c2": 1.6,
        "loss_function_mode": "finetune",
        "boundary_strategy": "boundary_jitter",
        "enable_early_stopping": True,
        "early_stopping_mode": "finetune",
        **shared_data_params
    }

    # 使用最小化配置以加速测试
    training_config = SwiftPsoTrainingConfig(
        bootstrap_iterations=1,
        narrow_window_factor=0.3,
        # 为两个阶段注入各自独立的配置
        pso_config_lowo=pso_config_lowo_test,
        pso_config_finetune=pso_config_finetune_test,
    )

    prediction_config = SwiftPsoPredictionConfig(
        t2_range_min=0.5,
        t2_range_max=2000.0,
    )
    # 创建一个自定义的可视化配置，覆盖部分默认值
    visual_config = TsneVisualConfig(
        perplexity=5,
        n_iter=251,
        n_clusters=2  # 为测试指定一个明确的聚类数量（与Bootstrap运行数量匹配）
    )

    # ==========================================================================
    # 2. 运行训练步骤 (Step 1)
    # ==========================================================================
    run_swift_pso_training_step(
        config=training_config,
        ctx=run_context,
        train_bundle=swift_pso_train_bundle,
        backend="cpu",
    )

    # 断言训练产物并加载依赖
    assert SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value in run_context.manifest["artifacts"]
    assert SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value in run_context.manifest["artifacts"]

    model_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.FINAL_PARAMETERS)
    model_assets = handler.load_parameters(model_params_path)

    tsne_data_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)
    tsne_source_data = handler.load_dataframe(tsne_data_path)

    # ==========================================================================
    # 3. 运行预测步骤 (Step 2)
    # ==========================================================================
    run_swift_pso_prediction_step(
        config=prediction_config,
        ctx=run_context,
        model_assets=model_assets,
        prediction_bundle=swift_pso_pred_bundle,
        t2_time=t2_time,
        output_curve_names=("K_PRED_SWIFT", "VMICRO_PRED_SWIFT", "VMESO_PRED_SWIFT", "VMACRO_PRED_SWIFT"),
        backend="cpu",
    )

    # 断言预测产物
    assert SwiftPsoPredictionArtifacts.PREDICTED_PERMEABILITY.value in run_context.manifest["artifacts"]
    pred_path = run_context.get_artifact_path(SwiftPsoPredictionArtifacts.PREDICTED_PERMEABILITY)
    assert pred_path.exists()

    # ==========================================================================
    # 4. 运行可视化步骤 (Step 3)
    # ==========================================================================
    run_tsne_visualization_step(
        config=visual_config,
        ctx=run_context,
        tsne_source_data=tsne_source_data,
        plot_profile=plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY),
    )

    # 断言可视化产物（数据快照原则）
    assert TsneVisualArtifacts.TSNE_PLOT.value in run_context.manifest["artifacts"]
    assert TsneVisualArtifacts.TSNE_PLOT_DATA.value in run_context.manifest["artifacts"]
    assert TsneVisualArtifacts.TSNE_PLOT_PROFILE.value in run_context.manifest["artifacts"]
    plot_path = run_context.get_artifact_path(TsneVisualArtifacts.TSNE_PLOT)
    assert plot_path.exists()

    # 断言新添加的功能
    # 验证轮廓系数指标已记录（可能为None，当样本数量不足时）
    assert "silhouette_score" in run_context.manifest["metrics"]["swift_pso_visualization"]
    silhouette_score = run_context.manifest["metrics"]["swift_pso_visualization"]["silhouette_score"]
    # 轮廓系数可能为None（样本数量不足时）或者是一个数值
    assert silhouette_score is None or isinstance(silhouette_score, (int, float))

    # 验证数据快照包含聚类标签
    tsne_result_path = run_context.get_artifact_path(TsneVisualArtifacts.TSNE_PLOT_DATA)
    tsne_result_df = handler.load_dataframe(tsne_result_path)
    assert 'cluster_label' in tsne_result_df.columns

    # ==========================================================================
    # 5. 验证最终运行状态
    # ==========================================================================
    # 手动标记运行成功，这样状态会变成COMPLETED
    run_context.success()
    assert run_context.manifest["status"] == "COMPLETED"
