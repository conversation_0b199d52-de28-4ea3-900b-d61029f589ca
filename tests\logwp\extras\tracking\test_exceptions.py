"""测试异常处理的功能。

测试tracking包的异常层次结构，包括：
- 异常继承关系
- 异常上下文信息
- 异常属性和消息
- 异常链保持
"""

from __future__ import annotations

import pytest

from logwp.infra.exceptions import WpError, ErrorContext
from logwp.extras.tracking.exceptions import (
    TrackingError,
    RunExistsError,
    RunNotFoundError,
    ArtifactNotFoundError,
    ModelNotRegisteredError,
    RegistryError,
    ManifestError,
    ArtifactIOError,
)


class TestExceptionHierarchy:
    """测试异常继承层次。"""

    def test_tracking_error_inheritance(self):
        """测试TrackingError继承关系。"""
        error = TrackingError("test message")

        assert isinstance(error, WpError)
        assert isinstance(error, Exception)

    def test_specific_exceptions_inheritance(self):
        """测试具体异常的继承关系。"""
        exceptions = [
            RunExistsError("test"),
            RunNotFoundError("test"),
            ArtifactNotFoundError("test"),
            ModelNotRegisteredError("test"),
            RegistryError("test"),
            ManifestError("test"),
            ArtifactIOError("test"),
        ]

        for exc in exceptions:
            assert isinstance(exc, TrackingError)
            assert isinstance(exc, WpError)
            assert isinstance(exc, Exception)


class TestRunExistsError:
    """测试RunExistsError异常。"""

    def test_basic_creation(self):
        """测试基本创建。"""
        error = RunExistsError("Run already exists")

        assert str(error) == "Run already exists"
        assert error.run_dir is None
        assert error.existing_run_id is None

    def test_with_attributes(self):
        """测试带属性创建。"""
        error = RunExistsError(
            "Run directory already exists",
            run_dir="output/run-001",
            existing_run_id="20250717-100000-abc123"
        )

        assert error.run_dir == "output/run-001"
        assert error.existing_run_id == "20250717-100000-abc123"

    def test_with_context(self):
        """测试带上下文创建。"""
        context = ErrorContext(
            operation="create_run",
            file_path="output/run-001"
        )

        error = RunExistsError(
            "Run already exists",
            context=context
        )

        assert error.context == context


class TestRunNotFoundError:
    """测试RunNotFoundError异常。"""

    def test_basic_creation(self):
        """测试基本创建。"""
        error = RunNotFoundError("Run not found")

        assert str(error) == "Run not found"
        assert error.run_dir is None
        assert error.missing_component is None

    def test_with_attributes(self):
        """测试带属性创建。"""
        error = RunNotFoundError(
            "Run manifest not found",
            run_dir="output/run-999",
            missing_component="manifest"
        )

        assert error.run_dir == "output/run-999"
        assert error.missing_component == "manifest"


class TestArtifactNotFoundError:
    """测试ArtifactNotFoundError异常。"""

    def test_basic_creation(self):
        """测试基本创建。"""
        error = ArtifactNotFoundError("Artifact not found")

        assert str(error) == "Artifact not found"
        assert error.artifact_name is None
        assert error.run_id is None
        assert error.registered_artifacts == []

    def test_with_attributes(self):
        """测试带属性创建。"""
        registered_artifacts = ["model.pkl", "config.yaml"]

        error = ArtifactNotFoundError(
            "Artifact 'results.json' not found",
            artifact_name="results.json",
            run_id="run-001",
            registered_artifacts=registered_artifacts
        )

        assert error.artifact_name == "results.json"
        assert error.run_id == "run-001"
        assert error.registered_artifacts == registered_artifacts

    def test_default_empty_list(self):
        """测试默认空列表。"""
        error = ArtifactNotFoundError("test")

        assert error.registered_artifacts == []


class TestModelNotRegisteredError:
    """测试ModelNotRegisteredError异常。"""

    def test_basic_creation(self):
        """测试基本创建。"""
        error = ModelNotRegisteredError("Model not registered")

        assert str(error) == "Model not registered"
        assert error.model_name is None
        assert error.version is None
        assert error.available_models == []

    def test_with_attributes(self):
        """测试带属性创建。"""
        available_models = ["model-a", "model-b"]

        error = ModelNotRegisteredError(
            "Model 'unknown-model' not found",
            model_name="unknown-model",
            version="1.0.0",
            available_models=available_models
        )

        assert error.model_name == "unknown-model"
        assert error.version == "1.0.0"
        assert error.available_models == available_models


class TestRegistryError:
    """测试RegistryError异常。"""

    def test_basic_creation(self):
        """测试基本创建。"""
        error = RegistryError("Registry error")

        assert str(error) == "Registry error"
        assert error.registry_path is None
        assert error.operation is None

    def test_with_attributes(self):
        """测试带属性创建。"""
        error = RegistryError(
            "Failed to write registry",
            registry_path="/path/to/registry.json",
            operation="write"
        )

        assert error.registry_path == "/path/to/registry.json"
        assert error.operation == "write"


class TestManifestError:
    """测试ManifestError异常。"""

    def test_basic_creation(self):
        """测试基本创建。"""
        error = ManifestError("Manifest error")

        assert str(error) == "Manifest error"
        assert error.manifest_path is None
        assert error.operation is None
        assert error.invalid_field is None

    def test_with_attributes(self):
        """测试带属性创建。"""
        error = ManifestError(
            "Invalid manifest format",
            manifest_path="output/run-001/manifest.json",
            operation="validate",
            invalid_field="start_time_utc"
        )

        assert error.manifest_path == "output/run-001/manifest.json"
        assert error.operation == "validate"
        assert error.invalid_field == "start_time_utc"


class TestArtifactIOError:
    """测试ArtifactIOError异常。"""

    def test_basic_creation(self):
        """测试基本创建。"""
        error = ArtifactIOError("Artifact I/O error")

        assert str(error) == "Artifact I/O error"
        assert error.source_path is None
        assert error.target_path is None
        assert error.operation is None

    def test_with_attributes(self):
        """测试带属性创建。"""
        error = ArtifactIOError(
            "Failed to copy artifact",
            source_path="/tmp/model.pkl",
            target_path="output/run-001/models/model.pkl",
            operation="copy"
        )

        assert error.source_path == "/tmp/model.pkl"
        assert error.target_path == "output/run-001/models/model.pkl"
        assert error.operation == "copy"


class TestExceptionChaining:
    """测试异常链保持。"""

    def test_exception_chaining(self):
        """测试异常链的保持。"""
        original_error = ValueError("Original error")

        try:
            raise original_error
        except ValueError as e:
            tracking_error = TrackingError("Tracking error occurred")
            tracking_error.__cause__ = e

        assert tracking_error.__cause__ is original_error
        assert isinstance(tracking_error.__cause__, ValueError)

    def test_nested_exception_handling(self):
        """测试嵌套异常处理。"""
        def inner_function():
            raise FileNotFoundError("File not found")

        def middle_function():
            try:
                inner_function()
            except FileNotFoundError as e:
                artifact_error = ArtifactIOError("Failed to process artifact")
                artifact_error.__cause__ = e
                raise artifact_error

        def outer_function():
            try:
                middle_function()
            except ArtifactIOError as e:
                tracking_error = TrackingError("Tracking operation failed")
                tracking_error.__cause__ = e
                raise tracking_error

        with pytest.raises(TrackingError) as exc_info:
            outer_function()

        # 验证异常链
        tracking_error = exc_info.value
        assert isinstance(tracking_error.__cause__, ArtifactIOError)
        assert isinstance(tracking_error.__cause__.__cause__, FileNotFoundError)


class TestExceptionContext:
    """测试异常上下文信息。"""

    def test_context_integration(self):
        """测试上下文信息集成。"""
        context = ErrorContext(
            operation="load_manifest",
            file_path="output/run-001/manifest.json",
            line_number=15,
            reason="Invalid JSON format"
        )

        error = ManifestError(
            "Failed to parse manifest",
            manifest_path="output/run-001/manifest.json",
            operation="read",
            context=context
        )

        assert error.context == context
        assert error.context.operation == "load_manifest"
        assert error.context.file_path == "output/run-001/manifest.json"

    def test_multiple_context_sources(self):
        """测试多个上下文信息源。"""
        # 异常自身的属性
        error = ArtifactIOError(
            "Copy failed",
            source_path="/tmp/file.txt",
            target_path="/output/file.txt",
            operation="copy"
        )

        # ErrorContext提供的上下文
        context = ErrorContext(
            operation="log_artifact",
            additional_info={"retry_count": 3}
        )
        error.context = context

        # 验证两种上下文信息都可用
        assert error.source_path == "/tmp/file.txt"
        assert error.operation == "copy"
        assert error.context.operation == "log_artifact"
        assert error.context.additional_info["retry_count"] == 3
