"""scape.core.baselines.timur.config - Timur/Coates基准模型配置

为Timur/Coates基准模型的训练和预测步骤定义类型安全的Pydantic配置模型。

"""

from __future__ import annotations

from pydantic import BaseModel, Field


class TimurModelParameters(BaseModel):
    """定义Timur/Coates模型优化后参数的结构，用于验证和解析。"""
    KTIM_A: float = Field(..., description="Timur/Coates渗透率方程系数")
    PHIT_EXP: float = Field(..., description="孔隙度指数")
    KTIM_EXP: float = Field(..., description="自由流体指数")


class TimurTrainingConfig(BaseModel):
    """Timur/Coates基准模型训练步骤的配置。

    Timur/Coates模型的参数 (KTIM_A, PHIT_EXP) 是通过优化数据得到的。
    此配置类包含一个模型物理参数 `bfv_min`。
    """
    bfv_min: float = Field(0.02, description="Timur/Coates模型中BFV的最小值 (v/v)", gt=0)


class TimurPredictionConfig(BaseModel):
    """Timur/Coates基准模型预测步骤的配置。

    预测步骤同样需要 `bfv_min` 参数来确保计算逻辑的一致性。
    """
    bfv_min: float = Field(0.02, description="Timur/Coates模型中BFV的最小值 (v/v)", gt=0)
