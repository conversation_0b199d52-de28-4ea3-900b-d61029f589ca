from __future__ import annotations

"""logwp.models.types.aliases - 类型别名定义

业务语义化的类型别名定义，提供更好的代码可读性和类型安全。

Architecture
------------
层次/依赖: types层类型别名，仅依赖标准库
设计原则: 业务语义、类型安全、可读性、文档化
性能特征: 零运行时开销、编译时检查、IDE友好

CIIA迁移指南
-----------
本文件中的标识符类型别名（WpWellName、WpCurveName、WpDatasetName、WpAttributeName）
正在从str类型迁移到WpIdentifier类型，以支持CIIA（Case-Insensitive Identifier Architecture）。

**当前状态**：
- 类型别名：保持str类型（向后兼容）
- 核心组件：已更新为WpIdentifier类型
- API方法：支持str和WpIdentifier两种类型

**迁移建议**：
1. **新代码**：使用WpIdentifier类型获得CIIA支持
2. **现有代码**：保持不变，自动获得CIIA支持（通过底层CaseInsensitiveDict）
3. **API调用**：可以混合使用str和WpIdentifier类型

**示例**：
```python
# 向后兼容（现有代码无需修改）
dataset = project.get_dataset("OBMIQ_logs")

# 推荐用法（新代码）
from logwp.models.types import WpIdentifier
dataset_id = WpIdentifier("OBMIQ_logs")
dataset = project.get_dataset(dataset_id)  # 大小写不敏感

# 混合使用
head.set_attribute("version", "1.0")  # str类型
head.set_attribute(WpIdentifier("Project_Name"), "Santos")  # WpIdentifier类型
```

遵循CCG规范：
- TS-1: 所有类型别名必须有完整类型注解
- TS-4: 使用现代语法和Python 3.11+联合类型语法
- CS-1: 命名约定遵循业务语义

References
----------
- 《SCAPE_CCG_编码与通用规范.md》TS-1 - 类型注解覆盖
- 《SCAPE_DDS_详细设计_logwp.md》§3.1.3 - 类型别名设计
- 《SCAPE_SAD_软件架构设计.md》§4.6 - CIIA架构设计
"""

from typing import Union, Any
from pathlib import Path
import pandas as pd
import numpy as np

# 数据相关类型别名
WpDataFrame = pd.DataFrame
"""测井数据DataFrame类型别名。

用于标识包含测井数据的pandas DataFrame，提供业务语义。

Examples:
    >>> def process_logs(data: WpDataFrame) -> WpDataFrame:
    ...     return data.dropna()
"""

WpArray = np.ndarray
"""测井数据数组类型别名。

用于标识包含测井数据的numpy数组，支持GPU计算。

Examples:
    >>> def compute_features(data: WpArray) -> WpArray:
    ...     return np.log(data + 1)
"""

WpSeries = pd.Series
"""测井曲线Series类型别名。

用于标识单条测井曲线的pandas Series。

Examples:
    >>> def normalize_curve(curve: WpSeries) -> WpSeries:
    ...     return (curve - curve.mean()) / curve.std()
"""

# 业务标识类型别名（向后兼容）
WpWellName = str
"""井名类型别名（向后兼容）。

用于标识井名字符串，提供业务语义和类型安全。
注意：推荐使用WpWellIdentifier获得CIIA大小写不敏感支持。

Examples:
    >>> def get_well_data(well: WpWellName) -> WpDataFrame:
    ...     return load_well_logs(well)

    >>> wells: list[WpWellName] = ["C-1", "C-2", "C-3", "T-1"]

    >>> # 推荐使用CIIA标识符
    >>> from logwp.models.types import WpWellIdentifier
    >>> well_id = WpWellIdentifier("C-1")
"""

WpCurveName = str
"""曲线名类型别名（向后兼容）。

用于标识测井曲线名称，支持WFS v1.0二维组合曲线命名。
注意：推荐使用WpIdentifier获得CIIA大小写不敏感支持。

CIIA支持说明：
- 当前类型：str（向后兼容）
- 推荐类型：WpIdentifier（CIIA大小写不敏感）
- 迁移建议：在新代码中使用WpIdentifier，现有代码保持不变

Examples:
    >>> # 当前用法（向后兼容）
    >>> def get_curve(name: WpCurveName) -> WpSeries:
    ...     return dataset.df[name]

    >>> curves: list[WpCurveName] = ["GR", "PHIT_NMR", "T2_P50"]

    >>> # 推荐用法（CIIA支持）
    >>> from logwp.models.types import WpIdentifier
    >>> curve_id = WpIdentifier("GR")
    >>> curve_data = dataset.df[str(curve_id)]  # 自动大小写不敏感
"""

WpDatasetName = str
"""数据集名称类型别名（向后兼容）。

用于标识数据集名称，对应WP文件中的工作表名。
注意：推荐使用WpIdentifier获得CIIA大小写不敏感支持。

CIIA支持说明：
- 当前类型：str（向后兼容）
- 推荐类型：WpIdentifier（CIIA大小写不敏感）
- 迁移建议：在新代码中使用WpIdentifier，现有代码保持不变
- 架构更新：WpDepthIndexedDatabaseBase.name已更新为WpIdentifier类型

Examples:
    >>> # 当前用法（向后兼容）
    >>> def load_dataset(name: WpDatasetName) -> WpDataFrame:
    ...     return project.get_dataset(name).df

    >>> datasets: list[WpDatasetName] = ["OBMIQ_logs", "K_Label", "PLT"]

    >>> # 推荐用法（CIIA支持）
    >>> from logwp.models.types import WpIdentifier
    >>> dataset_id = WpIdentifier("OBMIQ_logs")
    >>> dataset = project.get_dataset(dataset_id)  # 自动大小写不敏感
"""

WpExtAttributeName = str
"""属性名称类型别名（向后兼容）。

用于标识WFS v1.0属性名称，支持5层作用域查找。
注意：推荐使用WpIdentifier获得CIIA大小写不敏感支持。

CIIA支持说明：
- 当前类型：str（向后兼容）
- 推荐类型：WpIdentifier（CIIA大小写不敏感）
- 迁移建议：在新代码中使用WpIdentifier，现有代码保持不变
- API更新：WpHead的所有方法已支持WpIdentifier类型参数

Examples:
    >>> # 当前用法（向后兼容）
    >>> def get_attribute(name: WpAttributeName) -> Any:
    ...     return head.get_attribute(name)

    >>> attrs: list[WpAttributeName] = ["Version", "T2_AXIS", "Sampling_Rate"]

    >>> # 推荐用法（CIIA支持）
    >>> from logwp.models.types import WpIdentifier
    >>> attr_id = WpIdentifier("Version")
    >>> value = head.get_attribute(attr_id)  # 自动大小写不敏感
    >>> head.set_attribute(WpIdentifier("Project_Name"), "Santos_Field")
"""

# 曲线基本属性相关类型别名
WpCurveMetadataDict = dict[str, Any]
"""曲线元数据字典类型别名。

用于曲线基本属性的字典表示，便于序列化和传输。

Examples:
    >>> curve_meta: WpCurveMetadataDict = {
    ...     "name": "GR",
    ...     "unit": "API",
    ...     "data_type": "FLOAT",
    ...     "category": "LOGGING",
    ...     "dimension": "1D"
    ... }
"""

# 文件路径类型别名
WpFilePath = Union[str, Path]
"""文件路径类型别名。

用于标识文件路径，支持字符串和Path对象。

Examples:
    >>> def read_wp_file(path: WpFilePath) -> WpProject:
    ...     return WpExcelReader().read(str(path))

    >>> file_path: WpFilePath = "data/santos_data.wp.xlsx"
    >>> file_path: WpFilePath = Path("data/santos_data.wp.xlsx")
"""

WpSheetName = str
"""工作表名称类型别名。

用于标识Excel工作表名称，对应WFS v1.0工作表规范。

Examples:
    >>> def read_sheet(name: WpSheetName) -> pd.DataFrame:
    ...     return pd.read_excel(file_path, sheet_name=name)

    >>> sheets: list[WpSheetName] = ["_Head_Info", "_Well_Map", "OBMIQ_logs"]
"""

# 数值类型别名
WpDepthValue = float
"""深度值类型别名。

用于标识深度值，单位为米(m)。

Examples:
    >>> def filter_by_depth(start: WpDepthValue, end: WpDepthValue) -> WpDataFrame:
    ...     return data[(data.MD >= start) & (data.MD <= end)]

    >>> top_depth: WpDepthValue = 2500.0
    >>> bottom_depth: WpDepthValue = 3000.0
"""

WpTimeValue = float
"""时间值类型别名。

用于标识T2时间值，单位为毫秒(ms)。

Examples:
    >>> def process_t2_spectrum(t2_values: list[WpTimeValue]) -> WpArray:
    ...     return np.array(t2_values)

    >>> t2_cutoff: WpTimeValue = 33.0  # ms
"""

WpPorosityValue = float
"""孔隙度值类型别名。

用于标识孔隙度值，范围[0, 1]。

Examples:
    >>> def validate_porosity(phi: WpPorosityValue) -> bool:
    ...     return 0.0 <= phi <= 1.0

    >>> porosity: WpPorosityValue = 0.15  # 15%
"""

WpPermeabilityValue = float
"""渗透率值类型别名。

用于标识渗透率值，单位为毫达西(mD)。

Examples:
    >>> def validate_permeability(k: WpPermeabilityValue) -> bool:
    ...     return k > 0.0

    >>> permeability: WpPermeabilityValue = 125.5  # mD
"""

# 配置类型别名
WpChunkSize = int
"""数据块大小类型别名。

用于标识数据分块处理的块大小。

Examples:
    >>> def process_in_chunks(data: WpDataFrame, chunk_size: WpChunkSize) -> WpDataFrame:
    ...     return pd.concat([process_chunk(chunk) for chunk in np.array_split(data, chunk_size)])

    >>> chunk_size: WpChunkSize = 10000
"""

WpMemoryLimit = float
"""内存限制类型别名。

用于标识内存使用限制，单位为GB。

Examples:
    >>> def check_memory_usage(limit: WpMemoryLimit) -> bool:
    ...     return psutil.virtual_memory().used / (1024**3) < limit

    >>> memory_limit: WpMemoryLimit = 4.0  # 4GB
"""
