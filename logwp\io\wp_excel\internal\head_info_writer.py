"""井头信息表单写入服务。

严格按照WFS 5.2节规范实现_Head_Info表单写入功能。
支持ExtAttributeRecord的完整写入和Excel格式化。

Architecture
------------
层次/依赖: I/O层内部服务，被excel_writer调用
设计原则: WFS规范严格遵循、无状态函数、格式化支持
性能特征: 批量写入、内存优化、Excel格式化

Functions
---------
- write_head_info_sheet: 写入_Head_Info表单数据和格式化
- format_head_info_sheet: 格式化_Head_Info表单

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》§3.2.2 - 井头信息写入设计
- 《SCAPE_WFS_WP文件规范.md》§5.2 - _Head_Info表单规范
"""

from __future__ import annotations

import time
from typing import Any

import openpyxl
import structlog

from logwp.io.constants import WpXlsxKey
from logwp.io.exceptions import WpFileFormatError
from logwp.models.constants import WpDataType
from logwp.models.head import WpHead
from logwp.models.ext_attr import ExtAttributeRecord
from logwp.infra.exceptions import ErrorContext
from ..config import ExcelFormattingConfig
from .data_formatter import format_value_for_excel

logger = structlog.get_logger(__name__)

__all__ = ["write_head_info_sheet"]


def write_head_info_sheet(
    worksheet: openpyxl.Worksheet,
    head: WpHead,
    config: ExcelFormattingConfig
) -> None:
    """严格按照WFS 5.2节规范写入_Head_Info表单。

    WFS规范要求：
    1. 第一行：固定9列标题（S/DATASET/WELL/CURVE/ATTRIBUTE/TYPE/UNIT/VALUE/DESCRIPTION）
    2. 第二行起：ExtAttributeRecord数据，每行一个属性
    3. COMP类型：JSON对象转换为JSON字符串写入VALUE列
    4. 严格遵循WFS列结构和数据类型要求

    Args:
        worksheet: Excel工作表对象
        head: 井头信息对象
        config: 格式化配置

    Raises:
        WpFileFormatError: 井头信息写入失败
    """
    start_time = time.time()

    try:
        logger.info(
            "开始写入井头信息表单",
            total_attributes=len(head.attribute_records)
        )

        # 1. 写入表头行（第一行）
        _write_head_info_header(worksheet)

        # 2. 写入属性记录（第二行起）
        _write_attribute_records(worksheet, head.attribute_records, config)

        processing_time = time.time() - start_time
        logger.info(
            "井头信息表单写入完成",
            total_attributes=len(head.attribute_records),
            processing_time=round(processing_time, 3)
        )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(
            "井头信息表单写入失败",
            error_type=type(e).__name__,
            error_message=str(e),
            processing_time=round(processing_time, 3)
        )
        raise WpFileFormatError(
            f"井头信息表单写入失败: {e}",
            context=ErrorContext(
                operation="write_head_info_sheet",
                additional_info={"total_attributes": len(head.attribute_records)}
            )
        ) from e


def _write_head_info_header(worksheet: openpyxl.Worksheet) -> None:
    """写入_Head_Info表单的表头行。

    按照WFS 5.2节规范写入9列标题。使用worksheet.append()以兼容只写模式。

    Args:
        worksheet: Excel工作表对象
    """
    # 从常量模块获取WFS 5.2节规定的9列标题，确保与解析器一致
    headers = WpXlsxKey.expected_head_headers()
    worksheet.append(headers)

    logger.debug("井头信息表头写入完成", header_count=len(headers))


def _write_attribute_records(
    worksheet: openpyxl.Worksheet,
    records: list[ExtAttributeRecord],
    config: ExcelFormattingConfig
) -> None:
    """写入ExtAttributeRecord数据。

    Args:
        worksheet: Excel工作表对象
        records: ExtAttributeRecord列表
        config: 格式化配置
    """
    for record in records:
        # 根据WFS规范，对不同数据类型进行格式化
        if record.data_type.upper() == WpXlsxKey.TYPE_COMP:
            # 对于COMP类型，直接将其值（通常是dict或list）格式化为JSON字符串
            formatted_value = format_value_for_excel(record.value, WpDataType.COMP, config)
        else:
            # 对于其他基本类型，转换为WpDataType枚举进行格式化
            try:
                wp_data_type = WpDataType(record.data_type.upper())
                formatted_value = format_value_for_excel(record.value, wp_data_type, config)
            except ValueError:
                logger.warning(
                    "未知的属性数据类型，将按字符串处理",
                    data_type=record.data_type,
                    attribute=record.attribute
                )
                formatted_value = str(record.value)

        # 按照WFS 5.2节规定的顺序构造行数据，提高代码可读性和可维护性
        row_data = [
            record.category.value,
            str(record.dataset) if record.dataset else "",
            str(record.well) if record.well else "",
            str(record.curve) if record.curve else "",
            str(record.attribute),
            record.data_type,
            record.unit or "",
            formatted_value,
            record.description or ""
        ]
        # 使用append()方法批量追加行，性能远高于逐个单元格写入
        worksheet.append(row_data)

    logger.debug(
        "属性记录写入完成",
        record_count=len(records)
    )
