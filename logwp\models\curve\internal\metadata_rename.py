#!/usr/bin/env python3
"""曲线元数据重命名服务层模块。

提供CurveMetadata的曲线重命名功能，支持深度参考曲线和井标识符曲线的批量重命名。

Architecture
------------
层次/依赖: curve/service层，曲线元数据重命名业务逻辑
设计原则: Service Layer模式、无状态设计、类型安全
性能特征: 内存操作、原地修改、高效重命名

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_DDS_logwp_数据集合并.md》§3.3 - 曲线重命名服务设计
- 《SCAPE_SAD_软件架构设计.md》§4.12 - 内部服务层设计
"""

from __future__ import annotations

from typing import TYPE_CHECKING

from logwp.models.exceptions import WpCurveMetadataError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.curve.metadata import CurveMetadata

logger = get_logger(__name__)


def rename_depth_reference_curves(
    metadata: CurveMetadata,
    target_depth_curves: list[str],
    *,
    update_dataframe_column_names: bool = True
) -> dict[str, str]:
    """重命名深度参考曲线以匹配目标深度曲线列表（服务层实现）。

    **设计原则**：
    - SVC-1: 职责分离 - 专注曲线重命名业务逻辑
    - SVC-2: 无状态服务 - 纯函数设计，原地修改元数据
    - SVC-3: 内部使用 - 仅供logwp包内部调用
    - SVC-4: 操作原子性 - 完整的重命名操作或回滚
    - SVC-5: 参数通用性 - 依赖基础数据结构和接口
    - SVC-6: 命名语义化 - 函数名明确表示重命名操作

    Args:
        metadata: 曲线元数据对象（原地修改）
        target_depth_curves: 目标深度曲线名称列表
        update_dataframe_column_names: 是否同时更新dataframe_column_name属性

    Returns:
        dict[str, str]: 重命名映射 {原名称: 新名称}

    Raises:
        WpCurveMetadataError: 深度曲线数量不匹配时抛出异常

    Note:
        - 按深度曲线在元数据中的顺序进行一对一映射
        - 自动处理单深度（MD→DEPTH）和双深度（TOP_MD,BOT_MD→TOP_DEPTH,BOT_DEPTH）情形
        - 原地修改元数据对象，不创建新对象
        - 支持DataFrame列名同步更新

    Examples:
        >>> # 单深度情形
        >>> rename_mapping = rename_depth_reference_curves(
        ...     metadata, ['DEPTH']
        ... )
        >>> print(rename_mapping)  # {'MD': 'DEPTH'}

        >>> # 双深度情形
        >>> rename_mapping = rename_depth_reference_curves(
        ...     metadata, ['TOP_DEPTH', 'BOT_DEPTH']
        ... )
        >>> print(rename_mapping)  # {'TOP_MD': 'TOP_DEPTH', 'BOT_MD': 'BOT_DEPTH'}

    References:
        《SCAPE_DDS_logwp_数据集合并.md》§3.3.1 - 深度曲线重命名算法
    """
    logger.info(
        "开始深度参考曲线重命名",
        operation="rename_depth_reference_curves",
        target_curves=target_depth_curves,
        update_dataframe_columns=update_dataframe_column_names
    )

    # 获取当前深度参考曲线
    current_depth_curves = metadata.get_depth_reference_curves()

    # 验证数量匹配
    if len(current_depth_curves) != len(target_depth_curves):
        raise WpCurveMetadataError(
            f"深度曲线数量不匹配: 当前{len(current_depth_curves)}个，目标{len(target_depth_curves)}个",
            context=ErrorContext(
                operation="rename_depth_reference_curves",
                additional_info={
                    "current_depth_curves": current_depth_curves,
                    "target_depth_curves": target_depth_curves,
                    "current_count": len(current_depth_curves),
                    "target_count": len(target_depth_curves)
                }
            )
        )

    # 创建重命名映射
    rename_mapping = {}
    for old_name, new_name in zip(current_depth_curves, target_depth_curves):
        if old_name != new_name:
            rename_mapping[old_name] = new_name

    # 如果没有需要重命名的曲线，直接返回
    if not rename_mapping:
        logger.info(
            "深度参考曲线名称已匹配，无需重命名",
            operation="rename_depth_reference_curves",
            current_curves=current_depth_curves
        )
        return {}

    # 执行重命名
    _apply_curve_renaming(
        metadata=metadata,
        rename_mapping=rename_mapping,
        update_dataframe_column_names=update_dataframe_column_names
    )

    logger.info(
        "深度参考曲线重命名完成",
        operation="rename_depth_reference_curves",
        rename_mapping=rename_mapping,
        renamed_count=len(rename_mapping)
    )

    return rename_mapping


def rename_well_identifier_curves(
    metadata: CurveMetadata,
    target_well_curves: list[str],
    *,
    update_dataframe_column_names: bool = True
) -> dict[str, str]:
    """重命名井标识符曲线以匹配目标井标识符曲线列表（服务层实现）。

    **设计原则**：遵循与rename_depth_reference_curves相同的设计原则

    Args:
        metadata: 曲线元数据对象（原地修改）
        target_well_curves: 目标井标识符曲线名称列表
        update_dataframe_column_names: 是否同时更新dataframe_column_name属性

    Returns:
        dict[str, str]: 重命名映射 {原名称: 新名称}

    Raises:
        WpCurveMetadataError: 井标识符曲线数量不匹配时抛出异常

    Note:
        - 按井标识符曲线在元数据中的顺序进行一对一映射
        - 自动处理单井标识符（WELL→WELL_NAME）和多井标识符情形
        - 原地修改元数据对象，不创建新对象
        - 支持DataFrame列名同步更新

    Examples:
        >>> # 单井标识符情形
        >>> rename_mapping = rename_well_identifier_curves(
        ...     metadata, ['WELL_NAME']
        ... )
        >>> print(rename_mapping)  # {'WELL': 'WELL_NAME'}

        >>> # 多井标识符情形
        >>> rename_mapping = rename_well_identifier_curves(
        ...     metadata, ['WELL_NAME', 'FIELD_NAME']
        ... )
        >>> print(rename_mapping)  # {'WELL': 'WELL_NAME', 'FIELD': 'FIELD_NAME'}

    References:
        《SCAPE_DDS_logwp_数据集合并.md》§3.3.2 - 井标识符曲线重命名算法
    """
    logger.info(
        "开始井标识符曲线重命名",
        operation="rename_well_identifier_curves",
        target_curves=target_well_curves,
        update_dataframe_columns=update_dataframe_column_names
    )

    # 获取当前井标识符曲线
    current_well_curves = metadata.get_well_identifier_curves()

    # 验证数量匹配
    if len(current_well_curves) != len(target_well_curves):
        raise WpCurveMetadataError(
            f"井标识符曲线数量不匹配: 当前{len(current_well_curves)}个，目标{len(target_well_curves)}个",
            context=ErrorContext(
                operation="rename_well_identifier_curves",
                additional_info={
                    "current_well_curves": current_well_curves,
                    "target_well_curves": target_well_curves,
                    "current_count": len(current_well_curves),
                    "target_count": len(target_well_curves)
                }
            )
        )

    # 创建重命名映射
    rename_mapping = {}
    for old_name, new_name in zip(current_well_curves, target_well_curves):
        if old_name != new_name:
            rename_mapping[old_name] = new_name

    # 如果没有需要重命名的曲线，直接返回
    if not rename_mapping:
        logger.info(
            "井标识符曲线名称已匹配，无需重命名",
            operation="rename_well_identifier_curves",
            current_curves=current_well_curves
        )
        return {}

    # 执行重命名
    _apply_curve_renaming(
        metadata=metadata,
        rename_mapping=rename_mapping,
        update_dataframe_column_names=update_dataframe_column_names
    )

    logger.info(
        "井标识符曲线重命名完成",
        operation="rename_well_identifier_curves",
        rename_mapping=rename_mapping,
        renamed_count=len(rename_mapping)
    )

    return rename_mapping


def _apply_curve_renaming(
    metadata: CurveMetadata,
    rename_mapping: dict[str, str],
    *,
    update_dataframe_column_names: bool = True
) -> None:
    """应用曲线重命名映射到元数据对象（内部辅助函数）。

    Args:
        metadata: 曲线元数据对象（原地修改）
        rename_mapping: 重命名映射 {原名称: 新名称}
        update_dataframe_column_names: 是否同时更新dataframe_column_name属性

    Note:
        - 原地修改元数据对象的curves字典
        - 同时更新曲线的name和dataframe_column_name属性
        - 保持曲线对象的其他属性不变
        - 使用object.__setattr__绕过frozen限制
    """
    for old_name, new_name in rename_mapping.items():
        # 获取曲线对象
        curve_attrs = metadata.curves.get(old_name)
        if curve_attrs is None:
            logger.warning(
                "跳过不存在的曲线重命名",
                operation="_apply_curve_renaming",
                old_name=old_name,
                new_name=new_name
            )
            continue

        # 更新曲线的name属性
        object.__setattr__(curve_attrs, 'name', new_name)

        # 更新dataframe_column_name属性（如果需要）
        if update_dataframe_column_names:
            # DataFrame友好的列名（移除特殊字符）
            df_column_name = new_name.replace('[', '_').replace(']', '')
            object.__setattr__(curve_attrs, 'dataframe_column_name', df_column_name)

        # 更新元数据字典中的映射
        metadata.curves[new_name] = curve_attrs
        if old_name != new_name:
            del metadata.curves[old_name]

        logger.debug(
            "曲线重命名完成",
            operation="_apply_curve_renaming",
            old_name=old_name,
            new_name=new_name,
            dataframe_column_name=curve_attrs.dataframe_column_name
        )
