"""
logwp.extras.units

A package for handling physical quantities and unit conversions with
dimensional analysis, tailored for petroleum engineering.
"""

from .containers import Dimension, Unit
from .exceptions import DimensionalityError, UndefinedUnitError
from .quantity import Quantity
from .registry import UnitRegistry

# Create a default, global instance of the registry.
# This instance will be pre-populated with standard units and later,
# petroleum-specific units.
# Users can place a 'custom_units.yaml' file in their project root
# to extend or override the default units.
# The path can be configured via an environment variable as well.
ureg = UnitRegistry(definition_filepath='custom_units.yaml')

# Provide a convenient alias for the Quantity factory method.
# This allows for concise quantity creation, e.g., `units.Q_(10, 'm')`
Q_ = ureg.Quantity
