"""scape.core.obmiq.plot_profiles - OBMIQ绘图配置模板

定义并注册OBMIQ步骤所有图表的默认PlotProfile模板。
此模块在包被导入时自动执行，将模板注入全局注册表。

Architecture
------------
层次/依赖: scape/core层，OBMIQ组件的绘图配置模块
设计原则: 模板化、中心化注册、样式与逻辑解耦

References
----------
- 《logwp/extras/plotting/README.md》- 绘图系统设计文档
"""

from logwp.extras.plotting import PlotProfile, SaveConfig, registry

from .constants import ObmiqPlotProfiles


def _create_base_profile() -> PlotProfile:
    """创建OBMIQ模块的基础绘图模板。"""
    return PlotProfile(
        name=ObmiqPlotProfiles.BASE.value,
        rc_params={
            "font.family": "sans-serif",
            "axes.grid": True,
            "grid.linestyle": ":",
            "grid.alpha": 0.6,
        },
        figure_props={"figsize": (10, 7), "dpi": 150, "layout": "constrained"},
        save_config=SaveConfig(format=["png", "svg"], dpi=300),
    )


def _create_training_history_profile() -> PlotProfile:
    """创建训练历史曲线图的模板。"""
    return PlotProfile(
        name=ObmiqPlotProfiles.TRAINING_HISTORY.value,
        title_props={"label": "Model Training History", "fontsize": 16, "fontweight": "bold"},
        label_props={"xlabel": "Epoch", "ylabel": "Loss"},
        artist_props={
            "train_loss": {"label": "Training Loss", "color": "royalblue"},
            "val_loss": {"label": "Validation Loss", "color": "darkorange", "linestyle": "--"},
        },
    )


def _create_crossplot_profile() -> PlotProfile:
    """创建交会图的模板。"""
    return PlotProfile(
        name=ObmiqPlotProfiles.CROSSPLOT.value,
        figure_props={"figsize": (8, 8)},  # 交会图通常是正方形
        title_props={"label": "Prediction vs. Actual", "fontsize": 16, "fontweight": "bold"},
        label_props={"xlabel": "Actual Value", "ylabel": "Predicted Value"},
        artist_props={
            "scatter": {"s": 50, "alpha": 0.6, "edgecolor": "k", "linewidth": 0.5},
            "line_1_to_1": {"color": "red", "linestyle": "--", "label": "1:1 Line"},
        },
    )


def _create_residuals_plot_profile() -> PlotProfile:
    """创建残差图的模板。"""
    return PlotProfile(
        name=ObmiqPlotProfiles.RESIDUALS_PLOT.value,
        title_props={"label": "Residuals vs. Predicted Value", "fontsize": 16, "fontweight": "bold"},
        label_props={"xlabel": "Predicted Value", "ylabel": "Residual (Actual - Predicted)"},
        artist_props={
            "scatter": {"s": 50, "alpha": 0.6, "edgecolor": "k", "linewidth": 0.5, "color": "teal"},
            "zero_line": {"color": "red", "linestyle": "--", "label": "Zero Residual"},
        },
    )


def _create_residuals_hist_profile() -> PlotProfile:
    """创建残差直方图的模板。"""
    return PlotProfile(
        name=ObmiqPlotProfiles.RESIDUALS_HIST.value,
        title_props={"label": "Distribution of Residuals", "fontsize": 16, "fontweight": "bold"},
        label_props={"xlabel": "Residual (Actual - Predicted)", "ylabel": "Frequency"},
        artist_props={"hist": {"bins": 50, "edgecolor": "k", "kde": True}},
    )

def _create_captum_ig_summary_profile() -> PlotProfile:
    """创建Captum Integrated Gradients摘要图的模板。"""
    return PlotProfile(
        name=ObmiqPlotProfiles.CAPTUM_IG_SUMMARY.value,
        title_props={"label": "Captum Integrated Gradients Summary", "fontsize": 16, "fontweight": "bold"},
        label_props={"xlabel": "Feature", "ylabel": "Attribution Value"},
        artist_props={
            "bar": {"color": "skyblue"},
        },
    )


def _create_grad_cam_profile() -> PlotProfile:
    """创建Grad-CAM热力图的模板。"""
    return PlotProfile(
        name=ObmiqPlotProfiles.GRAD_CAM.value,
        title_props={"label": "Grad-CAM Saliency Heatmap", "fontsize": 16, "fontweight": "bold"},
        label_props={"xlabel": "T2 Time (ms)", "ylabel": "Amplitude"},
        artist_props={
            "line": {"color": "black", "linewidth": 2, "label": "T2 Spectrum Shape"},
            "imshow": {"cmap": "viridis", "aspect": "auto", "alpha": 0.5},
        },
    )

# 在模块加载时，立即执行注册
registry.register_base(_create_base_profile())

registry.register(_create_training_history_profile())
registry.register(_create_crossplot_profile())
registry.register(_create_residuals_plot_profile())
registry.register(_create_residuals_hist_profile())
registry.register(PlotProfile(name=ObmiqPlotProfiles.SHAP_SUMMARY.value))

registry.register(_create_captum_ig_summary_profile())
registry.register(_create_grad_cam_profile())
