"""WP Excel主读取器。

协调整个WP Excel文件读取流程，实现渐进式构造，集成所有服务层功能。
严格遵循WFS v1.0规范和项目架构要求。
"""

from __future__ import annotations

import time
from pathlib import Path
from typing import Any

import openpyxl
import structlog

from logwp.io.exceptions import WpFileFormatError, WpIOError
from logwp.models.constants import WpDsType, WpDepthUnit
from logwp.io.constants import WpXlsxKey
from logwp.models.datasets.base import WpDepthIndexedDatasetBase
from logwp.models.datasets.continuous import WpContinuousDataset
from logwp.models.datasets.discrete import WpDiscreteDataset
from logwp.models.datasets.interval import WpIntervalDataset
from logwp.models.exceptions import WpValidationError
from logwp.io.wp_excel.internal import (
    curve_parser,
    data_converter,
    dataset_parser,
    excel_parser,
    head_info_parser,
    validator,
    well_map_parser,
)
from logwp.models.head import WpHead
from logwp.models.mapping import WpWellMap
from logwp.models.well_project import WpWellProject

logger = structlog.get_logger(__name__)


class WpExcelReader:
    """高性能WP Excel文件读取器（流式处理实现）。

    采用openpyxl的只读流模式，以极低的内存占用高效处理大型WP Excel文件。
    本读取器遵循渐进式构造原则，按以下步骤构造WpWellProject：

    1. 创建空的WpWellProject对象
    2. 渐进式构造WpHead（井头信息）
    3. 渐进式构造WpWellMap（井名映射）
    4. 逐个数据集的渐进式构造
    5. 返回完整项目

    Examples:
        >>> reader = WpExcelReader()
        >>> project = reader.read(Path("test.wp.xlsx"))
        >>> print(f"项目: {project.name}, 数据集: {len(project.get_dataset_names())}")

    References:
        《SCAPE_DDS_logwp_io层的渐进式生成流程.md》- 构造流程设计
        《SCAPE_WFS_WP文件规范.md》- WFS v1.0规范
    """

    def read(
        self,
        file_path: str | Path,
        project_name: str | None = None,
        project_default_depth_unit: str = WpDepthUnit.METER
    ) -> WpWellProject:
        """主读取流程（渐进式构造）。

        Args:
            file_path: WP文件路径
            project_name: 可选的项目名称。如果指定则使用该名称，
                         否则从文件名自动提取（去除.wp.xlsx扩展名）
            project_default_depth_unit: 项目默认深度单位，默认为米(WpDepthUnit.METER)

        Returns:
            WpWellProject: 完整构造的测井项目对象

        Raises:
            WpFileFormatError: 文件格式不符合WFS规范
            WpValidationError: 数据验证失败
            WpIOError: 文件读取错误
            FileNotFoundError: 文件不存在
            PermissionError: 文件访问权限不足

        Examples:
            >>> reader = WpExcelReader()
            >>> # 使用自动提取的项目名称
            >>> project = reader.read("santos.wp.xlsx")
            >>> assert project.name == "santos"
            >>>
            >>> # 使用指定的项目名称和深度单位
            >>> project = reader.read("santos.wp.xlsx", project_name="我的测井工区",
            ...                      project_default_depth_unit=WpDepthUnit.FEET)
            >>> assert project.name == "我的测井工区"
            >>> assert project.default_depth_reference_unit == WpDepthUnit.FEET
        """
        start_time = time.time()
        file_path = Path(file_path)

        logger.info("开始读取WP Excel文件（流模式）", file_path=str(file_path))

        try:
            # 1. 文件验证和基础信息提取
            validator.validate_file_format(file_path)
            workbook = excel_parser.stream_load_workbook_safely(file_path)

            excel_parser.validate_workbook_structure(workbook)

            # 确定项目名称：用户指定优先，否则从文件名提取
            if project_name is not None:
                final_project_name = project_name
                logger.info("使用用户指定的项目名称", project_name=final_project_name)
            else:
                final_project_name = excel_parser.extract_project_name(file_path)
                logger.info("从文件名提取项目名称", project_name=final_project_name)

            # 2. 创建空的WpWellProject
            project = WpWellProject(
                name=final_project_name,
                default_depth_reference_unit=project_default_depth_unit
            )
            logger.info(
                "创建WpWellProject",
                project_name=final_project_name,
                default_depth_unit=project_default_depth_unit
            )

            # 3. 遍历所有工作表，根据类型分发处理
            self._process_all_sheets_from_stream(workbook, project)

            # 6. 最终验证
            self._validate_project(project)

            # 7. 记录完成信息
            processing_time = time.time() - start_time
            logger.info(
                "WP Excel文件读取完成",
                file_path=str(file_path),
                project_name=project.name,
                dataset_count=len(project.datasets),
                processing_time=round(processing_time, 3)
            )

            return project

        except Exception as e:
            processing_time = time.time() - start_time if 'start_time' in locals() else 0
            logger.error(
                "WP Excel文件读取失败",
                file_path=str(file_path),
                error_type=type(e).__name__,
                error_message=str(e),
                processing_time=round(processing_time, 3)
            )
            raise

    @staticmethod
    def _get_summary_sheet_names() -> set[str]:
        """获取所有概况信息工作表的标准大写名称集合。"""
        return {
            WpXlsxKey.SHEET_SUMMARY.upper(), WpXlsxKey.SHEET_OVERVIEW.upper(),
            WpXlsxKey.SHEET_REMARKS.upper(), WpXlsxKey.SHEET_REMARK.upper()
        }

    def _process_all_sheets_from_stream(self, workbook: openpyxl.Workbook, project: WpWellProject) -> None:
        """【新】遍历所有工作表，根据类型分发处理（流模式）。

        在只读模式下，工作表必须按顺序处理。

        Args:
            workbook: Excel工作簿对象 (以只读模式加载)
            project: 井项目对象
        """
        logger.debug("开始以流模式遍历工作表", sheet_count=len(workbook.sheetnames))
        summary_sheets = self._get_summary_sheet_names()

        # 在只读模式下，workbook.worksheets是生成器，保证顺序访问
        for worksheet in workbook.worksheets:
            sheet_name = worksheet.title
            logger.debug("处理工作表（流模式）", sheet_name=sheet_name)

            # 根据WFS规范判断工作表类型并分发处理（不区分大小写）
            if sheet_name.upper() == WpXlsxKey.SHEET_HEAD_INFO.upper():
                logger.debug("识别为井头信息工作表")
                project.head = self._build_head_info_from_stream(worksheet)

            elif sheet_name.upper() == WpXlsxKey.SHEET_WELL_MAP.upper():
                logger.debug("识别为井名映射工作表")
                project.well_map = self._build_well_map_from_stream(worksheet)
            elif sheet_name.upper().startswith(WpXlsxKey.HIDDEN_SHEET_PREFIX.upper()):
                logger.debug("跳过临时隐藏工作表")
                continue

            elif sheet_name.upper() in summary_sheets:
                logger.debug("跳过概况信息工作表")
                continue

            else:
                logger.debug("识别为数据集工作表")
                dataset = self._build_dataset_from_stream(worksheet)
                project.add_dataset(sheet_name, dataset)

        logger.info("工作表遍历完成（流模式）",
                   dataset_count=len(project.datasets),
                   has_head_info=project.head is not None,
                   has_well_map=project.well_map is not None)


    def _build_head_info_from_stream(self, worksheet: openpyxl.worksheet.worksheet.Worksheet) -> WpHead:
        """【新】从工作表流构造WpHead（井头信息）。

        此方法将替换_build_head_info_from_sheet，使用行迭代器进行解析，
        以兼容openpyxl的只读模式。

        Args:
            worksheet: 以只读模式加载的工作表对象

        Returns:
            WpHead: 构造的井头信息对象
        """
        logger.debug("开始以流模式构造井头信息", sheet_name=worksheet.title)

        row_iterator = worksheet.iter_rows(values_only=True)

        # 解析井头信息
        head = head_info_parser.parse_head_info_sheet(row_iterator, worksheet.title)

        logger.info("井头信息构造完成（流模式）", sheet_name=worksheet.title, attribute_count=len(head.attribute_records))
        return head


    def _build_well_map_from_stream(self, worksheet: openpyxl.worksheet.worksheet.Worksheet) -> WpWellMap:
        """【新】从工作表流构造WpWellMap（井名映射）。

        此方法将替换_build_well_map_from_sheet，使用行迭代器进行解析，
        以兼容openpyxl的只读模式。

        Args:
            worksheet: 以只读模式加载的工作表对象

        Returns:
            WpWellMap: 构造的井名映射对象
        """
        logger.debug("开始以流模式构造井名映射", sheet_name=worksheet.title)

        row_iterator = worksheet.iter_rows(values_only=True)

        # 解析井名映射
        well_map = well_map_parser.parse_well_map_sheet(row_iterator, worksheet.title)

        logger.info("井名映射构造完成（流模式）", sheet_name=worksheet.title, mapping_count=len(well_map.mappings))
        return well_map


    def _build_dataset_from_stream(self, worksheet: openpyxl.worksheet.worksheet.Worksheet) -> WpDepthIndexedDatasetBase:
        """【新】从工作表流渐进式构造数据集。

        此方法将替换_build_dataset，使用行迭代器进行解析，
        以兼容openpyxl的只读模式。

        Args:
            worksheet: 以只读模式加载的工作表对象

        Returns:
            WpDepthIndexedDatasetBase: 构造的数据集对象
        """
        sheet_name = worksheet.title
        logger.debug("开始以流模式构造数据集", dataset_name=sheet_name)
        start_time = time.time()

        try:
            row_iterator = worksheet.iter_rows(values_only=True)

            # 1. 解析元数据 (从前两行)
            try:
                row1 = next(row_iterator)
                row2 = next(row_iterator)
            except StopIteration:
                raise WpFileFormatError(f"工作表 '{sheet_name}' 行数不足，无法解析元数据。")

            ds_meta = dataset_parser.parse_metadata_from_header_rows(row1, row2, sheet_name)
            dataset_type = ds_meta['type']

            # 2. 创建空数据集
            dataset = self._create_empty_dataset(dataset_type, sheet_name)
            dataset.desc = ds_meta['desc']

            # 3. 解析曲线定义 (从3-7行)
            try:
                header_rows = [next(row_iterator) for _ in range(5)]
            except StopIteration:
                raise WpFileFormatError(f"工作表 '{sheet_name}' 行数不足，无法解析曲线定义。")

            curve_definitions = dataset_parser.extract_curve_definitions_from_rows(header_rows, sheet_name)
            curve_metadata = curve_parser.create_curve_metadata_from_definitions(
                curve_definitions, dataset_type
            )

            # 4. 直接从迭代器构造DataFrame (高性能)
            df = data_converter.build_dataframe_from_iterator(
                curve_metadata, row_iterator, curve_definitions, sheet_name
            )

            # 5. 原子性设置曲线数据 (CF-2)
            dataset.direct_attach_curves_data(df, curve_metadata)

            # 6. 设置静态采样间隔（DU-3规则）
            sampling_rate = ds_meta['sampling_rate']
            if sampling_rate is not None:
                dataset.depth_sampling_rate = sampling_rate

            # 7. 最终验证
            validator.validate_dataset_construction_compliance(dataset)

            processing_time = time.time() - start_time
            logger.info(
                "数据集构造完成（流模式）",
                dataset_name=sheet_name,
                dataset_type=dataset_type.value,
                df_shape=dataset.df.shape,
                curve_count=len(dataset.curve_metadata.curves),
                processing_time=round(processing_time, 3)
            )

            return dataset

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(
                "数据集构造失败（流模式）",
                dataset_name=sheet_name,
                error_type=type(e).__name__,
                error_message=str(e),
                processing_time=round(processing_time, 3)
            )
            raise

    def _create_empty_dataset(self, dataset_type: WpDsType, name: str) -> WpDepthIndexedDatasetBase:
        """创建空的数据集对象。

        Args:
            dataset_type: 数据集类型
            name: 数据集名称

        Returns:
            WpDepthIndexedDatabaseBase: 空的数据集对象
        """
        if dataset_type == WpDsType.CONTINUOUS:
            return WpContinuousDataset(name=name)
        elif dataset_type == WpDsType.POINT:
            return WpDiscreteDataset(name=name)
        elif dataset_type == WpDsType.INTERVAL:
            return WpIntervalDataset(name=name)
        else:
            raise WpValidationError(f"不支持的数据集类型: {dataset_type}")

    def _validate_project(self, project: WpWellProject) -> None:
        """验证完整项目。

        Args:
            project: 项目对象

        Raises:
            WpValidationError: 项目验证失败
        """
        logger.debug("开始项目完整性验证")

        # 验证项目基本信息
        if not project.name:
            raise WpValidationError("项目名称不能为空")

        # WFS 5.4.2.3: 缺少版本属性时应视为格式错误
        # 如果存在井头信息且包含属性，则必须有版本信息
        if project.head and project.head.attribute_records:
            if project.head.get_version() is None:
                raise WpValidationError("井头信息(_Head_Info)中缺少'V'类别的版本(Version)属性")

        # 验证数据集数量
        dataset_names = list(project.datasets.keys())
        if not dataset_names:
            raise WpValidationError("项目必须包含至少一个数据集")

        logger.info("项目完整性验证通过", dataset_count=len(dataset_names))
