"""logwp.extras.tracking.config - 基础配置模型

定义实验追踪系统的基础配置模型，供上游项目继承使用。

Architecture
------------
层次/依赖: logwp.extras.tracking包配置层
设计原则: 通用性与可扩展性、类型安全、配置即服务
性能特征: pydantic v2高性能验证、JSON序列化

遵循CCG规范：
- DV-1: pydantic v2模型使用
- DV-2: 字段验证和约束
- DV-4: 不可变模型设计（可选）
- TS-1: 完整类型注解覆盖

References
----------
- 《SCAPE_CCG_编码与通用规范》§DV - 数据验证规范
- 《logwp_extras_tracking设计.md》§3.3 - 统一化配置管理
- 《logwp_extras_tracking设计.md》§5 - 应用层配置管理实践
"""

from __future__ import annotations

from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class BaseRunConfig(BaseModel):
    """所有实验流程配置的基础模型。
    
    这是一个通用的配置基类，定义了所有运行都应具备的通用顶级字段。
    具体应用（如SCAPE项目）可以继承此类并添加项目特有的配置字段。
    
    Architecture
    ------------
    层次/依赖: 配置基类，被具体应用继承
    设计原则: 通用性优先、扩展性友好、类型安全
    性能特征: pydantic v2高性能验证、JSON序列化
    
    Attributes:
        project_name: 项目名称，用于标识实验所属的项目
        run_name: 本次运行的描述性名称，应该具有业务含义
        description: 可选的运行描述，用于记录实验目的和背景
        tags: 可选的标签列表，用于分类和检索
        
    Examples:
        >>> # 直接使用基础配置
        >>> config = BaseRunConfig(
        ...     project_name="SCAPE",
        ...     run_name="foster_nmr_baseline_test"
        ... )
        >>>
        >>> # 继承扩展配置
        >>> class ScapeConfig(BaseRunConfig):
        ...     bootstrap_iterations: int = 20
        ...     learning_rate: float = 0.01
        >>>
        >>> config = ScapeConfig(
        ...     project_name="SCAPE",
        ...     run_name="swift_pso_experiment_001",
        ...     bootstrap_iterations=25,
        ...     learning_rate=0.005
        ... )
    """
    
    # 必需字段
    project_name: str = Field(
        ...,
        description="项目名称，用于标识实验所属的项目",
        min_length=1,
        max_length=100
    )
    
    run_name: str = Field(
        ...,
        description="本次运行的描述性名称，应该具有业务含义",
        min_length=1,
        max_length=200
    )
    
    # 可选字段
    description: Optional[str] = Field(
        None,
        description="运行描述，用于记录实验目的和背景",
        max_length=1000
    )
    
    tags: list[str] = Field(
        default_factory=list,
        description="标签列表，用于分类和检索实验"
    )
    
    # 配置模型设置
    model_config = {
        "extra": "allow",  # 允许添加额外字段，支持扩展
        "str_strip_whitespace": True,  # 自动去除字符串首尾空白
        "validate_assignment": True,  # 赋值时验证
        "use_enum_values": True,  # 使用枚举值而非枚举对象
    }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。
        
        Returns:
            Dict[str, Any]: 配置的字典表示
            
        Examples:
            >>> config = BaseRunConfig(project_name="SCAPE", run_name="test")
            >>> config_dict = config.to_dict()
            >>> print(config_dict["project_name"])  # SCAPE
        """
        return self.model_dump()
    
    def to_json(self, *, indent: int = 4) -> str:
        """转换为JSON字符串。
        
        Args:
            indent: JSON缩进空格数
            
        Returns:
            str: 配置的JSON表示
            
        Examples:
            >>> config = BaseRunConfig(project_name="SCAPE", run_name="test")
            >>> json_str = config.to_json()
            >>> print(json_str)
        """
        return self.model_dump_json(indent=indent)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> BaseRunConfig:
        """从字典创建配置实例。
        
        Args:
            data: 配置数据字典
            
        Returns:
            BaseRunConfig: 配置实例
            
        Examples:
            >>> data = {"project_name": "SCAPE", "run_name": "test"}
            >>> config = BaseRunConfig.from_dict(data)
            >>> print(config.project_name)  # SCAPE
        """
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str) -> BaseRunConfig:
        """从JSON字符串创建配置实例。
        
        Args:
            json_str: JSON格式的配置字符串
            
        Returns:
            BaseRunConfig: 配置实例
            
        Examples:
            >>> json_str = '{"project_name": "SCAPE", "run_name": "test"}'
            >>> config = BaseRunConfig.from_json(json_str)
            >>> print(config.project_name)  # SCAPE
        """
        return cls.model_validate_json(json_str)


class TrackingConfig(BaseModel):
    """实验追踪系统的配置模型。
    
    定义追踪系统本身的行为配置，如文件命名、存储选项等。
    这些配置影响追踪系统的运行方式，而不是被追踪的实验内容。
    
    Architecture
    ------------
    层次/依赖: 追踪系统配置，控制系统行为
    设计原则: 合理默认值、灵活配置、向后兼容
    性能特征: 轻量级配置、快速验证
    
    Attributes:
        manifest_filename: 运行清单文件名
        config_snapshot_filename: 配置快照文件名
        auto_create_dirs: 是否自动创建目录
        compress_artifacts: 是否压缩大型产物文件
        max_artifact_size_mb: 单个产物文件的最大大小（MB）
        
    Examples:
        >>> # 使用默认配置
        >>> config = TrackingConfig()
        >>> print(config.manifest_filename)  # manifest.json
        >>>
        >>> # 自定义配置
        >>> config = TrackingConfig(
        ...     manifest_filename="run_info.json",
        ...     max_artifact_size_mb=1000
        ... )
    """
    
    # 文件命名配置
    manifest_filename: str = Field(
        default="manifest.json",
        description="运行清单文件名"
    )
    
    config_snapshot_filename: str = Field(
        default="config_snapshot.yaml",
        description="配置快照文件名"
    )
    
    # 行为配置
    auto_create_dirs: bool = Field(
        default=True,
        description="是否自动创建目录"
    )
    
    compress_artifacts: bool = Field(
        default=False,
        description="是否压缩大型产物文件"
    )
    
    max_artifact_size_mb: int = Field(
        default=500,
        description="单个产物文件的最大大小（MB）",
        gt=0
    )
    
    # 性能配置
    enable_file_hashing: bool = Field(
        default=True,
        description="是否计算文件哈希值"
    )
    
    hash_algorithm: str = Field(
        default="sha256",
        description="文件哈希算法"
    )
    
    # 配置模型设置
    model_config = {
        "extra": "forbid",  # 禁止额外字段，确保配置严格
        "str_strip_whitespace": True,
        "validate_assignment": True,
    }
