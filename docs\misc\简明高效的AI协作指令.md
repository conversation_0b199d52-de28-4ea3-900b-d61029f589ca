# 简明高效的AI协作指令

在与AI编程助手协作时，使用简洁、明确的指令可以极大地提升沟通效率和代码质量。这些指令通常源于业界成熟的软件工程实践。本文件汇总了一些实用的“一句话提示词”，帮助您快速传达复杂的思想和要求。

---

### 1. 关于节奏与控制 (Pacing & Control)

*   **指令**: "请遵循小步快跑的策略，将修改拆分成更小、更安全的步骤，逐一执行，等我确认后再执行下一步。"
    *   **意图**: 当面对大型重构或复杂修改时，要求AI将任务分解，每次只提交少量、可独立审查的改动，确保过程可控。

*   **指令**: "在应用修改前，请先给我一份计划或一份“干跑”（dry-run）报告。"
    *   **意图**: 要求AI先描述它将要做什么（例如，将修改哪些文件，应用什么模式），而不是直接动手，以便您预先评估方案。

*   **指令**: "请只提供diff格式的变更，而不是完整文件。"
    *   **意图**: 当您只想快速查看变更内容时，避免被完整的文件代码淹没，让审查更聚焦。

*   **指令**: "请撤销上一步的修改。"
    *   **意图**: 快速回退AI刚刚执行的操作，当您发现方向错误时非常有用。

*   **指令**: "我们先聚焦在`[文件名]`这个文件上。"
    *   **意图**: 将AI的注意力限制在特定文件或模块上，避免它在整个代码库中进行不相关的修改。

---

### 2. 关于代码质量与风格 (Code Quality & Style)

*   **指令**: "请优先保证代码的可读性，而不是追求技巧的炫耀。"
    *   **意图**: 引导AI生成清晰、直白、易于人类理解和维护的代码，避免使用过于晦涩或复杂的语法糖。

*   **指令**: "请严格遵守现有代码的风格。"
    *   **意图**: 要求AI在添加或修改代码时，主动学习并匹配当前文件的命名、格式化和代码结构，保持项目风格一致性。

*   **指令**: "请为这段代码添加注释，重点解释‘为什么’这么做，而不是‘做了什么’。"
    *   **意图**: 遵循高质量注释的原则，要求AI的注释提供背景、设计决策或业务逻辑等深层信息，而不是简单地复述代码。

*   **指令**: "请让这段代码更‘Pythonic’（或更符合某种语言的惯例）。"
    *   **意图**: 要求AI使用目标语言的最佳实践和惯用写法来优化代码，使其更地道、更优雅。

*   **指令**: "请检查并移除所有未使用的导入或变量。"
    *   **意图**: 执行静态代码分析中的“死代码”清理，保持代码库的整洁。

*   **指令**: "请将这段代码中的`print`语句替换为结构化的日志记录。"
    *   **意图**: 提升代码的可观测性，将临时的调试输出替换为符合项目规范的日志事件。

*   **指令**: "请从[性能/安全/可读性]的特定角度审查这段代码。"
    *   **意图**: 进行一次聚焦式代码审查，而不是泛泛的检查，以获得特定领域更深入的改进建议。

---

### 3. 关于重构与设计 (Refactoring & Design)

*   **指令**: "请将这段逻辑提取到一个独立的函数/方法中。"
    *   **意图**: 明确的重构指令，用于降低函数复杂度，提高代码复用性。

*   **指令**: "我们在这里应用[设计模式名称]，比如‘策略模式’。"
    *   **意图**: 直接指定一个设计模式，让AI围绕这个模式进行代码重构，解决特定的设计问题。

*   **指令**: "请减少这里的代码嵌套深度。"
    *   **意图**: 针对具有高圈复杂度的代码块（如多层`if/for`嵌套），要求AI进行简化，提高可读性和可测试性。

*   **指令**: "我们能把这个函数变成一个纯函数吗？"
    *   **意图**: 要求AI移除函数中的副作用（如修改全局变量、I/O操作），使其输出仅依赖于输入，更易于测试和推理。

*   **指令**: "请将这些硬编码的配置项提取到一个专门的配置文件中。"
    *   **意图**: 将配置与代码分离，提高程序灵活性和可维护性。

*   **指令**: "请用[新方法/库]替换掉这里的[旧方法/库]实现。"
    *   **意图**: 用于代码库的现代化改造，例如用`pathlib`替换`os.path`。

*   **指令**: "重构前，请先分析并列出旧代码的核心调用链。"
    *   **意图**: 采用调用链分析法，在动手前先理清逻辑，确保重构方向正确。

*   **指令**: "请为这个函数/类定义一个严格的‘契约’（前置/后置条件，异常）。"
    *   **意图**: 采用契约式设计（Design by Contract）的思想，在实现前就明确函数的行为边界，提升代码的可靠性。

*   **指令**: "请对比新旧版本的代码，验证核心功能是否完全一致。"
    *   **意图**: 在重构或修改后，进行功能对齐验证，确保没有引入回归错误。

---

### 4. 关于健壮性与测试 (Robustness & Testing)

*   **指令**: "请先为这个功能编写单元测试（TDD风格）。"
    *   **意图**: 要求AI采用测试驱动开发的方式，先定义好功能的期望行为和边界，再进行实现。

*   **指令**: "请充分考虑并处理这里的边界情况（edge cases）。"
    *   **意图**: 提醒AI思考并处理非标准或极端的输入情况，如空列表、零值、最大/最小值等，提升代码健壮性。

*   **指令**: "请为这段代码添加健壮的错误处理。"
    *   **意图**: 要求AI使用`try...except`块，并捕获具体的异常类型，而不是宽泛的`Exception`，同时提供有意义的错误信息或回退逻辑。

*   **指令**: "不要使用‘魔法数字’或硬编码的字符串，请将它们定义为常量。"
    *   **意图**: 提升代码的可维护性和可读性，将散落在代码中的字面量统一管理。

*   **指令**: "请为这个函数添加输入验证（precondition checks）。"
    *   **意图**: 在函数入口处增加断言或检查，确保输入参数的有效性，防止错误数据向下游传递。

*   **指令**: "请模拟一个[具体错误，如`FileNotFoundError`]，并验证错误处理逻辑是否正常工作。"
    *   **意图**: 编写测试用例来验证`try...except`块的健壮性。

---

### 5. 关于文档与解释 (Documentation & Explanation)

*   **指令**: "请更新docstring以反映刚才的变更。"
    *   **意图**: 在代码修改后，快速要求AI同步更新对应的API文档。

*   **指令**: "请用一个初级开发者的视角或现实生活的类比来解释这个概念。"
    *   **意图**: 采用费曼学习法，通过简化和类比来加深对抽象概念或复杂算法的理解。

*   **指令**: "请对比新旧代码，为我生成一份变更摘要（changelog）。"
    *   **意图**: 要求AI分析两个版本代码的差异，并自动生成一份人类可读的变更日志，用于文档同步或版本发布。

*   **指令**: "请画出这个函数的调用关系图（用Mermaid或PlantUML语法）。"
    *   **意图**: 将代码的逻辑关系可视化，帮助理解复杂的函数调用或类继承结构。

---

### 6. 关于探索与学习 (Exploration & Learning)

*   **指令**: "请比较一下 [库A] 和 [库B] 在这个场景下的优缺点。"
    *   **意图**: 利用AI的广博知识进行技术选型，快速获得一个结构化的对比分析。

*   **指令**: "请给我一个使用 [库/特性] 的最小可运行示例 (minimal working example)。"
    *   **意图**: 快速上手一个新技术或库，避免阅读冗长的官方文档。

*   **指令**: "这个错误信息 `[错误信息]` 通常是什么原因导致的？"
    *   **意图**: 快速诊断问题，获取常见的错误原因和解决方案。

*   **指令**: "请解释一下这个术语：[术语名称]，并给出一个代码示例。"
    *   **意图**: 学习新的编程概念、设计模式或行业术语。

---

### 7. 关于项目与架构 (Project & Architecture)

*   **指令**: "请分析一下这个项目的目录结构，并解释各个部分的作用。"
    *   **意图**: 快速了解一个新项目的整体结构和代码组织方式。

*   **指令**: "根据这个需求，我们应该创建一个新模块，还是在现有模块上扩展？请给出你的理由。"
    *   **意图**: 寻求架构层面的建议，帮助做出更合理的设计决策。

*   **指令**: "请为这个项目生成一个`pyproject.toml`的依赖项列表。"
    *   **意图**: 自动化处理项目依赖管理，快速生成配置文件。

*   **指令**: "请检查是否存在循环依赖（circular dependency）。"
    *   **意图**: 发现项目中潜在的架构问题，避免因循环导入导致的运行时错误。

---

### 8. 关于协作模式与方法论 (Collaboration Patterns & Methodologies)

*   **指令**: "请学习并遵循我们项目的术语表，后续的沟通都将基于此。"
    *   **意图**: 初始化AI的上下文，建立一个共享的领域特定语言，确保后续沟通的准确性。

*   **指令**: "我们采用‘计划-执行-核对’工作流。现在是[PLAN/EXECUTE/RECONCILE]阶段。"
    *   **意图**: 启动一个结构化的开发流程，明确告知AI当前所处的阶段和它需要承担的任务，以解决计划与实现不一致的问题。

---

### 9. 关于架构与分层 (Architecture & Layering)

*   **指令**: "当我开发应用层时，如果你发现基础API库缺少某个通用功能，请不要在应用层重复实现。你应该向我提议：‘这个功能似乎更通用，我建议将其添加到基础库 `[文件名]` 中，可以吗？’ 经我同意后，再执行这个API增强任务。"
    *   **意图**: 授权AI在发现API缺失时，主动提出改进建议，遵循DRY原则，将通用逻辑下沉到基础库，但修改核心库前必须获得批准。
