"""
数据缩放器，提供按井归一化等高级功能。
"""
from __future__ import annotations

from typing import TYPE_CHECKING, List, Dict, Any
import pandas as pd

from .base import BasePreprocessor
from logwp.models.exceptions import WpDataError

if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpDataFrameBundle


class WellwiseScaler(BasePreprocessor):
    """
    按井对指定的测井曲线进行数据缩放。

    这是一种关键的预处理技术，可以消除不同井之间的系统性偏差，
    同时保留每口井内部的数据相对变化特征。

    Attributes:
        stats_ (Dict[str, Dict[str, Dict[str, float]]]):
            在 fit 过程中学习到的统计数据。
            结构为: {well_name: {curve_name: {stat_name: value}}}
            例如: {'W1': {'GR': {'mean': 75.2, 'std': 15.3}}}
    """

    def __init__(self, curves: List[str], method: str = 'standard'):
        """
        初始化 WellwiseScaler。

        Args:
            curves (List[str]): 需要进行缩放的曲线名称列表。
            method (str, optional): 缩放方法。支持 'standard' (标准化, Z-score)
                                    和 'minmax' (归一化到 [0, 1])。
                                    默认为 'standard'。
        """
        if method not in ['standard', 'minmax']:
            raise ValueError("方法必须是 'standard' 或 'minmax'")
        self.curves = curves
        self.method = method
        self.stats_: Dict[str, Dict[str, Dict[str, float]]] = {}

    def fit(self, bundle: WpDataFrameBundle) -> "WellwiseScaler":
        """
        按井学习每条曲线的缩放参数。

        Args:
            bundle (WpDataFrameBundle): 包含多井数据的输入数据包。

        Returns:
            WellwiseScaler: 学习了参数的自身实例。
        """
        self.stats_ = {}
        all_wells_data = bundle.to_all_wells_data()

        for well_name, well_df in all_wells_data.items():
            self.stats_[well_name] = {}
            for curve in self.curves:
                if curve not in well_df.columns:
                    continue  # 如果某口井没有这条曲线，则跳过

                if self.method == 'standard':
                    self.stats_[well_name][curve] = {
                        'mean': well_df[curve].mean(),
                        'std': well_df[curve].std()
                    }
                elif self.method == 'minmax':
                    self.stats_[well_name][curve] = {
                        'min': well_df[curve].min(),
                        'max': well_df[curve].max()
                    }
        return self

    def transform(self, bundle: WpDataFrameBundle) -> WpDataFrameBundle:
        """
        使用学习到的参数，按井对数据进行缩放转换。

        Args:
            bundle (WpDataFrameBundle): 需要转换的数据包。

        Returns:
            WpDataFrameBundle: 一个新的、包含了缩放后数据的数据包。
        """
        if not self.stats_:
            raise RuntimeError("必须在 transform 之前调用 fit 方法。")

        transformed_df = bundle.data.copy()

        # 使用便捷方法健壮地获取井名列
        try:
            well_col = bundle.get_well_column_name()
        except WpDataError as e:
            # 将底层的WpDataError转换为对用户更友好的ValueError，
            # 并保留原始异常链以便调试。
            raise ValueError(
                "无法执行按井缩放：无法从Bundle中确定井名列。请检查Bundle的元数据。"
            ) from e

        for well_name, stats_dict in self.stats_.items():
            well_mask = (transformed_df[well_col] == well_name)
            for curve, stats in stats_dict.items():
                if self.method == 'standard':
                    mean, std = stats['mean'], stats['std']
                    # 避免除以零
                    transformed_df.loc[well_mask, curve] = (transformed_df.loc[well_mask, curve] - mean) / (std if std > 1e-9 else 1.0)
                elif self.method == 'minmax':
                    min_val, max_val = stats['min'], stats['max']
                    denominator = max_val - min_val
                    # 避免除以零
                    transformed_df.loc[well_mask, curve] = (transformed_df.loc[well_mask, curve] - min_val) / (denominator if denominator > 1e-9 else 1.0)

        # 创建一个新的Bundle返回，以保持不可变性
        return WpDataFrameBundle(
            name=f"{bundle.name}_scaled",
            data=transformed_df,
            curve_metadata=bundle.curve_metadata,
            curve_to_columns_map=bundle.curve_to_columns_map
        )
