from __future__ import annotations

"""logwp.models.datasets.continuous - 连续型数据集

WpContinuousDataset处理连续型测井数据，支持深度索引和曲线验证。

Architecture
------------
层次/依赖: datasets层具体实现，继承WpDepthIndexedDatabaseBase
设计原则: 连续数据特化、深度索引、曲线验证、性能优化
性能特征: 向量化计算、内存优化、GPU支持

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- SC-1: 算法正确性，包含物理约束验证

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§3.2.2 - WpContinuousDataset设计
- 《SCAPE_WFS_WP文件规范.md》A.3.1 - 连续型数据集规范
"""

from typing import Any
import pandas as pd
import numpy as np
import copy
from datetime import datetime

from logwp.models.constants import WpDsType, WpStandardColumn, WpStatisticsKeys, WpDepthRole
from logwp.models.exceptions import (
    WpValidationError, WpDataError,
    ErrorContext
)
from logwp.models.types import WpDepthValue, WpIdentifier, WpDatasetName
from logwp.infra import get_logger
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata
from logwp.models.datasets.base import WpDepthIndexedDatasetBase

logger = get_logger(__name__)


class WpContinuousDataset(WpDepthIndexedDatasetBase):
    """连续型数据集。

    处理连续型测井数据，如GR、PHIT、PERM等曲线数据。
    支持深度索引、数据结构验证和格式检查。

    DataFrame约定：
    - MD（测量深度）列作为普通列存储，不作为索引
    - 用户可调用 create_depth_index() 方法创建深度索引

    Architecture
    ------------
    层次/依赖: datasets层连续型数据集，继承WpDepthIndexedDatabaseBase
    设计原则: 连续数据特化、深度索引、数据完整性
    性能特征: 向量化计算、内存优化、快速查找

    Continuous Dataset Requirements（基于WFS A.3.1节）：
    - **深度列**: 必须包含MD（测量深度）或TVD（垂直深度）列
    - **数值列**: 所有测井曲线必须为数值类型
    - **单调性**: 深度列应单调递增（允许小幅回退）
    - **数据完整性**: 基础数据结构和格式验证

    Examples:
        >>> df = pd.DataFrame({
        ...     "MD": [2500.0, 2500.5, 2501.0],
        ...     "GR": [45.2, 52.1, 38.9],
        ...     "PHIT": [0.15, 0.18, 0.12],
        ...     "WELL": ["C-1", "C-1", "C-1"]
        ... })
        >>> dataset = WpContinuousDataset(name="OBMIQ_logs", df=df)
        >>> assert dataset.validate()
        >>>
        >>> # 深度范围查询
        >>> subset = dataset.filter_depth_range(2500.0, 2501.0)
        >>> assert len(subset.df) == 3

    References:
        《SCAPE_WFS_WP文件规范.md》A.3.1 - 连续型数据集规范
    """

    @property
    def dataset_type(self) -> WpDsType:
        """数据集类型。

        Returns:
            WpDsType: 连续型数据集类型
        """
        return WpDsType.CONTINUOUS

    @classmethod
    def create_empty(cls, name: str) -> WpContinuousDataset:
        """创建空的连续型数据集（io层调用）。

        Args:
            name: 数据集名称

        Returns:
            WpContinuousDataset: 空的连续型数据集实例

        Examples:
            >>> dataset = WpContinuousDataset.create_empty("OBMIQ_logs")
            >>> assert dataset.dataset_type == WpDsType.CONTINUOUS
            >>> assert dataset.df.empty

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.2 - 创建空数据集
        """
        return cls(name=WpIdentifier(name))

    @classmethod
    def create_with_data(
        cls,
        name: WpDatasetName | WpIdentifier,
        df: pd.DataFrame,
        curve_metadata: CurveMetadata,
        *,
        depth_sampling_rate: float = 0
    ) -> WpContinuousDataset:
        """创建连续型数据集并附加数据。

        工厂方法，创建新的连续型数据集实例并同时附加DataFrame和曲线元数据。

        Args:
            name: 数据集名称
            df: DataFrame数据体
            curve_metadata: 曲线元数据
            depth_sampling_rate: 深度采样间隔，用于设置数据集的depth_sampling_rate属性

        Returns:
            WpContinuousDataset: 创建的连续型数据集实例

        Examples:
            >>> df = pd.DataFrame({'WELL': ['C-1'], 'MD': [2500.0], 'GR': [45.2]})
            >>> metadata = CurveMetadata()
            >>> # ... 配置metadata ...
            >>> dataset = WpContinuousDataset.create_with_data("test_logs", df, metadata)
            >>> assert dataset.name == "test_logs"
            >>> assert dataset.dataset_type == WpDsType.CONTINUOUS

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.3 - 工厂方法模式
        """
        # 创建空数据集实例
        instance = cls.create_empty(name)

        # 附加数据和元数据
        instance.direct_attach_curves_data(df, curve_metadata)

        # 设置深度采样间隔
        instance.depth_sampling_rate = depth_sampling_rate

        return instance

    def clone_dataset(self) -> WpContinuousDataset:
        """克隆连续型数据集，返回新的数据集实例。

        创建当前数据集的深拷贝，包括：
        - 数据体（DataFrame）的完整拷贝
        - 曲线元数据的完整拷贝
        - 所有属性的拷贝（除时间戳外）

        Returns:
            WpContinuousDataset: 克隆后的新连续型数据集实例

        Note:
            - 克隆后的数据集具有新的时间戳（created_at, modified_at）
            - 数据体和元数据完全独立，修改不会相互影响

        Examples:
            >>> original = dataset
            >>> cloned = dataset.clone_dataset()
            >>> assert cloned.name == original.name
            >>> assert cloned.df.equals(original.df)
            >>> assert cloned is not original  # 不同的对象
            >>> assert cloned.df is not original.df  # 不同的DataFrame
            >>> assert cloned.dataset_type == WpDsType.CONTINUOUS
        """
        # 深拷贝DataFrame
        cloned_df = self.df.copy(deep=True)

        # 深拷贝曲线元数据
        cloned_curve_metadata = copy.deepcopy(self.curve_metadata)

        # 创建新的数据集实例，使用新的时间戳
        now = datetime.now()
        cloned = WpContinuousDataset(
            name=self.name,  # WpIdentifier是不可变的，可以共享
            df=cloned_df,
            curve_metadata=cloned_curve_metadata,
            created_at=now,
            modified_at=now
        )

        return cloned

    # ------------------------------------------------------------
    # 深度参考曲线便捷服务方法实现
    # ------------------------------------------------------------

    def get_depth_reference_count(self) -> int:
        """获取连续型数据集深度参考曲线的条数。

        Returns:
            int: 深度参考曲线数量，连续型数据集固定返回1

        Examples:
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> assert continuous_dataset.get_depth_reference_count() == 1
        """
        return 1

    def get_single_depth_reference_curve(self) -> CurveBasicAttributes:
        """获取连续型数据集的单个深度曲线。

        Returns:
            CurveBasicAttributes: 深度参考曲线的基本属性

        Raises:
            WpDataError: 当没有找到深度参考曲线时抛出

        Note:
            - 利用CurveMetadata.get_depth_reference_curves()方法
            - WFS规范保证返回列表的第一个元素是连续型数据集的深度参考曲线

        Examples:
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> depth_curve = continuous_dataset.get_single_depth_reference_curve()
            >>> assert depth_curve.depth_role == WpDepthRole.SINGLE
        """
        depth_curve_names = self.curve_metadata.get_depth_reference_curves()

        if not depth_curve_names:
            raise WpDataError(
                "连续型数据集缺少深度参考曲线",
                context=ErrorContext(
                    operation="get_single_depth_reference_curve",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "available_curves": list(self.curve_metadata.curves.keys())
                    }
                )
            )

        # WFS规范保证第一个是连续型数据集的深度参考曲线
        depth_curve_name = depth_curve_names[0]
        return self.curve_metadata.get_curve(depth_curve_name)

    def get_interval_depth_reference_curves(self) -> tuple[CurveBasicAttributes, CurveBasicAttributes]:
        """连续型数据集不支持区间深度索引。

        Raises:
            WpDataError: 连续型数据集不支持此操作

        Note:
            - 连续型数据集只有单一深度索引
            - 应使用get_single_depth_reference_curve()方法
        """
        raise WpDataError(
            "连续型数据集不支持区间深度索引操作",
            context=ErrorContext(
                operation="get_interval_depth_reference_curves",
                dataset_name=str(self.name),
                additional_info={
                    "dataset_type": self.dataset_type.value,
                    "supported_operation": "get_single_depth_reference_curve",
                    "reason": "continuous_dataset_single_depth_only"
                }
            )
        )

    def get_wells_depth_ranges(self) -> dict[str, tuple[float, float]]:
        """获取每口井的深度范围。

        Returns:
            dict[str, tuple[float, float]]: {井名: (最小深度, 最大深度)}

        Note:
            - 基于深度参考曲线的最小值和最大值
            - 空数据集返回空字典
            - 按井名分组计算每口井的深度范围

        Examples:
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> wells_ranges = continuous_dataset.get_wells_depth_ranges()
            >>> # {'C-1': (2500.0, 2600.0), 'C-2': (2550.0, 2650.0)}
        """
        if self.df.empty:
            return {}

        try:
            depth_curve = self.get_single_depth_reference_curve()
            depth_column = depth_curve.dataframe_column_name

            if depth_column not in self.df.columns:
                return {}

            # 获取井名曲线
            well_curves = self.curve_metadata.get_well_identifier_curves()
            if not well_curves or well_curves[0] not in self.df.columns:
                # 没有井名曲线，视为单井数据
                depth_series = self.df[depth_column].dropna()
                if depth_series.empty:
                    return {}
                return {"Unknown": (float(depth_series.min()), float(depth_series.max()))}

            well_column = well_curves[0]
            wells_ranges = {}

            # 按井分组计算深度范围
            for well_name in self.df[well_column].dropna().unique():
                well_data = self.df[self.df[well_column] == well_name]
                well_depths = well_data[depth_column].dropna()

                if not well_depths.empty:
                    wells_ranges[str(well_name)] = (
                        float(well_depths.min()),
                        float(well_depths.max())
                    )

            return wells_ranges

        except WpDataError:
            # 没有深度曲线时返回空字典
            return {}

    def get_depth_range_for_well(self, well_name: str) -> tuple[float, float]:
        """获取指定井的深度范围。

        Args:
            well_name: 井名

        Returns:
            tuple[float, float]: (最小深度, 最大深度)

        Raises:
            WpDataError: 当指定井不存在时抛出

        Examples:
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> min_depth, max_depth = continuous_dataset.get_depth_range_for_well("C-1")
            >>> assert min_depth <= max_depth
        """
        wells_ranges = self.get_wells_depth_ranges()

        if well_name not in wells_ranges:
            available_wells = list(wells_ranges.keys())
            raise WpDataError(
                f"井 '{well_name}' 不存在于数据集中",
                context=ErrorContext(
                    operation="get_depth_range_for_well",
                    dataset_name=str(self.name),
                    additional_info={
                        "requested_well": well_name,
                        "available_wells": available_wells,
                        "total_wells": len(available_wells)
                    }
                )
            )

        return wells_ranges[well_name]

    def get_depth_reference_unit(self) -> str | None:
        """获取连续型数据集深度参考曲线的单位。

        Returns:
            str | None: 深度曲线的单位，如果没有单位则返回None

        Note:
            - 利用get_single_depth_reference_curve()获取深度曲线
            - 返回深度曲线的unit属性

        Examples:
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> unit = continuous_dataset.get_depth_reference_unit()
            >>> assert unit == "m"  # 或其他深度单位
        """
        try:
            depth_curve = self.get_single_depth_reference_curve()
            return depth_curve.unit
        except WpDataError:
            # 没有深度曲线时返回None
            return None

    def calculate_depth_sampling_rate(self) -> float:
        """动态计算连续型数据集的深度间隔。

        Returns:
            float: 计算得到的深度间隔

        Raises:
            WpDataError: 当数据集为空或无法计算深度间隔时抛出

        Note:
            - 动态计算深度曲线相邻点之间的间隔
            - 对于连续型数据集，返回最常见的间隔值（众数）
            - 如果没有众数，则返回平均间隔

        Examples:
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> sampling_rate = continuous_dataset.calculate_depth_sampling_rate()
            >>> assert sampling_rate == 0.5  # 例如0.5米间隔
        """
        if self.df.empty:
            raise WpDataError(
                "空数据集无法计算深度间隔",
                context=ErrorContext(
                    operation="calculate_depth_sampling_rate",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "reason": "empty_dataset"
                    }
                )
            )

        try:
            depth_curve = self.get_single_depth_reference_curve()
            depth_column = depth_curve.dataframe_column_name

            if depth_column not in self.df.columns:
                raise WpDataError(
                    "深度曲线列不存在于DataFrame中",
                    context=ErrorContext(
                        operation="calculate_depth_sampling_rate",
                        dataset_name=str(self.name),
                        column_name=depth_column,
                        additional_info={
                            "available_columns": list(self.df.columns),
                            "expected_column": depth_column
                        }
                    )
                )

            depth_series = self.df[depth_column].dropna().sort_values()

            if len(depth_series) < 2:
                raise WpDataError(
                    "深度数据点不足，无法计算深度间隔",
                    context=ErrorContext(
                        operation="calculate_depth_sampling_rate",
                        dataset_name=str(self.name),
                        additional_info={
                            "valid_depth_points": len(depth_series),
                            "minimum_required": 2
                        }
                    )
                )

            # 计算相邻点之间的间隔
            intervals = depth_series.diff().dropna()

            # 过滤掉非正间隔（可能的数据错误）
            positive_intervals = intervals[intervals > 0]

            if positive_intervals.empty:
                raise WpDataError(
                    "未找到有效的正深度间隔",
                    context=ErrorContext(
                        operation="calculate_depth_sampling_rate",
                        dataset_name=str(self.name),
                        additional_info={
                            "total_intervals": len(intervals),
                            "positive_intervals": len(positive_intervals)
                        }
                    )
                )

            # 对于连续型数据集，返回最常见的间隔值（众数）
            # 使用round来处理浮点精度问题
            rounded_intervals = positive_intervals.round(6)  # 保留6位小数
            mode_interval = rounded_intervals.mode()

            if len(mode_interval) > 0:
                return float(mode_interval.iloc[0])
            else:
                # 如果没有众数，返回平均间隔
                return float(positive_intervals.mean())

        except WpDataError:
            # 重新抛出WpDataError
            raise
        except Exception as e:
            raise WpDataError(
                "计算深度间隔时发生错误",
                context=ErrorContext(
                    operation="calculate_depth_sampling_rate",
                    dataset_name=str(self.name),
                    additional_info={
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    }
                )
            ) from e

    def resample_depth(
        self,
        new_sampling_interval: float,
        *,
        depth_range: tuple[float, float] | None = None,
        interpolation_method: str = "nearest",
        out_of_range_fill_value: Any = np.nan,
        new_dataset_name: str | None = None
    ) -> "WpContinuousDataset":
        """对连续型数据集进行深度重采样。

        将当前连续型数据集重采样为新的深度采样间隔，支持多种插值方法和自定义深度范围。

        Args:
            new_sampling_interval: 新的深度采样间隔（必须 > 0）
            depth_range: 输出深度范围，None表示使用原数据集范围
            interpolation_method: 插值方法，默认"nearest"
            out_of_range_fill_value: 超出原数据集范围的填充值
            new_dataset_name: 新数据集名称，None表示自动生成

        Returns:
            WpContinuousDataset: 重采样后的连续型数据集

        Raises:
            WpValidationError: 当参数验证失败时抛出
            WpDataError: 当数据集状态异常时抛出

        Note:
            - 重采样后的深度曲线名称保持为原名称
            - 保留原有的井名曲线
            - 使用pandas插值方法进行数据重采样
            - 深度范围会按采样间隔对齐
            - 插值方法优先级：强制方法 > 数据类型约束 > 用户指定方法
            - 至少需要2个数据点进行插值
            - 指定的深度范围必须大于等于原数据集的深度范围

        Examples:
            >>> # 基本重采样（更密集的采样）
            >>> resampled_dataset = continuous_dataset.resample_depth(
            ...     new_sampling_interval=0.25  # 从0.5m变为0.25m
            ... )
            >>> assert resampled_dataset.depth_sampling_rate == 0.25
            >>> assert len(resampled_dataset.df) >= len(continuous_dataset.df)

            >>> # 线性插值重采样
            >>> linear_dataset = continuous_dataset.resample_depth(
            ...     new_sampling_interval=1.0,
            ...     interpolation_method="linear"
            ... )

            >>> # 扩展深度范围重采样
            >>> extended_dataset = continuous_dataset.resample_depth(
            ...     new_sampling_interval=0.5,
            ...     depth_range=(2490.0, 2530.0),  # 扩展范围
            ...     interpolation_method="spline",
            ...     out_of_range_fill_value=-999,  # 扩展区域填充-999
            ...     new_dataset_name="extended_logging_data"
            ... )

            >>> # 粗化采样（降低数据密度）
            >>> coarse_dataset = continuous_dataset.resample_depth(
            ...     new_sampling_interval=2.0,  # 从0.5m变为2.0m
            ...     interpolation_method="linear"
            ... )
            >>> assert len(coarse_dataset.df) <= len(continuous_dataset.df)
        """
        # 导入放在方法内部避免循环导入
        from logwp.models.datasets.internal.continuous_resample import resample_continuous_dataset

        logger.info(
            "开始连续型数据集深度重采样",
            operation="resample_depth",
            dataset_name=str(self.name),
            original_sampling_interval=self.depth_sampling_rate,
            new_sampling_interval=new_sampling_interval,
            custom_depth_range=depth_range is not None,
            interpolation_method=interpolation_method,
            custom_dataset_name=new_dataset_name is not None
        )

        # 调用服务层进行重采样
        resampled_df, resampled_metadata, actual_depth_range = resample_continuous_dataset(
            self,
            new_sampling_interval,
            depth_range=depth_range,
            interpolation_method=interpolation_method,
            out_of_range_fill_value=out_of_range_fill_value
        )

        # 确定新数据集名称
        if new_dataset_name is None:
            new_dataset_name = f"{self.name}_resampled"

        # 创建新的连续型数据集
        resampled_dataset = WpContinuousDataset.create_with_data(
            name=new_dataset_name,
            df=resampled_df,
            curve_metadata=resampled_metadata
        )

        # 设置新的深度采样间隔
        resampled_dataset.depth_sampling_rate = new_sampling_interval

        logger.info(
            "连续型数据集深度重采样完成",
            operation="resample_depth",
            original_dataset=str(self.name),
            new_dataset=str(resampled_dataset.name),
            original_rows=len(self.df),
            resampled_rows=len(resampled_df),
            actual_depth_range=actual_depth_range,
            new_sampling_interval=new_sampling_interval
        )

        return resampled_dataset
