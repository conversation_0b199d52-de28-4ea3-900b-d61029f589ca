# 真实测井数据说明

`tests/test_data/real`目录下存放了用于模块测试的真实测井数据。

## swift-pso模块测试数据

`tests/test_data/real/swift_pso`目录下存放了用于SWIFT-PSO模块测试的真实测井数据。

### `swift_pso_train.wp.xlsx`

用于swift-pso训练的测井数据,wp文件格式。

- 数据集名称：`swift_pso`
- 数据集类型：`Point`
- 测井曲线名称：'PHIT_NMR','T2LM','DT2_P50','T2_P50','DPHIT_NMR','PHI_T2_DIST', 'K_LABEL','K_LABEL_TYPE'
  - PHIT_T2_DIST: 为T2谱曲线（二维组合曲线），共64维，T2时间范围为0.1~10000ms，注意它在训练时需要映射为`T2_Value`
  - 除了PHIT_T2_DIST,其它曲线名与swift-pso训练要求的曲线名一致。
- 共有两口井：C-1和T-1，共有216行数据

### `swift_pso_pred.wp.xlsx`

用于swift-pso预测的测井数据,wp文件格式。

- 数据集名称：`swift_pso_train`
- 数据集类型：`Continuous`
- 测井曲线名称与`swift_pso_train.wp.xlsx`一致，但是作为预测数据集，是没有`K_LABEL`和`K_LABEL_TYPE`的。
- 共有四口井：C-1和T-1，共有2668行数据

### `swift_pso_pred_apply.wp.xlsx`

应用swift-pso模型计算过渗透率的测井数据,wp文件格式。

- 数据集名称：`apply_predictions`
- 数据集类型：`Continuous`
- 计算得到的渗透率曲线名称：`K_PRED_SWIFT`
- 其它测井曲线名称与`swift_pso_pred.wp.xlsx`一致
- 共有四口井：C-1和T-1，共有2668行数据

### `swift_pso_val_plt.wp.xlsx`

用于swift-pso的PLT生产测井解释盲井验证数据,wp文件格式。

- 数据集名称：`PLT`
- 数据集类型：`Interval`
- 产量测井曲线名称：`QOZI`
- 只有一口井：C-1
- 共有8个产层


### `swift_pso_val_cored_perm.wp.xlsx`

用于swift-pso的岩心渗透率盲井验证数据,wp文件格式。

- 数据集名称：`K_Val`
- 数据集类型：`Point`
- 只有一口井：T-1
- 渗透率曲线名称：`PERM`
- 共有45个深度点的数据
