"""井名映射解析服务。

解析_Well_Map表单，构造WpWellMap对象。实现WFS 6.1节规范，支持井名重映射、
大小写不敏感处理、循环映射检测等功能。

注意：本模块仅解析映射关系，不自动应用到数据集。
井名映射的应用由用户主动触发。
"""

from __future__ import annotations

from typing import Dict, List, Set, Iterator

import openpyxl
import structlog

from logwp.io.exceptions import WpFileFormatError
from logwp.models.exceptions import WpValidationError
from logwp.io.constants import WpXlsxKey
from logwp.models.mapping import WpWellMap

logger = structlog.get_logger(__name__)


def parse_well_map_sheet(row_iterator: Iterator[tuple], sheet_name: str) -> WpWellMap:
    """从_Well_Map工作表的行迭代器解析井名映射。

    实现WFS 6.1节规范：
    - WELL → MAP_WELL映射关系解析
    - 大小写不敏感处理
    - 仅解析映射关系，不自动应用到数据集

    Args:
        row_iterator: 行数据迭代器
        sheet_name: 工作表名称，用于日志记录

    Returns:
        WpWellMap: 构造的井名映射对象

    Raises:
        WpFileFormatError: 表单结构不符合WFS规范
        WpValidationError: 映射数据验证失败

    References:
        《SCAPE_WFS_WP文件规范.md》6.1节 - 井名映射规范
    """
    logger.debug("开始解析_Well_Map表单", sheet_name=sheet_name)

    # 获取并验证表头行
    try:
        header_row = next(row_iterator)
    except StopIteration:
        logger.warning("_Well_Map表单为空", sheet_name=sheet_name)
        return WpWellMap()

    validate_well_map_structure(header_row, sheet_name)

    # 创建空的WpWellMap对象
    well_map = WpWellMap()

    # 解析映射关系
    mapping_count = 0
    for row_num, row in enumerate(row_iterator):
        if _is_empty_row(row):
            continue

        try:
            well_name, map_well_name = _parse_mapping_row(row, row_num)
            if well_name and map_well_name:
                well_map.add_mapping(well_name, map_well_name)
                mapping_count += 1

        except Exception as e:
            logger.warning(
                "跳过无效映射行",
                row_number=row_num,
                row_data=row,
                error=str(e)
            )


    logger.info(
        "_Well_Map表单解析完成",
        sheet_name=sheet_name,
        mapping_count=mapping_count
    )

    return well_map


def validate_well_map_structure(header_row: tuple, sheet_name: str) -> None:
    """验证井名映射表单结构。

    Args:
        header_row: 表头行数据
        sheet_name: 工作表名称，用于日志记录

    Raises:
        WpFileFormatError: 表单结构不符合规范

    References:
        《SCAPE_WFS_WP文件规范.md》6.1.1节 - 表单结构
    """
    if len(header_row) < 2:
        raise WpFileFormatError(f"_Well_Map表单 '{sheet_name}' 至少需要2列")

    # 验证列标题（大小写不敏感）
    well_col = str(header_row[0] or "").strip()
    map_well_col = str(header_row[1] or "").strip()

    if well_col.upper() != WpXlsxKey.WELLMAP_COL_WELL.value.upper():
        raise WpFileFormatError(
            f"_Well_Map表单 '{sheet_name}' 第1列标题错误: 期望'{WpXlsxKey.WELLMAP_COL_WELL}', 实际'{well_col}'"
        )

    if map_well_col.upper() != WpXlsxKey.WELLMAP_COL_MAP_WELL.value.upper():
        raise WpFileFormatError(
            f"_Well_Map表单 '{sheet_name}' 第2列标题错误: 期望'{WpXlsxKey.WELLMAP_COL_MAP_WELL}', 实际'{map_well_col}'"
        )


def _parse_mapping_row(row: tuple, row_number: int) -> tuple[str | None, str | None]:
    """解析单行映射数据。

    Args:
        row: 行数据元组
        row_number: 行号（用于错误报告）

    Returns:
        tuple[str | None, str | None]: (原井名, 映射井名)，无效行返回(None, None)
    """
    if len(row) < 2:
        return None, None

    well_name = str(row[0] or "").strip()
    map_well_name = str(row[1] or "").strip()

    # 两个字段都不能为空
    if not well_name or not map_well_name:
        return None, None

    # 检查自映射（井名映射到自己）
    if well_name.upper() == map_well_name.upper():
        logger.debug(
            "跳过自映射",
            row_number=row_number,
            well_name=well_name
        )
        return None, None

    return well_name, map_well_name


def detect_circular_mapping(mappings: Dict[str, str]) -> List[List[str]]:
    """检测循环映射，返回循环链。

    注意：仅检测并返回循环链，不抛出异常。
    循环映射的处理由业务层决定。

    Args:
        mappings: 井名映射字典 {原井名: 映射井名}

    Returns:
        List[List[str]]: 循环链列表，每个循环链是井名列表

    Examples:
        >>> mappings = {"A": "B", "B": "C", "C": "A", "D": "E"}
        >>> chains = detect_circular_mapping(mappings)
        >>> print(chains)  # [["A", "B", "C"]]

    References:
        《SCAPE_WFS_WP文件规范.md》6.2节 - 循环映射检测
    """
    if not mappings:
        return []

    # 大小写不敏感处理
    normalized_mappings = {}
    original_names = {}  # 保存原始大小写

    for well, map_well in mappings.items():
        well_upper = well.upper()
        map_well_upper = map_well.upper()
        normalized_mappings[well_upper] = map_well_upper
        original_names[well_upper] = well
        original_names[map_well_upper] = map_well

    # 使用DFS检测循环
    visited = set()
    rec_stack = set()
    circular_chains = []

    def dfs(node: str, path: List[str]) -> None:
        if node in rec_stack:
            # 找到循环，提取循环部分
            cycle_start = path.index(node)
            cycle = path[cycle_start:] + [node]
            # 转换回原始大小写
            original_cycle = [original_names.get(n, n) for n in cycle[:-1]]  # 去掉重复的最后一个
            circular_chains.append(original_cycle)
            return

        if node in visited:
            return

        visited.add(node)
        rec_stack.add(node)
        path.append(node)

        # 继续遍历
        if node in normalized_mappings:
            next_node = normalized_mappings[node]
            dfs(next_node, path)

        rec_stack.remove(node)
        path.pop()

    # 检查所有节点
    for well in normalized_mappings:
        if well not in visited:
            dfs(well, [])

    if circular_chains:
        logger.debug(
            "检测到循环映射",
            circular_count=len(circular_chains),
            chains=circular_chains
        )

    return circular_chains


def parse_well_mapping_rules(mappings: Dict[str, str]) -> Dict[str, Any]:
    """解析井名映射规则。

    WFS 6.2节：解析映射关系，检测循环映射。
    注意：仅解析和检测，不验证业务逻辑有效性。

    Args:
        mappings: 井名映射字典

    Returns:
        Dict[str, Any]: 解析结果，包含映射统计和循环检测结果

    Examples:
        >>> result = parse_well_mapping_rules({"A": "B", "B": "A"})
        >>> print(result["has_circular"])  # True
    """
    if not mappings:
        return {
            "mapping_count": 0,
            "unique_sources": 0,
            "unique_targets": 0,
            "has_circular": False,
            "circular_chains": []
        }

    # 统计信息
    unique_sources = set(mappings.keys())
    unique_targets = set(mappings.values())

    # 检测循环映射
    circular_chains = detect_circular_mapping(mappings)

    result = {
        "mapping_count": len(mappings),
        "unique_sources": len(unique_sources),
        "unique_targets": len(unique_targets),
        "has_circular": len(circular_chains) > 0,
        "circular_chains": circular_chains,
        "bidirectional_count": 0
    }

    logger.debug("井名映射规则解析完成", **result)
    return result


def _is_empty_row(row: tuple) -> bool:
    """检查是否为空行。

    Args:
        row: 行数据元组

    Returns:
        bool: 是否为空行
    """
    return all(not str(cell or "").strip() for cell in row)
