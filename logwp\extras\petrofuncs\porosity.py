"""
孔隙度计算模型
"""
import numpy as np

from logwp.extras.units import Quantity
from .validators import validate_dimensions


@validate_dimensions(dt='slowness', dt_matrix='slowness', dt_fluid='slowness')
def wyllie_phi(
    dt: Quantity,
    dt_matrix: Quantity,
    dt_fluid: Quantity
) -> float:
    """
    使用经典的威利(Wyl<PERSON>)时间平均公式计算总孔隙度(phi)。

    该公式适用于压实、干净的饱和地层。

    公式: phi = (dt - dt_matrix) / (dt_fluid - dt_matrix)

    参数:
        dt (Quantity): 从声波测井中读出的地层声波时差，带有声波时差量纲。
        dt_matrix (Quantity): 岩石骨架的声波时差，带有声波时差量纲。
        dt_fluid (Quantity): 孔隙中流体的声波时差，带有声波时差量纲。

    返回:
        float: 计算出的总孔隙度(phi)，无量纲小数。
    """
    # 由于所有输入都是带有单位的 Quantity 对象，可以直接进行减法运算。
    # `units` 包会自动处理单位换算，确保分子和分母的单位一致。
    numerator = dt - dt_matrix
    denominator = dt_fluid - dt_matrix

    # 除法结果是一个无量纲的 Quantity 对象，我们取其 .value
    phi = (numerator / denominator).value

    # 孔隙度值应在0和1之间，进行裁剪以保证物理意义
    return np.clip(phi, 0.0, 1.0)


@validate_dimensions(rho_b='density', rho_matrix='density', rho_fluid='density')
def density_phi(
    rho_b: Quantity,
    rho_matrix: Quantity,
    rho_fluid: Quantity
) -> float:
    """
    使用密度测井值计算孔隙度(phi)。

    这是最常用的孔隙度计算方法之一。

    公式: phi = (rho_matrix - rho_b) / (rho_matrix - rho_fluid)

    参数:
        rho_b (Quantity): 从密度测井中读出的地层体积密度，带有密度量纲。
        rho_matrix (Quantity): 岩石骨架的密度，带有密度量纲。
        rho_fluid (Quantity): 孔隙中流体的密度，带有密度量纲。

    返回:
        float: 计算出的孔隙度(phi)，无量纲小数。
    """
    # 同样，由于所有输入都是带有单位的 Quantity 对象，可以直接进行数学运算。
    numerator = rho_matrix - rho_b
    denominator = rho_matrix - rho_fluid

    phi = (numerator / denominator).value

    # 孔隙度值应在0和1之间，进行裁剪以保证物理意义
    return np.clip(phi, 0.0, 1.0)
