{"run_id": "20250724-151240-68730aa4", "start_time_utc": "2025-07-24T15:12:40.877309Z", "end_time_utc": "2025-07-24T15:29:43.102386Z", "duration_seconds": 1022.225, "status": "COMPLETED", "lineage": {"inputs": {}, "code_version": {}}, "config_snapshot_path": null, "parameters": {}, "metrics": {}, "artifacts": {"obmiq_training.configs.training_config_snapshot": {"path": "obmiq_training_pytorch/training_config.json", "type": "data", "metadata": {"hash": "125d942044ba18c2867609b9343f14a999a1b050ee63c4631c4d0e1a5fe4ea24", "hash_algorithm": "sha256", "size_bytes": 512}, "description": "Snapshot of the training configuration used for this run."}, "obmiq_training.reports.cv_performance": {"path": "obmiq_training_pytorch/cv_performance_report.csv", "type": "data", "metadata": {"hash": "89fa7371f04151f10128ebeb4bef5449ca6cc68b59532f9533b4ccb018ce2701", "hash_algorithm": "sha256", "size_bytes": 295}, "description": "LOWO-CV中每一折的最佳验证损失和对应的超参数。"}, "obmiq_training.reports.hyperparameter_tuning": {"path": "obmiq_training_pytorch/hyperparameter_tuning_report.json", "type": "data", "metadata": {"hash": "83de932e0d1950b4006dd35c6d64b505e38ba9b424b0b291b3b1fd0b5d85d498", "hash_algorithm": "sha256", "size_bytes": 200}, "description": "在所有CV折中聚合得到的全局最佳超参数组合。"}, "obmiq_training.reports.lowo_cv_performance_summary": {"path": "obmiq_training_pytorch/lowo_cv_performance_summary.csv", "type": "data", "metadata": {"hash": "b42c7eb3ebe7a8f09db31fcc7b10644f31ff3dccd7f24ccfa15707500a33de65", "hash_algorithm": "sha256", "size_bytes": 208}, "description": "LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。"}, "obmiq_training.data_snapshots.lowo_cv_predictions": {"path": "obmiq_training_pytorch/lowo_cv_predictions.csv", "type": "data", "metadata": {"hash": "36c9105c62d9e29a35326c3a4a1dab9b65748ef5efccb2dcd40b4cc6264ab755", "hash_algorithm": "sha256", "size_bytes": 2152285}, "description": "LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。"}, "obmiq_training.plots.lowo_cv_crossplot_dt2_p50": {"path": "obmiq_training_pytorch/lowo_cv_crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "4f964985e6305a98b75fe418dbcdeff2cd7a4fc5c6199566e26604b336969a07", "hash_algorithm": "sha256", "size_bytes": 231429}, "description": "LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dt2 P50"}, "obmiq_training.plots.lowo_cv_crossplot_dphit_nmr": {"path": "obmiq_training_pytorch/lowo_cv_crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "41b836ba35ac9682f709717d1a80bcc5d383796c25fdf5c7ab9c86a21d4b77d6", "hash_algorithm": "sha256", "size_bytes": 162479}, "description": "LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dphit Nmr"}, "obmiq_training.logs.tensorboard": {"path": "obmiq_training_pytorch/tensorboard_logs", "type": "unknown", "metadata": {"size_bytes": 0}, "description": "用于TensorBoard可视化的日志文件目录。"}, "obmiq_training.models.assets_pytorch": {"path": "obmiq_training_pytorch/model_assets_pytorch.pkl", "type": "model", "metadata": {"hash": "403a4078d2cf887213f41d49499f65c54e0cfd3fd83ab3b6f607c55b8b7ba132", "hash_algorithm": "sha256", "size_bytes": 21507}, "description": "包含模型权重、超参数和预处理器的PyTorch模型资产包。"}, "obmiq_training.data_snapshots.final_training_history": {"path": "obmiq_training_pytorch/final_training_history.csv", "type": "data", "metadata": {"hash": "9ee9a32d80a825911db5940e0cfd6c716dd033953414eec8c49d9c4d6a2c75b0", "hash_algorithm": "sha256", "size_bytes": 8061}, "description": "最终模型训练过程中的损失变化历史。"}, "obmiq_training.plots.final_training_history": {"path": "obmiq_training_pytorch/final_training_history.png", "type": "image", "metadata": {"hash": "ee6ded772351c0ce9d2b242cdbbddc6afd531a7a4ccd87ad4f5e991676d5b246", "hash_algorithm": "sha256", "size_bytes": 225608}, "description": "最终模型训练的损失曲线图。"}, "obmiq_training.data_snapshots.final_model_evaluation": {"path": "obmiq_training_pytorch/final_model_evaluation.csv", "type": "data", "metadata": {"hash": "8c11cb0c6134f3c205ee0524bd66262bd8b5f911b121967858729ded40607b1e", "hash_algorithm": "sha256", "size_bytes": 2263137}, "description": "最终模型在全部训练数据上的预测和残差结果。"}, "obmiq_training.plots.eval_crossplot_dt2_p50": {"path": "obmiq_training_pytorch/eval_crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "f1f4e305ebd84f10702303a046add43963f2282ae1e33e74dec5310c9fe6fb9c", "hash_algorithm": "sha256", "size_bytes": 788371}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50"}, "obmiq_training.plots.eval_crossplot_dphit_nmr": {"path": "obmiq_training_pytorch/eval_crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "b89eb1a9854316fe1d872620de93d62971dff04de0e4cb9cafada040ab35bff4", "hash_algorithm": "sha256", "size_bytes": 727137}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr"}, "obmiq_training.plots.eval_residuals_plot_dt2_p50": {"path": "obmiq_training_pytorch/eval_residuals_plot_dt2_p50.png", "type": "image", "metadata": {"hash": "bbda2c25567d3ae9aebc7e5d51bb4af94e81476a1201cb5d4c4cf1a869682cab", "hash_algorithm": "sha256", "size_bytes": 882553}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50"}, "obmiq_training.plots.eval_residuals_plot_dphit_nmr": {"path": "obmiq_training_pytorch/eval_residuals_plot_dphit_nmr.png", "type": "image", "metadata": {"hash": "eeafafda4fe8a8591acf156c6f7d7701ecd8d2232d9e036df857083cbac53b07", "hash_algorithm": "sha256", "size_bytes": 1102284}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr"}, "obmiq_training.plots.eval_residuals_hist_dt2_p50": {"path": "obmiq_training_pytorch/eval_residuals_hist_dt2_p50.png", "type": "image", "metadata": {"hash": "1bd47fdf81d951e92d5f07a60c9ac697945ebbdf2ae6a681963f309f6b105e5e", "hash_algorithm": "sha256", "size_bytes": 194018}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50"}, "obmiq_training.plots.eval_residuals_hist_dphit_nmr": {"path": "obmiq_training_pytorch/eval_residuals_hist_dphit_nmr.png", "type": "image", "metadata": {"hash": "d2acccd6442d05a96c91bfded755358dc988a197f94242cc159b36f9cb9dff86", "hash_algorithm": "sha256", "size_bytes": 190295}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr"}, "obmiq_training.plots.captum_ig_summary_DT2_P50": {"path": "obmiq_training_pytorch/captum_ig_summary_DT2_P50.png", "type": "image", "metadata": {}, "description": "Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。"}, "obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50": {"path": "obmiq_training_pytorch/captum_ig_summary_DT2_P50_data.csv", "type": "data", "metadata": {"hash": "1c29f26bec90b202c061f6f72f795fcb277506c8580915cdcb380ba1fcce5158", "hash_algorithm": "sha256", "size_bytes": 555045}, "description": "Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50)"}, "obmiq_training.plots.captum_ig_summary_DPHIT_NMR": {"path": "obmiq_training_pytorch/captum_ig_summary_DPHIT_NMR.png", "type": "image", "metadata": {}, "description": "Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。"}, "obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR": {"path": "obmiq_training_pytorch/captum_ig_summary_DPHIT_NMR_data.csv", "type": "data", "metadata": {"hash": "86c061e1bcd365a08e820bc567f16bfbe72e1a7b20a4a3f59ad6eae5d44e3853", "hash_algorithm": "sha256", "size_bytes": 606650}, "description": "Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR)"}, "obmiq_training.data_snapshots.captum_sequence_attributions_dir": {"path": "obmiq_training_pytorch/captum_sequence_attributions_dir", "type": "unknown", "metadata": {"size_bytes": 4096}}, "obmiq_training.plots.captum_saliency_examples_dir": {"path": "obmiq_training_pytorch/captum_saliency_examples_dir", "type": "unknown", "metadata": {"size_bytes": 12288}}, "obmiq_training.models.onnx_model": {"path": "obmiq_training_pytorch/obmiq_model.onnx", "type": "unknown", "metadata": {"hash": "da4f4bde781ea22418c77ac1c5b8f0c9c025e2a0247cba74c6e460237367db34", "hash_algorithm": "sha256", "size_bytes": 15430}, "description": "可用于跨平台部署的ONNX格式模型。"}, "obmiq_prediction.datasets.predictions": {"path": "obmiq_prediction_pytorch/predictions.csv", "type": "data", "metadata": {"hash": "2c47e5ddbb83e7cd7565af94675205faf721a8c07c953b336834d8ae546736eb", "hash_algorithm": "sha256", "size_bytes": 3601061}, "description": "包含原始输入和模型预测结果的数据集"}, "obmiq_prediction.plots.crossplot_dt2_p50": {"path": "obmiq_prediction_pytorch/crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "4d19936b16196c47f7214ee052f8190098e1b844a70735ef44f1dcbd84e17976", "hash_algorithm": "sha256", "size_bytes": 693863}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50"}, "obmiq_prediction.plots.crossplot_dphit_nmr": {"path": "obmiq_prediction_pytorch/crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "f9e630b4a276afb7c763dbfdb2d296a8eb27fa8b123a580b289c27c7d2177233", "hash_algorithm": "sha256", "size_bytes": 491714}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr"}, "obmiq_prediction.plots.residuals_plot_dt2_p50": {"path": "obmiq_prediction_pytorch/residuals_plot_dt2_p50.png", "type": "image", "metadata": {"hash": "c4f2ab87128d715b6fe929997c0f15d1598235ead67aacf8d2c51ab80f7a76de", "hash_algorithm": "sha256", "size_bytes": 878763}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50"}, "obmiq_prediction.plots.residuals_plot_dphit_nmr": {"path": "obmiq_prediction_pytorch/residuals_plot_dphit_nmr.png", "type": "image", "metadata": {"hash": "2c85b4e26e38d617e66b1e4320908b755cf2e5fccd89c27aac3e12c598bd4b80", "hash_algorithm": "sha256", "size_bytes": 1104226}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr"}, "obmiq_prediction.plots.residuals_hist_dt2_p50": {"path": "obmiq_prediction_pytorch/residuals_hist_dt2_p50.png", "type": "image", "metadata": {"hash": "a770e08b8f434e55b5ff1b24b8e8f0183bb2fbb32558172e3e76b9e62a85decf", "hash_algorithm": "sha256", "size_bytes": 193848}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50"}, "obmiq_prediction.plots.residuals_hist_dphit_nmr": {"path": "obmiq_prediction_pytorch/residuals_hist_dphit_nmr.png", "type": "image", "metadata": {"hash": "dd4dadfa15a3608ad9d370be3f34398d43aa4c67c79ade518dcb6c9ce6d63531", "hash_algorithm": "sha256", "size_bytes": 206862}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr"}}}