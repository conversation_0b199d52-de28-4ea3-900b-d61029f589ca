"""logwp.infra.color_utils - 通用颜色处理工具

本模块提供与颜色表示、转换和操作相关的底层工具函数。
这些函数是纯粹的、无状态的，并且不依赖于任何上层业务逻辑。

Architecture
------------
层次/依赖: infra层，无外部依赖
设计原则: 纯函数、高性能、高复用性
"""


def hex_to_rgba(hex_color: str, opacity: float) -> str:
    """
    将一个HEX颜色代码转换为带有透明度的RGBA字符串。

    Args:
        hex_color: 6位的HEX颜色代码 (e.g., "FF0000" or "#FF0000")。
        opacity: 透明度，范围从 0.0 (完全透明) 到 1.0 (完全不透明)。

    Returns:
        一个Plotly和CSS兼容的RGBA颜色字符串 (e.g., "rgba(255, 0, 0, 0.5)")。

    Raises:
        ValueError: 如果HEX颜色代码格式无效。

    Examples:
        >>> hex_to_rgba("FF0000", 0.5)
        'rgba(255, 0, 0, 0.5)'
        >>> hex_to_rgba("#00FF00", 1.0)
        'rgba(0, 255, 0, 1.0)'
    """
    hex_color = hex_color.lstrip('#')
    if len(hex_color) != 6:
        raise ValueError(f"无效的6位HEX颜色代码: {hex_color}")
    r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    return f'rgba({r}, {g}, {b}, {opacity})'
