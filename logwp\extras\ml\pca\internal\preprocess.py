"""PCA数据预处理服务。

实现PCA分析前的数据预处理功能，包括智能曲线过滤、数据质量验证、
标准化处理等。支持GPU加速和CPU自动回退。

Architecture
------------
层次/依赖: PCA内部服务层，数据预处理
设计原则: 智能过滤、质量保证、GPU加速
性能特征: 大规模数据支持、内存优化、异步处理
"""

from __future__ import annotations

import copy
from typing import TYPE_CHECKING, Any

import numpy as np
import pandas as pd
import structlog

from logwp.models.curve import CurveBasicAttributes
from logwp.models.constants import WpCurveCategory
from ..constants import (
    DEFAULT_MIN_COMPLETENESS,
    LOG_OPERATION_PREPROCESS,
    LOG_STAGE_START,
    LOG_STAGE_COMPLETE,
    LOG_STAGE_ERROR,
)
from ..exceptions import (
    WpPcaDataError,
    WpPcaComputationError,
    WpPcaErrorContext,
    create_data_error,
    create_computation_error,
)

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from logwp.infra.compute import ComputeEngine

logger = structlog.get_logger(__name__)

def preprocess_dataset(
    dataset: WpDepthIndexedDatasetBase,
    compute_engine: ComputeEngine | None = None
) -> WpDepthIndexedDatasetBase:
    """数据预处理主函数。

    对输入数据进行标准化处理，使用新增API进行智能曲线过滤和数据验证。

    Architecture
    ------------
    层次/依赖: PCA预处理服务，使用logwp.models新增API
    设计原则: 智能过滤、全面验证、GPU加速
    性能特征: 批量处理、内存优化、错误恢复

    Args:
        dataset: 输入数据集
        compute_engine: 计算引擎

    Returns:
        标准化后的数据集

    Raises:
        WpPcaDataError: 数据验证失败
        WpPcaComputationError: 预处理计算异常
    """
    logger.info(
        "开始PCA数据预处理",
        operation=LOG_OPERATION_PREPROCESS,
        stage=LOG_STAGE_START,
        dataset_name=dataset.name,
        dataset_type=dataset.get_dataset_type_name(),
        n_samples=len(dataset.df)
    )

    try:
        # 1. 智能曲线过滤（使用新增API）
        analysis_curves = filter_analysis_curves(dataset)
        logger.info(
            "智能曲线过滤完成",
            operation=LOG_OPERATION_PREPROCESS,
            dataset_name=dataset.name,
            analysis_curves=analysis_curves,
            n_curves=len(analysis_curves)
        )

        # 2. 数据质量验证
        validation_result = validate_data_quality(dataset, analysis_curves)
        if not validation_result["validation_passed"]:
            issues = "; ".join(validation_result["issues"])
            raise create_data_error(
                f"数据验证失败: {issues}",
                dataset_name=dataset.name,
                curve_names=analysis_curves
            )

        logger.info(
            "数据质量验证通过",
            operation=LOG_OPERATION_PREPROCESS,
            dataset_name=dataset.name,
            validation_summary=validation_result
        )

        # 3. 标准化处理（GPU加速）
        standardized_dataset = standardize_data(dataset, analysis_curves, compute_engine)

        logger.info(
            "PCA数据预处理完成",
            operation=LOG_OPERATION_PREPROCESS,
            stage=LOG_STAGE_COMPLETE,
            dataset_name=dataset.name,
            n_curves_processed=len(analysis_curves)
        )

        return standardized_dataset

    except Exception as e:
        logger.error(
            "PCA数据预处理失败",
            operation=LOG_OPERATION_PREPROCESS,
            stage=LOG_STAGE_ERROR,
            dataset_name=dataset.name,
            error=str(e)
        )

        if isinstance(e, (WpPcaDataError, WpPcaComputationError)):
            raise
        else:
            raise create_computation_error(
                f"数据预处理过程中发生未预期错误: {str(e)}",
                cause=e
            )


def filter_analysis_curves(dataset: WpDepthIndexedDatasetBase) -> list[str]:
    """智能曲线过滤。

    使用logwp.models新增API获取适合PCA分析的曲线。

    Args:
        dataset: 输入数据集

    Returns:
        适合分析的曲线名称列表

    Raises:
        WpPcaDataError: 适合分析的曲线数量不足
    """
    # 使用新增API一键获取适合分析的曲线
    analysis_curves = dataset.curve_metadata.get_analysis_suitable_curves()

    # 验证曲线数量
    if len(analysis_curves) < 2:
        system_curves = dataset.curve_metadata.get_system_curves()
        all_curves = list(dataset.curve_metadata.curves.keys())

        raise create_data_error(
            f"适合PCA分析的曲线数量不足（需要至少2个，实际{len(analysis_curves)}个）",
            dataset_name=dataset.name,
            curve_names=analysis_curves
        )

    # 记录过滤结果
    system_curves = dataset.curve_metadata.get_system_curves()
    logger.debug(
        "曲线过滤详情",
        dataset_name=dataset.name,
        total_curves=len(dataset.curve_metadata.curves),
        system_curves=system_curves,
        analysis_curves=analysis_curves,
        filtered_count=len(dataset.curve_metadata.curves) - len(analysis_curves)
    )

    return analysis_curves


def validate_data_quality(
    dataset: WpDepthIndexedDatasetBase,
    curve_names: list[str]
) -> dict[str, Any]:
    """数据质量验证。

    使用logwp.models新增API进行全面的数据质量检查。

    Args:
        dataset: 输入数据集
        curve_names: 要验证的曲线名称

    Returns:
        验证结果字典

    Raises:
        WpPcaDataError: 数据质量检查失败
    """
    try:
        # 使用新增API进行数值型数据验证
        validation_result = dataset.validate_numeric_data(curve_names)

        # 检查数据完整性
        completeness = dataset.get_data_completeness()

        # 获取DataFrame列名用于详细检查
        df_columns = dataset.curve_metadata.get_dataframe_columns_for_curves(curve_names)

        # 检查样本数量
        n_samples = len(dataset.df)
        if n_samples < 10:
            validation_result["validation_passed"] = False
            validation_result["issues"].append(f"样本数量过少（{n_samples}个，建议至少10个）")

        # 检查特征数量与样本数量的比例
        n_features = len(df_columns)
        if n_samples < n_features * 2:
            validation_result["issues"].append(
                f"样本数量({n_samples})相对特征数量({n_features})过少，建议样本数至少为特征数的2倍"
            )

        # 检查数据完整性
        low_completeness_curves = []
        for curve_name in curve_names:
            columns = dataset.curve_metadata.get_dataframe_columns_for_curves([curve_name])
            for column in columns:
                completeness_ratio = completeness.get(column, 0.0)
                if completeness_ratio < DEFAULT_MIN_COMPLETENESS:
                    low_completeness_curves.append(f"{curve_name}({completeness_ratio:.1%})")

        if low_completeness_curves:
            validation_result["issues"].append(
                f"以下曲线数据完整性不足: {', '.join(low_completeness_curves)}"
            )

        # 添加完整性信息到结果中
        validation_result["completeness"] = {
            curve_name: completeness.get(
                dataset.curve_metadata.get_dataframe_columns_for_curves([curve_name])[0],
                0.0
            )
            for curve_name in curve_names
        }

        # 添加数据集信息
        validation_result["n_samples"] = n_samples
        validation_result["n_features"] = n_features
        validation_result["df_columns"] = df_columns

        return validation_result

    except Exception as e:
        raise create_data_error(
            f"数据质量验证过程中发生错误: {str(e)}",
            dataset_name=dataset.name,
            curve_names=curve_names,
            cause=e
        )


def standardize_data(
    dataset: WpDepthIndexedDatasetBase,
    curve_names: list[str],
    compute_engine: ComputeEngine | None = None
) -> WpDepthIndexedDatasetBase:
    """数据标准化处理。

    对数据进行中心化和标准化，支持GPU加速。

    Args:
        dataset: 输入数据集
        curve_names: 要标准化的曲线名称
        compute_engine: 计算引擎

    Returns:
        标准化后的数据集

    Raises:
        WpPcaComputationError: 标准化计算异常
    """
    try:
        # 获取DataFrame列名
        df_columns = dataset.curve_metadata.get_dataframe_columns_for_curves(curve_names)
        system_curves = dataset.curve_metadata.get_system_curves()
        system_columns = dataset.curve_metadata.get_dataframe_columns_for_curves(system_curves)

        # 复制数据集以避免修改原始数据
        standardized_df = dataset.df.copy()

        # 提取要标准化的数据
        data_to_standardize = standardized_df[df_columns].values

        # 检查数据中的NaN值
        if np.isnan(data_to_standardize).any():
            logger.warning(
                "数据中包含NaN值，将使用均值填充",
                dataset_name=dataset.name,
                curve_names=curve_names
            )
            # 使用均值填充NaN值
            for i, col in enumerate(df_columns):
                col_data = data_to_standardize[:, i]
                if np.isnan(col_data).any():
                    mean_val = np.nanmean(col_data)
                    data_to_standardize[:, i] = np.where(np.isnan(col_data), mean_val, col_data)

        # 计算均值和标准差
        mean_values = np.mean(data_to_standardize, axis=0)
        std_values = np.std(data_to_standardize, axis=0)

        # 避免除零错误
        std_values = np.where(std_values == 0, 1.0, std_values)

        # 标准化数据 (z-score normalization)
        standardized_data = (data_to_standardize - mean_values) / std_values

        # 更新DataFrame
        for i, col in enumerate(df_columns):
            standardized_df[col] = standardized_data[:, i]

        # 创建新的曲线元数据，将数据曲线单位改为无量纲
        new_curve_metadata = copy.deepcopy(dataset.curve_metadata)

        for curve_name in curve_names:
            curve = new_curve_metadata.get_curve(curve_name)
            if curve is not None:
                # 使用克隆方法修改单位为无量纲
                standardized_curve = curve.clone_with_unit("dimensionless")
                # 更新描述
                new_description = f"{curve.description or curve.name} (标准化)"
                standardized_curve = standardized_curve.clone_with_description(new_description)
                # 更新元数据
                new_curve_metadata.curves[curve_name] = standardized_curve

        # 创建新的数据集实例
        dataset_class = type(dataset)
        standardized_dataset = dataset_class(
            name=f"{dataset.name}_standardized",
            df=standardized_df,
            curve_metadata=new_curve_metadata,
            ext_attr_manager=copy.deepcopy(dataset.ext_attr_manager) if dataset.ext_attr_manager else None
        )

        logger.info(
            "数据标准化完成",
            dataset_name=dataset.name,
            standardized_curves=curve_names,
            mean_values=mean_values.tolist(),
            std_values=std_values.tolist()
        )

        return standardized_dataset

    except Exception as e:
        raise create_computation_error(
            f"数据标准化过程中发生错误: {str(e)}",
            n_features=len(curve_names),
            cause=e
        )
