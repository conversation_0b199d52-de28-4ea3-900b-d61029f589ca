"""scape.core.baselines.sdr.computer - SDR基准模型核心计算逻辑

包含SDR渗透率计算公式和参数优化器。

"""

from __future__ import annotations

from typing import Any, Dict

import numpy as np
from scipy.optimize import OptimizeResult, minimize


def _calculate_sdr_permeability(
    phit: np.ndarray, t2lm: np.ndarray, ksdr_a: float, phit_exp: float, rho_nmr: float, t2lm_exp: float
) -> np.ndarray:
    """根据SDR公式计算渗透率。

    k_SDR = KSDR_A * PHIT^PHIT_exp * (RHO_NMR * T2LM)^T2LM_exp

    Args:
        phit: 总孔隙度数组。
        t2lm: T2对数平均值数组。
        ksdr_a: SDR系数。
        phit_exp: 孔隙度指数。
        rho_nmr: 表面弛豫率。
        t2lm_exp: T2对数均值指数。

    Returns:
        计算出的渗透率数组。
    """
    epsilon = 1e-9  # 防止log(0)或除以零

    # 确保输入为正，以避免在计算中出现NaN或复数
    safe_phit = np.maximum(phit, epsilon)
    safe_t2lm = np.maximum(t2lm, epsilon)

    k_sdr = ksdr_a * (safe_phit**phit_exp) * ((rho_nmr * safe_t2lm) ** t2lm_exp)
    return np.maximum(k_sdr, epsilon)  # 确保渗透率输出为正


def find_sdr_parameters(data: Dict[str, np.ndarray]) -> Dict[str, Any]:
    """使用scipy.optimize寻找SDR模型的最佳参数。

    Args:
        data: 一个包含numpy数组的字典，键为 'phit_nmr', 't2lm', 'k_label'。

    Returns:
        一个包含优化结果的字典。
    """

    def _rmsle_loss(params: np.ndarray, data_dict: Dict[str, np.ndarray]) -> float:
        """均方根对数误差 (RMSLE) 损失函数。"""
        log10_ksdr_a, phit_exp, rho_nmr, t2lm_exp = params
        ksdr_a = 10**log10_ksdr_a

        k_pred = _calculate_sdr_permeability(
            data_dict["phit_nmr"], data_dict["t2lm"], ksdr_a, phit_exp, rho_nmr, t2lm_exp
        )
        k_label = data_dict["k_label"]

        log_diff_sq = (np.log10(k_pred) - np.log10(k_label)) ** 2
        return np.sqrt(np.mean(log_diff_sq))

    # 参数顺序: [log10_KSDR_A, PHIT_EXP, RHO_NMR, T2LM_EXP]
    bounds = [(-10, 4), (1, 5), (0.1, 50), (2, 5)]
    initial_guess = [0.0, 4.0, 25.0, 2.0]

    result: OptimizeResult = minimize(
        fun=_rmsle_loss,
        x0=np.array(initial_guess),
        args=(data,),
        method="L-BFGS-B",
        bounds=bounds,
        options={"disp": False},
    )

    optimized_params_values = result.x
    optimized_parameters = {
        "log10_KSDR_A": optimized_params_values[0],
        "PHIT_EXP": optimized_params_values[1],
        "RHO_NMR": optimized_params_values[2],
        "T2LM_EXP": optimized_params_values[3],
    }

    return {
        "optimized_parameters": optimized_parameters,
        "final_loss": result.fun,
        "success": result.success,
        "optimizer_status": result.message.decode("utf-8") if isinstance(result.message, bytes) else str(result.message),
    }
