"""scape.core.swift_pso.visualization_facade - SWIFT-PSO可视化步骤门面

实现t-SNE可视化步骤的公共接口，对SWIFT-PSO参数演化轨迹进行降维可视化。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO可视化步骤门面
设计原则: Facade模式、数据快照、绘图复现
性能特征: t-SNE计算优化、PlotProfile集成、产物管理

References
----------
- 《SCAPE_MS_方法说明书》§4.4.5 - Bootstrap+LOWO阶段θ演化轨迹t-SNE可视化
- 《logwp/extras/tracking/机器学习组件开发框架》§4 - 可复现性与可视化
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, Optional

if TYPE_CHECKING:
    from logwp.extras.tracking import RunContext

import pandas as pd

from logwp.infra import get_logger
from logwp.extras.plotting import PlotProfile, registry

from .config import TsneVisualConfig
from .constants import TsneVisualArtifacts, TsnePlotProfiles
from .artifact_handler import SwiftPsoArtifactHandler
from .internal import tsne_computer
from .plotting import replot_tsne_from_snapshot

logger = get_logger(__name__)


def run_tsne_visualization_step(
    config: TsneVisualConfig,
    ctx: RunContext,
    tsne_source_data: pd.DataFrame,
    *,
    plot_profiles: Optional[Dict[str, PlotProfile]] = None
) -> Dict[str, Any]:
    """执行t-SNE可视化步骤。

    对SWIFT-PSO训练产出的参数演化轨迹进行t-SNE降维分析和可视化，
    生成两张核心图表：
    1.  **收敛轨迹图**: 展示所有参数在优化过程中的演化路径。
    2.  **聚类分析图**: 对最终收敛的参数点进行聚类，并用凸包圈出，以分析解的稳定性。

    Args:
        config: t-SNE可视化步骤的配置对象
        ctx: 当前运行的上下文，用于追踪和产物管理
        tsne_source_data: t-SNE源数据DataFrame，即训练步骤产出的`all_parameters_from_lowo`，包含演化轨迹。
        plot_profiles (Optional[Dict[str, PlotProfile]], optional):
            一个可选的字典，用于传入自定义的PlotProfile对象以覆盖默认样式。
            键为 `TsnePlotProfiles` 枚举值 (如 "swift_pso.tsne_convergence")，
            值为 `PlotProfile` 实例。如果为 `None`，则使用默认配置。

    Returns:
        Dict[str, Any]: 包含可视化状态的轻量级字典：
            - "status": "completed"
            - "tsne_points": t-SNE降维后的数据点数量

    Raises:
        ValueError: 输入数据无效或配置错误时抛出
        KeyError: 指定的绘图配置不存在时抛出

    Artifacts:
        - swift_pso_visualization.plots.tsne_convergence_trajectory: 收敛轨迹图
        - swift_pso_visualization.data_snapshots.tsne_convergence_trajectory: 收敛轨迹数据快照
        - swift_pso_visualization.plots.tsne_cluster_analysis: 聚类分析图
        - swift_pso_visualization.data_snapshots.tsne_cluster_analysis: 聚类分析数据快照
        - swift_pso_visualization.reports.tsne_cluster_analysis: 聚类分析量化报告 (JSON)
        - swift_pso_visualization.configs.*: 各图表的绘图配置快照 (JSON)

    References:
        《SCAPE_MS_方法说明书》§4.4.5 - t-SNE可视化定义
    """
    logger.info(
        "开始t-SNE可视化步骤",
        operation="tsne_visualization_step",
        run_id=ctx.run_id,
        source_data_rows=len(tsne_source_data),
        custom_profiles=list(plot_profiles.keys()) if plot_profiles else []
    )

    # 1. 获取步骤目录
    step_dir = ctx.get_step_dir("swift_pso_visualization")

    # 2. 验证输入数据
    _validate_tsne_source_data(tsne_source_data)

    # 3. 初始化产物处理器和配置
    handler = SwiftPsoArtifactHandler()
    config_dict = config.model_dump()
    tsne_computer.validate_tsne_config(config_dict)
    plot_profiles = plot_profiles or {}

    # 4. 记录可视化参数
    _log_visualization_parameters(ctx, config)

    # --- 5.1. 执行收敛轨迹分析 (Convergence Trajectory Analysis) ---
    logger.info("开始执行收敛轨迹分析", operation="tsne_visualization_step")
    try:
        trajectory_profile = plot_profiles.get(
            TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value,
            registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value)
        )
        trajectory_df, silhouette_avg = tsne_computer.compute_tsne(
            source_data=tsne_source_data,
            config=config_dict
        )
        _save_and_register_trajectory_artifacts(
            ctx, step_dir, handler, trajectory_df, trajectory_profile
        )
        _log_trajectory_metrics(ctx, trajectory_df, silhouette_avg)
    except Exception as e:
        logger.error(
            "收敛轨迹分析失败",
            operation="tsne_visualization_step",
            error=str(e)
        )
        # 不中断，继续执行下一个分析

    # --- 5.2. 执行最终收敛点聚类分析 (Cluster Analysis) ---
    clusters_found = 0  # 默认值
    logger.info("开始执行最终收敛点聚类分析", operation="tsne_visualization_step")
    try:
        cluster_profile = plot_profiles.get(
            TsnePlotProfiles.CLUSTER_ANALYSIS.value,
            registry.get(TsnePlotProfiles.CLUSTER_ANALYSIS.value)
        )
        cluster_df, cluster_report = tsne_computer.compute_cluster_analysis(
            source_data=tsne_source_data,
            config=config_dict
        )
        _save_and_register_cluster_artifacts(
            ctx, step_dir, handler, cluster_df, cluster_report, cluster_profile
        )
        _log_cluster_metrics(ctx, cluster_report)
        clusters_found = cluster_report.get("n_clusters_found", 0)
    except Exception as e:
        logger.error(
            "最终收敛点聚类分析失败",
            operation="tsne_visualization_step",
            error=str(e)
        )

    logger.info(
        "t-SNE可视化步骤完成",
        operation="tsne_visualization_step"
    )

    # 8. 返回轻量级结果
    return {
        "status": "completed",
        # 返回一个关键指标，例如聚类数量
        "clusters_found": clusters_found
    }


def _validate_tsne_source_data(tsne_source_data: pd.DataFrame) -> None:
    """验证t-SNE源数据的有效性。

    Args:
        tsne_source_data: t-SNE源数据DataFrame

    Raises:
        ValueError: 数据无效时抛出
    """
    if tsne_source_data.empty:
        raise ValueError("t-SNE源数据不能为空")

    # 检查必需的元数据列，演化轨迹必须包含iteration
    required_metadata_columns = ['bootstrap_run', 'lowo_fold', 'iteration']
    missing_columns = [col for col in required_metadata_columns if col not in tsne_source_data.columns]
    if missing_columns:
        raise ValueError(f"t-SNE源数据缺少必需的元数据列: {missing_columns}")

    # 检查是否有参数列（除了元数据列之外的数值列）
    param_columns = [col for col in tsne_source_data.columns if col not in required_metadata_columns and pd.api.types.is_numeric_dtype(tsne_source_data[col])]
    if not param_columns:
        raise ValueError("t-SNE源数据中没有找到参数列")

    logger.debug(
        "t-SNE源数据验证通过",
        operation="tsne_visualization_step",
        data_rows=len(tsne_source_data),
        param_columns=len(param_columns)
    )

def _log_visualization_parameters(
    ctx: RunContext,
    config: TsneVisualConfig
) -> None:
    """记录可视化参数到RunContext。

    Args:
        ctx: 运行上下文
        config: t-SNE配置对象
    """
    # 记录t-SNE算法参数
    ctx.log_parameter("tsne_perplexity", config.perplexity, step_name="swift_pso_visualization")
    ctx.log_parameter("tsne_learning_rate", config.learning_rate, step_name="swift_pso_visualization")
    ctx.log_parameter("tsne_n_iter", config.n_iter, step_name="swift_pso_visualization")
    ctx.log_parameter("tsne_init", config.init, step_name="swift_pso_visualization")
    if config.random_state is not None:
        ctx.log_parameter(
            "tsne_random_state", config.random_state, step_name="swift_pso_visualization"
        )
    ctx.log_parameter(
        "kmeans_n_clusters", config.n_clusters, step_name="swift_pso_visualization"
    )


def _save_and_register_trajectory_artifacts(
    ctx: RunContext,
    step_dir: Any,  # Path
    handler: SwiftPsoArtifactHandler,
    tsne_result_df: pd.DataFrame,
    plot_profile: PlotProfile
) -> int:
    """保存和注册【收敛轨迹】相关的产物。

    - 数据快照 (CSV)
    - 绘图配置 (JSON)
    - 图表 (PNG/SVG)

    Args:
        ctx: 运行上下文
        step_dir: 步骤目录
        handler: 产物处理器
        tsne_result_df: t-SNE降维结果DataFrame
        plot_profile: 绘图配置对象

    Returns:
        int: t-SNE数据点数量
    """
    # 1. 保存数据快照
    tsne_data_path = step_dir / "tsne_convergence_trajectory_data.csv"
    handler.save_dataframe(tsne_result_df, tsne_data_path)

    ctx.register_artifact(
        artifact_path=tsne_data_path.relative_to(ctx.run_dir),
        artifact_name=TsneVisualArtifacts.TSNE_CONVERGENCE_PLOT_DATA.value,
        description="t-SNE收敛轨迹的坐标数据，用于绘图复现。"
    )

    # 2. 保存PlotProfile配置（确保完全可复现性），但不作为一级产物注册
    plot_profile_path = step_dir / "tsne_convergence_trajectory_profile.json"
    handler.save_plot_profile(plot_profile, plot_profile_path)
    # 暂不注册profile为独立产物，因为它与图表紧密绑定

    # 3. 生成并保存图表
    tsne_plot_path = step_dir / "tsne_convergence_trajectory_plot.png"

    try:
        replot_tsne_from_snapshot(
            snapshot_path=tsne_data_path,
            plot_profile=plot_profile,
            output_path=tsne_plot_path
        )

        ctx.register_artifact(
            artifact_path=tsne_plot_path.relative_to(ctx.run_dir),
            artifact_name=TsneVisualArtifacts.TSNE_CONVERGENCE_PLOT.value,
            description="SWIFT-PSO参数演化轨迹的t-SNE可视化图表。"
        )

    except Exception as e:
        logger.error(
            "生成t-SNE图表失败",
            operation="tsne_visualization_step",
            error=str(e)
        )
        # 不阻断整个步骤，只记录错误
        # 用户仍然可以使用数据快照和配置手动复现图表

    return len(tsne_result_df)


def _log_trajectory_metrics(
    ctx: RunContext,
    tsne_result_df: pd.DataFrame,
    silhouette_score: float | None
) -> None:
    """记录【收敛轨迹】分析的指标到RunContext。

    Args:
        ctx: 运行上下文
        tsne_result_df: t-SNE降维结果DataFrame
        silhouette_score: 聚类的轮廓系数
    """
    metrics = {}

    # 记录数据点数量
    metrics["tsne_points"] = len(tsne_result_df)

    # 记录t-SNE坐标范围
    if 'tsne_x' in tsne_result_df.columns and 'tsne_y' in tsne_result_df.columns:
        metrics["tsne_x_min"] = float(tsne_result_df['tsne_x'].min())
        metrics["tsne_x_max"] = float(tsne_result_df['tsne_x'].max())
        metrics["tsne_y_min"] = float(tsne_result_df['tsne_y'].min())
        metrics["tsne_y_max"] = float(tsne_result_df['tsne_y'].max())

    # 记录Bootstrap运行数量
    if 'bootstrap_run' in tsne_result_df.columns:
        unique_runs = tsne_result_df['bootstrap_run'].nunique()
        metrics["bootstrap_runs"] = unique_runs

    # 记录聚类质量（即使为None也记录，表示无法计算）
    metrics["silhouette_score"] = silhouette_score

    # 批量记录指标
    if metrics:
        ctx.log_metrics(metrics, step_name="swift_pso_visualization")


def _create_cluster_summary_table(cluster_statistics: Dict[str, Any]) -> pd.DataFrame:
    """从详细的簇统计数据创建易于阅读的摘要表。"""
    records = []
    # 鲁棒地获取参数名称列表，以确保列顺序一致
    param_names = []
    if cluster_statistics:
        # 遍历所有簇，找到第一个包含有效参数摘要的簇
        for stats in cluster_statistics.values():
            if stats.get("parameter_summary"):
                param_names = list(stats["parameter_summary"].keys())
                break  # 找到后立即跳出

    if not param_names:  # 如果所有簇都没有参数，直接返回空表
        return pd.DataFrame()

    for cluster_name, stats in sorted(cluster_statistics.items()):
        if stats['n_points'] > 0:
            mean_row = {'cluster_id': cluster_name, 'stat': 'mean', 'n_points': stats['n_points']}
            std_row = {'cluster_id': cluster_name, 'stat': 'std', 'n_points': stats['n_points']}
            for param in param_names:
                mean_row[param] = stats['parameter_summary'][param]['mean']
                std_row[param] = stats['parameter_summary'][param]['std']
            records.extend([mean_row, std_row])

    if not records:
        return pd.DataFrame()

    summary_df = pd.DataFrame(records).set_index(['cluster_id', 'stat'])
    return summary_df

def _save_and_register_cluster_artifacts(
    ctx: RunContext,
    step_dir: Any,
    handler: SwiftPsoArtifactHandler,
    cluster_df: pd.DataFrame,
    cluster_report: Dict[str, Any],
    profile: PlotProfile
) -> None:
    """保存和注册【聚类分析】相关的产物。

    - 数据快照 (CSV)
    - 量化报告 (JSON)
    - 绘图配置 (JSON)
    - 图表 (PNG/SVG)
    """
    # 1. 保存数据快照
    snapshot_path = step_dir / "tsne_cluster_analysis_data.csv"
    handler.save_dataframe(cluster_df, snapshot_path)
    ctx.register_artifact(
        artifact_path=snapshot_path.relative_to(ctx.run_dir),
        artifact_name=TsneVisualArtifacts.TSNE_CLUSTER_PLOT_DATA.value,
        description="最终收敛点聚类分析的坐标数据，用于绘图复现。"
    )

    # 2. 保存量化分析报告 (原始报告)
    report_path = step_dir / "tsne_cluster_analysis_report.json"
    handler.save_parameters(cluster_report, report_path)
    ctx.register_artifact(
        artifact_path=report_path.relative_to(ctx.run_dir),
        artifact_name=TsneVisualArtifacts.TSNE_CLUSTER_ANALYSIS_REPORT.value,
        description="聚类分析的总体量化指标报告，包括轮廓系数、簇心等。"
    )

    # 3. 新增：保存详细的簇内统计报告和摘要表
    if "cluster_statistics" in cluster_report:
        # 3.1 保存详细统计的JSON
        stats_report = cluster_report["cluster_statistics"]
        stats_path = step_dir / "tsne_cluster_statistics_report.json"
        handler.save_parameters(stats_report, stats_path)
        ctx.register_artifact(
            artifact_path=stats_path.relative_to(ctx.run_dir),
            artifact_name=TsneVisualArtifacts.TSNE_CLUSTER_STATISTICS_REPORT.value,
            description="每个簇内部所有参数的详细统计信息（均值、标准差等）。"
        )

        # 3.2 创建并保存易于阅读的摘要表
        summary_table_df = _create_cluster_summary_table(stats_report)
        if not summary_table_df.empty:
            summary_table_path = step_dir / "tsne_cluster_summary_table.csv"
            # 保存时保留多级索引
            handler.save_dataframe(summary_table_df, summary_table_path, index=True)
            ctx.register_artifact(
                artifact_path=summary_table_path.relative_to(ctx.run_dir),
                artifact_name=TsneVisualArtifacts.TSNE_CLUSTER_SUMMARY_TABLE.value,
                description="各簇参数均值和标准差的对比摘要表，便于物理意义解释。"
            )

    # 4. 保存PlotProfile配置
    profile_path = step_dir / "tsne_cluster_analysis_profile.json"
    handler.save_plot_profile(profile, profile_path)

    # 5. 生成并保存图表
    plot_path = step_dir / "tsne_cluster_analysis_plot.png"
    try:
        replot_tsne_from_snapshot(
            snapshot_path=snapshot_path,
            plot_profile=profile,
            output_path=plot_path
        )
        ctx.register_artifact(
            artifact_path=plot_path.relative_to(ctx.run_dir),
            artifact_name=TsneVisualArtifacts.TSNE_CLUSTER_PLOT.value,
            description="SWIFT-PSO最终收敛点的t-SNE聚类分析图表（带凸包）。"
        )
    except Exception as e:
        logger.error(
            "生成t-SNE聚类分析图表失败",
            operation="tsne_visualization_step",
            error=str(e)
        )


def _log_cluster_metrics(
    ctx: RunContext,
    cluster_report: Dict[str, Any]
) -> None:
    """记录【聚类分析】的指标到RunContext。"""
    metrics = {}
    if cluster_report.get("silhouette_score") is not None:
        metrics["cluster_silhouette_score"] = cluster_report["silhouette_score"]

    if "intra_cluster_variance" in cluster_report:
        # 计算平均簇内方差
        variances = list(cluster_report["intra_cluster_variance"].values())
        if variances:
            metrics["cluster_avg_intra_variance"] = sum(variances) / len(variances)

    if metrics:
        ctx.log_metrics(metrics, step_name="swift_pso_visualization")


def get_swift_pso_base_profile() -> PlotProfile:
    """获取SWIFT-PSO模块级基础配置模板（便捷函数）。

    Returns:
        PlotProfile: SWIFT-PSO模块级基础配置
    """
    return registry.get_base(TsnePlotProfiles.SWIFT_PSO_BASE.value)

def get_tsne_convergence_profile() -> PlotProfile:
    """获取t-SNE收敛轨迹图的配置模板（便捷函数）。

    Returns:
        PlotProfile: t-SNE收敛轨迹图配置
    """
    return registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value)
