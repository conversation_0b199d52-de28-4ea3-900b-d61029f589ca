**巴西桑托斯深水碳酸盐岩渗透率计算(SCAPE)**WP文件规范（WP File Specification，WFS）

版本：1.0

---

# 1. WP文件格式概述

## 1.1 设计目标与原则

WP文件（Well Project文件）是一种现代测井数据格式，专门面向测井数据的解释、机器学习、人工智能等应用场景。相比传统的LAS格式，WP格式具有更强的多井数据组织能力、更丰富的元数据支持和更灵活的数据结构定义。

### 设计原则

1. **现代化数据格式**：面向测井数据的解释、机器学习、人工智能等现代数据分析应用，相比传统LAS格式具有更强的数据组织能力和扩展性。

2. **多井数据支持**：天然支持多井数据存储，通过工作表机制在单个文件中组织多个数据集，构成完整的测井工区。

3. **人机友好性**：采用Excel格式，既方便科研人员直接查看和分析，又保持严格的格式规范以支持程序化处理。

4. **专用性与通用性**：为SCAPE项目专门设计，同时具有良好的通用性，可用于其它测井相关的机器学习和人工智能项目。

5. **易用性与规范性平衡**：在保证地质人员易于使用的前提下，建立严格的格式规范，确保数据的一致性、完整性和可解析性。

## 1.2 文件定义

测井数据存放在Excel文件中，这种Excel文件也称为测井工区文件（well project），简称wp文件。

**格式要求**：WP文件仅支持新版Excel格式（.xlsx），不支持旧版Excel格式（.xls）。这一设计决策旨在减轻格式兼容性负担，确保数据处理的一致性和可靠性，同时充分利用新版Excel格式的技术优势。

---

# 2. 文件结构规范

## 2.1 文件命名约定

- **命名规则**：为了方便标识，要求文件名在xlsx扩展名前加后缀标识"wp"
- **文件格式**：文件全名形如`xxx.wp.xlsx`
- **验证规则**：不符合此文件名规则的一律视为无效文件

## 2.2 Excel格式约定

### 2.2.1 平台限制

**文件限制**：
- **最大工作表数量**：255个
- **工作表名称长度**：最大31个字符
- **最大行数**：1,048,576行
- **最大列数**：16,384列

### 2.2.2 单元格引用

本文档采用A1引用样式（列字母+行数字），如A1表示第1行第1列，B2表示第2行第2列。

### 2.2.3 数据类型处理

**类型映射**：
- **数值型**：对应WP格式的INT和FLOAT类型
- **文本型**：对应WP格式的STR类型
- **空值**：空单元格视为null值

**类型转换**：某些特定列（如深度索引、井名）会强制转换为指定类型，不受Excel原始类型影响。

## 2.3 工作表组织结构

WP文件通过Excel的多个工作表来存储测井数据及相关信息，分为两大类：

### 2.3.1 工作表分类

**预定义工作表**：通过预定义的工作表名称来识别，用于存储特定类型的信息
**数据集工作表**：除预定义工作表之外的工作表，用于存放测井数据集，工作表名即为数据集名

### 2.3.2 规模建议

**性能考虑**：
- 单个WP文件建议不超过50个工作表
- 单个数据集建议不超过100万行数据
- 超大数据集建议拆分为多个WP文件

**组织策略**：
- 按地质区块或井组织不同的WP文件
- 按数据类型分别存储在不同WP文件中
- 为不同版本的数据集创建独立的WP文件

## 2.4 预定义工作表规范

### 2.4.1 命名规则
所有预定义工作表名以下划线开头，确保与数据集工作表明确区分。

### 2.4.2 概况信息工作表
以下工作表用于存放概况信息或备注信息，仅供人工查看，程序加载时忽略：
- **`_Summary`**：数据概要信息
- **`_Overview`**：数据总览信息
- **`_Remarks`**：备注信息
- **`_Remark`**：备注信息（单数形式）

### 2.4.3 系统功能工作表
- **`_Head_Info`**：井头信息工作表，存放关于测井工区、井、数据集、曲线的属性信息
- **`_Well_Map`**：井名映射信息，用于对井名进行二次映射

## 2.5 临时隐藏工作表

名称以双下划线开头的工作表在程序加载时被忽略，相当于临时隐藏工作表，方便用户临时不加载某些数据集。
- **命名格式**：`__工作表名`
- **用途**：临时禁用数据集而不删除

## 2.6 数据集工作表规范

### 2.6.1 命名要求

**长度限制**：最大31个字符，不能为空
**禁用字符**：不能包含`: \ / ? * [ ]`
**命名限制**：
- 不能为空白（只包含空格）
- 同一工作簿中工作表名必须唯一（不区分大小写）
- 不能以下划线开头（避免与预定义工作表冲突）

### 2.6.2 数据集组织
- 每个数据集工作表代表一个测井数据集
- 工作表名称就是数据集的名称
- 多个测井数据集构成完整的测井工区
- 不同数据集类型（Continuous、Point、Interval）的格式不同

---

# 3. 数据集类型定义

## 3.1 数据集类型标识

WP格式支持三种数据集类型，分别适用于不同的测井数据场景：
- **Continuous**：连续型数据集，适用于等间距采样的测井曲线（如常规测井）
- **Point**：点型数据集，适用于不等间距采样的数据（如岩心分析数据）
- **Interval**：区间型数据集，适用于层状数据（如测井解释结论）

**第一行格式**：
- A1单元格值：保留字"wp_ds_Type"
- B1单元格值：数据集类型标识符（字符串型，不区分大小写）
- C1单元格值：保留字"wp_ds_Index"
- D1单元格值：数据集索引类型，目前必须为"Depth"

**第二行格式**：
- C2单元格值：保留字"wp_ds_Desc"
- D2单元格值：数据集描述信息，可选

## 3.2 Continuous类型规范

**定义**：深度索引有相同的采样间隔（Periodic），即其中的测井曲线为连续型测井曲线，如常规测井曲线等。

**深度采样间隔**：
- **第二行格式**：A2单元格值为保留字"wp_ds_SR"，B2单元格为深度采样间隔，浮点数型
- **单位约定**：深度采样间隔不需要明确指定单位，强制采用深度索引的曲线单位
- **一致性检查**：需要检查第二行深度采样间隔（DSR，符号Δd）与实际深度差异之间的一致性

## 3.3 Point类型规范

**定义**：深度索引无固定的采样间隔，如岩心数据等。

**深度采样间隔**：
- **第二行格式**：A2单元格值为保留字"wp_ds_SR"，B2单元格为空
- **说明**：对于Point类型，深度采样间隔值无意义，B2单元格应为空

## 3.4 Interval类型规范

**定义**：层状深度索引，即有两个深度索引，分别代表顶界和底界深度，例如测井解释结论等。

**深度采样间隔**：
- **第二行格式**：A2单元格值为保留字"wp_ds_SR"，B2单元格为空
- **说明**：对于Interval类型，深度采样间隔值无意义，B2单元格应为空

---

# 4. 曲线格式规范

## 4.0 数据组织要求

### 4.0.1 全局数据排列规则

**井集中原则**：
- 测井数据必须按井进行集中放置
- 同一口井的所有数据行应连续排列，不能与其他井的数据交错

**井内排序原则**：
- 每口井内的数据必须按深度严格递增排列

### 4.0.2 数据完整性要求

**井名一致性**：
- 同一口井的所有数据行必须具有相同的井名标识
- 井名变化标志着新井数据的开始



## 4.1 曲线结构定义

### 4.1.1 表格结构布局

数据集表单采用表格形式组织曲线数据，具体布局如下：
- **第一列**：标题列，用于标识每行的数据含义
- **第二列及以后**：曲线列，每列定义一条测井曲线

### 4.1.2 行定义规范

**曲线定义区（第三行至第七行）**：
- **第三行**：曲线名称行，标题列为"Name"
- **第四行**：单位行，标题列为"Unit"
- **第五行**：数据类型行，标题列为"Type"
- **第六行**：曲线类别行，标题列为"Class"
- **第七行**：备注信息行，标题列为"Comment"

以上为曲线的基本属性。

**数据区（第八行起）**：
- **第八行第一列**：数据标识"Data"
- **第八行第二列起及以后各行**：具体的曲线数据值

### 4.1.3 数据类型规范

**支持的数据类型**：
- **INT**：整型数据，用于存储整数值
- **FLOAT**：浮点型数据，用于存储实数值
- **STR**：字符串型数据，用于存储文本信息
- **类型推断**：空值和其它未识别值统一按浮点型处理

### 4.1.4 曲线类别规范

**类别型曲线（CAT）**：
- **定义**：Categorical类型，用于表示分类数据
- **数据类型限制**：仅支持INT或STR类型，其它类型为非法定义
- **插值处理**：进行数据插值时采用最近邻（nearest）方法
- **应用场景**：适用于机器学习分类任务的标签数据

**普通型曲线（空值）**：
- **定义**：常规测井曲线类型
- **数据类型**：支持所有数据类型
- **插值处理**：采用由用户指定的插值方法
- **应用场景**：一般的测井数据存储和处理

### 4.1.5 数据完整性要求

**必填字段**：
- 曲线名称（Name）：不能为空
- 数据类型（Type）：必须为有效的类型标识

**可选字段**：
- 单位（Unit）：可为空
- 曲线类别（Class）：空值表示普通类型
- 备注信息（Comment）：可为空

**数据一致性**：
- 曲线类别为CAT时，数据类型必须为INT或STR
- 同一曲线的所有数据值应与声明的数据类型保持一致

## 4.2 深度索引规范

### 4.2.1 深度索引概述

深度索引是测井数据的空间定位基准，不同数据集类型具有不同的深度索引结构和要求。

### 4.2.2 Continuous和Point数据集深度索引

#### 4.2.2.1 曲线名称识别
**标准曲线名**：以下曲线名均视为深度索引曲线
- "MD"：测量深度（Measured Depth）
- "DEPTH"：深度的通用表示
- "DEP"：深度的缩写形式
- "DEPT"：深度的另一种缩写形式

#### 4.2.2.2 数据类型要求
- **强制类型**：浮点型（FLOAT），不论第五行Type声明如何
- **数据验证**：程序解析时应强制转换为浮点型

#### 4.2.2.3 数值约束条件
- **单调性要求**：深度索引数值必须严格递增
- **采样间隔**：Continuous数据集的深度采样间隔（Depth Sampling Rate，DSR，符号Δd）由工作表第二行定义
- **一致性检查**：Continuous数据集需验证实际深度间隔与声明的DSR的一致性

### 4.2.3 Interval数据集深度索引

#### 4.2.3.1 双深度索引结构
Interval数据集采用双深度索引结构，分别表示层段的顶界和底界深度。

#### 4.2.3.2 顶界深度曲线
**标准曲线名**：以下曲线名均视为顶界深度曲线
- "MD_Top"：顶界测量深度
- "MDTop"：顶界深度的紧凑表示
- "Top"：顶界深度的简化表示

#### 4.2.3.3 底界深度曲线
**标准曲线名**：以下曲线名均视为底界深度曲线
- "MD_Bottom"：底界测量深度
- "MDBottom"：底界深度的紧凑表示
- "MD_Bot"：底界深度的缩写形式
- "Bottom"：底界深度的简化表示
- "Bot"：底界深度的最简表示

#### 4.2.3.4 数据完整性要求
**数据类型**：
- 顶界和底界深度曲线均强制为浮点型
- 不论第五行Type声明如何，程序解析时强制转换

**几何约束**：
- **层内约束**：同一层的顶界深度必须小于底界深度
- **层间约束**：当前层的顶界深度必须大于等于上一层的底界深度
- **单位一致性**：顶界和底界深度曲线的单位必须相同

**数据验证**：
- 程序应验证所有几何约束条件
- 违反约束的数据行应标记为无效

## 4.3 井名规范

### 4.3.1 井名曲线识别

**标准曲线名**：以下曲线名均视为井名曲线
- "WELL_NO"：井号的标准表示
- "WELL"：井的通用表示
- "WELLNAME"：井名的完整表示

### 4.3.2 数据类型处理

**强制类型转换**：
- 不论第五行Type声明为何种类型，井名曲线均强制视为字符串类型（STR）
- 程序解析时应将所有井名数据转换为字符串格式

### 4.3.3 数据完整性要求

**非空约束**：
- 井名字段不能为空
- 空值或纯空格的井名记录应标记为无效

## 4.4 必需列要求

### 4.4.1 保留列定义

**保留列概念**：保留列是每个测井数据集必须包含的核心列，用于数据的基本定位和标识。

**必需保留列**：
- **深度索引列**：
  - Continuous和Point数据集：1条深度索引曲线
  - Interval数据集：2条深度索引曲线（顶界和底界）
- **井名列**：1条井名曲线

### 4.4.2 格式验证规则

**完整性检查**：
- 缺少任何保留列的数据集视为无效格式
- 程序应在加载时验证保留列的存在性

**数据有效性**：
- 保留列的数据不能全部为空
- 至少应包含有效的数据记录

## 4.5 深度单位规范

### 4.5.1 支持的单位系统

**米制单位标识**：
- "METERS"：米的完整表示
- "METER"：米的单数形式
- "M"：米的标准缩写

**英制单位标识**：
- "FT"：英尺的标准缩写
- "F"：英尺的简化表示

### 4.5.2 单位一致性规则

**采样间隔单位**：
- 第二行的深度采样间隔（DSR）不需要明确指定单位
- DSR强制采用深度索引曲线声明的单位

**Interval数据集约束**：
- 顶界和底界深度曲线的单位必须完全相同
- 单位不一致的Interval数据集视为格式错误

### 4.5.3 单位处理

**标准化处理**：
- 程序内部统一转换为标准单位（米或英尺）
- 提供单位转换功能以支持不同单位系统的数据

**验证机制**：
- 程序应验证单位标识的有效性
- 对于无法识别的单位标识应给出警告

## 4.6 程序处理建议

### 4.6.1 标准化命名
为便于程序处理，建议采用以下内部标准命名：
- **深度索引曲线**：统一命名为"depth"
- **顶界深度曲线**：统一命名为"depth_top"（Interval数据集）
- **底界深度曲线**：统一命名为"depth_bottom"（Interval数据集）
- **井名曲线**：统一命名为"well"

### 4.6.2 数据清理
- 自动去除井名中的前后空格
- 统一井名的大小写处理策略
- 验证数据类型与声明类型的一致性

## 4.7 曲线维数定义

### 4.7.1 维数分类概述

WP格式支持一维和二维组合曲线，以满足不同类型测井数据的存储需求。

### 4.7.2 一维曲线规范

**存储方式**：每一列存放一条一维曲线
**应用场景**：常规测井曲线（如GR、DEN、CN等）、单值物理量测量、简单的标量数据

### 4.7.3 二维组合曲线规范

**二维组合曲线**是一种逻辑上的数据实体，它在概念上代表一个单一的、具有完整物理意义的二维或多维数据（如一条完整的核磁T2谱），但在物理存储上，它被分解为一组有序的一维元素曲线，并分别存储在不同的数据列中。这些元素曲线必须严格遵循 `[NAME][INDEX]` 的命名模式（例如 `T2_VALUE[1]`, `T2_VALUE[2]`, ...），其中 `NAME` 是组合曲线的基础名称，`INDEX` 是从1开始的连续整数。这种设计允许用户既可以通过基础名称（如 `T2_VALUE`）来引用整个曲线集合，也可以通过完整名称（如 `T2_VALUE[5]`）来单独访问其中的任意一个元素曲线。


#### 4.7.3.1 表示方法
**分解存储**：二维组合曲线通过多条一维元素曲线表示，每个元素占用一列

#### 4.7.3.2 命名规则
**标准模式**：`[NAME][INDEX]`
- **NAME**：二维组合曲线的基础名称
- **INDEX**：元素索引，从1开始的连续整数
- **示例**：T2_VALUE[1]、T2_VALUE[2]、...、T2_VALUE[50]表示T2_VALUE为50维的二维组合曲线

#### 4.7.3.3 数据类型继承
**类型确定**：二维组合曲线的数据类型以其第一条元素曲线（INDEX=1）的数据类型为准

#### 4.7.3.4 引用方式

**双重引用模式**：
- **整体引用**：T2_VALUE（指代整个二维组合曲线的所有元素）
- **元素引用**：T2_VALUE[X]（指代特定索引的元素曲线）

**程序处理要求**：
- **自动展开**：当用户指定T2_VALUE时，程序应自动识别并处理所有相关的元素曲线
- **索引解析**：程序应能正确解析方括号中的索引值
- **完整性检查**：验证二维组合曲线的所有元素是否完整存在

#### 4.7.3.5 应用场景
- NMR T2谱数据
- 频谱分析结果
- 多参数传感器数据
- 机器学习特征向量

---

# 5. 井头信息格式规范

## 5.1 井头信息工作表概述

### 5.1.1 工作表标识与功能

**工作表标识**：`_Head_Info`

**功能定义**：井头信息工作表具有为整个测井工区、数据集、井、曲线附加任意扩展属性（Attribute）的能力，即在基本属性之外，为这些对象附加扩展属笥。是WP格式的核心元数据管理机制。

**应用场景**：
- 存储测井工区级别的全局属性
- 记录数据集特定的配置信息
- 定义井级别的地质参数
- 保存曲线相关的技术参数（如NMR T2谱的T2时间轴信息）

### 5.1.2 工作表结构规范

**基本结构**：
- **第一行**：固定的标题行，定义9列结构
- **第二行起**：具体的属性定义记录

**数据组织原则**：
- 每行定义一个属性
- 通过作用域机制实现分层属性管理
- 支持复杂的属性查找和继承逻辑

## 5.2 工作表列结构定义

### 5.2.1 标题行规范

井头信息工作表采用固定的9列结构，第一行为标题行：

| S | DATASET | WELL | CURVE | ATTRIBUTE | TYPE | UNIT | VALUE | DESCRIPTION |
|---|---------|------|-------|-----------|------|------|-------|-------------|

### 5.2.2 列定义规范

**数据类型说明**：除VALUE列根据TYPE列确定外，其余各列均为字符串型（STR）。

#### 第一列：S（属性类别标识）

**功能定义**：属性的一级类别（Category或Section），用于明确属性的大类别和作用域。

**类别枚举**：
- **V**：版本类别
  - **作用域**：全局唯一
  - **位置要求**：必须是第一个属性（第二行）
  - **数据约束**：忽略其它列，仅VALUE列有效
  - **值约束**：VALUE必须为"1.0"（当前版本）
- **WP**：工区全局属性
  - **作用域**：整个测井工区
  - **继承范围**：不属于任何DATASET、WELL、CURVE
- **DS**：数据集属性
  - **作用域**：数据集级别
  - **作用域限制**：数据集名由DATASET列指定，忽略WELL、CURVE列
  - **有效性检查**：DATASET为空时忽略该行定义
- **W**：井属性
  - **作用域**：井级别
  - **作用域限制**：井名由WELL列指定，忽略CURVE列
  - **继承逻辑**：
    - DATASET有值：针对指定数据集的指定井
    - DATASET为空：针对所有数据集中的指定井
  - **有效性检查**：WELL为空时忽略该行定义
- **C**：曲线属性
  - **作用域**：曲线级别
  - **作用域限制**：曲线名由CURVE列指定
  - **继承逻辑**：
    - DATASET和WELL同时有值：针对指定数据集下指定井的指定曲线
    - 仅DATASET有值：针对指定数据集的指定曲线
    - 仅WELL有值：针对指定井的指定曲线
    - DATASET和WELL均为空：针对所有数据集和所有井的指定曲线
  - **有效性检查**：CURVE为空时忽略该行定义
- **O**：其它类别
  - **作用域**：不受标准作用域限制
  - **查找方式**：需要专属的精确匹配查找方法

#### 第二列：DATASET（数据集作用域）
**功能定义**：指定属性的数据集作用域，用于限制属性的有效范围。

#### 第三列：WELL（井作用域）
**功能定义**：指定属性的井作用域，用于限制属性的有效范围。

#### 第四列：CURVE（曲线作用域）
**功能定义**：指定属性的曲线作用域，用于限制属性的有效范围。

#### 第五列：ATTRIBUTE（属性名称）
**功能定义**：属性的唯一标识名称，用于属性查找和引用。

**命名要求**：
- 不能为空
- 在相同作用域内应保持唯一性

#### 第六列：TYPE（属性数据类型）
**功能定义**：定义属性值的数据类型，用于数据解析和验证。

**类型枚举**：
- **INT**：整型数据
- **FLOAT**：浮点型数据
- **STR**：字符串型数据
- **BOOL**：布尔型数据
  - **真值表示**：T、TRUE、Y、Yes、1
  - **假值表示**：其它所有值
- **COMP**：复合型数据
  - **存储格式**：JSON字符串
  - **解析责任**：由使用者负责结构规范和含义解释

**COMP类型数据结构规范**：

COMP类型支持在JSON中嵌入类型和单位信息，提供自描述的复合数据结构。

**支持的子类型标识**：
- **STR**：字符串型
- **INT**：整型
- **FLOAT**：浮点型
- **BOOL**：布尔型

**数据结构规则**：
- **无单位字段**：`{"v": 值, "t": "类型"}`
- **有单位字段**：`{"v": 值, "u": "单位", "t": "类型"}`
- **纯字符串字段**：直接使用字符串值
- **数组字段**：t字段表示数组元素的类型，而非数组类型

**结构验证要求**：
- JSON格式必须有效
- 带单位字段必须包含"v"、"u"和"t"键
- 无单位字段必须包含"v"和"t"键
- 类型标识必须为有效值
- v字段的实际数据类型必须与t字段声明一致

**验证规则**：
- 无效类型值将被忽略
- 类型不匹配时应给出警告
- COMP类型的JSON结构必须符合规范要求

#### 第七列：UNIT（属性单位）
**功能定义**：属性值的物理单位或度量单位。

**使用规范**：
- 可为空值
- 建议使用标准单位符号

#### 第八列：VALUE（属性值）
**功能定义**：属性的具体数值或内容。

**数据约束**：
- 必须与TYPE列声明的类型兼容
- COMP类型时存储JSON格式字符串

#### 第九列：DESCRIPTION（属性描述）
**功能定义**：属性的详细描述信息，用于文档化和用户理解。

**使用规范**：
- 可为空值
- 建议提供清晰的属性说明

## 5.3 属性查找机制

### 5.3.1 作用域属性查找规范

**作用域属性定义**：WP、DS、W和C类别的属性称为作用域属性，具有分层的作用域继承机制。

**查找输入参数**：
- **dataset_name**：数据集名称（可选）
- **well_name**：井名（可选）
- **curve_name**：曲线名（可选）
- **attribute_name**：属性名称（可选）

**查找逻辑规范**：

#### ******* 全局作用域查找
**条件**：未指定任何作用域参数
**查找范围**：WP类别属性
**匹配规则**：ATTRIBUTE名称匹配

#### ******* 数据集作用域查找
**条件**：仅指定dataset_name
**查找范围**：DS类别属性
**匹配规则**：DATASET列值匹配且ATTRIBUTE名称匹配

#### ******* 井作用域查找
**条件1**：仅指定well_name
- **查找范围**：W类别中DATASET为空的属性子集
- **匹配规则**：WELL列值匹配且ATTRIBUTE名称匹配

**条件2**：同时指定dataset_name和well_name
- **查找范围**：W类别中DATASET不为空的属性子集
- **匹配规则**：DATASET和WELL列值同时匹配且ATTRIBUTE名称匹配

#### 5.3.1.4 曲线作用域查找
**条件1**：仅指定curve_name
- **查找范围**：C类别中DATASET、WELL同时为空的属性子集
- **匹配规则**：CURVE列值匹配且ATTRIBUTE名称匹配

**条件2**：指定curve_name和dataset_name
- **查找范围**：C类别中DATASET不为空、WELL为空的属性子集
- **匹配规则**：DATASET和CURVE列值匹配且ATTRIBUTE名称匹配

**条件3**：指定curve_name和well_name
- **查找范围**：C类别中DATASET为空、WELL不为空的属性子集
- **匹配规则**：WELL和CURVE列值匹配且ATTRIBUTE名称匹配

**条件4**：同时指定curve_name、well_name和dataset_name
- **查找范围**：C类别中DATASET和WELL同时不为空的属性子集
- **匹配规则**：DATASET、WELL和CURVE列值同时匹配且ATTRIBUTE名称匹配

#### 5.3.1.5 查找返回规则
**指定属性名称**：返回第一个匹配的属性记录
**未指定属性名称**：返回所有匹配作用域的属性记录列表

### 5.3.2 其它属性查找规范

**其它属性定义**：O类别的属性称为其它属性，不遵循标准作用域继承机制。

**查找方法**：精确匹配查找
- **输入参数**：DATASET、WELL、CURVE、ATTRIBUTE（均可为空）
- **匹配规则**：所有指定参数必须与记录中对应列值完全匹配
- **空值处理**：空值参数与空值列匹配
- **返回结果**：所有完全匹配的属性记录

## 5.4 预定义属性规范

### 5.4.1 预定义属性概述

**定义**：当ATTRIBUTE列出现特定名称时，具有预定义的特殊含义和处理规则。

**验证要求**：程序应验证预定义属性的格式正确性和数据有效性。

### 5.4.2 Version属性规范

#### 5.4.2.1 属性标识
**属性名称**：Version

#### 5.4.2.2 使用约束
**类别限制**：只能出现在V类别中
**功能定义**：标识WP文件的格式版本号

#### 5.4.2.3 数据规范
**数据类型**：字符串型（STR）
**值约束**：必须为"1.0"（当前版本）
**验证规则**：
- 版本号不匹配时应给出警告
- 缺少版本属性时应视为格式错误

### 5.4.3 T2_AXIS属性规范

#### 5.4.3.1 属性标识
**属性名称**：T2_AXIS

#### 5.4.3.2 基本定义
**数据类型**：COMP类型（复合型）
**功能定义**：定义核磁共振T2谱曲线的T2时间轴信息
**应用场景**：NMR测井数据的T2谱分析

#### 5.4.3.3 数据结构规范

**根属性Axis_Type**：T2时间轴类型标识
- **数据类型**：字符串型
- **枚举值**：
  - **log10**：对数刻度排列
  - **exp2**：指数刻度排列

#### 5.4.3.4 log10类型数据规范

**时间单位说明**：所有时间相关字段的单位均为毫秒（ms）。

**子属性定义**：
- **T2_Start**：T2起始时间
  - **数据结构**：`{"v": 数值, "u": "ms", "t": "FLOAT"}`
  - **约束条件**：数值必须大于0
- **T2_End**：T2终止时间
  - **数据结构**：`{"v": 数值, "u": "ms", "t": "FLOAT"}`
  - **约束条件**：数值必须大于T2_Start的数值
- **N**：布点个数
  - **数据结构**：`{"v": 数值, "t": "INT"}`
  - **约束条件**：必须大于1
- **Values**：T2值数组（可选）
  - **数据结构**：`{"v": [数值数组], "u": "ms", "t": "FLOAT"}`
  - **长度约束**：如果存在，数组长度必须等于N

**JSON格式示例**：
```json
{
  "Axis_Type": "log10",
  "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"},
  "T2_End": {"v": 10000, "u": "ms", "t": "FLOAT"},
  "N": {"v": 64, "t": "INT"},
  "Values": {"v": [0.01, 0.12, 0.15, ..., 10000], "u": "ms", "t": "FLOAT"}
}
```

**计算规则**：
- **直接使用**：Values对象存在且v数组长度等于N时，直接使用v数组值
- **公式计算**：Values对象缺失时，按以下公式计算：
  $$T2_i = T2_{Start}.v \cdot 10^{ (i-1) \cdot \frac{log10(\frac{T2_{End}.v}{T2_{Start}.v})}{(N-1)}}, i=1,2,...,N$$

#### 5.4.3.5 exp2类型数据规范

**时间单位说明**：所有时间相关字段的单位均为毫秒（ms）。

**子属性定义**：
- **T2_Start**：T2起始时间
  - **数据结构**：`{"v": 数值, "u": "ms", "t": "FLOAT"}`
- **T2_Step**：T2步长
  - **数据结构**：`{"v": 数值, "u": "ms", "t": "FLOAT"}`
- **N**：布点个数
  - **数据结构**：`{"v": 数值, "t": "INT"}`
  - **约束条件**：必须大于1
- **Values**：T2值数组（可选）
  - **数据结构**：`{"v": [数值数组], "u": "ms", "t": "FLOAT"}`
  - **长度约束**：如果存在，数组长度必须等于N

**JSON格式示例**：
```json
{
  "Axis_Type": "exp2",
  "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"},
  "T2_Step": {"v": 0.5, "u": "ms", "t": "FLOAT"},
  "N": {"v": 64, "t": "INT"},
  "Values": {"v": [0.01, 0.12, 0.15, ..., 10000], "u": "ms", "t": "FLOAT"}
}
```

**计算规则**：
- **直接使用**：Values对象存在且v数组长度等于N时，直接使用v数组值
- **公式计算**：Values对象缺失时，按以下公式计算：
  $$T2_i = 2 ^ {T2_{Start}.v + T2_{Step}.v \cdot (i-1)}, i=1,2,...,N$$

#### 5.4.3.6 数据验证规则

**结构验证**：
- JSON格式必须有效
- 必需字段不能缺失

**类型验证**：
- 数组字段的所有元素类型必须与声明一致
- 时间值必须为正数
- N值必须为正整数

**单位验证**：
- 时间相关字段的u值必须为有效时间单位
- 同一属性内相关字段的单位应保持一致

**数据完整性验证**：
- Values的v数组长度必须与N的v值一致（如果存在）
- 数组不能为空（如果存在）

**逻辑验证**：
- log10类型：T2_End.v必须大于T2_Start.v
- exp2类型：T2_Step.v可以为正数或负数
- 计算结果必须为单调序列

**兼容性处理**：
- 如果遇到旧格式（无类型和单位结构），程序应能向后兼容
- 缺失类型信息时，根据数值类型进行推断
- 缺失单位信息时，默认使用毫秒（ms）作为时间单位

---

# 6. 井名重映射机制

## 6.1 `_Well_Map`工作表格式

### 6.1.1 工作表标识与功能

**工作表标识**：`_Well_Map`

**功能定义**：井名映射表，用于在加载测井数据集时进行井名重映射，实现井名的标准化管理。

**应用场景**：
- 统一不同数据源的井名命名规范
- 修正井名拼写错误或格式不一致
- 建立井名别名系统

### 6.1.2 工作表结构规范

**基本结构**：
- **第一行**：标题行，定义列名
- **第二行起**：具体的映射记录

**列结构定义**：

| WELL | MAP_WELL |
|------|----------|

**数据类型说明**：两列均为字符串型（STR）。

#### 第一列：WELL（原始井名）
**功能定义**：测井数据集中的原始井名，作为映射查找的键值。

**数据要求**：
- 不能为空
- 不能为纯空格
- 区分大小写

#### 第二列：MAP_WELL（映射井名）
**功能定义**：重新映射后的新井名，作为映射的目标值。

**数据要求**：
- 不能为空
- 不能为纯空格
- 建议使用标准化的井名格式

### 6.1.3 数据有效性规则

**记录有效性**：
- 原始井名或映射井名为空的记录将被忽略
- 重复的原始井名记录，以第一个有效记录为准
- 空行或全空格行将被忽略

**数据清理**：
- 自动去除井名字段的前后空格
- 保持井名的大小写格式

## 6.2 映射规则与处理逻辑

### 6.2.1 映射查找规则

**查找逻辑**：
- **精确匹配**：原始井名与WELL列值完全匹配（区分大小写）
- **优先级**：多个匹配记录时，以第一个有效记录为准

**映射处理**：
- **存在映射**：使用MAP_WELL列的值替换原始井名
- **不存在映射**：保持原始井名不变

### 6.2.2 映射应用范围

**全局应用**：
- 映射规则应用于所有数据集表单
- 映射规则应用于`_Head_Info`表单中的井名字段
- 映射后的井名用于所有后续数据处理

**一致性保证**：
- 同一WP文件内的井名映射保持一致
- 映射操作在数据加载阶段完成

### 6.2.3 错误处理

**循环映射检测**：
- 检测并防止循环映射（A→B→A）
- 发现循环映射时应给出警告并中断映射链

---

# 7. 规范化处理

## 7.1 空值处理

**空值定义**：单元格为空或纯空格时视为空值（null）。

## 7.2 大小写处理

**不区分大小写**：所有保留字、工作表/数据集名称、井名、曲线名称、单位名称、属性名称、标识名称、数据类型等，以及枚举值等，在比较与索引时不区分大小写。

- 为保证跨平台一致性，文件解析与生成应执行如下步骤：
  1. 去除字段两端空格（Trim）
  2. 执行 Unicode NFKC 归一化
  3. 转换为大写（ASCII）或采用 Unicode Case Folding
  4. 当同一作用域内出现名称在规范化后相同但大小写不同的条目时，解析器必须报错并指出冲突
- 解析时一律以规范化后的键参与比较与索引；原始大小写仅用于显示。

---

# 8. LAS格式转换规范

（待完善）

# 9. Python处理WP格式最佳实践

## 9.1 概述

**推荐性质**：本节内容为推荐的最佳实践，不是强制要求。开发者可以根据项目需求选择合适的实现方案。


## 9.1 使用Pydantic进行COMP类型验证

### 9.1.1 验证原则

对于COMP类型的属性值，建议使用Pydantic进行以下验证：
1. JSON格式有效性验证
2. 必需字段存在性验证
3. 数据类型一致性验证
4. 业务逻辑验证

### 9.1.2 Pydantic模型定义

**基础模型**：
- 定义ValueWithUnit模型用于带单位和类型的数值验证
- 为不同的预定义属性创建专门的Pydantic模型
- 使用Literal类型限制枚举值的有效性

**T2_AXIS验证模型**：
- 创建T2AxisLog10和T2AxisExp2两个模型分别验证不同的轴类型
- 使用validator装饰器实现业务逻辑验证（如T2_End必须大于T2_Start）
- 支持可选字段的验证（如Values数组）

### 9.1.3 验证实现要点

**结构验证**：
- 验证JSON格式的有效性
- 检查必需字段（v、t）和可选字段（u）的存在性
- 验证字段值的数据类型与声明类型的一致性

**业务逻辑验证**：
- 实现数值范围检查（如时间值必须为正数）
- 验证逻辑关系（如结束时间大于开始时间）
- 检查数组长度与声明长度的一致性

**错误处理**：
- 提供清晰的验证错误信息
- 区分格式错误和业务逻辑错误
- 支持部分验证和完整验证模式


---

# 10. 格式版本管理

## 10.1 版本标识

**版本号格式**：MAJOR.MINOR（简化版本号）
**当前版本**：1.0
**版本记录**：在`_Head_Info`表单的Version属性中记录

## 10.2 兼容性策略

**版本升级规则**：
- **MAJOR版本**：不兼容的格式修改
- **MINOR版本**：向后兼容的功能新增或问题修正

**兼容性要求**：
- 新版本解析器必须支持旧版本WP文件
- 程序应检测版本号并选择相应解析策略
- 版本不兼容时应给出明确警告

---

# 附录A：术语与常量定义

## A.1 文件与格式相关

### A.1.1 文件扩展名与标识

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| WP文件扩展名 | WP File Extension | WP_FILE_EXTENSION | ".wp.xlsx" | WP文件的完整扩展名 |
| Excel扩展名 | Excel Extension | EXCEL_EXTENSION | ".xlsx" | Excel文件扩展名 |
| WP标识后缀 | WP Identifier Suffix | WP_IDENTIFIER | ".wp" | WP文件标识后缀 |

### A.1.2 文件格式限制

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 最大工作表数 | Max Worksheets | MAX_WORKSHEETS | 255 | Excel文件最大工作表数量 |
| 工作表名最大长度 | Max Sheet Name Length | MAX_SHEET_NAME_LENGTH | 31 | 工作表名称最大字符数 |

| 最大行数 | Max Rows | MAX_ROWS | 1048576 | Excel最大行数 |
| 最大列数 | Max Columns | MAX_COLUMNS | 16384 | Excel最大列数 |
| 建议最大工作表数 | Recommended Max Sheets | RECOMMENDED_MAX_SHEETS | 50 | 建议的最大工作表数 |
| 建议最大数据行数 | Recommended Max Data Rows | RECOMMENDED_MAX_DATA_ROWS | 1000000 | 建议的最大数据行数 |

## A.2 工作表相关

### A.2.1 预定义工作表名称

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 井头信息表 | Head Info Sheet | SHEET_HEAD_INFO | "_Head_Info" | 井头信息工作表名 |
| 井名映射表 | Well Map Sheet | SHEET_WELL_MAP | "_Well_Map" | 井名映射工作表名 |
| 概要信息表 | Summary Sheet | SHEET_SUMMARY | "_Summary" | 数据概要信息表 |
| 总览信息表 | Overview Sheet | SHEET_OVERVIEW | "_Overview" | 数据总览信息表 |
| 备注信息表(复数) | Remarks Sheet | SHEET_REMARKS | "_Remarks" | 备注信息表(复数形式) |
| 备注信息表(单数) | Remark Sheet | SHEET_REMARK | "_Remark" | 备注信息表(单数形式) |

### A.2.2 工作表前缀规则

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 预定义表前缀 | Predefined Sheet Prefix | PREDEFINED_SHEET_PREFIX | "_" | 预定义工作表前缀 |
| 隐藏表前缀 | Hidden Sheet Prefix | HIDDEN_SHEET_PREFIX | "__" | 临时隐藏工作表前缀 |


## A.3 数据集类型相关

### A.3.1 数据集类型标识

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 连续型数据集 | Continuous Dataset | DATASET_TYPE_CONTINUOUS | "Continuous" | 连续型数据集标识 |
| 点型数据集 | Point Dataset | DATASET_TYPE_POINT | "Point" | 点型数据集标识 |
| 区间型数据集 | Interval Dataset | DATASET_TYPE_INTERVAL | "Interval" | 区间型数据集标识 |

### A.3.2 数据集保留字

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 数据集类型保留字 | Dataset Type Reserved Word | RESERVED_WORD_DS_TYPE | "wp_ds_Type" | 数据集类型标识保留字 |
| 采样率保留字 | Sampling Rate Reserved Word | RESERVED_WORD_DS_SR | "wp_ds_SR" | 深度采样间隔保留字 |

## A.4 曲线结构相关

### A.4.1 曲线定义行标题

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 曲线名称行标题 | Name Row Title | ROW_TITLE_NAME | "Name" | 第三行标题 |
| 单位行标题 | Unit Row Title | ROW_TITLE_UNIT | "Unit" | 第四行标题 |
| 数据类型行标题 | Type Row Title | ROW_TITLE_TYPE | "Type" | 第五行标题 |
| 曲线类别行标题 | Class Row Title | ROW_TITLE_CLASS | "Class" | 第六行标题 |
| 备注行标题 | Comment Row Title | ROW_TITLE_COMMENT | "Comment" | 第七行标题 |
| 数据行标题 | Data Row Title | ROW_TITLE_DATA | "Data" | 第八行标题 |

### A.4.2 曲线定义行号

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 数据集类型行号 | Dataset Type Row | ROW_DATASET_TYPE | 1 | 数据集类型定义行 |
| 采样率行号 | Sampling Rate Row | ROW_SAMPLING_RATE | 2 | 深度采样间隔定义行 |
| 曲线名称行号 | Name Row | ROW_NAME | 3 | 曲线名称定义行 |
| 单位行号 | Unit Row | ROW_UNIT | 4 | 单位定义行 |
| 数据类型行号 | Type Row | ROW_TYPE | 5 | 数据类型定义行 |
| 曲线类别行号 | Class Row | ROW_CLASS | 6 | 曲线类别定义行 |
| 备注行号 | Comment Row | ROW_COMMENT | 7 | 备注定义行 |
| 数据起始行号 | Data Start Row | ROW_DATA_START | 8 | 数据开始行 |

## A.5 数据类型相关

### A.5.1 基础数据类型

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 整型 | Integer Type | DATA_TYPE_INT | "INT" | 整型数据类型 |
| 浮点型 | Float Type | DATA_TYPE_FLOAT | "FLOAT" | 浮点型数据类型 |
| 字符串型 | String Type | DATA_TYPE_STR | "STR" | 字符串型数据类型 |
| 布尔型 | Boolean Type | DATA_TYPE_BOOL | "BOOL" | 布尔型数据类型 |
| 复合型 | Composite Type | DATA_TYPE_COMP | "COMP" | 复合型数据类型 |

### A.5.2 曲线类别

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 类别型曲线 | Categorical Curve | CURVE_CLASS_CAT | "CAT" | 分类型曲线标识 |
| 普通型曲线 | Normal Curve | CURVE_CLASS_NORMAL | "" | 普通曲线(空值) |

### A.5.3 布尔值表示

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 布尔真值集合 | Boolean True Values | BOOL_TRUE_VALUES | ["T", "TRUE", "Y", "Yes", "1"] | 表示真值的字符串集合 |

## A.6 深度索引相关

### A.6.1 深度曲线名称

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 测量深度 | Measured Depth | DEPTH_CURVE_MD | "MD" | 标准测量深度曲线名 |
| 深度通用名 | Depth Generic | DEPTH_CURVE_DEPTH | "DEPTH" | 深度通用曲线名 |
| 深度缩写1 | Depth Abbreviation 1 | DEPTH_CURVE_DEP | "DEP" | 深度缩写曲线名 |
| 深度缩写2 | Depth Abbreviation 2 | DEPTH_CURVE_DEPT | "DEPT" | 深度另一缩写曲线名 |

### A.6.2 区间深度曲线名称

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 顶界深度1 | Top Depth 1 | DEPTH_CURVE_MD_TOP | "MD_Top" | 顶界测量深度 |
| 顶界深度2 | Top Depth 2 | DEPTH_CURVE_MDTOP | "MDTop" | 顶界深度紧凑表示 |
| 顶界深度3 | Top Depth 3 | DEPTH_CURVE_TOP | "Top" | 顶界深度简化表示 |
| 底界深度1 | Bottom Depth 1 | DEPTH_CURVE_MD_BOTTOM | "MD_Bottom" | 底界测量深度 |
| 底界深度2 | Bottom Depth 2 | DEPTH_CURVE_MDBOTTOM | "MDBottom" | 底界深度紧凑表示 |
| 底界深度3 | Bottom Depth 3 | DEPTH_CURVE_MD_BOT | "MD_Bot" | 底界深度缩写 |
| 底界深度4 | Bottom Depth 4 | DEPTH_CURVE_BOTTOM | "Bottom" | 底界深度简化表示 |
| 底界深度5 | Bottom Depth 5 | DEPTH_CURVE_BOT | "Bot" | 底界深度最简表示 |

### A.6.3 深度单位

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 米制单位1 | Meter Unit 1 | DEPTH_UNIT_METERS | "METERS" | 米的完整表示 |
| 米制单位2 | Meter Unit 2 | DEPTH_UNIT_METER | "METER" | 米的单数形式 |
| 米制单位3 | Meter Unit 3 | DEPTH_UNIT_M | "M" | 米的标准缩写 |
| 英制单位1 | Feet Unit 1 | DEPTH_UNIT_FT | "FT" | 英尺标准缩写 |
| 英制单位2 | Feet Unit 2 | DEPTH_UNIT_F | "F" | 英尺简化表示 |

### A.6.4 标准化内部命名

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 标准深度列名 | Standard Depth Column | STD_DEPTH_COLUMN | "depth" | 标准化深度索引列名 |
| 标准顶界深度列名 | Standard Top Depth Column | STD_DEPTH_TOP_COLUMN | "depth_top" | 标准化顶界深度列名 |
| 标准底界深度列名 | Standard Bottom Depth Column | STD_DEPTH_BOTTOM_COLUMN | "depth_bottom" | 标准化底界深度列名 |
| 标准井名列名 | Standard Well Column | STD_WELL_COLUMN | "well" | 标准化井名列名 |

## A.7 井名相关

### A.7.1 井名曲线名称

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 井号标准名 | Well Number Standard | WELL_CURVE_WELL_NO | "WELL_NO" | 井号标准表示 |
| 井通用名 | Well Generic | WELL_CURVE_WELL | "WELL" | 井的通用表示 |
| 井名完整名 | Well Name Full | WELL_CURVE_WELLNAME | "WELLNAME" | 井名完整表示 |

## A.8 井头信息相关

### A.8.1 井头信息列名

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 属性类别列 | Section Column | HEAD_INFO_COL_S | "S" | 属性类别标识列 |
| 数据集列 | Dataset Column | HEAD_INFO_COL_DATASET | "DATASET" | 数据集作用域列 |
| 井列 | Well Column | HEAD_INFO_COL_WELL | "WELL" | 井作用域列 |
| 曲线列 | Curve Column | HEAD_INFO_COL_CURVE | "CURVE" | 曲线作用域列 |
| 属性名列 | Attribute Column | HEAD_INFO_COL_ATTRIBUTE | "ATTRIBUTE" | 属性名称列 |
| 类型列 | Type Column | HEAD_INFO_COL_TYPE | "TYPE" | 属性数据类型列 |
| 单位列 | Unit Column | HEAD_INFO_COL_UNIT | "UNIT" | 属性单位列 |
| 值列 | Value Column | HEAD_INFO_COL_VALUE | "VALUE" | 属性值列 |
| 描述列 | Description Column | HEAD_INFO_COL_DESCRIPTION | "DESCRIPTION" | 属性描述列 |

### A.8.2 属性类别标识

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 版本类别 | Version Category | ATTR_CATEGORY_V | "V" | 版本类别标识 |
| 工区属性类别 | Work Area Category | ATTR_CATEGORY_WP | "WP" | 工区全局属性类别 |
| 数据集属性类别 | Dataset Category | ATTR_CATEGORY_DS | "DS" | 数据集属性类别 |
| 井属性类别 | Well Category | ATTR_CATEGORY_W | "W" | 井属性类别 |
| 曲线属性类别 | Curve Category | ATTR_CATEGORY_C | "C" | 曲线属性类别 |
| 其它属性类别 | Other Category | ATTR_CATEGORY_O | "O" | 其它属性类别 |

### A.8.3 预定义属性名

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 版本属性 | Version Attribute | ATTR_NAME_VERSION | "Version" | 版本属性名 |
| T2轴属性 | T2 Axis Attribute | ATTR_NAME_T2_AXIS | "T2_AXIS" | T2轴属性名 |

### A.8.4 版本相关

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 当前版本号 | Current Version | CURRENT_VERSION | "1.0" | 当前WP格式版本号 |

## A.9 井名映射相关

### A.9.1 井名映射列名

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 原始井名列 | Original Well Column | WELL_MAP_COL_WELL | "WELL" | 原始井名列 |
| 映射井名列 | Mapped Well Column | WELL_MAP_COL_MAP_WELL | "MAP_WELL" | 映射井名列 |

## A.10 T2轴相关

### A.10.1 T2轴类型

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 对数轴类型 | Log10 Axis Type | T2_AXIS_TYPE_LOG10 | "log10" | 对数刻度T2轴 |
| 指数轴类型 | Exp2 Axis Type | T2_AXIS_TYPE_EXP2 | "exp2" | 指数刻度T2轴 |

### A.10.2 T2轴属性名

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 轴类型属性 | Axis Type Property | T2_PROP_AXIS_TYPE | "Axis_Type" | T2轴类型属性名 |
| T2起始时间 | T2 Start Time | T2_PROP_T2_START | "T2_Start" | T2起始时间属性名 |
| T2终止时间 | T2 End Time | T2_PROP_T2_END | "T2_End" | T2终止时间属性名 |
| T2步长 | T2 Step | T2_PROP_T2_STEP | "T2_Step" | T2步长属性名 |
| 布点个数 | Number of Points | T2_PROP_N | "N" | 布点个数属性名 |
| T2值数组 | T2 Values Array | T2_PROP_VALUES | "Values" | T2值数组属性名 |

### A.10.3 T2时间单位

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 毫秒单位 | Millisecond Unit | T2_TIME_UNIT_MS | "ms" | T2时间轴毫秒单位 |

## A.11 COMP类型JSON结构

### A.11.1 JSON字段名

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 值字段 | Value Field | COMP_FIELD_V | "v" | COMP类型值字段 |
| 单位字段 | Unit Field | COMP_FIELD_U | "u" | COMP类型单位字段 |
| 类型字段 | Type Field | COMP_FIELD_T | "t" | COMP类型类型字段 |

## A.12 二维组合曲线相关

### A.12.1 二维组合曲线命名模式

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 二维组合曲线索引模式 | 2D Curve Index Pattern | CURVE_2D_INDEX_PATTERN | r"\[(\d+)\]$" | 二维组合曲线索引正则表达式 |
| 二维组合曲线索引起始 | 2D Curve Index Start | CURVE_2D_INDEX_START | 1 | 二维组合曲线索引起始值 |

## A.13 错误与警告消息

### A.13.1 文件格式错误

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 无效文件名错误 | Invalid Filename Error | ERR_INVALID_FILENAME | "Invalid WP filename format" | 文件名格式错误 |
| 版本不匹配警告 | Version Mismatch Warning | WARN_VERSION_MISMATCH | "Version mismatch detected" | 版本号不匹配警告 |
| 缺少版本属性错误 | Missing Version Error | ERR_MISSING_VERSION | "Missing Version attribute" | 缺少版本属性错误 |

### A.13.2 数据验证错误

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 空井名错误 | Empty Well Name Error | ERR_EMPTY_WELL_NAME | "Well name cannot be empty" | 井名为空错误 |
| 深度单调性错误 | Depth Monotonicity Error | ERR_DEPTH_NOT_MONOTONIC | "Depth values must be strictly increasing" | 深度非单调递增错误 |
| 循环映射错误 | Circular Mapping Error | ERR_CIRCULAR_MAPPING | "Circular well name mapping detected" | 循环井名映射错误 |

## A.14 常用集合定义

### A.14.1 预定义工作表名集合

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 概况信息表集合 | Summary Sheets Set | SUMMARY_SHEETS | {"_Summary", "_Overview", "_Remarks", "_Remark"} | 概况信息工作表名集合 |
| 系统功能表集合 | System Sheets Set | SYSTEM_SHEETS | {"_Head_Info", "_Well_Map"} | 系统功能工作表名集合 |
| 所有预定义表集合 | All Predefined Sheets Set | ALL_PREDEFINED_SHEETS | SUMMARY_SHEETS ∪ SYSTEM_SHEETS | 所有预定义工作表名集合 |

### A.14.2 深度曲线名集合

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 单深度曲线名集合 | Single Depth Curves Set | SINGLE_DEPTH_CURVES | {"MD", "DEPTH", "DEP", "DEPT"} | 单深度索引曲线名集合 |
| 顶界深度曲线名集合 | Top Depth Curves Set | TOP_DEPTH_CURVES | {"MD_Top", "MDTop", "Top"} | 顶界深度曲线名集合 |
| 底界深度曲线名集合 | Bottom Depth Curves Set | BOTTOM_DEPTH_CURVES | {"MD_Bottom", "MDBottom", "MD_Bot", "Bottom", "Bot"} | 底界深度曲线名集合 |
| 所有深度曲线名集合 | All Depth Curves Set | ALL_DEPTH_CURVES | SINGLE_DEPTH_CURVES ∪ TOP_DEPTH_CURVES ∪ BOTTOM_DEPTH_CURVES | 所有深度曲线名集合 |

### A.14.3 井名曲线名集合

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 井名曲线名集合 | Well Name Curves Set | WELL_NAME_CURVES | {"WELL_NO", "WELL", "WELLNAME"} | 井名曲线名集合 |

### A.14.4 数据类型集合

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 基础数据类型集合 | Basic Data Types Set | BASIC_DATA_TYPES | {"INT", "FLOAT", "STR"} | 基础数据类型集合 |
| 所有数据类型集合 | All Data Types Set | ALL_DATA_TYPES | {"INT", "FLOAT", "STR", "BOOL", "COMP"} | 所有数据类型集合 |
| 数值数据类型集合 | Numeric Data Types Set | NUMERIC_DATA_TYPES | {"INT", "FLOAT"} | 数值型数据类型集合 |

### A.14.5 数据集类型集合

| 中文术语 | 英文术语 | 常量名 | 取值 | 说明 |
|----------|----------|--------|------|------|
| 所有数据集类型集合 | All Dataset Types Set | ALL_DATASET_TYPES | {"Continuous", "Point", "Interval"} | 所有数据集类型集合 |
| 单深度数据集类型集合 | Single Depth Dataset Types Set | SINGLE_DEPTH_DATASET_TYPES | {"Continuous", "Point"} | 单深度索引数据集类型集合 |

**文档修订历史**

| 版本 | 日期 | 修订内容 | 修订人 |
|------|------|----------|--------|
| 1.0 | 2025-01-02 | 初始版本，基于MS文档1.1节编写，包含术语与常量定义附录 | - |
| 1.1 | 2025-01-02 | 新增第8节LAS格式转换规范，新增第10节实施建议与最佳实践 | - |
