{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-22T05:43:11.186892Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 126.87, 'cpu_percent': 0.0}\n", "2025-07-22T05:43:13.997454Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 375.13, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-22T05:43:14.015795Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 375.14, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-22T05:43:14.032766Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 375.16, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-22T05:43:15.176741Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 414.98, 'cpu_percent': 0.0} operation=register_base_profile profile_name=log_scout.base\n", "2025-07-22T05:43:15.197386Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 414.99, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=log_scout.heatmap\n", "2025-07-22T05:43:15.219103Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 414.99, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=log_scout.clustermap\n", "2025-07-22T05:43:15.237490Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 415.0, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=log_scout.pairplot\n", "2025-07-22T05:43:15.250141Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 415.01, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=log_scout.regplot\n", "2025-07-22T05:43:15.264079Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 415.02, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=log_scout.boxplot\n"]}], "source": ["from pathlib import Path\n", "\n", "from logwp.io.wp_excel import WpExcelReader\n", "from logwp.models.datasets.base import WpDepthIndexedDatasetBase\n", "from logwp.models.constants import WpCurveDimension\n", "import logwp.extras.ml.log_scout.plot_profiles\n", "from logwp.extras.ml.log_scout import LogScoutConfig, run_log_scout_step\n", "from logwp.extras.tracking import RunContext\n"]}, {"cell_type": "code", "execution_count": 2, "id": "7812e585", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-22T05:43:15.349523Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 415.32, 'cpu_percent': 0.0} file_path=..\\01_data\\santos_obmiq_cum_all.wp.xlsx\n", "2025-07-22T05:43:15.379621Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 415.79, 'cpu_percent': 0.0} file_path=..\\01_data\\santos_obmiq_cum_all.wp.xlsx file_size_mb=1.81 sheet_count=1\n", "2025-07-22T05:43:15.389291Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 415.8, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all\n", "2025-07-22T05:43:15.408972Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 415.81, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all\n", "2025-07-22T05:43:15.432378Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 416.29, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T05:43:15.472868Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 416.48, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=22 well_curves=1\n", "2025-07-22T05:43:18.302376Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.78, 'cpu_percent': 0.0} shape=(2651, 85) sheet_name=nmr_obmiq\n", "2025-07-22T05:43:18.323891Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.82, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq')\n", "2025-07-22T05:43:18.329732Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.82, 'cpu_percent': 0.0} curve_count=22 dataset_name=nmr_obmiq dataset_type=Continuous df_shape=(2651, 85) processing_time=2.908\n", "2025-07-22T05:43:18.355202Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.82, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-22T05:43:18.365347Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.82, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-22T05:43:18.380873Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.82, 'cpu_percent': 0.0} dataset_count=1 file_path=..\\01_data\\santos_obmiq_cum_all.wp.xlsx processing_time=3.031 project_name=WpIdentifier('santos_obmiq_cum_all')\n", "2025-07-22T05:43:18.402958Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.82, 'cpu_percent': 0.0} curve_count=19 has_query=False operation=extract_curves source_dataset=nmr_obmiq target_dataset=ds_1d\n", "2025-07-22T05:43:18.456110Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 425.86, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=21 input_curves=['CN', 'SWB_NMR', 'T2LM', 'SWI_NMR', 'T2_P20', 'PHIT_NMR', 'DRES', 'DT', 'DPHIT_NMR', 'T2LM_LONG', 'BVI_NMR', 'BFV_NMR', 'SDR_PROXY', 'T2_P50', 'DEN', 'DT2_P50', 'WELL_NO', 'PHIE_NMR', 'RD_LOG10', 'RS_LOG10', 'MD'] operation=extract_metadata output_curve_count=21 output_curves=['CN', 'SWB_NMR', 'T2LM', 'SWI_NMR', 'T2_P20', 'PHIT_NMR', 'DRES', 'DT', 'DPHIT_NMR', 'T2LM_LONG', 'BVI_NMR', 'BFV_NMR', 'SDR_PROXY', 'T2_P50', 'DEN', 'DT2_P50', 'WELL_NO', 'PHIE_NMR', 'RD_LOG10', 'RS_LOG10', 'MD']\n", "2025-07-22T05:43:18.485009Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 427.99, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_ds_1d dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T05:43:18.509041Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 428.05, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-22T05:43:18.530962Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 428.05, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=ds_1d target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-22T05:43:18.554228Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 428.05, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=ds_1d dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T05:43:18.576153Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 428.05, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'SWB_NMR', 'RD_LOG10', 'PHIT_NMR', 'DT', 'SDR_PROXY', 'BVI_NMR', 'DRES', 'T2LM_LONG', 'BFV_NMR', 'PHIE_NMR', 'CN', 'T2_P50', 'DEN', 'T2LM', 'T2_P20', 'DPHIT_NMR', 'SWI_NMR', 'DT2_P50', 'RS_LOG10'] operation=extract_curves selected_columns=21 source_rows=2651 target_rows=2651\n", "2025-07-22T05:43:18.592898Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 426.35, 'cpu_percent': 0.0} extracted_curve_count=19 operation=extract_curve_dataframe_bundle\n", "2025-07-22T05:43:18.612844Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 426.49, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=19 input_curves=['CN', 'SWB_NMR', 'T2LM', 'SWI_NMR', 'T2_P20', 'PHIT_NMR', 'DRES', 'DT', 'DPHIT_NMR', 'T2LM_LONG', 'BVI_NMR', 'BFV_NMR', 'SDR_PROXY', 'T2_P50', 'DEN', 'DT2_P50', 'PHIE_NMR', 'RD_LOG10', 'RS_LOG10'] operation=extract_metadata output_curve_count=19 output_curves=['CN', 'SWB_NMR', 'T2LM', 'SWI_NMR', 'T2_P20', 'PHIT_NMR', 'DRES', 'DT', 'DPHIT_NMR', 'T2LM_LONG', 'BVI_NMR', 'BFV_NMR', 'SDR_PROXY', 'T2_P50', 'DEN', 'DT2_P50', 'PHIE_NMR', 'RD_LOG10', 'RS_LOG10']\n"]}], "source": ["# 1. Load the WP Excel file\n", "\n", "file_path = Path('../01_data/santos_obmiq_cum_all.wp.xlsx')\n", "reader = WpExcelReader()\n", "project = reader.read(file_path)\n", "\n", "# Get the 'nmr_obmiq' dataset\n", "nmr_obmiq = project.get_dataset('nmr_obmiq')\n", "\n", "# 2. Get a list of non-2D curves (1D curves)\n", "one_d_curves = nmr_obmiq.curve_metadata.get_data_curves(dimensions=[WpCurveDimension.ONE_D])\n", "\n", "# 3. Extract 1D curves into a new dataset\n", "ds_1d = project.extract_curves(\n", "    source_dataset='nmr_obmiq',\n", "    target_dataset='ds_1d',\n", "    curve_names=one_d_curves\n", ")\n", "\n", "# 4. Extract curves to a DataFrame bundle, excluding system curves\n", "ds_1d_bundle = ds_1d.extract_curve_dataframe_bundle(\n", "    curve_names=None,\n", "    include_system_columns=False\n", ")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ad1d85fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-22T05:55:28.180215Z [warning  ] Overwriting existing run       [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 990.89, 'cpu_percent': 0.0} operation=overwrite_run run_dir=output01\n", "2025-07-22T05:55:28.195048Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 990.89, 'cpu_percent': 0.0} operation=init_context run_dir=output01 run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:28.215106Z [info     ] 开始 LogScout 特征分析步骤             [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 990.9, 'cpu_percent': 0.0} num_features=18 prefix=obmiq_DT2P50 target=DT2_P50 task_type=regression\n", "2025-07-22T05:55:28.243232Z [info     ] 开始定量分析计算...                    [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 990.91, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:28.261680Z [info     ] 相关系数矩阵计算完成。                    [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 990.91, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:28.376027Z [info     ] VIF分数计算完成。                     [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 990.96, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:28.694020Z [info     ] 互信息分数计算完成。                     [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.0, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:28.719999Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.pearson_correlation artifact_path=obmiq_DT2P50_log_scout\\quantitative_report\\pearson_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.02, 'cpu_percent': 0.0} description=输入特征间的皮尔逊相关系数矩阵。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:28.749971Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.pearson_correlation artifact_path=obmiq_DT2P50_log_scout\\quantitative_report\\pearson_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.02, 'cpu_percent': 0.0} description=用于生成皮尔逊相关性热力图的数据快照。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:28.771508Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.spearman_correlation artifact_path=obmiq_DT2P50_log_scout\\quantitative_report\\spearman_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.04, 'cpu_percent': 0.0} description=输入特征间的斯皮尔曼相关系数矩阵。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:28.800849Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.spearman_correlation artifact_path=obmiq_DT2P50_log_scout\\quantitative_report\\spearman_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.04, 'cpu_percent': 0.0} description=用于生成斯皮尔曼相关性热力图的数据快照。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:28.822932Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.vif_scores artifact_path=obmiq_DT2P50_log_scout\\quantitative_report\\vif_scores.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.05, 'cpu_percent': 0.0} description=每个输入特征的方差膨胀因子(VIF)得分。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:28.849626Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.mutual_information artifact_path=obmiq_DT2P50_log_scout\\quantitative_report\\mutual_information.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.05, 'cpu_percent': 0.0} description=每个输入特征与目标特征之间的互信息得分。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:28.863153Z [info     ] 所有定量分析产物已保存并注册。                [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.05, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:28.876295Z [info     ] 开始可视化分析...                     [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.05, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:28.893800Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 991.11, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.heatmap\n", "2025-07-22T05:55:29.953079Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.pearson_heatmap artifact_path=obmiq_DT2P50_log_scout\\visual_report\\feature_correlation\\pearson_heatmap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1033.79, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:31.075585Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.pearson_clustermap artifact_path=obmiq_DT2P50_log_scout\\visual_report\\feature_correlation\\pearson_clustermap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1097.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:31.116287Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1097.98, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.heatmap\n", "2025-07-22T05:55:32.200203Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.spearman_heatmap artifact_path=obmiq_DT2P50_log_scout\\visual_report\\feature_correlation\\spearman_heatmap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1141.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:33.345397Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.spearman_clustermap artifact_path=obmiq_DT2P50_log_scout\\visual_report\\feature_correlation\\spearman_clustermap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1206.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:33.359315Z [info     ] 特征相关性图表已生成。                    [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1206.3, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:33.374522Z [warning  ] 输入特征数量 (18) 超过配置上限 (10), 跳过 Pairplot 生成以避免性能问题。 [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1206.3, 'cpu_percent': 0.0} extra={'feature_count': 18, 'max_features': 10}\n", "2025-07-22T05:55:33.386325Z [info     ] 正在为每个输入特征生成与目标的关系图...          [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1206.32, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:33.416483Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_CN_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_CN_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1207.0, 'cpu_percent': 0.0} description=Data snapshot for regplot_CN_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:33.466674Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1207.14, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:34.260607Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_CN_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_CN_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1225.09, 'cpu_percent': 0.0} description=Plot of CN vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:34.322831Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_SWB_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_SWB_NMR_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1225.56, 'cpu_percent': 0.0} description=Data snapshot for regplot_SWB_NMR_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:34.358848Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1225.63, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:35.176911Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_SWB_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_SWB_NMR_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1243.32, 'cpu_percent': 0.0} description=Plot of SWB_NMR vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:35.238696Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2LM_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2LM_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1243.94, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2LM_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:35.273457Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1243.94, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:36.022774Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2LM_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2LM_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1261.49, 'cpu_percent': 0.0} description=Plot of T2LM vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:36.087353Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_SWI_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_SWI_NMR_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1262.04, 'cpu_percent': 0.0} description=Data snapshot for regplot_SWI_NMR_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:36.121253Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1262.05, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:36.924987Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_SWI_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_SWI_NMR_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1279.87, 'cpu_percent': 0.0} description=Plot of SWI_NMR vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:36.985904Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2_P20_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2_P20_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1280.22, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2_P20_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:37.031721Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1280.22, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:37.813418Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2_P20_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2_P20_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1297.9, 'cpu_percent': 0.0} description=Plot of T2_P20 vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:37.861524Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_PHIT_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_PHIT_NMR_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1298.68, 'cpu_percent': 0.0} description=Data snapshot for regplot_PHIT_NMR_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:37.895295Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1298.69, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:38.722168Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_PHIT_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_PHIT_NMR_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1316.6, 'cpu_percent': 0.0} description=Plot of PHIT_NMR vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:38.775609Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DRES_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DRES_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1317.25, 'cpu_percent': 0.0} description=Data snapshot for regplot_DRES_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:38.816284Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1317.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:39.618170Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DRES_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DRES_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1334.79, 'cpu_percent': 0.0} description=Plot of DRES vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:39.671144Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DT_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DT_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1335.22, 'cpu_percent': 0.0} description=Data snapshot for regplot_DT_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:39.708185Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1335.31, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:40.508562Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DT_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DT_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1353.05, 'cpu_percent': 0.0} description=Plot of DT vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:40.558349Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DPHIT_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DPHIT_NMR_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1353.73, 'cpu_percent': 0.0} description=Data snapshot for regplot_DPHIT_NMR_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:40.894718Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 828.31, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:41.681490Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DPHIT_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DPHIT_NMR_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 847.05, 'cpu_percent': 0.0} description=Plot of DPHIT_NMR vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:41.726275Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2LM_LONG_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2LM_LONG_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 847.51, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2LM_LONG_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:41.757631Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 847.51, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:42.533607Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2LM_LONG_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2LM_LONG_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 864.64, 'cpu_percent': 0.0} description=Plot of T2LM_LONG vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:42.579057Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_BVI_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_BVI_NMR_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 864.64, 'cpu_percent': 0.0} description=Data snapshot for regplot_BVI_NMR_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:42.618091Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 864.64, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:43.377297Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_BVI_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_BVI_NMR_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 881.82, 'cpu_percent': 0.0} description=Plot of BVI_NMR vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:43.437599Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_BFV_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_BFV_NMR_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 881.82, 'cpu_percent': 0.0} description=Data snapshot for regplot_BFV_NMR_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:43.471145Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 881.82, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:44.270274Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_BFV_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_BFV_NMR_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 899.06, 'cpu_percent': 0.0} description=Plot of BFV_NMR vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:44.323543Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_SDR_PROXY_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_SDR_PROXY_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 899.06, 'cpu_percent': 0.0} description=Data snapshot for regplot_SDR_PROXY_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:44.353341Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 899.06, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:45.128592Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_SDR_PROXY_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_SDR_PROXY_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 916.27, 'cpu_percent': 0.0} description=Plot of SDR_PROXY vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:45.179330Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2_P50_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2_P50_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 916.27, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2_P50_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:45.204587Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 916.27, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:45.966145Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2_P50_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_T2_P50_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 933.39, 'cpu_percent': 0.0} description=Plot of T2_P50 vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:46.011288Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DEN_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DEN_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 933.39, 'cpu_percent': 0.0} description=Data snapshot for regplot_DEN_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:46.047748Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 933.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:46.815667Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DEN_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_DEN_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 950.56, 'cpu_percent': 0.0} description=Plot of DEN vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:46.877824Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_PHIE_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_PHIE_NMR_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 950.56, 'cpu_percent': 0.0} description=Data snapshot for regplot_PHIE_NMR_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:46.912744Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 950.56, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:47.727876Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_PHIE_NMR_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_PHIE_NMR_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 967.81, 'cpu_percent': 0.0} description=Plot of PHIE_NMR vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:47.797346Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_RD_LOG10_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_RD_LOG10_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 967.81, 'cpu_percent': 0.0} description=Data snapshot for regplot_RD_LOG10_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:47.833371Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 967.81, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:48.587402Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_RD_LOG10_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_RD_LOG10_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 985.0, 'cpu_percent': 0.0} description=Plot of RD_LOG10 vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:48.644971Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_RS_LOG10_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_RS_LOG10_vs_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 985.0, 'cpu_percent': 0.0} description=Data snapshot for regplot_RS_LOG10_vs_DT2_P50 plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:48.684876Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 985.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:49.457395Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_RS_LOG10_vs_DT2_P50 artifact_path=obmiq_DT2P50_log_scout\\visual_report\\target_relationship\\regplot_RS_LOG10_vs_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1002.62, 'cpu_percent': 0.0} description=Plot of RS_LOG10 vs. DT2_P50. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:49.496463Z [info     ] 特征与目标关系图已全部生成。                 [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1002.62, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:49.513420Z [info     ] 开始 LogScout 特征分析步骤             [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1002.62, 'cpu_percent': 0.0} num_features=18 prefix=obmiq_DPHIT target=DPHIT_NMR task_type=regression\n", "2025-07-22T05:55:49.540537Z [info     ] 开始定量分析计算...                    [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1002.62, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:49.563597Z [info     ] 相关系数矩阵计算完成。                    [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1002.99, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:49.683335Z [info     ] VIF分数计算完成。                     [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.2, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:49.979930Z [info     ] 互信息分数计算完成。                     [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:50.006353Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.pearson_correlation artifact_path=obmiq_DPHIT_log_scout\\quantitative_report\\pearson_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0} description=输入特征间的皮尔逊相关系数矩阵。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:50.036131Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.pearson_correlation artifact_path=obmiq_DPHIT_log_scout\\quantitative_report\\pearson_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0} description=用于生成皮尔逊相关性热力图的数据快照。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:50.073753Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.spearman_correlation artifact_path=obmiq_DPHIT_log_scout\\quantitative_report\\spearman_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0} description=输入特征间的斯皮尔曼相关系数矩阵。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:50.096906Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.spearman_correlation artifact_path=obmiq_DPHIT_log_scout\\quantitative_report\\spearman_correlation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0} description=用于生成斯皮尔曼相关性热力图的数据快照。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:50.126269Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.vif_scores artifact_path=obmiq_DPHIT_log_scout\\quantitative_report\\vif_scores.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0} description=每个输入特征的方差膨胀因子(VIF)得分。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:50.153383Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.reports.mutual_information artifact_path=obmiq_DPHIT_log_scout\\quantitative_report\\mutual_information.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0} description=每个输入特征与目标特征之间的互信息得分。 operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:50.173381Z [info     ] 所有定量分析产物已保存并注册。                [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:50.177160Z [info     ] 开始可视化分析...                     [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:50.201654Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1003.45, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.heatmap\n", "2025-07-22T05:55:51.222232Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.pearson_heatmap artifact_path=obmiq_DPHIT_log_scout\\visual_report\\feature_correlation\\pearson_heatmap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1045.57, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:52.288800Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.pearson_clustermap artifact_path=obmiq_DPHIT_log_scout\\visual_report\\feature_correlation\\pearson_clustermap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1108.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:52.355096Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1108.31, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.heatmap\n", "2025-07-22T05:55:53.388768Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.spearman_heatmap artifact_path=obmiq_DPHIT_log_scout\\visual_report\\feature_correlation\\spearman_heatmap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1150.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:54.432443Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.spearman_clustermap artifact_path=obmiq_DPHIT_log_scout\\visual_report\\feature_correlation\\spearman_clustermap.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1213.03, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:54.481345Z [info     ] 特征相关性图表已生成。                    [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1213.03, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:54.495281Z [warning  ] 输入特征数量 (18) 超过配置上限 (10), 跳过 Pairplot 生成以避免性能问题。 [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1213.03, 'cpu_percent': 0.0} extra={'feature_count': 18, 'max_features': 10}\n", "2025-07-22T05:55:54.513788Z [info     ] 正在为每个输入特征生成与目标的关系图...          [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1213.03, 'cpu_percent': 0.0}\n", "2025-07-22T05:55:54.535250Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_CN_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_CN_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1213.12, 'cpu_percent': 0.0} description=Data snapshot for regplot_CN_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:54.570349Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1213.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:55.331538Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_CN_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_CN_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1230.57, 'cpu_percent': 0.0} description=Plot of CN vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:55.395403Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_SWB_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_SWB_NMR_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1230.82, 'cpu_percent': 0.0} description=Data snapshot for regplot_SWB_NMR_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:55.432440Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1230.82, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:56.232307Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_SWB_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_SWB_NMR_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1248.0, 'cpu_percent': 0.0} description=Plot of SWB_NMR vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:56.293610Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2LM_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2LM_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1248.0, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2LM_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:56.328590Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1248.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:57.100487Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2LM_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2LM_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1265.04, 'cpu_percent': 0.0} description=Plot of T2LM vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:57.164273Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_SWI_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_SWI_NMR_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1265.69, 'cpu_percent': 0.0} description=Data snapshot for regplot_SWI_NMR_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:57.200514Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1265.69, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:57.975082Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_SWI_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_SWI_NMR_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1283.52, 'cpu_percent': 0.0} description=Plot of SWI_NMR vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:58.039920Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2_P20_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2_P20_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1283.54, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2_P20_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:58.075568Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1283.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:58.785198Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2_P20_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2_P20_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1300.56, 'cpu_percent': 0.0} description=Plot of T2_P20 vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:58.857991Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_PHIT_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_PHIT_NMR_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1300.71, 'cpu_percent': 0.0} description=Data snapshot for regplot_PHIT_NMR_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:58.896778Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1300.71, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:55:59.686935Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_PHIT_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_PHIT_NMR_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1318.26, 'cpu_percent': 0.0} description=Plot of PHIT_NMR vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:59.741346Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DRES_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DRES_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1318.91, 'cpu_percent': 0.0} description=Data snapshot for regplot_DRES_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:55:59.771898Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1318.91, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:00.544299Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DRES_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DRES_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1336.12, 'cpu_percent': 0.0} description=Plot of DRES vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:00.594937Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DT_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DT_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1336.12, 'cpu_percent': 0.0} description=Data snapshot for regplot_DT_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:00.629983Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1336.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:01.408564Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DT_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DT_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1353.58, 'cpu_percent': 0.0} description=Plot of DT vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:01.461090Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2LM_LONG_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2LM_LONG_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1354.29, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2LM_LONG_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:01.491921Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1354.29, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:02.298212Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2LM_LONG_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2LM_LONG_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1371.93, 'cpu_percent': 0.0} description=Plot of T2LM_LONG vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:02.556466Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_BVI_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_BVI_NMR_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1372.3, 'cpu_percent': 0.0} description=Data snapshot for regplot_BVI_NMR_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:02.597058Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1372.3, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:03.376709Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_BVI_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_BVI_NMR_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1389.34, 'cpu_percent': 0.0} description=Plot of BVI_NMR vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:03.433097Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_BFV_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_BFV_NMR_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1389.87, 'cpu_percent': 0.0} description=Data snapshot for regplot_BFV_NMR_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:03.467097Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1389.87, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:04.263577Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_BFV_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_BFV_NMR_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1407.67, 'cpu_percent': 0.0} description=Plot of BFV_NMR vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:04.320644Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_SDR_PROXY_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_SDR_PROXY_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1408.0, 'cpu_percent': 0.0} description=Data snapshot for regplot_SDR_PROXY_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:04.355277Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1407.99, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:05.104042Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_SDR_PROXY_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_SDR_PROXY_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1425.59, 'cpu_percent': 0.0} description=Plot of SDR_PROXY vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:05.156562Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_T2_P50_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2_P50_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.21, 'cpu_percent': 0.0} description=Data snapshot for regplot_T2_P50_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:05.197700Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.21, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:05.970490Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_T2_P50_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_T2_P50_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1443.85, 'cpu_percent': 0.0} description=Plot of T2_P50 vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:06.028823Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DEN_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DEN_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.45, 'cpu_percent': 0.0} description=Data snapshot for regplot_DEN_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:06.068446Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.46, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:06.853740Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DEN_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DEN_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1461.9, 'cpu_percent': 0.0} description=Plot of DEN vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:06.916372Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_DT2_P50_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DT2_P50_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1462.98, 'cpu_percent': 0.0} description=Data snapshot for regplot_DT2_P50_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:06.956406Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1462.98, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:07.704356Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_DT2_P50_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_DT2_P50_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1480.21, 'cpu_percent': 0.0} description=Plot of DT2_P50 vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:07.761797Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_PHIE_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_PHIE_NMR_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1480.64, 'cpu_percent': 0.0} description=Data snapshot for regplot_PHIE_NMR_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:07.803703Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1480.65, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:08.611431Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_PHIE_NMR_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_PHIE_NMR_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1498.22, 'cpu_percent': 0.0} description=Plot of PHIE_NMR vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:08.669484Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_RD_LOG10_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_RD_LOG10_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1499.14, 'cpu_percent': 0.0} description=Data snapshot for regplot_RD_LOG10_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:08.707053Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1499.14, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:09.518441Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_RD_LOG10_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_RD_LOG10_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1516.89, 'cpu_percent': 0.0} description=Plot of RD_LOG10 vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:09.573949Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.data_snapshots.target_relationship.regplot_RS_LOG10_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_RS_LOG10_vs_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1517.42, 'cpu_percent': 0.0} description=Data snapshot for regplot_RS_LOG10_vs_DPHIT_NMR plot. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:09.612494Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1517.42, 'cpu_percent': 0.0} operation=apply_profile profile_name=log_scout.regplot\n", "2025-07-22T05:56:10.399090Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=log_scout.plots.target_relationship.regplot_RS_LOG10_vs_DPHIT_NMR artifact_path=obmiq_DPHIT_log_scout\\visual_report\\target_relationship\\regplot_RS_LOG10_vs_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1535.22, 'cpu_percent': 0.0} description=Plot of RS_LOG10 vs. DPHIT_NMR. operation=register_artifact run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:10.443853Z [info     ] 特征与目标关系图已全部生成。                 [logwp.extras.ml.log_scout.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1535.22, 'cpu_percent': 0.0}\n", "2025-07-22T05:56:10.457026Z [info     ] Run marked as successful       [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1535.22, 'cpu_percent': 0.0} operation=mark_success run_id=20250722-055528-b2b72ae7\n", "2025-07-22T05:56:10.485409Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1535.22, 'cpu_percent': 0.0} duration_seconds=42.287 manifest_path=output01\\manifest.json operation=finalize run_id=20250722-055528-b2b72ae7 status=COMPLETED\n"]}], "source": ["\n", "# 5. Initialize LogScoutConfig\n", "log_scout_config = LogScoutConfig()\n", "\n", "# 6. <PERSON><PERSON> a <PERSON><PERSON><PERSON>\n", "run_dir = Path('output01')\n", "with RunContext(run_dir=run_dir, overwrite=True) as ctx:\n", "    # 7. <PERSON>\n", "    # 从特征列表中移除目标曲线，避免自相关\\n\n", "    ds_1d_features = [c for c in ds_1d_bundle.data.columns if c != 'DT2_P50']\n", "    run_log_scout_step(\n", "        config=log_scout_config,\n", "        ctx=ctx,\n", "        data=ds_1d_bundle.data,\n", "        features=ds_1d_features,\n", "        target='DT2_P50',  # 使用一个实际存在的曲线作为示例目标\n", "        task_type='regression',  # 选择适当的任务类型\n", "        prefix='obmiq_DT2P50'\n", "    )\n", "\n", "    ds_1d_features = [c for c in ds_1d_bundle.data.columns if c != 'DPHIT_NMR']\n", "    run_log_scout_step(\n", "        config=log_scout_config,\n", "        ctx=ctx,\n", "        data=ds_1d_bundle.data,\n", "        features=ds_1d_features,\n", "        target='DPHIT_NMR',  # 使用一个实际存在的曲线作为示例目标\n", "        task_type='regression',  # 选择适当的任务类型\n", "        prefix='obmiq_DPHIT'\n", "    )"]}, {"cell_type": "markdown", "id": "d98e564e", "metadata": {}, "source": ["ChatGPT分析结果：\n", "SWB_NMR, BFV_NMR, PHIE_NMR, RD_LOG10, CN, DRES, DEN, RS_LOG10, DT, BVI_NMR"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}