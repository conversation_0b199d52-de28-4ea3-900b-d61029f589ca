"""validation包的集成测试。

测试PLT盲井检验和渗透率相关性分析两个步骤的协同工作能力。
"""

from __future__ import annotations

from typing import TYPE_CHECKING

from logwp.extras.plotting import registry as plot_registry

from scape.core.validation import (
    PermCorrelationArtifacts,
    PermCorrelationConfig,
    PermCorrelationPlotProfiles,
    PltAnalysisArtifacts,
    PltAnalysisConfig,
    PltAnalysisPlotProfiles,
    PltPlotTypes,
    run_perm_correlation_step,
    run_plt_analysis_step,
)

if TYPE_CHECKING:
    from logwp.extras.tracking import RunContext
    from logwp.models.datasets.bundle import WpDataFrameBundle


def test_full_validation_workflow(
    run_context: RunContext,
    validation_pred_bundle: WpDataFrameBundle,
    validation_plt_bundle: WpDataFrameBundle,
    validation_core_bundle: WpDataFrameBundle,
):
    """
    测试完整的验证工作流，验证两个步骤的协同工作和产物生成。

    此测试用例扮演"工作流驱动脚本"的角色，负责：
    1. 运行PLT盲井检验步骤。
    2. 运行渗透率相关性分析步骤。
    3. 验证所有步骤的产物和指标都已正确生成和注册。
    """
    # ==========================================================================
    # 1. 准备配置和数据
    # ==========================================================================
    # 假设测试数据中至少有一口共同的井，例如 'C-1'
    test_well = "C-1"

    # 准备PLT分析步骤的配置
    plt_config = PltAnalysisConfig()
    # 从注册表获取默认绘图模板，并按需修改以测试自定义功能
    capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE)
    capture_profile.title_props["label"] = "Custom Capture Curve Title for Test"

    plt_plot_profiles = {
        PltPlotTypes.CAPTURE_CURVE: capture_profile,
        PltPlotTypes.LORENZ_CURVE: plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE),
        PltPlotTypes.CONTRIBUTION_CROSSPLOT: plot_registry.get(
            PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT
        ),
    }

    # 准备渗透率相关性分析步骤的配置
    perm_corr_config = PermCorrelationConfig(relaxed_wells=[test_well])
    perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT)

    # ==========================================================================
    # 2. 运行PLT盲井检验步骤 (Step 1)
    # ==========================================================================
    plt_summary = run_plt_analysis_step(
        config=plt_config,
        ctx=run_context,
        prediction_bundle=validation_pred_bundle,
        plt_bundle=validation_plt_bundle,
        permeability_curve="K_PRED_SWIFT",
        flow_rate_curve="QOZI",
        plot_profiles=plt_plot_profiles,
    )

    # 断言PLT步骤的产物和指标
    assert test_well in plt_summary
    manifest = run_context.manifest

    assert f"plt_analysis.{test_well}" in manifest["metrics"]
    assert "spearman_rho" in manifest["metrics"][f"plt_analysis.{test_well}"]

    capture_plot_artifact = f"{PltAnalysisArtifacts.CAPTURE_CURVE_PLOT_PREFIX.value}{test_well}"
    assert capture_plot_artifact in manifest["artifacts"]
    assert run_context.get_artifact_path(capture_plot_artifact).exists()

    # ==========================================================================
    # 3. 运行渗透率相关性分析步骤 (Step 2)
    # ==========================================================================
    perm_corr_summary = run_perm_correlation_step(
        config=perm_corr_config,
        ctx=run_context,
        left_bundle=validation_pred_bundle,
        right_bundle=validation_core_bundle,
        left_curve="K_PRED_SWIFT",
        right_curve="PERM",
        plot_profile=perm_corr_profile,
    )

    # 断言渗透率相关性步骤的产物和指标
    # 注意：渗透率相关性分析可能处理不同的井，所以检查实际处理的井
    perm_corr_wells = list(perm_corr_summary.keys())
    assert len(perm_corr_wells) > 0, "应该至少有一口井被处理"
    actual_perm_well = perm_corr_wells[0]  # 使用实际处理的第一口井
    manifest = run_context.manifest  # 重新获取以包含新步骤的记录

    assert f"perm_corr_analysis.{actual_perm_well}" in manifest["metrics"]
    assert "3x" in manifest["metrics"][f"perm_corr_analysis.{actual_perm_well}"]

    crossplot_artifact = f"{PermCorrelationArtifacts.CROSSPLOT_PREFIX}{actual_perm_well}"
    assert crossplot_artifact in manifest["artifacts"]
    assert run_context.get_artifact_path(crossplot_artifact).exists()
