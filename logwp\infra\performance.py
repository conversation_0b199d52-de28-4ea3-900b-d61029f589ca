from __future__ import annotations

"""logwp.infra.performance - 性能监控工具

提供函数性能监控和资源使用统计。

Architecture
------------
层次/依赖: logwp包工具层，性能监控
设计原则: 装饰器模式、非侵入式、轻量级
性能特征: 低开销监控、详细统计、可配置

Core Features
-------------
- **performance_monitor**: 函数性能监控装饰器
- **memory_monitor**: 内存使用监控
- **gpu_monitor**: GPU资源监控
- **统计收集**: 执行时间、内存使用、GPU状态
- **日志集成**: 与structlog集成

Examples
--------
>>> from logwp.infra.performance import performance_monitor
>>>
>>> @performance_monitor
>>> def process_data(df):
...     return df.groupby('WELL_NO').mean()
"""

import time
import functools
import psutil
from typing import Any, Callable, Dict, Optional
from datetime import datetime

from .logging_config import get_logger

__all__ = [
    "performance_monitor",
    "memory_monitor",
    "gpu_monitor",
    "PerformanceStats",
]

logger = get_logger(__name__)


class PerformanceStats:
    """性能统计数据类。

    收集和管理函数执行的性能统计信息。

    Attributes:
        function_name: 函数名称
        start_time: 开始时间
        end_time: 结束时间
        execution_time: 执行时间（秒）
        memory_before: 执行前内存使用（MB）
        memory_after: 执行后内存使用（MB）
        memory_peak: 峰值内存使用（MB）
        gpu_info: GPU信息（如果可用）
    """

    def __init__(self, function_name: str):
        self.function_name = function_name
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.execution_time: Optional[float] = None
        self.memory_before: Optional[float] = None
        self.memory_after: Optional[float] = None
        self.memory_peak: Optional[float] = None
        self.gpu_info: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "function_name": self.function_name,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "execution_time_ms": self.execution_time * 1000 if self.execution_time else None,
            "memory_before_mb": self.memory_before,
            "memory_after_mb": self.memory_after,
            "memory_peak_mb": self.memory_peak,
            "memory_delta_mb": (
                self.memory_after - self.memory_before
                if self.memory_before and self.memory_after else None
            ),
            "gpu_info": self.gpu_info
        }


def get_memory_usage() -> float:
    """获取当前内存使用量（MB）。

    Returns:
        float: 内存使用量（MB）
    """
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # 转换为MB
    except Exception:
        return 0.0


def get_gpu_info() -> Optional[Dict[str, Any]]:
    """获取GPU信息。

    Returns:
        Optional[Dict[str, Any]]: GPU信息字典，如果GPU不可用则返回None
    """
    try:
        # 尝试导入GPU工具
        from .gpu.gpu_utils import is_gpu_available, get_gpu_info as _get_gpu_info

        if is_gpu_available():
            return _get_gpu_info()
        return None

    except ImportError:
        return None
    except Exception:
        return None


def performance_monitor(
    log_level: str = "DEBUG",
    include_memory: bool = True,
    include_gpu: bool = False
) -> Callable:
    """性能监控装饰器。

    监控函数执行时间、内存使用和GPU状态。

    Args:
        log_level: 日志级别，默认DEBUG
        include_memory: 是否包含内存监控
        include_gpu: 是否包含GPU监控

    Returns:
        Callable: 装饰器函数

    Examples:
        >>> @performance_monitor()
        >>> def process_data(df):
        ...     return df.groupby('WELL_NO').mean()

        >>> @performance_monitor(log_level="INFO", include_gpu=True)
        >>> def gpu_computation(data):
        ...     return compute_on_gpu(data)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 创建性能统计对象
            stats = PerformanceStats(func.__name__)

            # 记录开始状态
            stats.start_time = datetime.now()
            start_perf = time.perf_counter()

            if include_memory:
                stats.memory_before = get_memory_usage()

            if include_gpu:
                stats.gpu_info = get_gpu_info()

            try:
                # 执行函数
                result = func(*args, **kwargs)

                # 记录结束状态
                end_perf = time.perf_counter()
                stats.end_time = datetime.now()
                stats.execution_time = end_perf - start_perf

                if include_memory:
                    stats.memory_after = get_memory_usage()
                    # 简单的峰值估算（实际峰值可能更高）
                    stats.memory_peak = max(
                        stats.memory_before or 0,
                        stats.memory_after or 0
                    )

                # 记录性能日志
                log_data = stats.to_dict()
                log_data.update({
                    "operation": "function_execution",
                    "status": "success"
                })

                getattr(logger, log_level.lower())(
                    f"函数 {func.__name__} 执行完成",
                    **log_data
                )

                return result

            except Exception as e:
                # 记录异常状态
                end_perf = time.perf_counter()
                stats.end_time = datetime.now()
                stats.execution_time = end_perf - start_perf

                if include_memory:
                    stats.memory_after = get_memory_usage()

                # 记录异常日志
                log_data = stats.to_dict()
                log_data.update({
                    "operation": "function_execution",
                    "status": "error",
                    "error": str(e)
                })

                logger.error(
                    f"函数 {func.__name__} 执行失败",
                    **log_data
                )

                raise

        return wrapper
    return decorator


def memory_monitor(threshold_mb: float = 100.0) -> Callable:
    """内存监控装饰器。

    监控函数内存使用，超过阈值时发出警告。

    Args:
        threshold_mb: 内存增长阈值（MB）

    Returns:
        Callable: 装饰器函数

    Examples:
        >>> @memory_monitor(threshold_mb=50.0)
        >>> def memory_intensive_function(data):
        ...     return process_large_data(data)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            memory_before = get_memory_usage()

            try:
                result = func(*args, **kwargs)
                memory_after = get_memory_usage()
                memory_delta = memory_after - memory_before

                if memory_delta > threshold_mb:
                    logger.warning(
                        f"函数 {func.__name__} 内存使用超过阈值",
                        function_name=func.__name__,
                        memory_before_mb=memory_before,
                        memory_after_mb=memory_after,
                        memory_delta_mb=memory_delta,
                        threshold_mb=threshold_mb
                    )
                else:
                    logger.debug(
                        f"函数 {func.__name__} 内存使用正常",
                        function_name=func.__name__,
                        memory_delta_mb=memory_delta
                    )

                return result

            except Exception as e:
                memory_after = get_memory_usage()
                memory_delta = memory_after - memory_before

                logger.error(
                    f"函数 {func.__name__} 执行失败，内存变化",
                    function_name=func.__name__,
                    memory_delta_mb=memory_delta,
                    error=str(e)
                )

                raise

        return wrapper
    return decorator


def gpu_monitor(log_memory: bool = True) -> Callable:
    """GPU监控装饰器。

    监控函数GPU资源使用情况。

    Args:
        log_memory: 是否记录GPU内存使用

    Returns:
        Callable: 装饰器函数

    Examples:
        >>> @gpu_monitor(log_memory=True)
        >>> def gpu_computation(data):
        ...     return compute_on_gpu(data)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            gpu_info_before = get_gpu_info() if log_memory else None

            try:
                result = func(*args, **kwargs)
                gpu_info_after = get_gpu_info() if log_memory else None

                if gpu_info_before and gpu_info_after:
                    memory_delta = (
                        gpu_info_after.get("memory_used", 0) -
                        gpu_info_before.get("memory_used", 0)
                    )

                    logger.debug(
                        f"函数 {func.__name__} GPU资源使用",
                        function_name=func.__name__,
                        gpu_memory_before=gpu_info_before.get("memory_used"),
                        gpu_memory_after=gpu_info_after.get("memory_used"),
                        gpu_memory_delta=memory_delta,
                        gpu_device=gpu_info_after.get("device_name")
                    )

                return result

            except Exception as e:
                logger.error(
                    f"函数 {func.__name__} GPU执行失败",
                    function_name=func.__name__,
                    error=str(e)
                )

                raise

        return wrapper
    return decorator


# 便捷的性能监控函数
def monitor_performance(func: Callable) -> Callable:
    """便捷的性能监控装饰器。

    使用默认设置监控函数性能。

    Args:
        func: 要监控的函数

    Returns:
        Callable: 装饰后的函数

    Examples:
        >>> @monitor_performance
        >>> def my_function(data):
        ...     return process(data)
    """
    return performance_monitor()(func)


def monitor_memory(func: Callable) -> Callable:
    """便捷的内存监控装饰器。

    使用默认设置监控函数内存使用。

    Args:
        func: 要监控的函数

    Returns:
        Callable: 装饰后的函数

    Examples:
        >>> @monitor_memory
        >>> def memory_intensive_function(data):
        ...     return process_large_data(data)
    """
    return memory_monitor()(func)
