"""测试RunContext类的功能。

测试实验运行上下文管理器的核心功能，包括：
- 基本的上下文管理器功能
- 参数、指标、产物记录
- 文件I/O和清单管理
- 错误处理和状态管理
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict

import pytest

from logwp.extras.tracking import (
    RunContext,
    TrackingConfig,
    ArtifactNotFoundError,
    RunNotFoundError,
    RunExistsError,
)


class TestRunContextBasic:
    """测试RunContext的基本功能。"""

    def test_context_manager_basic(
        self,
        run_dir: Path,
        sample_config: Dict[str, Any],
        sample_metadata: Dict[str, Any]
    ):
        """测试基本的上下文管理器功能。"""
        with RunContext(
            run_dir=run_dir,
            config=sample_config,
            metadata=sample_metadata
        ) as ctx:
            assert ctx.run_id is not None
            assert ctx.run_dir == run_dir
            assert not ctx.is_completed
            assert not ctx.is_successful

        # 运行完成后检查状态
        assert ctx.is_completed
        assert ctx.is_successful
        assert ctx.manifest["status"] == "COMPLETED"

    def test_manifest_file_creation(
        self,
        run_dir: Path,
        sample_config: Dict[str, Any]
    ):
        """测试清单文件的创建。"""
        with RunContext(run_dir=run_dir, config=sample_config) as ctx:
            pass

        # 检查清单文件是否创建
        manifest_path = run_dir / "manifest.json"
        assert manifest_path.exists()

        # 验证清单内容
        with open(manifest_path) as f:
            manifest = json.load(f)

        assert manifest["run_id"] == ctx.run_id
        assert manifest["status"] == "COMPLETED"
        assert "start_time_utc" in manifest
        assert "end_time_utc" in manifest
        assert "duration_seconds" in manifest

    def test_config_snapshot_creation(
        self,
        run_dir: Path,
        sample_config: Dict[str, Any]
    ):
        """测试配置快照的创建。"""
        with RunContext(run_dir=run_dir, config=sample_config) as ctx:
            pass

        # 检查配置快照文件
        config_path = run_dir / "config_snapshot.yaml"
        assert config_path.exists()

        # 验证清单中的配置路径引用
        assert ctx.manifest["config_snapshot_path"] == "config_snapshot.yaml"

    def test_custom_tracking_config(
        self,
        run_dir: Path,
        tracking_config: TrackingConfig
    ):
        """测试自定义追踪配置。"""
        with RunContext(
            run_dir=run_dir,
            tracking_config=tracking_config
        ) as ctx:
            pass

        # 检查使用自定义文件名
        manifest_path = run_dir / tracking_config.manifest_filename
        assert manifest_path.exists()

        config_path = run_dir / tracking_config.config_snapshot_filename
        # 没有配置数据时不应创建配置文件
        assert not config_path.exists()


class TestRunContextLogging:
    """测试RunContext的日志记录功能。"""

    def test_log_parameter(self, run_dir: Path, sample_parameters: Dict[str, Any]):
        """测试参数记录功能。"""
        with RunContext(run_dir=run_dir) as ctx:
            for key, value in sample_parameters.items():
                ctx.log_parameter(key, value)

        # 验证参数记录
        assert ctx.parameters == sample_parameters
        assert ctx.manifest["parameters"] == sample_parameters

    def test_log_metrics_summary(self, run_dir: Path, sample_metrics: Dict[str, float]):
        """测试摘要指标记录。"""
        with RunContext(run_dir=run_dir) as ctx:
            ctx.log_metrics(sample_metrics)

        # 验证指标记录
        assert ctx.metrics["summary"] == sample_metrics
        assert ctx.manifest["metrics"]["summary"] == sample_metrics

    def test_log_metrics_by_step(self, run_dir: Path):
        """测试分步骤指标记录。"""
        training_metrics = {"loss": 0.123, "accuracy": 0.95}
        validation_metrics = {"val_loss": 0.156, "val_accuracy": 0.92}

        with RunContext(run_dir=run_dir) as ctx:
            ctx.log_metrics(training_metrics, step_name="training")
            ctx.log_metrics(validation_metrics, step_name="validation")

        # 验证分步骤指标
        assert ctx.metrics["training"] == training_metrics
        assert ctx.metrics["validation"] == validation_metrics

    def test_log_artifact(self, run_dir: Path, sample_artifact_file: Path):
        """测试产物记录功能。"""
        with RunContext(run_dir=run_dir) as ctx:
            ctx.log_artifact(sample_artifact_file, artifact_name="test_model", artifact_path="models/test_model.json")

        # 验证产物记录
        artifacts = ctx.artifacts
        assert "test_model" in artifacts

        artifact_info = artifacts["test_model"]
        assert artifact_info["path"] == "models/test_model.json"
        assert artifact_info["type"] == "data"  # .json文件推断为data类型

        # 验证哈希记录
        metadata = artifact_info["metadata"]
        assert metadata["hash_algorithm"] == "sha256"
        assert len(metadata["hash"]) == 64

        # 验证文件确实被复制
        target_path = run_dir / "models/test_model.json"
        assert target_path.exists()

    def test_log_artifact_default_path(self, run_dir: Path, sample_artifact_file: Path):
        """测试使用默认路径的产物记录。"""
        with RunContext(run_dir=run_dir) as ctx:
            ctx.log_artifact(sample_artifact_file, artifact_name="sample_artifact.json")

        # 验证使用 artifact_name 作为默认路径
        artifacts = ctx.artifacts
        assert "sample_artifact.json" in artifacts
        assert artifacts["sample_artifact.json"]["path"] == "sample_artifact.json"

    def test_log_artifact_with_custom_hash(self, run_dir: Path, sample_artifact_file: Path):
        """测试使用自定义哈希算法记录产物。"""
        # 使用md5作为哈希算法
        tracking_config = TrackingConfig(hash_algorithm="md5")

        with RunContext(run_dir=run_dir, tracking_config=tracking_config) as ctx:
            ctx.log_artifact(sample_artifact_file, artifact_name="test_artifact")

        # 验证清单
        artifacts = ctx.artifacts
        assert "test_artifact" in artifacts
        metadata = artifacts["test_artifact"]["metadata"]
        assert metadata["hash_algorithm"] == "md5"
        assert isinstance(metadata["hash"], str)
        assert len(metadata["hash"]) == 32

    def test_get_artifact_path(self, run_dir: Path, sample_artifact_file: Path):
        """测试产物路径获取。"""
        with RunContext(run_dir=run_dir) as ctx:
            ctx.log_artifact(sample_artifact_file, artifact_name="my_model", artifact_path="models/test.json")

            # 获取产物路径
            artifact_path = ctx.get_artifact_path("my_model")
            assert artifact_path == run_dir / "models/test.json"
            assert artifact_path.exists()

    def test_get_artifact_path_not_found(self, run_dir: Path):
        """测试获取不存在产物的路径。"""
        with RunContext(run_dir=run_dir) as ctx:
            with pytest.raises(ArtifactNotFoundError) as exc_info:
                ctx.get_artifact_path("nonexistent.file")

            assert "nonexistent.file" in str(exc_info.value)
            assert exc_info.value.artifact_name == "nonexistent.file"

    def test_get_step_dir(self, run_dir: Path):
        """测试步骤目录获取。"""
        with RunContext(run_dir=run_dir) as ctx:
            step_dir = ctx.get_step_dir("training")

            assert step_dir == run_dir / "training"
            assert step_dir.exists()
            assert step_dir.is_dir()

    def test_log_artifact_name_overwrite_protection(self, run_dir: Path, temp_dir: Path):
        """测试 artifact_name 覆盖保护机制。

        验证使用相同的 artifact_name 会覆盖之前的记录，
        这是修复后的预期行为（使用逻辑名称作为键）。
        """
        # 创建两个不同的文件
        file1 = temp_dir / "step1" / "results.json"
        file1.parent.mkdir(exist_ok=True)
        file1.write_text('{"step": 1, "value": 100}')

        file2 = temp_dir / "step2" / "results.json"
        file2.parent.mkdir(exist_ok=True)
        file2.write_text('{"step": 2, "value": 200}')

        with RunContext(run_dir=run_dir) as ctx:
            # 记录第一个产物
            ctx.log_artifact(file1, artifact_name="results", artifact_path="step1/results.json")

            # 验证第一个产物已记录
            assert "results" in ctx.artifacts
            assert ctx.artifacts["results"]["path"] == "step1/results.json"

            # 记录第二个产物，使用相同的 artifact_name
            ctx.log_artifact(file2, artifact_name="results", artifact_path="step2/results.json")

            # 验证第二个产物覆盖了第一个（这是预期行为）
            assert "results" in ctx.artifacts
            assert ctx.artifacts["results"]["path"] == "step2/results.json"

            # 验证清单中只有一个 "results" 条目
            assert len([k for k in ctx.artifacts.keys() if k == "results"]) == 1

    def test_register_artifact(self, run_dir: Path, temp_dir: Path):
        """测试 register_artifact 方法。"""
        with RunContext(run_dir=run_dir) as ctx:
            # 先手动复制文件到运行目录
            target_path = run_dir / "models" / "existing_model.pkl"
            target_path.parent.mkdir(parents=True, exist_ok=True)
            target_path.write_text("mock model data")

            # 注册已存在的产物
            ctx.register_artifact(
                artifact_path="models/existing_model.pkl",
                artifact_name="existing_model"
            )

            # 验证产物已注册
            assert "existing_model" in ctx.artifacts
            artifact_info = ctx.artifacts["existing_model"]
            assert artifact_info["path"] == "models/existing_model.pkl"
            assert artifact_info["type"] == "model"

            # 验证可以通过 get_artifact_path 获取路径
            artifact_path = ctx.get_artifact_path("existing_model")
            assert artifact_path == run_dir / "models/existing_model.pkl"
            assert artifact_path.exists()

    def test_register_artifact_with_hash(self, run_dir: Path, temp_dir: Path):
        """测试 register_artifact 方法的哈希计算。"""
        # 使用自定义哈希算法
        tracking_config = TrackingConfig(hash_algorithm="md5")

        with RunContext(run_dir=run_dir, tracking_config=tracking_config) as ctx:
            # 创建并复制文件到运行目录
            target_path = run_dir / "test_file.txt"
            target_path.parent.mkdir(parents=True, exist_ok=True)
            target_path.write_text("test content for hashing")

            # 注册产物
            ctx.register_artifact(
                artifact_path="test_file.txt",
                artifact_name="test_file"
            )

            # 验证哈希信息
            artifact_info = ctx.artifacts["test_file"]
            metadata = artifact_info["metadata"]
            assert metadata["hash_algorithm"] == "md5"
            assert len(metadata["hash"]) == 32  # MD5 哈希长度


class TestRunContextStateManagement:
    """测试RunContext的状态管理。"""

    def test_success_marking(self, run_dir: Path):
        """测试成功状态标记。"""
        with RunContext(run_dir=run_dir) as ctx:
            ctx.success()
            assert ctx.manifest["status"] == "COMPLETED"

        assert ctx.is_successful
        assert ctx.is_completed

    def test_failure_marking(self, run_dir: Path):
        """测试失败状态标记。"""
        with RunContext(run_dir=run_dir) as ctx:
            ctx.failure("Test failure message")
            assert ctx.manifest["status"] == "FAILED"
            assert ctx.manifest["error_message"] == "Test failure message"

        assert not ctx.is_successful
        assert ctx.is_completed

    def test_exception_handling(self, run_dir: Path):
        """测试异常情况下的状态处理。"""
        with pytest.raises(ValueError):
            with RunContext(run_dir=run_dir) as ctx:
                raise ValueError("Test exception")

        # 异常情况下应标记为失败
        assert ctx.manifest["status"] == "FAILED"
        assert "ValueError" in ctx.manifest["error_message"]

    def test_finalized_state_protection(self, run_dir: Path):
        """测试完成状态下的操作保护。"""
        with RunContext(run_dir=run_dir) as ctx:
            pass

        # 完成后不应允许继续记录
        with pytest.raises(RuntimeError):
            ctx.log_parameter("test", "value")

        with pytest.raises(RuntimeError):
            ctx.log_metrics({"test": 1.0})

    def test_run_exists_error_on_creation(self, run_dir: Path):
        """测试在创建时如果运行已存在且overwrite=False，则抛出RunExistsError。"""
        # 1. 先创建一个运行
        with RunContext(run_dir=run_dir) as ctx:
            ctx.log_parameter("initial_run", True)
            original_run_id = ctx.run_id

        assert (run_dir / "manifest.json").exists()

        # 2. 尝试在同一目录再次创建运行，不使用overwrite
        with pytest.raises(RunExistsError) as exc_info:
            RunContext(run_dir=run_dir, overwrite=False)

        # 3. 验证异常信息
        assert str(run_dir) in str(exc_info.value)
        assert exc_info.value.run_dir == str(run_dir)
        assert exc_info.value.existing_run_id == original_run_id

    def test_overwrite_existing_run(self, run_dir: Path):
        """测试使用overwrite=True可以成功覆盖现有运行。"""
        # 1. 创建一个初始运行
        with RunContext(run_dir=run_dir, run_id="first-run") as ctx:
            ctx.log_parameter("run_version", 1)

        # 2. 使用overwrite=True创建新运行
        with RunContext(run_dir=run_dir, run_id="second-run", overwrite=True) as ctx:
            ctx.log_parameter("run_version", 2)

        # 3. 验证清单内容已被覆盖
        loaded_ctx = RunContext.load(run_dir)
        assert loaded_ctx.run_id == "second-run"
        assert loaded_ctx.parameters["run_version"] == 2


class TestRunContextLoading:
    """测试RunContext的加载功能。"""

    def test_load_completed_run(
        self,
        run_dir: Path,
        sample_config: Dict[str, Any],
        sample_parameters: Dict[str, Any]
    ):
        """测试加载已完成的运行。"""
        # 先创建一个完成的运行
        original_run_id = None
        with RunContext(run_dir=run_dir, config=sample_config) as ctx:
            original_run_id = ctx.run_id
            for key, value in sample_parameters.items():
                ctx.log_parameter(key, value)

        # 加载运行
        loaded_ctx = RunContext.load(run_dir)

        assert loaded_ctx.run_id == original_run_id
        assert loaded_ctx.is_completed
        assert loaded_ctx.is_successful
        assert loaded_ctx.parameters == sample_parameters

    def test_load_nonexistent_run(self, temp_dir: Path):
        """测试加载不存在的运行。"""
        nonexistent_dir = temp_dir / "nonexistent"

        with pytest.raises(RunNotFoundError) as exc_info:
            RunContext.load(nonexistent_dir)

        assert str(nonexistent_dir) in str(exc_info.value)
        assert exc_info.value.missing_component == "manifest"
