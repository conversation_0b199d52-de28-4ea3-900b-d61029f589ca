"""数据集表单写入服务。

实现数据集表单的完整写入功能，严格按照WFS v1.0规范。
包括结构指示区、曲线定义和数据区的写入和格式化。

Architecture
------------
层次/依赖: I/O层内部服务，被excel_writer调用
设计原则: WFS规范严格遵循、无状态函数、格式化支持
性能特征: 批量写入、内存优化、Excel格式化

Functions
---------
- write_dataset_sheet: 写入数据集表单数据和格式化
- write_dataset_structure_area: 写入数据集结构指示区
- write_dataset_curves_data: 写入曲线定义和数据区
- format_dataset_sheet: 格式化数据集表单

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》§3.2.3 - 数据集写入设计
- 《SCAPE_WFS_WP文件规范.md》§3 - 数据集表单规范
"""

from __future__ import annotations

import time
from typing import Any

import traceback
import openpyxl
import pandas as pd
import structlog
from openpyxl.utils.dataframe import dataframe_to_rows

from logwp.io.constants import WpXlsxKey
from logwp.io.exceptions import WpFileFormatError
from logwp.models.constants import WpDsType, WpDataType
from logwp.models.datasets.base import WpDepthIndexedDatasetBase
from logwp.models.exceptions import WpValidationError
from logwp.infra.exceptions import ErrorContext
from ..config import ExcelFormattingConfig
from .data_formatter import extract_excel_curve_order, format_dataframe_for_excel

logger = structlog.get_logger(__name__)

__all__ = [
    "write_dataset_sheet",
]


def write_dataset_sheet(
    worksheet: openpyxl.Worksheet,
    dataset: WpDepthIndexedDatasetBase,
    config: ExcelFormattingConfig
) -> None:
    """写入数据集表单数据和格式化。

    按照WFS规范的完整流程，使用append()方法以兼容只写模式：
    1. 写入元数据区（行1-7），包括数据集结构和曲线定义
    2. 写入数据区（行8起）

    Args:
        worksheet: Excel工作表对象
        dataset: 数据集对象
        config: 格式化配置

    Raises:
        WpFileFormatError: 数据集写入失败
    """
    start_time = time.time()

    try:
        logger.info(
            "开始写入数据集表单",
            dataset_name=dataset.name,
            dataset_type=dataset.dataset_type.value,
            df_shape=dataset.df.shape,
            curve_count=len(dataset.curve_metadata.curves)
        )

        # 1. 提取曲线写入顺序
        curve_order = extract_excel_curve_order(dataset.curve_metadata, dataset.dataset_type)

        # 2. 写入元数据区 (Rows 1-7)
        _write_dataset_metadata_section(worksheet, dataset, curve_order)

        # 3. 格式化DataFrame并写入数据区
        if curve_order:
            formatted_df = format_dataframe_for_excel(
                df=dataset.df,
                curve_order=curve_order,
                metadata=dataset.curve_metadata,
                config=config
            )
            # 4. 写入数据区（从第8行开始）
            _write_dataframe_to_worksheet(worksheet, formatted_df, start_row=8)

        processing_time = time.time() - start_time
        logger.info(
            "数据集表单写入完成",
            dataset_name=dataset.name,
            processing_time=round(processing_time, 3)
        )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(
            "数据集表单写入失败",
            dataset_name=dataset.name,
            error_type=type(e).__name__,
            error_message=str(e),
            processing_time=round(processing_time, 3)
        )
        raise WpFileFormatError(
            f"数据集表单写入失败: {e}",
            context=ErrorContext(
                operation="write_dataset_sheet",
                dataset_name=dataset.name,
                additional_info={"dataset_type": dataset.dataset_type.value}
            )
        ) from e


def _write_dataset_metadata_section(
    worksheet: openpyxl.Worksheet,
    dataset: WpDepthIndexedDatasetBase,
    curve_order: list[tuple[str, str]]
) -> None:
    """使用append()方法写入数据集的元数据区（行1-7），以兼容只写模式。"""
    # Row 1: Dataset Type
    row1 = [
        WpXlsxKey.HDR_DS_TYPE.value,
        dataset.dataset_type.value,
        WpXlsxKey.HDR_DS_INDEX_TYPE.value,
        WpXlsxKey.DATASET_INDEX_TYPE_DEPTH.value
    ]
    worksheet.append(row1)

    # Row 2: Sample Rate and Description
    row2 = [
        WpXlsxKey.HDR_SAMPLE_RATE.value,
        dataset.depth_sampling_rate or "",
        WpXlsxKey.HDR_DS_DESC.value,
        dataset.desc or ""
    ]
    worksheet.append(row2)

    # Rows 3-7: Curve Definitions
    names = [WpXlsxKey.ROW_TITLE_NAME.value]
    units = [WpXlsxKey.ROW_TITLE_UNIT.value]
    types = [WpXlsxKey.ROW_TITLE_TYPE.value]
    classes = [WpXlsxKey.ROW_TITLE_CLASS.value]
    comments = [WpXlsxKey.ROW_TITLE_COMMENT.value]

    for excel_curve_name, df_column_name in curve_order:
        curve = dataset.curve_metadata.get_curve_by_dataframe_name(df_column_name)
        if curve is None:
            logger.warning("未找到曲线元数据", dataframe_column=df_column_name)
            unit, data_type, curve_class, comment = "", WpDataType.STR.value, "", ""
        else:
            unit = curve.unit or ""
            data_type = curve.data_type.value
            curve_class = curve.curve_class.value if curve.curve_class else ""
            comment = curve.description or ""

        names.append(excel_curve_name)
        units.append(unit)
        types.append(data_type)
        classes.append(curve_class)
        comments.append(comment)

    worksheet.append(names)
    worksheet.append(units)
    worksheet.append(types)
    worksheet.append(classes)
    worksheet.append(comments)

    logger.debug("数据集元数据区（行1-7）写入完成", dataset_name=dataset.name)


def _write_dataframe_to_worksheet(
    worksheet: openpyxl.Worksheet,
    df: pd.DataFrame,
    start_row: int
) -> None:
    """将DataFrame高效地写入工作表。

    使用worksheet.append()方法进行批量行写入，性能远高于逐个单元格写入。
    同时，根据WFS规范，在数据区起始位置(A8)写入"Data"标识。

    Args:
        worksheet: Excel工作表对象
        df: 要写入的DataFrame
        start_row: 起始行号（1-based），此参数在此实现中主要用于日志记录，
                   因为append会自动从已有内容的下一行开始。
    """
    if df.empty:
        logger.debug("DataFrame为空，不写入数据区")
        return

    # 1. 使用dataframe_to_rows高效转换DataFrame
    rows = dataframe_to_rows(df, index=False, header=False)

    # 2. 准备并写入第一行数据（包含"Data"标识）
    try:
        first_row_values = next(rows)
        # 根据WFS规范，第一行数据在A列有"Data"标识
        first_full_row = [WpXlsxKey.ROW_TITLE_DATA.value] + list(first_row_values)
        worksheet.append(first_full_row)
    except StopIteration:
        # DataFrame不为空但没有数据行（例如，只有列名），这是一个边缘情况
        logger.debug("DataFrame中没有有效数据行，不写入数据区")
        return

    # 3. 准备并写入剩余行数据
    for row_values in rows:
        # 后续行在A列留空
        full_row = [""] + list(row_values)
        worksheet.append(full_row)

    logger.debug(
        "DataFrame写入完成",
        df_shape=df.shape,
        start_row=start_row,
        end_row=start_row + len(df) - 1
    )
