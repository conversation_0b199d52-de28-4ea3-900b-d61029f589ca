"""PCA外部API接口层。

负责参数验证和请求转发到内部服务层，提供用户友好的API接口。

Architecture
------------
层次/依赖: PCA外部API层，转发到internal服务层
设计原则: 参数验证、错误处理、API简化
性能特征: 轻量级转发、快速验证、详细错误信息

Notes
-----
本模块遵循SCAPE项目的内部服务层模式，所有核心业务逻辑
都在internal/目录下实现，此处只做参数验证和请求转发。
"""

from __future__ import annotations

from typing import TYPE_CHECKING
from pathlib import Path

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from logwp.infra.compute import ComputeEngine
    from .internal.model import PCAParameters
    from .internal.visualization import SaveConfig

# =============================================================================
# 便利函数 - 任务3.3新增
# =============================================================================

def analyze_pca_complete(
    dataset: WpDepthIndexedDatasetBase,
    n_components: int | None = None,
    curve_name: str = "PC",
    compute_engine: ComputeEngine | None = None,
    standardize: bool = True
) -> tuple[PCAParameters, WpDepthIndexedDatasetBase, WpDepthIndexedDatasetBase]:
    """完整的PCA分析流程。

    一键完成数据预处理、PCA计算的完整流程，适合快速分析。

    Architecture
    ------------
    层次/依赖: PCA API层，组合多个内部服务
    设计原则: 一键分析、流程简化、结果完整
    性能特征: 端到端处理、自动优化、错误恢复

    Args:
        dataset: 原始测井数据集
        n_components: 主成分数量，None表示自动选择
        curve_name: PCA曲线基础名称
        compute_engine: 计算引擎
        standardize: 是否进行数据标准化

    Returns:
        tuple: (PCA模型, 预处理数据集, PCA结果数据集)

    Raises:
        WpPcaDataError: 数据验证失败
        WpPcaComputationError: PCA计算异常

    Examples:
        >>> pca_model, preprocessed, pca_result = analyze_pca_complete(dataset)
        >>> # 一键完成完整PCA分析

    References:
        《SCAPE_DDS_logwp_extras_pca.md》- 完整PCA分析流程
    """
    # 参数验证
    if dataset is None:
        raise ValueError("dataset参数不能为None")

    if dataset.df.empty:
        from .exceptions import create_data_error
        raise create_data_error(
            "数据集为空，无法进行PCA分析",
            dataset_name=dataset.name
        )

    try:
        # 1. 数据预处理（如果需要）
        if standardize:
            preprocessed_dataset = preprocess_data(dataset, compute_engine)
        else:
            preprocessed_dataset = dataset

        # 2. PCA计算
        pca_model, pca_dataset = apply_pca_full(
            preprocessed_dataset, n_components, curve_name, compute_engine
        )

        return pca_model, preprocessed_dataset, pca_dataset

    except Exception as e:
        # 重新抛出已知的PCA异常
        if hasattr(e, '__class__') and 'WpPca' in e.__class__.__name__:
            raise
        else:
            from .exceptions import create_computation_error
            raise create_computation_error(
                f"完整PCA分析过程中发生错误: {str(e)}",
                cause=e
            )


def get_optimal_n_components(
    pca_model: PCAParameters,
    variance_threshold: float = 0.95
) -> int:
    """根据方差解释比例确定最优主成分数量。

    Architecture
    ------------
    层次/依赖: PCA API层，决策支持工具
    设计原则: 自动优化、阈值可配、科学决策
    性能特征: 快速计算、智能推荐

    Args:
        pca_model: PCA模型参数
        variance_threshold: 累积方差解释比例阈值，默认95%

    Returns:
        推荐的主成分数量

    Examples:
        >>> optimal_n = get_optimal_n_components(pca_model, 0.90)
        >>> # 获取解释90%方差所需的主成分数量
    """
    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    if not (0 < variance_threshold <= 1):
        raise ValueError(f"variance_threshold必须在(0,1]范围内，当前值: {variance_threshold}")

    # 计算累积方差解释比例
    variance_ratio, cumulative_ratio = explained_variance(pca_model)

    # 找到第一个超过阈值的主成分
    for i, cum_var in enumerate(cumulative_ratio):
        if cum_var >= variance_threshold:
            return i + 1

    # 如果所有主成分都不能达到阈值，返回全部主成分
    return len(variance_ratio)


def create_pca_summary(
    pca_model: PCAParameters,
    pca_dataset: WpDepthIndexedDatasetBase | None = None
) -> dict[str, any]:
    """创建PCA分析结果摘要。

    Architecture
    ------------
    层次/依赖: PCA API层，结果汇总工具
    设计原则: 信息完整、结构化、用户友好
    性能特征: 快速汇总、格式化输出

    Args:
        pca_model: PCA模型参数
        pca_dataset: PCA结果数据集（可选）

    Returns:
        包含PCA分析摘要的字典

    Examples:
        >>> summary = create_pca_summary(pca_model, pca_dataset)
        >>> print(f"总方差解释: {summary['total_variance_explained']:.1%}")
    """
    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    # 计算方差信息
    variance_ratio, cumulative_ratio = explained_variance(pca_model)

    # 基本信息
    summary = {
        "model_info": {
            "n_components": pca_model["n_components"],
            "n_features": pca_model["n_features"],
            "pca_curve_name": pca_model["pca_curve_name"],
            "feature_names": pca_model["feature_names"]
        },
        "variance_analysis": {
            "individual_variance": variance_ratio,
            "cumulative_variance": cumulative_ratio,
            "total_variance_explained": cumulative_ratio[-1],
            "top_5_components_variance": cumulative_ratio[min(4, len(cumulative_ratio)-1)]
        },
        "recommendations": {
            "optimal_n_95": get_optimal_n_components(pca_model, 0.95),
            "optimal_n_90": get_optimal_n_components(pca_model, 0.90),
            "optimal_n_85": get_optimal_n_components(pca_model, 0.85)
        }
    }

    # 数据集信息（如果提供）
    if pca_dataset is not None:
        summary["dataset_info"] = {
            "dataset_name": pca_dataset.name,
            "n_samples": len(pca_dataset.df),
            "dataset_type": pca_dataset.get_dataset_type_name()
        }

    return summary


# =============================================================================
# 核心功能函数
# =============================================================================

def preprocess_data(
    dataset: WpDepthIndexedDatasetBase,
    compute_engine: ComputeEngine | None = None
) -> WpDepthIndexedDatasetBase:
    """数据预处理：标准化和验证。

    对输入数据进行标准化处理，用户需要保证输入数据集的规范性，
    方法内部只进行必要的数值型检查。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.preprocess
    设计原则: 参数验证、智能曲线过滤、GPU加速
    性能特征: GPU/CPU自动切换、内存优化

    Args:
        dataset: 用户预处理的测井数据集
        compute_engine: 计算引擎，支持GPU加速和CPU回退

    Returns:
        标准化后的数据集，保持原有曲线元数据结构

    Raises:
        WpPcaDataError: 数据验证失败
        WpPcaComputationError: 计算过程异常

    References:
        《SCAPE_DDS_logwp_extras_pca.md》§ 3.1 preprocess_data方法
    """
    # 参数验证
    if dataset is None:
        raise ValueError("dataset参数不能为None")

    if dataset.df.empty:
        from .exceptions import create_data_error
        raise create_data_error(
            "数据集为空，无法进行PCA预处理",
            dataset_name=dataset.name
        )

    # 转发到内部服务层
    from .internal.preprocess import preprocess_dataset
    return preprocess_dataset(dataset, compute_engine)


def apply_pca_full(
    dataset: WpDepthIndexedDatasetBase,
    n_components: int | None = None,
    curve_name: str = "PC",
    compute_engine: ComputeEngine | None = None
) -> tuple[PCAParameters, WpDepthIndexedDatasetBase]:
    """执行PCA计算并返回所有主成分。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.compute
    设计原则: GPU加速、二维组合曲线、方差解释
    性能特征: 大规模数据支持、自动回退机制

    Args:
        dataset: 经过预处理的数据集
        n_components: 指定要保留的主成分数量，None表示返回所有主成分
        curve_name: 二维组合曲线的基础名称，默认为"PC"
        compute_engine: 计算引擎，支持GPU加速

    Returns:
        tuple: (PCA模型参数, 包含所有主成分的降维数据)

    Raises:
        WpPcaComputationError: PCA计算异常
        WpPcaModelError: 模型构建异常

    References:
        《SCAPE_DDS_logwp_extras_pca.md》§ 3.2 apply_pca_full方法
    """
    # 参数验证
    if dataset is None:
        raise ValueError("dataset参数不能为None")

    if dataset.df.empty:
        from .exceptions import create_data_error
        raise create_data_error(
            "数据集为空，无法进行PCA计算",
            dataset_name=dataset.name
        )

    if n_components is not None and n_components <= 0:
        raise ValueError(f"n_components必须大于0，当前值: {n_components}")

    if not curve_name or not isinstance(curve_name, str):
        raise ValueError("curve_name必须是非空字符串")

    # 转发到内部服务层
    from .internal.compute import compute_pca_full
    return compute_pca_full(dataset, n_components, curve_name, compute_engine)


def select_and_apply_pca(
    pca_model: PCAParameters,
    pca_data_all: WpDepthIndexedDatasetBase,
    n_components: int
) -> WpDepthIndexedDatasetBase:
    """从所有主成分中选择用户指定数量的主成分。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.model
    设计原则: 主成分选择、元数据维护、数据一致性
    性能特征: 轻量级操作、快速选择

    Args:
        pca_model: PCA模型，包含所有主成分的矩阵
        pca_data_all: 包含所有主成分的降维数据
        n_components: 用户指定要保留的主成分数量

    Returns:
        降维后的数据，仅包含前n_components个主成分

    Raises:
        WpPcaModelError: 主成分选择异常

    References:
        《SCAPE_DDS_logwp_extras_pca.md》§ 3.3 select_and_apply_pca方法
    """
    # 参数验证
    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    if pca_data_all is None:
        raise ValueError("pca_data_all参数不能为None")

    if n_components <= 0:
        raise ValueError(f"n_components必须大于0，当前值: {n_components}")

    if pca_data_all.df.empty:
        from .exceptions import create_data_error
        raise create_data_error(
            "PCA数据集为空，无法进行主成分选择",
            dataset_name=pca_data_all.name
        )

    # 转发到内部服务层
    from .internal.model import select_principal_components
    return select_principal_components(pca_model, pca_data_all, n_components)


def explained_variance(pca_model: PCAParameters) -> tuple[list[float], list[float]]:
    """返回每个主成分的方差解释比例。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.model
    设计原则: 方差分析、决策支持
    性能特征: 快速计算、轻量级操作

    Args:
        pca_model: PCA模型参数，包含方差解释比例信息

    Returns:
        tuple: (方差解释比例数组, 累积方差解释比例数组)

    References:
        《SCAPE_DDS_logwp_extras_pca.md》§ 3.4 explained_variance方法
    """
    # 参数验证
    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    # 转发到内部服务层
    from .internal.model import compute_explained_variance
    variance_ratio, cumulative_ratio = compute_explained_variance(pca_model)

    # 转换为Python列表返回（更用户友好）
    return variance_ratio.tolist(), cumulative_ratio.tolist()


def inverse_transform(
    dataset_pca: WpDepthIndexedDatasetBase,
    pca_model: PCAParameters,
    compute_engine: ComputeEngine | None = None
) -> WpDepthIndexedDatasetBase:
    """将降维后的数据通过PCA模型恢复回原始数据空间。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.compute
    设计原则: 数据重构、反标准化、GPU加速
    性能特征: 大规模数据支持、内存优化

    Args:
        dataset_pca: 降维后的PCA数据
        pca_model: PCA模型参数，包含逆变换所需的所有信息
        compute_engine: 计算引擎，支持GPU加速

    Returns:
        重构的原始数据空间数据

    Raises:
        WpPcaComputationError: 逆变换计算异常
        WpPcaModelError: 模型参数异常

    References:
        《SCAPE_DDS_logwp_extras_pca.md》§ 3.5 inverse_transform方法
    """
    # 参数验证
    if dataset_pca is None:
        raise ValueError("dataset_pca参数不能为None")

    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    if dataset_pca.df.empty:
        from .exceptions import create_data_error
        raise create_data_error(
            "PCA数据集为空，无法进行逆变换",
            dataset_name=dataset_pca.name
        )

    # 转发到内部服务层
    from .internal.compute import compute_inverse_transform_dataset
    return compute_inverse_transform_dataset(dataset_pca, pca_model, compute_engine)


def save_pca_model(
    pca_model: PCAParameters,
    filepath: str | Path,
    format: str = "json"
) -> None:
    """保存PCA模型到文件。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.persistence
    设计原则: 多格式支持、版本兼容、完整性验证
    性能特征: 高效序列化、压缩存储

    Args:
        pca_model: PCA模型参数
        filepath: 保存文件路径
        format: 保存格式，支持"json"、"pickle"、"npz"

    Raises:
        WpPcaModelError: 模型保存异常

    References:
        《SCAPE_DDS_logwp_extras_pca.md》§ 5.1 save_pca_model方法
    """
    # 参数验证
    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    if not filepath:
        raise ValueError("filepath参数不能为空")

    valid_formats = {"json", "pickle", "npz"}
    if format not in valid_formats:
        raise ValueError(f"不支持的格式: {format}，支持的格式: {valid_formats}")

    # 转发到内部服务层
    from .internal.persistence import save_model_to_file
    save_model_to_file(pca_model, filepath, format)


def load_pca_model(
    filepath: str | Path,
    format: str = "auto"
) -> PCAParameters:
    """从文件加载PCA模型。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.persistence
    设计原则: 自动格式检测、版本兼容、完整性验证
    性能特征: 快速加载、内存优化

    Args:
        filepath: 模型文件路径
        format: 文件格式，"auto"表示自动检测

    Returns:
        加载的PCA模型参数

    Raises:
        WpPcaModelError: 模型加载异常

    References:
        《SCAPE_DDS_logwp_extras_pca.md》§ 5.2 load_pca_model方法
    """
    # 参数验证
    if not filepath:
        raise ValueError("filepath参数不能为空")

    from pathlib import Path
    filepath_obj = Path(filepath)
    if not filepath_obj.exists():
        from .exceptions import create_data_error
        raise create_data_error(f"模型文件不存在: {filepath}")

    valid_formats = {"auto", "json", "pickle", "npz"}
    if format not in valid_formats:
        raise ValueError(f"不支持的格式: {format}，支持的格式: {valid_formats}")

    # 转发到内部服务层
    from .internal.persistence import load_model_from_file
    return load_model_from_file(filepath, format)


# =============================================================================
# 可视化函数 - 任务3.2新增
# =============================================================================

def plot_variance_explained(
    pca_model: PCAParameters,
    save_path: str | Path | None = None,
    **save_kwargs
) -> None:
    """绘制主成分方差贡献图。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.visualization
    设计原则: 科学可视化、用户友好、多格式支持
    性能特征: 高分辨率、快速渲染、美观布局

    Args:
        pca_model: PCA模型参数，包含方差解释比例信息
        save_path: 图像保存路径，None表示显示图像
        **save_kwargs: 图像保存配置参数

    Raises:
        WpPcaVisualizationError: 可视化异常

    Examples:
        >>> plot_variance_explained(pca_model)  # 显示图像
        >>> plot_variance_explained(pca_model, "variance.png", dpi=300)  # 保存图像
    """
    # 参数验证
    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    # 准备保存配置
    save_config = None
    if save_path:
        from .internal.visualization import SaveConfig
        save_config = SaveConfig(filename=save_path, **save_kwargs)

    # 转发到内部服务层
    from .internal.visualization import plot_variance_explained as _plot_variance_explained
    _plot_variance_explained(pca_model, save_config)


def plot_pca_scatter(
    pca_dataset: WpDepthIndexedDatasetBase,
    n_components: int = 2,
    save_path: str | Path | None = None,
    **save_kwargs
) -> None:
    """绘制PCA降维结果的散点图。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.visualization
    设计原则: 直观展示、交互友好、信息丰富
    性能特征: 大数据支持、快速渲染、美观布局

    Args:
        pca_dataset: 降维后的PCA数据集
        n_components: 要展示的主成分数量（2或3）
        save_path: 图像保存路径，None表示显示图像
        **save_kwargs: 图像保存配置参数

    Raises:
        WpPcaVisualizationError: 可视化异常

    Examples:
        >>> plot_pca_scatter(pca_dataset)  # 2D散点图
        >>> plot_pca_scatter(pca_dataset, n_components=3)  # 3D散点图
        >>> plot_pca_scatter(pca_dataset, save_path="scatter.png")  # 保存图像
    """
    # 参数验证
    if pca_dataset is None:
        raise ValueError("pca_dataset参数不能为None")

    if n_components not in [2, 3]:
        raise ValueError(f"n_components必须是2或3，当前值: {n_components}")

    # 准备保存配置
    save_config = None
    if save_path:
        from .internal.visualization import SaveConfig
        save_config = SaveConfig(filename=save_path, **save_kwargs)

    # 转发到内部服务层
    from .internal.visualization import plot_pca_scatter as _plot_pca_scatter
    _plot_pca_scatter(pca_dataset, n_components, save_config)


def plot_pca_heatmap(
    pca_model: PCAParameters,
    save_path: str | Path | None = None,
    **save_kwargs
) -> None:
    """绘制PCA主成分载荷热图。

    Architecture
    ------------
    层次/依赖: PCA API层，转发到internal.visualization
    设计原则: 信息密集、颜色映射、特征解释
    性能特征: 高分辨率、清晰标注、美观配色

    Args:
        pca_model: PCA模型参数，包含主成分矩阵
        save_path: 图像保存路径，None表示显示图像
        **save_kwargs: 图像保存配置参数

    Raises:
        WpPcaVisualizationError: 可视化异常

    Examples:
        >>> plot_pca_heatmap(pca_model)  # 显示热图
        >>> plot_pca_heatmap(pca_model, "heatmap.png", dpi=300)  # 保存热图
    """
    # 参数验证
    if pca_model is None:
        raise ValueError("pca_model参数不能为None")

    # 准备保存配置
    save_config = None
    if save_path:
        from .internal.visualization import SaveConfig
        save_config = SaveConfig(filename=save_path, **save_kwargs)

    # 转发到内部服务层
    from .internal.visualization import plot_pca_heatmap as _plot_pca_heatmap
    _plot_pca_heatmap(pca_model, save_config)
