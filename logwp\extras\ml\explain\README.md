# Model Explainability (`logwp.extras.ml.explain`)

一个提供用于机器学习模型可解释性（XAI, eXplainable AI）工具的包。

在地球科学等高风险领域，理解模型的决策依据与评估其预测准确性同等重要。此包旨在为流行的可解释性库（如 `SHAP`）提供智能封装器，使其能与 `logwp` 的数据结构和模型无缝协作，帮助我们打开“黑箱”。

## 核心特性

- **智能封装**: 自动为不同类型的模型选择最优的SHAP解释器。
- **统一的API**: 提供一致的、高级的函数来计算和可视化特征贡献度。
- **经典与现代结合**: 同时支持经典的特征重要性方法、先进的SHAP值分析和全局的偏依赖图。

## 可用工具

### 1. SHAP 值计算与可视化 (`shap_wrapper.py`)

#### `calculate_shap_values(model, X, ...)`

一个智能函数，用于为给定的模型和数据集计算SHAP值。它会自动检测模型类型（如树模型）并选择最高效的解释器。

```python
from sklearn.ensemble import RandomForestRegressor
import pandas as pd
from logwp.extras.ml.explain import calculate_shap_values

# 1. 准备模型和数据
X_train = pd.DataFrame(np.random.rand(100, 5), columns=[f'F_{i}' for i in range(5)])
y_train = np.random.rand(100)
model = RandomForestRegressor(n_estimators=10).fit(X_train, y_train)

# 2. 计算SHAP值 (自动选择TreeExplainer)
shap_values = calculate_shap_values(model, X_train)
```

#### `plot_shap_summary(shap_values, X, ...)`

绘制一个SHAP摘要图（通常是蜜蜂蜂群图），这是可视化特征重要性和影响的最强大工具之一。

**解读**:
- **特征排序**: 纵轴按特征的全局重要性（平均绝对SHAP值）从上到下排序。
- **影响方向**: 横轴是SHAP值，正值表示该特征将预测推向更高，负值则推向更低。
- **特征值**: 点的颜色表示特征值本身的大小（红色为高，蓝色为低）。

```python
import matplotlib.pyplot as plt
from logwp.extras.ml.explain import plot_shap_summary

# 假设 shap_values 和 X_train 已计算得出
fig, ax = plt.subplots()
plot_shap_summary(shap_values, X_train, ax=ax)
plt.show()
```

### 2. 特征重要性 (`feature_importance.py`)

#### `plot_feature_importance(model_or_shap_values, X, ...)`

一个通用的函数，用于绘制特征重要性的条形图。它支持两种模式：
- **`classic`**: 直接从树模型（如RandomForest）的 `feature_importances_` 属性获取重要性。
- **`shap`**: 通过计算平均绝对SHAP值来确定重要性，这通常被认为是更可靠的方法。

```python
import matplotlib.pyplot as plt
from logwp.extras.ml.explain import plot_feature_importance

# 假设 model, shap_values, X_train 已准备好

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

# 模式1: 从模型直接绘制 (method='auto' 或 'classic')
plot_feature_importance(
    model,
    X_train,
    ax=ax1,
    top_n=10
)
ax1.set_title("Classic Feature Importance")

# 模式2: 从SHAP值绘制 (method='auto' 或 'shap')
plot_feature_importance(
    shap_values,
    X_train,
    ax=ax2,
    top_n=10
)
ax2.set_title("SHAP-based Feature Importance")

plt.tight_layout()
plt.show()
```

### 3. 偏依赖图 (PDP) (`pdp.py`)

#### `plot_pdp(model, X, features, ...)`

绘制一个或两个特征的偏依赖图。PDP展示了特征对模型预测的边际效应，帮助理解特征与目标之间的全局关系。

**适用场景**: 验证模型是否学习到了符合物理或地质规律的趋势。例如，验证模型是否学到了“孔隙度越高，渗透率越高”这一基本规律。

```python
from sklearn.ensemble import RandomForestRegressor
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from logwp.extras.ml.explain import plot_pdp

# 1. 准备模型和数据
X_train = pd.DataFrame(np.random.rand(200, 5), columns=['GR', 'NPHI', 'RHOB', 'DT', 'RT'])
y_train = X_train['GR'] * 0.5 - X_train['NPHI'] * 2 + np.random.rand(200)
model = RandomForestRegressor(n_estimators=20, random_state=42).fit(X_train, y_train)

# 2. 绘制单个特征的PDP
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
plot_pdp(model, X_train, features='NPHI', ax=ax1)
ax1.set_ylabel("Partial Dependence")

# 3. 绘制两个特征交互的PDP
plot_pdp(model, X_train, features=['GR', 'RHOB'], ax=ax2)

plt.show()
```
