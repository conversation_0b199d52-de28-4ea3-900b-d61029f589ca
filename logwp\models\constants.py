from __future__ import annotations

"""logwp.constants
~~~~~~~~~~~~~~~~~~~
现代化项目 *logwp* 的统一常量定义。

本文件包含现代化技术栈相关的常量定义，支持：
- GPU计算配置常量
- 异步I/O操作常量
- 现代化工具配置常量
- 环境变量Feature Flag
- 性能基准和限制常量

注意：
1. 本文件 **不得** 导入任何第三方库，以免在安装阶段触发 ImportError。
2. 请遵守 CursorRules·CS-5：常量枚举类名采用包规范化前缀 `Wp`。
3. 若需扩展，请保持向后兼容，严禁修改已发布成员值（CT-6）。
4. 支持现代化技术栈：GPU计算、异步I/O、结构化日志等。
"""

from enum import Enum

__all__ = [
    # 业务模型相关（格式无关）
    "WpDsType",
    "WpDsIndexType",
    "WpKeywords",
    "WpPredefinedAttributes",
    # API响应相关
    "WpApiKeys",
    "WpIOResponseKeys",
    # 系统配置相关
    "WpSystemDefaults",
    "WpPerformanceLimits",
    "WpGpuDefaults",
    "WpAsyncIODefaults",
    # 环境变量相关
    "WpEnvironmentVars",
    "WpFeatureFlags",
    # 现代化工具相关
    "WpModernToolDefaults",
    # WFS v1.0 新增
    "WpValidationRule",
    "WpErrorCode",
    # CIIA架构支持
    "WpNormalizationMode",
    # 对象模型层次常量
    "WpDataType",
    "WpAttributeScope",
    "WpStandardColumn",
    "WpProcessingStage",
    "WpComputeBackend",

    # WFS规范支持（格式无关）
    "WpExtAttributeCategory",
    "WpCurve2DPattern",

    # 曲线基本属性相关（格式无关）
    "WpDataType",
    "WpCurveCategory",
    "WpCurveDimension",
    "WpCurveClass",
    "WpDepthRole",

    # 统计信息相关
    "WpStatisticsKeys",

    # 描述和日志消息常量
    "WpCurveDescription",
    "WpLogMessage",

    # 注意：WpXlsxKey和WpFileFormat仅限I/O层使用，不在模块级导出
]


# WpXlsxKey 已移动到 logwp/io/constants.py
# 请使用: from logwp.io.constants import WpXlsxKey


class WpDsType(str, Enum):
    """数据集类型枚举，与WFS规范数据集类型标识对应。

    **使用范围**：logwp包内所有层次，定义数据集类型标识
    **架构位置**：核心数据模型层，跨层共享常量
    **依赖关系**：被models、datasets、io等模块广泛使用
    **设计原则**：类型安全、枚举完整性、向后兼容、WFS规范兼容
    **性能特征**：编译时检查、快速类型判断

    **命名映射说明**：
    - CONTINUOUS: 对应WpContinuousDataset（连续型）
    - POINT: 对应WpDiscreteDataset（离散型，WFS规范中称为Point类型）
    - INTERVAL: 对应WpIntervalDataset（区间型）

    基于 WFS A.3.1节 数据集类型标识。

    Examples:
        >>> # 数据集类型判断
        >>> if dataset.dataset_type == WpDsType.CONTINUOUS:
        ...     print("连续数据集")
        >>>
        >>> # 类型验证
        >>> valid_types = WpDsType.all_types()
        >>> assert "Continuous" in valid_types

    References:
        《SCAPE_WFS_WP文件规范》A.3.1节 - 数据集类型标识
    """

    CONTINUOUS = "Continuous"
    POINT = "Point"
    INTERVAL = "Interval"
    TIMESERIES = "TimeSeries"  # 规划中扩展，向后兼容

    @classmethod
    def all_types(cls) -> set[str]:
        """返回所有数据集类型。"""
        return {
            cls.CONTINUOUS.value,
            cls.POINT.value,
            cls.INTERVAL.value,
            cls.TIMESERIES.value,
        }

    @classmethod
    def single_depth_types(cls) -> set[str]:
        """返回单深度索引数据集类型。"""
        return {
            cls.CONTINUOUS.value,
            cls.POINT.value,
        }


class WpDsIndexType(str, Enum):
    """数据集索引类型枚举。

    定义数据集的索引类型，具有测井领域含义。
    目前只实现深度索引，未来可扩展时间索引等其他类型。

    **使用范围**：logwp包内所有层次，定义数据集索引类型
    **架构位置**：核心数据模型层，跨层共享常量
    **依赖关系**：被models、datasets、io等模块使用
    **设计原则**：类型安全、枚举完整性、向后兼容、扩展性

    Examples:
        >>> # 数据集索引类型判断
        >>> if dataset.get_index_type() == WpDsIndexType.DEPTH:
        ...     print("深度索引数据集")
        >>>
        >>> # 类型验证
        >>> assert WpDsIndexType.DEPTH == "Depth"

    References:
        《SCAPE_WFS_WP文件规范》§3 - 数据集类型定义
    """

    DEPTH = "Depth"  # 深度索引类型


class WpKeywords(str, Enum):
    """常用曲线/字段关键字（节选）。

    **使用范围**：logwp包models、datasets、algorithms层通用引用
    **架构位置**：领域模型层，文件格式无关的业务常量
    **依赖关系**：被models、datasets、scape算法层使用，不依赖I/O层
    **设计原则**：业务语义、格式解耦、避免硬编码
    **性能特征**：编译时检查、业务语义清晰

    *仅* 供模型/算法层通用引用，避免散落硬编码；完整业务线曲线枚举请在
    相关横切包各自维护专属枚举。

    Examples:
        >>> # 业务逻辑中使用
        >>> if WpKeywords.PHIT_NMR in dataframe.columns:
        ...     process_nmr_porosity(dataframe[WpKeywords.PHIT_NMR])
        >>>
        >>> # 算法中引用
        >>> target_column = WpKeywords.K_LABEL

    References:
        《SCAPE_MS_方法说明书》§1.3 - 曲线名称和含义
    """

    MD = "MD"  # 深度索引
    WELL_NO = "WELL_NO"  # 井名
    PHIT_NMR = "PHIT_NMR"  # NMR 总孔隙度
    T2_P50 = "T2_P50"  # 累积 T2 谱 50% 分位
    DPHIT_NMR = "DPHIT_NMR"  # NMR 孔隙度差值
    K_LABEL = "K_LABEL"  # 渗透率标签


class WpPredefinedAttributes(str, Enum):
    """预定义属性名称枚举。

    **使用范围**：logwp包models层，避免预定义属性名的硬编码
    **架构位置**：预定义属性标准名称定义
    **依赖关系**：被models层使用，遵循CCG CT-2规范
    **设计原则**：避免硬编码、标准化命名、支持多种变体
    **性能特征**：编译时常量、类型安全

    Examples:
        >>> # 避免硬编码字符串
        >>> attr_names = WpPredefinedAttributes.t2_axis_variants()
        >>> for attr_name in attr_names:
        ...     t2_data = head.get_attribute(attr_name)

    References:
        《SCAPE_CCG_编码与通用规范》CT-2 - 禁止硬编码字符串
    """

    # T2轴相关属性名
    T2_AXIS_STANDARD = "T2_AXIS"  # 标准T2轴属性名
    T2_AXIS_LOWERCASE = "t2_axis"  # 小写变体
    T2_AXIS_MIXED = "T2_Axis"  # 混合大小写变体
    T2_AXIS_WITH_PREFIX = "NMR_T2_AXIS"  # 带NMR前缀变体

    @classmethod
    def t2_axis_variants(cls) -> list[str]:
        """返回所有T2轴属性名变体。

        Returns:
            list[str]: T2轴属性名的所有可能变体
        """
        return [
            cls.T2_AXIS_STANDARD.value,
            cls.T2_AXIS_LOWERCASE.value,
            cls.T2_AXIS_MIXED.value,
            cls.T2_AXIS_WITH_PREFIX.value
        ]


class WpStatisticsKeys(str, Enum):
    """统计信息键名枚举。

    **使用范围**：logwp包datasets、models层，定义统计信息字典的标准化键名
    **架构位置**：数据模型层统计接口，与具体文件格式无关
    **依赖关系**：被datasets、models使用，替代硬编码字符串
    **设计原则**：统一命名、类型安全、格式解耦、扩展性
    **性能特征**：编译时检查、运行时优化、JSON友好

    该枚举提供格式无关的统计信息键名，确保数据统计结果的一致性和类型安全。

    Examples:
        >>> # 曲线统计信息
        >>> stats = {
        ...     WpStatisticsKeys.COUNT: 1000,
        ...     WpStatisticsKeys.MEAN: 45.2,
        ...     WpStatisticsKeys.STD: 12.8,
        ...     WpStatisticsKeys.MIN: 15.0,
        ...     WpStatisticsKeys.MAX: 85.3
        ... }
        >>>
        >>> # 数据质量统计
        >>> quality_stats = {
        ...     WpStatisticsKeys.NULL_COUNT: 25,
        ...     WpStatisticsKeys.NULL_PERCENTAGE: 2.5,
        ...     WpStatisticsKeys.VALID_COUNT: 975
        ... }

    References:
        《SCAPE_DDS_详细设计_logwp》§3.2 - 数据集统计设计
        《SCAPE_CCG_编码与通用规范》§9.2 - 常量使用规范
    """

    # 基础统计量
    COUNT = "count"                    # 有效数据点数量
    MEAN = "mean"                      # 算术平均值
    STD = "std"                        # 标准差
    MIN = "min"                        # 最小值
    MAX = "max"                        # 最大值
    MEDIAN = "median"                  # 中位数

    # 数据质量统计
    NULL_COUNT = "null_count"          # 空值数量
    NULL_PERCENTAGE = "null_percentage" # 空值百分比
    VALID_COUNT = "valid_count"        # 有效数据数量
    TOTAL_COUNT = "total_count"        # 总数据数量

    # 扩展统计量
    VARIANCE = "variance"              # 方差
    SKEWNESS = "skewness"              # 偏度
    KURTOSIS = "kurtosis"              # 峰度
    PERCENTILE_25 = "percentile_25"    # 25%分位数
    PERCENTILE_75 = "percentile_75"    # 75%分位数
    RANGE = "range"                    # 极差（max - min）

    # 分布统计
    UNIQUE_COUNT = "unique_count"      # 唯一值数量
    MODE = "mode"                      # 众数
    MODE_COUNT = "mode_count"          # 众数出现次数

    # 属性管理统计
    TOTAL_ATTRIBUTES = "total_attributes"    # 总属性数量
    CATEGORY_COUNTS = "category_counts"      # 分类计数
    UNIQUE_NAMES = "unique_names"            # 唯一名称数量
    HAS_VERSION = "has_version"              # 是否包含版本属性
    SCOPED_ATTRIBUTES = "scoped_attributes"  # 作用域属性数量

    # ErrorContext additional_info 常用键名
    MIN_DEPTH = "min_depth"                  # 最小深度值
    MAX_DEPTH = "max_depth"                  # 最大深度值
    AVAILABLE_COLUMNS = "available_columns"  # 可用列名列表

    # ErrorContext operation 常用操作名称
    OPERATION_FILTER_DEPTH_RANGE = "filter_depth_range"           # 深度范围过滤
    OPERATION_VALIDATE_DATASET = "validate_dataset"               # 数据集验证
    OPERATION_VALIDATE_DISCRETE_DATASET = "validate_discrete_dataset"  # 离散型数据集验证
    OPERATION_VALIDATE_DEPTH_DATA = "validate_depth_data"         # 深度数据验证
    OPERATION_GET_CURVE_STATISTICS = "get_curve_statistics"       # 获取曲线统计
    OPERATION_GET_ATTRIBUTE_VALUE = "get_attribute_value"         # 获取属性值
    OPERATION_ATTRIBUTE_LOOKUP = "attribute_lookup"               # 属性查找
    OPERATION_VALIDATE_CURVE_ATTRIBUTES = "validate_curve_attributes"  # 曲线属性验证
    OPERATION_ADD_CURVE = "add_curve"                             # 添加曲线
    OPERATION_UPDATE_CURVE = "update_curve"                       # 更新曲线
    OPERATION_REMOVE_CURVE = "remove_curve"                       # 移除曲线
    OPERATION_REMOVE_CURVE_ELEMENT = "remove_curve_element"       # 删除曲线元素
    OPERATION_SPLIT_2D_CURVE = "split_2d_curve"                  # 拆分二维组合曲线
    OPERATION_CLEAR_CURVES = "clear_curves"                       # 清空曲线
    OPERATION_EXPAND_CURVE_NAMES = "expand_curve_names"           # 展开曲线名称
    OPERATION_EXTRACT_METADATA = "extract_metadata"               # 提取曲线元数据

    # ErrorContext additional_info reason 常用原因
    REASON_DUPLICATE_CURVE = "duplicate_curve"                    # 重复曲线
    REASON_CURVE_NOT_FOUND = "curve_not_found"                   # 曲线不存在

    # 属性查找路径常量
    LOOKUP_PATH_VERSION = "VERSION_LOOKUP"                        # 版本属性查找
    LOOKUP_PATH_CURVE_SCOPE = "CURVE_SCOPE_LOOKUP"               # 曲线作用域查找
    LOOKUP_PATH_WELL_SCOPE = "WELL_SCOPE_LOOKUP"                 # 井作用域查找
    LOOKUP_PATH_DATASET_SCOPE = "DATASET_SCOPE_LOOKUP"           # 数据集作用域查找
    LOOKUP_PATH_WORKAREA = "WORKAREA_LOOKUP"                     # 工区查找
    LOOKUP_PATH_OTHER_EXACT = "OTHER_EXACT_MATCH"                # 其它属性精确匹配
    LOOKUP_PATH_CURVE_GLOBAL = "CURVE_GLOBAL"                    # 曲线全局查找
    LOOKUP_PATH_CURVE_DATASET = "CURVE_DATASET"                  # 曲线数据集查找
    LOOKUP_PATH_CURVE_WELL = "CURVE_WELL"                        # 曲线井查找
    LOOKUP_PATH_CURVE_FULL = "CURVE_FULL"                        # 曲线完整查找
    LOOKUP_PATH_WELL_GLOBAL = "WELL_GLOBAL"                      # 井全局查找
    LOOKUP_PATH_WELL_DATASET = "WELL_DATASET"                    # 井数据集查找
    LOOKUP_PATH_DATASET_MATCH = "DATASET_MATCH"                  # 数据集匹配
    LOOKUP_PATH_OTHER_EXACT_MATCH = "OTHER_EXACT"                # 其它精确匹配

    # 日志系统常用键名
    GPU_AVAILABLE = "gpu_available"                               # GPU可用性
    GPU_MEMORY_USED_MB = "gpu_memory_used_mb"                     # GPU已用内存(MB)
    GPU_MEMORY_TOTAL_MB = "gpu_memory_total_mb"                   # GPU总内存(MB)
    GPU_DEVICE_COUNT = "gpu_device_count"                         # GPU设备数量
    GPU_ERROR = "error"                                           # GPU错误信息
    GPU_INFO_FAILED = "gpu_info_failed"                           # GPU信息获取失败

    # 数据束统计键名
    DATASET_COUNT = "dataset_count"                               # 数据集数量
    TOTAL_ROWS = "total_rows"                                     # 总行数
    TOTAL_COLUMNS = "total_columns"                               # 总列数
    TOTAL_MEMORY_MB = "total_memory_mb"                           # 总内存使用(MB)
    WELL_COUNT = "well_count"                                     # 井数量
    DATASET_DETAILS = "dataset_details"                           # 数据集详情
    ROWS = "rows"                                                 # 行数
    COLUMNS = "columns"                                           # 列数
    MEMORY_MB = "memory_mb"                                       # 内存使用(MB)
    DTYPES = "dtypes"                                             # 数据类型

    # 映射统计键名
    MAPPINGS = "mappings"                                         # 映射关系
    MAPPING_COUNT = "mapping_count"                               # 映射数量
    UNIQUE_TARGETS = "unique_targets"                             # 唯一目标数量
    REVERSE_MAPPINGS = "reverse_mappings"                         # 反向映射
    IS_VALID = "is_valid"                                         # 是否有效

    # 数据束to_dict键名
    DATASET_NAMES = "dataset_names"                               # 数据集名称列表
    WELL_NAMES = "well_names"                                     # 井名称列表
    STATISTICS = "statistics"                                     # 统计信息

    @classmethod
    def basic_stats(cls) -> set[str]:
        """返回基础统计量键名。"""
        return {
            cls.COUNT.value, cls.MEAN.value, cls.STD.value,
            cls.MIN.value, cls.MAX.value, cls.MEDIAN.value
        }

    @classmethod
    def quality_stats(cls) -> set[str]:
        """返回数据质量统计键名。"""
        return {
            cls.NULL_COUNT.value, cls.NULL_PERCENTAGE.value,
            cls.VALID_COUNT.value, cls.TOTAL_COUNT.value
        }

    @classmethod
    def attribute_stats(cls) -> set[str]:
        """返回属性管理统计键名。"""
        return {
            cls.TOTAL_ATTRIBUTES.value, cls.CATEGORY_COUNTS.value,
            cls.UNIQUE_NAMES.value, cls.HAS_VERSION.value,
            cls.SCOPED_ATTRIBUTES.value
        }


class WpApiKeys(str, Enum):
    """统一API响应键名（顶层）。

    **使用范围**：logwp包API响应层，定义统一的响应格式键名
    **架构位置**：API层通用响应格式，跨模块标准化接口
    **依赖关系**：被API响应、结果封装、错误处理模块使用
    **设计原则**：统一响应格式、类型安全、JSON友好、扩展性
    **性能特征**：轻量级键名、序列化优化、网络传输友好

    Examples:
        >>> # API响应构建
        >>> response = {
        ...     WpApiKeys.IS_VALID: True,
        ...     WpApiKeys.DATA: result_data,
        ...     WpApiKeys.META: metadata
        ... }
        >>>
        >>> # 错误响应
        >>> error_response = {
        ...     WpApiKeys.IS_VALID: False,
        ...     WpApiKeys.ERRORS: error_list,
        ...     WpApiKeys.MESSAGE: "处理失败"
        ... }

    References:
        《SCAPE_DDS_详细设计_logwp》§4.1 - API响应格式设计
    """

    IS_VALID = "is_valid"
    MESSAGE = "message"
    DATA = "data"
    ERRORS = "errors"
    META = "meta"

    # 现代化扩展字段
    PERFORMANCE = "performance"
    GPU_INFO = "gpu_info"
    ASYNC_STATUS = "async_status"
    FEATURE_FLAGS = "feature_flags"

    # 数据集通用字段
    MISSING_COLUMNS = "missing_columns"
    NULL_COUNT = "null_count"
    COLUMNS = "columns"
    NUMERIC_COLUMNS = "numeric_columns"
    FILTER_TYPE = "filter_type"
    ORIGINAL_ROWS = "original_rows"
    FILTERED_ROWS = "filtered_rows"
    ERROR = "error"

    # 深度相关字段
    MIN_DEPTH = "min_depth"
    MAX_DEPTH = "max_depth"
    DEPTH_RANGE = "depth_range"
    DEPTH_COLUMN = "depth_column"
    DEPTH_POINTS = "depth_points"

    # 点数据集字段
    TOTAL_POINTS = "total_points"
    VALID_POINTS = "valid_points"
    NULL_POINTS = "null_points"
    POINT_STATISTICS = "point_statistics"
    SPARSE_DATA = "sparse_data"
    SAMPLING_TYPE = "sampling_type"

    # 区间数据集字段
    TOTAL_INTERVALS = "total_intervals"
    VALID_INTERVALS = "valid_intervals"
    NULL_INTERVALS = "null_intervals"
    TOP_COLUMN = "top_column"
    BOTTOM_COLUMN = "bottom_column"
    MIN_TOP_DEPTH = "min_top_depth"
    MAX_BOTTOM_DEPTH = "max_bottom_depth"
    TOTAL_DEPTH_RANGE = "total_depth_range"
    MIN_THICKNESS = "min_thickness"
    MAX_THICKNESS = "max_thickness"
    AVG_THICKNESS = "avg_thickness"
    VALID_INTERVAL_COUNT = "valid_interval_count"
    TOP_NULL_COUNT = "top_null_count"
    BOTTOM_NULL_COUNT = "bottom_null_count"
    INVALID_INTERVALS = "invalid_intervals"
    TOTAL_INVALID = "total_invalid"
    INTERVAL_STATISTICS = "interval_statistics"
    LAYERED_DATA = "layered_data"
    DUAL_DEPTH_INDEX = "dual_depth_index"

    # 注册表字段
    TOTAL_REGISTERED = "total_registered"
    REGISTERED_TYPES = "registered_types"
    REGISTERED_CLASSES = "registered_classes"

    # WFS v1.0 API字段
    ATTRIBUTE_NAME = "attribute_name"
    ATTRIBUTE_VALUE = "attribute_value"
    ATTRIBUTE_TYPE = "attribute_type"
    ATTRIBUTE_UNIT = "attribute_unit"
    ATTRIBUTE_SCOPE = "attribute_scope"
    ATTRIBUTE_DESCRIPTION = "attribute_description"
    DATA_TYPE = "data_type"
    UNIT = "unit"
    VALUE = "value"
    SCOPE = "scope"
    DESCRIPTION = "description"
    COLUMN_NAME = "column_name"
    INDEX = "index"
    BASE_NAME = "base_name"
    TOTAL_DATASETS = "total_datasets"
    SEARCH_PATTERN = "search_pattern"
    QUERY_ATTRIBUTE = "query_attribute"
    SCOPE_FILTER = "scope_filter"
    FUZZY_MATCH = "fuzzy_match"
    TOTAL_MATCHES = "total_matches"

    # WpHead相关字段
    ATTRIBUTES = "attributes"
    METADATA = "metadata"
    ATTRIBUTE_COUNT = "attribute_count"
    HAS_T2_AXIS = "has_t2_axis"
    SYSTEM_DEFAULTS = "system_defaults"
    FORMAT_VERSION = "format_version"
    SCOPE_INFO = "scope_info"
    DATASET = "dataset"
    WELL = "well"
    CURVE = "curve"
    AVAILABLE_ATTRIBUTES = "available_attributes"

    # 曲线元数据字典键名（格式无关）
    CURVE_NAME = "name"
    CURVE_UNIT = "unit"
    CURVE_DATA_TYPE = "data_type"
    CURVE_CATEGORY = "category"
    CURVE_DESCRIPTION = "description"
    CURVE_DIMENSION = "dimension"
    CURVE_CLASS = "curve_class"
    IS_WELL_IDENTIFIER = "is_well_identifier"
    DEPTH_ROLE = "depth_role"
    ELEMENT_NAMES = "element_names"

    # 曲线过滤条件键名
    FILTER_CATEGORY = "category"
    FILTER_DIMENSION = "dimension"
    FILTER_DATA_TYPE = "data_type"
    FILTER_IS_WELL_IDENTIFIER = "is_well_identifier"
    FILTER_DEPTH_ROLE = "depth_role"
    FILTER_IS_NUMERIC = "is_numeric"
    FILTER_CURVE_CLASS = "curve_class"




# WpFileFormat 已移动到 logwp/io/constants.py
# 请使用: from logwp.io.constants import WpFileFormat


class WpNormalizationMode(str, Enum):
    """字符串规范化模式（CIIA架构）。

    Architecture
    ------------
    层次/依赖: 常量层，定义CIIA规范化行为
    设计原则: 格式无关、国际化支持、可配置
    性能特征: 编译时常量、运行时选择

    遵循CIIA规范：
    - CIIA-2: Unicode标准化要求
    - CIIA-6: 格式无关设计
    """

    UNICODE_CASEFOLD = "unicode_casefold"  # Unicode Case Folding（推荐）
    ASCII_UPPER = "ascii_upper"  # ASCII大写转换（兼容模式）
    PRESERVE_CASE = "preserve_case"  # 保持原始大小写（调试模式）


class WpValidationRule(str, Enum):
    """数据验证规则枚举。

    Architecture
    ------------
    层次/依赖: 验证层规则定义
    设计原则: 规则标识、类型安全、扩展性
    性能特征: 编译时常量、运行时验证
    """

    # 基础验证规则
    NOT_NULL = "not_null"
    POSITIVE = "positive"
    NON_NEGATIVE = "non_negative"
    MONOTONIC_INCREASING = "monotonic_increasing"

    # 范围验证规则
    POROSITY_RANGE = "porosity_range"  # [0, 1]
    PERMEABILITY_POSITIVE = "permeability_positive"  # > 0
    DEPTH_INCREASING = "depth_increasing"

    # 格式验证规则
    WELL_NAME_FORMAT = "well_name_format"
    CURVE_NAME_FORMAT = "curve_name_format"
    UNIT_FORMAT = "unit_format"

    # COMP类型验证规则
    COMP_JSON_VALID = "comp_json_valid"
    COMP_STRUCTURE_VALID = "comp_structure_valid"
    T2_AXIS_VALID = "t2_axis_valid"


# ------------------------------------------------------------
# 对象模型层次常量（与文件格式无关）
# ------------------------------------------------------------


class WpAttributeScope(str, Enum):
    """属性作用域枚举。

    定义属性查找的作用域层次，与具体文件格式无关。

    Architecture
    ------------
    层次/依赖: 属性管理层作用域定义
    设计原则: 分层查找、继承机制、类型安全
    性能特征: 高效查找、缓存友好
    """

    GLOBAL = "global"
    WORKAREA = "workarea"
    DATASET = "dataset"
    WELL = "well"
    CURVE = "curve"
    OTHER = "other"

    @classmethod
    def hierarchical_scopes(cls) -> list[str]:
        """返回分层作用域（按优先级排序）。"""
        return [
            cls.CURVE.value,
            cls.WELL.value,
            cls.DATASET.value,
            cls.WORKAREA.value,
            cls.GLOBAL.value,
        ]


class WpExtAttributeCategory(str, Enum):
    """WFS v1.0属性类别枚举（格式无关）。

    基于WFS规范第5章定义的6种属性类别，提供格式无关的属性分类管理。

    Architecture
    ------------
    层次/依赖: 属性管理层类别定义，格式无关
    设计原则: WFS规范兼容、分层继承、类型安全
    性能特征: 高效分类、查找优化、缓存友好

    WFS v1.0属性类别定义：
    - V（版本）：全局唯一版本属性，必须是第一个属性
    - WP（工区）：工区全局属性，整个测井工区范围
    - DS（数据集）：数据集级别属性，特定数据集范围
    - W（井）：井级别属性，支持数据集继承逻辑
    - C（曲线）：曲线级别属性，支持复杂继承机制
    - O（其它）：其它属性，精确匹配查找，不受标准作用域限制

    Examples:
        >>> # 版本属性（全局唯一）
        >>> category = WpAttributeCategory.VERSION
        >>> assert category.is_global_unique()
        >>>
        >>> # 作用域属性（支持继承）
        >>> category = WpAttributeCategory.CURVE
        >>> assert category.is_scoped_attribute()
        >>>
        >>> # 获取查找优先级
        >>> priority = WpAttributeCategory.WELL.get_lookup_priority()

    References:
        《SCAPE_WFS_WP文件规范.md》§5.2.1 - 属性类别定义
    """

    VERSION = "V"      # 版本类别：全局唯一版本属性
    WORKAREA = "WP"    # 工区类别：工区全局属性
    DATASET = "DS"     # 数据集类别：数据集级别属性
    WELL = "W"         # 井类别：井级别属性，支持继承逻辑
    CURVE = "C"        # 曲线类别：曲线级别属性，支持复杂继承
    OTHER = "O"        # 其它类别：不受标准作用域限制

    def is_global_unique(self) -> bool:
        """检查是否为全局唯一属性类别。

        Returns:
            bool: 是否为全局唯一属性（仅VERSION类别）

        Examples:
            >>> assert WpAttributeCategory.VERSION.is_global_unique()
            >>> assert not WpAttributeCategory.WELL.is_global_unique()
        """
        return self == WpExtAttributeCategory.VERSION

    def is_scoped_attribute(self) -> bool:
        """检查是否为作用域属性类别。

        作用域属性支持分层继承机制，包括WP、DS、W、C类别。

        Returns:
            bool: 是否为作用域属性

        Examples:
            >>> assert WpAttributeCategory.CURVE.is_scoped_attribute()
            >>> assert not WpAttributeCategory.OTHER.is_scoped_attribute()
        """
        return self in {
            WpExtAttributeCategory.WORKAREA,
            WpExtAttributeCategory.DATASET,
            WpExtAttributeCategory.WELL,
            WpExtAttributeCategory.CURVE
        }

    def requires_exact_match(self) -> bool:
        """检查是否需要精确匹配查找。

        Returns:
            bool: 是否需要精确匹配（仅OTHER类别）

        Examples:
            >>> assert WpAttributeCategory.OTHER.requires_exact_match()
            >>> assert not WpAttributeCategory.WELL.requires_exact_match()
        """
        return self == WpExtAttributeCategory.OTHER

    def get_lookup_priority(self) -> int:
        """获取查找优先级（数值越小优先级越高）。

        Returns:
            int: 查找优先级

        Examples:
            >>> assert WpAttributeCategory.CURVE.get_lookup_priority() < WpAttributeCategory.WELL.get_lookup_priority()
        """
        priority_map = {
            WpExtAttributeCategory.VERSION: 0,   # 最高优先级
            WpExtAttributeCategory.CURVE: 1,     # 曲线级别
            WpExtAttributeCategory.WELL: 2,      # 井级别
            WpExtAttributeCategory.DATASET: 3,   # 数据集级别
            WpExtAttributeCategory.WORKAREA: 4,  # 工区级别
            WpExtAttributeCategory.OTHER: 5      # 最低优先级
        }
        return priority_map[self]

    @classmethod
    def get_scoped_categories(cls) -> list['WpExtAttributeCategory']:
        """获取所有作用域属性类别（按优先级排序）。

        Returns:
            list[WpAttributeCategory]: 作用域属性类别列表

        Examples:
            >>> categories = WpAttributeCategory.get_scoped_categories()
            >>> assert categories[0] == WpAttributeCategory.CURVE
        """
        return [
            cls.CURVE,
            cls.WELL,
            cls.DATASET,
            cls.WORKAREA
        ]

    @classmethod
    def from_scope(cls, scope: WpAttributeScope) -> 'WpExtAttributeCategory':
        """从作用域枚举转换为属性类别。

        Args:
            scope: 作用域枚举值

        Returns:
            WpAttributeCategory: 对应的属性类别

        Examples:
            >>> category = WpAttributeCategory.from_scope(WpAttributeScope.CURVE)
            >>> assert category == WpAttributeCategory.CURVE
        """
        scope_to_category = {
            WpAttributeScope.GLOBAL: cls.VERSION,
            WpAttributeScope.WORKAREA: cls.WORKAREA,
            WpAttributeScope.DATASET: cls.DATASET,
            WpAttributeScope.WELL: cls.WELL,
            WpAttributeScope.CURVE: cls.CURVE,
            WpAttributeScope.OTHER: cls.OTHER
        }
        return scope_to_category.get(scope, cls.OTHER)


class WpDataType(str, Enum):
    """数据类型枚举（格式无关）。

    定义曲线数据的基本类型，用于曲线基本属性管理。

    Architecture
    ------------
    层次/依赖: 常量定义层，曲线基本属性类型定义
    设计原则: 格式无关、类型安全、业务语义明确
    性能特征: 编译时常量、类型检查优化

    Examples:
        >>> # 曲线数据类型定义
        >>> gr_type = WpDataType.FLOAT
        >>> well_type = WpDataType.STR
        >>> flag_type = WpDataType.BOOL

        >>> # 类型验证
        >>> assert WpDataType.FLOAT in WpDataType.numeric_types()
        >>> assert WpDataType.STR in WpDataType.text_types()

    References:
        《SCAPE_WFS_WP文件规范.md》§4.1.3 - 数据类型规范
    """

    INT = "INT"         # 整型数据
    FLOAT = "FLOAT"     # 浮点型数据
    STR = "STR"         # 字符串型数据
    BOOL = "BOOL"       # 布尔型数据
    COMP = "COMP"       # 复合型数据（JSON）

    @classmethod
    def numeric_types(cls) -> set['WpDataType']:
        """获取数值类型集合。

        Returns:
            set[WpDataType]: 数值类型集合
        """
        return {cls.INT, cls.FLOAT}

    @classmethod
    def text_types(cls) -> set['WpDataType']:
        """获取文本类型集合。

        Returns:
            set[WpDataType]: 文本类型集合
        """
        return {cls.STR}

    @classmethod
    def from_pandas_dtype(cls, dtype: str) -> 'WpDataType':
        """从pandas数据类型推断WpDataType。

        Args:
            dtype: pandas数据类型字符串

        Returns:
            WpDataType: 对应的数据类型
        """
        dtype_str = str(dtype).lower()
        if 'int' in dtype_str:
            return cls.INT
        elif 'float' in dtype_str:
            return cls.FLOAT
        elif 'bool' in dtype_str:
            return cls.BOOL
        else:
            return cls.STR


class WpCurveCategory(str, Enum):
    """曲线类别枚举（格式无关）。

    定义曲线的业务类别，用于曲线分类和管理。

    Architecture
    ------------
    层次/依赖: 常量定义层，曲线业务分类定义
    设计原则: 业务语义明确、扩展性强、格式无关
    性能特征: 编译时常量、分类查询优化

    Examples:
        >>> # 曲线类别定义
        >>> gr_category = WpCurveCategory.LOGGING
        >>> porosity_category = WpCurveCategory.COMPUTED
        >>> well_category = WpCurveCategory.IDENTIFIER

        >>> # 类别过滤
        >>> logging_curves = [c for c in curves if c.category == WpCurveCategory.LOGGING]
    """

    LOGGING = "LOGGING"         # 测井曲线（原始测井数据）
    COMPUTED = "COMPUTED"       # 计算曲线（处理后的数据）
    IDENTIFIER = "IDENTIFIER"   # 标识曲线（井名、深度等）
    QUALITY = "QUALITY"         # 质量控制曲线
    CUSTOM = "CUSTOM"           # 自定义曲线

    @classmethod
    def data_curves(cls) -> set['WpCurveCategory']:
        """获取数据曲线类别集合。

        Returns:
            set[WpCurveCategory]: 数据曲线类别集合
        """
        return {cls.LOGGING, cls.COMPUTED, cls.CUSTOM, cls.QUALITY, cls.CUSTOM}

    @classmethod
    def system_curves(cls) -> set['WpCurveCategory']:
        """获取系统曲线类别集合。

        Returns:
            set[WpCurveCategory]: 系统曲线类别集合
        """
        return {cls.IDENTIFIER, cls.QUALITY}


class WpCurveDimension(str, Enum):
    """曲线维数枚举（格式无关）。

    定义曲线的维数特征，区分一维和二维组合曲线。

    Architecture
    ------------
    层次/依赖: 常量定义层，曲线维数定义
    设计原则: 维数语义明确、处理逻辑分离、格式无关
    性能特征: 编译时常量、维数查询优化

    Examples:
        >>> # 曲线维数定义
        >>> gr_dimension = WpCurveDimension.ONE_D
        >>> t2_dimension = WpCurveDimension.TWO_D

        >>> # 维数过滤
        >>> one_d_curves = [c for c in curves if c.dimension == WpCurveDimension.ONE_D]
        >>> two_d_curves = [c for c in curves if c.dimension == WpCurveDimension.TWO_D]
    """

    ONE_D = "1D"    # 一维曲线（标准测井曲线）
    TWO_D_COMP = "2D"    # 二维组合曲线（如T2谱、频谱等）


class WpCurveClass(str, Enum):
    """WFS曲线类别枚举（格式无关）。

    基于WFS规范4.1.4节定义的曲线类别，用于决定插值方法和数据类型约束。

    Architecture
    ------------
    层次/依赖: 常量定义层，WFS曲线类别定义
    设计原则: WFS规范兼容、插值方法区分、格式无关
    性能特征: 编译时常量、插值策略优化

    Examples:
        >>> # WFS曲线类别定义
        >>> facies_class = WpCurveClass.CATEGORICAL
        >>> gr_class = WpCurveClass.NORMAL

        >>> # 插值方法选择
        >>> if curve.curve_class == WpCurveClass.CATEGORICAL:
        ...     interpolation_method = "nearest"
        >>> else:
        ...     interpolation_method = "linear"

    References:
        《SCAPE_WFS_WP文件规范.md》§4.1.4 - 曲线类别规范
    """

    CATEGORICAL = "CAT"    # 类别型曲线（仅支持INT/STR，最近邻插值）
    NORMAL = ""           # 普通型曲线（支持所有类型，用户指定插值）

    def is_categorical(self) -> bool:
        """检查是否为类别型曲线。

        Returns:
            bool: 是否为类别型曲线

        Examples:
            >>> assert WpCurveClass.CATEGORICAL.is_categorical()
            >>> assert not WpCurveClass.NORMAL.is_categorical()
        """
        return self == WpCurveClass.CATEGORICAL

    def get_allowed_data_types(self) -> set[str]:
        """获取允许的数据类型集合。

        Returns:
            set[str]: 允许的数据类型集合

        Examples:
            >>> cat_types = WpCurveClass.CATEGORICAL.get_allowed_data_types()
            >>> assert cat_types == {"INT", "STR"}
            >>> normal_types = WpCurveClass.NORMAL.get_allowed_data_types()
            >>> assert "FLOAT" in normal_types
        """
        if self == WpCurveClass.CATEGORICAL:
            return {"INT", "STR"}
        else:
            return {"INT", "FLOAT", "STR", "BOOL"}

    def get_mandatory_interpolation_method(self) -> str | None:
        """获取强制插值方法。

        对于某些曲线类型，必须使用特定的插值方法以保证数据语义正确性。

        Returns:
            str | None: 强制插值方法名称，None表示无强制要求

        Examples:
            >>> assert WpCurveClass.CATEGORICAL.get_mandatory_interpolation_method() == "nearest"
            >>> assert WpCurveClass.NORMAL.get_mandatory_interpolation_method() is None
        """
        if self == WpCurveClass.CATEGORICAL:
            return "nearest"  # 类别型曲线强制使用最近邻插值
        else:
            return None  # 普通曲线无强制要求


class WpDepthRole(str, Enum):
    """深度曲线角色枚举（格式无关）。

    定义深度曲线在不同数据集类型中的角色，支持interval数据集的双深度索引。

    Architecture
    ------------
    层次/依赖: 常量定义层，深度角色定义
    设计原则: 数据集类型适配、角色语义明确、格式无关
    性能特征: 编译时常量、角色查询优化

    Examples:
        >>> # 深度角色定义
        >>> md_role = WpDepthRole.SINGLE
        >>> top_role = WpDepthRole.TOP
        >>> bottom_role = WpDepthRole.BOTTOM

        >>> # 角色过滤
        >>> depth_curves = [c for c in curves if c.depth_role is not None]
        >>> interval_tops = [c for c in curves if c.depth_role == WpDepthRole.TOP]

    References:
        《SCAPE_WFS_WP文件规范.md》A.3.3 - 区间型数据集规范
    """

    SINGLE = "SINGLE"      # 单一深度索引（continuous、discrete数据集）
    TOP = "TOP"           # 顶界深度（interval数据集）
    BOTTOM = "BOTTOM"     # 底界深度（interval数据集）

    def is_interval_depth(self) -> bool:
        """检查是否为区间深度角色。

        Returns:
            bool: 是否为区间深度角色（TOP或BOTTOM）

        Examples:
            >>> assert WpDepthRole.TOP.is_interval_depth()
            >>> assert WpDepthRole.BOTTOM.is_interval_depth()
            >>> assert not WpDepthRole.SINGLE.is_interval_depth()
        """
        return self in {WpDepthRole.TOP, WpDepthRole.BOTTOM}

    def get_compatible_dataset_types(self) -> set[str]:
        """获取兼容的数据集类型集合。

        Returns:
            set[str]: 兼容的数据集类型集合

        Examples:
            >>> single_types = WpDepthRole.SINGLE.get_compatible_dataset_types()
            >>> assert "CONTINUOUS" in single_types
            >>> interval_types = WpDepthRole.TOP.get_compatible_dataset_types()
            >>> assert interval_types == {"INTERVAL"}
        """
        if self == WpDepthRole.SINGLE:
            return {"CONTINUOUS", "POINT"}  # POINT对应WpDsType.POINT（discrete）
        else:
            return {"INTERVAL"}


class WpDepthUnit(str, Enum):
    """深度单位枚举。

    定义测井数据中深度曲线的标准单位。

    Architecture
    ------------
    层次/依赖: 常量定义层，深度单位标准化
    设计原则: 单位统一、转换支持、IO层规范
    性能特征: 编译时常量、单位验证优化

    Values:
        METER: 米（国际单位制）
        FEET: 英尺（英制单位）

    Note:
        - IO层加载时会保证深度曲线单位为这两种之一
        - 单位转换工具支持米和英尺的相互转换
        - 用于统一项目中的深度单位管理

    Examples:
        >>> # 单位定义
        >>> meter_unit = WpDepthUnit.METER
        >>> feet_unit = WpDepthUnit.FEET
        >>> assert meter_unit == "m"
        >>> assert feet_unit == "ft"

        >>> # 单位验证
        >>> valid_units = {WpDepthUnit.METER, WpDepthUnit.FEET}
        >>> assert "m" in valid_units

    References:
        《SCAPE_DDS_详细设计_logwp.md》§4.2 - 深度单位管理系统
    """

    METER = "m"    # 米（国际单位制）
    FEET = "ft"    # 英尺（英制单位）

    @classmethod
    def get_all_units(cls) -> set[str]:
        """获取所有支持的深度单位。

        Returns:
            set[str]: 所有支持的深度单位集合

        Examples:
            >>> units = WpDepthUnit.get_all_units()
            >>> assert units == {"m", "ft"}
        """
        return {cls.METER, cls.FEET}

    def is_metric(self) -> bool:
        """检查是否为公制单位。

        Returns:
            bool: 是否为公制单位（米）

        Examples:
            >>> assert WpDepthUnit.METER.is_metric()
            >>> assert not WpDepthUnit.FEET.is_metric()
        """
        return self == WpDepthUnit.METER

    def is_imperial(self) -> bool:
        """检查是否为英制单位。

        Returns:
            bool: 是否为英制单位（英尺）

        Examples:
            >>> assert WpDepthUnit.FEET.is_imperial()
            >>> assert not WpDepthUnit.METER.is_imperial()
        """
        return self == WpDepthUnit.FEET


class WpStandardColumn(str, Enum):
    """标准化列名枚举。

    **使用范围**：logwp包models、datasets层，定义内存对象模型标准化列名
    **架构位置**：数据模型层标准化接口，与具体文件格式无关
    **依赖关系**：被datasets、models使用，替代对WpXlsxKey的依赖
    **设计原则**：统一命名、类型安全、格式解耦、扩展性
    **性能特征**：编译时检查、运行时优化、内存友好

    该枚举提供文件格式无关的标准化列名，确保数据模型层不依赖具体的
    文件格式（如Excel列名），支持多种数据源格式的统一处理。

    Examples:
        >>> # 数据集中使用标准化列名
        >>> well_column = WpStandardColumn.WELL_NAME
        >>> depth_column = WpStandardColumn.DEPTH
        >>>
        >>> # 区间数据集使用
        >>> top_col = WpStandardColumn.DEPTH_TOP
        >>> bottom_col = WpStandardColumn.DEPTH_BOTTOM
        >>>
        >>> # 列名映射（在I/O层进行）
        >>> column_mapping = {
        ...     "WELL_NO": WpStandardColumn.WELL_NAME,
        ...     "MD": WpStandardColumn.DEPTH
        ... }

    References:
        《SCAPE_DDS_详细设计_logwp》§3.2 - 数据集标准化设计
        《SCAPE_SAD_软件架构设计》§4.3 - Port-Adapter模式
    """

    # 标准化深度列
    MD = "MD"  # 测量深度
    TVD = "TVD"  # 垂直深度
    TVDSS = "TVDSS"  # 海平面垂直深度
    DEPTH = "depth"  # 通用深度
    DEPT = "DEPT"  # 深度变体

    # 区间深度列（基于WFS规范）
    MD_TOP = "MD_Top"  # 顶界测量深度
    MDTOP = "MDTop"  # 顶界深度紧凑表示
    TOP = "TOP"
    TVD_TOP = "TVD_Top"  # 顶界垂直深度
    TVDTOP = "TVDTop"  # 顶界垂直深度紧凑表示
    MD_BOTTOM = "MD_Bottom"  # 底界测量深度
    MDBOTTOM = "MDBottom"  # 底界深度紧凑表示
    MD_BOT = "MD_Bot"  # 底界深度缩写
    MDBOT = "MDBot"  # 底界深度紧凑表示
    BOTTOM = "BOTTOM"
    BOT = "BOT"
    TVD_BOTTOM = "TVD_Bottom"  # 底界垂直深度
    TVDBOTTOM = "TVDBottom"  # 底界垂直深度紧凑表示

    # 标准化深度列（内部使用）
    DEPTH_TOP = "depth_top"
    DEPTH_BOTTOM = "depth_bottom"

    # 标准化井名列
    WELL_NAME = "well_name"  # 标准化井名列
    WELL = "well"  # 简化井名列（向后兼容）
    WELLNAME = "wellname"  # 紧凑井名列（向后兼容）

    # 常见井名列变体（用于推断）
    WELL_NO = "WELL_NO"  # 井号
    WELL_ID = "WELL_ID"  # 井标识

    # 标准化数据集标识
    DATASET_NAME = "dataset_name"
    DATASET_TYPE = "dataset_type"

    # 标准化时间列
    TIMESTAMP = "timestamp"
    CREATED_AT = "created_at"
    MODIFIED_AT = "modified_at"

    # 标准化索引列
    INDEX = "index"
    ROW_ID = "row_id"

    # 标准化质量列
    QUALITY = "quality"
    QUALITY_FLAG = "quality_flag"

    # 标准化元数据列
    SOURCE = "source"
    VERSION = "version"

    # 标准化元数据字典键名
    DESCRIPTION = "description"
    ATTRIBUTES = "attributes"

    # 标准化T2轴配置键名（格式无关）
    AXIS_TYPE = "axis_type"
    T2_START = "t2_start"
    T2_END = "t2_end"
    N_BINS = "n_bins"
    T2_UNIT = "unit"
    T2_VALUE = "value"

    # 标准化系统默认值键名
    CREATED_BY = "created_by"
    FORMAT = "format"
    ENCODING = "encoding"

    # 注意：COMP类型现在直接保存JSON结构，不再使用简化的字段映射

    @classmethod
    def well_name_aliases(cls) -> set[str]:
        """返回所有井名列别名。"""
        return {
            cls.WELL_NAME.value,
            cls.WELL.value,
            cls.WELLNAME.value,
            cls.WELL_NO.value,
            cls.WELL_ID.value
        }

    @classmethod
    def depth_aliases(cls) -> set[str]:
        """返回所有单条测量深度(MD)相关列名。"""
        return {
            cls.MD.value,
            cls.DEPTH.value,
            cls.DEPT.value
        }

    @classmethod
    def depth_top_aliases(cls) -> set[str]:
        """返回所有测量顶界深度别名。"""
        return {
            cls.MD_TOP.value,
            cls.MDTOP.value,
            cls.TOP.value,
            cls.DEPTH_TOP.value
        }

    @classmethod
    def depth_bottom_aliases(cls) -> set[str]:
        """返回所有测量底界深度别名。"""
        return {
            cls.MD_BOTTOM.value,
            cls.MDBOTTOM.value,
            cls.BOTTOM.value,
            cls.BOT.value,
            cls.MD_BOT.value,
            cls.MDBOT.value,
            cls.DEPTH_BOTTOM.value
        }

    @classmethod
    def depth_all_aliases(cls) -> set[str]:
        """返回所有测量深度相关列名。"""
        return cls.depth_aliases() | cls.depth_top_aliases() | cls.depth_bottom_aliases()


class WpCurveDescription(str, Enum):
    """曲线描述常量枚举。

    定义标准曲线的描述信息，避免硬编码字符串。

    Architecture
    ------------
    层次/依赖: 常量定义层，曲线描述标准化
    设计原则: 避免硬编码、国际化友好、语义明确
    性能特征: 编译时常量、内存友好

    Examples:
        >>> # 使用描述常量
        >>> well_desc = WpCurveDescription.WELL_IDENTIFIER_CURVE
        >>> depth_desc = WpCurveDescription.DEPTH_REFERENCE_CURVE

    References:
        《SCAPE_CCG_编码与通用规范.md》CT-2 - 禁止硬编码字符串
    """

    WELL_IDENTIFIER_CURVE = "Well identifier curve"
    DEPTH_REFERENCE_CURVE = "Depth reference curve"
    INTERVAL_TOP_DEPTH_CURVE = "Interval top depth reference curve"
    INTERVAL_BOTTOM_DEPTH_CURVE = "Interval bottom depth reference curve"


class WpLogMessage(str, Enum):
    """日志消息常量枚举。

    定义标准日志消息，避免硬编码字符串。

    Architecture
    ------------
    层次/依赖: 常量定义层，日志消息标准化
    设计原则: 避免硬编码、国际化友好、结构化日志
    性能特征: 编译时常量、日志性能优化

    Examples:
        >>> # 使用日志消息常量
        >>> logger.debug(WpLogMessage.CURVE_ADDED, curve_name=name)
        >>> logger.warning(WpLogMessage.CURVE_EXISTS_SKIP, curve_name=name)

    References:
        《SCAPE_CCG_编码与通用规范.md》LG-2 - 结构化日志信息
    """

    CURVE_ADDED = "Curve added"
    CURVE_EXISTS_SKIP = "Curve already exists, skipping addition"
    DEPTH_CURVE_ADDED = "Depth curve added"
    TOP_DEPTH_CURVE_ADDED = "Top depth curve added"
    BOTTOM_DEPTH_CURVE_ADDED = "Bottom depth curve added"
    BATCH_MAPPING_COMPLETE = "Batch well mapping creation completed"
    DATASET_ADDED_TO_PROJECT = "Dataset added to project"
    PROJECT_HEAD_ATTRIBUTES_SET = "Project head attributes set"
    WELL_MAPPINGS_SET = "Well mappings set"
    PROJECT_CREATED_COMPLETE = "Project creation completed"

    @classmethod
    def well_columns(cls) -> set[str]:
        """返回所有井名相关列名。"""
        return {cls.WELL_NAME.value, cls.WELL.value, cls.WELLNAME.value}

    @classmethod
    def metadata_columns(cls) -> set[str]:
        """返回所有元数据相关列名。"""
        return {
            cls.DATASET_NAME.value, cls.DATASET_TYPE.value,
            cls.TIMESTAMP.value, cls.CREATED_AT.value, cls.MODIFIED_AT.value,
            cls.SOURCE.value, cls.VERSION.value, cls.DESCRIPTION.value,
            cls.ATTRIBUTES.value
        }

    @classmethod
    def t2_axis_keys(cls) -> set[str]:
        """返回所有T2轴配置相关键名。"""
        return {
            cls.AXIS_TYPE.value, cls.T2_START.value, cls.T2_END.value,
            cls.N_BINS.value, cls.T2_UNIT.value, cls.T2_VALUE.value,
            cls.DESCRIPTION.value
        }

    @classmethod
    def system_default_keys(cls) -> set[str]:
        """返回所有系统默认值键名。"""
        return {
            cls.VERSION.value, cls.CREATED_BY.value, cls.FORMAT.value,
            cls.ENCODING.value, cls.TIMESTAMP.value
        }

    # 注意：COMP相关的方法已移除，因为现在直接使用JSON结构


class WpCurve2DPattern(str, Enum):
    """二维组合曲线命名模式常量（格式无关）。

    基于WFS规范§4.7.3定义的二维组合曲线[NAME][INDEX]命名模式。

    Architecture
    ------------
    层次/依赖: 曲线管理层命名模式定义，格式无关
    设计原则: WFS规范兼容、模式匹配、类型安全
    性能特征: 正则表达式优化、缓存友好

    二维组合曲线命名规则：
    - 标准模式：[NAME][INDEX]
    - NAME：二维组合曲线的基础名称
    - INDEX：元素索引，从1开始的连续整数
    - 示例：T2_VALUE[1]、T2_VALUE[2]、...、T2_VALUE[50]

    Examples:
        >>> # 检查是否为二维组合曲线
        >>> pattern = WpCurve2DPattern.INDEX_PATTERN
        >>> assert pattern.matches("T2_VALUE[1]")
        >>> assert not pattern.matches("GR")
        >>>
        >>> # 解析二维组合曲线名称
        >>> base_name, index = pattern.parse("T2_VALUE[42]")
        >>> assert base_name == "T2_VALUE"
        >>> assert index == 42

    References:
        《SCAPE_WFS_WP文件规范.md》§4.7.3 - 二维组合曲线规范
    """

    # 二维组合曲线索引模式：[数字]
    INDEX_PATTERN = r"\[(\d+)\]$"

    # 二维组合曲线完整模式：名称[数字]
    FULL_PATTERN = r"^(.+)\[(\d+)\]$"

    # 索引分隔符
    INDEX_SEPARATOR = "["
    INDEX_CLOSER = "]"

    # 索引起始值
    INDEX_START = 1

    @classmethod
    def matches(cls, column_name: str) -> bool:
        """检查列名是否匹配二维组合曲线模式。

        Args:
            column_name: 列名

        Returns:
            bool: 是否匹配二维组合曲线模式

        Examples:
            >>> assert WpCurve2DPattern.matches("T2_VALUE[1]")
            >>> assert not WpCurve2DPattern.matches("GR")
        """
        import re
        return bool(re.search(cls.INDEX_PATTERN, column_name))

    @classmethod
    def parse(cls, column_name: str) -> tuple[str, int] | None:
        """解析二维组合曲线名称。

        Args:
            column_name: 列名，如 "T2_VALUE[1]"

        Returns:
            tuple[str, int] | None: (基础名称, 索引) 或 None（非二维组合曲线）

        Examples:
            >>> result = WpCurve2DPattern.parse("T2_VALUE[1]")
            >>> assert result == ("T2_VALUE", 1)
            >>> assert WpCurve2DPattern.parse("GR") is None
        """
        import re
        match = re.search(cls.FULL_PATTERN, column_name)
        if match:
            base_name = match.group(1)
            index = int(match.group(2))
            return (base_name, index)
        return None

    @classmethod
    def build_name(cls, base_name: str, index: int) -> str:
        """构建二维组合曲线名称。

        Args:
            base_name: 基础名称
            index: 索引值

        Returns:
            str: 二维组合曲线名称

        Examples:
            >>> name = WpCurve2DPattern.build_name("T2_VALUE", 1)
            >>> assert name == "T2_VALUE[1]"
        """
        return f"{base_name}[{index}]"

    @classmethod
    def extract_base_name(cls, column_name: str) -> str:
        """提取二维组合曲线的基础名称。

        Args:
            column_name: 列名

        Returns:
            str: 基础名称，如果不是二维组合曲线则返回原名称

        Examples:
            >>> base = WpCurve2DPattern.extract_base_name("T2_VALUE[1]")
            >>> assert base == "T2_VALUE"
            >>> base = WpCurve2DPattern.extract_base_name("GR")
            >>> assert base == "GR"
        """
        result = cls.parse(column_name)
        if result:
            return result[0]
        return column_name

    @classmethod
    def extract_index(cls, column_name: str) -> int | None:
        """提取二维组合曲线的索引值。

        Args:
            column_name: 列名

        Returns:
            int | None: 索引值，如果不是二维组合曲线则返回None

        Examples:
            >>> index = WpCurve2DPattern.extract_index("T2_VALUE[1]")
            >>> assert index == 1
            >>> assert WpCurve2DPattern.extract_index("GR") is None
        """
        result = cls.parse(column_name)
        if result:
            return result[1]
        return None


class WpProcessingStage(str, Enum):
    """数据处理阶段枚举。

    Architecture
    ------------
    层次/依赖: 处理流程阶段定义
    设计原则: 流程标识、状态管理、监控友好
    性能特征: 状态追踪、性能监控
    """

    # I/O阶段
    LOADING = "loading"
    PARSING = "parsing"
    VALIDATION = "validation"

    # 数据处理阶段
    PREPROCESSING = "preprocessing"
    FEATURE_EXTRACTION = "feature_extraction"
    TRANSFORMATION = "transformation"

    # 算法阶段
    MODEL_TRAINING = "model_training"
    PREDICTION = "prediction"
    OPTIMIZATION = "optimization"

    # 输出阶段
    POSTPROCESSING = "postprocessing"
    SERIALIZATION = "serialization"
    EXPORT = "export"
