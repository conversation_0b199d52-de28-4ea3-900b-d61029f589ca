from __future__ import annotations

from typing import Literal

from pydantic import BaseModel, Field


class LoggingConfig(BaseModel):
    """
    结构化日志的配置模型。

    旨在由全局配置系统（如 pydantic-settings）加载和管理，或在应用启动时
    （例如Jupyter Notebook的第一个单元格）手动实例化和配置。
    """
    level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = Field(
        "INFO",
        description="日志记录的最低级别。"
    )
    json_format: bool = Field(
        False,
        description="是否以JSON格式输出日志。在Jupyter中建议为False，生产环境或文件日志建议为True。"
    )
    log_to_file: bool = Field(
        False,
        description="是否启用文件日志记录。"
    )
    log_file_path: str = Field(
        "logs/scape_project.log",
        description="日志文件路径，当 log_to_file 为 True 时生效。"
    )
    include_performance_context: bool = Field(
        True,
        description="是否在日志中包含性能上下文（CPU, Memory）。"
    )
    include_gpu_context: bool = Field(
        True,
        description="如果GPU可用，是否在日志中包含GPU上下文。"
    )
