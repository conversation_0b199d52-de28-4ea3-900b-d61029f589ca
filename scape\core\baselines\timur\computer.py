"""scape.core.baselines.timur.computer - Timur/Coates基准模型核心计算逻辑

包含Timur/Coates渗透率计算公式和参数优化器。

"""

from __future__ import annotations

from typing import Any, Dict

import numpy as np
from scipy.optimize import OptimizeResult, minimize


def _calculate_timur_permeability(
    phit: np.ndarray, bfv: np.ndarray, ktim_a: float, phit_exp: float, ktim_exp: float, bfv_min: float
) -> np.ndarray:
    """根据Timur/Coates公式计算渗透率。

    k_TIM = KTIM_A * PHIT^PHIT_exp * ((PHIT - BFV_Restricted) / BFV_Restricted)^KTIM_exp

    Args:
        phit: 总孔隙度数组。
        bfv: 束缚水孔隙度数组。
        ktim_a: Timur/Coates系数。
        phit_exp: 孔隙度指数。
        ktim_exp: 自由流体指数。
        bfv_min: BFV的最小值，来自配置。

    Returns:
        计算出的渗透率数组。
    """
    epsilon = 1e-9  # 防止log(0)或除以零

    # 确保输入为正，以避免在计算中出现NaN或复数
    safe_phit = np.maximum(phit, epsilon)
    safe_bfv = np.maximum(bfv, epsilon)

    # 遵循用户定义的BFV约束逻辑: BFV_Restricted = MAX(MIN(PHIT, BFV_MIN), BFV)
    bfv_synthetic = np.minimum(safe_phit, bfv_min)
    bfv_restricted = np.maximum(bfv_synthetic, safe_bfv)

    # 安全检查，防止 movable_fluid_fraction 为负或零
    movable_fluid_fraction = np.maximum(epsilon, safe_phit - bfv_restricted)
    bfv_restricted_safe = np.maximum(epsilon, bfv_restricted)
    ratio = movable_fluid_fraction / bfv_restricted_safe

    k_tim = ktim_a * (safe_phit**phit_exp) * (ratio**ktim_exp)
    return np.maximum(k_tim, epsilon)  # 确保渗透率输出为正


def find_timur_parameters(data: Dict[str, np.ndarray], bfv_min: float) -> Dict[str, Any]:
    """使用scipy.optimize寻找Timur/Coates模型的最佳参数。

    Args:
        data: 一个包含numpy数组的字典，键为 'phit_nmr', 'bfv_nmr', 'k_label'。
        bfv_min: BFV的最小值，来自配置。

    Returns:
        一个包含优化结果的字典。
    """

    def _rmsle_loss(params: np.ndarray, data_dict: Dict[str, np.ndarray], bfv_min_val: float) -> float:
        """均方根对数误差 (RMSLE) 损失函数。"""
        log10_ktim_a, phit_exp, ktim_exp = params
        ktim_a = 10**log10_ktim_a

        k_pred = _calculate_timur_permeability(
            data_dict["phit_nmr"], data_dict["bfv_nmr"], ktim_a, phit_exp, ktim_exp, bfv_min_val
        )
        k_label = data_dict["k_label"]

        log_diff_sq = (np.log10(k_pred) - np.log10(k_label)) ** 2
        return np.sqrt(np.mean(log_diff_sq))

    # 参数顺序: [log10_KTIM_A, PHIT_EXP, KTIM_EXP]
    bounds = [(-10, 4), (0.5, 5), (0.5, 5)]
    initial_guess = [0.0, 4.0, 2.0]

    result: OptimizeResult = minimize(
        fun=_rmsle_loss,
        x0=np.array(initial_guess),
        args=(data, bfv_min),
        method="L-BFGS-B",
        bounds=bounds,
        options={"disp": False},
    )

    optimized_params_values = result.x
    optimized_parameters = {
        "log10_KTIM_A": optimized_params_values[0],
        "PHIT_EXP": optimized_params_values[1],
        "KTIM_EXP": optimized_params_values[2],
    }

    return {
        "optimized_parameters": optimized_parameters,
        "final_loss": result.fun,
        "success": result.success,
        "optimizer_status": result.message.decode("utf-8") if isinstance(result.message, bytes) else str(result.message),
    }
