"""
提供专为测井机器学习设计的自定义评估指标。
"""
from __future__ import annotations

import numpy as np
import pandas as pd


def accuracy_within_tolerance(
    y_true: np.ndarray | pd.Series,
    y_pred: np.ndarray | pd.Series,
    tolerance: float
) -> float:
    """
    计算在给定容差范围内的预测准确率。

    这个指标在工程领域非常有用，它衡量了有多少预测值落在了
    真实值的一个可接受的误差范围内。例如，在孔隙度预测中，
    我们可能关心有多少预测点与岩心孔隙度的误差在±0.02 (2 p.u.) 以内。

    公式: (count(|y_pred - y_true| <= tolerance) / total_count) * 100

    Args:
        y_true (np.ndarray | pd.Series): 真实的标签值。
        y_pred (np.ndarray | pd.Series): 模型预测的标签值。
        tolerance (float): 绝对容差。必须为非负数。

    Returns:
        float: 落在容差范围内的预测百分比 (0.0 to 100.0)。
    """
    if tolerance < 0:
        raise ValueError("容差 (tolerance) 必须为非负数。")

    y_true_arr, y_pred_arr = np.asarray(y_true), np.asarray(y_pred)
    if y_true_arr.shape != y_pred_arr.shape:
        raise ValueError(f"y_true 和 y_pred 的形状必须一致，但收到了 {y_true_arr.shape} 和 {y_pred_arr.shape}")

    if len(y_true_arr) == 0:
        return 100.0  # 对于空集，可以认为100%符合要求

    absolute_error = np.abs(y_true_arr - y_pred_arr)
    within_tolerance_count = np.sum(absolute_error <= tolerance)

    accuracy_percentage = (within_tolerance_count / len(y_true_arr)) * 100.0

    return accuracy_percentage
