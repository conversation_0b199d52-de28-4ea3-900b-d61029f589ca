# Markdown 大师级速查表 v1.2

## 核心思想先行：Markdown 是什么？

想象一下，你在用最纯粹的文本聊天软件（比如早期的短信）给朋友描述一份文档的格式，你会怎么做？

你可能会说：“这句要 *加粗*，那句要用‘点点’列个清单。”

**Markdown 的核心思想就是如此：它是一套用纯文本符号来「标记」文档格式的规则。**

它就像是你和计算机之间的一个君子协定：

* **你 (作者)**：专注于内容，用最直观、干扰最小的符号（如 `*`、`#`、`-`）来表达格式意图。你的源文件 (`.md`) 永远清晰可读。
* **计算机 (渲染器)**：忠实地将这些标记符号转换为漂亮的 HTML、PDF 或其他格式。

> **专家见解：为什么这很重要？**
> Markdown 的美妙之处在于 **「关注点分离」**。它将 **「内容创作」** 与 **「最终样式」** 彻底分开。你无需像使用 Word 那样，在写作时不断分心于调整字体、颜色和间距。你只需专注思想的流动，格式在无形中自然完成。

---

## 场景零：新环境初始化 —— 创建你的第一份结构化文档

**目标 (Goal):** 快速上手，创建一份包含标题和段落的基本文档，理解 Markdown 的结构基础。

### 流程/解决方案：

1.  **创建文件**
    任何纯文本编辑器（VS Code, Notepad++, Sublime Text, 甚至记事本）都可以。只需创建一个新文件，并将其命名为 `README.md` 或任何以 `.md` 结尾的文件名。

2.  **设置主标题 (一级标题)**
    这是整个文档的最高标题，每个文件通常只有一个。

    ```markdown
    # 这是我的项目主标题
    ```

3.  **撰写段落**
    直接输入文字即可。段落之间必须用 **一个完整的空行** 隔开。

    ```markdown
    这是第一个段落。Markdown 非常简洁，你只需要专注写作。

    这是第二个段落。注意，我们用一个空行来区分它们。
    ```
    > **专家经验：为何需要空行？**
    > 这个空行是 Markdown 解析器识别“段落”的关键。如果没有空行，即使你在编辑器里换了行，它们在最终渲染时也可能被视为同一段落。空行是明确的结构信号。

4.  **添加子标题 (二级、三级...)**
    使用更多的 `#` 来创建层级更低的子标题，构建文档的骨架。

    ```markdown
    ## 章节一：引言
    ### 小节 1.1：背景介绍
    ```

5.  **强制换行**
    在大多数情况下，你应该让解析器自动处理换行。但如果确实需要在段落内强制换行（比如诗歌），可以在行末添加 **两个或更多的空格**，然后按回车。

    ```markdown
    玫瑰是红的，
    紫罗兰是蓝的。
    # ↑ 在“的，”后面有两个空格
    ```

---

## 场景一：日常核心操作 —— 丰富文档内容

**目标 (Goal):** 在具备基本结构后，为文档添加最常用的元素：强调、列表、链接、图片和代码块。

### 流程/解决方案：

1.  **强调文本 (粗体与斜体)**
    * `*斜体*` 或 `_斜体_`
    * `**粗体**` 或 `__粗体__`
    * `***粗斜体***` 或 `___粗斜体___`

2.  **创建列表 (无序与有序)**
    * **无序列表**：使用 `*`、`+` 或 `-`。
    * **有序列表**：使用 `1.`、`2.`、`3.`。

3.  **引用文字**
    在行首使用 `>`。

4.  **插入链接与图片**
    * **链接**: `[链接显示的文字](链接地址)`
    * **图片**: `![图片的替代文字](图片地址)`

5.  **插入代码**
    * **行内代码**: 用反引号 `` ` `` 包围。
    * **代码块**: 用三个反引号 ```` 包围，并可指定语言。

---

## 场景二：进阶用法 —— 创建复杂的结构化内容

**目标 (Goal):** 当你需要撰写技术文档、报告或项目计划时，使用表格、任务列表等进阶功能。

> **版本提示：** 此场景中的许多功能属于 **扩展语法 (Extended Syntax)**，最常见于 GitHub Flavored Markdown (GFM)。并非所有基础的 Markdown 解析器都支持。

### 流程/解决方案：

1.  **创建表格**
    使用竖线 `|` 分隔单元格，使用连字符 `-` 创建表头分隔线。冒号 `:` 用于控制对齐。

    ```markdown
    | 功能      | 快捷键 (Windows) | 描述                  |
    | :-------- | :--------------: | --------------------: |
    | 复制      | `Ctrl+C`         | 将选中内容复制到剪贴板  |
    | 粘贴      | `Ctrl+V`         | 粘贴剪贴板中的内容    |
    ```

2.  **创建任务列表 (To-Do List)**
    在无序列表项的开头加上 `[ ]` (未完成) 或 `[x]` (已完成)。

    ```markdown
    # 项目发布计划
    - [x] 完成功能开发
    - [ ] 进行用户验收测试 (UAT)
    ```

3.  **添加删除线**
    使用两个波浪线 `~~` 包围文本。 `~~像这样~~`。

---

## 场景三：“糟糕，格式错了！” —— 疑难杂症与特殊处理

**目标 (Goal):** 我想输入 Markdown 的特殊符号（如 `*`、`#`），但不希望它们被解析为格式。

### 流程/解决方案：

**核心方法：使用反斜杠 `\` 进行转义。**

在任何有特殊含义的 Markdown 符号前，放一个 `\`，计算机会忽略该符号的特殊功能，将其作为普通文本处理。

| 你想输入的内容 | 你应该如何编写 | 渲染结果 |
| :------------- | :------------- | :------- |
| `*真的*`       | `\*真的\*`     | \*真的\* |
| `C#`           | `C\#`          | C\#      |
| `1. 项目`      | `1\. 项目`     | 1\. 项目 |

---

## 场景四：撰写技术与学术内容 —— 插入数学公式

**目标 (Goal):** 在文档中清晰地展示数学和科学公式。

> **版本提示：** 公式支持并非 Markdown 核心标准的一部分，甚至不属于 GFM。它依赖于平台或工具集成了特定的数学渲染引擎，最常见的是 **MathJax** 或 **KaTeX**。幸运的是，如今绝大多数技术写作平台（如 GitHub、GitLab、Notion、Jupyter Notebook）都支持这种语法。

### 流程/解决方案：

**核心方法：使用 LaTeX 语法。**

你可以在 Markdown 中无缝地嵌入 LaTeX 格式的数学公式。

1.  **插入行内公式 (Inline Formula)**
    将公式用一对 `$` 包围，它会嵌入到文本行中。

    ```markdown
    # 示例：
    爱因斯坦的质能方程是 $E=mc^2$，它揭示了质量和能量之间的关系。
    ```

2.  **插入块级公式 (Block Formula)**
    将公式用一对 `$$` 包围，它会单独成行并居中显示，更加突出。

    ```markdown
    # 示例：
    二次方程的求根公式如下：
    $$
    x = \frac{-b \pm \sqrt{b^2-4ac}}{2a}
    $$
    ```

### 常用 LaTeX 语法速查

| 类别 | 效果 | LaTeX 代码 |
| :--- | :--- | :--- |
| **上下标** | $X^2$ | `$X^2$` |
| | $X_i$ | `$X_i$` |
| | $log_2(N)$ | `$log_2(N)$` |
| **分数与根号**| $\frac{a}{b}$ | `$\frac{a}{b}$` |
| | $\sqrt[n]{x}$ | `$\sqrt[n]{x}$` |
| **希腊字母** | $\alpha, \beta, \gamma$ | `$\alpha, \beta, \gamma$` |
| | $\Delta, \Theta, \Sigma$ | `$\Delta, \Theta, \Sigma$` |
| **运算符** | $\pm, \times, \div$ | `$\pm, \times, \div$` |
| | $\approx, \neq, \leq, \geq$ | `$\approx, \neq, \leq, \geq$` |
| **求和与积分**| $\sum_{i=1}^{n} i$ | `$\sum_{i=1}^{n} i$` |
| | $\int_{a}^{b} f(x)dx$ | `$\int_{a}^{b} f(x)dx$` |
| **定界符** | $(\frac{a}{b})$ | `$(\frac{a}{b})$` |
| | $\left[\frac{a}{b}\right]$ | `$\left[\frac{a}{b}\right]$` |

> **专家经验：从哪里学习 LaTeX 数学语法？**
> LaTeX 的数学语法非常强大但也很庞大。你不必全部记住。当需要一个不熟悉的符号时，可以搜索 "LaTeX + [符号名称]"（例如 "LaTeX integral"）。有许多在线的 LaTeX 公式编辑器可以帮助你实时预览和生成代码。

---

## 附录：版本差异对比 (核心语法 vs. 扩展语法)

**核心理念：** "核心 Markdown" (或称 CommonMark) 是基础，保证了在任何地方的最大兼容性。"扩展语法" (以 GFM 为代表) 增加了便利性，但在某些旧的或特定的平台可能不支持。

| 功能 (Feature) | 核心 Markdown (CommonMark) | 扩展语法 (以 GFM 为例) | 注释 (Notes) |
| :------------- | :------------------------- | :----------------------------- | :--------------------------------------------------------------------------------------------------------------------------------- |
| **表格** | ❌ 不支持 | ✅ 支持 | 这是 GFM 最重要的扩展之一，对于技术文档至关重要。 |
| **任务列表** | ❌ 不支持 | ✅ 支持 | `- [x]` 形式的列表，在项目管理中极为有用。 |
| **删除线** | ❌ 不支持 | ✅ 支持 (`~~text~~`) | 用于表示过时或已删除的信息。 |
| **数学公式** | ❌ 不支持 | ✅ 广泛支持 (非官方) | 通过 MathJax/KaTeX 实现，是事实上的标准。 |
| **语法高亮** | ⚠️ 不在规范内 | ✅ 广泛支持 | 在代码块后加语言名 (` ```python `) 是事实上的标准。 |
| **自动链接** | ⚠️ 有限支持 | ✅ 广泛支持 | 在 GFM 中，直接写 `https://google.com` 会自动变成链接。 |
