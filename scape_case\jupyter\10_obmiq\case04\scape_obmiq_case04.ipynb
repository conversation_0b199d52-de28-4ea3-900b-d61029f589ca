{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ 端到端工作流 (PyTorch版)\n", "\n", "- 表格数据使用更多的输入曲线\n", "  - DEN, CN, DT, RD_LOG10, RS_LOG10, DRES\n", "  - T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10\n", "  - PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR\n", "  - VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO\n", "  - LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-30T02:46:19.368631Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 127.98, 'cpu_percent': 0.0}\n", "2025-07-30T02:46:30.694234Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 723.32, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-30T02:46:30.702815Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 723.32, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-30T02:46:30.729564Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 723.32, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-30T02:46:32.823835Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.54, 'cpu_percent': 0.0} operation=register_base_profile profile_name=obmiq.base\n", "2025-07-30T02:46:32.845375Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.6, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.training_history\n", "2025-07-30T02:46:32.864119Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.6, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.crossplot\n", "2025-07-30T02:46:32.896664Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.61, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_plot\n", "2025-07-30T02:46:32.917938Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.61, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_hist\n", "2025-07-30T02:46:32.946145Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.61, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.shap_summary\n", "2025-07-30T02:46:32.971284Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.61, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.captum_ig_summary\n", "2025-07-30T02:46:32.996881Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.61, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.grad_cam\n", "环境设置与导入完成。\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "from pathlib import Path\n", "\n", "# 导入 SCAPE 和 LOGWP 的核心组件\n", "from logwp.io import WpExcelReader\n", "from logwp.extras.tracking import RunContext\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "\n", "# 导入新版OBMIQ组件\n", "import scape.core.obmiq.plot_profiles # 导入以注册绘图模板\n", "from scape.core.obmiq import (\n", "    run_obmiq_training_step,\n", "    run_obmiq_prediction_step,\n", "    ObmiqTrainingConfig,\n", "    ObmiqPredictionConfig,\n", "    ObmiqTrainingArtifacts,\n", "    ObmiqArtifactHandler\n", ")\n", "from logwp.models.constants import WpDepthRole\n", "\n", "# 设置\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', 50)\n", "\n", "print(\"环境设置与导入完成。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T02:46:38.462824Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.68, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx\n", "2025-07-30T02:46:38.494465Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.91, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx file_size_mb=1.54 sheet_count=1\n", "2025-07-30T02:46:38.511067Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.93, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all\n", "2025-07-30T02:46:38.532208Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 780.95, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all\n", "2025-07-30T02:46:38.556132Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 781.32, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:46:38.587824Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 781.57, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=33 well_curves=1\n", "2025-07-30T02:46:41.777148Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.12, 'cpu_percent': 0.0} shape=(2651, 96) sheet_name=nmr_obmiq\n", "2025-07-30T02:46:41.823603Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.13, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq')\n", "2025-07-30T02:46:41.842402Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.13, 'cpu_percent': 0.0} curve_count=33 dataset_name=nmr_obmiq dataset_type=Continuous df_shape=(2651, 96) processing_time=3.294\n", "2025-07-30T02:46:41.873443Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.13, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-30T02:46:41.898424Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.13, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-30T02:46:41.912941Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.13, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx processing_time=3.45 project_name=WpIdentifier('santos_obmiq_cum_all')\n", "2025-07-30T02:46:41.941585Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.13, 'cpu_percent': 0.0} extracted_curve_count=31 operation=extract_curve_dataframe_bundle\n", "2025-07-30T02:46:41.989833Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 796.58, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['LKURT_FFI', 'RS_LOG10', 'DT', 'DT2_P50', 'LSKEW_FFI', 'T2LM_LOG10', 'DRES', 'WELL_NO', 'VMESO', 'LT2STDDEV_FFI', 'SDR_PROXY', 'VMICRO', 'SMACRO', 'PHI_T2_DIST_CUM', 'SMESO', 'FFV_NMR', 'SWI_NMR', 'VMACRO', 'T2_P50_LOG10', 'DEN', 'SWB_NMR', 'SFF_NMR', 'CN', 'T2_P20_LOG10', 'SMICRO', 'RD_LOG10', 'PHIT_NMR', 'BFV_NMR', 'MD', 'DPHIT_NMR', 'PHIE_NMR', 'T2LM_LONG_LOG10', 'BVI_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['LKURT_FFI', 'RS_LOG10', 'DT', 'DT2_P50', 'LSKEW_FFI', 'T2LM_LOG10', 'DRES', 'WELL_NO', 'VMESO', 'LT2STDDEV_FFI', 'SDR_PROXY', 'VMICRO', 'SMACRO', 'PHI_T2_DIST_CUM', 'SMESO', 'FFV_NMR', 'SWI_NMR', 'VMACRO', 'T2_P50_LOG10', 'DEN', 'SWB_NMR', 'SFF_NMR', 'CN', 'T2_P20_LOG10', 'SMICRO', 'RD_LOG10', 'PHIT_NMR', 'BFV_NMR', 'MD', 'DPHIT_NMR', 'PHIE_NMR', 'T2LM_LONG_LOG10', 'BVI_NMR']\n", "2025-07-30T02:46:42.015637Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 796.58, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx\n", "2025-07-30T02:46:42.028330Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 796.6, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx file_size_mb=2.54 sheet_count=1\n", "2025-07-30T02:46:42.042742Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 796.6, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all_apply\n", "2025-07-30T02:46:42.060553Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 796.6, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all_apply\n", "2025-07-30T02:46:42.074506Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 796.62, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T02:46:42.110448Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 796.63, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=33 well_curves=1\n", "2025-07-30T02:46:47.300639Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 805.2, 'cpu_percent': 0.0} shape=(4503, 96) sheet_name=nmr_obmiq_apply\n", "2025-07-30T02:46:47.334597Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 805.14, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_apply')\n", "2025-07-30T02:46:47.348941Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 805.14, 'cpu_percent': 0.0} curve_count=33 dataset_name=nmr_obmiq_apply dataset_type=Continuous df_shape=(4503, 96) processing_time=5.278\n", "2025-07-30T02:46:47.382642Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 805.14, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-30T02:46:47.407908Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 805.14, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-30T02:46:47.422763Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 805.14, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=5.407 project_name=WpIdentifier('santos_obmiq_cum_all_apply')\n", "2025-07-30T02:46:47.444697Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 805.14, 'cpu_percent': 0.0} extracted_curve_count=31 operation=extract_curve_dataframe_bundle\n", "2025-07-30T02:46:47.484963Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 812.61, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['LKURT_FFI', 'RS_LOG10', 'DT', 'DT2_P50', 'LSKEW_FFI', 'T2LM_LOG10', 'DRES', 'WELL_NO', 'VMESO', 'LT2STDDEV_FFI', 'SDR_PROXY', 'VMICRO', 'SMACRO', 'PHI_T2_DIST_CUM', 'SMESO', 'FFV_NMR', 'SWI_NMR', 'VMACRO', 'T2_P50_LOG10', 'DEN', 'SWB_NMR', 'SFF_NMR', 'CN', 'T2_P20_LOG10', 'SMICRO', 'RD_LOG10', 'PHIT_NMR', 'BFV_NMR', 'MD', 'DPHIT_NMR', 'PHIE_NMR', 'T2LM_LONG_LOG10', 'BVI_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['LKURT_FFI', 'RS_LOG10', 'DT', 'DT2_P50', 'LSKEW_FFI', 'T2LM_LOG10', 'DRES', 'WELL_NO', 'VMESO', 'LT2STDDEV_FFI', 'SDR_PROXY', 'VMICRO', 'SMACRO', 'PHI_T2_DIST_CUM', 'SMESO', 'FFV_NMR', 'SWI_NMR', 'VMACRO', 'T2_P50_LOG10', 'DEN', 'SWB_NMR', 'SFF_NMR', 'CN', 'T2_P20_LOG10', 'SMICRO', 'RD_LOG10', 'PHIT_NMR', 'BFV_NMR', 'MD', 'DPHIT_NMR', 'PHIE_NMR', 'T2LM_LONG_LOG10', 'BVI_NMR']\n", "数据加载成功。\n", "训练数据束: nmr_obmiq, 形状: (2651, 96), 井名: WELL_NO\n", "预测数据束: nmr_obmiq_apply, 形状: (4503, 96)\n", "T2时间轴长度: 64\n"]}], "source": ["# --- 请在此处填写您的数据路径 ---\n", "\n", "\n", "TRAIN_WP_FILE_PATH = \"santos_obmiq_cum_all.wp.xlsx\"\n", "TRAIN_DATASET_NAME = \"nmr_obmiq\"\n", "\n", "PRED_WP_FILE_PATH = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "PREDICTION_DATASET_NAME = \"nmr_obmiq_apply\"\n", "# ---------------------------------\n", "\n", "# 加载整个工区文件\n", "try:\n", "    # 获取训练和预测数据束\n", "    reader = WpExcelReader()\n", "    project = reader.read(TRAIN_WP_FILE_PATH)\n", "    train_ds = project.get_dataset(TRAIN_DATASET_NAME)\n", "    train_bundle = train_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "    well_no_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "    depth_name = train_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "    pred_reader = WpExcelReader()\n", "    pred_project = pred_reader.read(PRED_WP_FILE_PATH)\n", "    prediction_ds = pred_project.get_dataset(PREDICTION_DATASET_NAME)\n", "    prediction_bundle = prediction_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "\n", "    # 从 head_info 获取 T2 时间轴\n", "    # 假设所有井共享一个T2轴定义\n", "\n", "    t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "    t2_time_array = t2_axis_info.calculate_values()\n", "\n", "    print(\"数据加载成功。\")\n", "    print(f\"训练数据束: {train_ds.name}, 形状: {train_ds.df.shape}, 井名: {well_no_name}\")\n", "    print(f\"预测数据束: {prediction_ds.name}, 形状: {prediction_ds.df.shape}\")\n", "    print(f\"T2时间轴长度: {len(t2_time_array)}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 数据加载失败: {e}\")\n", "    project = None\n", "    pred_project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 运行 OBMIQ 工作流\n", "\n", "我们使用 `RunContext` 来包裹整个实验流程，以确保所有参数、指标和产物都被追踪。"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T02:48:02.832009Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 813.16, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\obmiq_run_pytorch_20250730_104802 run_id=20250730-024802-b761ade0\n", "--- 开始 OBMIQ 训练步骤 (PyTorch) ---\n", "2025-07-30T02:48:02.861354Z [info     ] ===== OBMIQ Training Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 813.16, 'cpu_percent': 0.0}\n", "2025-07-30T02:48:02.873380Z [info     ] 曲线名已成功解析为DataFrame列名。          [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 813.16, 'cpu_percent': 0.0}\n", "2025-07-30T02:48:02.889360Z [info     ] --- Stage 0: Saving Configuration Snapshot --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 813.16, 'cpu_percent': 0.0}\n", "2025-07-30T02:48:02.915966Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.configs.training_config_snapshot artifact_path=obmiq_training_pytorch\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 813.17, 'cpu_percent': 0.0} description=Snapshot of the training configuration used for this run. operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T02:48:02.946041Z [info     ] --- Stage 2: Hyperparameter Tuning using LOWO-CV --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 813.17, 'cpu_percent': 0.0}\n", "2025-07-30T02:48:03.042754Z [info     ] Tuning procedure started on device: cuda [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 813.34, 'cpu_percent': 0.0}\n", "2025-07-30T02:48:03.062862Z [info     ] --- Starting CV Fold 1/2 ---   [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 814.54, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-30 10:48:03,126] A new study created in memory with name: no-name-8b7d389e-3cc3-402b-9815-32793da92531\n", "[I 2025-07-30 10:48:32,630] Trial 0 finished with value: -7.147099399566651 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.3444265567601009, 'learning_rate': 0.0017651138593695326, 'weight_decay': 0.00024391707168990053}. Best is trial 0 with value: -7.147099399566651.\n", "[I 2025-07-30 10:48:58,469] Trial 1 finished with value: -9.162540817260743 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.38941415710629035, 'learning_rate': 0.0070965785468682, 'weight_decay': 0.00028929510858120074}. Best is trial 1 with value: -9.162540817260743.\n", "[I 2025-07-30 10:49:23,477] Trial 2 finished with value: -0.8260837912559509 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.5216038345151808, 'learning_rate': 0.00015446601903284675, 'weight_decay': 0.00045097036761793514}. Best is trial 1 with value: -9.162540817260743.\n", "[I 2025-07-30 10:49:48,712] Trial 3 finished with value: -2.121476888656616 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2043275956758787, 'learning_rate': 0.0003842187643331789, 'weight_decay': 3.231194892626722e-05}. Best is trial 1 with value: -9.162540817260743.\n", "[I 2025-07-30 10:50:13,591] Trial 4 finished with value: -7.379725933074951 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.46550907273481695, 'learning_rate': 0.0018350469169798988, 'weight_decay': 0.00012675426071758942}. Best is trial 1 with value: -9.162540817260743.\n", "[I 2025-07-30 10:50:13,775] Trial 5 pruned. \n", "[I 2025-07-30 10:50:39,516] Trial 6 finished with value: -8.905484199523926 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.5866609654339914, 'learning_rate': 0.003583848202914345, 'weight_decay': 0.00016816672967820355}. Best is trial 1 with value: -9.162540817260743.\n", "[I 2025-07-30 10:51:04,525] Trial 7 finished with value: -9.232821083068847 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.3360277966809969, 'learning_rate': 0.00556214149709175, 'weight_decay': 0.0009468475049836335}. Best is trial 7 with value: -9.232821083068847.\n", "[I 2025-07-30 10:51:04,677] Trial 8 pruned. \n", "[I 2025-07-30 10:51:04,857] Trial 9 pruned. \n", "[I 2025-07-30 10:51:21,285] Trial 10 finished with value: -9.08420238494873 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.2804083522618132, 'learning_rate': 0.009412136953297963, 'weight_decay': 0.0009762083667052755}. Best is trial 7 with value: -9.232821083068847.\n", "[I 2025-07-30 10:51:45,608] Trial 11 finished with value: -9.141224479675293 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.3137051599560298, 'learning_rate': 0.008656117762174505, 'weight_decay': 0.0008387221545262362}. Best is trial 7 with value: -9.232821083068847.\n", "[I 2025-07-30 10:52:07,482] Trial 12 finished with value: -9.127252769470214 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.42283818134203593, 'learning_rate': 0.004924478235288899, 'weight_decay': 0.0003687707831914543}. Best is trial 7 with value: -9.232821083068847.\n", "[I 2025-07-30 10:52:09,030] Trial 13 pruned. \n", "[I 2025-07-30 10:52:09,228] Trial 14 pruned. \n", "[I 2025-07-30 10:52:30,037] Trial 15 finished with value: -9.098163223266601 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.4312959921104657, 'learning_rate': 0.006782736419802552, 'weight_decay': 0.000612828888312091}. Best is trial 7 with value: -9.232821083068847.\n", "[I 2025-07-30 10:52:30,248] Trial 16 pruned. \n", "[I 2025-07-30 10:52:30,491] Trial 17 pruned. \n", "[I 2025-07-30 10:52:53,513] Trial 18 finished with value: -9.391091346740723 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.33033968226553023, 'learning_rate': 0.0062989060461867315, 'weight_decay': 0.0008180049224091088}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:52:53,720] Trial 19 pruned. \n", "[I 2025-07-30 10:52:53,950] Trial 20 pruned. \n", "[I 2025-07-30 10:53:12,907] Trial 21 finished with value: -9.161478042602539 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.4113935295169036, 'learning_rate': 0.0065267911391880835, 'weight_decay': 0.0003290001605709075}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:53:13,094] Trial 22 pruned. \n", "[I 2025-07-30 10:53:30,887] Trial 23 finished with value: -9.078058052062989 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.4516826908786279, 'learning_rate': 0.009593729803902868, 'weight_decay': 0.0009798908898857315}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:53:56,403] Trial 24 finished with value: -9.25710849761963 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.26380289894485315, 'learning_rate': 0.006137829038959823, 'weight_decay': 0.0002372333746182972}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:53:56,617] Trial 25 pruned. \n", "[I 2025-07-30 10:53:56,844] Trial 26 pruned. \n", "[I 2025-07-30 10:53:57,110] Trial 27 pruned. \n", "[I 2025-07-30 10:53:58,270] Trial 28 pruned. \n", "[I 2025-07-30 10:53:58,458] Trial 29 pruned. \n", "[I 2025-07-30 10:53:58,659] Trial 30 pruned. \n", "[I 2025-07-30 10:54:03,526] Trial 31 pruned. \n", "[I 2025-07-30 10:54:27,748] Trial 32 finished with value: -9.165070724487304 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.3880019614399794, 'learning_rate': 0.007790995313518663, 'weight_decay': 0.00046845028049150814}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:54:45,403] Trial 33 finished with value: -9.104642295837403 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.38339770334700607, 'learning_rate': 0.008067896102532451, 'weight_decay': 0.00047657316848961163}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:54:45,544] Trial 34 pruned. \n", "[I 2025-07-30 10:54:45,730] Trial 35 pruned. \n", "[I 2025-07-30 10:55:06,947] Trial 36 finished with value: -9.209078788757324 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.31041351143338036, 'learning_rate': 0.009694015106037282, 'weight_decay': 0.000535605290119864}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:55:24,131] Trial 37 finished with value: -9.1409517288208 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.22584098655919987, 'learning_rate': 0.009905697478507126, 'weight_decay': 0.00014027419066107657}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:55:24,311] Trial 38 pruned. \n", "[I 2025-07-30 10:55:24,473] Trial 39 pruned. \n", "[I 2025-07-30 10:55:24,664] Trial 40 pruned. \n", "[I 2025-07-30 10:55:38,699] Trial 41 finished with value: -9.101975440979004 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.3669017004577973, 'learning_rate': 0.0076314095241374145, 'weight_decay': 0.0005101929017453745}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:55:38,866] Trial 42 pruned. \n", "[I 2025-07-30 10:55:39,068] Trial 43 pruned. \n", "[I 2025-07-30 10:55:39,230] Trial 44 pruned. \n", "[I 2025-07-30 10:55:39,467] Trial 45 pruned. \n", "[I 2025-07-30 10:55:53,040] Trial 46 pruned. \n", "[I 2025-07-30 10:55:53,257] Trial 47 pruned. \n", "[I 2025-07-30 10:56:09,933] Trial 48 finished with value: -9.126102447509766 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.4763647961938905, 'learning_rate': 0.007945148142676129, 'weight_decay': 0.00032168571890649764}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:56:10,111] Trial 49 pruned. \n", "[I 2025-07-30 10:56:10,287] Trial 50 pruned. \n", "[I 2025-07-30 10:56:19,585] Trial 51 pruned. \n", "[I 2025-07-30 10:56:19,922] Trial 52 pruned. \n", "[I 2025-07-30 10:56:20,106] Trial 53 pruned. \n", "[I 2025-07-30 10:56:20,280] Trial 54 pruned. \n", "[I 2025-07-30 10:56:20,471] Trial 55 pruned. \n", "[I 2025-07-30 10:56:35,395] Trial 56 finished with value: -9.237943840026855 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.24229259588343735, 'learning_rate': 0.008549507055519484, 'weight_decay': 0.00011362323046670513}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:56:52,942] Trial 57 finished with value: -9.297460556030273 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.24042989666746561, 'learning_rate': 0.008589904707500894, 'weight_decay': 9.504339635389262e-05}. Best is trial 18 with value: -9.391091346740723.\n", "[I 2025-07-30 10:57:09,641] Trial 58 finished with value: -9.400118255615235 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23201845834900503, 'learning_rate': 0.008728763656466987, 'weight_decay': 9.801538154390444e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:57:09,816] Trial 59 pruned. \n", "[I 2025-07-30 10:57:22,826] Trial 60 finished with value: -9.118267822265626 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.22194610101528361, 'learning_rate': 0.008678774222764224, 'weight_decay': 9.561844677973471e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:57:34,917] Trial 61 finished with value: -9.302987480163575 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.26593491809870606, 'learning_rate': 0.00913970692208547, 'weight_decay': 0.00011763687747187332}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:57:35,092] Trial 62 pruned. \n", "[I 2025-07-30 10:57:49,803] Trial 63 finished with value: -9.247849464416504 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2385469441287431, 'learning_rate': 0.00877279443157734, 'weight_decay': 7.601200729475271e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:58:03,608] Trial 64 finished with value: -9.351416206359863 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23645996497498992, 'learning_rate': 0.008658666685209355, 'weight_decay': 7.044505374111042e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:58:03,782] Trial 65 pruned. \n", "[I 2025-07-30 10:58:03,941] Trial 66 pruned. \n", "[I 2025-07-30 10:58:24,100] Trial 67 finished with value: -9.251182365417481 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2312478374323097, 'learning_rate': 0.00884882470045082, 'weight_decay': 9.423250882130177e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:58:24,289] Trial 68 pruned. \n", "[I 2025-07-30 10:58:33,289] Trial 69 pruned. \n", "[I 2025-07-30 10:58:33,684] Trial 70 pruned. \n", "[I 2025-07-30 10:58:41,821] Trial 71 pruned. \n", "[I 2025-07-30 10:58:56,383] Trial 72 finished with value: -9.283538627624512 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23305313997014807, 'learning_rate': 0.00868716021159355, 'weight_decay': 8.546241039427964e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:58:56,563] Trial 73 pruned. \n", "[I 2025-07-30 10:59:04,647] Trial 74 pruned. \n", "[I 2025-07-30 10:59:04,972] Trial 75 pruned. \n", "[I 2025-07-30 10:59:05,132] Trial 76 pruned. \n", "[I 2025-07-30 10:59:05,323] Trial 77 pruned. \n", "[I 2025-07-30 10:59:05,496] Trial 78 pruned. \n", "[I 2025-07-30 10:59:05,665] Trial 79 pruned. \n", "[I 2025-07-30 10:59:05,823] Trial 80 pruned. \n", "[I 2025-07-30 10:59:17,983] Trial 81 finished with value: -9.298054695129395 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.21576282377829978, 'learning_rate': 0.008770013109878969, 'weight_decay': 6.562399228001153e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:59:18,177] Trial 82 pruned. \n", "[I 2025-07-30 10:59:35,878] Trial 83 finished with value: -9.195323753356934 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2596043803447337, 'learning_rate': 0.009990026925259998, 'weight_decay': 4.888325853510386e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:59:36,069] Trial 84 pruned. \n", "[I 2025-07-30 10:59:36,414] Trial 85 pruned. \n", "[I 2025-07-30 10:59:36,602] Trial 86 pruned. \n", "[I 2025-07-30 10:59:36,792] Trial 87 pruned. \n", "[I 2025-07-30 10:59:53,392] Trial 88 finished with value: -9.285220909118653 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.24851067030308568, 'learning_rate': 0.008917278683370047, 'weight_decay': 6.118867584337857e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 10:59:53,572] Trial 89 pruned. \n", "[I 2025-07-30 10:59:53,875] Trial 90 pruned. \n", "[I 2025-07-30 10:59:55,404] Trial 91 pruned. \n", "[I 2025-07-30 10:59:55,579] Trial 92 pruned. \n", "[I 2025-07-30 10:59:55,770] Trial 93 pruned. \n", "[I 2025-07-30 10:59:55,940] Trial 94 pruned. \n", "[I 2025-07-30 11:00:09,215] Trial 95 finished with value: -9.221585273742676 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2955147136332407, 'learning_rate': 0.009096140375298975, 'weight_decay': 0.0001612958168642856}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:00:09,405] Trial 96 pruned. \n", "[I 2025-07-30 11:00:09,653] Trial 97 pruned. \n", "[I 2025-07-30 11:00:09,827] Trial 98 pruned. \n", "[I 2025-07-30 11:00:18,490] Trial 99 pruned. \n", "[I 2025-07-30 11:00:18,688] Trial 100 pruned. \n", "[I 2025-07-30 11:00:19,161] Trial 101 pruned. \n", "[I 2025-07-30 11:00:19,336] Trial 102 pruned. \n", "[I 2025-07-30 11:00:33,095] Trial 103 finished with value: -9.170259475708008 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.26213680255749294, 'learning_rate': 0.00925081359399879, 'weight_decay': 6.683192904391465e-05}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:00:33,269] Trial 104 pruned. \n", "[I 2025-07-30 11:00:33,454] Trial 105 pruned. \n", "[I 2025-07-30 11:00:33,597] Trial 106 pruned. \n", "[I 2025-07-30 11:00:33,773] Trial 107 pruned. \n", "[I 2025-07-30 11:00:51,871] Trial 108 finished with value: -9.35390510559082 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.25558203403853447, 'learning_rate': 0.009935912443761446, 'weight_decay': 0.00018818165593016199}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:01:06,010] Trial 109 finished with value: -9.161477088928223 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2767039107547668, 'learning_rate': 0.009956626909308474, 'weight_decay': 0.00018003472476084794}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:01:06,169] Trial 110 pruned. \n", "[I 2025-07-30 11:01:06,347] Trial 111 pruned. \n", "[I 2025-07-30 11:01:21,262] Trial 112 finished with value: -9.209238243103027 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.2506765280270634, 'learning_rate': 0.009388149147347199, 'weight_decay': 0.00012822434481729486}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:01:21,434] Trial 113 pruned. \n", "[I 2025-07-30 11:01:21,609] Trial 114 pruned. \n", "[I 2025-07-30 11:01:21,816] Trial 115 pruned. \n", "[I 2025-07-30 11:01:24,000] Trial 116 pruned. \n", "[I 2025-07-30 11:01:24,161] Trial 117 pruned. \n", "[I 2025-07-30 11:01:24,345] Trial 118 pruned. \n", "[I 2025-07-30 11:01:24,523] Trial 119 pruned. \n", "[I 2025-07-30 11:01:24,715] Trial 120 pruned. \n", "[I 2025-07-30 11:01:25,760] Trial 121 pruned. \n", "[I 2025-07-30 11:01:25,958] Trial 122 pruned. \n", "[I 2025-07-30 11:01:41,689] Trial 123 finished with value: -9.262327575683594 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.25314463508258234, 'learning_rate': 0.009547050652228902, 'weight_decay': 0.00013519860891435264}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:01:58,450] Trial 124 finished with value: -9.210415649414063 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.25743901104035166, 'learning_rate': 0.009592911434848746, 'weight_decay': 0.00013531044687448032}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:01:58,631] Trial 125 pruned. \n", "[I 2025-07-30 11:02:11,396] Trial 126 finished with value: -9.128724288940429 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.25191727379564877, 'learning_rate': 0.009928622743120095, 'weight_decay': 0.0001954258185291773}. Best is trial 58 with value: -9.400118255615235.\n", "[I 2025-07-30 11:02:11,582] Trial 127 pruned. \n", "[I 2025-07-30 11:02:11,742] Trial 128 pruned. \n", "[I 2025-07-30 11:02:11,916] Trial 129 pruned. \n", "[I 2025-07-30 11:02:29,715] Trial 130 finished with value: -9.430588531494141 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20035507957244322, 'learning_rate': 0.008984050100529915, 'weight_decay': 6.592823138251963e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:02:30,852] Trial 131 pruned. \n", "[I 2025-07-30 11:02:50,261] Trial 132 finished with value: -9.275055694580079 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2176785884536581, 'learning_rate': 0.009879424660259506, 'weight_decay': 6.801220124209681e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:03:01,529] Trial 133 finished with value: -9.110983657836915 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.21636387499701687, 'learning_rate': 0.009480257477222, 'weight_decay': 6.68267603461827e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:03:20,685] Trial 134 finished with value: -9.38723201751709 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20149336874572576, 'learning_rate': 0.009975321612530523, 'weight_decay': 5.9960876370536885e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:03:20,844] Trial 135 pruned. \n", "[I 2025-07-30 11:03:36,630] Trial 136 finished with value: -9.324541664123535 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20058597726323857, 'learning_rate': 0.00991562547850295, 'weight_decay': 5.2294916048143025e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:03:57,161] Trial 137 finished with value: -9.289596939086914 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20919673907176778, 'learning_rate': 0.009997240678157424, 'weight_decay': 4.076314567691407e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:04:12,091] Trial 138 finished with value: -9.388993835449218 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20885645618255616, 'learning_rate': 0.009938371973666739, 'weight_decay': 3.743712760332546e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:04:12,265] Trial 139 pruned. \n", "[I 2025-07-30 11:04:12,447] Trial 140 pruned. \n", "[I 2025-07-30 11:04:18,450] Trial 141 pruned. \n", "[I 2025-07-30 11:04:38,416] Trial 142 finished with value: -9.362843894958496 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20970660545272468, 'learning_rate': 0.00982842040057349, 'weight_decay': 2.5802615909206843e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:04:54,380] Trial 143 finished with value: -9.24487075805664 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20021933197554762, 'learning_rate': 0.009940268448738559, 'weight_decay': 2.7142624605856608e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:04:54,570] Trial 144 pruned. \n", "[I 2025-07-30 11:05:08,376] Trial 145 finished with value: -9.252269554138184 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2244086468183869, 'learning_rate': 0.009990376776672116, 'weight_decay': 2.1745184880692725e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:05:08,550] Trial 146 pruned. \n", "[I 2025-07-30 11:05:08,802] Trial 147 pruned. \n", "[I 2025-07-30 11:05:09,254] Trial 148 pruned. \n", "[I 2025-07-30 11:05:09,443] Trial 149 pruned. \n", "[I 2025-07-30 11:05:09,644] Trial 150 pruned. \n", "[I 2025-07-30 11:05:09,837] Trial 151 pruned. \n", "[I 2025-07-30 11:05:31,048] Trial 152 finished with value: -9.42661075592041 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.22872739047781732, 'learning_rate': 0.009994493601291208, 'weight_decay': 2.380358393140218e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:05:31,210] Trial 153 pruned. \n", "[I 2025-07-30 11:05:31,389] Trial 154 pruned. \n", "[I 2025-07-30 11:05:44,779] Trial 155 finished with value: -9.280929374694825 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.22501046688672127, 'learning_rate': 0.009991199485449366, 'weight_decay': 1.8561733282441272e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:05:44,957] Trial 156 pruned. \n", "[I 2025-07-30 11:05:45,199] Trial 157 pruned. \n", "[I 2025-07-30 11:05:45,375] Trial 158 pruned. \n", "[I 2025-07-30 11:05:45,550] Trial 159 pruned. \n", "[I 2025-07-30 11:06:01,624] Trial 160 finished with value: -9.165678215026855 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.24461397291966222, 'learning_rate': 0.009996958422399544, 'weight_decay': 2.3773848191901817e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:06:14,457] Trial 161 finished with value: -9.323103523254394 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.22696296822356246, 'learning_rate': 0.009260155047548568, 'weight_decay': 1.9587001046993537e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:06:15,069] Trial 162 pruned. \n", "[I 2025-07-30 11:06:15,231] Trial 163 pruned. \n", "[I 2025-07-30 11:06:15,429] Trial 164 pruned. \n", "[I 2025-07-30 11:06:15,605] Trial 165 pruned. \n", "[I 2025-07-30 11:06:15,780] Trial 166 pruned. \n", "[I 2025-07-30 11:06:15,973] Trial 167 pruned. \n", "[I 2025-07-30 11:06:16,675] Trial 168 pruned. \n", "[I 2025-07-30 11:06:16,863] Trial 169 pruned. \n", "[I 2025-07-30 11:06:17,079] Trial 170 pruned. \n", "[I 2025-07-30 11:06:31,582] Trial 171 finished with value: -9.301978492736817 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2254323791785283, 'learning_rate': 0.009877368561718109, 'weight_decay': 1.7532209624143172e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:06:42,690] Trial 172 finished with value: -9.095804405212402 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.3583719220557093, 'learning_rate': 0.009904242583628018, 'weight_decay': 7.434648231100661e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:07:03,660] Trial 173 finished with value: -9.26708106994629 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.22216852204689413, 'learning_rate': 0.00996290709936454, 'weight_decay': 2.0690795106338198e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:07:03,834] Trial 174 pruned. \n", "[I 2025-07-30 11:07:04,008] Trial 175 pruned. \n", "[I 2025-07-30 11:07:04,177] Trial 176 pruned. \n", "[I 2025-07-30 11:07:04,355] Trial 177 pruned. \n", "[I 2025-07-30 11:07:04,542] Trial 178 pruned. \n", "[I 2025-07-30 11:07:04,922] Trial 179 pruned. \n", "[I 2025-07-30 11:07:05,158] Trial 180 pruned. \n", "[I 2025-07-30 11:07:22,505] Trial 181 finished with value: -9.244888305664062 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.22210202278076976, 'learning_rate': 0.009924305851767882, 'weight_decay': 1.9303860789799965e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:07:22,829] Trial 182 pruned. \n", "[I 2025-07-30 11:07:23,005] Trial 183 pruned. \n", "[I 2025-07-30 11:07:39,885] Trial 184 finished with value: -9.240827751159667 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2000617654753208, 'learning_rate': 0.009881796899013378, 'weight_decay': 2.224919063059289e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:07:52,380] Trial 185 finished with value: -9.20450553894043 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.21503344313719733, 'learning_rate': 0.009972293342866367, 'weight_decay': 1.6642813674273285e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:07:52,555] Trial 186 pruned. \n", "[I 2025-07-30 11:07:52,745] Trial 187 pruned. \n", "[I 2025-07-30 11:07:52,913] Trial 188 pruned. \n", "[I 2025-07-30 11:07:53,103] Trial 189 pruned. \n", "[I 2025-07-30 11:08:09,163] Trial 190 finished with value: -9.37194881439209 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2075784526539405, 'learning_rate': 0.009998831110260497, 'weight_decay': 2.5391747939810067e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:08:21,506] Trial 191 finished with value: -9.24626121520996 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2077924840534571, 'learning_rate': 0.009954881105028832, 'weight_decay': 2.2766271514048612e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:08:21,708] Trial 192 pruned. \n", "[I 2025-07-30 11:08:40,616] Trial 193 finished with value: -9.24212188720703 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2001382915933681, 'learning_rate': 0.009980947662161396, 'weight_decay': 2.6050975602243695e-05}. Best is trial 130 with value: -9.430588531494141.\n", "[I 2025-07-30 11:08:40,803] Trial 194 pruned. \n", "[I 2025-07-30 11:08:41,117] Trial 195 pruned. \n", "[I 2025-07-30 11:08:41,303] Trial 196 pruned. \n", "[I 2025-07-30 11:08:41,461] Trial 197 pruned. \n", "[I 2025-07-30 11:08:41,647] Trial 198 pruned. \n", "[I 2025-07-30 11:08:58,919] Trial 199 finished with value: -9.351162147521972 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.2336160107963634, 'learning_rate': 0.009992056247457295, 'weight_decay': 9.863625176307966e-05}. Best is trial 130 with value: -9.430588531494141.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-30T03:08:58.919362Z [info     ] Fold 1 best trial: value=-9.4306, params={'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.20035507957244322, 'learning_rate': 0.008984050100529915, 'weight_decay': 6.592823138251963e-05} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1318.75, 'cpu_percent': 0.0}\n", "2025-07-30T03:08:58.951330Z [info     ] --- Performing blind test for Fold 1 on well 'C-1' --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1318.75, 'cpu_percent': 0.0}\n", "2025-07-30T03:09:15.369617Z [info     ] --- Starting CV Fold 2/2 ---   [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1323.57, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-30 11:09:15,414] A new study created in memory with name: no-name-6e59fbf7-f739-4760-ac98-9a940ad7d79c\n", "[I 2025-07-30 11:09:30,336] Trial 0 finished with value: -8.791361331939697 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.2316129463508589, 'learning_rate': 0.007274757455518377, 'weight_decay': 2.568748516494324e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:09:45,591] Trial 1 finished with value: -0.44848108291625977 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.5498931807435539, 'learning_rate': 0.00014402284333425543, 'weight_decay': 0.0007886763322061447}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:09:53,161] Trial 2 finished with value: -7.721673607826233 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.2792036218922839, 'learning_rate': 0.00999409431441075, 'weight_decay': 0.00015299019294429136}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:10:08,054] Trial 3 finished with value: -0.716193288564682 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.5424344652854387, 'learning_rate': 0.00021265317962162537, 'weight_decay': 3.414701624306092e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:10:23,190] Trial 4 finished with value: -0.74796561896801 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.28039463990897284, 'learning_rate': 0.00020912585907607878, 'weight_decay': 2.3516585694936628e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:10:23,285] Trial 5 pruned. \n", "[I 2025-07-30 11:10:38,570] Trial 6 finished with value: -7.846475124359131 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.33720917517030474, 'learning_rate': 0.0032887634555257656, 'weight_decay': 0.0002151089369023035}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:10:54,007] Trial 7 finished with value: -7.53733229637146 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.2032022465242002, 'learning_rate': 0.0030425134921642596, 'weight_decay': 0.00045876472007193184}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:10:54,134] Trial 8 pruned. \n", "[I 2025-07-30 11:10:54,245] Trial 9 pruned. \n", "[I 2025-07-30 11:10:54,388] Trial 10 pruned. \n", "[I 2025-07-30 11:11:07,809] Trial 11 finished with value: -8.453052282333374 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.36969150876887824, 'learning_rate': 0.009081467535135588, 'weight_decay': 0.00017911144816267288}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:11:20,975] Trial 12 finished with value: -8.357372879981995 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4158678025044368, 'learning_rate': 0.008489722227256599, 'weight_decay': 5.3514200716092546e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:11:35,274] Trial 13 finished with value: -8.29056966304779 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.35548603269488016, 'learning_rate': 0.005022746493054395, 'weight_decay': 0.00023722503952367083}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:11:35,392] Trial 14 pruned. \n", "[I 2025-07-30 11:11:35,520] Trial 15 pruned. \n", "[I 2025-07-30 11:11:50,709] Trial 16 finished with value: -8.074357509613037 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.2539305209263084, 'learning_rate': 0.005550750709030418, 'weight_decay': 4.7553307375159596e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:11:50,869] Trial 17 pruned. \n", "[I 2025-07-30 11:11:50,996] Trial 18 pruned. \n", "[I 2025-07-30 11:11:51,128] Trial 19 pruned. \n", "[I 2025-07-30 11:11:51,249] Trial 20 pruned. \n", "[I 2025-07-30 11:12:05,116] Trial 21 finished with value: -8.513570070266724 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.45481947505639986, 'learning_rate': 0.00971507611981487, 'weight_decay': 4.44929176962915e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:12:19,606] Trial 22 finished with value: -8.383997082710266 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4615315580811714, 'learning_rate': 0.006943445431528107, 'weight_decay': 3.8044849500792055e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:12:19,734] Trial 23 pruned. \n", "[I 2025-07-30 11:12:31,664] Trial 24 finished with value: -8.252974033355713 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.38987862155009606, 'learning_rate': 0.00945875185267326, 'weight_decay': 2.81245513001905e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:12:46,182] Trial 25 finished with value: -8.58811616897583 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.30813505539084757, 'learning_rate': 0.006495117376197385, 'weight_decay': 7.754367048075104e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:12:46,325] Trial 26 pruned. \n", "[I 2025-07-30 11:12:46,458] Trial 27 pruned. \n", "[I 2025-07-30 11:13:01,435] Trial 28 finished with value: -8.455116391181946 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.24222840807704601, 'learning_rate': 0.0069934161972444635, 'weight_decay': 1.4070869495946721e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:13:01,578] Trial 29 pruned. \n", "[I 2025-07-30 11:13:01,705] Trial 30 pruned. \n", "[I 2025-07-30 11:13:02,582] Trial 31 pruned. \n", "[I 2025-07-30 11:13:02,724] Trial 32 pruned. \n", "[I 2025-07-30 11:13:14,649] Trial 33 finished with value: -8.206039786338806 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.28017993706157907, 'learning_rate': 0.007247851068802369, 'weight_decay': 1.2329709707672364e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:13:24,344] Trial 34 finished with value: -8.375992774963379 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2913298869629559, 'learning_rate': 0.009344690131911549, 'weight_decay': 2.4514243886755043e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:13:24,501] Trial 35 pruned. \n", "[I 2025-07-30 11:13:24,618] Trial 36 pruned. \n", "[I 2025-07-30 11:13:24,746] Trial 37 pruned. \n", "[I 2025-07-30 11:13:24,879] Trial 38 pruned. \n", "[I 2025-07-30 11:13:25,019] Trial 39 pruned. \n", "[I 2025-07-30 11:13:25,134] Trial 40 pruned. \n", "[I 2025-07-30 11:13:39,016] Trial 41 finished with value: -8.45755648612976 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4564762060344146, 'learning_rate': 0.009440530033970234, 'weight_decay': 0.0002024652893319296}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:13:53,488] Trial 42 finished with value: -8.43765926361084 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.45395592740755597, 'learning_rate': 0.009886868569614288, 'weight_decay': 0.0002962936362265389}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:13:53,632] Trial 43 pruned. \n", "[I 2025-07-30 11:13:53,780] Trial 44 pruned. \n", "[I 2025-07-30 11:13:53,907] Trial 45 pruned. \n", "[I 2025-07-30 11:14:05,050] Trial 46 finished with value: -8.279338836669922 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.40002434412495125, 'learning_rate': 0.008080007091086049, 'weight_decay': 6.643646145628903e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:14:05,203] Trial 47 pruned. \n", "[I 2025-07-30 11:14:05,336] Trial 48 pruned. \n", "[I 2025-07-30 11:14:16,358] Trial 49 finished with value: -8.263881087303162 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2958284908376441, 'learning_rate': 0.00989355859505016, 'weight_decay': 2.0225191842821597e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:14:29,664] Trial 50 finished with value: -8.40974485874176 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.4615879851256363, 'learning_rate': 0.00792239644021381, 'weight_decay': 8.469308952650909e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:14:29,807] Trial 51 pruned. \n", "[I 2025-07-30 11:14:39,804] Trial 52 finished with value: -8.312795162200928 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4062379520347686, 'learning_rate': 0.008868809609234168, 'weight_decay': 0.0002739330954480613}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:14:39,937] Trial 53 pruned. \n", "[I 2025-07-30 11:14:40,064] Trial 54 pruned. \n", "[I 2025-07-30 11:14:40,191] Trial 55 pruned. \n", "[I 2025-07-30 11:14:40,319] Trial 56 pruned. \n", "[I 2025-07-30 11:14:40,455] Trial 57 pruned. \n", "[I 2025-07-30 11:14:40,597] Trial 58 pruned. \n", "[I 2025-07-30 11:14:40,724] Trial 59 pruned. \n", "[I 2025-07-30 11:14:40,850] Trial 60 pruned. \n", "[I 2025-07-30 11:14:51,278] Trial 61 finished with value: -8.26806914806366 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.45156229214099264, 'learning_rate': 0.009763566897035149, 'weight_decay': 0.0003129909185600325}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:14:51,404] Trial 62 pruned. \n", "[I 2025-07-30 11:14:51,548] Trial 63 pruned. \n", "[I 2025-07-30 11:15:01,738] Trial 64 finished with value: -8.281853079795837 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4203687856683075, 'learning_rate': 0.009889469000748528, 'weight_decay': 0.0004770706170190049}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:15:03,892] Trial 65 pruned. \n", "[I 2025-07-30 11:15:04,047] Trial 66 pruned. \n", "[I 2025-07-30 11:15:04,170] Trial 67 pruned. \n", "[I 2025-07-30 11:15:13,790] Trial 68 finished with value: -8.323854207992554 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.214320350345206, 'learning_rate': 0.008768157801385462, 'weight_decay': 7.824416191610214e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:15:13,905] Trial 69 pruned. \n", "[I 2025-07-30 11:15:14,064] Trial 70 pruned. \n", "[I 2025-07-30 11:15:14,286] Trial 71 pruned. \n", "[I 2025-07-30 11:15:14,437] Trial 72 pruned. \n", "[I 2025-07-30 11:15:16,002] Trial 73 pruned. \n", "[I 2025-07-30 11:15:28,870] Trial 74 finished with value: -8.367659330368042 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.41025582811304956, 'learning_rate': 0.009872628803134905, 'weight_decay': 0.0002844858500336836}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:15:29,009] Trial 75 pruned. \n", "[I 2025-07-30 11:15:29,146] Trial 76 pruned. \n", "[I 2025-07-30 11:15:29,271] Trial 77 pruned. \n", "[I 2025-07-30 11:15:29,400] Trial 78 pruned. \n", "[I 2025-07-30 11:15:29,538] Trial 79 pruned. \n", "[I 2025-07-30 11:15:29,700] Trial 80 pruned. \n", "[I 2025-07-30 11:15:31,731] Trial 81 pruned. \n", "[I 2025-07-30 11:15:31,861] Trial 82 pruned. \n", "[I 2025-07-30 11:15:32,019] Trial 83 pruned. \n", "[I 2025-07-30 11:15:32,162] Trial 84 pruned. \n", "[I 2025-07-30 11:15:32,306] Trial 85 pruned. \n", "[I 2025-07-30 11:15:32,441] Trial 86 pruned. \n", "[I 2025-07-30 11:15:42,896] Trial 87 finished with value: -8.044927954673767 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4840967373296042, 'learning_rate': 0.009197356941450444, 'weight_decay': 1.459991170051107e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:15:43,031] Trial 88 pruned. \n", "[I 2025-07-30 11:15:43,173] Trial 89 pruned. \n", "[I 2025-07-30 11:15:43,300] Trial 90 pruned. \n", "[I 2025-07-30 11:15:54,999] Trial 91 finished with value: -8.422903418540955 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.29701898501999185, 'learning_rate': 0.009068903505756877, 'weight_decay': 2.4388589845215586e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:16:04,689] Trial 92 finished with value: -8.264726996421814 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2409427464524658, 'learning_rate': 0.00913181753947693, 'weight_decay': 2.2262208123023076e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:16:19,495] Trial 93 finished with value: -8.338701605796814 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.30095074653910875, 'learning_rate': 0.009987317945785614, 'weight_decay': 2.6081642737239726e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:16:19,638] Trial 94 pruned. \n", "[I 2025-07-30 11:16:19,806] Trial 95 pruned. \n", "[I 2025-07-30 11:16:19,941] Trial 96 pruned. \n", "[I 2025-07-30 11:16:20,090] Trial 97 pruned. \n", "[I 2025-07-30 11:16:20,217] Trial 98 pruned. \n", "[I 2025-07-30 11:16:20,344] Trial 99 pruned. \n", "[I 2025-07-30 11:16:20,474] Trial 100 pruned. \n", "[I 2025-07-30 11:16:20,624] Trial 101 pruned. \n", "[I 2025-07-30 11:16:20,882] Trial 102 pruned. \n", "[I 2025-07-30 11:16:33,754] Trial 103 finished with value: -8.507723569869995 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2716071183984574, 'learning_rate': 0.009124731622746605, 'weight_decay': 2.9139899082106315e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:16:33,909] Trial 104 pruned. \n", "[I 2025-07-30 11:16:46,711] Trial 105 finished with value: -8.476041078567505 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.23343398067491805, 'learning_rate': 0.009952091792811878, 'weight_decay': 3.488423033361897e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:16:48,205] Trial 106 pruned. \n", "[I 2025-07-30 11:16:48,333] Trial 107 pruned. \n", "[I 2025-07-30 11:16:48,476] Trial 108 pruned. \n", "[I 2025-07-30 11:16:58,716] Trial 109 finished with value: -8.460381388664246 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.21570860202516096, 'learning_rate': 0.009960869731358337, 'weight_decay': 0.0005836984876393541}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:16:58,941] Trial 110 pruned. \n", "[I 2025-07-30 11:17:09,716] Trial 111 finished with value: -8.452612161636353 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.21749751125786085, 'learning_rate': 0.009875139861494906, 'weight_decay': 0.0009765091197644188}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:17:18,010] Trial 112 finished with value: -8.202580213546753 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.20901903639659186, 'learning_rate': 0.009906173775222803, 'weight_decay': 0.0007448270297121716}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:17:18,141] Trial 113 pruned. \n", "[I 2025-07-30 11:17:31,488] Trial 114 finished with value: -8.34151577949524 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.20013646252522457, 'learning_rate': 0.00984765060708375, 'weight_decay': 0.0005606941456145076}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:17:31,629] Trial 115 pruned. \n", "[I 2025-07-30 11:17:31,743] Trial 116 pruned. \n", "[I 2025-07-30 11:17:31,883] Trial 117 pruned. \n", "[I 2025-07-30 11:17:32,025] Trial 118 pruned. \n", "[I 2025-07-30 11:17:33,012] Trial 119 pruned. \n", "[I 2025-07-30 11:17:43,317] Trial 120 finished with value: -8.192288517951965 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2750319248359034, 'learning_rate': 0.009972458547819344, 'weight_decay': 2.3136556150787536e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:17:43,459] Trial 121 pruned. \n", "[I 2025-07-30 11:17:43,585] Trial 122 pruned. \n", "[I 2025-07-30 11:17:43,744] Trial 123 pruned. \n", "[I 2025-07-30 11:17:43,975] Trial 124 pruned. \n", "[I 2025-07-30 11:17:44,149] Trial 125 pruned. \n", "[I 2025-07-30 11:17:44,276] Trial 126 pruned. \n", "[I 2025-07-30 11:17:44,417] Trial 127 pruned. \n", "[I 2025-07-30 11:17:44,559] Trial 128 pruned. \n", "[I 2025-07-30 11:17:44,706] Trial 129 pruned. \n", "[I 2025-07-30 11:17:44,871] Trial 130 pruned. \n", "[I 2025-07-30 11:17:45,027] Trial 131 pruned. \n", "[I 2025-07-30 11:17:45,172] Trial 132 pruned. \n", "[I 2025-07-30 11:17:45,421] Trial 133 pruned. \n", "[I 2025-07-30 11:17:45,563] Trial 134 pruned. \n", "[I 2025-07-30 11:17:45,690] Trial 135 pruned. \n", "[I 2025-07-30 11:17:45,832] Trial 136 pruned. \n", "[I 2025-07-30 11:17:45,969] Trial 137 pruned. \n", "[I 2025-07-30 11:17:46,226] Trial 138 pruned. \n", "[I 2025-07-30 11:17:46,370] Trial 139 pruned. \n", "[I 2025-07-30 11:17:46,505] Trial 140 pruned. \n", "[I 2025-07-30 11:17:52,616] Trial 141 pruned. \n", "[I 2025-07-30 11:17:52,743] Trial 142 pruned. \n", "[I 2025-07-30 11:17:53,012] Trial 143 pruned. \n", "[I 2025-07-30 11:17:53,154] Trial 144 pruned. \n", "[I 2025-07-30 11:17:53,646] Trial 145 pruned. \n", "[I 2025-07-30 11:17:53,803] Trial 146 pruned. \n", "[I 2025-07-30 11:17:53,951] Trial 147 pruned. \n", "[I 2025-07-30 11:17:54,139] Trial 148 pruned. \n", "[I 2025-07-30 11:17:54,300] Trial 149 pruned. \n", "[I 2025-07-30 11:17:54,452] Trial 150 pruned. \n", "[I 2025-07-30 11:18:09,113] Trial 151 finished with value: -8.570221424102783 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.4095336683308968, 'learning_rate': 0.009797830089953018, 'weight_decay': 0.0002531938730280039}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:18:23,398] Trial 152 finished with value: -8.637274503707886 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3834562628871234, 'learning_rate': 0.009917192487716423, 'weight_decay': 0.00024314312770968693}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:18:23,580] Trial 153 pruned. \n", "[I 2025-07-30 11:18:23,726] Trial 154 pruned. \n", "[I 2025-07-30 11:18:23,854] Trial 155 pruned. \n", "[I 2025-07-30 11:18:30,846] Trial 156 pruned. \n", "[I 2025-07-30 11:18:30,967] Trial 157 pruned. \n", "[I 2025-07-30 11:18:31,110] Trial 158 pruned. \n", "[I 2025-07-30 11:18:31,270] Trial 159 pruned. \n", "[I 2025-07-30 11:18:31,411] Trial 160 pruned. \n", "[I 2025-07-30 11:18:31,546] Trial 161 pruned. \n", "[I 2025-07-30 11:18:31,687] Trial 162 pruned. \n", "[I 2025-07-30 11:18:31,830] Trial 163 pruned. \n", "[I 2025-07-30 11:18:41,385] Trial 164 finished with value: -8.330348491668701 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.2175434172189775, 'learning_rate': 0.009882649257538498, 'weight_decay': 0.00022145985846509883}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:18:41,568] Trial 165 pruned. \n", "[I 2025-07-30 11:18:41,711] Trial 166 pruned. \n", "[I 2025-07-30 11:18:41,868] Trial 167 pruned. \n", "[I 2025-07-30 11:18:42,150] Trial 168 pruned. \n", "[I 2025-07-30 11:18:42,300] Trial 169 pruned. \n", "[I 2025-07-30 11:18:42,436] Trial 170 pruned. \n", "[I 2025-07-30 11:18:55,273] Trial 171 finished with value: -8.177945494651794 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.413388302473841, 'learning_rate': 0.009998255346134933, 'weight_decay': 0.00030589815310463984}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:19:05,090] Trial 172 finished with value: -8.214579701423645 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.40373998890086044, 'learning_rate': 0.009969624205259202, 'weight_decay': 0.000258229144482451}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:19:05,233] Trial 173 pruned. \n", "[I 2025-07-30 11:19:05,495] Trial 174 pruned. \n", "[I 2025-07-30 11:19:05,639] Trial 175 pruned. \n", "[I 2025-07-30 11:19:05,766] Trial 176 pruned. \n", "[I 2025-07-30 11:19:05,920] Trial 177 pruned. \n", "[I 2025-07-30 11:19:06,064] Trial 178 pruned. \n", "[I 2025-07-30 11:19:07,733] Trial 179 pruned. \n", "[I 2025-07-30 11:19:07,885] Trial 180 pruned. \n", "[I 2025-07-30 11:19:08,059] Trial 181 pruned. \n", "[I 2025-07-30 11:19:08,200] Trial 182 pruned. \n", "[I 2025-07-30 11:19:08,373] Trial 183 pruned. \n", "[I 2025-07-30 11:19:13,449] Trial 184 pruned. \n", "[I 2025-07-30 11:19:13,642] Trial 185 pruned. \n", "[I 2025-07-30 11:19:13,899] Trial 186 pruned. \n", "[I 2025-07-30 11:19:14,079] Trial 187 pruned. \n", "[I 2025-07-30 11:19:14,227] Trial 188 pruned. \n", "[I 2025-07-30 11:19:14,373] Trial 189 pruned. \n", "[I 2025-07-30 11:19:14,538] Trial 190 pruned. \n", "[I 2025-07-30 11:19:15,126] Trial 191 pruned. \n", "[I 2025-07-30 11:19:15,273] Trial 192 pruned. \n", "[I 2025-07-30 11:19:25,185] Trial 193 finished with value: -8.296005129814148 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.21448788597979665, 'learning_rate': 0.009984502398410642, 'weight_decay': 0.00019226568868465445}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:19:25,408] Trial 194 pruned. \n", "[I 2025-07-30 11:19:25,555] Trial 195 pruned. \n", "[I 2025-07-30 11:19:25,704] Trial 196 pruned. \n", "[I 2025-07-30 11:19:25,847] Trial 197 pruned. \n", "[I 2025-07-30 11:19:38,228] Trial 198 finished with value: -8.192373394966125 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.37668400401793367, 'learning_rate': 0.009922243122886623, 'weight_decay': 4.5813174220310335e-05}. Best is trial 0 with value: -8.791361331939697.\n", "[I 2025-07-30 11:19:38,381] Trial 199 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-30T03:19:38.381481Z [info     ] Fold 2 best trial: value=-8.7914, params={'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.2316129463508589, 'learning_rate': 0.007274757455518377, 'weight_decay': 2.568748516494324e-05} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1338.41, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:38.399213Z [info     ] --- Performing blind test for Fold 2 on well 'T-1' --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1338.41, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.183821Z [info     ] Aggregating results from all CV folds... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.93, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.215817Z [info     ] 成功从交叉验证流程中收集到泛化能力评估数据。         [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.7, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.231609Z [info     ] Best hyperparameters found: {'cnn_filters': 8.0, 'cnn_kernel_size': 3.0, 'dropout_rate': 0.20035507957244322, 'learning_rate': 0.008984050100529915, 'mlp_units': 32.0, 'weight_decay': 6.592823138251963e-05} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.7, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.249946Z [info     ] --- Stage 2 Artifacts: Saving CV and Tuning Reports --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.7, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.294418Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.cv_performance artifact_path=obmiq_training_pytorch\\cv_performance_report.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.72, 'cpu_percent': 0.0} description=LOWO-CV中每一折的最佳验证损失和对应的超参数。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:19:53.335231Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.hyperparameter_tuning artifact_path=obmiq_training_pytorch\\hyperparameter_tuning_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.72, 'cpu_percent': 0.0} description=在所有CV折中聚合得到的全局最佳超参数组合。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:19:53.357426Z [info     ] --- Stage 2 Artifacts: Generating Generalization Evaluation Artifacts --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.72, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.377968Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.lowo_cv_performance_summary artifact_path=obmiq_training_pytorch\\lowo_cv_performance_summary.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.72, 'cpu_percent': 0.0} description=LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:19:53.404860Z [info     ] 成功生成并保存了泛化能力性能评估表: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\lowo_cv_performance_summary.csv [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1343.72, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.715094Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.lowo_cv_predictions artifact_path=obmiq_training_pytorch\\lowo_cv_predictions.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1346.9, 'cpu_percent': 0.0} description=LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:19:53.750690Z [info     ] 成功保存了泛化能力评估的数据快照: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\lowo_cv_predictions.csv [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1346.9, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:53.921873Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1349.29, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T03:19:53.972121Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1349.34, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:19:54.882816Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1372.99, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.svg']\n", "2025-07-30T03:19:54.927128Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1371.02, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dt2 P50 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:19:55.009632Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1372.99, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T03:19:55.041577Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1372.99, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:19:55.820908Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.67, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.svg']\n", "2025-07-30T03:19:55.869163Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1393.71, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dphit Nmr operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:19:55.885573Z [info     ] 成功生成并保存了泛化能力交叉图。               [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1393.71, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:55.900780Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1393.71, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\tensorboard_logs' operation=register_artifact\n", "2025-07-30T03:19:55.932322Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.logs.tensorboard artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1393.71, 'cpu_percent': 0.0} description=用于TensorBoard可视化的日志文件目录。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:19:55.974078Z [info     ] --- Stage 3: Final Model Training --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1393.71, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:56.038253Z [info     ] Final model training started on device: cuda [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1393.71, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:56.735842Z [info     ] Epoch 1/300, Train Loss: -0.1171, Val Loss: -0.4673 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1484.36, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:57.035216Z [info     ] Epoch 2/300, Train Loss: -0.7302, Val Loss: -1.0274 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.32, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:57.302240Z [info     ] Epoch 3/300, Train Loss: -1.2774, Val Loss: -1.5797 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:57.597766Z [info     ] Epoch 4/300, Train Loss: -1.7844, Val Loss: -2.0711 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:57.871298Z [info     ] Epoch 5/300, Train Loss: -2.2585, Val Loss: -2.5587 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:58.147941Z [info     ] Epoch 6/300, Train Loss: -2.7678, Val Loss: -2.9861 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:58.426434Z [info     ] Epoch 7/300, Train Loss: -3.1668, Val Loss: -3.3127 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:58.713436Z [info     ] Epoch 8/300, Train Loss: -3.5466, Val Loss: -3.7545 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:58.978413Z [info     ] Epoch 9/300, Train Loss: -3.8592, Val Loss: -4.0040 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:59.234234Z [info     ] Epoch 10/300, Train Loss: -4.2220, Val Loss: -4.3223 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.47, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:59.511428Z [info     ] Epoch 11/300, Train Loss: -4.5690, Val Loss: -4.6884 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:19:59.813910Z [info     ] Epoch 12/300, Train Loss: -4.8496, Val Loss: -5.0445 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:00.124891Z [info     ] Epoch 13/300, Train Loss: -5.1497, Val Loss: -5.2174 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:00.428328Z [info     ] Epoch 14/300, Train Loss: -5.5168, Val Loss: -5.6689 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:00.727920Z [info     ] Epoch 15/300, Train Loss: -5.7981, Val Loss: -5.9579 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:01.009355Z [info     ] Epoch 16/300, Train Loss: -6.0644, Val Loss: -6.0769 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:01.251981Z [info     ] Epoch 17/300, Train Loss: -6.2632, Val Loss: -6.4095 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:01.502308Z [info     ] Epoch 18/300, Train Loss: -6.6266, Val Loss: -6.6087 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:01.774243Z [info     ] Epoch 19/300, Train Loss: -6.8956, Val Loss: -6.9317 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:02.076629Z [info     ] Epoch 20/300, Train Loss: -7.0927, Val Loss: -7.0558 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:02.339697Z [info     ] Epoch 21/300, Train Loss: -7.1547, Val Loss: -7.1159 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:02.657938Z [info     ] Epoch 22/300, Train Loss: -7.4136, Val Loss: -7.6093 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:02.969453Z [info     ] Epoch 23/300, Train Loss: -7.6417, Val Loss: -7.7557 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:03.254674Z [info     ] Epoch 24/300, Train Loss: -7.8472, Val Loss: -7.9650 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:03.541414Z [info     ] Epoch 25/300, Train Loss: -8.0280, Val Loss: -8.0337 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:03.858644Z [info     ] Epoch 26/300, Train Loss: -8.0263, Val Loss: -8.0238 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:04.118618Z [info     ] Epoch 27/300, Train Loss: -8.0355, Val Loss: -8.2082 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:04.414570Z [info     ] Epoch 28/300, Train Loss: -8.1854, Val Loss: -8.2346 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:04.735452Z [info     ] Epoch 29/300, Train Loss: -8.1768, Val Loss: -7.7029 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:04.998609Z [info     ] Epoch 30/300, Train Loss: -8.1603, Val Loss: -8.1545 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:05.253402Z [info     ] Epoch 31/300, Train Loss: -8.2397, Val Loss: -8.2657 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:05.564128Z [info     ] Epoch 32/300, Train Loss: -8.4005, Val Loss: -8.4868 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:05.842879Z [info     ] Epoch 33/300, Train Loss: -8.5014, Val Loss: -8.4545 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:06.129724Z [info     ] Epoch 34/300, Train Loss: -8.4889, Val Loss: -8.4018 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:06.425883Z [info     ] Epoch 35/300, Train Loss: -8.4184, Val Loss: -8.5179 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:06.726705Z [info     ] Epoch 36/300, Train Loss: -8.4197, Val Loss: -8.6423 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:07.027704Z [info     ] Epoch 37/300, Train Loss: -8.4324, Val Loss: -8.4967 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:07.293202Z [info     ] Epoch 38/300, Train Loss: -8.5063, Val Loss: -8.6457 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:07.588386Z [info     ] Epoch 39/300, Train Loss: -8.5550, Val Loss: -8.3121 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:07.859312Z [info     ] Epoch 40/300, Train Loss: -8.4909, Val Loss: -8.6232 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:08.167651Z [info     ] Epoch 41/300, Train Loss: -8.5266, Val Loss: -8.5468 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:08.409032Z [info     ] Epoch 42/300, Train Loss: -8.5993, Val Loss: -8.5620 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:08.705973Z [info     ] Epoch 43/300, Train Loss: -8.5429, Val Loss: -8.7382 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:09.004040Z [info     ] Epoch 44/300, Train Loss: -8.3094, Val Loss: -8.5402 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:09.295690Z [info     ] Epoch 45/300, Train Loss: -8.4040, Val Loss: -8.6101 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:09.550482Z [info     ] Epoch 46/300, Train Loss: -8.4159, Val Loss: -8.5756 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:09.837957Z [info     ] Epoch 47/300, Train Loss: -8.4719, Val Loss: -8.7041 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:10.148266Z [info     ] Epoch 48/300, Train Loss: -8.6890, Val Loss: -8.3884 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:10.449367Z [info     ] Epoch 49/300, Train Loss: -8.6434, Val Loss: -8.6023 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:10.727424Z [info     ] Epoch 50/300, Train Loss: -8.6503, Val Loss: -8.6203 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:10.997860Z [info     ] Epoch 51/300, Train Loss: -8.6180, Val Loss: -8.6882 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:11.298666Z [info     ] Epoch 52/300, Train Loss: -8.6470, Val Loss: -8.6923 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:11.569217Z [info     ] Epoch 53/300, Train Loss: -8.6152, Val Loss: -8.5999 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:11.845865Z [info     ] Epoch 54/300, Train Loss: -8.5412, Val Loss: -8.5203 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:12.146751Z [info     ] Epoch 55/300, Train Loss: -8.5478, Val Loss: -8.6206 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:12.427516Z [info     ] Epoch 56/300, Train Loss: -8.5468, Val Loss: -8.7096 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:12.711203Z [info     ] Epoch 57/300, Train Loss: -8.7553, Val Loss: -8.7262 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:12.996610Z [info     ] Epoch 58/300, Train Loss: -8.6923, Val Loss: -8.6790 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:13.291024Z [info     ] Epoch 59/300, Train Loss: -8.6388, Val Loss: -8.3830 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:13.624605Z [info     ] Epoch 60/300, Train Loss: -8.5932, Val Loss: -8.6472 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:13.919896Z [info     ] Epoch 61/300, Train Loss: -8.7014, Val Loss: -8.8262 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:14.237685Z [info     ] Epoch 62/300, Train Loss: -8.6637, Val Loss: -8.8217 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:14.530474Z [info     ] Epoch 63/300, Train Loss: -8.6250, Val Loss: -8.7257 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:14.804026Z [info     ] Epoch 64/300, Train Loss: -8.6559, Val Loss: -8.5720 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:15.103396Z [info     ] Epoch 65/300, Train Loss: -8.5696, Val Loss: -8.7144 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:15.418631Z [info     ] Epoch 66/300, Train Loss: -8.6156, Val Loss: -8.6175 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:15.665920Z [info     ] Epoch 67/300, Train Loss: -8.6093, Val Loss: -8.6534 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:15.912106Z [info     ] Epoch 68/300, Train Loss: -8.7034, Val Loss: -8.7856 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:16.214089Z [info     ] Epoch 69/300, Train Loss: -8.6507, Val Loss: -8.6901 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:16.494814Z [info     ] Epoch 70/300, Train Loss: -8.7752, Val Loss: -8.6431 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:16.763910Z [info     ] Epoch 71/300, Train Loss: -8.6843, Val Loss: -8.7703 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:17.032052Z [info     ] Epoch 72/300, Train Loss: -8.6624, Val Loss: -8.6851 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:17.343372Z [info     ] Epoch 73/300, Train Loss: -8.7191, Val Loss: -8.6545 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:17.596693Z [info     ] Epoch 74/300, Train Loss: -8.6876, Val Loss: -8.7854 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:17.849807Z [info     ] Epoch 75/300, Train Loss: -8.7174, Val Loss: -8.8303 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:18.144708Z [info     ] Epoch 76/300, Train Loss: -8.7223, Val Loss: -8.5304 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:18.462611Z [info     ] Epoch 77/300, Train Loss: -8.4871, Val Loss: -8.7997 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.49, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:18.757422Z [info     ] Epoch 78/300, Train Loss: -8.7324, Val Loss: -8.8569 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:19.072663Z [info     ] Epoch 79/300, Train Loss: -8.7530, Val Loss: -8.5200 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:19.375390Z [info     ] Epoch 80/300, Train Loss: -8.5692, Val Loss: -8.7565 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:19.683953Z [info     ] Epoch 81/300, Train Loss: -8.6620, Val Loss: -8.7872 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:19.985986Z [info     ] Epoch 82/300, Train Loss: -8.6779, Val Loss: -8.7793 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:20.274946Z [info     ] Epoch 83/300, Train Loss: -8.7860, Val Loss: -8.8205 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:20.581540Z [info     ] Epoch 84/300, Train Loss: -8.7379, Val Loss: -8.5582 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:20.877061Z [info     ] Epoch 85/300, Train Loss: -8.6075, Val Loss: -8.6296 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:21.161558Z [info     ] Epoch 86/300, Train Loss: -8.6709, Val Loss: -8.8065 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:21.445514Z [info     ] Epoch 87/300, Train Loss: -8.7598, Val Loss: -8.7235 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:21.708280Z [info     ] Epoch 88/300, Train Loss: -8.7329, Val Loss: -8.6992 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:21.994848Z [info     ] Epoch 89/300, Train Loss: -8.7329, Val Loss: -8.6541 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:22.283172Z [info     ] Epoch 90/300, Train Loss: -8.8130, Val Loss: -8.8140 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:22.563667Z [info     ] Epoch 91/300, Train Loss: -8.7279, Val Loss: -8.8839 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:22.842796Z [info     ] Epoch 92/300, Train Loss: -8.7690, Val Loss: -8.7696 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:23.128954Z [info     ] Epoch 93/300, Train Loss: -8.8086, Val Loss: -8.7213 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:23.454888Z [info     ] Epoch 94/300, Train Loss: -8.6637, Val Loss: -8.6921 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:23.739390Z [info     ] Epoch 95/300, Train Loss: -8.7733, Val Loss: -8.7340 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:24.029791Z [info     ] Epoch 96/300, Train Loss: -8.7132, Val Loss: -8.4796 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:24.295355Z [info     ] Epoch 97/300, Train Loss: -8.7985, Val Loss: -8.8194 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:24.572562Z [info     ] Epoch 98/300, Train Loss: -8.8655, Val Loss: -8.7903 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:24.841626Z [info     ] Epoch 99/300, Train Loss: -8.8015, Val Loss: -8.8411 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:25.106583Z [info     ] Epoch 100/300, Train Loss: -8.8129, Val Loss: -8.7781 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:25.361358Z [info     ] Epoch 101/300, Train Loss: -8.8017, Val Loss: -8.8423 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:25.594628Z [info     ] Epoch 102/300, Train Loss: -8.8976, Val Loss: -8.8183 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:25.857675Z [info     ] Epoch 103/300, Train Loss: -8.7869, Val Loss: -8.8370 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:26.110012Z [info     ] Epoch 104/300, Train Loss: -8.8036, Val Loss: -8.7423 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:26.380961Z [info     ] Epoch 105/300, Train Loss: -8.8555, Val Loss: -8.6986 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:26.658898Z [info     ] Epoch 106/300, Train Loss: -8.7430, Val Loss: -8.5637 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:26.928970Z [info     ] Epoch 107/300, Train Loss: -8.7113, Val Loss: -8.7571 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:27.246253Z [info     ] Epoch 108/300, Train Loss: -8.7122, Val Loss: -8.8263 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:27.549693Z [info     ] Epoch 109/300, Train Loss: -8.8396, Val Loss: -8.6465 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:27.822998Z [info     ] Epoch 110/300, Train Loss: -8.8045, Val Loss: -8.6185 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.089969Z [info     ] Epoch 111/300, Train Loss: -8.7729, Val Loss: -8.8088 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.121511Z [info     ] Early stopping triggered at epoch 111. [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1487.62, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.153446Z [info     ] Loaded best model state with validation loss: -8.8839 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1488.07, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.169164Z [info     ] Performing final evaluation on the full training dataset... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1488.07, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.296627Z [info     ] Final model training completed. [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1490.38, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.312736Z [info     ] --- Stage 3 Artifacts: Saving Final Model Assets --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1490.3, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.328645Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.assets_pytorch artifact_path=obmiq_training_pytorch\\model_assets_pytorch.pkl context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1490.41, 'cpu_percent': 0.0} description=包含模型权重、超参数和预处理器的PyTorch模型资产包。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:28.362833Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1490.41, 'cpu_percent': 0.0} description=最终模型训练过程中的损失变化历史。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:28.395076Z [info     ] Plotting final training history... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1490.41, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:28.423807Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1490.41, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.training_history\n", "2025-07-30T03:20:28.451525Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=final_training_history base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1490.41, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:29.074561Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1515.14, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\final_training_history.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\final_training_history.svg']\n", "2025-07-30T03:20:29.107867Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1515.14, 'cpu_percent': 0.0} description=最终模型训练的损失曲线图。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:29.140899Z [info     ] Saving final model evaluation data and generating plots... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1515.14, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:29.455071Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_model_evaluation artifact_path=obmiq_training_pytorch\\final_model_evaluation.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1517.71, 'cpu_percent': 0.0} description=最终模型在全部训练数据上的预测和残差结果。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:29.475956Z [info     ] Generating evaluation plots... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1517.71, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:29.555004Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T03:20:29.571036Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1519.72, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:30.319222Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1542.32, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.svg']\n", "2025-07-30T03:20:30.366820Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_crossplot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1540.32, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:30.446835Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1542.32, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T03:20:30.478312Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1542.32, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:31.274373Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1564.93, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.svg']\n", "2025-07-30T03:20:31.313553Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_crossplot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1562.92, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:31.388387Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1564.93, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T03:20:31.424760Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1564.93, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:32.272880Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1589.62, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.svg']\n", "2025-07-30T03:20:32.323148Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1587.62, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:32.392981Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1590.73, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T03:20:32.425025Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1590.73, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:33.311924Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1615.43, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.svg']\n", "2025-07-30T03:20:33.374617Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1613.42, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:33.468036Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1615.43, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T03:20:33.574047Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1616.0, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:34.329694Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1640.87, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.svg']\n", "2025-07-30T03:20:34.361432Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1638.86, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:34.466024Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1641.7, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T03:20:34.593390Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1641.7, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:35.328426Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1666.45, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.svg']\n", "2025-07-30T03:20:35.359196Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1664.45, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:35.384251Z [info     ] --- Stage 3: Model Interpretability Analysis (Captum) --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1664.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:35.629631Z [info     ] Running Captum analysis for target: DT2_P50... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1666.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:39.756595Z [info     ] Running Captum analysis for target: DPHIT_NMR... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1743.77, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:43.770835Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1742.83, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-30T03:20:43.793440Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1742.83, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-30T03:20:43.818028Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1742.83, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:43.953092Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1745.38, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50) operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:44.003017Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1745.59, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-30T03:20:44.053427Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1745.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:44.905859Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1770.55, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.svg']\n", "2025-07-30T03:20:44.937940Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1770.55, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-30T03:20:44.956198Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1770.55, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-30T03:20:45.001206Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1770.55, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:45.166959Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1771.6, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR) operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:20:45.231104Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1772.69, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-30T03:20:45.263009Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1772.69, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:46.161211Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.39, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.svg']\n", "2025-07-30T03:20:46.186825Z [warning  ] 井名列 'WELL_NO' 不是字符串类型，将进行区分大小写的精确匹配。这可能导致因大小写或空格问题而找不到井。 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.39, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.202608Z [info     ] 找到样本: 井='c-1', 目标深度=6311.73, 实际深度=6311.80 (索引=9) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.45, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.218564Z [info     ] 找到样本: 井='c-1', 目标深度=6313.38, 实际深度=6313.32 (索引=19) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.234577Z [info     ] 找到样本: 井='c-1', 目标深度=6318.8, 实际深度=6318.81 (索引=50) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.250411Z [info     ] 找到样本: 井='c-1', 目标深度=6334.55, 实际深度=6334.51 (索引=129) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.266245Z [info     ] 找到样本: 井='c-1', 目标深度=6409.94, 实际深度=6409.94 (索引=624) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.282189Z [info     ] 找到样本: 井='c-1', 目标深度=6426.71, 实际深度=6426.71 (索引=734) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.282189Z [info     ] 找到样本: 井='c-1', 目标深度=6440.34, 实际深度=6440.27 (索引=823) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.48, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.297928Z [info     ] 找到样本: 井='t-1', 目标深度=6426.51, 实际深度=6426.56 (索引=1069) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1798.67, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.313840Z [info     ] 找到样本: 井='t-1', 目标深度=6471.0, 实际深度=6471.06 (索引=1327) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1798.68, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.313840Z [info     ] 找到样本: 井='t-1', 目标深度=6552.36, 实际深度=6552.29 (索引=1835) [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1798.68, 'cpu_percent': 0.0}\n", "2025-07-30T03:20:46.358637Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.52, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:20:46.441723Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1797.78, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:52.643986Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1850.01, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.svg']\n", "2025-07-30T03:20:52.691512Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1850.04, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:20:52.770855Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1850.89, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:20:58.507415Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1904.55, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.svg']\n", "2025-07-30T03:20:58.553843Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1904.59, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:20:58.633458Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1905.46, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:04.572589Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1908.33, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.svg']\n", "2025-07-30T03:21:04.630793Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1908.33, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:04.721459Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1908.73, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:10.289737Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1961.33, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.svg']\n", "2025-07-30T03:21:10.337200Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1961.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:10.416745Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1962.24, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:16.102370Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2016.06, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.svg']\n", "2025-07-30T03:21:16.171190Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2016.23, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:16.245855Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2017.07, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:21.930707Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2070.63, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.svg']\n", "2025-07-30T03:21:21.978734Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2070.71, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:22.083217Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2071.58, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:27.807567Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2124.98, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.svg']\n", "2025-07-30T03:21:27.855724Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2125.09, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:27.935028Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2125.88, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:33.899199Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2057.19, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.svg']\n", "2025-07-30T03:21:33.963357Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2057.46, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:34.057834Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2057.97, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:39.648825Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2107.96, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.svg']\n", "2025-07-30T03:21:39.698015Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2107.97, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:39.790539Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2108.12, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:45.448336Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2160.59, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.svg']\n", "2025-07-30T03:21:45.482670Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2160.66, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:45.582895Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2161.55, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:51.230487Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2214.83, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.svg']\n", "2025-07-30T03:21:51.275937Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2214.86, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:51.356047Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2215.69, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:21:57.073707Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2269.79, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.svg']\n", "2025-07-30T03:21:57.134226Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2269.83, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:21:57.229944Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2270.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:02.994885Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2324.08, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.svg']\n", "2025-07-30T03:22:03.396301Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2179.88, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:22:03.494837Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2180.24, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:09.171376Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2229.96, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.svg']\n", "2025-07-30T03:22:09.219290Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2229.98, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:22:09.312458Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2230.03, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:15.000433Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2280.32, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.svg']\n", "2025-07-30T03:22:15.048039Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2280.36, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:22:15.136239Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2280.76, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:20.890298Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2333.75, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.svg']\n", "2025-07-30T03:22:20.940001Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2333.77, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:22:21.016396Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2334.53, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:26.756824Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2387.79, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.svg']\n", "2025-07-30T03:22:26.803493Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2387.88, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:22:26.878099Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2388.7, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:32.468942Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2442.16, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.svg']\n", "2025-07-30T03:22:32.516499Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2442.27, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:22:32.625139Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2442.95, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:38.187978Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2496.66, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.svg']\n", "2025-07-30T03:22:38.242930Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2496.94, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-30T03:22:38.331470Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2497.66, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:44.223034Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2404.31, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.svg']\n", "2025-07-30T03:22:44.238930Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2404.31, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_sequence_attributions_dir' operation=register_artifact\n", "2025-07-30T03:22:44.282449Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_sequence_attributions_dir artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2404.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:44.310647Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2404.31, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir' operation=register_artifact\n", "2025-07-30T03:22:44.326332Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_saliency_examples_dir artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2404.31, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:44.358014Z [info     ] --- Stage 5: Exporting Model to ONNX --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2404.31, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.595231Z [info     ] Model successfully exported to ONNX format at: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_training_pytorch\\obmiq_model.onnx [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2410.99, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.608222Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.onnx_model artifact_path=obmiq_training_pytorch\\obmiq_model.onnx context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2410.99, 'cpu_percent': 0.0} description=可用于跨平台部署的ONNX格式模型。 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:44.611355Z [info     ] ===== OBMIQ Training Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2411.0, 'cpu_percent': 0.0}\n", "训练步骤完成。最佳超参数: {'cnn_filters': 8.0, 'cnn_kernel_size': 3.0, 'dropout_rate': 0.20035507957244322, 'learning_rate': 0.008984050100529915, 'mlp_units': 32.0, 'weight_decay': 6.592823138251963e-05}\n", "\n", "--- 开始 OBMIQ 预测步骤 (PyTorch) ---\n", "2025-07-30T03:22:44.678974Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2372.35, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.690932Z [info     ] Validating prediction inputs... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2372.35, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.708478Z [info     ] Loading model assets and reconstructing model... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2372.35, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.722623Z [info     ] Preprocessing prediction data... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2372.36, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.722623Z [info     ] 成功将模型所需的逻辑曲线名解析为预测数据的物理列名。     [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2372.36, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.754192Z [info     ] Performing inference on device: cuda... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2375.41, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:44.769870Z [info     ] Formatting predictions and saving artifacts... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2375.55, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:45.229399Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.datasets.predictions artifact_path=obmiq_prediction_pytorch\\predictions.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2381.86, 'cpu_percent': 0.0} description=包含原始输入和模型预测结果的数据集 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:45.261129Z [info     ] Ground truth found in prediction data. Generating evaluation plots... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2381.86, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:45.731156Z [info     ] Updated prediction snapshot with residual columns at: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch\\predictions.csv [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2382.85, 'cpu_percent': 0.0}\n", "2025-07-30T03:22:45.827010Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2388.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T03:22:45.872689Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2388.67, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:46.749703Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2411.29, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.svg']\n", "2025-07-30T03:22:46.797464Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\crossplot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2407.89, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:46.908540Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2411.97, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-30T03:22:46.940077Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2412.07, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:47.742473Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2434.68, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.svg']\n", "2025-07-30T03:22:47.774423Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\crossplot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2431.27, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:47.903828Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2435.2, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T03:22:47.919358Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2435.23, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:48.807189Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2460.0, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.svg']\n", "2025-07-30T03:22:48.854822Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_plot_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2456.6, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:48.966070Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2460.89, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-30T03:22:48.982026Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2460.92, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:49.911044Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2485.72, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.svg']\n", "2025-07-30T03:22:49.942750Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_plot_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2482.32, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:50.068973Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2486.65, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T03:22:50.187753Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2486.8, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:50.864609Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2511.5, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.svg']\n", "2025-07-30T03:22:50.912211Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_hist_dt2_p50.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2508.1, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50 operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:51.039418Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2512.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-30T03:22:51.157584Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2512.54, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T03:22:51.901555Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2537.24, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250730_104802\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.svg']\n", "2025-07-30T03:22:51.934857Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_hist_dphit_nmr.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2533.84, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr operation=register_artifact run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:51.980849Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2533.84, 'cpu_percent': 0.0}\n", "预测步骤完成。\n", "\n", "预测结果已保存至: output01\\obmiq_run_pytorch_20250730_104802\\obmiq_prediction_pytorch\\predictions.csv\n", "\n", "预测结果预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL_NO</th>\n", "      <th>MD</th>\n", "      <th>DT2_P50</th>\n", "      <th>DT2_P50_PRED</th>\n", "      <th>DPHIT_NMR</th>\n", "      <th>DPHIT_NMR_PRED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C-1</td>\n", "      <td>6310.4268</td>\n", "      <td>0.104076</td>\n", "      <td>-0.151948</td>\n", "      <td>0.065477</td>\n", "      <td>0.021146</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C-1</td>\n", "      <td>6310.5792</td>\n", "      <td>-0.148789</td>\n", "      <td>-0.292192</td>\n", "      <td>0.061988</td>\n", "      <td>0.017926</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C-1</td>\n", "      <td>6310.7316</td>\n", "      <td>-0.211292</td>\n", "      <td>-0.295557</td>\n", "      <td>0.052548</td>\n", "      <td>0.014885</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C-1</td>\n", "      <td>6310.8840</td>\n", "      <td>-0.245636</td>\n", "      <td>-0.175467</td>\n", "      <td>0.038409</td>\n", "      <td>0.010544</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C-1</td>\n", "      <td>6311.0364</td>\n", "      <td>0.112414</td>\n", "      <td>-0.130904</td>\n", "      <td>0.051449</td>\n", "      <td>0.012145</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  WELL_NO         MD   DT2_P50  DT2_P50_PRED  DPHIT_NMR  DPHIT_NMR_PRED\n", "0     C-1  6310.4268  0.104076     -0.151948   0.065477        0.021146\n", "1     C-1  6310.5792 -0.148789     -0.292192   0.061988        0.017926\n", "2     C-1  6310.7316 -0.211292     -0.295557   0.052548        0.014885\n", "3     C-1  6310.8840 -0.245636     -0.175467   0.038409        0.010544\n", "4     C-1  6311.0364  0.112414     -0.130904   0.051449        0.012145"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-30T03:22:52.093005Z [info     ] Run marked as successful       [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2532.99, 'cpu_percent': 0.0} operation=mark_success run_id=20250730-024802-b761ade0\n", "2025-07-30T03:22:52.124232Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 2532.99, 'cpu_percent': 0.0} duration_seconds=2089.276 manifest_path=output01\\obmiq_run_pytorch_20250730_104802\\manifest.json operation=finalize run_id=20250730-024802-b761ade0 status=COMPLETED\n"]}], "source": ["if project:\n", "    # 定义输出目录\n", "    output_dir = Path(\"./output01\") # 使用新的输出目录\n", "    output_dir.mkdir(exist_ok=True)\n", "\n", "    run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"obmiq_run_pytorch\")\n", "    # 使用 RunContext 包裹整个工作流\n", "    with RunContext(run_dir=output_dir / run_dir_name, overwrite=True) as ctx:\n", "\n", "        # --- 1. 训练步骤 ---\n", "        print(\"--- 开始 OBMIQ 训练步骤 (PyTorch) ---\")\n", "\n", "        # a. 定义训练配置\n", "        training_config = ObmiqTrainingConfig(\n", "            n_trials=200,\n", "            max_epochs_per_trial=150,\n", "            final_train_epochs=300,\n", "            patience=20,\n", "            batch_size=64\n", "        )\n", "\n", "        # b. 定义facade函数的关键字参数\n", "        training_kwargs = {\n", "            \"sequence_feature\": \"PHI_T2_DIST_CUM\",\n", "            \"normalization_feature\": \"PHIT_NMR\",\n", "            \"tabular_features\": ['DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "                                'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "                                'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "                                'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "                                'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY'],\n", "            \"target_features\": [\"DT2_P50\", \"DPHIT_NMR\"],\n", "            \"grouping_feature\": well_no_name, # 使用新的参数名\n", "            \"t2_time_axis\": t2_time_array,\n", "        }\n", "\n", "        saliency_samples = [(\"c-1\",6311.73),\n", "                            (\"c-1\",6313.38),\n", "                            (\"c-1\",6318.8),\n", "                            (\"c-1\",6334.55),\n", "                            (\"c-1\",6409.94),\n", "                            (\"c-1\",6426.71),\n", "                            (\"c-1\",6440.34),\n", "                            (\"t-1\",6426.51),\n", "                            (\"t-1\",6471.0),\n", "                            (\"t-1\",6552.36)]\n", "\n", "        # c. 执行训练步骤\n", "        # 新版facade直接接收WpDataFrameBundle，无需手动准备X, y\n", "        training_results = run_obmiq_training_step(\n", "            config=training_config,\n", "            ctx=ctx,\n", "            train_bundle=train_bundle,\n", "            depth_feature=depth_name,\n", "            saliency_samples=saliency_samples,\n", "            **training_kwargs\n", "        )\n", "\n", "        print(f\"训练步骤完成。最佳超参数: {training_results.get('best_hyperparameters')}\")\n", "\n", "        # --- 2. 预测步骤 ---\n", "        print(\"\\n--- 开始 OBMIQ 预测步骤 (PyTorch) ---\")\n", "\n", "        # a. 从上下文中获取模型资产的路径\n", "        model_assets_path = ctx.get_artifact_path(ObmiqTrainingArtifacts.MODEL_ASSETS.value)\n", "\n", "        # b. 使用产物处理器加载模型资产\n", "        handler = ObmiqArtifactHandler()\n", "        model_assets = handler.load_model_assets(model_assets_path)\n", "\n", "        # c. 定义预测配置\n", "        prediction_config = ObmiqPredictionConfig()\n", "\n", "        # d. 定义facade函数的关键字参数\n", "        prediction_kwargs = {\n", "            \"source_t2_time_axis\": t2_time_array, # 假设预测数据与训练数据T2轴相同\n", "            \"output_curve_names\": (\"DT2_P50_PRED\", \"DPHIT_NMR_PRED\")\n", "        }\n", "\n", "        # e. 执行预测步骤\n", "        prediction_results = run_obmiq_prediction_step(\n", "            config=prediction_config,\n", "            ctx=ctx,\n", "            model_assets=model_assets,\n", "            prediction_bundle=prediction_bundle,\n", "            **prediction_kwargs\n", "        )\n", "\n", "        print(\"预测步骤完成。\")\n", "\n", "        # --- 3. 结果检查 ---\n", "        # 新版facade返回的是预测结果文件的路径\n", "        prediction_path = Path(prediction_results[\"output_path\"])\n", "        if prediction_path.exists():\n", "            print(f\"\\n预测结果已保存至: {prediction_path}\")\n", "            # 从CSV加载结果进行预览\n", "            predicted_df = pd.read_csv(prediction_path)\n", "            print(\"\\n预测结果预览:\")\n", "            display(predicted_df[[\"WELL_NO\", \"MD\", \"DT2_P50\", \"DT2_P50_PRED\", \"DPHIT_NMR\", \"DPHIT_NMR_PRED\"]].head())\n", "        else:\n", "            print(f\"❌ 预测结果文件未找到: {prediction_path}\")\n", "\n", "else:\n", "    print(\"因数据加载失败，跳过工作流执行。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}