"""LogScout: A generic step for systematic feature analysis in machine learning workflows.

This package provides a standardized, reusable component for performing exploratory
data analysis on well log data (or any tabular data). It helps diagnose relationships
between features, identify collinearity, and generates a comprehensive set of
quantitative and visual artifacts to support machine learning modeling decisions.

The main entry point is the `run_log_scout_step` function in the `facade` module.
"""

from .config import LogScoutConfig
from .constants import LogScoutArtifacts, LogScoutPlotProfiles, LogScoutPlotTypes
from .facade import run_log_scout_step

__all__ = [
    "LogScoutArtifacts",
    "LogScoutConfig",
    "LogScoutPlotProfiles",
    "LogScoutPlotTypes",
    "run_log_scout_step",
]
