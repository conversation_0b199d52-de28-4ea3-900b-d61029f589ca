"""算法专用测试数据工厂 - 测试专用

为 scape/core 中的核心算法提供一键生成“输入+预期输出”的完整测试用例。

Examples
--------
>>> # 为FOSTER-NMR模型生成测试数据
>>> input_dataset, expected_perm = FosterNMRTestDataFactory.create_dataset_with_expected_perm()
>>>
>>> # 在测试中使用
>>> from scape.core.algorithms import foster_nmr
>>> calculated_perm = foster_nmr.calculate(input_dataset)
>>> np.testing.assert_allclose(calculated_perm, expected_perm)
"""
from __future__ import annotations

import numpy as np
from typing import TYPE_CHECKING

from logwp.testing.builders import DatasetBuilder
from logwp.testing.factories.synthetic_curve_factory import SyntheticCurveFactory

if TYPE_CHECKING:
    from logwp.models.datasets import WpContinuousDataset


class FosterNMRTestDataFactory:
    """为FOSTER-NMR渗透率模型生成测试数据的工厂。"""

    @staticmethod
    def create_dataset_with_expected_perm(
        n_points: int = 100,
        phit_range: tuple[float, float] = (0.05, 0.35),
        t2lm_range: tuple[float, float] = (10.0, 500.0),
        foster_c: float = 1.0,
        foster_m: float = 4.0,
        foster_n: float = 2.0,
    ) -> tuple[WpContinuousDataset, np.ndarray]:
        """创建包含PHIT、T2LM的数据集和预期的PERM曲线。

        使用 FOSTER-NMR 公式 `PERM = C * (PHIT ** M) * (T2LM ** N)`
        来生成精确的预期输出，用于算法验证。

        Args:
            n_points: 数据点数量。
            phit_range: 孔隙度 (PHIT) 的范围 (min, max)。
            t2lm_range: T2对数均值 (T2LM) 的范围 (min, max)。
            foster_c: Foster-NMR 公式中的常数 C。
            foster_m: Foster-NMR 公式中的孔隙度指数 M。
            foster_n: Foster-NMR 公式中的T2LM指数 N。

        Returns:
            tuple[WpContinuousDataset, np.ndarray]:
                - 一个包含 `PHIT` 和 `T2LM` 曲线的 `WpContinuousDataset`。
                - 一个根据公式计算出的、精确的 `PERM` 渗透率 NumPy 数组。
        """
        phit_curve = np.linspace(phit_range[0], phit_range[1], n_points)
        t2lm_curve = np.linspace(t2lm_range[0], t2lm_range[1], n_points)

        # 根据公式计算精确的预期渗透率
        expected_perm = foster_c * (phit_curve**foster_m) * (t2lm_curve**foster_n)

        # 使用 DatasetBuilder 创建输入数据集
        input_dataset = DatasetBuilder.quick_continuous_dataset(
            name="foster_nmr_input",
            curves={"PHIT": phit_curve, "T2LM": t2lm_curve},
        )

        return input_dataset, expected_perm


class OBMIQTestDataFactory:
    """为OBMIQ机器学习模型创建测试数据。"""

    @staticmethod
    def create_dataset_with_target(
        n_points: int = 200,
        feature_names: list[str] | None = None,
        target_name: str = "PHIT_CORE",
    ) -> tuple[WpContinuousDataset, np.ndarray]:
        """创建包含多个输入特征和目标变量的数据集。

        Args:
            n_points: 数据点数量。
            feature_names: 特征名称列表。默认为 ["GR", "RHOB", "NPHI"]。
            target_name: 目标变量的名称。

        Returns:
            tuple[WpContinuousDataset, np.ndarray]:
                - 一个包含输入特征的 `WpContinuousDataset`。
                - 一个与特征线性相关的、带噪声的目标变量 NumPy 数组。
        """
        if feature_names is None:
            feature_names = ["GR", "RHOB", "NPHI"]

        # 1. Generate correlated features using the synthetic factory
        # These values are just for creating plausible-looking data
        means = [60, 2.5, 0.2]
        cov_matrix = [[225, -1.5, -3.0], [-1.5, 0.01, 0.005], [-3.0, 0.005, 0.01]]

        n_features = len(feature_names)
        if len(means) < n_features or np.shape(cov_matrix)[0] < n_features:
            # Pad with default values if more features are requested
            means.extend([50] * (n_features - len(means)))
            old_size = np.shape(cov_matrix)[0]
            new_cov = np.identity(n_features) * 10.0
            new_cov[:old_size, :old_size] = cov_matrix
            cov_matrix = new_cov.tolist()

        features_dict = SyntheticCurveFactory.create_correlated_curves(
            n_points=n_points,
            means=means[:n_features],
            cov_matrix=np.array(cov_matrix)[:n_features, :n_features].tolist(),
            curve_names=feature_names,
        )

        # 2. Create a target variable that is a linear combination of features + noise
        feature_matrix = np.column_stack(list(features_dict.values()))
        weights = np.array([-0.001, -0.1, 0.5])[:n_features]  # Example weights
        target_data = np.dot(feature_matrix, weights)
        target_data += np.random.normal(0, np.std(target_data) * 0.1, size=n_points)
        target_data = np.clip(target_data, 0.01, 0.5)  # Clip to be like porosity

        # 3. Build the input dataset
        input_dataset = DatasetBuilder.quick_continuous_dataset(
            name="obmiq_input", curves=features_dict
        )

        return input_dataset, target_data
