"""WP Excel写入器配置类。

定义Excel格式化配置，支持字体、颜色、数值格式等自定义设置。
遵循CCG编码规范，使用dataclass和类型注解。

Architecture
------------
层次/依赖: I/O层配置模块，被wp_excel_writer使用
设计原则: 类型安全、可配置性、默认值合理
性能特征: 编译时检查、零运行时开销

Examples
--------
>>> # 使用默认配置
>>> config = ExcelFormattingConfig()
>>> 
>>> # 自定义配置
>>> config = ExcelFormattingConfig(
...     default_font_size=10,
...     structure_bg_color="FFFF99",
...     bool_false_value="FALSE"
... )

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》§2.2 - 配置类设计
- 《SCAPE_CCG_编码与通用规范.md》- 编码规范
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Literal

__all__ = [
    "ExcelFormattingConfig",
]


@dataclass
class ExcelFormattingConfig:
    """Excel格式化配置。
    
    定义Excel文件写入时的格式化选项，包括字体、颜色、数值格式等。
    所有配置项都有合理的默认值，用户可以根据需要自定义。
    
    Architecture
    ------------
    层次/依赖: I/O层配置类，被excel_formatter使用
    设计原则: 不可变配置、类型安全、默认值优先
    性能特征: 编译时检查、配置验证
    
    Attributes:
        default_font_name: 默认字体名称
        default_font_size: 默认字体大小
        structure_bg_color: 表单结构指示区背景色（十六进制）
        header_bg_color: 数据区标题行背景色（十六进制）
        depth_decimal_places: 深度列小数位数
        float_format: 浮点数格式字符串
        bool_true_value: 布尔真值的字符串表示
        bool_false_value: 布尔假值的字符串表示
        enable_formatting: 是否启用格式化
        enable_auto_width: 是否启用自动列宽调整
        enable_freeze_panes: 是否启用冻结窗格
        enable_borders: 是否启用表格边框
    
    Examples:
        >>> # 默认配置
        >>> config = ExcelFormattingConfig()
        >>> assert config.default_font_name == "Arial"
        >>> assert config.default_font_size == 9
        >>>
        >>> # 自定义配置
        >>> config = ExcelFormattingConfig(
        ...     default_font_size=10,
        ...     structure_bg_color="FFFF99",
        ...     bool_false_value="FALSE"
        ... )
        >>> assert config.default_font_size == 10
        >>>
        >>> # 禁用格式化
        >>> config = ExcelFormattingConfig(enable_formatting=False)
        >>> assert not config.enable_formatting
    
    References:
        《SCAPE_DDS_logwp_io_write_wp_excel.md》§2.2 - 配置类设计
    """
    
    # 字体配置
    default_font_name: str = "Arial"
    default_font_size: int = 9
    
    # 颜色配置（十六进制颜色代码，不含#前缀）
    structure_bg_color: str = "FFFF00"      # 黄色，表单结构指示区
    header_bg_color: str = "CCFFFF"         # 浅蓝色，数据区标题行
    
    # 数值格式配置
    depth_decimal_places: int = 2           # 深度列小数位数
    float_format: str = "[>=1E+05]0.00E+00;[<=-1E+05]0.00E+00;0.0000"  # 浮点数格式
    
    # 布尔值配置
    bool_true_value: str = "T"              # 布尔真值
    bool_false_value: str = "N"             # 布尔假值
    
    # 格式化开关
    enable_formatting: bool = True          # 是否启用格式化
    enable_auto_width: bool = True          # 是否启用自动列宽调整
    enable_freeze_panes: bool = True        # 是否启用冻结窗格
    enable_borders: bool = True             # 是否启用表格边框
    
    def __post_init__(self) -> None:
        """配置验证。
        
        验证配置参数的有效性，确保颜色代码格式正确、数值范围合理等。
        
        Raises:
            ValueError: 配置参数无效
        """
        # 验证字体大小
        if not (6 <= self.default_font_size <= 72):
            raise ValueError(f"字体大小必须在6-72之间: {self.default_font_size}")
        
        # 验证颜色代码格式（6位十六进制）
        if not self._is_valid_hex_color(self.structure_bg_color):
            raise ValueError(f"无效的结构背景色: {self.structure_bg_color}")
        
        if not self._is_valid_hex_color(self.header_bg_color):
            raise ValueError(f"无效的标题背景色: {self.header_bg_color}")
        
        # 验证深度小数位数
        if not (0 <= self.depth_decimal_places <= 10):
            raise ValueError(f"深度小数位数必须在0-10之间: {self.depth_decimal_places}")
        
        # 验证布尔值字符串
        if not self.bool_true_value or not self.bool_false_value:
            raise ValueError("布尔值字符串不能为空")
        
        if self.bool_true_value == self.bool_false_value:
            raise ValueError("布尔真值和假值不能相同")
    
    def _is_valid_hex_color(self, color: str) -> bool:
        """验证十六进制颜色代码。
        
        Args:
            color: 颜色代码字符串
            
        Returns:
            bool: 是否为有效的6位十六进制颜色代码
        """
        if len(color) != 6:
            return False
        
        try:
            int(color, 16)
            return True
        except ValueError:
            return False
    
    @classmethod
    def create_minimal(cls) -> ExcelFormattingConfig:
        """创建最小格式化配置。
        
        禁用所有格式化功能，仅写入数据，适用于性能优先场景。
        
        Returns:
            ExcelFormattingConfig: 最小格式化配置
        """
        return cls(
            enable_formatting=False,
            enable_auto_width=False,
            enable_freeze_panes=False,
            enable_borders=False
        )
    
    @classmethod
    def create_enhanced(cls) -> ExcelFormattingConfig:
        """创建增强格式化配置。
        
        使用更大的字体和更鲜明的颜色，适用于演示和报告场景。
        
        Returns:
            ExcelFormattingConfig: 增强格式化配置
        """
        return cls(
            default_font_size=10,
            structure_bg_color="FFD700",  # 金色
            header_bg_color="87CEEB",     # 天蓝色
            depth_decimal_places=3
        )
    
    def get_depth_format(self) -> str:
        """获取深度列的数值格式字符串。
        
        Returns:
            str: Excel数值格式字符串
        """
        return f"0.{'0' * self.depth_decimal_places}"
    
    def validate_colors(self) -> bool:
        """验证所有颜色配置。
        
        Returns:
            bool: 所有颜色配置是否有效
        """
        return (
            self._is_valid_hex_color(self.structure_bg_color) and
            self._is_valid_hex_color(self.header_bg_color)
        )
