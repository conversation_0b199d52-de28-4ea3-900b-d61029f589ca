"""
Defines the ConstantRegistry, which manages all physical constant definitions.
"""
from typing import TYPE_CHECKING

from .containers import Constant

if TYPE_CHECKING:
    from logwp.extras.units import UnitRegistry


class ConstantRegistry:
    """
    Manages the definition and retrieval of physical constants.
    """

    def __init__(self, unit_registry: "UnitRegistry"):
        self._unit_registry = unit_registry
        self._constants: dict[str, Constant] = {}
        self._symbol_map: dict[str, Constant] = {}

        self._register_core_constants()
        self._register_petroleum_constants()

    def register(self, constant: Constant):
        """Registers a new constant."""
        if constant.name in self._constants:
            raise ValueError(f"Constant '{constant.name}' is already defined.")
        if constant.symbol and constant.symbol in self._symbol_map:
            raise ValueError(f"Symbol '{constant.symbol}' is already used by constant '{self._symbol_map[constant.symbol].name}'.")

        self._constants[constant.name] = constant
        if constant.symbol:
            self._symbol_map[constant.symbol] = constant

    def __getattr__(self, name: str) -> Constant:
        """Allows retrieving constants using attribute-style access (e.g., pconsts.standard_gravity)."""
        try:
            return self._constants[name]
        except KeyError:
            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'") from None

    def __getitem__(self, key: str) -> Constant:
        """Allows retrieving constants using dictionary-style access (e.g., pconsts['g'])."""
        try:
            return self._constants.get(key) or self._symbol_map[key]
        except KeyError:
            raise KeyError(f"Constant with name or symbol '{key}' not found.") from None

    def _register_core_constants(self):
        """
        Registers fundamental physical constants.

        These are non-domain-specific and form the base of the library.
        """
        Q_ = self._unit_registry.Quantity

        self.register(Constant(
            name='standard_gravity',
            value=Q_(9.80665, 'm/s**2'),
            symbol='g',
            reference='ISO 80000-3:2006'
        ))

        self.register(Constant(
            name='speed_of_light',
            value=Q_(299792458, 'm/s'),
            symbol='c',
            reference='CODATA 2018'
        ))

    def _register_petroleum_constants(self):
        """
        Registers constants commonly used in petroleum engineering.
        """
        Q_ = self._unit_registry.Quantity

        # Standard Temperature and Pressure (STP) are often defined differently
        # depending on the organization. We use the common oilfield standard here.
        self.register(Constant(
            name='standard_pressure',
            value=Q_(14.696, 'psi'),
            symbol='P_std',
            reference='SPE Standard Conditions'
        ))

        self.register(Constant(
            name='standard_temperature',
            value=Q_(60, 'degF'),
            symbol='T_std',
            reference='SPE Standard Conditions'
        ))
