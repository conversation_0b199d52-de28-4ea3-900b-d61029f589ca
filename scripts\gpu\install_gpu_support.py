#!/usr/bin/env python3
"""GPU支持安装脚本

自动检测CUDA版本并安装对应的GPU计算库。

运行方式:
    python scripts/install_gpu_support.py
    python scripts/install_gpu_support.py --cuda-version 12
    python scripts/install_gpu_support.py --check-only
"""

from __future__ import annotations

import sys
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def run_command(cmd: list[str], check: bool = True) -> subprocess.CompletedProcess:
    """运行命令并返回结果。"""
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=check)
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误: {e.stderr.strip()}")
        raise


def detect_cuda_version() -> str | None:
    """检测CUDA版本。"""
    print("=== 检测CUDA版本 ===")

    try:
        # 尝试使用nvidia-smi检测
        result = run_command(["nvidia-smi", "--query-gpu=driver_version", "--format=csv,noheader"], check=False)
        if result.returncode == 0:
            print("✓ 检测到NVIDIA GPU")

            # 尝试获取CUDA版本
            result = run_command(["nvcc", "--version"], check=False)
            if result.returncode == 0:
                output = result.stdout
                if "release 12" in output:
                    print("✓ 检测到CUDA 12.x")
                    return "12"
                elif "release 11" in output:
                    print("✓ 检测到CUDA 11.x")
                    return "11"
                else:
                    print("⚠️ 无法确定CUDA版本")
            else:
                print("⚠️ nvcc命令不可用，可能未安装CUDA开发工具")
        else:
            print("ℹ️ 未检测到NVIDIA GPU或驱动")

    except Exception as e:
        print(f"⚠️ CUDA检测失败: {e}")

    return None


def check_gpu_libraries() -> dict[str, bool]:
    """检查GPU库安装状态。"""
    print("\n=== 检查GPU库安装状态 ===")

    libraries = {
        "cupy": False,
        "cudf": False,
        "numba": False,
    }

    for lib in libraries:
        try:
            __import__(lib)
            libraries[lib] = True
            print(f"✓ {lib}: 已安装")
        except ImportError:
            print(f"✗ {lib}: 未安装")

    return libraries


def install_gpu_libraries(cuda_version: str) -> bool:
    """安装GPU库。"""
    print(f"\n=== 安装CUDA {cuda_version}.x GPU库 ===")

    success = True

    try:
        # 安装基础GPU支持
        print("安装基础GPU支持...")
        run_command([sys.executable, "-m", "pip", "install", "-e", ".[gpu]"])

        # 根据CUDA版本安装cupy
        if cuda_version == "12":
            print("安装CuPy for CUDA 12.x...")
            run_command([sys.executable, "-m", "pip", "install", "cupy-cuda12x"])

            print("安装CuDF for CUDA 12.x...")
            run_command([sys.executable, "-m", "pip", "install", "cudf-cu12"], check=False)

        elif cuda_version == "11":
            print("安装CuPy for CUDA 11.x...")
            run_command([sys.executable, "-m", "pip", "install", "cupy-cuda11x"])

            print("安装CuDF for CUDA 11.x...")
            run_command([sys.executable, "-m", "pip", "install", "cudf-cu11"], check=False)

        else:
            print(f"⚠️ 不支持的CUDA版本: {cuda_version}")
            success = False

    except Exception as e:
        print(f"❌ GPU库安装失败: {e}")
        success = False

    return success


def test_gpu_functionality() -> bool:
    """测试GPU功能。"""
    print("\n=== 测试GPU功能 ===")

    try:
        # 测试logwp GPU功能
        from logwp.infra.gpu import is_gpu_available, get_gpu_info, ComputeEngine

        gpu_available = is_gpu_available()
        print(f"GPU可用性: {'✓ 可用' if gpu_available else '✗ 不可用'}")

        if gpu_available:
            # 获取GPU信息
            try:
                gpu_info = get_gpu_info()
                print(f"GPU设备: {gpu_info['device_name']}")
                print(f"GPU内存: {gpu_info['memory_total_mb']:.1f} MB")
                print(f"计算能力: {gpu_info['compute_capability']}")
            except Exception as e:
                print(f"⚠️ 获取GPU信息失败: {e}")

            # 测试简单计算
            try:
                import numpy as np

                print("测试GPU计算...")
                engine = ComputeEngine(prefer_gpu=True)

                # 创建测试数据
                a = np.random.rand(100, 100).astype(np.float32)
                b = np.random.rand(100, 100).astype(np.float32)

                # 执行GPU计算
                result = engine.compute_matrix_multiply(a, b)

                print(f"✓ GPU计算测试成功")
                print(f"  使用后端: {result.backend}")
                print(f"  计算时间: {result.computation_time_ms:.2f} ms")

                return True

            except Exception as e:
                print(f"⚠️ GPU计算测试失败: {e}")

        return gpu_available

    except Exception as e:
        print(f"❌ GPU功能测试失败: {e}")
        return False


def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description="GPU支持安装脚本")
    parser.add_argument("--cuda-version", choices=["11", "12"], help="指定CUDA版本")
    parser.add_argument("--check-only", action="store_true", help="仅检查，不安装")
    parser.add_argument("--force", action="store_true", help="强制重新安装")

    args = parser.parse_args()

    print("SCAPE项目GPU支持安装脚本\n")

    # 检查当前状态
    cuda_version = args.cuda_version or detect_cuda_version()
    libraries = check_gpu_libraries()

    if args.check_only:
        print("\n📊 检查结果:")
        print(f"  CUDA版本: {cuda_version or '未检测到'}")
        for lib, installed in libraries.items():
            status = "✓ 已安装" if installed else "✗ 未安装"
            print(f"  {lib}: {status}")

        # 测试GPU功能（只要有基础库就可以测试）
        if libraries.get("cupy", False) or libraries.get("numba", False):
            test_gpu_functionality()
        else:
            print("\n⚠️ 缺少基础GPU库，无法测试GPU功能")

        return

    # 安装GPU支持
    if not cuda_version:
        print("❌ 无法检测CUDA版本，请手动指定：")
        print("  python scripts/install_gpu_support.py --cuda-version 12")
        print("  python scripts/install_gpu_support.py --cuda-version 11")
        sys.exit(1)

    # 检查是否需要安装
    if all(libraries.values()) and not args.force:
        print("✓ GPU库已安装，跳过安装步骤")
        print("  使用 --force 参数强制重新安装")
    else:
        # 执行安装
        success = install_gpu_libraries(cuda_version)

        if not success:
            print("\n❌ GPU库安装失败")
            print("💡 手动安装建议:")
            if cuda_version == "12":
                print("  pip install cupy-cuda12x")
                print("  pip install cudf-cu12")
            elif cuda_version == "11":
                print("  pip install cupy-cuda11x")
                print("  pip install cudf-cu11")
            sys.exit(1)

    # 测试安装结果
    print("\n=== 验证安装结果 ===")
    libraries_after = check_gpu_libraries()

    if test_gpu_functionality():
        print("\n🎉 GPU支持安装成功！")
        print("📈 现在可以享受10-15倍的性能提升")
    else:
        print("\n⚠️ GPU支持安装完成，但功能测试失败")
        print("💡 可能的原因:")
        print("  - CUDA驱动版本不兼容")
        print("  - GPU内存不足")
        print("  - 库版本冲突")
        print("  - 系统环境配置问题")


if __name__ == "__main__":
    main()
