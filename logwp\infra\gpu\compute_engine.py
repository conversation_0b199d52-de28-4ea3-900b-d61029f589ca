from __future__ import annotations

from ..constants import WpGpuDefaults

from ..exceptions import WpGpuError

"""logwp.infra.compute_engine - 统一计算接口

实现CPU/GPU统一计算接口，支持自动设备选择和回退机制。

Architecture
------------
层次/依赖: logwp包工具层，统一计算接口
设计原则: 统一接口、自动回退、性能优化、内存管理
性能特征: GPU加速、智能调度、内存优化、异步处理

Core Features
-------------
- **统一接口**: CPU/GPU透明计算接口
- **自动回退**: GPU不可用时自动切换CPU
- **智能调度**: 根据数据大小自动选择设备
- **内存管理**: 智能内存分配和释放
- **性能监控**: 计算性能统计和优化建议

Examples
--------
>>> from logwp.infra import ComputeEngine
>>>
>>> # 创建计算引擎
>>> engine = ComputeEngine(prefer_gpu=True)
>>>
>>> # 统一计算接口
>>> import numpy as np
>>> data = np.random.rand(1000, 1000)
>>> result = engine.compute_matrix_multiply(data, data.T)
>>> print(f"使用设备: {engine.current_backend}")
"""

import time
from typing import Any, Dict, Optional, Union, Callable, TYPE_CHECKING
from dataclasses import dataclass, field
from enum import Enum

from .gpu_utils import (
    is_gpu_available, get_gpu_info, auto_select_device,
    to_gpu, to_cpu, optimize_gpu_memory
)
from ..constants import WpComputeBackend
from ...models.exceptions import WpError, ErrorContext
from ..logging_config import get_logger

if TYPE_CHECKING:
    import numpy as np

__all__ = [
    "ComputeEngine",
    "ComputeBackend",
    "ComputeResult",
    "BackendSelector",
]

logger = get_logger(__name__)


class ComputeBackend(Enum):
    """计算后端枚举。"""
    CPU = "cpu"
    GPU = "gpu"
    AUTO = "auto"


@dataclass
class ComputeResult:
    """计算结果包装器。

    Architecture
    ------------
    层次/依赖: 计算结果封装，包含性能信息
    设计原则: 结果封装、性能统计、设备信息
    性能特征: 轻量级封装、性能监控
    """

    data: Any
    backend: str
    computation_time_ms: float
    memory_used_mb: Optional[float] = None
    device_info: Optional[Dict[str, Any]] = None

    def to_cpu(self) -> ComputeResult:
        """将结果转换到CPU。"""
        cpu_data = to_cpu(self.data)
        return ComputeResult(
            data=cpu_data,
            backend="cpu",
            computation_time_ms=self.computation_time_ms,
            memory_used_mb=self.memory_used_mb,
            device_info=self.device_info
        )


class BackendSelector:
    """计算后端选择器。

    Architecture
    ------------
    层次/依赖: 计算后端选择逻辑
    设计原则: 智能选择、性能优化、自动回退
    性能特征: 快速决策、内存感知、负载均衡
    """

    def __init__(self, prefer_gpu: bool = True):
        """初始化后端选择器。

        Args:
            prefer_gpu: 是否优先使用GPU
        """
        self.prefer_gpu = prefer_gpu
        self._gpu_available = is_gpu_available()

    def select_backend(
        self,
        data_size_mb: float,
        operation_type: str = "general",
        force_backend: Optional[str] = None
    ) -> str:
        """选择计算后端。

        Args:
            data_size_mb: 数据大小(MB)
            operation_type: 操作类型
            force_backend: 强制使用的后端

        Returns:
            str: 选择的后端 ("cpu" 或 "gpu")
        """
        if force_backend:
            if force_backend == "gpu" and not self._gpu_available:
                logger.warning(
                    "强制使用GPU但GPU不可用，回退到CPU",
                    operation=operation_type,
                    data_size_mb=data_size_mb
                )
                return "cpu"
            return force_backend

        return auto_select_device(data_size_mb, self.prefer_gpu)


class ComputeEngine:
    """统一计算引擎。

    Architecture
    ------------
    层次/依赖: logwp包统一计算接口
    设计原则: 统一接口、自动回退、性能优化
    性能特征: GPU加速、智能调度、内存管理

    Examples:
        >>> engine = ComputeEngine(prefer_gpu=True)
        >>> result = engine.compute_matrix_multiply(a, b)
        >>> print(f"计算时间: {result.computation_time_ms:.2f}ms")
    """

    def __init__(
        self,
        prefer_gpu: bool = True,
        auto_optimize_memory: bool = True,
        performance_monitoring: bool = True
    ):
        """初始化计算引擎。

        Args:
            prefer_gpu: 是否优先使用GPU
            auto_optimize_memory: 是否自动优化内存
            performance_monitoring: 是否启用性能监控
        """
        self.prefer_gpu = prefer_gpu
        self.auto_optimize_memory = auto_optimize_memory
        self.performance_monitoring = performance_monitoring

        self.backend_selector = BackendSelector(prefer_gpu)
        self._current_backend = "cpu"
        self._performance_stats = {
            "total_computations": 0,
            "gpu_computations": 0,
            "cpu_computations": 0,
            "total_time_ms": 0.0,
            "gpu_time_ms": 0.0,
            "cpu_time_ms": 0.0,
        }

        logger.info(
            "计算引擎初始化",
            prefer_gpu=prefer_gpu,
            gpu_available=is_gpu_available(),
            auto_optimize_memory=auto_optimize_memory
        )

    @property
    def current_backend(self) -> str:
        """当前使用的计算后端。"""
        return self._current_backend

    @property
    def performance_stats(self) -> Dict[str, Any]:
        """性能统计信息。"""
        stats = self._performance_stats.copy()
        if stats["total_computations"] > 0:
            stats["avg_time_ms"] = stats["total_time_ms"] / stats["total_computations"]
            stats["gpu_usage_percent"] = (stats["gpu_computations"] / stats["total_computations"]) * 100
        return stats

    def _estimate_data_size(self, *args: Any) -> float:
        """估算数据大小(MB)。"""
        total_size = 0
        for arg in args:
            if hasattr(arg, "nbytes"):  # numpy数组
                total_size += arg.nbytes
            elif hasattr(arg, "memory_usage"):  # pandas DataFrame
                total_size += arg.memory_usage(deep=True).sum()
            elif isinstance(arg, (list, tuple)):
                total_size += len(arg) * 8  # 估算

        return total_size / 1024 / 1024  # 转换为MB

    def _execute_computation(
        self,
        computation_func: Callable,
        backend: str,
        *args: Any,
        **kwargs: Any
    ) -> ComputeResult:
        """执行计算。"""
        start_time = time.time()

        try:
            if backend == "gpu":
                # 转换数据到GPU
                gpu_args = [to_gpu(arg) for arg in args]
                gpu_kwargs = {k: to_gpu(v) for k, v in kwargs.items()}

                # 执行GPU计算
                result = computation_func(*gpu_args, **gpu_kwargs)

                # 可选：自动优化内存
                if self.auto_optimize_memory:
                    optimize_gpu_memory()

            else:
                # CPU计算
                result = computation_func(*args, **kwargs)

            computation_time = (time.time() - start_time) * 1000  # 转换为毫秒

            # 更新性能统计
            if self.performance_monitoring:
                self._update_performance_stats(backend, computation_time)

            # 获取设备信息
            device_info = None
            if backend == "gpu" and is_gpu_available():
                try:
                    device_info = get_gpu_info()
                except Exception:
                    pass

            return ComputeResult(
                data=result,
                backend=backend,
                computation_time_ms=computation_time,
                device_info=device_info
            )

        except Exception as e:
            # GPU计算失败，尝试CPU回退
            if backend == "gpu":
                logger.warning(
                    "GPU计算失败，回退到CPU",
                    error=str(e),
                    operation="computation_fallback"
                )
                return self._execute_computation(computation_func, "cpu", *args, **kwargs)
            else:
                raise WpError(
                    f"计算失败: {e}",
                    context=ErrorContext(
                        operation="compute_execution",
                        backend=backend,
                        error_type=type(e).__name__
                    )
                ) from e

    def _update_performance_stats(self, backend: str, computation_time: float) -> None:
        """更新性能统计。"""
        self._performance_stats["total_computations"] += 1
        self._performance_stats["total_time_ms"] += computation_time

        if backend == "gpu":
            self._performance_stats["gpu_computations"] += 1
            self._performance_stats["gpu_time_ms"] += computation_time
        else:
            self._performance_stats["cpu_computations"] += 1
            self._performance_stats["cpu_time_ms"] += computation_time

    def compute_matrix_multiply(
        self,
        a: Any,
        b: Any,
        force_backend: Optional[str] = None
    ) -> ComputeResult:
        """矩阵乘法计算。

        Args:
            a: 矩阵A
            b: 矩阵B
            force_backend: 强制使用的后端

        Returns:
            ComputeResult: 计算结果
        """
        data_size_mb = self._estimate_data_size(a, b)
        backend = self.backend_selector.select_backend(
            data_size_mb, "matrix_multiply", force_backend
        )
        self._current_backend = backend

        def matrix_multiply(x, y):
            if hasattr(x, "dot"):  # numpy/cupy数组
                return x.dot(y)
            else:
                # 回退到标准乘法
                import numpy as np
                return np.dot(x, y)

        return self._execute_computation(matrix_multiply, backend, a, b)

    def compute_element_wise(
        self,
        operation: str,
        *arrays: Any,
        force_backend: Optional[str] = None
    ) -> ComputeResult:
        """元素级计算。

        Args:
            operation: 操作类型 ("add", "multiply", "sqrt", etc.)
            *arrays: 输入数组
            force_backend: 强制使用的后端

        Returns:
            ComputeResult: 计算结果
        """
        data_size_mb = self._estimate_data_size(*arrays)
        backend = self.backend_selector.select_backend(
            data_size_mb, f"element_wise_{operation}", force_backend
        )
        self._current_backend = backend

        def element_wise_op(*args):
            if backend == "gpu":
                import cupy as cp
                if operation == "add":
                    return cp.add(*args)
                elif operation == "multiply":
                    return cp.multiply(*args)
                elif operation == "sqrt":
                    return cp.sqrt(args[0])
                else:
                    raise ValueError(f"不支持的GPU操作: {operation}")
            else:
                import numpy as np
                if operation == "add":
                    return np.add(*args)
                elif operation == "multiply":
                    return np.multiply(*args)
                elif operation == "sqrt":
                    return np.sqrt(args[0])
                else:
                    raise ValueError(f"不支持的CPU操作: {operation}")

        return self._execute_computation(element_wise_op, backend, *arrays)
