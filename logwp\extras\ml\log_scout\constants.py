"""Constants for the LogScout step, including artifact names and plot types."""

from enum import Enum


class LogScoutArtifacts(str, Enum):
    """Defines all logical artifact names for the LogScout step."""

    # --- Quantitative Reports ---
    PEARSON_CORR = "log_scout.reports.pearson_correlation"
    SPEARMAN_CORR = "log_scout.reports.spearman_correlation"
    VIF_SCORES = "log_scout.reports.vif_scores"
    MUTUAL_INFO = "log_scout.reports.mutual_information"

    # --- Data Snapshots (for plot reproducibility) ---
    PEARSON_CORR_DATA = "log_scout.data_snapshots.pearson_correlation"
    SPEARMAN_CORR_DATA = "log_scout.data_snapshots.spearman_correlation"
    PAIRPLOT_DATA = "log_scout.data_snapshots.pairplot_data"
    # Prefix for data snapshots of target relationship plots
    TARGET_RELATIONSHIP_DATA_PREFIX = "log_scout.data_snapshots.target_relationship"

    # --- Visual Reports: Feature Correlation ---
    PEARSON_HEATMAP = "log_scout.plots.pearson_heatmap"
    PEARSON_CLUSTERMAP = "log_scout.plots.pearson_clustermap"
    SPEARMAN_HEATMAP = "log_scout.plots.spearman_heatmap"
    SPEARMAN_CLUSTERMAP = "log_scout.plots.spearman_clustermap"
    PAIRPLOT = "log_scout.plots.pairplot"

    # --- Visual Reports: Target Relationship (Prefix Pattern) ---
    # Actual names are generated at runtime, e.g.,
    # log_scout.plots.target_relationship.regplot_GR
    TARGET_RELATIONSHIP_PLOT_PREFIX = "log_scout.plots.target_relationship"


class LogScoutPlotTypes(str, Enum):
    """Defines plot types, used as keys in the plot_profiles dictionary."""

    PEARSON_HEATMAP = "pearson_heatmap"
    PEARSON_CLUSTERMAP = "pearson_clustermap"
    SPEARMAN_HEATMAP = "spearman_heatmap"
    SPEARMAN_CLUSTERMAP = "spearman_clustermap"
    PAIRPLOT = "pairplot"
    REGRESSION_PLOT = "regression_plot"  # For regression tasks
    BOX_PLOT = "box_plot"  # For classification tasks


class LogScoutPlotProfiles(str, Enum):
    """Defines the names of PlotProfile templates to be registered in the global registry."""

    BASE = "log_scout.base"
    HEATMAP = "log_scout.heatmap"
    CLUSTERMAP = "log_scout.clustermap"
    PAIRPLOT = "log_scout.pairplot"
    REGPLOT = "log_scout.regplot"
    BOXPLOT = "log_scout.boxplot"
