{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ数据准备 \n", "\n", "- 使用DS_F==1挑选有效层段\n", "- 去掉T-1井下段致密段（6630以下)\n", "- 表格数据使用更多的输入曲线\n", "  - DEN, CN, DT, RD_LOG10, RS_LOG10, DRES\n", "  - T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10\n", "  - PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR\n", "  - VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO\n", "  - LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-30T08:14:50.245308Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 130.2, 'cpu_percent': 0.0}\n", "库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T08:14:52.843140Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 358.69, 'cpu_percent': 0.0} file_path=santos_data_v1.wp.xlsx\n", "2025-07-30T08:14:52.911522Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 359.73, 'cpu_percent': 0.0} file_path=santos_data_v1.wp.xlsx file_size_mb=28.86 sheet_count=8\n", "2025-07-30T08:14:52.922224Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 359.73, 'cpu_percent': 0.0} project_name=santos_data_v1\n", "2025-07-30T08:14:52.934324Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 359.74, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data_v1\n", "2025-07-30T08:14:52.947661Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 359.75, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-30T08:14:52.965145Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 359.75, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-30T08:14:52.978506Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 359.75, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-07-30T08:14:52.989976Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 359.75, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-30T08:14:53.001151Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 360.0, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:14:53.041640Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 360.39, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=74 well_curves=1\n", "2025-07-30T08:15:33.616783Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.02, 'cpu_percent': 0.0} shape=(16303, 263) sheet_name=Logs\n", "2025-07-30T08:15:33.683499Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.33, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-07-30T08:15:33.710186Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.33, 'cpu_percent': 0.0} curve_count=74 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 263) processing_time=40.709\n", "2025-07-30T08:15:33.749559Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.39, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:33.791230Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.41, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=91 well_curves=1\n", "2025-07-30T08:15:38.325363Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.41, 'cpu_percent': 0.0} shape=(4502, 91) sheet_name=OBMIQ_Pred\n", "2025-07-30T08:15:38.364889Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.42, 'cpu_percent': 0.0} dataset_name=WpIdentifier('OBMIQ_Pred')\n", "2025-07-30T08:15:38.390755Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.42, 'cpu_percent': 0.0} curve_count=91 dataset_name=OBMIQ_Pred dataset_type=Continuous df_shape=(4502, 91) processing_time=4.649\n", "2025-07-30T08:15:38.418142Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.45, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-07-30T08:15:38.445800Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.45, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-30T08:15:38.539323Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-07-30T08:15:38.555981Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-07-30T08:15:38.567378Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.152\n", "2025-07-30T08:15:38.592067Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-30T08:15:38.618701Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-30T08:15:38.652082Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} shape=(15, 4) sheet_name=PLT\n", "2025-07-30T08:15:38.671880Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-30T08:15:38.671880Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(15, 4) processing_time=0.08\n", "2025-07-30T08:15:38.711560Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-30T08:15:38.751607Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.53, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-30T08:15:38.777732Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-07-30T08:15:38.791751Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-30T08:15:38.807722Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.096\n", "2025-07-30T08:15:38.830981Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} dataset_count=5 has_head_info=True has_well_map=True\n", "2025-07-30T08:15:38.849981Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} dataset_count=5\n", "2025-07-30T08:15:38.860782Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} dataset_count=5 file_path=santos_data_v1.wp.xlsx processing_time=46.018 project_name=WpIdentifier('santos_data_v1')\n", "2025-07-30T08:15:38.871973Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.57, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data_v1'}\n", "2025-07-30T08:15:38.925491Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.65, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 5}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data_v1\n", "📅 创建时间: 2025-07-30 16:14:52.934324\n", "🔧 默认深度单位: WpDepthUnit.METER\n"]}], "source": ["data_file_path = \"./santos_data_v1.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 创建时间: {project.created_at}\")\n", "    print(f\"🔧 默认深度单位: {project.default_depth_reference_unit}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 生成测井数据概况报告"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 提取OBMIQ累积分布数据集"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 准备提取OBMIQ相关曲线，共31条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR, VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO, LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY, PHI_T2_DIST_CUM, DT2_P50, DPHIT_NMR\n", "📊 准备提取OBMIQ相关曲线（dropna），共29条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR, VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO, LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY, PHI_T2_DIST_CUM\n"]}], "source": ["# 定义要提取的曲线列表\n", "# obmiq_curves = [\n", "#    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "#    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',\n", "#    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',\n", "#    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "#]\n", "\n", "#log_scout分析结果\n", "obmiq_curves = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "]\n", "\n", "obmiq_curves_dropna = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM'\n", "]\n", "\n", "print(f\"📊 准备提取OBMIQ相关曲线，共{len(obmiq_curves)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves)}\")\n", "print(f\"📊 准备提取OBMIQ相关曲线（dropna），共{len(obmiq_curves_dropna)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves_dropna)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始提取OBMIQ数据集(训练)...\n", "\n", "📍 提取C-1井数据(训练)...\n", "2025-07-30T08:15:39.096132Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.75, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T08:15:39.165585Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.84, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50'] operation=extract_metadata output_curve_count=33 output_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50']\n", "2025-07-30T08:15:39.484656Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.0, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:39.512376Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T08:15:39.552422Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.07, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-30T08:15:39.592261Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:39.617786Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.07, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=894\n", "2025-07-30T08:15:39.645814Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.08, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_c_1\n", "2025-07-30T08:15:39.672356Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=894 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=894 removed_rows=0\n", "2025-07-30T08:15:39.699122Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T08:15:39.725646Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:39.752258Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T08:15:39.765757Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_c1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_C1') save_head_info=True save_well_map=True\n", "2025-07-30T08:15:39.792423Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(894, 96)\n", "2025-07-30T08:15:40.990748Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=1.198\n", "2025-07-30T08:15:41.017457Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.09, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T08:15:41.163481Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.13, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_c1.wp.xlsx processing_time=1.398 project_name=WpIdentifier('Santos_OBMIQ_C1')\n", "✅ C-1井数据已保存: santos_obmiq_cum_c1.wp.xlsx\n", "   数据形状: (894, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取T-1井数据(训练)...\n", "2025-07-30T08:15:41.200974Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.13, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T08:15:41.236598Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 439.13, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50'] operation=extract_metadata output_curve_count=33 output_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50']\n", "2025-07-30T08:15:41.494248Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.83, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:41.533271Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.83, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T08:15:41.561977Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.83, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-30T08:15:41.595157Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.83, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:41.617972Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.83, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=1189\n", "2025-07-30T08:15:41.637465Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.83, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_t_1\n", "2025-07-30T08:15:41.668899Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1189 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.84, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1189 removed_rows=0\n", "2025-07-30T08:15:41.688076Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.84, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T08:15:41.722402Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.84, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:41.741051Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.84, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T08:15:41.752303Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.84, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_t1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_T1') save_head_info=True save_well_map=True\n", "2025-07-30T08:15:41.772280Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.84, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1189, 96)\n", "2025-07-30T08:15:43.313252Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.96, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=1.541\n", "2025-07-30T08:15:43.347024Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.96, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T08:15:43.528794Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.96, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_t1.wp.xlsx processing_time=1.776 project_name=WpIdentifier('Santos_OBMIQ_T1')\n", "✅ T-1井数据已保存: santos_obmiq_cum_t1.wp.xlsx\n", "   数据形状: (1189, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取所有井数据(训练)...\n", "2025-07-30T08:15:43.560659Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.96, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T08:15:43.593242Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.96, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50'] operation=extract_metadata output_curve_count=33 output_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50']\n", "2025-07-30T08:15:43.887014Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.24, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:43.918733Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.97, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-30T08:15:43.950447Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.97, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-30T08:15:43.981389Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.97, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:44.013018Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 478.97, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=14285\n", "2025-07-30T08:15:44.029158Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 453.48, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all\n", "2025-07-30T08:15:44.060797Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=2083 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.02, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=14285 removed_rows=12202\n", "2025-07-30T08:15:44.092588Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.09, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T08:15:44.119148Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.09, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:44.126469Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.09, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T08:15:44.140055Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.09, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All') save_head_info=True save_well_map=True\n", "2025-07-30T08:15:44.155395Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 455.09, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(2083, 96)\n", "2025-07-30T08:15:45.004189Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 473.17, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.849\n", "2025-07-30T08:15:45.051802Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 471.66, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T08:15:45.083493Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 471.67, 'cpu_percent': 0.0}\n", "2025-07-30T08:15:45.086053Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 471.67, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-30T08:15:51.116745Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 496.39, 'cpu_percent': 0.0}\n", "2025-07-30T08:15:51.129979Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 496.4, 'cpu_percent': 0.0}\n", "2025-07-30T08:15:54.345682Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.98, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx processing_time=10.206 project_name=WpIdentifier('Santos_OBMIQ_All')\n", "✅ 所有井数据已保存: santos_obmiq_cum_all.wp.xlsx\n", "   数据形状: (2083, 96)\n", "   数据集类型: WpContinuousDataset\n", "   井名分布: {'T-1': 1189, 'C-1': 894}\n", "\n", "📍 提取所有井数据(预测)...\n", "2025-07-30T08:15:54.384529Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.04, 'cpu_percent': 0.0} curve_count=31 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq_all_apply\n", "2025-07-30T08:15:54.411225Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.04, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50'] operation=extract_metadata output_curve_count=33 output_curves=['T2_P50_LOG10', 'T2LM_LONG_LOG10', 'SWB_NMR', 'MD', 'LT2STDDEV_FFI', 'DPHIT_NMR', 'DEN', 'RS_LOG10', 'WELL_NO', 'FFV_NMR', 'T2LM_LOG10', 'T2_P20_LOG10', 'BVI_NMR', 'PHI_T2_DIST_CUM', 'DRES', 'CN', 'LSKEW_FFI', 'SMESO', 'VMESO', 'VMICRO', 'RD_LOG10', 'SWI_NMR', 'SMACRO', 'BFV_NMR', 'SDR_PROXY', 'SFF_NMR', 'PHIE_NMR', 'VMACRO', 'LKURT_FFI', 'PHIT_NMR', 'SMICRO', 'DT', 'DT2_P50']\n", "2025-07-30T08:15:54.541203Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 539.64, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:54.557543Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 540.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-30T08:15:54.584049Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 540.07, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq_all_apply target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-30T08:15:54.610689Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 540.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:54.624130Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 540.07, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=16303\n", "2025-07-30T08:15:54.663522Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 507.99, 'cpu_percent': 0.0} curve_count=29 dropna_how=any new_dataset=nmr_obmiq_all_apply_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all_apply\n", "2025-07-30T08:15:54.706902Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=4504 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.39, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=11799\n", "2025-07-30T08:15:54.726936Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T08:15:54.745053Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.38, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T08:15:54.771207Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.38, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T08:15:54.784712Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.38, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All_Apply') save_head_info=True save_well_map=True\n", "2025-07-30T08:15:54.811424Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.38, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') dataset_type=Continuous df_shape=(4504, 96)\n", "2025-07-30T08:15:56.969334Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 546.4, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') processing_time=2.158\n", "2025-07-30T08:15:57.012127Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 543.14, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T08:15:57.038845Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 543.14, 'cpu_percent': 0.0}\n", "2025-07-30T08:15:57.052132Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 543.14, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-30T08:16:09.870464Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 596.62, 'cpu_percent': 0.0}\n", "2025-07-30T08:16:09.897183Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 596.62, 'cpu_percent': 0.0}\n", "2025-07-30T08:16:16.849055Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.92, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=22.064 project_name=WpIdentifier('Santos_OBMIQ_All_Apply')\n", "2025-07-30T08:16:16.872996Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.92, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All_Apply\n", "2025-07-30T08:16:17.860192Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.48, 'cpu_percent': 0.0} file_size=41421 format=markdown output_path=santos_obmiq_cum_all_apply_report.md\n", "✅ 所有井数据(预测）已保存: santos_obmiq_cum_all_apply.wp.xlsx\n", "   数据形状: (4504, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "🎉 OBMIQ数据集提取完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "    from logwp.models.well_project import WpWellProject\n", "\n", "    print(\"🔧 开始提取OBMIQ数据集(训练)...\")\n", "\n", "    try:\n", "        # 1. 提取C-1井的数据\n", "        print(\"\\n📍 提取C-1井数据(训练)...\")\n", "        c1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'C-1' and DS_F == 1 \"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_c_1\", c1_dataset)\n", "        c1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_c_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_c1 = WpWellProject(name=\"Santos_OBMIQ_C1\")\n", "        temp_project_c1.add_dataset(\"nmr_obmiq\", c1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        c1_path = \"santos_obmiq_cum_c1.wp.xlsx\"\n", "        writer.write(temp_project_c1, c1_path, apply_formatting=False)\n", "\n", "        print(f\"✅ C-1井数据已保存: {c1_path}\")\n", "        print(f\"   数据形状: {c1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(c1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ C-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 2. 提取T-1井的数据\n", "        print(\"\\n📍 提取T-1井数据(训练)...\")\n", "        t1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'T-1' and DS_F == 1 \"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_t_1\", t1_dataset)\n", "        t1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_t_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_t1 = WpWellProject(name=\"Santos_OBMIQ_T1\")\n", "        temp_project_t1.add_dataset(\"nmr_obmc\", t1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        t1_path = \"santos_obmiq_cum_t1.wp.xlsx\"\n", "        writer.write(temp_project_t1, t1_path, apply_formatting=False)\n", "\n", "        print(f\"✅ T-1井数据已保存: {t1_path}\")\n", "        print(f\"   数据形状: {t1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(t1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ T-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 3. 提取所有井的数据\n", "        print(\"\\n📍 提取所有井数据(训练)...\")\n", "        all_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"DS_F == 1\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all\", all_dataset)\n", "        all_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq\", all_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "\n", "        print(f\"✅ 所有井数据已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_dataset).__name__}\")\n", "\n", "        # 显示井名统计\n", "        if 'WELL_NO' in all_dataset.df.columns:\n", "            well_counts = all_dataset.df['WELL_NO'].value_counts()\n", "            print(f\"   井名分布: {dict(well_counts)}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "\n", "    try:\n", "        # 4. 提取所有井的数据(预测)\n", "        print(\"\\n📍 提取所有井数据(预测)...\")\n", "        all_apply_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves #需要包含真值，这样方便对比\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all_apply\", all_apply_dataset)\n", "        all_apply_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves_dropna, # dropna时不考虑真值\n", "            new_dataset_name=\"nmr_obmiq_all_apply_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All_Apply\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq_apply\", all_apply_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_apply_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据(预测）已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_apply_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_apply_dataset).__name__}\")\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据(预测）提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 OBMIQ数据集提取完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过OBMIQ数据集提取\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}