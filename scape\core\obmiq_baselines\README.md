# OBMIQ Baselines 组件说明文档

**版本: 1.0**

## 1. 引言

### 1.1. 目标与定位

`scape.core.obmiq_baselines` 是一个标准化的、可重用的**多步骤机器学习组件**。它的核心任务与 `scape.core.obmiq` (PyTorch版) 完全相同：基于测井数据，预测油基泥浆侵入导致的 `DT2_P50` 和 `DPHIT_NMR` 变化。

然而，与采用深度学习的 `obmiq` 组件不同，`obmiq_baselines` 采用了一套**经典的、基于Scikit-learn的机器学习流程**。它旨在：

1.  提供一个强大的**基准模型 (Baseline Model)**，用于评估更复杂的深度学习模型的性能增益。
2.  在数据量较小或特征关系相对简单的情况下，提供一个**计算效率更高、可解释性更强**的替代方案。
3.  作为《可追踪机器学习组件开发框架》在经典机器学习技术栈上的一个标准参考实现。

### 1.2. 实现要点

*   **经典技术栈**: 完全基于 `Scikit-learn` 和 `XGBoost` 构建，利用 `Pipeline` 机制确保数据处理流程的稳健性。
*   **稳健的评估流程**: 采用**嵌套交叉验证 (Nested Cross-Validation)** 框架，无偏地评估和选择最佳模型组合，有效避免了“过拟合的评估”。
*   **自动化模型选择与融合**: 流程会自动从多个候选模型（如XGBoost, RandomForest, SVR）中，选择表现最佳的两个进行加权融合，以提升模型的稳定性和泛化能力。
*   **端到端可追踪**: 严格遵循 `logwp.extras.tracking` 规范，从配置、交叉验证到最终模型，所有环节均被 `RunContext` 精确追踪。

---

## 2. 核心概念对照表

| 框架概念 | `obmiq_baselines` 中的具体实现 |
| :--- | :--- |
| **多步骤包** | `scape.core.obmiq_baselines` 整个包 |
| **步骤 (Step)** | 1. OBMIQ Baselines 训练步骤 <br> 2. OBMIQ Baselines 预测步骤 |
| **门面 (Facade)** | 1. `training_facade.py` <br> 2. `prediction_facade.py` |
| **主执行函数** | 1. `run_obmiq_baselines_training_step()` <br> 2. `run_obmiq_baselines_prediction_step()` |
| **配置 (Config)** | 1. `config.ObmiqBaselinesTrainingConfig` <br> 2. `config.ObmiqBaselinesPredictionConfig` |
| **内部逻辑 (Internal)** | `internal/nested_cv_procedure.py` <br> `internal/final_training_procedure.py` <br> `internal/model_definitions.py` |
| **产物常量** | 1. `constants.ObmiqBaselinesTrainingArtifacts` <br> 2. `constants.ObmiqBaselinesPredictionArtifacts` |
| **产物处理器** | `artifact_handler.ObmiqBaselinesArtifactHandler` |
| **绘图复现** | `plotting.py` (包含多个`replot_*`函数) |
| **绘图配置常量** | `constants.ObmiqBaselinesPlotProfiles` |
| **数据快照** | 所有图表在生成前，都会先保存其绘图所需的数据为`.csv`快照。 |

---

## 3. 组件架构与目录结构

`obmiq_baselines` 作为一个标准的多步骤包，其目录结构如下：

```
scape/core/obmiq_baselines/
├── __init__.py
├── README.md                 # 本文档
├── training_facade.py        # 训练步骤的门面
├── prediction_facade.py      # 预测步骤的门面
├── config.py                 # Pydantic配置模型
├── constants.py              # 产物和绘图模板的常量
├── artifact_handler.py       # 产物处理器
├── plotting.py               # 从数据快照复现图表的功能
├── plot_profiles.py          # 注册本模块专属的PlotProfile
└── internal/                 # 内部实现细节
    ├── __init__.py
    ├── model_definitions.py      # 定义Scikit-learn模型和超参数空间
    ├── nested_cv_procedure.py    # 嵌套交叉验证规程
    ├── final_training_procedure.py # 最终模型训练规程
    └── ensemble_model.py         # WeightedEnsembleModel类的实现
```

---

## 4. API 与使用

`obmiq_baselines`组件通过`training_facade`和`prediction_facade`提供服务。以下是一个典型的工作流示例。

```python
# 在一个工作流脚本中
from pathlib import Path
from logwp.extras.tracking import RunContext
from scape.core.obmiq_baselines import (
    run_obmiq_baselines_training_step,
    run_obmiq_baselines_prediction_step,
    ObmiqBaselinesTrainingConfig,
    ObmiqBaselinesPredictionConfig,
    ObmiqBaselinesTrainingArtifacts,
    ObmiqBaselinesArtifactHandler
)

# 假设 train_bundle, prediction_bundle 已加载

with RunContext(run_dir="path/to/output/run_xyz") as ctx:
    # --- 1. 训练步骤 ---
    print("--- 开始 OBMIQ Baselines 训练步骤 ---")
    training_config = ObmiqBaselinesTrainingConfig(
        n_iter_random_search=50,
        inner_cv_folds=5
    )

    training_results = run_obmiq_baselines_training_step(
        config=training_config,
        ctx=ctx,
        train_bundle=train_bundle,
        tabular_features=["GR", "DEN", "DT", ...],
        target_features=["DT2_P50", "DPHIT_NMR"],
        grouping_feature="WELL_NO"
    )

    # --- 2. 预测步骤 (为每个目标独立调用) ---
    print("\\n--- 开始 OBMIQ Baselines 预测步骤 ---")
    handler = ObmiqBaselinesArtifactHandler()

    # 2a. 预测 DT2_P50
    model_assets_dt2 = handler.load_model_assets(
        ctx.get_artifact_path(f"{ObmiqBaselinesTrainingArtifacts.MODEL_ASSETS.value}_DT2_P50")
    )
    pred_results_dt2 = run_obmiq_baselines_prediction_step(
        config=ObmiqBaselinesPredictionConfig(),
        ctx=ctx,
        model_assets=model_assets_dt2,
        prediction_bundle=prediction_bundle,
        output_curve_name="DT2_P50_PRED"
    )

    # 2b. 预测 DPHIT_NMR
    model_assets_dphit = handler.load_model_assets(
        ctx.get_artifact_path(f"{ObmiqBaselinesTrainingArtifacts.MODEL_ASSETS.value}_DPHIT_NMR")
    )
    pred_results_dphit = run_obmiq_baselines_prediction_step(
        config=ObmiqBaselinesPredictionConfig(),
        ctx=ctx,
        model_assets=model_assets_dphit,
        prediction_bundle=prediction_bundle,
        output_curve_name="DPHIT_NMR_PRED"
    )
```

### 4.1. 配置 (`config.py`)

`ObmiqBaselinesTrainingConfig` 控制训练流程的核心行为：
*   `random_seed`: 全局随机种子，确保可复现性。
*   `inner_cv_folds`: 在嵌套交叉验证的内层循环中，用于特征选择和超参数寻优的K-Fold折数。
*   `n_iter_random_search`: 在`RandomizedSearchCV`中，每个模型要尝试的超参数组合数量。

### 4.2. 产物清单 (Artifacts)

训练步骤会为**每个目标**生成一套独立的产物，并通过在产物逻辑名后附加目标名（如 `_DT2_P50`）来区分。

*   **模型资产 (`MODEL_ASSETS`)**: 核心产物。一个`.joblib`文件，包含最终训练好的`WeightedEnsembleModel`对象和元数据。元数据中包含了预测时必需的、带有固定顺序的特征列表。
*   **嵌套CV报告 (`NESTED_CV_REPORT`)**: 一个`.csv`文件，记录了在嵌套交叉验证的外层循环中，每个候选模型在留出井上的RMSE。这是评估模型泛化能力的核心依据。
*   **最终模型评估数据 (`FINAL_MODEL_EVALUATION_DATA`)**: 一个`.csv`数据快照，包含最终模型在全部训练数据上的预测值和真实值，是所有评估图表的数据源。
*   **评估图表 (`EVAL_*`)**: 一系列`.png`图表，包括预测值-真实值交会图、残差图和残差分布直方图，用于直观评估最终模型的性能。
*   **特征重要性 (`FEATURE_IMPORTANCE_*`)**: 包含数据快照和图表，展示了最终融合模型中各特征的平均重要性得分，提供了模型可解释性。

---

## 5. 核心内部逻辑

*   **模型定义 (`internal/model_definitions.py`)**: 集中定义了所有候选模型（XGBoost, RandomForest, SVR）的`scikit-learn`评估器实例及其对应的超参数搜索空间。
*   **嵌套交叉验证 (`internal/nested_cv_procedure.py`)**:
    *   **目的**: 无偏地评估各候选模型的泛化能力，并确定最佳的两个模型及其融合权重。
    *   **防泄漏核心**: 严格使用`scikit-learn`的`Pipeline`机制，将数据填充、特征选择(`RFECV`)和模型训练封装在一起，整体传递给`RandomizedSearchCV`进行优化，从根本上杜绝了数据泄露。
*   **最终模型训练 (`internal/final_training_procedure.py`)**:
    *   **目的**: 使用嵌套CV选出的“最佳配方”，在**全部数据**上训练出性能最强的、可部署的模型。
    *   **流程**: 为每个选定的模型组件，在全量数据上重新、独立地执行特征选择和超参数寻优（内部使用`GroupKFold`防止井间信息泄露），然后用最优配置训练最终模型。
*   **融合模型 (`internal/ensemble_model.py`)**: 实现了一个简单的`WeightedEnsembleModel`类，用于封装两个最佳模型及其融合权重，并提供统一的`.predict()`接口。

