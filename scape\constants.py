from __future__ import annotations

from enum import Enum


class WpEnvironmentVars(str, Enum):
    """环境变量名称常量。

    Architecture
    ------------
    层次/依赖: 配置层，用于环境变量管理
    设计原则: 统一命名、类型安全、文档化
    性能特征: 运行时配置覆盖、Feature Flag控制
    """

    # 基础配置环境变量
    SCAPE_DEBUG_MODE = "SCAPE_DEBUG_MODE"
    SCAPE_LOG_LEVEL = "SCAPE_LOG_LEVEL"
    SCAPE_CONFIG_FILE = "SCAPE_CONFIG_FILE"

    # 性能配置环境变量
    SCAPE_MAX_CPU_MEMORY_GB = "SCAPE_MAX_CPU_MEMORY_GB"
    SCAPE_MAX_GPU_MEMORY_GB = "SCAPE_MAX_GPU_MEMORY_GB"
    SCAPE_CHUNK_SIZE = "SCAPE_CHUNK_SIZE"
    SCAPE_NUM_WORKERS = "SCAPE_NUM_WORKERS"

    # GPU配置环境变量
    SCAPE_GPU_ENABLED = "SCAPE_GPU_ENABLED"
    SCAPE_GPU_DEVICE_ID = "SCAPE_GPU_DEVICE_ID"
    SCAPE_GPU_MEMORY_FRACTION = "SCAPE_GPU_MEMORY_FRACTION"

    # 异步I/O配置环境变量
    SCAPE_ASYNC_IO_ENABLED = "SCAPE_ASYNC_IO_ENABLED"
    SCAPE_ASYNC_IO_WORKERS = "SCAPE_ASYNC_IO_WORKERS"
    SCAPE_IO_TIMEOUT_SEC = "SCAPE_IO_TIMEOUT_SEC"

    # 缓存配置环境变量
    SCAPE_CACHE_DIR = "SCAPE_CACHE_DIR"
    SCAPE_CACHE_ENABLED = "SCAPE_CACHE_ENABLED"


class WpFeatureFlags(str, Enum):
    """Feature Flag 常量，用于控制实验性功能。

    Architecture
    ------------
    层次/依赖: 特性控制层，用于功能开关
    设计原则: 渐进式功能发布、A/B测试支持
    性能特征: 运行时功能切换、零重启部署
    """

    # GPU计算Feature Flags
    ENABLE_GPU_COMPUTING = "gpu_computing"
    ENABLE_MIXED_PRECISION = "mixed_precision"
    ENABLE_GPU_MEMORY_POOL = "gpu_memory_pool"

    # 异步I/O Feature Flags
    ENABLE_ASYNC_IO = "async_io"
    ENABLE_ASYNC_BATCH_PROCESSING = "async_batch_processing"
    ENABLE_STREAMING_IO = "streaming_io"

    # 算法优化Feature Flags
    ENABLE_JIT_COMPILATION = "jit_compilation"
    ENABLE_VECTORIZED_OPERATIONS = "vectorized_operations"
    ENABLE_PARALLEL_FEATURE_EXTRACTION = "parallel_feature_extraction"

    # 实验性功能Feature Flags
    ENABLE_ADVANCED_CACHING = "advanced_caching"
    ENABLE_PERFORMANCE_PROFILING = "performance_profiling"
    ENABLE_MEMORY_OPTIMIZATION = "memory_optimization"


class WpModernToolDefaults(Enum):
    """现代化工具默认配置。

    Architecture
    ------------
    层次/依赖: 开发工具层配置
    设计原则: 现代化工具链、开发效率、代码质量
    性能特征: 统一工具配置、自动化流程
    """

    # ruff配置
    RUFF_TARGET_VERSION = "py311"
    RUFF_LINE_LENGTH = 88
    RUFF_SELECT_RULES = "E,W,F,I,B,C4,UP,RUF"

    # mypy配置
    MYPY_PYTHON_VERSION = "3.11"
    MYPY_STRICT_MODE = True
    MYPY_WARN_RETURN_ANY = True

    # structlog配置
    STRUCTLOG_FORMAT = "json"
    STRUCTLOG_TIMESTAMP_FORMAT = "iso"
    STRUCTLOG_LOG_LEVEL = "INFO"

    # pydantic配置
    PYDANTIC_VERSION = "v2"
    PYDANTIC_VALIDATE_ASSIGNMENT = True
    PYDANTIC_USE_ENUM_VALUES = True

    # pytest配置
    PYTEST_MIN_VERSION = "7.0"
    PYTEST_COVERAGE_TARGET = 75  # 目标覆盖率75%
    PYTEST_TIMEOUT_SEC = 300

    # 性能监控配置
    ENABLE_PERFORMANCE_LOGGING = True
    PERFORMANCE_LOG_INTERVAL_SEC = 60
    MEMORY_MONITORING_ENABLED = True

    # 开发模式配置
    DEV_MODE_CHUNK_SIZE = 1000  # 开发模式使用小chunk
    DEV_MODE_MAX_ROWS = 10000   # 开发模式数据限制
    DEV_MODE_TIMEOUT_SEC = 30   # 开发模式超时

