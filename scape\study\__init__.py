from __future__ import annotations

"""scape.study - 研究工具层

提供实验配置管理、研究执行引擎和结果分析工具。

Architecture
------------
层次/依赖: scape包研究工具层，依赖scape.core
设计原则: 实验可重现、配置管理、结果分析、报告生成
性能特征: 异步执行、并行分析、结果缓存

Core Features
-------------
- **实验配置**: StudyConfig支持参数空间定义和实验设计
- **执行引擎**: StudyRunner协调三大算法的完整工作流
- **结果分析**: StudyAnalyzer提供统计分析和可视化
- **报告生成**: StudyReporter自动生成科学研究报告
- **可重现性**: 随机种子管理和环境配置记录

Package Structure
-----------------
- config/: 实验配置管理
- runner/: 研究执行引擎
- analyzer/: 结果分析工具
- reporter/: 报告生成器
- types/: 研究相关类型定义

Examples
--------
>>> from scape.study import StudyConfig, StudyRunner
>>>
>>> # 配置实验
>>> config = StudyConfig.from_file("santos_study.json")
>>> config.parameter_space = {
>>>     "c_factor": [2.0, 4.0, 6.0],
>>>     "alpha": [1.5, 2.0, 2.5]
>>> }
>>>
>>> # 执行研究
>>> runner = StudyRunner(config=config)
>>> results = await runner.run_full_study_async(project)
>>>
>>> # 分析结果
>>> analyzer = StudyAnalyzer()
>>> analysis = analyzer.analyze_results(results)
"""

__all__ = [
    # 配置管理
    "StudyConfig",
    "ParameterSpace",
    "ExperimentDesign",

    # 执行引擎
    "StudyRunner",
    "StudyExecutor",
    "StudyScheduler",

    # 结果分析
    "StudyAnalyzer",
    "ResultComparator",
    "StatisticalAnalyzer",

    # 报告生成
    "StudyReporter",
    "ReportGenerator",
    "VisualizationEngine",

    # 类型定义
    "StudyResult",
    "StudyMetrics",
    "StudyStatus",

    # 工具函数
    "create_study_config",
    "validate_study_design",
    "export_study_results",
]


def __getattr__(name: str) -> object:
    """延迟导入研究工具组件。"""
    # 配置管理
    if name in ("StudyConfig", "ParameterSpace", "ExperimentDesign"):
        from scape.study.config import StudyConfig, ParameterSpace, ExperimentDesign
        return locals()[name]

    # 执行引擎
    elif name in ("StudyRunner", "StudyExecutor", "StudyScheduler"):
        from scape.study.runner import StudyRunner, StudyExecutor, StudyScheduler
        return locals()[name]

    # 结果分析
    elif name in ("StudyAnalyzer", "ResultComparator", "StatisticalAnalyzer"):
        from scape.study.analyzer import StudyAnalyzer, ResultComparator, StatisticalAnalyzer
        return locals()[name]

    # 报告生成
    elif name in ("StudyReporter", "ReportGenerator", "VisualizationEngine"):
        from scape.study.reporter import StudyReporter, ReportGenerator, VisualizationEngine
        return locals()[name]

    # 类型定义
    elif name in ("StudyResult", "StudyMetrics", "StudyStatus"):
        from scape.study.types import StudyResult, StudyMetrics, StudyStatus
        return locals()[name]

    # 工具函数
    elif name in ("create_study_config", "validate_study_design", "export_study_results"):
        from scape.study.utils import create_study_config, validate_study_design, export_study_results
        return locals()[name]

    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
