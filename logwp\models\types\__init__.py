from __future__ import annotations

"""logwp.models.types - 类型定义层

现代化类型系统实现，提供TypedDict结构和类型别名。

Architecture
------------
层次/依赖: logwp包类型定义层，仅依赖constants和标准库
设计原则: 类型安全、结构化数据、业务语义、简化设计
性能特征: 编译时检查、零运行时开销、类型推导优化

Core Features
-------------
- **TypedDict结构**: 结构化数据传输对象，支持NotRequired字段
- **CIIA标识符**: 大小写不敏感的标识符类型
- **类型别名**: 业务语义化的类型别名定义
- **简化设计**: 避免过度抽象，专注实际需求

Package Structure
-----------------
- typed_dicts: TypedDict数据结构（WpDataDict等）
- identifiers: CIIA标识符类型（WpIdentifier等）
- aliases: 类型别名定义（WpDataFrame等）

Examples
--------
>>> from logwp.models.types import WpDataDict, WpDataFrame, WpWellName
>>>
>>> # TypedDict结构使用
>>> data: WpDataDict = {
...     "name": "OBMIQ_logs",
...     "type": "Continuous",
...     "rows": 1000
... }
>>>
>>> # 类型别名使用
>>> def process_logs(data: WpDataFrame, well: WpWellName) -> WpDataFrame:
...     return data[data["WELL"] == well]

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§3.1 - types层设计
- 《SCAPE_CCG_编码与通用规范.md》TS-3 - TypedDict规范
- 《SCAPE_WFS_WP文件规范.md》A.11 - COMP类型规范
"""

# TypedDict数据结构
from .typed_dicts import (
    WpDataDict,
    WpMetaDict,
    WpConfigDict,
    WpResultDict,
    WpErrorDict,
    WpGpuDict,
    WpProjectConfig,
    WpMetaData,
)

# CIIA标识符类型
from .identifiers import (
    WpIdentifier,
    WpWellIdentifier,
    WpDatasetIdentifier,
    WpCurveIdentifier,
    WpAttributeIdentifier,
)

# 类型别名
from .aliases import (
    WpDataFrame,
    WpWellName,
    WpCurveName,
    WpDatasetName,
    WpFilePath,
    WpSheetName,
    WpExtAttributeName,
    WpDepthValue,
    WpTimeValue,
    WpCurveMetadataDict,
)

__all__ = [
    # CIIA标识符类型
    "WpIdentifier",
    "WpWellIdentifier",
    "WpDatasetIdentifier",
    "WpCurveIdentifier",
    "WpAttributeIdentifier",

    # TypedDict结构
    "WpDataDict",
    "WpMetaDict",
    "WpConfigDict",
    "WpResultDict",
    "WpErrorDict",
    "WpGpuDict",
    "WpProjectConfig",
    "WpMetaData",

    # 类型别名
    "WpDataFrame",
    "WpWellName",
    "WpCurveName",
    "WpDatasetName",
    "WpFilePath",
    "WpSheetName",
    "WpExtAttributeName",
    "WpDepthValue",
    "WpTimeValue",
    "WpCurveMetadataDict",
]

# T2_Axis预定义属性处理已移至 logwp.models.ext_attr.predefined
# "T2AxisProcessor",    # 已移至 logwp.models.ext_attr.predefined.T2AxisProcessor
