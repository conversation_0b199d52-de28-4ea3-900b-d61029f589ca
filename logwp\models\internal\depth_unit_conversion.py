#!/usr/bin/env python3
"""项目级深度单位转换服务模块。

提供WpWellProject级别的批量深度单位转换功能。

Architecture
------------
层次/依赖: models/service层，项目级业务逻辑
设计原则: Service Layer模式、批量处理、错误恢复
性能特征: 批量转换、并发支持、进度监控

Examples:
    >>> # 转换指定数据集
    >>> convert_project_datasets_depth_unit(project, ["logs", "core"], "ft")

    >>> # 转换所有数据集
    >>> convert_project_datasets_depth_unit(project, None, "m")

    >>> # 转换为默认单位
    >>> convert_project_datasets_depth_unit_to_default(project, ["logs"])

References:
    《SCAPE_DDS_详细设计_logwp.md》§4.2 - 深度单位管理系统
"""

from typing import TYPE_CHECKING, Optional

from logwp.models.constants import WpDepthUnit
from logwp.models.exceptions import WpValidationError, WpDataError
from logwp.infra.exceptions import ErrorContext
from logwp.models.utils.unit_conversion import is_valid_depth_unit
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.well_project import WpWellProject

logger = get_logger(__name__)


def convert_project_datasets_depth_unit(
    project: "WpWellProject",
    dataset_names: Optional[list[str]],
    target_unit: str
) -> None:
    """批量转换项目中数据集的深度单位。

    Args:
        project: 井工程项目
        dataset_names: 要转换的数据集名称列表，None表示转换所有数据集
        target_unit: 目标深度单位（"m" 或 "ft"）

    Raises:
        WpValidationError: 当目标单位不支持时抛出
        WpDataError: 当数据集转换失败时抛出

    Note:
        - 批量处理多个数据集
        - 跳过不存在的数据集（记录警告）
        - 跳过不支持深度转换的数据集（记录警告）
        - 部分失败时继续处理其他数据集

    Examples:
        >>> # 转换指定数据集
        >>> convert_project_datasets_depth_unit(project, ["logs", "core"], "ft")

        >>> # 转换所有数据集
        >>> convert_project_datasets_depth_unit(project, None, "m")
    """
    # 验证目标单位
    if not is_valid_depth_unit(target_unit):
        raise WpValidationError(
            f"不支持的目标深度单位: {target_unit}",
            context=ErrorContext(
                operation="convert_project_datasets_depth_unit",
                project_name=str(project.name),
                additional_info={
                    "target_unit": target_unit,
                    "supported_units": list(WpDepthUnit.get_all_units())
                }
            )
        )

    # 确定要转换的数据集列表
    if dataset_names is None:
        # 转换所有数据集
        target_datasets = list(project.datasets.keys())
        logger.info(
            "开始转换项目中所有数据集的深度单位",
            project_name=str(project.name),
            target_unit=target_unit,
            total_datasets=len(target_datasets),
            operation="convert_project_datasets_depth_unit"
        )
    else:
        # 转换指定数据集
        target_datasets = dataset_names
        logger.info(
            "开始转换项目中指定数据集的深度单位",
            project_name=str(project.name),
            target_unit=target_unit,
            target_datasets=target_datasets,
            operation="convert_project_datasets_depth_unit"
        )

    # 统计转换结果
    success_count = 0
    skip_count = 0
    error_count = 0
    errors = []

    # 逐个转换数据集
    for dataset_name in target_datasets:
        try:
            # 检查数据集是否存在
            if dataset_name not in project.datasets:
                logger.warning(
                    "数据集不存在，跳过转换",
                    project_name=str(project.name),
                    dataset_name=dataset_name,
                    operation="convert_project_datasets_depth_unit"
                )
                skip_count += 1
                continue

            dataset = project.datasets[dataset_name]

            # 检查数据集是否有深度参考曲线
            try:
                current_unit = dataset.get_depth_reference_unit()
                if current_unit is None:
                    logger.warning(
                        "数据集没有深度参考曲线，跳过转换",
                        project_name=str(project.name),
                        dataset_name=dataset_name,
                        operation="convert_project_datasets_depth_unit"
                    )
                    skip_count += 1
                    continue
            except Exception:
                logger.warning(
                    "数据集不支持深度单位操作，跳过转换",
                    project_name=str(project.name),
                    dataset_name=dataset_name,
                    operation="convert_project_datasets_depth_unit"
                )
                skip_count += 1
                continue

            # 执行转换
            dataset.convert_depth_reference_unit(target_unit)
            success_count += 1

            logger.debug(
                "数据集深度单位转换成功",
                project_name=str(project.name),
                dataset_name=dataset_name,
                current_unit=current_unit,
                target_unit=target_unit,
                operation="convert_project_datasets_depth_unit"
            )

        except Exception as e:
            error_count += 1
            error_msg = f"数据集 {dataset_name} 转换失败: {str(e)}"
            errors.append(error_msg)

            logger.error(
                "数据集深度单位转换失败",
                project_name=str(project.name),
                dataset_name=dataset_name,
                target_unit=target_unit,
                error=str(e),
                operation="convert_project_datasets_depth_unit"
            )

    # 记录转换结果
    logger.info(
        "项目数据集深度单位转换完成",
        project_name=str(project.name),
        target_unit=target_unit,
        total_datasets=len(target_datasets),
        success_count=success_count,
        skip_count=skip_count,
        error_count=error_count,
        operation="convert_project_datasets_depth_unit"
    )

    # 如果有错误，抛出异常
    if errors:
        raise WpDataError(
            f"部分数据集转换失败 ({error_count}/{len(target_datasets)})",
            context=ErrorContext(
                operation="convert_project_datasets_depth_unit",
                project_name=str(project.name),
                additional_info={
                    "target_unit": target_unit,
                    "success_count": success_count,
                    "skip_count": skip_count,
                    "error_count": error_count,
                    "errors": errors
                }
            )
        )


def convert_project_datasets_depth_unit_to_default(
    project: "WpWellProject",
    dataset_names: Optional[list[str]]
) -> None:
    """批量转换项目中数据集的深度单位为默认单位。

    Args:
        project: 井工程项目
        dataset_names: 要转换的数据集名称列表，None表示转换所有数据集

    Raises:
        WpValidationError: 当默认单位不支持时抛出
        WpDataError: 当数据集转换失败时抛出

    Note:
        - 使用project.default_depth_reference_unit作为目标单位
        - 其他行为与convert_project_datasets_depth_unit相同

    Examples:
        >>> # 设置默认单位为英尺
        >>> project.default_depth_reference_unit = "ft"
        >>> convert_project_datasets_depth_unit_to_default(project, ["logs"])

        >>> # 转换所有数据集为默认单位
        >>> convert_project_datasets_depth_unit_to_default(project, None)
    """
    target_unit = project.default_depth_reference_unit

    logger.info(
        "开始转换项目数据集深度单位为默认单位",
        project_name=str(project.name),
        default_unit=target_unit,
        dataset_names=dataset_names,
        operation="convert_project_datasets_depth_unit_to_default"
    )

    # 委托给通用转换函数
    convert_project_datasets_depth_unit(project, dataset_names, target_unit)

    logger.info(
        "项目数据集深度单位转换为默认单位完成",
        project_name=str(project.name),
        default_unit=target_unit,
        operation="convert_project_datasets_depth_unit_to_default"
    )


def get_project_depth_unit_summary(project: "WpWellProject") -> dict[str, any]:
    """获取项目深度单位使用情况摘要。

    Args:
        project: 井工程项目

    Returns:
        dict: 深度单位使用情况摘要

    Examples:
        >>> summary = get_project_depth_unit_summary(project)
        >>> print(f"默认单位: {summary['default_unit']}")
        >>> print(f"单位分布: {summary['unit_distribution']}")
    """
    summary = {
        "default_unit": project.default_depth_reference_unit,
        "total_datasets": len(project.datasets),
        "unit_distribution": {},
        "datasets_by_unit": {},
        "datasets_without_depth": []
    }

    # 统计各数据集的深度单位
    for dataset_name, dataset in project.datasets.items():
        try:
            unit = dataset.get_depth_reference_unit()
            if unit is None:
                summary["datasets_without_depth"].append(dataset_name)
            else:
                # 更新单位分布统计
                if unit not in summary["unit_distribution"]:
                    summary["unit_distribution"][unit] = 0
                    summary["datasets_by_unit"][unit] = []

                summary["unit_distribution"][unit] += 1
                summary["datasets_by_unit"][unit].append(dataset_name)
        except Exception:
            # 数据集不支持深度单位操作
            summary["datasets_without_depth"].append(dataset_name)

    return summary
