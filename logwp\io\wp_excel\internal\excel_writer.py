"""Excel文件基础写入服务。

提供Excel工作簿创建、工作表管理、文件保存等基础操作。
遵循SAD文档的"内部服务层设计（Utility/Helper Pattern）"模式。

Architecture
------------
层次/依赖: I/O层内部服务，被wp_excel_writer调用
设计原则: 无状态函数、WFS规范兼容、异常处理
性能特征: 内存优化、批量操作、资源管理

Functions
---------
- write_wp_excel_file: 主写入流程协调函数
- create_workbook_with_sheets: 创建工作簿并添加所有工作表
- save_workbook_to_file: 保存工作簿到文件
- validate_sheet_names: 验证工作表名称符合Excel规范

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》§3.2.1 - 基础服务设计
- 《SCAPE_WFS_WP文件规范.md》- WFS v1.0规范
"""

from __future__ import annotations

import time
from pathlib import Path
from typing import Sequence

import openpyxl
import structlog

from logwp.io.constants import WpXlsxKey
from logwp.io.exceptions import WpFileFormatError, WpIOError
from logwp.models.exceptions import WpValidationError
from logwp.infra.exceptions import ErrorContext
from logwp.models.well_project import WpWellProject
from ..config import ExcelFormattingConfig
from .dataset_writer import write_dataset_sheet
from .head_info_writer import write_head_info_sheet
from .well_map_writer import write_well_map_sheet

logger = structlog.get_logger(__name__)

__all__ = [
    "create_workbook_with_sheets",
    "save_workbook_to_file",
    "validate_sheet_names",
]


def create_workbook_with_sheets(
    project: WpWellProject,
    save_head_info: bool,
    save_well_map: bool,
    dataset_names: list[str] | None,
    formatting_config: ExcelFormattingConfig,
    write_only: bool = False
) -> openpyxl.Workbook:
    """创建工作簿并添加所有工作表。

    按照WFS规范的顺序创建工作表：
    1. _Head_Info（如果需要）
    2. _Well_Map（如果需要）
    3. 数据集工作表（按指定顺序）

    Args:
        project: 测井项目对象
        save_head_info: 是否保存井头信息
        save_well_map: 是否保存井名映射
        dataset_names: 要保存的数据集名称列表
        formatting_config: Excel格式化配置
        write_only: 是否以只写模式创建工作簿，用于高性能无格式写入

    Returns:
        openpyxl.Workbook: 创建的工作簿对象

    Raises:
        WpFileFormatError: 工作表创建失败
    """
    try:
        # 创建新工作簿
        if write_only:
            workbook = openpyxl.Workbook(write_only=True)
            logger.debug("以只写模式创建工作簿")
        else:
            workbook = openpyxl.Workbook()
            # 删除默认工作表
            if workbook.worksheets:
                workbook.remove(workbook.active)

        # 收集要创建的工作表名称
        sheet_names = []

        # 1. 井头信息表单
        if save_head_info and project.head.attribute_records:
            sheet_names.append(WpXlsxKey.SHEET_HEAD_INFO.value)

        # 2. 井名映射表单
        if save_well_map and project.well_map.mappings:
            sheet_names.append(WpXlsxKey.SHEET_WELL_MAP.value)

        # 3. 数据集表单
        if dataset_names is None:
            # 保存所有数据集
            dataset_names = list(project.datasets.keys())

        # 转换数据集名称为Excel工作表名称
        for dataset_name in dataset_names:
            excel_sheet_name = _convert_dataset_name_to_excel_sheet(dataset_name)
            sheet_names.append(excel_sheet_name)

        # 验证工作表名称
        validate_sheet_names(sheet_names)

        # 创建工作表并写入数据
        # 写入井头信息
        if save_head_info and project.head.attribute_records:
            worksheet = workbook.create_sheet(WpXlsxKey.SHEET_HEAD_INFO.value)
            write_head_info_sheet(worksheet, project.head, formatting_config)

            logger.debug("井头信息表单写入完成", sheet_name=WpXlsxKey.SHEET_HEAD_INFO.value)

        # 写入井名映射
        if save_well_map and project.well_map.mappings:
            worksheet = workbook.create_sheet(WpXlsxKey.SHEET_WELL_MAP.value)
            write_well_map_sheet(worksheet, project.well_map)

            logger.debug("井名映射表单写入完成", sheet_name=WpXlsxKey.SHEET_WELL_MAP.value)

        # 写入数据集
        for dataset_name in dataset_names:
            dataset = project.datasets[dataset_name]
            excel_sheet_name = _convert_dataset_name_to_excel_sheet(dataset_name)

            worksheet = workbook.create_sheet(excel_sheet_name)
            write_dataset_sheet(worksheet, dataset, formatting_config)

            logger.debug(
                "数据集表单写入完成",
                dataset_name=dataset_name,
                sheet_name=excel_sheet_name,
                df_shape=dataset.df.shape
            )

        logger.info(
            "工作簿创建完成",
            total_sheets=len(sheet_names),
            head_info=save_head_info and bool(project.head.attribute_records),
            well_map=save_well_map and bool(project.well_map.mappings),
            dataset_count=len(dataset_names)
        )

        return workbook

    except Exception as e:
        logger.error(
            "工作簿创建失败",
            error_type=type(e).__name__,
            error_message=str(e)
        )
        raise WpFileFormatError(
            f"创建Excel工作簿失败: {e}",
            context=ErrorContext(
                operation="create_workbook_with_sheets",
                additional_info={
                    "save_head_info": save_head_info,
                    "save_well_map": save_well_map,
                    "dataset_names": dataset_names
                }
            )
        ) from e


def save_workbook_to_file(workbook: openpyxl.Workbook, file_path: Path) -> None:
    """保存工作簿到文件。

    Args:
        workbook: 要保存的工作簿
        file_path: 输出文件路径

    Raises:
        WpIOError: 文件保存失败
    """
    try:
        workbook.save(file_path)

        logger.debug(
            "工作簿保存成功",
            file_path=str(file_path),
            file_size=file_path.stat().st_size if file_path.exists() else 0
        )

    except PermissionError as e:
        logger.error("文件写入权限不足", file_path=str(file_path))
        raise PermissionError(f"无法写入文件，权限不足: {file_path}") from e

    except Exception as e:
        logger.error(
            "工作簿保存失败",
            file_path=str(file_path),
            error_type=type(e).__name__,
            error_message=str(e)
        )
        raise WpIOError(
            f"保存Excel文件失败: {e}",
            context=ErrorContext(
                operation="save_workbook_to_file",
                file_path=str(file_path)
            )
        ) from e


def validate_sheet_names(sheet_names: list[str]) -> None:
    """验证工作表名称符合Excel规范。

    Args:
        sheet_names: 工作表名称列表

    Raises:
        WpValidationError: 工作表名称不符合规范
    """
    # 检查重复名称
    seen_names = set()
    duplicate_names = []

    for name in sheet_names:
        if name in seen_names:
            duplicate_names.append(name)
        seen_names.add(name)

    if duplicate_names:
        raise WpValidationError(
            f"工作表名称重复: {duplicate_names}",
            context=ErrorContext(
                operation="validate_sheet_names",
                additional_info={"sheet_names": sheet_names}
            )
        )

    # 检查名称长度和字符
    invalid_names = []
    for name in sheet_names:
        if len(name) > 31:  # Excel工作表名称最大长度
            invalid_names.append(f"{name} (长度超限: {len(name)})")

        # Excel不允许的字符: \ / ? * [ ]
        invalid_chars = set(name) & {'\\', '/', '?', '*', '[', ']'}
        if invalid_chars:
            invalid_names.append(f"{name} (包含无效字符: {invalid_chars})")

    if invalid_names:
        raise WpValidationError(
            f"工作表名称不符合Excel规范: {invalid_names}",
            context=ErrorContext(
                operation="validate_sheet_names",
                additional_info={"sheet_names": sheet_names}
            )
        )


def _convert_dataset_name_to_excel_sheet(dataset_name: str) -> str:
    """将数据集名称转换为Excel工作表名称。

    确保名称符合Excel工作表命名规范，同时保持WFS兼容性。

    Args:
        dataset_name: 数据集名称

    Returns:
        str: Excel工作表名称
    """
    # 替换Excel不允许的字符
    excel_name = dataset_name
    for char in ['\\', '/', '?', '*', '[', ']']:
        excel_name = excel_name.replace(char, '_')

    # 限制长度
    if len(excel_name) > 31:  # Excel工作表名称最大长度
        excel_name = excel_name[:31]

    return excel_name
