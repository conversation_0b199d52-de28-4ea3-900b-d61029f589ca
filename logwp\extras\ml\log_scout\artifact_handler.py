"""Artifact handler for the LogScout step.

Provides stateless methods for saving and loading artifacts produced by LogScout,
such as DataFrames (reports) and matplotlib figures (plots).
"""

from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    import pandas as pd
    from matplotlib.figure import Figure


class LogScoutArtifactHandler:
    """A stateless handler for reading and writing LogScout artifacts."""

    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path) -> None:
        """Saves a pandas DataFrame to a CSV file.

        Ensures the parent directory exists before writing.

        Args:
            df: The DataFrame to save.
            path: The destination file path.
        """
        path.parent.mkdir(parents=True, exist_ok=True)
        # index=True is important for correlation matrices where the index
        # contains feature names.
        df.to_csv(path, index=True)

    @staticmethod
    def save_figure(fig: Figure, path: Path) -> None:
        """Saves a matplotlib Figure to a file.

        Ensures the parent directory exists before writing. The figure is saved
        with a high DPI and a tight bounding box for quality.

        Args:
            fig: The Figure object to save.
            path: The destination file path (e.g., 'figure.png').
        """
        path.parent.mkdir(parents=True, exist_ok=True)
        fig.savefig(path, dpi=300, bbox_inches="tight")
