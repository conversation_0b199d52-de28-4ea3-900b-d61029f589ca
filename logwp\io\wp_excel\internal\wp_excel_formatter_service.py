"""中央Excel格式化服务。

将所有工作表的格式化逻辑集中到一个服务类中，实现数据写入与样式格式化的分离。

Architecture
------------
层次/依赖: I/O层内部服务，被wp_excel_writer调用
设计原则: 关注点分离、单一职责、可配置性
性能特征: 批量格式化、样式复用

Classes
-------
- WpExcelFormatter: 负责对整个工作簿应用格式化

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》- 格式化与写入分离设计
"""

from __future__ import annotations

from typing import TYPE_CHECKING

import openpyxl
import structlog
from openpyxl.styles import Alignment
from openpyxl.styles.colors import Color

from logwp.io.constants import WpXlsxKey
from logwp.models.constants import WpDataType
from . import excel_formatter
from .data_formatter import extract_excel_curve_order

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from logwp.models.well_project import WpWellProject
    from ..config import ExcelFormattingConfig

logger = structlog.get_logger(__name__)


class WpExcelFormatter:
    """中央Excel格式化服务。

    将所有工作表的格式化逻辑集中管理，支持对整个工作簿应用格式化。
    """

    def __init__(self, project: WpWellProject, config: ExcelFormattingConfig):
        """初始化格式化服务。

        Args:
            project: 测井项目对象，用于获取上下文信息（如数据集）
            config: 格式化配置
        """
        self.project = project
        self.config = config

    def format_workbook(self, workbook: openpyxl.Workbook) -> None:
        """对整个工作簿应用格式化。

        遍历工作簿中的所有工作表，并根据其名称调用相应的格式化方法。

        Args:
            workbook: 要格式化的工作簿对象
        """
        if not self.config.enable_formatting:
            logger.debug("格式化功能已禁用，跳过工作簿格式化。")
            return

        logger.info("开始对工作簿应用格式化", workbook_sheets=len(workbook.sheetnames))
        for sheet in workbook:
            if sheet.title == WpXlsxKey.SHEET_HEAD_INFO.value:
                self._format_head_info_sheet(sheet)
            elif sheet.title == WpXlsxKey.SHEET_WELL_MAP.value:
                self._format_well_map_sheet(sheet)
            elif sheet.title in self.project.datasets:
                dataset = self.project.get_dataset(sheet.title)
                if dataset:
                    self._format_dataset_sheet(sheet, dataset)
                else:
                    logger.warning("未在项目中找到与工作表匹配的数据集", sheet_name=sheet.title)
            else:
                logger.debug("跳过非标准工作表的格式化", sheet_name=sheet.title)
        logger.info("工作簿格式化完成")

    def _format_head_info_sheet(self, worksheet: openpyxl.Worksheet) -> None:
        """格式化_Head_Info表单。"""
        max_row = worksheet.max_row
        max_col = int(WpXlsxKey.WELLHEAD_MAX_COLUMN.value)

        excel_formatter.apply_default_formatting(worksheet, self.config)
        excel_formatter.apply_background_colors(worksheet, [(1, 1, 1, max_col, "header")], self.config)
        excel_formatter.apply_table_borders(worksheet, 1, 1, max_row, max_col, self.config)
        excel_formatter.freeze_panes(worksheet, 1, 1, self.config)
        column_widths = {
            1: 8, 2: 15, 3: 12, 4: 15, 5: 20, 6: 8, 7: 10, 8: 30, 9: 25
        }
        excel_formatter.auto_adjust_column_widths(worksheet, column_widths, self.config)
        value_col_idx = 8
        excel_formatter.format_cell_range(
            worksheet, start_row=2, start_col=value_col_idx, end_row=max_row, end_col=value_col_idx,
            alignment=Alignment(wrap_text=True, vertical='top')
        )
        logger.debug("井头信息表单格式化完成", worksheet_name=worksheet.title)

    def _format_well_map_sheet(self, worksheet: openpyxl.Worksheet) -> None:
        """格式化_Well_Map表单。"""
        max_row = worksheet.max_row
        max_col = int(WpXlsxKey.WELLMAP_MAX_COLUMN.value)

        excel_formatter.apply_default_formatting(worksheet, self.config)
        excel_formatter.apply_background_colors(worksheet, [(1, 1, 1, max_col, "header")], self.config)
        excel_formatter.apply_table_borders(worksheet, 1, 1, max_row, max_col, self.config)
        excel_formatter.freeze_panes(worksheet, 1, 1, self.config)
        column_widths = {1: 20, 2: 20}
        excel_formatter.auto_adjust_column_widths(worksheet, column_widths, self.config)
        logger.debug("井名映射表单格式化完成", worksheet_name=worksheet.title)

    def _format_dataset_sheet(self, worksheet: openpyxl.Worksheet, dataset: WpDepthIndexedDatasetBase) -> None:
        """格式化数据集表单。"""
        max_row = worksheet.max_row
        max_col = worksheet.max_column

        excel_formatter.apply_default_formatting(worksheet, self.config)
        excel_formatter.apply_background_colors(
            worksheet,
            [
                (1, 1, 2, 4, "structure"),
                (3, 1, 7, 1, "structure"),
                (3, 2, 7, max_col, "header"),
            ],
            self.config
        )
        excel_formatter.apply_table_borders(worksheet, 1, 1, max_row, max_col, self.config)
        excel_formatter.freeze_panes(worksheet, 8, 2, self.config)
        excel_formatter.auto_adjust_column_widths(worksheet, None, self.config)

        depth_format = f'0.{"0" * self.config.depth_decimal_places}'
        float_format = self.config.float_format
        curve_order = extract_excel_curve_order(dataset.curve_metadata, dataset.dataset_type)

        for col_idx, (_, df_col_name) in enumerate(curve_order, start=2):
            curve = dataset.curve_metadata.get_curve_by_dataframe_name(df_col_name)
            if not curve:
                continue

            number_format = None
            if curve.depth_role is not None:
                number_format = depth_format
            elif curve.data_type == WpDataType.FLOAT:
                number_format = float_format

            if number_format:
                excel_formatter.format_cell_range(
                    worksheet, start_row=8, start_col=col_idx, end_row=max_row, end_col=col_idx,
                    number_format=number_format
                )

        worksheet.sheet_properties.tabColor = Color(rgb=self.config.structure_bg_color)
        logger.debug("数据集表单格式化完成", dataset_name=dataset.name)
