"""scape.core.swift_pso.artifact_handler - SWIFT-PSO无状态产物处理器

将所有与文件I/O相关的逻辑集中到一个无状态的工具类中。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO产物I/O层
设计原则: 无状态设计、职责单一、标准签名
性能特征: 静态方法、内存友好、异常安全

遵循CCG规范：
- PF-2: 异步I/O支持
- EH-4: 异常链保持
- TS-1: 完整类型注解

References
----------
- 《logwp/extras/tracking/机器学习组件开发框架》§3.3 - 产物处理器规范
- 《SCAPE_CCG_编码与通用规范》§PF - 性能优化要求
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict

import pandas as pd

from logwp.extras.plotting import PlotProfile

# 条件导入CuPy，支持GPU计算环境
try:
    import cupy as cp
except ImportError:
    cp = None


def _convert_to_json_serializable(obj: Any) -> Any:
    """递归地将包含NumPy/CuPy类型的对象转换为JSON可序列化的格式。

    此函数确保在保存为JSON之前，所有来自GPU的数据（CuPy数组/标量）
    都被安全地转换为CPU端的Python原生类型。

    Args:
        obj: 要转换的对象，可以是任意类型

    Returns:
        Any: JSON可序列化的对象

    References:
        迁移自 scape/core/swift_pso_backup/pso_result.py
    """
    import numpy as np

    # 1. 优先处理CuPy类型（如果cupy已导入）
    if cp is not None and isinstance(obj, (cp.ndarray, cp.generic)):
        return _convert_to_json_serializable(cp.asnumpy(obj))

    # 2. 处理NumPy数组和标量
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    if isinstance(obj, np.generic):
        return obj.item()

    # 3. 递归处理容器类型
    if isinstance(obj, dict):
        return {key: _convert_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_convert_to_json_serializable(item) for item in obj]

    # 4. 对于其他Python原生类型，直接返回
    return obj


class SwiftPsoArtifactHandler:
    """SWIFT-PSO包的无状态产物处理器。

    负责所有SWIFT-PSO步骤产物的序列化/反序列化和读/写操作。
    此类不存储任何实例变量，所有方法都是静态方法。

    Architecture
    ------------
    层次/依赖: scape/core层，SWIFT-PSO产物I/O层
    设计原则: 无状态设计、职责单一、标准签名
    性能特征: 静态方法、内存友好、异常安全

    遵循CCG规范：
    - PF-2: 异步I/O支持
    - EH-4: 异常链保持
    - TS-1: 完整类型注解

    References
    ----------
    - 《logwp/extras/tracking/机器学习组件开发框架》§3.3 - 产物处理器规范
    """

    @staticmethod
    def save_parameters(params: Dict[str, Any], path: Path) -> None:
        """将参数字典保存为JSON文件。

        Args:
            params: 要保存的参数字典
            path: 保存路径

        Raises:
            IOError: 文件写入失败时抛出
        """
        # 确保参数中所有值都是JSON可序列化的
        params_serializable = _convert_to_json_serializable(params)

        # 确保父目录存在
        path.parent.mkdir(parents=True, exist_ok=True)

        with open(path, 'w', encoding='utf-8') as f:
            json.dump(params_serializable, f, indent=2, ensure_ascii=False)

    @staticmethod
    def load_parameters(path: Path) -> Dict[str, Any]:
        """从JSON文件加载参数字典。

        Args:
            path: 文件路径

        Returns:
            Dict[str, Any]: 加载的参数字典

        Raises:
            FileNotFoundError: 文件不存在时抛出
            json.JSONDecodeError: JSON格式错误时抛出
        """
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)

    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path, *, index: bool = False) -> None:
        """将DataFrame保存为CSV文件。

        Args:
            df: 要保存的DataFrame
            path: 保存路径
            index: 是否将DataFrame索引写入文件。默认为False。

        Raises:
            IOError: 文件写入失败时抛出
        """
        # 确保父目录存在
        path.parent.mkdir(parents=True, exist_ok=True)

        df.to_csv(path, index=index, encoding='utf-8')

    @staticmethod
    def load_dataframe(path: Path) -> pd.DataFrame:
        """从CSV文件加载DataFrame。

        Args:
            path: 文件路径

        Returns:
            pd.DataFrame: 加载的DataFrame

        Raises:
            FileNotFoundError: 文件不存在时抛出
            pd.errors.EmptyDataError: CSV文件为空时抛出
        """
        return pd.read_csv(path, encoding='utf-8')

    @staticmethod
    def save_plot_profile(profile: PlotProfile, path: Path) -> None:
        """将PlotProfile对象保存为JSON文件。

        Args:
            profile: 要保存的PlotProfile对象
            path: 保存路径

        Raises:
            IOError: 文件写入失败时抛出
        """
        # 确保父目录存在
        path.parent.mkdir(parents=True, exist_ok=True)

        # 使用PlotProfile的内置方法保存
        profile.to_json(path)

    @staticmethod
    def load_plot_profile(path: Path) -> PlotProfile:
        """从JSON文件加载PlotProfile对象。

        Args:
            path: 文件路径

        Returns:
            PlotProfile: 加载的PlotProfile对象

        Raises:
            FileNotFoundError: 文件不存在时抛出
            json.JSONDecodeError: JSON格式错误时抛出
        """
        # 使用PlotProfile的内置方法加载
        return PlotProfile.from_json(path)

    @staticmethod
    def _print_params_table(title: str, params: Dict[str, float]):
        """(私有) 辅助函数，以格式化表格打印参数字典。"""
        if not params:
            print(f"--- {title} ---\n(无参数)\n")
            return

        records = []
        for name, value in params.items():
            # 检查参数名是否以 'log10_' 开头，以确定是否需要计算线性域值
            if name.startswith("log10_"):
                linear_value = f"{10**value:.4f}"
            else:
                linear_value = "N/A"  # 对于非对数参数，线性域值不适用

            records.append({
                "参数名": name,
                "数值": f"{value:.4f}",
                "线性域数值": linear_value
            })

        df = pd.DataFrame(records)
        print(f"--- {title} ---")
        # 使用 to_string() 获得更好的对齐效果
        print(df.to_string(index=False))
        print()

    @staticmethod
    def print_model_assets_human_readable(model_assets: Dict[str, Any]) -> None:
        """
        以人类可读的格式打印模型产物（model_assets）。

        此函数将优化参数和固定参数以表格形式展示，并自动计算
        对数域参数的线性域值，方便用户快速解读模型结果。

        Args:
            model_assets: 从训练步骤产出的模型产物字典，
                          期望结构为 `{'optimized_parameters': {...}, 'fixed_parameters': {...}, 'context': {...}}`。
        """
        print("\n" + "="*80)
        print(" " * 28 + "SWIFT-PSO 模型产物摘要")
        print("="*80 + "\n")

        # 打印优化参数
        optimized_params = model_assets.get("optimized_parameters", {})
        SwiftPsoArtifactHandler._print_params_table("优化参数 (Optimized Parameters)", optimized_params)

        # 打印固定参数
        fixed_params = model_assets.get("fixed_parameters", {})
        SwiftPsoArtifactHandler._print_params_table("固定参数 (Fixed Parameters)", fixed_params)

        # 打印上下文信息
        context = model_assets.get("context", {})
        if context:
            print("--- 上下文 (Context) ---")
            for key, value in context.items():
                print(f"  - {key}: {value:.4f}")
            print()

        print("="*80)
