from __future__ import annotations

"""logwp.models.utils.string_normalizer - CIIA字符串规范化工具

实现CIIA架构的字符串规范化引擎，提供格式无关的标识符处理。

Architecture
------------
层次/依赖: utils层工具，CIIA架构第一层（字符串规范化引擎）
设计原则: 格式解耦、国际化支持、性能优化、Unicode标准
性能特征: Unicode标准化、缓存优化、批量处理

遵循CIIA规范：
- CIIA-1: 显示索引分离 - 提供原始和规范化两种格式
- CIIA-2: Unicode标准化 - 采用NFKC归一化 + Case Folding
- CIIA-6: 格式无关 - 完全独立于具体数据格式

References
----------
- 《SCAPE_SAD_软件架构设计.md》§4.6 - CIIA架构设计
- .augment-guidelines CIIA规则 - CIIA-1到CIIA-8
"""

import unicodedata
from typing import Any
from functools import lru_cache

from logwp.models.constants import WpNormalizationMode
from logwp.models.exceptions import WpValidationError
from logwp.infra.exceptions import ErrorContext

__all__ = [
    "WpStringNormalizer",
]


class WpStringNormalizer:
    """CIIA字符串规范化器（格式无关）。

    实现CIIA架构的核心字符串规范化功能，提供大小写不敏感的标识符处理。
    完全格式无关，可用于井名、数据集名、曲线名、属性名等所有标识符。

    Architecture
    ------------
    层次/依赖: CIIA架构第一层，字符串规范化引擎
    设计原则: Unicode标准、性能优化、国际化支持
    性能特征: LRU缓存、批量处理、零副作用

    遵循CIIA规范：
    - CIIA-1: 提供原始和规范化两种格式
    - CIIA-2: Unicode NFKC归一化 + Case Folding
    - CIIA-6: 完全格式无关设计

    Examples:
        >>> normalizer = WpStringNormalizer()
        >>>
        >>> # 基本规范化
        >>> result = normalizer.normalize("  OBMIQ_Logs  ")
        >>> assert result == "obmiq_logs"
        >>>
        >>> # 大小写不敏感比较
        >>> assert normalizer.compare("OBMIQ_logs", "obmiq_LOGS")
        >>>
        >>> # 批量处理
        >>> names = ["C-1", "c-1", "C-2"]
        >>> normalized = normalizer.batch_normalize(names)

    References:
        《SCAPE_SAD_软件架构设计.md》§4.6.2 - 分层架构设计
    """

    def __init__(self, mode: WpNormalizationMode = WpNormalizationMode.UNICODE_CASEFOLD):
        """初始化规范化器。

        Args:
            mode: 规范化模式，默认使用Unicode Case Folding
        """
        self.mode = mode

    @staticmethod
    @lru_cache(maxsize=1024)
    def normalize(text: str, mode: WpNormalizationMode = WpNormalizationMode.UNICODE_CASEFOLD) -> str:
        """标准化字符串用于比较和索引（CIIA-2规范）。

        执行CIIA-2要求的规范化流程：
        1. 去除首尾空格（Trim）
        2. Unicode NFKC归一化（兼容性分解+组合）
        3. Unicode Case Folding（国际化大小写折叠）

        Args:
            text: 原始字符串
            mode: 规范化模式

        Returns:
            str: 规范化后的字符串

        Examples:
            >>> result = WpStringNormalizer.normalize("  OBMIQ_Logs  ")
            >>> assert result == "obmiq_logs"
            >>>
            >>> # Unicode支持
            >>> result = WpStringNormalizer.normalize("Ñoël")
            >>> assert result == "ñoël"

        References:
            CIIA-2: Unicode标准化要求
        """
        if not text:
            return text

        # 1. 去除首尾空格
        normalized = text.strip()

        # 2. Unicode NFKC归一化（兼容性分解+组合）
        normalized = unicodedata.normalize('NFKC', normalized)

        # 3. 大小写处理
        if mode == WpNormalizationMode.UNICODE_CASEFOLD:
            # Unicode Case Folding（推荐，国际化支持）
            normalized = normalized.casefold()
        elif mode == WpNormalizationMode.ASCII_UPPER:
            # ASCII大写转换（兼容模式）
            normalized = normalized.upper()
        elif mode == WpNormalizationMode.PRESERVE_CASE:
            # 保持原始大小写（调试模式）
            pass

        return normalized

    @staticmethod
    def compare(text1: str, text2: str, mode: WpNormalizationMode = WpNormalizationMode.UNICODE_CASEFOLD) -> bool:
        """大小写不敏感比较（CIIA-1规范）。

        Args:
            text1: 第一个字符串
            text2: 第二个字符串
            mode: 规范化模式

        Returns:
            bool: 是否相等（大小写不敏感）

        Examples:
            >>> assert WpStringNormalizer.compare("OBMIQ_logs", "obmiq_LOGS")
            >>> assert WpStringNormalizer.compare("C-1", "c-1")
            >>> assert not WpStringNormalizer.compare("C-1", "C-2")
        """
        return WpStringNormalizer.normalize(text1, mode) == WpStringNormalizer.normalize(text2, mode)

    @staticmethod
    def batch_normalize(
        texts: list[str],
        mode: WpNormalizationMode = WpNormalizationMode.UNICODE_CASEFOLD
    ) -> list[str]:
        """批量规范化，提高性能。

        Args:
            texts: 字符串列表
            mode: 规范化模式

        Returns:
            list[str]: 规范化后的字符串列表

        Examples:
            >>> texts = ["OBMIQ_logs", "K_Label", "C-1"]
            >>> normalized = WpStringNormalizer.batch_normalize(texts)
            >>> assert normalized == ["obmiq_logs", "k_label", "c-1"]
        """
        return [WpStringNormalizer.normalize(text, mode) for text in texts]

    @staticmethod
    def detect_conflicts(
        texts: list[str],
        mode: WpNormalizationMode = WpNormalizationMode.UNICODE_CASEFOLD
    ) -> dict[str, list[str]]:
        """检测大小写冲突（CIIA-3规范）。

        Args:
            texts: 字符串列表
            mode: 规范化模式

        Returns:
            dict[str, list[str]]: 冲突映射，键为规范化字符串，值为原始字符串列表

        Raises:
            WpValidationError: 检测到冲突时抛出

        Examples:
            >>> texts = ["OBMIQ_logs", "obmiq_LOGS", "K_Label"]
            >>> conflicts = WpStringNormalizer.detect_conflicts(texts)
            >>> assert "obmiq_logs" in conflicts
            >>> assert len(conflicts["obmiq_logs"]) == 2
        """
        normalized_map: dict[str, list[str]] = {}

        for text in texts:
            normalized = WpStringNormalizer.normalize(text, mode)
            if normalized not in normalized_map:
                normalized_map[normalized] = []
            normalized_map[normalized].append(text)

        # 找出冲突
        conflicts = {k: v for k, v in normalized_map.items() if len(v) > 1}

        return conflicts

    @staticmethod
    def validate_no_conflicts(
        texts: list[str],
        context: str = "标识符列表",
        mode: WpNormalizationMode = WpNormalizationMode.UNICODE_CASEFOLD
    ) -> None:
        """验证无大小写冲突（CIIA-3规范）。

        Args:
            texts: 字符串列表
            context: 上下文描述
            mode: 规范化模式

        Raises:
            WpValidationError: 检测到冲突时抛出

        Examples:
            >>> texts = ["OBMIQ_logs", "K_Label", "C-1"]
            >>> WpStringNormalizer.validate_no_conflicts(texts)  # 正常
            >>>
            >>> texts_conflict = ["OBMIQ_logs", "obmiq_LOGS"]
            >>> WpStringNormalizer.validate_no_conflicts(texts_conflict)  # 抛出异常
        """
        conflicts = WpStringNormalizer.detect_conflicts(texts, mode)

        if conflicts:
            conflict_details = []
            for normalized, originals in conflicts.items():
                conflict_details.append(f"'{normalized}': {originals}")

            raise WpValidationError(
                f"{context}中检测到大小写冲突",
                context=ErrorContext(
                    operation="validate_no_conflicts",
                    context_name=context,
                    conflicts=conflicts,
                    conflict_count=len(conflicts)
                )
            )

    def normalize_instance(self, text: str) -> str:
        """实例方法版本的规范化。"""
        return self.normalize(text, self.mode)

    def compare_instance(self, text1: str, text2: str) -> bool:
        """实例方法版本的比较。"""
        return self.compare(text1, text2, self.mode)
