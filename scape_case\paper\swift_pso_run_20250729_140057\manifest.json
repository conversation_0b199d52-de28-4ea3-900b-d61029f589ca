{"run_id": "20250729-060057-0554043c", "start_time_utc": "2025-07-29T06:00:57.014401Z", "end_time_utc": "2025-07-29T06:03:25.229837Z", "duration_seconds": 148.215, "status": "RUNNING", "lineage": {"inputs": {}, "code_version": {}}, "config_snapshot_path": null, "parameters": {"swift_pso_training": {"bootstrap_iterations": 20, "narrow_window_factor": 0.3, "bootstrap_sample_ratio": 0.8, "pso_lowo_particles": 150, "pso_lowo_iterations": 400, "pso_finetune_particles": 100, "pso_finetune_iterations": 200}, "t2_time_length": 64, "t2_time_min": 0.1, "t2_time_max": 10000.0, "output_curve_permeability": "K_PRED_SWIFT", "output_curve_vmicro": "VMICRO", "output_curve_vmeso": "VMESO", "output_curve_vmacro": "VMACRO", "swift_pso_visualization": {"tsne_perplexity": 15, "tsne_learning_rate": 200.0, "tsne_n_iter": 2000, "tsne_init": "pca", "tsne_random_state": 42, "kmeans_n_clusters": 4}}, "metrics": {"swift_pso_training": {"final_finetune_loss": 6.269598623287391, "convergence_iterations": 31, "total_parameter_sets": 504}, "swift_pso_prediction": {"prediction_rows": 4689, "permeability_min": 3.930231244165812e-12, "permeability_max": 20.473737472212086, "permeability_mean": 0.2611506080335196}, "train_all_perm_corr_analysis.C-1": {"3x": 22.86, "5x": 31.43, "10x": 44.57, "pearson_r": 0.4683418618080053, "spearman_rho": 0.4214668192690991, "r_squared": 0.21934409952178874}, "train_all_perm_corr_analysis.C-2": {"3x": 13.86, "5x": 19.31, "10x": 28.22, "pearson_r": 0.49347526981130563, "spearman_rho": 0.6101423736491004, "r_squared": 0.2435178419153409}, "train_pz_perm_corr_analysis.C-1": {"3x": 38.78, "5x": 53.06, "10x": 71.43, "pearson_r": 0.5748959035789, "spearman_rho": 0.40927675518472584, "r_squared": 0.33050529995179984}, "train_pz_perm_corr_analysis.C-2": {"3x": 22.22, "5x": 33.33, "10x": 50.62, "pearson_r": 0.6166915899371578, "spearman_rho": 0.6509197031424623, "r_squared": 0.3803085170992196}, "train_core_perm_corr_analysis.C-1": {"3x": 24.83, "5x": 33.79, "10x": 44.83, "pearson_r": 0.545744702288478, "spearman_rho": 0.38266568248859223, "r_squared": 0.2978372800759395}, "train_core_perm_corr_analysis.C-2": {"3x": 12.61, "5x": 19.82, "10x": 32.43, "pearson_r": 0.646568220128803, "spearman_rho": 0.6223800583450879, "r_squared": 0.41805046328052825}, "train_core_pz_perm_corr_analysis.C-1": {"3x": 24.83, "5x": 33.79, "10x": 44.83, "pearson_r": 0.545744702288478, "spearman_rho": 0.38266568248859223, "r_squared": 0.2978372800759395}, "train_core_pz_perm_corr_analysis.C-2": {"3x": 12.61, "5x": 19.82, "10x": 32.43, "pearson_r": 0.646568220128803, "spearman_rho": 0.6223800583450879, "r_squared": 0.41805046328052825}, "swift_pso_visualization": {"tsne_points": 504, "tsne_x_min": -1057.15283203125, "tsne_x_max": 1072.8553466796875, "tsne_y_min": -845.835205078125, "tsne_y_max": 893.7765502929688, "bootstrap_runs": 20, "silhouette_score": 0.20647638037667185, "cluster_silhouette_score": 0.2877653619651417, "cluster_avg_intra_variance": 1.017155122250287}, "plt_analysis.C-1": {"pearson_r": 0.8863248246469775, "spearman_rho": 0.5714285714285715, "mae": 0.05460142332818777, "mape": 109.2039719710146, "rmse": 0.07122485393772453, "auc": 0.8182044602869906, "gini": -0.04091139346906436, "capture_at_30_percent": 0.8251027513480071, "lift_at_30_percent": 2.750342504493357}, "plt_analysis.C-2": {"pearson_r": 0.5294419063090288, "spearman_rho": 0.3571428571428572, "mae": 0.1171351288694386, "mape": 115.01459236271107, "rmse": 0.15090720419399306, "auc": 0.4748963393589479, "gini": 0.3178634789011404, "capture_at_30_percent": 0.2595721421948284, "lift_at_30_percent": 0.8652404739827613}, "ct_perm_corr_analysis.T-1": {"3x": 7.32, "5x": 12.2, "10x": 34.15, "pearson_r": 0.5462933795385674, "spearman_rho": 0.5339547855394632, "r_squared": 0.2984364565276692}, "t-1_perm_corr_analysis.T-1": {"3x": 37.5, "5x": 58.33, "10x": 70.83, "pearson_r": 0.6591562017305906, "spearman_rho": 0.6853652815125449, "r_squared": 0.4344868982798991}}, "artifacts": {"swift_pso_training.configs.training_config": {"path": "swift_pso_training/training_config.json", "type": "data", "metadata": {"hash": "479cc340f61172d9c6fbafeb629ad9ad2f7d227f72da5d7e14908339a4cb42e7", "hash_algorithm": "sha256", "size_bytes": 11397}, "description": "本次训练步骤的完整Pydantic配置快照，确保可复现性。"}, "swift_pso_training.diagnostics.fold_parameters.b00_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b00_f00/params_optimal.json", "type": "data", "metadata": {"hash": "f51a08857e2a35aa7f820540bb6af102c683ab57c553c56675d929d0e1854d8b", "hash_algorithm": "sha256", "size_bytes": 350}}, "swift_pso_training.diagnostics.fold_loss_history.b00_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b00_f00/loss_history.csv", "type": "data", "metadata": {"hash": "f8ce6a224e1124ebd1c04b07cb3fe7a16e77bfe5e1de795cde3aa616430f437e", "hash_algorithm": "sha256", "size_bytes": 1653}}, "swift_pso_training.diagnostics.fold_parameters.b00_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b00_f01/params_optimal.json", "type": "data", "metadata": {"hash": "5139259d41a542d0329b6abd6f315c7bf001e401ed427ad378d0fd377c34f6b4", "hash_algorithm": "sha256", "size_bytes": 349}}, "swift_pso_training.diagnostics.fold_loss_history.b00_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b00_f01/loss_history.csv", "type": "data", "metadata": {"hash": "cc845948e8487470b1f906aa0a2534001cfeef0ee02b62e0462e5aef93574859", "hash_algorithm": "sha256", "size_bytes": 2503}}, "swift_pso_training.diagnostics.fold_parameters.b01_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b01_f00/params_optimal.json", "type": "data", "metadata": {"hash": "8e4e8a08f0352234fc82f9330b2a46f7785ba9e0e73be3cdaf0faec86cd02f21", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b01_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b01_f00/loss_history.csv", "type": "data", "metadata": {"hash": "c1d010e1ff4e864c1fc80eba7f747c53d506f4413015cb40594f890c130aee3c", "hash_algorithm": "sha256", "size_bytes": 2128}}, "swift_pso_training.diagnostics.fold_parameters.b01_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b01_f01/params_optimal.json", "type": "data", "metadata": {"hash": "d148846111905c2973989026af9de3c94b6d3d9a29255aa587f6ed982c212dc6", "hash_algorithm": "sha256", "size_bytes": 345}}, "swift_pso_training.diagnostics.fold_loss_history.b01_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b01_f01/loss_history.csv", "type": "data", "metadata": {"hash": "9eb8b27b08d61b57aac76fd73b29187a03dabf4d2b7438fccb50f4431ece9da3", "hash_algorithm": "sha256", "size_bytes": 1607}}, "swift_pso_training.diagnostics.fold_parameters.b02_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b02_f00/params_optimal.json", "type": "data", "metadata": {"hash": "1ce168ca12ed6fc9b265bbc74deb08aba5720e5ec7d983b11f7549516a3a4e1d", "hash_algorithm": "sha256", "size_bytes": 345}}, "swift_pso_training.diagnostics.fold_loss_history.b02_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b02_f00/loss_history.csv", "type": "data", "metadata": {"hash": "6e3b02b4bbb977657bb38180bd4c120709f5ee917ffb3f1bd8f164ad19ce9882", "hash_algorithm": "sha256", "size_bytes": 3110}}, "swift_pso_training.diagnostics.fold_parameters.b02_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b02_f01/params_optimal.json", "type": "data", "metadata": {"hash": "d148846111905c2973989026af9de3c94b6d3d9a29255aa587f6ed982c212dc6", "hash_algorithm": "sha256", "size_bytes": 345}}, "swift_pso_training.diagnostics.fold_loss_history.b02_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b02_f01/loss_history.csv", "type": "data", "metadata": {"hash": "5b54f67519239fcc93124b39ac59edf8b20eb059e229f969c29e3fedbea25132", "hash_algorithm": "sha256", "size_bytes": 1631}}, "swift_pso_training.diagnostics.fold_parameters.b03_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b03_f00/params_optimal.json", "type": "data", "metadata": {"hash": "ef208d03800be4f5fbd7d1d30e5307d7035a661d792bb50e28d9658e5d02a7e0", "hash_algorithm": "sha256", "size_bytes": 350}}, "swift_pso_training.diagnostics.fold_loss_history.b03_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b03_f00/loss_history.csv", "type": "data", "metadata": {"hash": "ef3f54acd7e52172b0657ace9462cf9af8aa2e704bc6b7ba16c40252b1c8cf66", "hash_algorithm": "sha256", "size_bytes": 1949}}, "swift_pso_training.diagnostics.fold_parameters.b03_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b03_f01/params_optimal.json", "type": "data", "metadata": {"hash": "6ef2c56b777f3b624c2ebbafbec691c9b426cd4ac08c51a0f72d562a1f81dd35", "hash_algorithm": "sha256", "size_bytes": 344}}, "swift_pso_training.diagnostics.fold_loss_history.b03_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b03_f01/loss_history.csv", "type": "data", "metadata": {"hash": "1391e857ebe1654cd4ad488687aea2115f527ccbb8392ce66985b2fe2687123e", "hash_algorithm": "sha256", "size_bytes": 4241}}, "swift_pso_training.diagnostics.fold_parameters.b04_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b04_f00/params_optimal.json", "type": "data", "metadata": {"hash": "aada8bf99534e7003a8e8cc23f104cef637c125695921a0b96afdd97e4292ab5", "hash_algorithm": "sha256", "size_bytes": 348}}, "swift_pso_training.diagnostics.fold_loss_history.b04_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b04_f00/loss_history.csv", "type": "data", "metadata": {"hash": "2ea0bb2dfcecaaa85b2fa503d968bee84b5b5dc78a6a7e8ecd4706440e62bf88", "hash_algorithm": "sha256", "size_bytes": 2668}}, "swift_pso_training.diagnostics.fold_parameters.b04_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b04_f01/params_optimal.json", "type": "data", "metadata": {"hash": "c6f02bc323aa460362853c198220698ed4b18d7c25339d803a5566bfed61e243", "hash_algorithm": "sha256", "size_bytes": 346}}, "swift_pso_training.diagnostics.fold_loss_history.b04_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b04_f01/loss_history.csv", "type": "data", "metadata": {"hash": "1844111ce5cc28977d95de4006813174dc215ad866056dff6b07ec6aca37d81b", "hash_algorithm": "sha256", "size_bytes": 2714}}, "swift_pso_training.diagnostics.fold_parameters.b05_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b05_f00/params_optimal.json", "type": "data", "metadata": {"hash": "3040795dfad0681a6efacf2e6bf88ada27caa40a6970279a1d3306fc880a32da", "hash_algorithm": "sha256", "size_bytes": 345}}, "swift_pso_training.diagnostics.fold_loss_history.b05_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b05_f00/loss_history.csv", "type": "data", "metadata": {"hash": "0da8b453f4e1eff15dcc359cbbd2060a6a482b5b1d2667cad2fb90ec7e1ab5d5", "hash_algorithm": "sha256", "size_bytes": 3637}}, "swift_pso_training.diagnostics.fold_parameters.b05_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b05_f01/params_optimal.json", "type": "data", "metadata": {"hash": "221cdc703735ce270124afe2b7432a89f48914a5bb8a14edf9559555a364fbce", "hash_algorithm": "sha256", "size_bytes": 348}}, "swift_pso_training.diagnostics.fold_loss_history.b05_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b05_f01/loss_history.csv", "type": "data", "metadata": {"hash": "a27815c0bd6e77cb819612446760586a89fbe2a0c19abf055de591bf05f64ce2", "hash_algorithm": "sha256", "size_bytes": 2255}}, "swift_pso_training.diagnostics.fold_parameters.b06_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b06_f00/params_optimal.json", "type": "data", "metadata": {"hash": "0b8898ec42c5d416644928c9c74e29dcbd7c5222916952fe7dfdce278ade2fb8", "hash_algorithm": "sha256", "size_bytes": 348}}, "swift_pso_training.diagnostics.fold_loss_history.b06_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b06_f00/loss_history.csv", "type": "data", "metadata": {"hash": "4e96ced18162d759b055e5e420324eb8f13a40debe376e47cca157cfde7ba3af", "hash_algorithm": "sha256", "size_bytes": 4669}}, "swift_pso_training.diagnostics.fold_parameters.b06_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b06_f01/params_optimal.json", "type": "data", "metadata": {"hash": "9533b77cebcbce85fe757c007f0d954a12f6d951ac61aea9fed3a557059ad4af", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b06_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b06_f01/loss_history.csv", "type": "data", "metadata": {"hash": "ea8917506d36616fdc85cce38b3e0b8aae9a2910d28eeffcece27b5a1ddcf513", "hash_algorithm": "sha256", "size_bytes": 2404}}, "swift_pso_training.diagnostics.fold_parameters.b07_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b07_f00/params_optimal.json", "type": "data", "metadata": {"hash": "04c02388c4f5eef7852656caa75d0b7ac77618593c1225813131a95f9b9f58ba", "hash_algorithm": "sha256", "size_bytes": 350}}, "swift_pso_training.diagnostics.fold_loss_history.b07_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b07_f00/loss_history.csv", "type": "data", "metadata": {"hash": "0015be974ba87b561890884000ede1657ca188d443fe452b0f96334ebf01d257", "hash_algorithm": "sha256", "size_bytes": 3856}}, "swift_pso_training.diagnostics.fold_parameters.b07_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b07_f01/params_optimal.json", "type": "data", "metadata": {"hash": "d148846111905c2973989026af9de3c94b6d3d9a29255aa587f6ed982c212dc6", "hash_algorithm": "sha256", "size_bytes": 345}}, "swift_pso_training.diagnostics.fold_loss_history.b07_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b07_f01/loss_history.csv", "type": "data", "metadata": {"hash": "b055b96bcb5f513494d4ad12c1ab23cfa396f6028df1ce759a5e71fc1e563bac", "hash_algorithm": "sha256", "size_bytes": 1633}}, "swift_pso_training.diagnostics.fold_parameters.b08_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b08_f00/params_optimal.json", "type": "data", "metadata": {"hash": "f24ae7c3fc6b18889359b0940efb33e17dbfed4934880f37378d9f09780200cb", "hash_algorithm": "sha256", "size_bytes": 349}}, "swift_pso_training.diagnostics.fold_loss_history.b08_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b08_f00/loss_history.csv", "type": "data", "metadata": {"hash": "38eddd5f6587df1cf7ef7f4aa57c8d147a44f2ffcaee5e7ca29da837d1afd381", "hash_algorithm": "sha256", "size_bytes": 1646}}, "swift_pso_training.diagnostics.fold_parameters.b08_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b08_f01/params_optimal.json", "type": "data", "metadata": {"hash": "d148846111905c2973989026af9de3c94b6d3d9a29255aa587f6ed982c212dc6", "hash_algorithm": "sha256", "size_bytes": 345}}, "swift_pso_training.diagnostics.fold_loss_history.b08_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b08_f01/loss_history.csv", "type": "data", "metadata": {"hash": "4a595d4c406a299b71a59986920f32f4ea0e1c37774ec7aed092e16a56c8a6f0", "hash_algorithm": "sha256", "size_bytes": 1615}}, "swift_pso_training.diagnostics.fold_parameters.b09_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b09_f00/params_optimal.json", "type": "data", "metadata": {"hash": "623e17b63b3c2052196ced5a0624472fd9e635f402263f3e6f50285b752df629", "hash_algorithm": "sha256", "size_bytes": 346}}, "swift_pso_training.diagnostics.fold_loss_history.b09_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b09_f00/loss_history.csv", "type": "data", "metadata": {"hash": "6c2ba966ed9c870217dd414e65783f9a215f11c512887ebc748cad8be3d3a188", "hash_algorithm": "sha256", "size_bytes": 1546}}, "swift_pso_training.diagnostics.fold_parameters.b09_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b09_f01/params_optimal.json", "type": "data", "metadata": {"hash": "ee23e13fa6f1ab343bba6441dcf4f32e950e8271dfe9d08b0d56bd9c84847a58", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b09_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b09_f01/loss_history.csv", "type": "data", "metadata": {"hash": "24a1e594b8b19d69fe4da235e17c3d5fe9c30432f5866885a4f53722b246a6b2", "hash_algorithm": "sha256", "size_bytes": 2532}}, "swift_pso_training.diagnostics.fold_parameters.b10_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b10_f00/params_optimal.json", "type": "data", "metadata": {"hash": "5edfa195308807c350704d931314fc81e20cea690c8622e44105af88e0beb981", "hash_algorithm": "sha256", "size_bytes": 349}}, "swift_pso_training.diagnostics.fold_loss_history.b10_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b10_f00/loss_history.csv", "type": "data", "metadata": {"hash": "e23de97da04bb4989b7f3fb01f66d6cfa0d9e4d15f47021d3421600643ff7b7c", "hash_algorithm": "sha256", "size_bytes": 4072}}, "swift_pso_training.diagnostics.fold_parameters.b10_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b10_f01/params_optimal.json", "type": "data", "metadata": {"hash": "85ee4c7ab8e6f965997b0df66ff11849c71ba18b21aad584f807717295bbf3bf", "hash_algorithm": "sha256", "size_bytes": 349}}, "swift_pso_training.diagnostics.fold_loss_history.b10_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b10_f01/loss_history.csv", "type": "data", "metadata": {"hash": "35a8125a7d4d805857a0b6d68dbc8c1140d2fe36899798440ae0e8c1ddc3d3a2", "hash_algorithm": "sha256", "size_bytes": 1691}}, "swift_pso_training.diagnostics.fold_parameters.b11_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b11_f00/params_optimal.json", "type": "data", "metadata": {"hash": "39998ad0c763be244e0175caa922d989d9633ae9c8fc6bcfd45f1a61d637e446", "hash_algorithm": "sha256", "size_bytes": 344}}, "swift_pso_training.diagnostics.fold_loss_history.b11_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b11_f00/loss_history.csv", "type": "data", "metadata": {"hash": "c8e6efa374a60ae5e35d2af4ff9414797235f80fa5dfe264b3ea03b8007640ee", "hash_algorithm": "sha256", "size_bytes": 1538}}, "swift_pso_training.diagnostics.fold_parameters.b11_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b11_f01/params_optimal.json", "type": "data", "metadata": {"hash": "dd8ef06704c8d39b84e4b9bc1f0bbd427557a7083e0226ae726c7295297b810f", "hash_algorithm": "sha256", "size_bytes": 331}}, "swift_pso_training.diagnostics.fold_loss_history.b11_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b11_f01/loss_history.csv", "type": "data", "metadata": {"hash": "154456da5b14c11dcaa191629ee77d36b5262505415026264efd297e4ae84397", "hash_algorithm": "sha256", "size_bytes": 2867}}, "swift_pso_training.diagnostics.fold_parameters.b12_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b12_f00/params_optimal.json", "type": "data", "metadata": {"hash": "22464a60f7e60f5b81a3030c8cfbe5b8c906f18f31b6e797a7c12750f8ef8903", "hash_algorithm": "sha256", "size_bytes": 349}}, "swift_pso_training.diagnostics.fold_loss_history.b12_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b12_f00/loss_history.csv", "type": "data", "metadata": {"hash": "ab9165f3d8852d7120857087e66a14df6f1dcb06506f848c79be1923322429ce", "hash_algorithm": "sha256", "size_bytes": 2236}}, "swift_pso_training.diagnostics.fold_parameters.b12_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b12_f01/params_optimal.json", "type": "data", "metadata": {"hash": "33699d5f84eed2f25ca44925642b9753150a7fc08b408e0485217028d2802c73", "hash_algorithm": "sha256", "size_bytes": 345}}, "swift_pso_training.diagnostics.fold_loss_history.b12_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b12_f01/loss_history.csv", "type": "data", "metadata": {"hash": "40bfa975a15dd59594a1c6b972d1e88a63d9f0f52cee0cf13426968cdf4350ce", "hash_algorithm": "sha256", "size_bytes": 1587}}, "swift_pso_training.diagnostics.fold_parameters.b13_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b13_f00/params_optimal.json", "type": "data", "metadata": {"hash": "a1b6afbb2a065bef41b8e9d25439e356c9713cd6cfd4cb7c8f805acc402e1cc1", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b13_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b13_f00/loss_history.csv", "type": "data", "metadata": {"hash": "968e2fdcda5fd38a2dbf756637d206e185b92529ced734e0af22021744bb23ef", "hash_algorithm": "sha256", "size_bytes": 4570}}, "swift_pso_training.diagnostics.fold_parameters.b13_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b13_f01/params_optimal.json", "type": "data", "metadata": {"hash": "15ef2c41e4eb20c8498d6907ef2143839549b6430241613bc088ce3ce735d10d", "hash_algorithm": "sha256", "size_bytes": 348}}, "swift_pso_training.diagnostics.fold_loss_history.b13_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b13_f01/loss_history.csv", "type": "data", "metadata": {"hash": "b8c677fe92b8fda0d48d0cf460abb7a8b0775521eec83666569c61e924963b2a", "hash_algorithm": "sha256", "size_bytes": 1651}}, "swift_pso_training.diagnostics.fold_parameters.b14_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b14_f00/params_optimal.json", "type": "data", "metadata": {"hash": "eee3d1583bf625a5c115a21552aa06dd6b7083f0aba1cab5914f54f7118ff7f9", "hash_algorithm": "sha256", "size_bytes": 349}}, "swift_pso_training.diagnostics.fold_loss_history.b14_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b14_f00/loss_history.csv", "type": "data", "metadata": {"hash": "5eee937a85892247195b33950009120ecd3b9138aaad396f0c82bf6170f5ada5", "hash_algorithm": "sha256", "size_bytes": 4341}}, "swift_pso_training.diagnostics.fold_parameters.b14_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b14_f01/params_optimal.json", "type": "data", "metadata": {"hash": "c38f1353d606f850b8ca407a987aebc18a59c6453cbb228ddafa14aa0a46d70f", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b14_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b14_f01/loss_history.csv", "type": "data", "metadata": {"hash": "8a3156bd0ccc9df9028d7d85afbd7b9d70fba2e237b590fc97b5c8d139e1ef8b", "hash_algorithm": "sha256", "size_bytes": 2676}}, "swift_pso_training.diagnostics.fold_parameters.b15_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b15_f00/params_optimal.json", "type": "data", "metadata": {"hash": "6a6a48e5dc18114ebbb7464f0ad63c2e072c7e4c6a236cbac4338f1954c84071", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b15_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b15_f00/loss_history.csv", "type": "data", "metadata": {"hash": "edc293c4d5b805f8ff91e00f6929883b5235932c133634c3a4328084172a4872", "hash_algorithm": "sha256", "size_bytes": 4131}}, "swift_pso_training.diagnostics.fold_parameters.b15_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b15_f01/params_optimal.json", "type": "data", "metadata": {"hash": "dd845bef0c40ed00e5855bc003c7598f63803c0cc761e9532faa5981e6930cab", "hash_algorithm": "sha256", "size_bytes": 348}}, "swift_pso_training.diagnostics.fold_loss_history.b15_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b15_f01/loss_history.csv", "type": "data", "metadata": {"hash": "374ba8948f48d86f8729b3c2c2c5c9e521ec865aef8c9429d577cebddc9af177", "hash_algorithm": "sha256", "size_bytes": 2012}}, "swift_pso_training.diagnostics.fold_parameters.b16_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b16_f00/params_optimal.json", "type": "data", "metadata": {"hash": "5c719c0809e3473efbf469bf5c537205c28861fb08bd94b377ac8ee0211f3671", "hash_algorithm": "sha256", "size_bytes": 350}}, "swift_pso_training.diagnostics.fold_loss_history.b16_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b16_f00/loss_history.csv", "type": "data", "metadata": {"hash": "154a23a3714b321f7b6c17ddec77abd33729d16ca794af2fbba56fa6fa79296e", "hash_algorithm": "sha256", "size_bytes": 2069}}, "swift_pso_training.diagnostics.fold_parameters.b16_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b16_f01/params_optimal.json", "type": "data", "metadata": {"hash": "9400ba245b6d80e6b438035eac025bfbbefa87b5e881f1f24dd8c7ecd19737f4", "hash_algorithm": "sha256", "size_bytes": 348}}, "swift_pso_training.diagnostics.fold_loss_history.b16_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b16_f01/loss_history.csv", "type": "data", "metadata": {"hash": "d656c2d8b46794bb4c9c003193d6bc0344945a3b2ce747716dfabdcd0595eefe", "hash_algorithm": "sha256", "size_bytes": 1596}}, "swift_pso_training.diagnostics.fold_parameters.b17_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b17_f00/params_optimal.json", "type": "data", "metadata": {"hash": "a5f27ad79959d796dbc11c066cfb33fd6b2edb04862f1b065dda94520a645724", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b17_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b17_f00/loss_history.csv", "type": "data", "metadata": {"hash": "b3338fd3e78bae1fbd044405e38d440dbde918d864b6f3467719a6699826593f", "hash_algorithm": "sha256", "size_bytes": 2058}}, "swift_pso_training.diagnostics.fold_parameters.b17_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b17_f01/params_optimal.json", "type": "data", "metadata": {"hash": "04a1f2c81f0ac34c90d16b9b15c4453324d0c6e75be5809e3e2f5d5a1885f9bf", "hash_algorithm": "sha256", "size_bytes": 333}}, "swift_pso_training.diagnostics.fold_loss_history.b17_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b17_f01/loss_history.csv", "type": "data", "metadata": {"hash": "5a7b03194d87cc20bb968ab2bc688e1c5e52704841040acae4bc9a2e613feb01", "hash_algorithm": "sha256", "size_bytes": 2717}}, "swift_pso_training.diagnostics.fold_parameters.b18_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b18_f00/params_optimal.json", "type": "data", "metadata": {"hash": "73af89a7324bbf9f1069fa0da82c51d59631f6686d3baf0cfc6f561521cb0755", "hash_algorithm": "sha256", "size_bytes": 347}}, "swift_pso_training.diagnostics.fold_loss_history.b18_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b18_f00/loss_history.csv", "type": "data", "metadata": {"hash": "7329b566116791095a37da9d7db823a7d5108c887c226740fceb7ad5500822a8", "hash_algorithm": "sha256", "size_bytes": 5236}}, "swift_pso_training.diagnostics.fold_parameters.b18_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b18_f01/params_optimal.json", "type": "data", "metadata": {"hash": "d83f8d7b995aeeffc57305e6e4d1ab4012ad36af67af35415e4f0c76f3c12e62", "hash_algorithm": "sha256", "size_bytes": 348}}, "swift_pso_training.diagnostics.fold_loss_history.b18_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b18_f01/loss_history.csv", "type": "data", "metadata": {"hash": "8014bfd338232096f6f248c94f78c69f9853568e4518daa1e2b68493a6e921d3", "hash_algorithm": "sha256", "size_bytes": 4262}}, "swift_pso_training.diagnostics.fold_parameters.b19_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b19_f00/params_optimal.json", "type": "data", "metadata": {"hash": "53e527e3e643506cf99ecf59636d5e55e0a17e9481fd9a992eabf42dd9be6be9", "hash_algorithm": "sha256", "size_bytes": 346}}, "swift_pso_training.diagnostics.fold_loss_history.b19_f00": {"path": "swift_pso_training/1_bootstrap_lowo/run_b19_f00/loss_history.csv", "type": "data", "metadata": {"hash": "c2fdc98e32247e5a032e1d8db52f8dbfb2d2c8c0a420e248dbeda0b647d390e9", "hash_algorithm": "sha256", "size_bytes": 1986}}, "swift_pso_training.diagnostics.fold_parameters.b19_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b19_f01/params_optimal.json", "type": "data", "metadata": {"hash": "e705dd3ea96952666a11830fbdbb1252df7085c66a4e209e50266d6092b49256", "hash_algorithm": "sha256", "size_bytes": 343}}, "swift_pso_training.diagnostics.fold_loss_history.b19_f01": {"path": "swift_pso_training/1_bootstrap_lowo/run_b19_f01/loss_history.csv", "type": "data", "metadata": {"hash": "d2c4de5cfa15bdb39f7948c599906670e4c600c6432912ed4234a9d8cf79f903", "hash_algorithm": "sha256", "size_bytes": 3726}}, "swift_pso_training.models.final_parameters": {"path": "swift_pso_training/final_parameters.json", "type": "data", "metadata": {"hash": "de5573c5f0484c438b540b84af0a603adc038fa0f35890195e850e139a69a6ce", "hash_algorithm": "sha256", "size_bytes": 1032}, "description": "最终优化后的模型参数及上下文，可直接用于预测步骤。"}, "swift_pso_training.datasets.all_parameters_from_lowo": {"path": "swift_pso_training/all_parameters_from_lowo.csv", "type": "data", "metadata": {"hash": "39aa9c9a1e67501b41b0d8d1bc9808e905f1f0aa6520c7ef2b79b90968db9a4b", "hash_algorithm": "sha256", "size_bytes": 88637}, "description": "所有Bootstrap+LOWO迭代产生的优化参数集，用于t-SNE可视化分析。"}, "swift_pso_training.reports.convergence_history_finetune": {"path": "swift_pso_training/convergence_history_finetune.csv", "type": "data", "metadata": {"hash": "52336675f2fb7633eb691c2ca593d44eca4f648fd309c8725967cf65dccde280", "hash_algorithm": "sha256", "size_bytes": 691}, "description": "Fine-Tuning阶段的损失函数收敛历史。"}, "swift_pso_training.reports.bootstrap_summary": {"path": "swift_pso_training/summary_bootstrap_mu_rmse.csv", "type": "data", "metadata": {"hash": "6270c024c336e9fdf44dc213a11cea8da2cc0228c60bc411df3e1da1ccd959cc", "hash_algorithm": "sha256", "size_bytes": 536}, "description": "每轮Bootstrap的平均验证误差（mu_rmse），用于确定最佳轮次b*。"}, "swift_pso_training.diagnostics.params_warm_start": {"path": "swift_pso_training/params_warm_start.json", "type": "data", "metadata": {"hash": "faba1a86baa882cb5b27c30e4efdb5b5d1a016b3de8b2b663f83b7399afaaabd", "hash_algorithm": "sha256", "size_bytes": 349}, "description": "用于启动Fine-Tuning阶段的热启动参数（theta_hat_star）。"}, "swift_pso_prediction.datasets.predicted_permeability": {"path": "swift_pso_prediction/predicted_permeability.csv", "type": "data", "metadata": {"hash": "4b890cbedab518a5003c4d3aec8f547d550517ddf3387406e975bafdf18cbc2c", "hash_algorithm": "sha256", "size_bytes": 391491}}, "perm_corr_analysis.datasets.aligned_data_C-1": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.datasets.aligned_data_C-1.csv", "type": "data", "metadata": {"hash": "1bc3bbc367b1bf468677dca768b2c78c4669d3869ed08636389360d90f5108f3", "hash_algorithm": "sha256", "size_bytes": 5046}, "description": "渗透率相关性分析后 C-1 井的对齐数据表。"}, "perm_corr_analysis.configs.crossplot_profile_C-1": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.configs.crossplot_profile_C-1.json", "type": "data", "metadata": {"hash": "a38cc70d26c8fb92d293a304730b4f31ec1e26049ad1a44ecb2ab2f6bbaf7a78", "hash_algorithm": "sha256", "size_bytes": 2163}}, "perm_corr_analysis.data_snapshots.crossplot_C-1": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.data_snapshots.crossplot_C-1.csv", "type": "data", "metadata": {"hash": "1bc3bbc367b1bf468677dca768b2c78c4669d3869ed08636389360d90f5108f3", "hash_algorithm": "sha256", "size_bytes": 5046}}, "perm_corr_analysis.plots.crossplot_C-1": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.plots.crossplot_C-1.png", "type": "image", "metadata": {"hash": "706b5702d790fbf4a6faca80986d46ebe8c9fe8b85b9b3e0c3892e44dd1515b6", "hash_algorithm": "sha256", "size_bytes": 154296}}, "perm_corr_analysis.datasets.aligned_data_C-2": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.datasets.aligned_data_C-2.csv", "type": "data", "metadata": {"hash": "54c6572a50f2e8861c69a81bc5b0667521b2bf4d48509ad3249d14301da97608", "hash_algorithm": "sha256", "size_bytes": 3850}, "description": "渗透率相关性分析后 C-2 井的对齐数据表。"}, "perm_corr_analysis.configs.crossplot_profile_C-2": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.configs.crossplot_profile_C-2.json", "type": "data", "metadata": {"hash": "a38cc70d26c8fb92d293a304730b4f31ec1e26049ad1a44ecb2ab2f6bbaf7a78", "hash_algorithm": "sha256", "size_bytes": 2163}}, "perm_corr_analysis.data_snapshots.crossplot_C-2": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.data_snapshots.crossplot_C-2.csv", "type": "data", "metadata": {"hash": "54c6572a50f2e8861c69a81bc5b0667521b2bf4d48509ad3249d14301da97608", "hash_algorithm": "sha256", "size_bytes": 3850}}, "perm_corr_analysis.plots.crossplot_C-2": {"path": "train_core_pz_perm_corr_analysis/perm_corr_analysis.plots.crossplot_C-2.png", "type": "image", "metadata": {"hash": "b73f73cfed8d3b908f5f0e45577a5071448c2018c446661731dd10844124979a", "hash_algorithm": "sha256", "size_bytes": 155619}}, "swift_pso_visualization.data_snapshots.tsne_convergence_trajectory": {"path": "swift_pso_visualization/tsne_convergence_trajectory_data.csv", "type": "data", "metadata": {"hash": "d76304d09b941c2e0921cf68dd36a309839ab62fa0b7006aaff5ee1794e64ecb", "hash_algorithm": "sha256", "size_bytes": 99021}, "description": "t-SNE收敛轨迹的坐标数据，用于绘图复现。"}, "swift_pso_visualization.plots.tsne_convergence_trajectory": {"path": "swift_pso_visualization/tsne_convergence_trajectory_plot.png", "type": "image", "metadata": {"hash": "edcd5c493c6a12f015a8886f8cd8ec5615f1f6d3cd163f8bd7ac3bdbab577be6", "hash_algorithm": "sha256", "size_bytes": 2030289}, "description": "SWIFT-PSO参数演化轨迹的t-SNE可视化图表。"}, "swift_pso_visualization.data_snapshots.tsne_cluster_analysis": {"path": "swift_pso_visualization/tsne_cluster_analysis_data.csv", "type": "data", "metadata": {"hash": "7aa5d860eb3bb7a053546e5319fcdb94f37c6dd273086ace826e673744230c92", "hash_algorithm": "sha256", "size_bytes": 8171}, "description": "最终收敛点聚类分析的坐标数据，用于绘图复现。"}, "swift_pso_visualization.reports.tsne_cluster_analysis": {"path": "swift_pso_visualization/tsne_cluster_analysis_report.json", "type": "data", "metadata": {"hash": "7adc8aa6cb1ae90f9876cb7ba099b5b5316d93e4cf374e863c5a679eceac59e1", "hash_algorithm": "sha256", "size_bytes": 9870}, "description": "聚类分析的总体量化指标报告，包括轮廓系数、簇心等。"}, "swift_pso_visualization.reports.tsne_cluster_statistics": {"path": "swift_pso_visualization/tsne_cluster_statistics_report.json", "type": "data", "metadata": {"hash": "3215ebdceacbb0c05b92d5d494d64b3067d60208fe8a5fda13afd85746ceac26", "hash_algorithm": "sha256", "size_bytes": 6726}, "description": "每个簇内部所有参数的详细统计信息（均值、标准差等）。"}, "swift_pso_visualization.reports.tsne_cluster_summary_table": {"path": "swift_pso_visualization/tsne_cluster_summary_table.csv", "type": "data", "metadata": {"hash": "ccc584d9108406945966092d21d6d62c0ac4023b9099424c3d69c47927c935ef", "hash_algorithm": "sha256", "size_bytes": 1684}, "description": "各簇参数均值和标准差的对比摘要表，便于物理意义解释。"}, "swift_pso_visualization.plots.tsne_cluster_analysis": {"path": "swift_pso_visualization/tsne_cluster_analysis_plot.png", "type": "image", "metadata": {"hash": "54a80b8778ad92fc071f4e2df91070fe7f1e82a65520b974845db81776ff79d3", "hash_algorithm": "sha256", "size_bytes": 547347}, "description": "SWIFT-PSO最终收敛点的t-SNE聚类分析图表（带凸包）。"}, "plt_analysis.reports.analyzed_layers_C-1": {"path": "plt_analysis/plt_analysis.reports.analyzed_layers_C-1.csv", "type": "data", "metadata": {"hash": "4e5dfb0cac0fa595d4939d3397734063a3e4481e7f78d4c698d7f8446b96d4ab", "hash_algorithm": "sha256", "size_bytes": 916}, "description": "PLT分析后 C-1 井的分层属性表。"}, "plt_analysis.configs.contribution_crossplot_profile_C-1": {"path": "plt_analysis/plt_analysis.configs.contribution_crossplot_profile_C-1.json", "type": "data", "metadata": {"hash": "123891756b91737100acc9ef34650da7bfd856c8fa6b0e08b69871881b7531cc", "hash_algorithm": "sha256", "size_bytes": 1318}}, "plt_analysis.data_snapshots.contribution_crossplot_C-1": {"path": "plt_analysis/plt_analysis.data_snapshots.contribution_crossplot_C-1.csv", "type": "data", "metadata": {"hash": "e55cf8f164f971c46df2602a6d26fbf76f23073317833223693a4967e2710d3b", "hash_algorithm": "sha256", "size_bytes": 303}}, "plt_analysis.plots.contribution_crossplot_C-1": {"path": "plt_analysis/plt_analysis.plots.contribution_crossplot_C-1.png", "type": "image", "metadata": {"hash": "a46d42937460f68c4e0cf66193a364119e63bb67ff97c4fc05c200ebf0dd3c37", "hash_algorithm": "sha256", "size_bytes": 56289}, "description": "C-1 井的 plt_analysis.plots.contribution_crossplot 图表。"}, "plt_analysis.configs.capture_curve_profile_C-1": {"path": "plt_analysis/plt_analysis.configs.capture_curve_profile_C-1.json", "type": "data", "metadata": {"hash": "fd692ccc141c90ab954a60705a8ae90347c44a875085f4a3c70ccf179de3f4bf", "hash_algorithm": "sha256", "size_bytes": 1397}}, "plt_analysis.data_snapshots.capture_curve_C-1": {"path": "plt_analysis/plt_analysis.data_snapshots.capture_curve_C-1.csv", "type": "data", "metadata": {"hash": "4275a7abbb7cd6872dfde8cb4f960e184c141fea9e1e5dc99f79efeed295b6bc", "hash_algorithm": "sha256", "size_bytes": 184}}, "plt_analysis.plots.capture_curve_C-1": {"path": "plt_analysis/plt_analysis.plots.capture_curve_C-1.png", "type": "image", "metadata": {"hash": "1d3400d0c96e2ed499a1f4e4a59a738ba0c75433ed7b28eb1f62bb74517f90bd", "hash_algorithm": "sha256", "size_bytes": 73712}, "description": "C-1 井的 plt_analysis.plots.capture_curve 图表。"}, "plt_analysis.configs.lorenz_curve_profile_C-1": {"path": "plt_analysis/plt_analysis.configs.lorenz_curve_profile_C-1.json", "type": "data", "metadata": {"hash": "72ca2bd3e450bf64d85d4339feb5fd5b9771468b7063f979c7759d73e0b7d77b", "hash_algorithm": "sha256", "size_bytes": 1394}}, "plt_analysis.data_snapshots.lorenz_curve_C-1": {"path": "plt_analysis/plt_analysis.data_snapshots.lorenz_curve_C-1.csv", "type": "data", "metadata": {"hash": "20a3d60694f179e919ae0ecc5f0a38bc71a6643e0f846e064ad8a7b62a5ac598", "hash_algorithm": "sha256", "size_bytes": 256}}, "plt_analysis.plots.lorenz_curve_C-1": {"path": "plt_analysis/plt_analysis.plots.lorenz_curve_C-1.png", "type": "image", "metadata": {"hash": "cd272fc5b447a507017ac55424bd1c5e07e0a9dcb3d57925b6fcdd2d345bec41", "hash_algorithm": "sha256", "size_bytes": 61096}, "description": "C-1 井的 plt_analysis.plots.lorenz_curve 图表。"}, "plt_analysis.reports.analyzed_layers_C-2": {"path": "plt_analysis/plt_analysis.reports.analyzed_layers_C-2.csv", "type": "data", "metadata": {"hash": "7c7218318b251dd3ab650e6a759281b4d04493647274f7af32a3c2a0c3e21d0a", "hash_algorithm": "sha256", "size_bytes": 936}, "description": "PLT分析后 C-2 井的分层属性表。"}, "plt_analysis.configs.contribution_crossplot_profile_C-2": {"path": "plt_analysis/plt_analysis.configs.contribution_crossplot_profile_C-2.json", "type": "data", "metadata": {"hash": "123891756b91737100acc9ef34650da7bfd856c8fa6b0e08b69871881b7531cc", "hash_algorithm": "sha256", "size_bytes": 1318}}, "plt_analysis.data_snapshots.contribution_crossplot_C-2": {"path": "plt_analysis/plt_analysis.data_snapshots.contribution_crossplot_C-2.csv", "type": "data", "metadata": {"hash": "da7d7b0cebf03ef01c746c6ace3b7fce57aae4ae427f39f3d8f01221374aba26", "hash_algorithm": "sha256", "size_bytes": 304}}, "plt_analysis.plots.contribution_crossplot_C-2": {"path": "plt_analysis/plt_analysis.plots.contribution_crossplot_C-2.png", "type": "image", "metadata": {"hash": "5b40d901bb03d9febcd6a530928c767055231675615a3ab7b45ded822c87aca8", "hash_algorithm": "sha256", "size_bytes": 58007}, "description": "C-2 井的 plt_analysis.plots.contribution_crossplot 图表。"}, "plt_analysis.configs.capture_curve_profile_C-2": {"path": "plt_analysis/plt_analysis.configs.capture_curve_profile_C-2.json", "type": "data", "metadata": {"hash": "fd692ccc141c90ab954a60705a8ae90347c44a875085f4a3c70ccf179de3f4bf", "hash_algorithm": "sha256", "size_bytes": 1397}}, "plt_analysis.data_snapshots.capture_curve_C-2": {"path": "plt_analysis/plt_analysis.data_snapshots.capture_curve_C-2.csv", "type": "data", "metadata": {"hash": "f2cf217e05b8c506c7aaff4d290f3c0fa2fff3ec5034d5921cecac9c7078e05a", "hash_algorithm": "sha256", "size_bytes": 243}}, "plt_analysis.plots.capture_curve_C-2": {"path": "plt_analysis/plt_analysis.plots.capture_curve_C-2.png", "type": "image", "metadata": {"hash": "22ae73c518d438a08f6c1cafa42abf84893b335880f0d9bf0813969fd6fe9ec0", "hash_algorithm": "sha256", "size_bytes": 73574}, "description": "C-2 井的 plt_analysis.plots.capture_curve 图表。"}, "plt_analysis.configs.lorenz_curve_profile_C-2": {"path": "plt_analysis/plt_analysis.configs.lorenz_curve_profile_C-2.json", "type": "data", "metadata": {"hash": "72ca2bd3e450bf64d85d4339feb5fd5b9771468b7063f979c7759d73e0b7d77b", "hash_algorithm": "sha256", "size_bytes": 1394}}, "plt_analysis.data_snapshots.lorenz_curve_C-2": {"path": "plt_analysis/plt_analysis.data_snapshots.lorenz_curve_C-2.csv", "type": "data", "metadata": {"hash": "94adf814919e77cc0e3947f52abc75b10ff0d5a1dff3379499a727bc28972ae2", "hash_algorithm": "sha256", "size_bytes": 257}}, "plt_analysis.plots.lorenz_curve_C-2": {"path": "plt_analysis/plt_analysis.plots.lorenz_curve_C-2.png", "type": "image", "metadata": {"hash": "ad1d9b6dfe08dc40255b1737bcee74cb8abb721e4feb7e10d2e55a56a203aef5", "hash_algorithm": "sha256", "size_bytes": 62482}, "description": "C-2 井的 plt_analysis.plots.lorenz_curve 图表。"}, "perm_corr_analysis.datasets.aligned_data_T-1": {"path": "t-1_perm_corr_analysis/perm_corr_analysis.datasets.aligned_data_T-1.csv", "type": "data", "metadata": {"hash": "5dfca3ee72b39a5d395b3c6b2b732ee983e377915d0e2a871a8fecbd4c62ac5c", "hash_algorithm": "sha256", "size_bytes": 829}, "description": "渗透率相关性分析后 T-1 井的对齐数据表。"}, "perm_corr_analysis.configs.crossplot_profile_T-1": {"path": "t-1_perm_corr_analysis/perm_corr_analysis.configs.crossplot_profile_T-1.json", "type": "data", "metadata": {"hash": "c12c1047b46998e15e7dc372b8720cea71cd3d81b330206bb95a2f2157312903", "hash_algorithm": "sha256", "size_bytes": 2147}}, "perm_corr_analysis.data_snapshots.crossplot_T-1": {"path": "t-1_perm_corr_analysis/perm_corr_analysis.data_snapshots.crossplot_T-1.csv", "type": "data", "metadata": {"hash": "5dfca3ee72b39a5d395b3c6b2b732ee983e377915d0e2a871a8fecbd4c62ac5c", "hash_algorithm": "sha256", "size_bytes": 829}}, "perm_corr_analysis.plots.crossplot_T-1": {"path": "t-1_perm_corr_analysis/perm_corr_analysis.plots.crossplot_T-1.png", "type": "image", "metadata": {"hash": "d8366651c42892b9f77c6c655e887f15861c023ac8a6ccbc70892bd5fb37fbf9", "hash_algorithm": "sha256", "size_bytes": 143791}}}}