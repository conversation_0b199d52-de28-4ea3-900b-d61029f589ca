from __future__ import annotations

import json
from pathlib import Path
from typing import Any

import joblib
import pandas as pd


class ObmiqBaselinesArtifactHandler:
    """
    一个无状态的产物处理器，用于处理OBMIQ Baselines组件的产物。
    """

    @staticmethod
    def save_model_assets(assets: dict[str, Any], path: Path):
        """将包含模型和元数据的资产包保存到文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        joblib.dump(assets, path)

    @staticmethod
    def load_model_assets(path: Path) -> dict[str, Any]:
        """从文件加载模型资产包。"""
        return joblib.load(path)

    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path):
        """将DataFrame保存为CSV文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=False)

    @staticmethod
    def save_parameters(params: dict[str, Any], path: Path):
        """将参数字典保存为JSON文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        path.write_text(json.dumps(params, indent=2))
