{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ数据准备 - Santos测井数据分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-22T14:58:47.704445Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 126.54, 'cpu_percent': 0.0}\n", "库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-22T14:58:52.841761Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 350.49, 'cpu_percent': 0.0} file_path=..\\01_data\\santos_data.wp.xlsx\n", "2025-07-22T14:58:52.869379Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.9, 'cpu_percent': 0.0} file_path=..\\01_data\\santos_data.wp.xlsx file_size_mb=24.05 sheet_count=7\n", "2025-07-22T14:58:52.880346Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.91, 'cpu_percent': 0.0} project_name=santos_data\n", "2025-07-22T14:58:52.886698Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.91, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data\n", "2025-07-22T14:58:52.893769Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.91, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-22T14:58:52.903776Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.92, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-22T14:58:52.920577Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.92, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-07-22T14:58:52.939875Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.93, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-22T14:58:52.952695Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 352.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:58:52.979738Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 352.38, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=62 well_curves=1\n", "2025-07-22T14:59:13.981433Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 428.83, 'cpu_percent': 0.0} shape=(16303, 251) sheet_name=Logs\n", "2025-07-22T14:59:14.011762Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.39, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-07-22T14:59:14.020655Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.39, 'cpu_percent': 0.0} curve_count=62 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 251) processing_time=21.07\n", "2025-07-22T14:59:14.044155Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.43, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-07-22T14:59:14.056917Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.43, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-22T14:59:14.102810Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-07-22T14:59:14.114963Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-07-22T14:59:14.122650Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.084\n", "2025-07-22T14:59:14.137992Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-22T14:59:14.149451Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-22T14:59:14.163202Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} shape=(8, 4) sheet_name=PLT\n", "2025-07-22T14:59:14.174358Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-22T14:59:14.216918Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(8, 4) processing_time=0.083\n", "2025-07-22T14:59:14.245889Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-22T14:59:14.260895Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.59, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-22T14:59:14.282081Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.63, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-07-22T14:59:14.293182Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.63, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-22T14:59:14.301609Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.63, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.058\n", "2025-07-22T14:59:14.316328Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.63, 'cpu_percent': 0.0} dataset_count=4 has_head_info=True has_well_map=True\n", "2025-07-22T14:59:14.328902Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.63, 'cpu_percent': 0.0} dataset_count=4\n", "2025-07-22T14:59:14.339023Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.63, 'cpu_percent': 0.0} dataset_count=4 file_path=..\\01_data\\santos_data.wp.xlsx processing_time=21.497 project_name=WpIdentifier('santos_data')\n", "2025-07-22T14:59:14.353697Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.63, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data'}\n", "2025-07-22T14:59:14.375862Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 429.88, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 4}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data\n", "📅 创建时间: 2025-07-22 22:58:52.885701\n", "🔧 默认深度单位: WpDepthUnit.METER\n"]}], "source": ["data_file_path = \"../01_data/santos_data.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 创建时间: {project.created_at}\")\n", "    print(f\"🔧 默认深度单位: {project.default_depth_reference_unit}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 生成测井数据概况报告"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 提取OBMIQ累积分布数据集"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 准备提取OBMIQ相关曲线，共15条曲线\n", "曲线列表: SWB_NMR, BFV_NMR, PHIE_NMR, RD_LOG10, CN, DRES, DEN, RS_LOG10, DT, BVI_NMR, SDR_PROXY, PHIT_NMR, PHI_T2_DIST_CUM, DT2_P50, DPHIT_NMR\n", "📊 准备提取OBMIQ相关曲线（dropna），共13条曲线\n", "曲线列表: SWB_NMR, BFV_NMR, PHIE_NMR, RD_LOG10, CN, DRES, DEN, RS_LOG10, DT, BVI_NMR, SDR_PROXY, PHIT_NMR, PHI_T2_DIST_CUM\n"]}], "source": ["# 定义要提取的曲线列表\n", "# obmiq_curves = [\n", "#    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "#    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',\n", "#    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',\n", "#    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "#]\n", "\n", "#log_scout分析结果\n", "obmiq_curves = [\n", "    'SWB_NMR', 'BFV_NMR', 'PHIE_NMR', 'RD_LOG10', 'CN', 'DRES',\n", "    'DEN', 'RS_LOG10', 'DT', 'BVI_NMR','SDR_PROXY','PHIT_NMR', # PHIT_NMR用于归一化\n", "    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "]\n", "\n", "obmiq_curves_dropna = [\n", "    'SWB_NMR', 'BFV_NMR', 'PHIE_NMR', 'RD_LOG10', 'CN', 'DRES',\n", "    'DEN', 'RS_LOG10', 'DT', 'BVI_NMR','SDR_PROXY','PHIT_NMR',\n", "    'PHI_T2_DIST_CUM'\n", "]\n", "\n", "print(f\"📊 准备提取OBMIQ相关曲线，共{len(obmiq_curves)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves)}\")\n", "print(f\"📊 准备提取OBMIQ相关曲线（dropna），共{len(obmiq_curves_dropna)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves_dropna)}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始提取OBMIQ数据集(训练)...\n", "\n", "📍 提取C-1井数据(训练)...\n", "2025-07-22T14:59:40.226562Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 430.15, 'cpu_percent': 0.0} curve_count=15 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-22T14:59:40.242368Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 430.16, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10'] operation=extract_metadata output_curve_count=17 output_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10']\n", "2025-07-22T14:59:40.353648Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.29, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:40.370178Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.38, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-22T14:59:40.387430Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.38, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-22T14:59:40.411898Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.38, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:40.466358Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.38, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'SWB_NMR', 'BFV_NMR', 'PHIE_NMR', 'RD_LOG10', 'CN', 'DRES', 'DEN', 'RS_LOG10', 'DT', 'BVI_NMR', 'SDR_PROXY', 'PHIT_NMR', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=80 source_rows=16303 target_rows=1085\n", "2025-07-22T14:59:40.480178Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.38, 'cpu_percent': 0.0} curve_count=15 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_c_1\n", "2025-07-22T14:59:40.504789Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1067 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.39, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1085 removed_rows=18\n", "2025-07-22T14:59:40.516744Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.39, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-22T14:59:40.528797Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.39, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:40.540420Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.4, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-22T14:59:40.547980Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.4, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_c1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_C1') save_head_info=True save_well_map=True\n", "2025-07-22T14:59:40.559844Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.4, 'cpu_percent': 0.0} curve_count=17 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1067, 80)\n", "2025-07-22T14:59:41.182443Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.5, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.623\n", "2025-07-22T14:59:41.191849Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.5, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-22T14:59:41.266092Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.67, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_c1.wp.xlsx processing_time=0.718 project_name=WpIdentifier('Santos_OBMIQ_C1')\n", "2025-07-22T14:59:41.279914Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 434.67, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_C1\n", "2025-07-22T14:59:41.438353Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.37, 'cpu_percent': 0.0} file_size=12382 format=markdown output_path=santos_obmiq_cum_c1_report.md\n", "✅ C-1井数据已保存: santos_obmiq_cum_c1.wp.xlsx\n", "   数据形状: (1067, 80)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取T-1井数据(训练)...\n", "2025-07-22T14:59:41.472313Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.37, 'cpu_percent': 0.0} curve_count=15 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-22T14:59:41.495729Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.37, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10'] operation=extract_metadata output_curve_count=17 output_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10']\n", "2025-07-22T14:59:41.626990Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 439.94, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:41.638912Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 439.94, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-22T14:59:41.651250Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 439.94, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-22T14:59:41.665101Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 439.94, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:41.671321Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 439.94, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'SWB_NMR', 'BFV_NMR', 'PHIE_NMR', 'RD_LOG10', 'CN', 'DRES', 'DEN', 'RS_LOG10', 'DT', 'BVI_NMR', 'SDR_PROXY', 'PHIT_NMR', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=80 source_rows=16303 target_rows=1661\n", "2025-07-22T14:59:41.688886Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 439.94, 'cpu_percent': 0.0} curve_count=15 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_t_1\n", "2025-07-22T14:59:41.705370Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1601 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.0, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1661 removed_rows=60\n", "2025-07-22T14:59:41.719594Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.0, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-22T14:59:41.752235Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.0, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:41.798775Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.0, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-22T14:59:41.822493Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.0, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_t1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_T1') save_head_info=True save_well_map=True\n", "2025-07-22T14:59:41.842894Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.0, 'cpu_percent': 0.0} curve_count=17 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1601, 80)\n", "2025-07-22T14:59:42.714145Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.11, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.871\n", "2025-07-22T14:59:42.729278Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.11, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-22T14:59:42.849995Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.18, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_t1.wp.xlsx processing_time=1.028 project_name=WpIdentifier('Santos_OBMIQ_T1')\n", "2025-07-22T14:59:42.860182Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.18, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_T1\n", "2025-07-22T14:59:43.017750Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.22, 'cpu_percent': 0.0} file_size=12365 format=markdown output_path=santos_obmiq_cum_t1_report.md\n", "✅ T-1井数据已保存: santos_obmiq_cum_t1.wp.xlsx\n", "   数据形状: (1601, 80)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取所有井数据(训练)...\n", "2025-07-22T14:59:43.037999Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.22, 'cpu_percent': 0.0} curve_count=15 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-22T14:59:43.055527Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 440.22, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10'] operation=extract_metadata output_curve_count=17 output_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10']\n", "2025-07-22T14:59:43.110385Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 479.37, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:43.121216Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.0, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-22T14:59:43.139000Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.0, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-22T14:59:43.155520Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.0, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:43.168172Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 480.0, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'SWB_NMR', 'BFV_NMR', 'PHIE_NMR', 'RD_LOG10', 'CN', 'DRES', 'DEN', 'RS_LOG10', 'DT', 'BVI_NMR', 'SDR_PROXY', 'PHIT_NMR', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=80 source_rows=16303 target_rows=16303\n", "2025-07-22T14:59:43.183389Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 449.4, 'cpu_percent': 0.0} curve_count=15 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all\n", "2025-07-22T14:59:43.207315Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=2668 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 451.04, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=13635\n", "2025-07-22T14:59:43.241846Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 451.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-22T14:59:43.266708Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 451.12, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:43.280524Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 451.12, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-22T14:59:43.288945Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 451.12, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All') save_head_info=True save_well_map=True\n", "2025-07-22T14:59:43.306627Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 451.12, 'cpu_percent': 0.0} curve_count=17 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(2668, 80)\n", "2025-07-22T14:59:43.867305Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 465.04, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.561\n", "2025-07-22T14:59:43.878869Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 463.44, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-22T14:59:43.891350Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 463.45, 'cpu_percent': 0.0}\n", "2025-07-22T14:59:43.898517Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 463.45, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-22T14:59:47.541957Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 489.79, 'cpu_percent': 0.0}\n", "2025-07-22T14:59:47.549503Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 489.79, 'cpu_percent': 0.0}\n", "2025-07-22T14:59:49.337777Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 491.36, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx processing_time=6.049 project_name=WpIdentifier('Santos_OBMIQ_All')\n", "2025-07-22T14:59:49.361531Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 491.36, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All\n", "2025-07-22T14:59:49.659913Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 493.03, 'cpu_percent': 0.0} file_size=27274 format=markdown output_path=santos_obmiq_cum_all_report.md\n", "✅ 所有井数据已保存: santos_obmiq_cum_all.wp.xlsx\n", "   数据形状: (2668, 80)\n", "   数据集类型: WpContinuousDataset\n", "   井名分布: {'T-1': 1601, 'C-1': 1067}\n", "\n", "📍 提取所有井数据(预测)...\n", "2025-07-22T14:59:49.702096Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 493.03, 'cpu_percent': 0.0} curve_count=15 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq_all_apply\n", "2025-07-22T14:59:49.724138Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 493.03, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=17 input_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10'] operation=extract_metadata output_curve_count=17 output_curves=['DT', 'SDR_PROXY', 'DPHIT_NMR', 'WELL_NO', 'DEN', 'BFV_NMR', 'DRES', 'DT2_P50', 'RS_LOG10', 'MD', 'PHI_T2_DIST_CUM', 'SWB_NMR', 'PHIT_NMR', 'BVI_NMR', 'CN', 'PHIE_NMR', 'RD_LOG10']\n", "2025-07-22T14:59:49.810269Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 532.57, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:49.838682Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.22, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-22T14:59:49.854567Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.22, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq_all_apply target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-22T14:59:49.872398Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.22, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:49.887556Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.22, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'SWB_NMR', 'BFV_NMR', 'PHIE_NMR', 'RD_LOG10', 'CN', 'DRES', 'DEN', 'RS_LOG10', 'DT', 'BVI_NMR', 'SDR_PROXY', 'PHIT_NMR', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=80 source_rows=16303 target_rows=16303\n", "2025-07-22T14:59:49.903856Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 502.62, 'cpu_percent': 0.0} curve_count=13 dropna_how=any new_dataset=nmr_obmiq_all_apply_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all_apply\n", "2025-07-22T14:59:49.926724Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=4599 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 505.48, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=11704\n", "2025-07-22T14:59:49.972376Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 505.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-22T14:59:49.989750Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 505.61, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-22T14:59:50.007109Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 505.61, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-22T14:59:50.016344Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 505.61, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All_Apply') save_head_info=True save_well_map=True\n", "2025-07-22T14:59:50.038023Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 505.61, 'cpu_percent': 0.0} curve_count=17 dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') dataset_type=Continuous df_shape=(4599, 80)\n", "2025-07-22T14:59:50.967834Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 521.85, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') processing_time=0.93\n", "2025-07-22T14:59:50.977634Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 519.07, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-22T14:59:50.996256Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 519.07, 'cpu_percent': 0.0}\n", "2025-07-22T14:59:51.011222Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 519.07, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-22T14:59:56.602199Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 564.52, 'cpu_percent': 0.0}\n", "2025-07-22T14:59:56.613587Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 564.52, 'cpu_percent': 0.0}\n", "2025-07-22T14:59:59.596162Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 566.8, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=9.58 project_name=WpIdentifier('Santos_OBMIQ_All_Apply')\n", "2025-07-22T14:59:59.619259Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 566.8, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All_Apply\n", "2025-07-22T15:00:00.007551Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 566.86, 'cpu_percent': 0.0} file_size=34845 format=markdown output_path=santos_obmiq_cum_all_apply_report.md\n", "✅ 所有井数据(预测）已保存: santos_obmiq_cum_all_apply.wp.xlsx\n", "   数据形状: (4599, 80)\n", "   数据集类型: WpContinuousDataset\n", "\n", "🎉 OBMIQ数据集提取完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "    from logwp.models.well_project import WpWellProject\n", "\n", "    print(\"🔧 开始提取OBMIQ数据集(训练)...\")\n", "\n", "    try:\n", "        # 1. 提取C-1井的数据\n", "        print(\"\\n📍 提取C-1井数据(训练)...\")\n", "        c1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'C-1'\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_c_1\", c1_dataset)\n", "        c1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_c_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_c1 = WpWellProject(name=\"Santos_OBMIQ_C1\")\n", "        temp_project_c1.add_dataset(\"nmr_obmiq\", c1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        c1_path = \"santos_obmiq_cum_c1.wp.xlsx\"\n", "        writer.write(temp_project_c1, c1_path, apply_formatting=False)\n", "        report_path = temp_project_c1.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_c1_report.md\"\n", "        )\n", "\n", "        print(f\"✅ C-1井数据已保存: {c1_path}\")\n", "        print(f\"   数据形状: {c1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(c1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ C-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 2. 提取T-1井的数据\n", "        print(\"\\n📍 提取T-1井数据(训练)...\")\n", "        t1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'T-1'\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_t_1\", t1_dataset)\n", "        t1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_t_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_t1 = WpWellProject(name=\"Santos_OBMIQ_T1\")\n", "        temp_project_t1.add_dataset(\"nmr_obmc\", t1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        t1_path = \"santos_obmiq_cum_t1.wp.xlsx\"\n", "        writer.write(temp_project_t1, t1_path, apply_formatting=False)\n", "        report_path = temp_project_t1.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_t1_report.md\"\n", "        )\n", "\n", "        print(f\"✅ T-1井数据已保存: {t1_path}\")\n", "        print(f\"   数据形状: {t1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(t1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ T-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 3. 提取所有井的数据\n", "        print(\"\\n📍 提取所有井数据(训练)...\")\n", "        all_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all\", all_dataset)\n", "        all_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq\", all_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_dataset).__name__}\")\n", "\n", "        # 显示井名统计\n", "        if 'WELL_NO' in all_dataset.df.columns:\n", "            well_counts = all_dataset.df['WELL_NO'].value_counts()\n", "            print(f\"   井名分布: {dict(well_counts)}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "\n", "    try:\n", "        # 4. 提取所有井的数据(预测)\n", "        print(\"\\n📍 提取所有井数据(预测)...\")\n", "        all_apply_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves #需要包含真值，这样方便对比\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all_apply\", all_apply_dataset)\n", "        all_apply_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves_dropna, # dropna时不考虑真值\n", "            new_dataset_name=\"nmr_obmiq_all_apply_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All_Apply\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq_apply\", all_apply_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_apply_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据(预测）已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_apply_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_apply_dataset).__name__}\")\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据(预测）提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 OBMIQ数据集提取完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过OBMIQ数据集提取\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}