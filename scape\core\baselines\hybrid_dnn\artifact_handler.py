"""scape.core.baselines.hybrid_dnn.artifact_handler - 产物处理器

本模块提供一个无状态的工具类，用于处理OBMIQ训练和预测步骤所有产物的
序列化/反序列化和读/写操作。

Architecture
------------
设计原则: 无状态、职责单一 (I/O操作)

Classes:
    ObmiqArtifactHandler: 产物处理器

References:
    - 《可追踪机器学习组件开发框架》§3.3
"""
from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict

import joblib
import pandas as pd


class DnnArtifactHandler:
    """
    服务于OBMIQ所有步骤的无状态产物处理器。
    """

    @staticmethod
    def save_parameters(params: Dict[str, Any], path: Path):
        """将参数字典保存为JSON文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        path.write_text(json.dumps(params, indent=2, ensure_ascii=False))

    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path):
        """将DataFrame保存为CSV文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=False)

    @staticmethod
    def save_model_assets(assets: Dict[str, Any], path: Path):
        """使用joblib将完整的模型资产字典序列化到单个文件。

        这确保了模型权重、超参数、预处理器和元数据被原子地保存。
        """
        path.parent.mkdir(parents=True, exist_ok=True)
        joblib.dump(assets, path)

    @staticmethod
    def load_model_assets(path: Path) -> Dict[str, Any]:
        """从文件加载完整的模型资产字典。"""
        return joblib.load(path)
