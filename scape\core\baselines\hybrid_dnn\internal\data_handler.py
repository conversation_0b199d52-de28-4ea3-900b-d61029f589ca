"""scape.core.baselines.hybrid_dnn.internal.data_handler - Hybrid DNN PyTorch数据处理器

本模块负责将原始的Pandas DataFrame转换为PyTorch模型所需的DataLoader对象。
它封装了所有数据预处理逻辑，包括逐样本归一化、特征标准化，并确保在
交叉验证过程中正确处理数据以防止泄露。

Architecture
------------
层次/依赖: scape/core/baselines/hybrid_dnn/internal层，被tuning和training规程调用
设计原则: 职责单一、防止数据泄露、与训练逻辑解耦

Classes:
    DnnDataset: 自定义的PyTorch Dataset类

Functions:
    create_dataloaders_for_fold: 为单个CV折创建训练和验证DataLoader
    prepare_final_preprocessors: 为最终模型训练准备预处理器

References:
    - 《swift_pso_基准模型开发计划(第二阶段)_new2.md》§2.4
"""
from __future__ import annotations

from typing import Any, Dict, List, Tuple

import numpy as np
import pandas as pd
import torch
from sklearn.preprocessing import StandardScaler
from torch.utils.data import DataLoader, Dataset

from ..config import DnnTrainingConfig


class DnnDataset(Dataset):
    """
    一个自定义的PyTorch Dataset，用于Hybrid DNN混合输入模型。

    它将预处理后的NumPy数组转换为PyTorch张量，并按模型期望的
    字典格式返回每个样本。
    """

    def __init__(
        self,
        sequence_data: np.ndarray,
        tabular_data: np.ndarray,
        targets: np.ndarray,
    ):
        """初始化数据集。

        Args:
            sequence_data: 序列输入数据 (n_samples, sequence_length)。
            tabular_data: 表格输入数据 (n_samples, num_tabular_features)。
            targets: 目标值 (n_samples, 1)。
        """
        self.sequence_data = sequence_data.astype(np.float32)
        self.tabular_data = tabular_data.astype(np.float32)
        self.targets = targets.astype(np.float32).reshape(-1, 1) # 确保目标是 (n, 1)

    def __len__(self) -> int:
        """返回数据集中的样本总数。"""
        return len(self.targets)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """根据索引获取一个样本。"""
        # 序列数据需要增加一个通道维度 (C, L) -> (1, L)
        sequence_tensor = torch.from_numpy(self.sequence_data[idx]).unsqueeze(0)
        tabular_tensor = torch.from_numpy(self.tabular_data[idx])
        target_tensor = torch.from_numpy(self.targets[idx])

        return {
            "sequence_input": sequence_tensor,
            "tabular_input": tabular_tensor,
            "target": target_tensor,
        }


def create_dataloaders_for_fold(
    train_df: pd.DataFrame,
    val_df: pd.DataFrame,
    config: DnnTrainingConfig,
    feature_selectors: Dict[str, Any],
) -> Tuple[DataLoader, DataLoader, Dict[str, Any]]:
    """为单个交叉验证折创建训练和验证的DataLoader。

    此函数是防止数据泄露的关键。它严格遵循以下流程：
    1. 仅在`train_df`上拟合StandardScaler。
    2. 使用拟合好的scaler分别转换`train_df`和`val_df`。
    3. 对两个DataFrame执行逐样本归一化。
    4. 创建并返回对应的DataLoader和拟合好的预处理器。

    Args:
        train_df: 当前CV折的训练数据。
        val_df: 当前CV折的验证数据。
        config: 训练配置，用于获取batch_size。
        feature_selectors: 包含各特征列名的字典。

    Returns:
        一个元组 (train_loader, val_loader, preprocessors)，其中
        preprocessors是一个包含拟合好的scaler的字典。
    """
    seq_cols = feature_selectors["sequence_cols"]
    norm_col = feature_selectors["normalization_col"]
    tab_cols = feature_selectors["tabular_cols"]
    target_col = feature_selectors["target_col"] # 单个目标

    # 1. 拟合预处理器 (仅在训练集上)
    tabular_scaler = StandardScaler()
    # 创建副本以避免原地修改输入DataFrame，防止数据泄露
    train_df_processed = train_df.copy()
    val_df_processed = val_df.copy()

    train_df_processed[tab_cols] = tabular_scaler.fit_transform(train_df[tab_cols])
    preprocessors = {"tabular_scaler": tabular_scaler}

    # 2. 转换验证集 (使用训练集拟合的scaler)
    val_df_processed[tab_cols] = tabular_scaler.transform(val_df[tab_cols])

    # 3. 准备数据数组
    def _extract_data(df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        # 序列数据逐样本归一化
        sequence_values = df[seq_cols].values
        norm_values = df[norm_col].values[:, np.newaxis]
        # 避免除以零
        norm_values[norm_values == 0] = 1e-6
        normalized_sequence = sequence_values / norm_values

        # 对目标变量应用log10变换
        epsilon = 1e-9 # 防止log(0)
        target_values = np.log10(np.maximum(df[target_col].values, epsilon))

        tabular_values = df[tab_cols].values
        return normalized_sequence, tabular_values, target_values

    X_seq_train, X_tab_train, y_train = _extract_data(train_df_processed)
    X_seq_val, X_tab_val, y_val = _extract_data(val_df_processed)

    # 4. 创建Dataset和DataLoader
    train_dataset = DnnDataset(X_seq_train, X_tab_train, y_train)
    val_dataset = DnnDataset(X_seq_val, X_tab_val, y_val)

    train_loader = DataLoader(
        train_dataset, batch_size=config.batch_size, shuffle=True, num_workers=0
    )
    val_loader = DataLoader(
        val_dataset, batch_size=config.batch_size, shuffle=False, num_workers=0
    )

    return train_loader, val_loader, preprocessors


def prepare_final_preprocessors(
    df: pd.DataFrame, tab_cols: List[str]
) -> Dict[str, Any]:
    """为最终模型训练准备预处理器。

    在全部数据上拟合预处理器，用于最终模型的训练和部署。
    """
    tabular_scaler = StandardScaler()
    tabular_scaler.fit(df[tab_cols])
    return {"tabular_scaler": tabular_scaler}
