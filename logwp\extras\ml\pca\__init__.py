"""PCA分析包公共API。

提供测井数据主成分分析的完整功能，包括数据预处理、PCA计算、
可视化和模型持久化。支持GPU加速和CPU自动回退。

Architecture
------------
层次/依赖: logwp.extras.pca包，测井数据PCA分析
设计原则: 格式无关、GPU加速、类型安全、实用主义
性能特征: GPU/CPU自动切换、内存优化、异步I/O

Features
--------
🔬 **核心分析功能**
- 智能数据预处理和标准化
- 高性能PCA计算（GPU加速）
- 主成分选择和方差分析
- 数据重构和逆变换

💾 **模型持久化**
- 多格式支持（JSON、Pickle、NPZ）
- 自动格式检测和版本兼容
- 完整性验证和错误恢复

📊 **科学可视化**
- 方差解释图和累积方差分析
- 2D/3D PCA散点图
- 主成分载荷热图
- 高分辨率图像导出

🚀 **便利功能**
- 一键完整分析流程
- 智能主成分数量推荐
- 结构化分析结果摘要

Examples
--------
**快速开始 - 一键分析**：

>>> from logwp.extras.pca import analyze_pca_complete
>>>
>>> # 一键完成完整PCA分析
>>> pca_model, preprocessed, pca_result = analyze_pca_complete(dataset)
>>>
>>> # 获取分析摘要
>>> from logwp.extras.pca import create_pca_summary
>>> summary = create_pca_summary(pca_model, pca_result)
>>> print(f"总方差解释: {summary['variance_analysis']['total_variance_explained']:.1%}")

**分步分析流程**：

>>> from logwp.extras.pca import preprocess_data, apply_pca_full, explained_variance
>>>
>>> # 1. 数据预处理
>>> processed_data = preprocess_data(dataset)
>>>
>>> # 2. PCA计算
>>> pca_model, pca_data = apply_pca_full(processed_data, n_components=5)
>>>
>>> # 3. 方差分析
>>> variance_ratio, cumulative_ratio = explained_variance(pca_model)
>>> print(f"前5个主成分解释方差: {cumulative_ratio[4]:.1%}")

**可视化分析**：

>>> from logwp.extras.pca import plot_variance_explained, plot_pca_scatter, plot_pca_heatmap
>>>
>>> # 方差解释图
>>> plot_variance_explained(pca_model, "variance_analysis.png", dpi=300)
>>>
>>> # PCA散点图
>>> plot_pca_scatter(pca_data, n_components=3, "scatter_3d.png")
>>>
>>> # 载荷热图
>>> plot_pca_heatmap(pca_model, "loadings_heatmap.pdf")

**模型持久化**：

>>> from logwp.extras.pca import save_pca_model, load_pca_model
>>>
>>> # 保存模型
>>> save_pca_model(pca_model, "my_pca_model.pkl", format="pickle")
>>>
>>> # 加载模型
>>> loaded_model = load_pca_model("my_pca_model.pkl")

References
----------
《SCAPE_DDS_logwp_extras_pca.md》- PCA包详细设计文档
《SCAPE_MS_方法说明书》- SCAPE项目科学方法定义
《SCAPE_API_logwp_models.md》- logwp.models包API文档
"""

from __future__ import annotations

# 核心功能API
from .pca import (
    # 基础功能
    preprocess_data,  # ✅ 数据预处理
    apply_pca_full,  # ✅ PCA计算
    inverse_transform,  # ✅ 逆变换
    select_and_apply_pca,  # ✅ 主成分选择
    explained_variance,  # ✅ 方差分析

    # 模型持久化
    save_pca_model,  # ✅ 模型保存
    load_pca_model,  # ✅ 模型加载

    # 便利功能 - 任务3.3新增
    analyze_pca_complete,  # ✅ 一键完整分析
    get_optimal_n_components,  # ✅ 最优主成分数推荐
    create_pca_summary,  # ✅ 分析结果摘要
)

# 可视化API - 已实现
from .pca import (
    plot_variance_explained,  # ✅ 已实现
    plot_pca_scatter,  # ✅ 已实现
    plot_pca_heatmap,  # ✅ 已实现
)

# 可视化配置类
from .internal.visualization import SaveConfig  # ✅ 已实现

# 异常API - 可选导入
from .exceptions import (
    WpPcaError,  # ✅ PCA基础异常
    WpPcaDataError,  # ✅ 数据异常
    WpPcaComputationError,  # ✅ 计算异常
    WpPcaModelError,  # ✅ 模型异常
    WpPcaVisualizationError,  # ✅ 可视化异常
)

# 完整API导出列表
__all__ = [
    # 核心功能
    "preprocess_data",  # ✅ 数据预处理
    "apply_pca_full",  # ✅ PCA计算
    "inverse_transform",  # ✅ 逆变换
    "select_and_apply_pca",  # ✅ 主成分选择
    "explained_variance",  # ✅ 方差分析

    # 模型持久化
    "save_pca_model",  # ✅ 模型保存
    "load_pca_model",  # ✅ 模型加载

    # 便利功能
    "analyze_pca_complete",  # ✅ 一键完整分析
    "get_optimal_n_components",  # ✅ 最优主成分数推荐
    "create_pca_summary",  # ✅ 分析结果摘要

    # 可视化
    "plot_variance_explained",  # ✅ 方差解释图
    "plot_pca_scatter",  # ✅ PCA散点图
    "plot_pca_heatmap",  # ✅ 载荷热图
    "SaveConfig",  # ✅ 可视化配置

    # 异常类 - 可选导入
    "WpPcaError",  # ✅ PCA基础异常
    "WpPcaDataError",  # ✅ 数据异常
    "WpPcaComputationError",  # ✅ 计算异常
    "WpPcaModelError",  # ✅ 模型异常
    "WpPcaVisualizationError",  # ✅ 可视化异常
]
