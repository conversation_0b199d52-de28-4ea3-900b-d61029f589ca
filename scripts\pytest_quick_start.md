# Pytest 大师级速查表 1.0

## 核心思想：测试界的“乐高积木”与“依赖注入”

想象一下，你不是在写僵硬、独立的测试脚本，而是在组装一个个功能强大、可独立插拔、又能巧妙组合的“乐高积木”。

* **测试函数 (Test Functions)**: 这是最基础的积木块，一个简单的 `test_` 开头的 Python 函数。
* **断言 (Assertions)**: 你用最直观的 `assert` 关键字来声明“我期望A等于B”，pytest 会智能地展示出差异细节，无需记忆 `assertEqual`, `assertTrue` 等繁琐方法。
* **Fixtures**: 这是 pytest 的“超级积木”和灵魂。它不仅仅是测试前后的准备（setup）和清理（teardown），更是一种**依赖注入 (Dependency Injection)**系统。当你的测试函数需要某个东西（比如一个数据库连接、一个临时文件、一个登录后的用户对象），你只需在函数参数里“声明”这个需求，pytest 就会自动找到对应的 fixture，执行它，并将结果“注入”到你的测试函数中。这让测试代码变得极其干净、模块化且易于复用。

> **专家见解**: 请始终用“依赖注入”的思维去理解 Fixture。它不是一个简单的辅助函数，而是一个承诺：“我会为你准备好测试所需的一切，你只管拿来用。”

---

## 场景零：环境初始化与项目配置

**目标**: 在新环境或新项目中首次配置 pytest，并建立一套规范的测试结构。

### 1. 安装 Pytest 及常用插件

```bash
# 安装核心的 pytest 框架
pip install pytest

# (强烈推荐) 安装常用插件，开启更多超能力
pip install pytest-cov  # 用于生成测试覆盖率报告
pip install pytest-mock # 集成 mock 功能，轻松模拟对象
pip install pytest-xdist # 并发执行测试，极大提升速度
```

### 2. 创建项目结构与配置文件

一个良好的起点结构如下：

```
my_project/
├── src/
│   ├── my_app/
│   │   ├── __init__.py
│   │   └── main.py
├── tests/
│   ├── __init__.py
│   ├── test_main.py
│   └── conftest.py
├── pytest.ini
└── .gitignore
```

> **专家见解**:
> * `tests/`: 所有测试代码的家。
> * `conftest.py`: 这是 pytest 的“魔法文件”。在此文件中定义的 fixture 可以在整个 `tests` 目录下被所有测试用例自动发现和使用，无需手动导入。它是共享 fixture 的最佳实践场所。
> * `pytest.ini`: 项目级的 pytest 配置文件。在这里统一配置，可以避免在命令行里敲写冗长的参数，并确保团队成员的测试行为一致。

### 3. 配置 `pytest.ini`

在项目根目录创建 `pytest.ini` 文件，并添加基础配置：

```ini
[pytest]
# 命令行默认参数
addopts = -ra -q --cov=src --cov-report=term-missing

# 指定测试文件和函数的命名规则
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 注册自定义标记 (Markers)，避免拼写错误并提供说明
markers =
    smoke: 运行核心功能的冒烟测试
    slow: 标记执行缓慢的测试
    api: 与真实 API 交互的测试
```
> **专家见解**: `addopts` 是你第一个应该配置的选项。`-ra` 会在测试结束后简要报告所有测试（除了通过的），`-q` 是静默模式，`--cov=src` 指定了要计算代码覆盖率的目录，这几乎是现代项目的标配。

---

## 场景一：编写你的第一个测试

**目标**: 学习如何编写基础测试，包括简单的断言、异常测试和使用 fixture。

### 1. 基础断言

在 `tests/test_main.py` 中：

```python
# tests/test_main.py

def test_addition():
    # pytest 使用原生 assert，并提供丰富的失败信息
    assert 1 + 1 == 2

def test_string_contains():
    assert "hello" in "hello world"

# 假设 src/my_app/main.py 有一个简单的函数
# def get_greeting(name):
#     return f"Hello, {name}"

from src.my_app.main import get_greeting

def test_get_greeting():
    assert get_greeting("World") == "Hello, World"
```

> **专家见-解**: 忘掉 `unittest` 的 `self.assertEqual()` 吧！直接使用 `assert`。当断言失败时，pytest 会重写断言并展示出表达式中每个变量的实际值，这被称为“断言内省”（Assertion Introspection），是其核心优势之一。

### 2. 测试预期异常

```python
# tests/test_main.py
import pytest

def test_divide_by_zero():
    # 使用 pytest.raises 作为上下文管理器，优雅地检查异常
    with pytest.raises(ZeroDivisionError):
        result = 1 / 0

    # 你甚至可以捕获异常对象，并对它的属性进行断言
    with pytest.raises(ValueError, match="must be a positive number"):
        my_func(-1) # 假设 my_func 会在输入负数时抛出 ValueError
```

### 3. 使用 Fixture (入门)

**目标**: 为多个测试提供一个共享的、干净的数据对象。

首先，在 `conftest.py` 中定义一个 fixture：

```python
# tests/conftest.py
import pytest

@pytest.fixture
def sample_user_data():
    """提供一个字典作为测试用户数据"""
    print("\n(Setting up sample_user_data)") # 方便观察执行时机
    data = {"username": "testuser", "email": "<EMAIL>", "is_active": True}
    yield data
    # yield 关键字之后的部分是清理代码 (teardown)
    print("\n(Tearing down sample_user_data)")
    data.clear()
```

然后，在测试函数中“请求”它：

```python
# tests/test_main.py

# 只需将 fixture 的名字作为参数传入
def test_user_creation(sample_user_data):
    assert sample_user_data["username"] == "testuser"
    assert "@" in sample_user_data["email"]

def test_user_is_active(sample_user_data):
    assert sample_user_data["is_active"] is True
```

> **专家见-解**: 注意，`sample_user_data` fixture 会在每个需要它的测试函数运行前执行一次。`yield` 是现代 `pytest` fixture 的推荐写法，它清晰地分离了 `setup` 和 `teardown` 逻辑。

---

## 场景二：日常核心测试操作

**目标**: 高效地运行、筛选和调试测试。

### 1. 运行所有测试

```bash
# 在项目根目录执行，pytest 会自动发现 tests/ 目录下的所有测试
pytest
```

### 2. 运行特定文件或目录

```bash
# 仅运行指定文件
pytest tests/test_main.py

# 仅运行指定目录
pytest tests/api_tests/
```

### 3. 通过关键字表达式筛选测试 (`-k`)

```bash
# 运行函数名中包含 "user" 的测试
pytest -k "user"

# 运行函数名中包含 "user" 且不包含 "creation" 的测试
pytest -k "user and not creation"

# 运行包含 "user" 或 "login" 的测试
pytest -k "user or login"
```

### 4. 通过标记筛选测试 (`-m`)

这是比 `-k` 更规范、更强大的筛选方式。

```bash
# 首先，在测试函数上用装饰器打上标记
# @pytest.mark.smoke
# def test_user_creation(sample_user_data): ...

# 运行所有标记为 smoke 的测试
pytest -m smoke

# 运行所有未标记为 slow 的测试
pytest -m "not slow"

# 运行 smoke 或 api 的测试
pytest -m "smoke or api"
```
> **警告**: 如果你使用的标记没有在 `pytest.ini` 中注册，pytest 会发出警告。这是一个良好的实践，可以防止团队成员间的标记拼写不一致。

### 5. 失败时立即停止 (`-x` 或 `--exitfirst`)

```bash
# 在遇到第一个失败或错误的测试时，立即停止执行
pytest -x
```

### 6. 查看详细输出

```bash
# -v: (verbose) 显示每个测试函数的完整名称和结果
pytest -v

# -s: 在测试执行时，显示测试函数中的 print() 输出
pytest -s

# -ra: (report all) 在末尾报告所有测试的摘要，非常有用
pytest -ra

# 组合使用
pytest -v -s
```

---

## 场景三：“糟糕，测试失败了！” (调试)

**目标**: 快速定位并修复失败的测试。

### 1. 重新运行上次失败的测试

```bash
# 仅运行上一次执行中失败的测试
pytest --lf  # --last-failed
```
> **专家见-解**: 这是我日常使用频率最高的命令之一。修改完代码后，无需记住失败的是哪个测试，`--lf` 会帮你搞定。

### 2. 失败时进入调试器

```bash
# 当测试失败时，自动进入 Python 的内置调试器 pdb
pytest --pdb
```
进入 `pdb` 后，你可以检查变量、单步执行代码，就像在调试普通程序一样。

### 3. 获取更详细的失败信息

```bash
# --showlocals: 显示失败断言处的所有局部变量的值
pytest --showlocals

# -vv: (extra verbose) 提供比 -v 更详尽的信息，包括 fixture 的 setup 过程
pytest -vv
```

---

## 场景四：高级用法与最佳实践

**目标**: 提升测试的复用性、覆盖面和维护性。

### 1. 参数化测试 (`@pytest.mark.parametrize`)

**目标**: 用多组不同的数据重复测试同一个逻辑。

```python
# tests/test_math.py
import pytest

@pytest.mark.parametrize("test_input, expected_output", [
    (2, 4),      # 第1组数据
    (3, 9),      # 第2组数据
    (10, 100),   # 第3组数据
    pytest.param(0, 0, marks=pytest.mark.smoke) # 单独标记某一组数据
])
def test_square(test_input, expected_output):
    assert test_input * test_input == expected_output
```
> **专家见-解**: 参数化是避免代码重复的终极武器。当你有多个测试只是输入和预期输出不同时，立即重构为参数化测试。

### 2. 控制 Fixture 的作用域 (Scope)

**目标**: 让 fixture 在多个测试之间共享，以节省昂贵的 setup/teardown 时间（如数据库连接）。

```python
# tests/conftest.py
import pytest

@pytest.fixture(scope="session")
def db_connection():
    """
    一个昂贵的数据库连接。
    scope="session" 意味着它在整个测试会话中只会执行一次。
    """
    print("\n(Connecting to DB...)")
    conn = ... # 实际的连接代码
    yield conn
    print("\n(Closing DB connection...)")
    conn.close()

# 其他可选的 scope:
# "module": 每个测试模块执行一次
# "class": 每个测试类执行一次
# "function": (默认) 每个测试函数执行一次
```

### 3. 使用 Mock (`pytest-mock` 插件)

**目标**: 隔离被测单元，替换掉外部依赖（如 API 调用、数据库写入）。

```python
# tests/test_api_client.py

def test_get_data_from_external_api(mocker):
    # 'mocker' 是 pytest-mock 提供的一个 fixture

    # 模拟 requests.get 方法，让它返回一个假的对象
    mock_response = mocker.Mock()
    mock_response.json.return_value = {"key": "mock_value"}
    mock_response.status_code = 200
    mocker.patch("requests.get", return_value=mock_response)

    # 调用你的代码，它内部会调用 requests.get
    from my_app.api_client import get_data
    result = get_data("some_url")

    # 断言你的代码正确处理了模拟的返回
    assert result == {"key": "mock_value"}
```
> **专家见-解**: `mocker.patch` 是最常用的 mock 功能。它的目标字符串路径应该是**被测代码中调用依赖的地方**，而不是依赖本身的位置。这是一个常见的混淆点。
