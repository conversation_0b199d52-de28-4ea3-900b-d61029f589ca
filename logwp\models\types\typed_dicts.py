from __future__ import annotations

from logwp.infra.constants import WpComputeBackend

"""logwp.models.types.typed_dicts - TypedDict数据结构定义

现代化TypedDict实现，提供结构化数据传输对象。

Architecture
------------
层次/依赖: types层数据结构，仅依赖标准库和typing
设计原则: 结构化数据、类型安全、JSON友好、扩展性
性能特征: 零运行时开销、编译时检查、序列化优化

遵循CCG规范：
- TS-3: 结构化数据使用TypedDict，可选字段用NotRequired
- TS-1: 所有字段必须有完整类型注解
- CT-2: 所有字符串键名必须使用枚举常量

References
----------
- 《SCAPE_CCG_编码与通用规范.md》TS-3 - TypedDict数据规范
- 《SCAPE_DDS_详细设计_logwp.md》§3.1.2 - TypedDict设计
"""

from typing import Any, TypedDict, NotRequired
from datetime import datetime

from logwp.models.constants import WpDsType, WpProcessingStage


class WpDataDict(TypedDict):
    """数据集信息字典。

    用于数据集基本信息的结构化传输。

    Architecture
    ------------
    层次/依赖: 数据传输层，被datasets和models层使用
    设计原则: 结构化数据、类型安全、JSON序列化友好
    性能特征: 零运行时开销、编译时类型检查

    Examples:
        >>> data: WpDataDict = {
        ...     "name": "OBMIQ_logs",
        ...     "type": WpDsType.CONTINUOUS,
        ...     "rows": 1000,
        ...     "columns": 15,
        ...     "wells": ["C-1", "C-2"],
        ...     "memory_mb": 12.5
        ... }
    """
    name: str
    type: WpDsType
    rows: int
    columns: int
    wells: list[str]
    memory_mb: float
    created_at: NotRequired[datetime]
    modified_at: NotRequired[datetime]
    source_file: NotRequired[str]
    validation_status: NotRequired[bool]


class WpMetaDict(TypedDict):
    """元数据信息字典。

    用于项目和数据集元信息的结构化传输。

    Architecture
    ------------
    层次/依赖: 元数据层，被models和io层使用
    设计原则: 元信息管理、版本控制、审计追踪
    性能特征: 轻量级结构、快速序列化
    """
    version: str
    created_by: str
    created_at: datetime
    modified_at: NotRequired[datetime]
    description: NotRequired[str]
    tags: NotRequired[list[str]]
    attributes: NotRequired[dict[str, Any]]
    file_info: NotRequired[dict[str, Any]]
    statistics: NotRequired[dict[str, Any]]


class WpConfigDict(TypedDict):
    """配置信息字典。

    用于系统和算法配置的结构化传输。

    Architecture
    ------------
    层次/依赖: 配置层，被config和算法层使用
    设计原则: 配置管理、类型安全、环境变量支持
    性能特征: 配置缓存、快速访问、验证优化
    """
    chunk_size: int
    max_memory_gb: float
    enable_gpu: bool
    enable_async_io: bool
    log_level: str
    cache_dir: NotRequired[str]
    timeout_sec: NotRequired[int]
    feature_flags: NotRequired[dict[str, bool]]
    algorithm_params: NotRequired[dict[str, Any]]


class WpResultDict(TypedDict):
    """操作结果字典。

    用于API操作结果的统一结构化传输。

    Architecture
    ------------
    层次/依赖: API响应层，被所有操作返回使用
    设计原则: 统一响应格式、错误处理、性能监控
    性能特征: 轻量级响应、快速序列化、监控友好
    """
    is_valid: bool
    message: str
    data: NotRequired[Any]
    errors: NotRequired[list[str]]
    meta: NotRequired[WpMetaDict]
    performance: NotRequired[dict[str, float]]
    stage: NotRequired[WpProcessingStage]
    timestamp: NotRequired[datetime]


class WpErrorDict(TypedDict):
    """错误信息字典。

    用于错误信息的结构化传输和诊断。

    Architecture
    ------------
    层次/依赖: 异常处理层，被异常和日志系统使用
    设计原则: 错误诊断、上下文信息、调试友好
    性能特征: 轻量级错误信息、快速序列化
    """
    error_code: str
    error_type: str
    message: str
    operation: NotRequired[str]
    file_path: NotRequired[str]
    line_number: NotRequired[int]
    column_name: NotRequired[str]
    dataset_name: NotRequired[str]
    additional_info: NotRequired[dict[str, Any]]
    stack_trace: NotRequired[str]


class WpGpuDict(TypedDict):
    """GPU信息字典。

    用于GPU计算状态和性能信息的结构化传输。

    Architecture
    ------------
    层次/依赖: GPU计算层，被计算引擎和监控系统使用
    设计原则: GPU状态监控、性能追踪、设备管理
    性能特征: 实时GPU指标、内存监控、性能分析
    """
    device_id: int
    device_name: str
    is_available: bool
    memory_used_mb: float
    memory_total_mb: float
    memory_utilization: float
    compute_capability: NotRequired[str]
    cuda_version: NotRequired[str]
    computation_time_sec: NotRequired[float]
    speedup_ratio: NotRequired[float]
    backend: NotRequired[WpComputeBackend]
    fallback_to_cpu: NotRequired[bool]


# 复合类型别名
WpProjectConfig = WpConfigDict
"""项目配置类型别名，用于项目级配置管理。"""

WpMetaData = WpMetaDict
"""元数据类型别名，用于数据源元信息管理。"""
