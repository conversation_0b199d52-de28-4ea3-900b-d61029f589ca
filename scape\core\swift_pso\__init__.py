"""scape.core.swift_pso - SWIFT-PSO多步骤包

符合logwp.extras.tracking机器学习组件开发框架规范的SWIFT-PSO实现。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO算法实现
设计原则: 多步骤包架构、关注点分离、可追踪性
性能特征: 支持GPU加速、异步I/O、内存优化

Package Structure
-----------------
- training_facade.py: 训练步骤门面
- prediction_facade.py: 预测步骤门面
- visualization_facade.py: 可视化步骤门面
- config.py: Pydantic配置模型
- constants.py: 产物常量定义
- artifact_handler.py: 无状态产物处理器
- plotting.py: 绘图复现功能
- exceptions.py: 专属异常类
- internal/: 内部实现逻辑

Public API
----------
Step Functions (主要接口):
    - run_swift_pso_training_step: 执行SWIFT-PSO训练步骤
    - run_swift_pso_prediction_step: 执行SWIFT-PSO预测步骤
    - run_tsne_visualization_step: 执行t-SNE可视化步骤

Configuration Models:
    - SwiftPsoTrainingConfig: 训练步骤配置模型
    - SwiftPsoPredictionConfig: 预测步骤配置模型
    - TsneVisualConfig: t-SNE可视化配置模型

Artifact Constants:
    - SwiftPsoTrainingArtifacts: 训练步骤产物常量
    - SwiftPsoPredictionArtifacts: 预测步骤产物常量
    - TsneVisualArtifacts: 可视化步骤产物常量

Utility Functions:
    - SwiftPsoArtifactHandler: 产物处理器
    - replot_tsne_from_snapshot: 从快照复现t-SNE图表

User Convenience Functions:
    - get_swift_pso_base_profile: 获取SWIFT-PSO模块级基础绘图配置。
    - get_tsne_convergence_profile: 获取t-SNE收敛轨迹图的默认绘图配置。

Exception Classes:
    - SwiftPsoError: SWIFT-PSO根异常
    - SwiftPsoTrainingError: 训练步骤异常
    - SwiftPsoPredictionError: 预测步骤异常
    - SwiftPsoVisualizationError: 可视化步骤异常

References
----------
- 《SCAPE_MS_方法说明书》§4 - SWIFT-PSO算法定义
- 《logwp/extras/tracking/机器学习组件开发框架》- 架构规范


"""

# =============================================================================
# 导入所有公共API
# =============================================================================

# Step Functions - 主要接口
# 使用延迟导入避免循环导入问题
def run_swift_pso_training_step(*args, **kwargs):
    """延迟导入的训练步骤函数。"""
    from .training_facade import run_swift_pso_training_step as _run_training
    return _run_training(*args, **kwargs)

def run_swift_pso_prediction_step(*args, **kwargs):
    """延迟导入的预测步骤函数。"""
    from .prediction_facade import run_swift_pso_prediction_step as _run_prediction
    return _run_prediction(*args, **kwargs)

def run_tsne_visualization_step(*args, **kwargs):
    """延迟导入的可视化步骤函数。"""
    from .visualization_facade import run_tsne_visualization_step as _run_visualization
    return _run_visualization(*args, **kwargs)

# Configuration Models
from .config import (
    SwiftPsoTrainingConfig,
    SwiftPsoPredictionConfig,
    TsneVisualConfig,
)

# Artifact Constants
from .constants import (
    SwiftPsoTrainingArtifacts,
    SwiftPsoPredictionArtifacts,
    TsneVisualArtifacts,
    TsnePlotProfiles,
)

# Utility Classes and Functions
from .artifact_handler import SwiftPsoArtifactHandler

# 延迟导入绘图相关功能
def replot_tsne_from_snapshot(*args, **kwargs):
    """延迟导入的t-SNE重绘函数。"""
    from .plotting import replot_tsne_from_snapshot as _replot
    return _replot(*args, **kwargs)

# 导入plot_profiles模块以触发配置注册
try:
    from . import plot_profiles  # noqa: F401
except ImportError:
    # 如果导入失败，记录警告但不阻止模块加载
    import warnings
    warnings.warn("无法导入plot_profiles模块，t-SNE绘图配置可能不可用", ImportWarning)

# User Convenience Functions - 延迟导入
def get_swift_pso_base_profile():
    """延迟导入的基础配置获取函数。"""
    from .visualization_facade import get_swift_pso_base_profile as _get_base
    return _get_base()

def get_tsne_convergence_profile():
    """延迟导入的收敛配置获取函数。"""
    from .visualization_facade import get_tsne_convergence_profile as _get_convergence
    return _get_convergence()

# Exception Classes
from .exceptions import (
    SwiftPsoError,
    SwiftPsoTrainingError,
    SwiftPsoPredictionError,
    SwiftPsoVisualizationError,
)

# =============================================================================
# 公共API导出列表
# =============================================================================

__all__ = [
    # Step Functions - 主要接口
    "run_swift_pso_training_step",
    "run_swift_pso_prediction_step",
    "run_tsne_visualization_step",

    # Configuration Models
    "SwiftPsoTrainingConfig",
    "SwiftPsoPredictionConfig",
    "TsneVisualConfig",

    # Artifact Constants
    "SwiftPsoTrainingArtifacts",
    "SwiftPsoPredictionArtifacts",
    "TsneVisualArtifacts",

    "TsnePlotProfiles",

    # Utility Classes and Functions
    "SwiftPsoArtifactHandler",
    "replot_tsne_from_snapshot",

    # User Convenience Functions
    "get_swift_pso_base_profile",
    "get_tsne_convergence_profile",

    # Exception Classes
    "SwiftPsoError",
    "SwiftPsoTrainingError",
    "SwiftPsoPredictionError",
    "SwiftPsoVisualizationError",
]
