"""测试工具函数的功能。

测试utils模块中的工具函数，包括：
- ID生成和时间戳函数
- 文件操作和哈希计算
- JSON文件读写
- 字典合并和目录管理
"""

from __future__ import annotations

import json
import re
from pathlib import Path
from typing import Any, Dict

import pytest

from logwp.extras.tracking.utils import (
    generate_run_id,
    get_current_utc_timestamp,
    calculate_file_hash,
    get_file_size,
    copy_artifact_file,
    atomic_write_json,
    load_json_file,
    deep_merge_dict,
    ensure_directory,
)
from logwp.extras.tracking.exceptions import (
    ArtifactIOError,
    ManifestError,
)


class TestIDGeneration:
    """测试ID生成和时间戳函数。"""

    def test_generate_run_id_format(self):
        """测试运行ID格式。"""
        run_id = generate_run_id()

        # 验证格式：YYYYMMDD-HHMMSS-xxxxxxxx
        pattern = r"^\d{8}-\d{6}-[a-f0-9]{8}$"
        assert re.match(pattern, run_id), f"Invalid run_id format: {run_id}"

    def test_generate_run_id_uniqueness(self):
        """测试运行ID唯一性。"""
        ids = [generate_run_id() for _ in range(10)]

        # 所有ID应该不同
        assert len(set(ids)) == len(ids)

    def test_get_current_utc_timestamp_format(self):
        """测试UTC时间戳格式。"""
        timestamp = get_current_utc_timestamp()

        # 验证ISO格式：YYYY-MM-DDTHH:MM:SS.sssZ 或 YYYY-MM-DDTHH:MM:SS.ssssssZ
        pattern = r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z$"
        assert re.match(pattern, timestamp), f"Invalid timestamp format: {timestamp}"

    def test_get_current_utc_timestamp_timezone(self):
        """测试时间戳时区。"""
        timestamp = get_current_utc_timestamp()

        # 应该以Z结尾表示UTC时区
        assert timestamp.endswith("Z")


class TestFileOperations:
    """测试文件操作函数。"""

    def test_calculate_file_hash_sha256(self, sample_artifact_file: Path):
        """测试文件哈希计算（SHA256）。"""
        hash_value = calculate_file_hash(sample_artifact_file, algorithm="sha256")

        # SHA256哈希应该是64个十六进制字符
        assert len(hash_value) == 64
        assert all(c in "0123456789abcdef" for c in hash_value)

    def test_calculate_file_hash_md5(self, sample_artifact_file: Path):
        """测试文件哈希计算（MD5）。"""
        hash_value = calculate_file_hash(sample_artifact_file, algorithm="md5")

        # MD5哈希应该是32个十六进制字符
        assert len(hash_value) == 32
        assert all(c in "0123456789abcdef" for c in hash_value)

    def test_calculate_file_hash_consistency(self, sample_artifact_file: Path):
        """测试文件哈希一致性。"""
        hash1 = calculate_file_hash(sample_artifact_file)
        hash2 = calculate_file_hash(sample_artifact_file)

        # 同一文件的哈希应该相同
        assert hash1 == hash2

    def test_calculate_file_hash_nonexistent(self, temp_dir: Path):
        """测试计算不存在文件的哈希。"""
        nonexistent_file = temp_dir / "nonexistent.txt"

        with pytest.raises(ArtifactIOError) as exc_info:
            calculate_file_hash(nonexistent_file)

        assert "File not found" in str(exc_info.value)
        assert exc_info.value.operation == "hash"

    def test_get_file_size(self, sample_artifact_file: Path):
        """测试获取文件大小。"""
        size = get_file_size(sample_artifact_file)

        assert isinstance(size, int)
        assert size > 0

    def test_get_file_size_nonexistent(self, temp_dir: Path):
        """测试获取不存在文件的大小。"""
        nonexistent_file = temp_dir / "nonexistent.txt"

        with pytest.raises(ArtifactIOError) as exc_info:
            get_file_size(nonexistent_file)

        assert "File not found" in str(exc_info.value)
        assert exc_info.value.operation == "stat"

    def test_copy_artifact_file(self, sample_artifact_file: Path, temp_dir: Path):
        """测试产物文件复制。"""
        target_path = temp_dir / "copied_artifact.json"

        copy_artifact_file(sample_artifact_file, target_path)

        # 验证文件已复制
        assert target_path.exists()

        # 验证内容相同
        original_content = sample_artifact_file.read_text()
        copied_content = target_path.read_text()
        assert original_content == copied_content

    def test_copy_artifact_file_create_dirs(self, sample_artifact_file: Path, temp_dir: Path):
        """测试复制文件时自动创建目录。"""
        target_path = temp_dir / "subdir" / "nested" / "artifact.json"

        copy_artifact_file(sample_artifact_file, target_path, create_dirs=True)

        # 验证目录和文件都已创建
        assert target_path.exists()
        assert target_path.parent.exists()

    def test_copy_artifact_file_no_create_dirs(self, sample_artifact_file: Path, temp_dir: Path):
        """测试不自动创建目录时的复制。"""
        target_path = temp_dir / "nonexistent_dir" / "artifact.json"

        with pytest.raises(ArtifactIOError):
            copy_artifact_file(sample_artifact_file, target_path, create_dirs=False)

    def test_copy_artifact_file_nonexistent_source(self, temp_dir: Path):
        """测试复制不存在的源文件。"""
        source_path = temp_dir / "nonexistent.txt"
        target_path = temp_dir / "target.txt"

        with pytest.raises(ArtifactIOError) as exc_info:
            copy_artifact_file(source_path, target_path)

        assert "Source file not found" in str(exc_info.value)
        assert exc_info.value.operation == "copy"


class TestJSONOperations:
    """测试JSON文件操作函数。"""

    def test_atomic_write_json(self, temp_dir: Path):
        """测试原子性JSON写入。"""
        data = {"test": "value", "number": 42, "list": [1, 2, 3]}
        file_path = temp_dir / "test.json"

        atomic_write_json(data, file_path)

        # 验证文件已创建
        assert file_path.exists()

        # 验证内容正确
        with open(file_path) as f:
            loaded_data = json.load(f)
        assert loaded_data == data

    def test_atomic_write_json_custom_indent(self, temp_dir: Path):
        """测试自定义缩进的JSON写入。"""
        data = {"nested": {"key": "value"}}
        file_path = temp_dir / "test.json"

        atomic_write_json(data, file_path, indent=2)

        content = file_path.read_text()
        # 验证使用了2空格缩进
        assert "  \"nested\":" in content

    def test_load_json_file(self, temp_dir: Path):
        """测试JSON文件加载。"""
        data = {"test": "value", "number": 42}
        file_path = temp_dir / "test.json"

        # 先写入数据
        with open(file_path, "w") as f:
            json.dump(data, f)

        # 加载数据
        loaded_data = load_json_file(file_path)
        assert loaded_data == data

    def test_load_json_file_nonexistent(self, temp_dir: Path):
        """测试加载不存在的JSON文件。"""
        file_path = temp_dir / "nonexistent.json"

        with pytest.raises(ManifestError) as exc_info:
            load_json_file(file_path)

        assert "JSON file not found" in str(exc_info.value)
        assert exc_info.value.operation == "read"

    def test_load_json_file_invalid_format(self, temp_dir: Path):
        """测试加载无效格式的JSON文件。"""
        file_path = temp_dir / "invalid.json"
        file_path.write_text("invalid json content")

        with pytest.raises(ManifestError) as exc_info:
            load_json_file(file_path)

        assert "Invalid JSON format" in str(exc_info.value)
        assert exc_info.value.operation == "read"

    def test_atomic_write_json_cleanup_failure_does_not_mask_original_error(
        self, temp_dir: Path, mocker
    ):
        """
        测试原子性写入失败时，如果临时文件清理也失败，原始错误不会被屏蔽。
        """
        file_path = temp_dir / "test.json"
        # 使用一个不可JSON序列化的对象来触发写入错误
        data = {"key": object()}

        # Mock Path.unlink() 使其在被调用时抛出异常
        mocker.patch.object(Path, "unlink", side_effect=PermissionError("Cannot delete temp file"))

        # Mock logger以捕获警告信息
        mock_logger = mocker.patch("logwp.extras.tracking.utils._get_logger").return_value

        with pytest.raises(ManifestError) as exc_info:
            atomic_write_json(data, file_path)

        # 1. 验证抛出的异常是由原始的写入错误（TypeError）引起的
        assert isinstance(exc_info.value.__cause__, TypeError)
        assert "Object of type" in str(exc_info.value.__cause__)

        # 2. 验证记录了关于清理失败的警告日志
        mock_logger.warning.assert_called_once()
        log_args, log_kwargs = mock_logger.warning.call_args
        assert "Failed to clean up temporary file" in log_args[0]
        assert "cleanup_error" in log_kwargs
        assert "Cannot delete temp file" in str(log_kwargs["cleanup_error"])

    def test_calculate_file_hash_different_algorithms(self, sample_artifact_file: Path):
        """测试不同哈希算法的配置生效。"""
        # 测试 SHA256
        hash_sha256 = calculate_file_hash(sample_artifact_file, algorithm="sha256")
        assert len(hash_sha256) == 64

        # 测试 MD5
        hash_md5 = calculate_file_hash(sample_artifact_file, algorithm="md5")
        assert len(hash_md5) == 32

        # 测试 SHA1
        hash_sha1 = calculate_file_hash(sample_artifact_file, algorithm="sha1")
        assert len(hash_sha1) == 40

        # 验证不同算法产生不同的哈希值
        assert hash_sha256 != hash_md5
        assert hash_sha256 != hash_sha1
        assert hash_md5 != hash_sha1

    def test_calculate_file_hash_invalid_algorithm(self, sample_artifact_file: Path):
        """测试无效哈希算法。"""
        with pytest.raises(ArtifactIOError) as exc_info:
            calculate_file_hash(sample_artifact_file, algorithm="invalid_algorithm")

        assert "Failed to calculate file hash" in str(exc_info.value)
        assert "unsupported hash type" in str(exc_info.value)


class TestDictOperations:
    """测试字典操作函数。"""

    def test_deep_merge_dict_simple(self):
        """测试简单字典合并。"""
        base = {"a": 1, "b": 2}
        update = {"b": 3, "c": 4}

        result = deep_merge_dict(base, update)

        expected = {"a": 1, "b": 3, "c": 4}
        assert result == expected

    def test_deep_merge_dict_nested(self):
        """测试嵌套字典合并。"""
        base = {
            "a": 1,
            "nested": {
                "x": 10,
                "y": 20
            }
        }
        update = {
            "nested": {
                "y": 30,
                "z": 40
            },
            "b": 2
        }

        result = deep_merge_dict(base, update)

        expected = {
            "a": 1,
            "b": 2,
            "nested": {
                "x": 10,
                "y": 30,
                "z": 40
            }
        }
        assert result == expected

    def test_deep_merge_dict_type_override(self):
        """测试类型覆盖的字典合并。"""
        base = {"key": {"nested": "value"}}
        update = {"key": "simple_value"}

        result = deep_merge_dict(base, update)

        # 非字典类型应该直接覆盖
        expected = {"key": "simple_value"}
        assert result == expected

    def test_deep_merge_dict_empty(self):
        """测试空字典合并。"""
        base = {"a": 1}
        update = {}

        result = deep_merge_dict(base, update)
        assert result == base

        result = deep_merge_dict({}, base)
        assert result == base


class TestDirectoryOperations:
    """测试目录操作函数。"""

    def test_ensure_directory_new(self, temp_dir: Path):
        """测试创建新目录。"""
        new_dir = temp_dir / "new_directory"

        result = ensure_directory(new_dir)

        assert result == new_dir
        assert new_dir.exists()
        assert new_dir.is_dir()

    def test_ensure_directory_existing(self, temp_dir: Path):
        """测试确保已存在的目录。"""
        existing_dir = temp_dir / "existing"
        existing_dir.mkdir()

        result = ensure_directory(existing_dir)

        assert result == existing_dir
        assert existing_dir.exists()

    def test_ensure_directory_nested(self, temp_dir: Path):
        """测试创建嵌套目录。"""
        nested_dir = temp_dir / "level1" / "level2" / "level3"

        result = ensure_directory(nested_dir)

        assert result == nested_dir
        assert nested_dir.exists()
        assert nested_dir.is_dir()

    def test_ensure_directory_string_path(self, temp_dir: Path):
        """测试使用字符串路径创建目录。"""
        new_dir_str = str(temp_dir / "string_path")

        result = ensure_directory(new_dir_str)

        assert isinstance(result, Path)
        assert result.exists()
        assert result.is_dir()
