# OBMIQ 组件说明文档

**版本: 1.0**

## 1. 引言

### 1.1. 目标与定位

`scape.core.obmiq` 是一个标准化的、可重用的**多步骤机器学习组件**，其核心任务是精准量化油基泥浆侵入对储层的影响。具体而言，它通过分析测井数据，训练一个先进的深度学习模型，以预测`DT2_P50`和`DPHIT_NMR`这两个关键侵入指示因子的变化。

作为 SCAPE 框架的第一阶段（Stage-1），`obmiq` 的高质量输出是后续 `swift_pso` 渗透率计算准确性的关键基石。

### 1.2. 实现要点

`obmiq` 组件严格遵循《可追踪机器学习组件开发框架》，并体现了现代化的AI工程实践：

*   **先进的模型架构**: 采用基于 **PyTorch** 的**混合输入深度学习模型**，该模型包含一个用于处理T2谱序列的**1D-CNN分支**和一个用于处理常规测井曲线的**MLP分支**，最大化地利用了不同类型数据的独特信息。
*   **自动化的超参数寻优**: 集成 **Optuna** 库，在严格的**留一井交叉验证 (LOWO-CV)** 框架下自动搜索最佳模型超参数，确保了模型的泛化能力。
*   **端到端的可追踪性**: 深度集成 `logwp.extras.tracking` 的 `RunContext`，从参数配置、交叉验证过程到最终模型产物，所有环节均被精确追踪，保证了实验的完全可复现性。
*   **模型可解释性**: 内置了基于 **Captum** 的模型可解释性分析，能够生成特征重要性排序和注意力热力图（Saliency Map），为理解模型决策提供了有力工具。

---

## 2. 核心概念对照表

下表将《框架》中的核心概念与`obmiq`包中的具体实现一一对应：

| 框架概念 | `obmiq` 中的具体实现 |
| :--- | :--- |
| **多步骤包** | `scape.core.obmiq` 整个包 |
| **步骤 (Step)** | 1. OBMIQ 训练步骤 <br> 2. OBMIQ 预测步骤 |
| **门面 (Facade)** | 1. `training_facade.py` <br> 2. `prediction_facade.py` |
| **主执行函数** | 1. `run_obmiq_training_step()` <br> 2. `run_obmiq_prediction_step()` |
| **配置 (Config)** | 1. `config.ObmiqTrainingConfig` <br> 2. `config.ObmiqPredictionConfig` |
| **内部逻辑 (Internal)** | `internal/model_builder.py` <br> `internal/tuning_procedure.py` <br> `internal/final_training_procedure.py` <br> `internal/interpretability.py` |
| **产物常量** | 1. `constants.ObmiqTrainingArtifacts` <br> 2. `constants.ObmiqPredictionArtifacts` |
| **产物处理器** | `artifact_handler.ObmiqArtifactHandler` (服务于所有步骤) |
| **绘图复现** | `plotting.py` (包含多个`replot_*`函数) |
| **绘图配置常量** | `constants.ObmiqPlotProfiles` |
| **数据快照** | `LOWO_CV_PREDICTIONS_DATA`, `FINAL_MODEL_EVALUATION_DATA` 等 |

---

## 3. 组件架构与目录结构

`obmiq` 作为一个标准的多步骤包，其目录结构如下：

```
scape/core/obmiq/
├── __init__.py               # 导出所有步骤的公共API
├── README.md                 # 本文档
├── training_facade.py        # 【必须】训练步骤的门面
├── prediction_facade.py      # 【必须】预测步骤的门面
├── config.py                 # 【必须】包含所有步骤的Pydantic配置模型
├── constants.py              # 【推荐】包含所有步骤的产物和绘图配置常量
├── artifact_handler.py       # 【推荐】服务于所有步骤的无状态产物处理器
├── plotting.py               # (可选) 定义从数据快照复现图表的功能
├── plot_profiles.py          # (可选) 注册本模块专属的PlotProfile
└── internal/                 # 【必须】存放所有内部实现细节的目录
    ├── __init__.py
    ├── model_builder.py      # PyTorch模型构建 (OBMIQPyTorchModel, AdaptiveLossModule)
    ├── data_handler.py       # 数据预处理与PyTorch Dataset/DataLoader创建
    ├── tuning_procedure.py   # 超参数寻优规程 (Optuna + LOWO-CV)
    ├── final_training_procedure.py # 最终模型训练规程
    └── interpretability.py   # 模型可解释性计算 (Captum)
```

---

## 4. 开发细节与最佳实践

### 4.1. API 与使用

`obmiq`组件通过`training_facade`和`prediction_facade`提供服务。以下是一个典型的工作流示例，展示了如何按顺序调用训练和预测步骤。

```python
# 在一个工作流脚本中
from pathlib import Path
from logwp.extras.tracking import RunContext
from scape.core.obmiq import (
    run_obmiq_training_step,
    run_obmiq_prediction_step,
    ObmiqTrainingConfig,
    ObmiqPredictionConfig,
    ObmiqTrainingArtifacts,
    ObmiqArtifactHandler
)

# 假设 train_bundle, prediction_bundle, t2_time_array, well_no_name, depth_name 已加载

with RunContext(run_dir="path/to/output/run_xyz") as ctx:
    # --- 1. 训练步骤 ---
    print("--- 开始 OBMIQ 训练步骤 ---")
    training_config = ObmiqTrainingConfig(
        n_trials=200,
        final_train_epochs=300,
        patience=20
    )

    # 定义用于可解释性分析的样本点
    saliency_samples = [("c-1", 6311.73), ("t-1", 6471.0)]

    training_results = run_obmiq_training_step(
        config=training_config,
        ctx=ctx,
        train_bundle=train_bundle,
        sequence_feature="PHI_T2_DIST_CUM",
        normalization_feature="PHIT_NMR",
        tabular_features=["SWB_NMR", "BFV_NMR", "PHIE_NMR", ...],
        target_features=["DT2_P50", "DPHIT_NMR"],
        grouping_feature=well_no_name,
        depth_feature=depth_name,
        t2_time_axis=t2_time_array,
        saliency_samples=saliency_samples
    )

    # --- 2. 预测步骤 ---
    print("\\n--- 开始 OBMIQ 预测步骤 ---")
    handler = ObmiqArtifactHandler()
    model_assets_path = ctx.get_artifact_path(ObmiqTrainingArtifacts.MODEL_ASSETS.value)
    model_assets = handler.load_model_assets(model_assets_path)

    prediction_results = run_obmiq_prediction_step(
        config=ObmiqPredictionConfig(),
        ctx=ctx,
        model_assets=model_assets,
        prediction_bundle=prediction_bundle,
        source_t2_time_axis=t2_time_array,
        output_curve_names=("DT2_P50_PRED", "DPHIT_NMR_PRED")
    )
```

### 4.2. 配置 (`config.py`)

`obmiq`的行为主要由`ObmiqTrainingConfig`控制，它不定义超参数的搜索空间（由`Optuna`在代码中动态定义），而是控制整个训练和寻优流程的行为。

*   `n_trials`: `Optuna`超参数搜索的总试验次数。
*   `max_epochs_per_trial`: 在单次`Optuna`试验中，模型训练的最大轮次。
*   `final_train_epochs`: 最终模型训练的最大轮次。
*   `patience`: 早停机制的耐心轮次数。
*   `batch_size`: 训练和验证时使用的批处理大小。
*   `val_split_ratio`: 在每个LOWO-CV折内部，用于从训练集中划分出验证集的比例。

### 4.3. 产物清单 (Artifacts)

`obmiq`训练步骤会生成一套丰富的产物，全面记录模型从交叉验证、最终训练到可解释性分析的全过程。

#### 训练步骤产物 (`ObmiqTrainingArtifacts`)

*   **模型资产与配置**:
    *   `MODEL_ASSETS`: 核心产物，一个包含模型权重、超参数和预处理器的PyTorch模型资产包。
    *   `ONNX_MODEL`: 可用于跨平台部署的ONNX格式模型。
    *   `TRAINING_CONFIG`: 本次运行的配置快照。

*   **泛化能力评估 (基于LOWO-CV的盲测结果)**:
    *   `LOWO_CV_PERFORMANCE_SUMMARY`: **(表一)** **用途**: 定量评估模型的泛化能力和稳定性。**计算过程**: 在LOWO-CV的每一折中，计算模型对留出井的预测性能（RMSE, R²），最后汇总所有折的性能指标，计算其均值和标准差。
    *   `LOWO_CV_PREDICTIONS_DATA`: **(图二数据源)** **用途**: 为生成泛化能力交叉图提供数据基础。**计算过程**: 在LOWO-CV的每一折中，收集模型对留出井的“盲测”预测值和真实值，最终将所有井的盲测结果合并成一个DataFrame。
    *   `LOWO_CV_CROSSPLOT_*`: **(图二)** **用途**: 直观展示模型在未知数据上的预测表现。**计算过程**: 基于`LOWO_CV_PREDICTIONS_DATA`数据快照绘制的预测值-真实值交叉图。


*   **最终模型评估 (在全部训练集上的表现)**:
    *   `FINAL_MODEL_EVALUATION_DATA`: **用途**: 评估最终模型在整个训练集上的拟合能力，并为可解释性分析提供数据基础。**计算过程**: 使用最终训练好的模型，对全部训练数据进行一次预测，并计算预测值、真实值和残差。
    *   `EVAL_CROSSPLOT_*`: **用途**: 直观展示最终模型的拟合情况。**计算过程**: 基于`FINAL_MODEL_EVALUATION_DATA`绘制的交叉图。
    *   `EVAL_RESIDUALS_*`: **用途**: 诊断最终模型的预测偏差是否存在系统性模式。**计算过程**: 基于`FINAL_MODEL_EVALUATION_DATA`中的残差列绘制的残差图和直方图。


*   **训练过程监控**:
    *   `TUNING_REPORT`: **用途**: 记录超参数寻优的结果。**计算过程**: 在LOWO-CV的所有折中，对每个超参数组合的平均验证损失进行排序，找到全局最优的超参数组合。
    *   `CV_PERFORMANCE_REPORT`: **用途**: 详细记录每一折交叉验证的寻优过程。**计算过程**: 在LOWO-CV的每一折中，记录由`Optuna`找到的最佳验证损失及其对应的超参数。
    *   `FINAL_TRAINING_HISTORY_*`: **用途**: 监控最终模型的收敛过程。**计算过程**: 记录最终模型在训练过程中，每个轮次的训练损失和验证损失，并保存为CSV数据和PNG图表。
    *   `TENSORBOARD_LOGS`: **用途**: 提供详细的、交互式的训练过程诊断。**计算过程**: 在最终模型训练的每个轮次，将损失、RMSE、自适应损失权重(`log_vars`)等详细指标写入TensorBoard日志文件。


*   **模型可解释性 (Captum)**:
    *   `CAPTUM_TABULAR_ATTRIBUTIONS_DATA` & `CAPTUM_IG_SUMMARY_*`: **(类似SHAP)** **用途**: 解释表格化特征（如常规测井曲线）对模型预测的贡献度。**计算过程**: 使用Captum库的`IntegratedGradients`算法，计算每个表格特征对每个目标输出的归因值，然后将平均归因值绘制成条形图。
    *   `CAPTUM_SEQUENCE_ATTRIBUTIONS_DIR` & `CAPTUM_SALIENCY_EXAMPLES_DIR`: **(图三)** **用途**: 解释序列特征（T2谱）对模型预测的贡献度，即模型在做预测时“关注”了T2谱的哪个部分。**计算过程**: 使用Captum库的`Saliency`算法，计算T2谱上每个点对预测结果的梯度，生成“注意力热力图”，并与原始T2谱叠加显示。


#### 预测步骤产物 (`ObmiqPredictionArtifacts`)

*   `PREDICTIONS`: **核心产物**。这是一个CSV文件，包含了原始输入数据以及模型预测出的新曲线（如`DT2_P50_PRED`）。如果输入数据本身包含真实目标值，该文件还会包含计算出的残差列，并触发一系列评估图表的生成。


### 4.4. 核心内部逻辑

*   **混合模型 (`internal/model_builder.py`)**: 定义了`OBMIQPyTorchModel`，它包含并行的1D-CNN和MLP分支，以及一个`AdaptiveLossModule`来自适应地加权两个目标的损失。
    *   **MLP分支 (升级版)**: 为了学习更复杂的特征交互，MLP分支被设计为一个包含两个隐藏层的深度网络。每个隐藏层都采用了`Linear -> BatchNorm1d -> ReLU -> Dropout`的结构，以增强模型的表达能力、稳定训练过程并有效防止过拟合。
*   **数据处理 (`internal/data_handler.py`)**: 实现了`OBMIQDataset`和`create_dataloaders_for_fold`，确保在交叉验证的每一折中独立地拟合预处理器，防止数据泄露。
*   **超参数寻优 (`internal/tuning_procedure.py`)**: 实现了`run_hyperparameter_tuning_cv`，它编排了`LOWO-CV`和`Optuna`的嵌套循环，并增加了收集**盲测**结果的逻辑，为生成泛化能力评估产物提供了数据基础。
*   **最终训练 (`internal/final_training_procedure.py`)**: 实现了`train_final_model`，使用最佳超参数在全量数据上进行训练，并记录详细的监控指标到TensorBoard。
*   **可解释性 (`internal/interpretability.py`)**: 封装了与`Captum`库的交互，计算特征归因和显著性图。

### 4.5. 测试策略

`obmiq`组件的测试策略遵循框架规范，确保了代码的质量和稳定性。

*   **单元测试**:
    *   对`internal`中的核心模块（如`model_builder`, `data_handler`）进行严格的单元测试，验证其功能的正确性。
    *   对`facade`层进行测试时，**必须**使用`pytest.mock.MagicMock`来模拟`RunContext`和内部规程的调用，重点验证`facade`的流程编排和产物注册逻辑是否正确。

*   **集成测试**:
    *   编写一个完整的端到端测试用例，该用例创建一个指向临时目录的**真实`RunContext`**。
    *   测试脚本按顺序调用`run_obmiq_training_step`和`run_obmiq_prediction_step`。
    *   断言预测步骤能成功加载训练步骤产出的模型资产，并验证最终的预测结果符合预期。这个测试确保了两个`Step`之间通过`RunContext`进行的产物交接是正确无误的。

---

## 5. 总结

`scape.core.obmiq`是**可追踪机器学习组件开发框架**在PyTorch技术栈上的一个标杆实现。它通过清晰的架构、严谨的验证流程和丰富的可解释性产物，为解决复杂的地球物理问题提供了一个强大、可靠且易于维护的解决方案。

**关键要点回顾**:

1.  **混合模型，物尽其用**: 针对不同数据结构采用不同网络，最大化信息利用率。
2.  **泛化为先**: 以LOWO-CV为核心，所有关键评估指标均基于对未知数据的“盲测”结果。
3.  **追踪完备**: 从配置到最终产物，一切皆可追踪，保证实验的可复现性。
4.  **解释有力**: 内置Captum分析，不仅给出结果，更试图解释“为什么”。
5.  **分层清晰**: `facade`管流程，`internal`管算法，职责分明，易于维护。

---

**相关文档**:

-   `docs/SCAPE_方法说明书_V5_OBMIQ建模_Pytorch版.md`
-   `logwp/extras/tracking/机器学习组件开发框架.md`
