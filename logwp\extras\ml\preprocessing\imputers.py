"""
数据填充器，提供按井填充缺失值等高级功能。
"""
from __future__ import annotations

from typing import TYPE_CHECKING, List, Dict, Any
import pandas as pd

from .base import BasePreprocessor
from logwp.models.exceptions import WpDataError

if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpDataFrameBundle


class WellwiseImputer(BasePreprocessor):
    """
    按井对指定的测井曲线进行缺失值填充。

    这是一种比全局填充更稳健的方法，因为它使用每口井自身的统计特性
    来填充缺失值，避免了不同井之间系统性差异的干扰。

    Attributes:
        fill_values_ (Dict[str, Dict[str, Any]]):
            在 fit 过程中学习到的填充值。
            结构为: {well_name: {curve_name: fill_value}}
            例如: {'W1': {'GR': 75.2, 'PHIT': 0.18}}
    """

    def __init__(self, curves: List[str], strategy: str = 'mean', fill_value: Any = None):
        """
        初始化 WellwiseImputer。

        Args:
            curves (List[str]): 需要进行填充的曲线名称列表。
            strategy (str, optional): 填充策略。支持 'mean', 'median', 'most_frequent', 'constant'。
                                    默认为 'mean'。
            fill_value (Any, optional): 当 strategy='constant' 时使用的填充值。默认为 None。
        """
        supported_strategies = ['mean', 'median', 'most_frequent', 'constant']
        if strategy not in supported_strategies:
            raise ValueError(f"策略必须是 {supported_strategies} 中的一个")
        if strategy == 'constant' and fill_value is None:
            raise ValueError("当 strategy='constant' 时，必须提供 fill_value。")

        self.curves = curves
        self.strategy = strategy
        self.fill_value = fill_value
        self.fill_values_: Dict[str, Dict[str, Any]] = {}

    def fit(self, bundle: WpDataFrameBundle) -> "WellwiseImputer":
        """
        按井学习每条曲线的填充值。

        Args:
            bundle (WpDataFrameBundle): 包含多井数据的输入数据包。

        Returns:
            WellwiseImputer: 学习了参数的自身实例。
        """
        self.fill_values_ = {}
        all_wells_data = bundle.to_all_wells_data()

        for well_name, well_df in all_wells_data.items():
            self.fill_values_[well_name] = {}
            for curve in self.curves:
                if curve not in well_df.columns or well_df[curve].isna().all():
                    continue

                if self.strategy == 'mean':
                    self.fill_values_[well_name][curve] = well_df[curve].mean()
                elif self.strategy == 'median':
                    self.fill_values_[well_name][curve] = well_df[curve].median()
                elif self.strategy == 'most_frequent':
                    self.fill_values_[well_name][curve] = well_df[curve].mode().iloc[0]
                elif self.strategy == 'constant':
                    self.fill_values_[well_name][curve] = self.fill_value
        return self

    def transform(self, bundle: WpDataFrameBundle) -> WpDataFrameBundle:
        """
        使用学习到的参数，按井对数据进行缺失值填充。

        Args:
            bundle (WpDataFrameBundle): 需要转换的数据包。

        Returns:
            WpDataFrameBundle: 一个新的、包含了填充后数据的数据包。
        """
        if not self.fill_values_:
            raise RuntimeError("必须在 transform 之前调用 fit 方法。")

        transformed_df = bundle.data.copy()
        try:
            well_col = bundle.get_well_column_name()
        except WpDataError as e:
            raise ValueError("无法执行按井填充：无法从Bundle中确定井名列。") from e

        for curve in self.curves:
            if curve in transformed_df.columns:
                fill_map = {well: stats.get(curve) for well, stats in self.fill_values_.items() if stats.get(curve) is not None}
                fill_series = transformed_df[well_col].map(fill_map)
                transformed_df[curve] = transformed_df[curve].fillna(fill_series)

        return WpDataFrameBundle(
            name=f"{bundle.name}_imputed",
            data=transformed_df,
            curve_metadata=bundle.curve_metadata,
            curve_to_columns_map=bundle.curve_to_columns_map
        )
