{"cells": [{"cell_type": "code", "execution_count": 25, "id": "a9e0a2e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "1e86095f", "metadata": {}, "source": ["# SWIFT-PSO 案例\n", "- 固定随机种子\n", "- 新增`收敛轨迹与聚类分析图`以及各Cluster参数统计\n", "- 12个优化参数,t2lm_exp、vmacro_b、vmacro_min为固定\n", "- random_seed = 2000\n", "- t-SNE可视化最终收敛点聚类分析 (Cluster Analysis)聚类方法：kmeans"]}, {"cell_type": "markdown", "id": "4266395e", "metadata": {}, "source": ["## 1. 初始化与导入"]}, {"cell_type": "code", "execution_count": 26, "id": "3e9b170c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "# 导入 swift_pso 组件\n", "from scape.core.swift_pso import (\n", "    run_swift_pso_training_step,\n", "    run_swift_pso_prediction_step,\n", "    run_tsne_visualization_step,\n", "    SwiftPsoTrainingConfig,\n", "    SwiftPsoPredictionConfig,\n", "    TsneVisualConfig,\n", "    SwiftPsoTrainingArtifacts,\n", "    SwiftPsoPredictionArtifacts,\n", "    TsneVisualArtifacts,\n", "    TsnePlotProfiles\n", ")\n", "\n", "# 导入 validation 组件\n", "from scape.core.validation import (\n", "    run_plt_analysis_step,\n", "    run_perm_correlation_step,\n", "    PltAnalysisConfig,\n", "    PermCorrelationConfig,\n", "    PltAnalysisArtifacts,\n", "    PermCorrelationArtifacts,\n", "    PltPlotTypes,\n", "    PltAnalysisPlotProfiles,\n", "    PermCorrelationPlotProfiles\n", ")\n", "\n", "import scape.core.swift_pso.plot_profiles"]}, {"cell_type": "markdown", "id": "f519543e", "metadata": {}, "source": ["## 2. 实验设置与数据加载\n", "\n", "使用 `RunContext` 初始化一个实验，所有后续操作都将在此上下文中进行，确保所有产物和日志都保存在一个独立的运行目录中。"]}, {"cell_type": "code", "execution_count": 27, "id": "e446002f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:00:57.014401Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 877.45, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\swift_pso_run_20250729_140057 run_id=20250729-060057-0554043c\n", "实验运行已初始化，所有产物将保存至: F:\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\20_swift_pso\\case32\\output01\\swift_pso_run_20250729_140057\n", "2025-07-29T06:00:57.042697Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 877.45, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T06:00:57.081382Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 877.45, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx file_size_mb=0.16 sheet_count=1\n", "2025-07-29T06:00:57.098670Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 877.47, 'cpu_percent': 0.0} project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T06:00:57.107720Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 877.48, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T06:00:57.134444Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 877.67, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T06:00:57.162517Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 877.81, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=11 well_curves=1\n", "2025-07-29T06:00:57.573194Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} shape=(377, 74) sheet_name=swift_pso_train_cleaned\n", "2025-07-29T06:00:57.594084Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned')\n", "2025-07-29T06:00:57.613446Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} curve_count=11 dataset_name=swift_pso_train_cleaned dataset_type=Point df_shape=(377, 74) processing_time=0.492\n", "2025-07-29T06:00:57.641534Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:00:57.654772Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:00:57.667815Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=0.625 project_name=WpIdentifier('scape_swift_pso_train_cleaned')\n", "✅ 成功读取训练数据: ./scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T06:00:57.700654Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=11 input_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'DPHIT_NMR', 'PHIT_NMR', 'MD', 'T2LM', 'WELL_NO', 'PHI_T2_DIST', 'DT2_P50', 'T2_P50'] operation=extract_metadata output_curve_count=11 output_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'DPHIT_NMR', 'PHIT_NMR', 'MD', 'T2LM', 'WELL_NO', 'PHI_T2_DIST', 'DT2_P50', 'T2_P50']\n", "2025-07-29T06:00:57.721271Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T06:00:57.734243Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T06:00:57.761177Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T06:00:57.774747Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0}\n", "===train_bundle:   WELL_NO       MD  PHIT_NMR      T2LM   DT2_P50    T2_P50  DPHIT_NMR  \\\n", "0     C-1  6319.40  0.105551   785.428  0.316622   997.666   0.079668   \n", "1     C-1  6335.20  0.138267   746.054  0.111100   871.799   0.023800   \n", "2     C-1  6334.52  0.137828  3960.371  1.190176  6131.592   0.060766   \n", "3     C-1  6337.00  0.100931   527.925  0.102427   663.328   0.004202   \n", "4     C-1  6349.70  0.047791   111.798 -0.399350   203.148  -0.029715   \n", "\n", "     T2_VALUE_1    T2_VALUE_2    T2_VALUE_3  ...  T2_VALUE_58  T2_VALUE_59  \\\n", "0  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003882     0.003707   \n", "1  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.004239     0.003638   \n", "2  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.010259     0.012969   \n", "3  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003190     0.003105   \n", "4  1.506000e-05  3.392000e-05  6.404000e-05  ...     0.000000     0.000000   \n", "\n", "   T2_VALUE_60  T2_VALUE_61  T2_VALUE_62  T2_VALUE_63  T2_VALUE_64  K_LABEL  \\\n", "0     0.003506     0.003268     0.002990     0.002676     0.002339    0.888   \n", "1     0.003084     0.002577     0.002114     0.001696     0.001326    0.205   \n", "2     0.015308     0.017210     0.018582     0.019324     0.019364  153.000   \n", "3     0.003001     0.002867     0.002698     0.002490     0.002247    6.130   \n", "4     0.000000     0.000000     0.000000     0.000000     0.000000    0.263   \n", "\n", "   K_LABEL_TYPE  PZI  \n", "0          CORE    1  \n", "1           MDT    1  \n", "2          CORE    1  \n", "3          CORE    1  \n", "4          CORE    1  \n", "\n", "[5 rows x 74 columns]\n", "2025-07-29T06:00:57.800910Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO']\n", "===train_label_all_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T06:00:57.867539Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO']\n", "===train_label_pz_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T06:00:57.922194Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO']\n", "===train_label_core_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "2025-07-29T06:00:58.001981Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'WELL_NO']\n", "===train_label_core_pz_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "  - T2轴信息: T2AxisLog10(t2_start=0.1, t2_end=10000, n=64, unit='ms')\n"]}], "source": ["# --- 实验设置 ---\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"swift_pso_run\")\n", "run_context = RunContext(output_dir / run_dir_name,overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")\n", "\n", "# --- 加载训练数据 ---\n", "data_file_path = \"./scape_swift_pso_train_cleaned.wp.xlsx\"\n", "reader = WpExcelReader()\n", "train_project = reader.read(data_file_path)\n", "print(f\"✅ 成功读取训练数据: {data_file_path}\")\n", "\n", "train_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',\n", "        'PHI_T2_DIST', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_bundle: {train_bundle.data.head()}\")\n", "\n", "# 训练集岩心检验数据准备\n", "train_label_all_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_all_bundle: {train_label_all_bundle.data.head()}\")\n", "\n", "train_label_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_pz_bundle: {train_label_pz_bundle.data.head()}\")\n", "\n", "train_label_core_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE'\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_bundle: {train_label_core_bundle.data.head()}\")\n", "\n", "train_label_core_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE' and PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_pz_bundle: {train_label_core_pz_bundle.data.head()}\")\n", "\n", "# --- 准备数据依赖参数 ---\n", "t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "t2_time_array = t2_axis_info.calculate_values()\n", "print(f\"  - T2轴信息: {t2_axis_info}\")"]}, {"cell_type": "markdown", "id": "e5b87190", "metadata": {}, "source": ["## 3. 步骤一：SWIFT-PSO 训练\n", "\n", "调用 `run_swift_pso_training_step` 执行训练。我们首先使用 `SwiftPsoTrainingConfig.create_default()` 创建一个默认配置对象，然后将数据依赖的参数（如参考值、T2轴）注入其中。"]}, {"cell_type": "code", "execution_count": 28, "id": "76495147", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\n", "2025-07-29T06:00:58.121039Z [info     ] 开始SWIFT-PSO训练步骤                [scape.core.swift_pso.training_facade] backend=gpu bootstrap_iterations=20 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} operation=swift_pso_training_step run_id=20250729-060057-0554043c\n", "2025-07-29T06:00:58.147129Z [info     ] 成功替换Bundle中的曲线                 [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} curve_name=K_LABEL_TYPE new_data_type=INT new_shape=(377,) operation=replace_curve\n", "2025-07-29T06:00:58.161002Z [info     ] 必需曲线验证通过                       [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') checked_curves_count=9 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} operation=validate_required_curves\n", "2025-07-29T06:00:58.174355Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=gpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T06:00:58.202537Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.configs.training_config artifact_path=swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} description=本次训练步骤的完整Pydantic配置快照，确保可复现性。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:00:58.214373Z [info     ] 训练配置已保存为产物                     [scape.core.swift_pso.training_facade] config_path=output01\\swift_pso_run_20250729_140057\\swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0}\n", "2025-07-29T06:00:58.227722Z [info     ] 调用内部PSO优化器                     [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} operation=swift_pso_training_step\n", "2025-07-29T06:00:58.241014Z [info     ] 开始按井拆分DataFrame Bundle         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} operation=to_all_wells_data\n", "2025-07-29T06:00:58.257731Z [info     ] DataFrame Bundle按井拆分完成         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} operation=to_all_wells_data wells_found=['C-1', 'C-2']\n", "--- Bootstrap-<PERSON>de 1/20 ---\n", "2025-07-29T06:00:58.281059Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 75 代触发。\n", "2025-07-29T06:00:59.497205Z [info     ] PSO 优化完成。最终迭代次数: 75, 最优损失: 6.390044 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0}\n", "2025-07-29T06:00:59.513218Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:00:59.558980Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.46, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:00:59.584777Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.58, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(1e-08, 0.08579137999999999) v_meso_range=(0.00815435, 0.18337234999999993) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T06:00:59.616808Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.58, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T06:01:01.163126Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 7.150064 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.58, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:01.178997Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.58, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:01.214804Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.58, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:01.231497Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(6.999999999999999e-08, 0.07633129999999999) v_micro_range=(0.00457161, 0.10102376999999998)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 2/20 ---\n", "2025-07-29T06:01:01.255118Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 96 代触发。\n", "2025-07-29T06:01:02.621191Z [info     ] PSO 优化完成。最终迭代次数: 96, 最优损失: 6.709494 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:02.634331Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:02.666002Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:02.698449Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.08579139000000001) v_meso_range=(0.0014767899999999993, 0.17591793999999997) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T06:01:02.738057Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 73 代触发。\n", "2025-07-29T06:01:03.742704Z [info     ] PSO 优化完成。最终迭代次数: 73, 最优损失: 5.389160 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:03.758624Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:03.791801Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:03.824478Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.4e-07, 0.09115818999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 3/20 ---\n", "2025-07-29T06:01:03.855175Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 141 代触发。\n", "2025-07-29T06:01:05.889583Z [info     ] PSO 优化完成。最终迭代次数: 141, 最优损失: 5.967508 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:05.921040Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:05.952856Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:05.968722Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.0014767699999999994, 0.16761701999999998) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T06:01:06.000213Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 74 代触发。\n", "2025-07-29T06:01:07.145034Z [info     ] PSO 优化完成。最终迭代次数: 74, 最优损失: 4.135771 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:07.160801Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:07.192416Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:07.224287Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.4e-07, 0.09115818999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 4/20 ---\n", "2025-07-29T06:01:07.255964Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 91 代触发。\n", "2025-07-29T06:01:08.537629Z [info     ] PSO 优化完成。最终迭代次数: 91, 最优损失: 6.888717 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:08.552050Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:08.578734Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:08.611033Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.08544808999999999) v_meso_range=(0.00562029, 0.18011805999999994) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T06:01:08.644353Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 188 代触发。\n", "2025-07-29T06:01:11.139718Z [info     ] PSO 优化完成。最终迭代次数: 188, 最优损失: 8.188078 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:11.152970Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:11.192986Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:11.218091Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(8.999999999999999e-08, 0.0780848) v_micro_range=(0.00370749, 0.09978174999999999)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 5/20 ---\n", "2025-07-29T06:01:11.232913Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 121 代触发。\n", "2025-07-29T06:01:12.927615Z [info     ] PSO 优化完成。最终迭代次数: 121, 最优损失: 6.830977 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:12.940370Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:12.975680Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:12.993608Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285233, 0.17923820999999998) v_micro_range=(3.500000000000002e-07, 0.04422076000000001)\n", "2025-07-29T06:01:13.033351Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T06:01:14.741013Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 7.523342 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:14.754245Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:14.794364Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:14.820500Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.00393, 0.10102375999999999)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 6/20 ---\n", "2025-07-29T06:01:14.847597Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 162 代触发。\n", "2025-07-29T06:01:17.048465Z [info     ] PSO 优化完成。最终迭代次数: 162, 最优损失: 6.532766 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:17.071516Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:17.109526Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:17.128444Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(8.999999999999999e-08, 0.16021067999999997) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T06:01:17.168460Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 102 代触发。\n", "2025-07-29T06:01:18.675964Z [info     ] PSO 优化完成。最终迭代次数: 102, 最优损失: 4.577308 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:18.689023Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:18.726346Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:18.755790Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.4e-07, 0.09115818999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 7/20 ---\n", "2025-07-29T06:01:18.795544Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 207 代触发。\n", "2025-07-29T06:01:21.556904Z [info     ] PSO 优化完成。最终迭代次数: 207, 最优损失: 6.069865 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:21.582924Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:21.614991Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:21.636904Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285231, 0.17610280999999997) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T06:01:21.667356Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 108 代触发。\n", "2025-07-29T06:01:23.225841Z [info     ] PSO 优化完成。最终迭代次数: 108, 最优损失: 5.053191 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:23.237583Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:23.277617Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:23.312791Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.6000000000000003e-07, 0.09288630999999999) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 8/20 ---\n", "2025-07-29T06:01:23.340415Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 173 代触发。\n", "2025-07-29T06:01:25.913436Z [info     ] PSO 优化完成。最终迭代次数: 173, 最优损失: 5.735554 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:25.931913Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:25.958680Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:25.991694Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285229, 0.17392412) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T06:01:26.011739Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 74 代触发。\n", "2025-07-29T06:01:27.027233Z [info     ] PSO 优化完成。最终迭代次数: 74, 最优损失: 4.632611 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:27.038938Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:27.078990Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:27.107294Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.6, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.4e-07, 0.09115818999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 9/20 ---\n", "2025-07-29T06:01:27.131817Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.6, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 75 代触发。\n", "2025-07-29T06:01:28.252691Z [info     ] PSO 优化完成。最终迭代次数: 75, 最优损失: 7.100903 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.6, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:28.269620Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.6, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:28.292780Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.6, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:28.319905Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.08154969) v_meso_range=(0.01212452, 0.19095866999999997) v_micro_range=(3.300000000000002e-07, 0.042200610000000006)\n", "2025-07-29T06:01:28.346015Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 73 代触发。\n", "2025-07-29T06:01:29.475208Z [info     ] PSO 优化完成。最终迭代次数: 73, 最优损失: 5.413245 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:29.479927Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:29.517139Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:29.541503Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.4e-07, 0.09115818999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 10/20 ---\n", "2025-07-29T06:01:29.573221Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 70 代触发。\n", "2025-07-29T06:01:30.520382Z [info     ] PSO 优化完成。最终迭代次数: 70, 最优损失: 6.740289 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:30.550570Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:30.585986Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:30.608453Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.08579139000000001) v_meso_range=(0.00635756, 0.18938357999999997) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T06:01:30.653965Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 114 代触发。\n", "2025-07-29T06:01:32.160889Z [info     ] PSO 优化完成。最终迭代次数: 114, 最优损失: 4.222773 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:32.183318Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:32.214138Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:32.240916Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(9.999999999999998e-08, 0.08015218999999998) v_micro_range=(0.0008690600000000001, 0.09878561)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11/20 ---\n", "2025-07-29T06:01:32.275636Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 181 代触发。\n", "2025-07-29T06:01:34.725889Z [info     ] PSO 优化完成。最终迭代次数: 181, 最优损失: 6.942780 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:34.735241Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:34.775064Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:34.788431Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285231, 0.17610280999999997) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T06:01:34.815136Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 78 代触发。\n", "2025-07-29T06:01:35.924059Z [info     ] PSO 优化完成。最终迭代次数: 78, 最优损失: 4.645434 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:35.935637Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:35.975661Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:36.007757Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.04208165) v_meso_range=(2.1000000000000008e-07, 0.11335440000000001) v_micro_range=(3.300000000000002e-07, 0.09200621000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 12/20 ---\n", "2025-07-29T06:01:36.041824Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 70 代触发。\n", "2025-07-29T06:01:36.989332Z [info     ] PSO 优化完成。最终迭代次数: 70, 最优损失: 6.795838 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:37.014019Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:37.045469Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:37.069221Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.0857914) v_meso_range=(0.00455383, 0.17864539999999995) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T06:01:37.102937Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 125 代触发。\n", "2025-07-29T06:01:38.887157Z [info     ] PSO 优化完成。最终迭代次数: 125, 最优损失: 7.118107 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:38.896872Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:38.936856Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:38.966330Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.6000000000000003e-07, 0.09288630999999999) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 13/20 ---\n", "2025-07-29T06:01:38.989944Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 101 代触发。\n", "2025-07-29T06:01:40.375143Z [info     ] PSO 优化完成。最终迭代次数: 101, 最优损失: 6.797258 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:40.390589Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:40.423479Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:40.443491Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.08579139000000001) v_meso_range=(0.0014767899999999993, 0.17591793999999997) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T06:01:40.470207Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 73 代触发。\n", "2025-07-29T06:01:41.551076Z [info     ] PSO 优化完成。最终迭代次数: 73, 最优损失: 5.339221 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:41.574482Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:41.605770Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:41.617511Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.2e-07, 0.08808926999999998) v_micro_range=(4.0000000000000025e-07, 0.09835938999999999)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 14/20 ---\n", "2025-07-29T06:01:41.663086Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 204 代触发。\n", "2025-07-29T06:01:44.512148Z [info     ] PSO 优化完成。最终迭代次数: 204, 最优损失: 5.523681 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:44.536400Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:44.570098Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:44.592133Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285234, 0.18108265999999998) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T06:01:44.631270Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 73 代触发。\n", "2025-07-29T06:01:45.632580Z [info     ] PSO 优化完成。最终迭代次数: 73, 最优损失: 5.149456 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:45.654340Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:45.688485Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:45.726338Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.0999999999999998e-07, 0.08015219999999999) v_micro_range=(4.0000000000000025e-07, 0.09835938999999999)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 15/20 ---\n", "2025-07-29T06:01:45.752319Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 194 代触发。\n", "2025-07-29T06:01:48.400296Z [info     ] PSO 优化完成。最终迭代次数: 194, 最优损失: 7.096612 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:48.419247Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:48.445925Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:48.473522Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285228, 0.17181711999999996) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T06:01:48.500805Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T06:01:50.210306Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 7.317685 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:50.233441Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:50.269001Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:50.287644Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(9.999999999999998e-08, 0.08015218999999998) v_micro_range=(0.0008690600000000001, 0.09878561)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 16/20 ---\n", "2025-07-29T06:01:50.327321Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 184 代触发。\n", "2025-07-29T06:01:52.817737Z [info     ] PSO 优化完成。最终迭代次数: 184, 最优损失: 5.938601 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:52.838101Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:52.869031Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:52.888554Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285229, 0.17392412) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T06:01:52.918264Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 91 代触发。\n", "2025-07-29T06:01:54.249181Z [info     ] PSO 优化完成。最终迭代次数: 91, 最优损失: 4.590591 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:54.275432Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:54.301863Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:54.328175Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(9.999999999999998e-08, 0.08015218999999998) v_micro_range=(0.0008690600000000001, 0.09878561)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 17/20 ---\n", "2025-07-29T06:01:54.355812Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 95 代触发。\n", "2025-07-29T06:01:55.729750Z [info     ] PSO 优化完成。最终迭代次数: 95, 最优损失: 5.420373 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:55.754429Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:55.791213Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:55.820856Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.08579139000000001) v_meso_range=(0.00635756, 0.18938357999999997) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T06:01:55.849807Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 74 代触发。\n", "2025-07-29T06:01:56.836914Z [info     ] PSO 优化完成。最终迭代次数: 74, 最优损失: 4.741187 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:56.857850Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:56.889868Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:56.912246Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.03535313) v_meso_range=(2.200000000000001e-07, 0.11725652) v_micro_range=(3.300000000000002e-07, 0.09200621000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 18/20 ---\n", "2025-07-29T06:01:56.933582Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 91 代触发。\n", "2025-07-29T06:01:58.209708Z [info     ] PSO 优化完成。最终迭代次数: 91, 最优损失: 5.994164 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:58.231193Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:58.264382Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:01:58.290536Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.08544808999999999) v_meso_range=(0.00986743, 0.18337235999999993) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T06:01:58.317432Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 121 代触发。\n", "2025-07-29T06:01:59.971338Z [info     ] PSO 优化完成。最终迭代次数: 121, 最优损失: 7.760371 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:01:59.998130Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:00.038110Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:00.069200Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.8000000000000005e-07, 0.08969831) v_micro_range=(3.300000000000002e-07, 0.09200621000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 19/20 ---\n", "2025-07-29T06:02:00.090967Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 232 代触发。\n", "2025-07-29T06:02:03.268421Z [info     ] PSO 优化完成。最终迭代次数: 232, 最优损失: 6.080583 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:03.288760Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:03.319267Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:03.360607Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(7.999999999999999e-08, 0.14883592999999998) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T06:02:03.385881Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 187 代触发。\n", "2025-07-29T06:02:05.830557Z [info     ] PSO 优化完成。最终迭代次数: 187, 最优损失: 7.316604 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:05.840165Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:05.879285Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:05.906246Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.4e-07, 0.08560667999999999) v_micro_range=(3.700000000000002e-07, 0.09835936)\n", "--- Boots<PERSON><PERSON>-<PERSON><PERSON> 20/20 ---\n", "2025-07-29T06:02:05.933401Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 90 代触发。\n", "2025-07-29T06:02:07.176036Z [info     ] PSO 优化完成。最终迭代次数: 90, 最优损失: 6.091694 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:07.200636Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:07.232158Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:07.257497Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(8.999999999999999e-08, 0.16021067999999997) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T06:02:07.287388Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 167 代触发。\n", "2025-07-29T06:02:09.548210Z [info     ] PSO 优化完成。最终迭代次数: 167, 最优损失: 7.696652 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:09.572416Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:09.613821Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:09.640513Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.66, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.3e-07, 0.08431650999999998) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T06:02:09.685096Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.68, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 31 代触发。\n", "2025-07-29T06:02:10.015054Z [info     ] PSO 优化完成。最终迭代次数: 31, 最优损失: 6.269599 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.77, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:10.041625Z [info     ] Fine-Tuning阶段完成，最终损失: 6.269599 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.77, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:10.054989Z [info     ] SWIFT-PSO 优化完成。                [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.77, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:10.071927Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.models.final_parameters artifact_path=swift_pso_training\\final_parameters.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 879.77, 'cpu_percent': 0.0} description=最终优化后的模型参数及上下文，可直接用于预测步骤。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:10.108424Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.datasets.all_parameters_from_lowo artifact_path=swift_pso_training\\all_parameters_from_lowo.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.07, 'cpu_percent': 0.0} description=所有Bootstrap+LOWO迭代产生的优化参数集，用于t-SNE可视化分析。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:10.135304Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.convergence_history_finetune artifact_path=swift_pso_training\\convergence_history_finetune.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.07, 'cpu_percent': 0.0} description=Fine-Tuning阶段的损失函数收敛历史。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:10.166202Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.bootstrap_summary artifact_path=swift_pso_training\\summary_bootstrap_mu_rmse.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.07, 'cpu_percent': 0.0} description=每轮Bootstrap的平均验证误差（mu_rmse），用于确定最佳轮次b*。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:10.187879Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.params_warm_start artifact_path=swift_pso_training\\params_warm_start.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.07, 'cpu_percent': 0.0} description=用于启动Fine-Tuning阶段的热启动参数（theta_hat_star）。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:10.207256Z [info     ] SWIFT-PSO训练步骤完成                [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.07, 'cpu_percent': 0.0} final_loss=6.269598623287391 operation=swift_pso_training_step\n", "✅ [Step 1/5] 训练完成！ 结果: {'status': 'completed', 'final_loss': 6.269598623287391}\n", "   - 最终模型参数已保存为产物: swift_pso_training.models.final_parameters\n", "   - t-SNE源数据已保存为产物: swift_pso_training.datasets.all_parameters_from_lowo\n"]}], "source": ["# 1. 使用 Pydantic 模型创建配置\n", "training_config = SwiftPsoTrainingConfig.create_default()\n", "\n", "training_config.enable_fold_diagnostics = True\n", "training_config.optimization_params=[\n", "            'log10_KSDR_A', 'PHIT_EXP', 'RHO_NMR', 'log10_KMACRO_A',\n", "            'log10_T2cutoff_short', 'log10_T2cutoff_long',\n", "            'beta_1', 'beta_2', 'delta_MDT'\n", "        ]\n", "\n", "training_config.fixed_params['T2LM_EXP'] = 2.0\n", "training_config.fixed_params['KMACRO_B'] = 2.0\n", "training_config.fixed_params['Vmacro_min'] = 0.01\n", "\n", "# 2. 注入数据依赖参数\n", "t2_p50_ref = train_bundle.data['T2_P50'].median()\n", "phit_nmr_ref = train_bundle.data['PHIT_NMR'].median()\n", "\n", "training_config.random_seed = 2000\n", "training_config.pso_config_lowo['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_lowo['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_lowo['t2_time'] = t2_time_array\n", "training_config.pso_config_lowo['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_lowo['t2_range_min'] = 0.1\n", "training_config.pso_config_lowo['t2_range_max'] = 8000\n", "\n", "training_config.pso_config_finetune['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_finetune['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_finetune['t2_time'] = t2_time_array\n", "training_config.pso_config_finetune['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_finetune['t2_range_min'] = 0.1\n", "training_config.pso_config_finetune['t2_range_max'] = 8000\n", "\n", "# 3. 调整执行参数\n", "training_config.bootstrap_iterations = 20\n", "training_config.pso_config_lowo[\"max_iterations\"] = 400\n", "training_config.pso_config_lowo[\"n_particles\"] = 150\n", "training_config.pso_config_finetune[\"max_iterations\"] = 200\n", "training_config.pso_config_finetune[\"n_particles\"] = 100\n", "\n", "# 4. 执行训练步骤\n", "print(\"🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\")\n", "training_result = run_swift_pso_training_step(\n", "    config=training_config,\n", "    ctx=run_context, # 直接传递上下文对象\n", "    train_bundle=train_bundle,\n", "    backend='gpu'  # 执行层参数直接传入\n", ")\n", "\n", "print(f\"✅ [Step 1/5] 训练完成！ 结果: {training_result}\")\n", "print(f\"   - 最终模型参数已保存为产物: {SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value}\")\n", "print(f\"   - t-SNE源数据已保存为产物: {SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value}\")"]}, {"cell_type": "markdown", "id": "b1c2d3e4", "metadata": {}, "source": ["## 4. 步骤二：模型预测\n", "\n", "使用 `run_swift_pso_prediction_step` 对训练集和应用集进行预测。我们首先从 `RunContext` 中显式加载上一步训练产出的模型，然后将其作为参数传入预测函数。"]}, {"cell_type": "code", "execution_count": 29, "id": "f5g6h7i8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:02:10.295426Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.08, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T06:02:10.325643Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.08, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx file_size_mb=1.87 sheet_count=1\n", "2025-07-29T06:02:10.345614Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.08, 'cpu_percent': 0.0} project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T06:02:10.361549Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.08, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T06:02:10.374929Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.09, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T06:02:10.401398Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 880.11, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=8 well_curves=1\n", "2025-07-29T06:02:14.883695Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 629.39, 'cpu_percent': 0.0} shape=(4689, 71) sheet_name=swift_pso_apply_cleaned\n", "2025-07-29T06:02:14.942497Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 629.39, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned')\n", "2025-07-29T06:02:14.963580Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 629.39, 'cpu_percent': 0.0} curve_count=8 dataset_name=swift_pso_apply_cleaned dataset_type=Continuous df_shape=(4689, 71) processing_time=4.589\n", "2025-07-29T06:02:14.990134Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 629.39, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:02:15.009237Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 629.39, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:02:15.016781Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 629.39, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=4.721 project_name=WpIdentifier('scape_swift_pso_apply_cleaned')\n", "✅ 成功读取应用数据: ./scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T06:02:15.056807Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.91, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=8 input_curves=['DPHIT_NMR', 'PHIT_NMR', 'MD', 'T2LM', 'WELL_NO', 'PHI_T2_DIST', 'DT2_P50', 'T2_P50'] operation=extract_metadata output_curve_count=8 output_curves=['DPHIT_NMR', 'PHIT_NMR', 'MD', 'T2LM', 'WELL_NO', 'PHI_T2_DIST', 'DT2_P50', 'T2_P50']\n", "2025-07-29T06:02:15.083562Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.91, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T06:02:15.107063Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.91, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T06:02:15.117027Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.91, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T06:02:15.136911Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.91, 'cpu_percent': 0.0}\n"]}], "source": ["# --- 加载应用数据 ---\n", "apply_data_path = \"./scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "apply_project = reader.read(apply_data_path)\n", "print(f\"✅ 成功读取应用数据: {apply_data_path}\")\n", "\n", "apply_bundle = apply_project.get_dataset(\"swift_pso_apply_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR', 'PHI_T2_DIST'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "\n", "# --- 准备预测配置 (输出曲线名将作为facade函数的直接参数传入) ---\n", "prediction_config = SwiftPsoPredictionConfig.create_default()"]}, {"cell_type": "code", "execution_count": 30, "id": "j9k0l1m2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 2.1/5] 开始对训练集进行预测...\n", "\n", "================================================================================\n", "                            SWIFT-PSO 模型产物摘要\n", "================================================================================\n", "\n", "--- 优化参数 (Optimized Parameters) ---\n", "                 参数名      数值     线性域数值\n", "        log10_KSDR_A -1.8842    0.0131\n", "            PHIT_EXP  4.7886       N/A\n", "             RHO_NMR  4.9507       N/A\n", "      log10_KMACRO_A -1.2732    0.0533\n", "log10_T2cutoff_short  2.1928  155.8955\n", " log10_T2cutoff_long  3.0255 1060.5950\n", "              beta_1  0.1356       N/A\n", "              beta_2  0.6031       N/A\n", "           delta_MDT -0.4970       N/A\n", "\n", "--- 固定参数 (Fixed Parameters) ---\n", "       参数名     数值 线性域数值\n", "  T2LM_EXP 2.0000   N/A\n", "  KMACRO_B 2.0000   N/A\n", "Vmacro_min 0.0100   N/A\n", "\n", "--- 上下文 (Context) ---\n", "  - t2_p50_ref: 303.4830\n", "  - phit_nmr_ref: 0.0672\n", "\n", "================================================================================\n", "2025-07-29T06:02:15.244178Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.96, 'cpu_percent': 0.0} data_rows=377 operation=swift_pso_prediction_step run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:15.270025Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.96, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T06:02:15.283510Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.96, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T06:02:15.296970Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.96, 'cpu_percent': 0.0} data_rows=377 operation=swift_pso_prediction\n", "2025-07-29T06:02:15.313222Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.04, 'cpu_percent': 0.0} n_bins=64 n_depths=377 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.12447955000000001) v_meso_range=(9.999999999999998e-08, 0.16761701999999998) v_micro_range=(0.00038958999999999993, 0.09878561)\n", "2025-07-29T06:02:15.349984Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.04, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(2.3385145836888782e-08, 46.84669814619756) result_rows=377\n", "2025-07-29T06:02:15.363342Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.04, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(377,) operation=add_1d_curve\n", "2025-07-29T06:02:15.376556Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.04, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(377,) operation=add_1d_curve\n", "2025-07-29T06:02:15.407987Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(377,) operation=add_1d_curve\n", "2025-07-29T06:02:15.430094Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(377,) operation=add_1d_curve\n", "2025-07-29T06:02:15.443546Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T06:02:15.483837Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:15.501733Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=377\n", "✅ 训练集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n", "🚀 [Step 2.2/5] 开始对应用集进行预测...\n", "2025-07-29T06:02:15.516674Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction_step run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:15.537142Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T06:02:15.550063Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T06:02:15.563293Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.05, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction\n", "2025-07-29T06:02:15.589996Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 635.36, 'cpu_percent': 0.0} n_bins=64 n_depths=4689 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.12447955000000001) v_meso_range=(9.999999999999998e-08, 0.16893248) v_micro_range=(4.1000000000000026e-07, 0.2788430899999999)\n", "2025-07-29T06:02:15.629567Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(3.930231244165812e-12, 20.473737472212086) result_rows=4689\n", "2025-07-29T06:02:15.643306Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T06:02:15.670091Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T06:02:15.697035Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T06:02:15.723341Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T06:02:15.736872Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T06:02:15.790343Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:15.822266Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=4689\n", "2025-07-29T06:02:15.843818Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} copy_data=False new_dataset_name=train_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T06:02:15.857154Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T06:02:15.882977Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=train_apply dataset_type=Point operation=dataset_initialization\n", "2025-07-29T06:02:15.897280Z [info     ] 成功添加 'train_apply' (WpDiscreteDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T06:02:15.910588Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} copy_data=False new_dataset_name=pred_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T06:02:15.949617Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:02:15.969662Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=pred_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T06:02:15.987121Z [info     ] 成功添加 'pred_apply' (WpContinuousDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T06:02:16.003953Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} dataset_count=2 file_path=output01\\swift_pso_run_20250729_140057\\swift_pso_apply_result.wp.xlsx project_name=WpIdentifier('swift_pso_apply_result') save_head_info=True save_well_map=True\n", "2025-07-29T06:02:16.030196Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 633.06, 'cpu_percent': 0.0} curve_count=15 dataset_name=WpIdentifier('train_apply') dataset_type=Point df_shape=(377, 78)\n", "2025-07-29T06:02:16.164018Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 634.88, 'cpu_percent': 0.0} dataset_name=WpIdentifier('train_apply') processing_time=0.134\n", "2025-07-29T06:02:16.195169Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 634.88, 'cpu_percent': 0.0} curve_count=12 dataset_name=WpIdentifier('pred_apply') dataset_type=Continuous df_shape=(4689, 75)\n", "2025-07-29T06:02:17.778090Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 620.36, 'cpu_percent': 0.0} dataset_name=WpIdentifier('pred_apply') processing_time=1.583\n", "2025-07-29T06:02:17.791280Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 617.72, 'cpu_percent': 0.0} dataset_count=2 head_info=False total_sheets=2 well_map=False\n", "2025-07-29T06:02:17.830151Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 617.73, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:17.841297Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 617.73, 'cpu_percent': 0.0} workbook_sheets=2\n", "2025-07-29T06:02:49.849648Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 655.68, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:49.870062Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 655.7, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:56.142939Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 658.92, 'cpu_percent': 0.0} file_path=output01\\swift_pso_run_20250729_140057\\swift_pso_apply_result.wp.xlsx processing_time=40.139 project_name=WpIdentifier('swift_pso_apply_result')\n", "✅ 应用集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n"]}], "source": ["# --- 对训练集进行预测 ---\n", "print(\"🚀 [Step 2.1/5] 开始对训练集进行预测...\")\n", "# 1. 实例化产物处理器\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "# 2. 从RunContext加载上一步训练产出的模型参数\n", "model_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.FINAL_PARAMETERS)\n", "model_assets = handler.load_parameters(model_params_path)\n", "handler.print_model_assets_human_readable(model_assets)\n", "\n", "# 3. 调用预测步骤，并传入加载的模型\n", "train_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets,  # 传入加载的模型\n", "    prediction_bundle=train_bundle, # 使用训练数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu' # 预测通常在CPU上进行\n", ")\n", "\n", "# 获取带预测结果的bundle，以备后续步骤使用\n", "train_bundle_with_pred = train_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 训练集预测完成！新增曲线: {train_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")\n", "\n", "# --- 对应用集进行预测 ---\n", "print(\"🚀 [Step 2.2/5] 开始对应用集进行预测...\")\n", "# 模型参数只需加载一次，可复用\n", "apply_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets, # 传入加载的模型\n", "    prediction_bundle=apply_bundle, # 使用应用数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu'\n", ")\n", "\n", "temp_project = WpWellProject(name=\"swift_pso_apply_result\")\n", "temp_project.add_dataframe_bundle(\"train_apply\",train_bundle)\n", "temp_project.add_dataframe_bundle(\"pred_apply\",apply_bundle)\n", "\n", "writer = WpExcelWriter()\n", "apply_result_path = output_dir / run_dir_name / \"swift_pso_apply_result.wp.xlsx\"\n", "writer.write(temp_project, apply_result_path, apply_formatting=True)\n", "\n", "apply_bundle_with_pred = apply_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 应用集预测完成！新增曲线: {apply_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")"]}, {"cell_type": "markdown", "id": "1af960c4", "metadata": {}, "source": ["## 训练集上渗透率交会图"]}, {"cell_type": "code", "execution_count": 31, "id": "e07ee165", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:02:56.248542Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} step_name=train_all_perm_corr_analysis\n", "2025-07-29T06:02:56.273321Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0}\n", "2025-07-29T06:02:56.289438Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:02:56.316609Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:56.349581Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:02:56.362457Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:56.390900Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:56.427651Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:02:56.450772Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_140057\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.41, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:02:58.384071Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T06:02:58.418470Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:58.447985Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:02:58.477728Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:58.499537Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:02:58.521041Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:58.548354Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:02:58.583378Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:02:58.608290Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_140057\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 572.81, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:00.180778Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T06:03:00.220584Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:00.247164Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0}\n", "  - TRAIN_ALL 模型验证完成。\n", "\n", "2025-07-29T06:03:00.260494Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} step_name=train_pz_perm_corr_analysis\n", "2025-07-29T06:03:00.273384Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:00.273384Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:03:00.300201Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:00.327159Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:00.340480Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:00.367214Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:00.419759Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:00.433575Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_140057\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 577.48, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:01.914816Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T06:03:01.954581Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:01.981194Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:03:02.015653Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:02.040086Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:02.056249Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:02.080883Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:02.114676Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:02.135183Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_140057\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 579.7, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:03.675203Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T06:03:03.703297Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:03.732035Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0}\n", "  - TRAIN_PZ 模型验证完成。\n", "\n", "2025-07-29T06:03:03.754286Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} step_name=train_core_perm_corr_analysis\n", "2025-07-29T06:03:03.772189Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:03.782017Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:03:03.803984Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:03.826540Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:03.843448Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:03.870257Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:03.905578Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:03.928773Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_140057\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 585.16, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:05.581338Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T06:03:05.620702Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:05.646236Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:03:05.675433Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:05.696903Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:05.719548Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:05.742489Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:05.768885Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:05.789716Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_140057\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 582.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:07.290041Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T06:03:07.329938Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:07.369880Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE 模型验证完成。\n", "\n", "2025-07-29T06:03:07.385726Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} step_name=train_core_pz_perm_corr_analysis\n", "2025-07-29T06:03:07.396279Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:07.408962Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:03:07.433007Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:07.449597Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:07.471609Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:07.503826Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.79, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:07.529897Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.78, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:07.556607Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_140057\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 584.78, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:09.037432Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T06:03:09.077429Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:09.109460Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:03:09.130573Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:09.157261Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:09.170556Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:09.196982Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:09.250638Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:09.276337Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_140057\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 590.87, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:10.798021Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 596.45, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_140057\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T06:03:10.831790Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 596.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:10.859098Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 596.45, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE_PZ 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"train_all\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.4214668192690991,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 22.9% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.6% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6101423736491004,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 13.9% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 28.2% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.40927675518472584,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 38.8% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 71.4% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6509197031424623,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 22.2% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 50.6% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.38266568248859223,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 24.8% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.8% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6223800583450879,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 12.6% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 32.4% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.38266568248859223,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 24.8% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 44.8% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6223800583450879,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 12.6% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 32.4% <= 90%\\u3002\"\n", "    }\n", "  }\n", "}\n"]}], "source": ["models_to_perm_corr = {\n", "    \"train_all\": train_label_all_bundle,\n", "    \"train_pz\": train_label_pz_bundle,\n", "    \"train_core\": train_label_core_bundle,\n", "    \"train_core_pz\": train_label_core_bundle,\n", "}\n", "\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_results = {}\n", "for model_prefix, label_bundle in models_to_perm_corr.items():\n", "    perm_corr_config = PermCorrelationConfig()\n", "    perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": f\"Core Perm vs. Predicted Perm ({model_prefix.upper()})\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", "    )\n", "    perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=train_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=label_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=model_prefix\n", "    )\n", "    perm_corr_results[model_prefix] = perm_corr_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(perm_corr_results, indent=2))"]}, {"cell_type": "markdown", "id": "n3o4p5q6", "metadata": {}, "source": ["## 5. 步骤三：t-SNE 可视化\n", "\n", "调用 `run_tsne_visualization_step` 对训练过程中的参数演化进行可视化。此步骤会自动消费训练步骤产出的 `ALL_OPTIMIZED_PARAMETERS` 产物。\n", "\n", "我们采用 **Get -> Modify -> Pass** 模式来定制图表：\n", "1.  **Get**: 从全局注册表 `plot_registry` 获取一个默认的 `PlotProfile` 模板。\n", "2.  **Modify**: 在运行时动态修改模板的属性（如标题）。\n", "3.  **Pass**: 将修改后的 `PlotProfile` 对象传入 `facade` 函数。"]}, {"cell_type": "code", "execution_count": 32, "id": "r7s8t9u0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 3/5] 开始执行 t-SNE 可视化...\n", "2025-07-29T06:03:10.957898Z [info     ] 开始t-SNE可视化步骤                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 597.36, 'cpu_percent': 0.0} custom_profiles=['swift_pso.tsne_convergence', 'swift_pso.tsne_cluster_analysis'] operation=tsne_visualization_step run_id=20250729-060057-0554043c source_data_rows=504\n", "2025-07-29T06:03:10.989387Z [info     ] 开始执行收敛轨迹分析                     [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 597.36, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T06:03:10.997921Z [info     ] 开始t-SNE降维计算                    [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 597.36, 'cpu_percent': 0.0} data_rows=504 operation=tsne_computation perplexity=15\n", "2025-07-29T06:03:11.032093Z [info     ] 执行t-SNE降维                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 597.37, 'cpu_percent': 0.0} init=pca learning_rate=200.0 max_iter=2000 n_components=2 operation=tsne_computation perplexity=15 random_state=42 verbose=0\n", "2025-07-29T06:03:12.758083Z [info     ] 在原始高维空间上执行K-means聚类            [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 597.59, 'cpu_percent': 0.0} n_clusters=4 operation=tsne_computation\n", "2025-07-29T06:03:12.798025Z [info     ] 聚类轮廓系数 (Silhouette Score): 0.2065 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 597.61, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:12.811819Z [info     ] t-SNE降维和聚类计算完成                 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 597.61, 'cpu_percent': 0.0} operation=tsne_computation result_rows=504 tsne_x_range=(-1057.15283203125, 1072.8553466796875) tsne_y_range=(-845.835205078125, 893.7765502929688)\n", "2025-07-29T06:03:12.851734Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 598.16, 'cpu_percent': 0.0} description=t-SNE收敛轨迹的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:12.885682Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 598.16, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization\\tsne_convergence_trajectory_profile.json profile_name=swift_pso.tsne_convergence\n", "2025-07-29T06:03:12.901892Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 598.16, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization\\tsne_convergence_trajectory_plot.png profile_name=swift_pso.tsne_convergence snapshot_path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization\\tsne_convergence_trajectory_data.csv\n", "2025-07-29T06:03:12.938488Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 598.36, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_convergence\n", "2025-07-29T06:03:13.012092Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_convergence_trajectory_plot base_path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 598.36, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:14.513496Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.73, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T06:03:14.546613Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.73, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T06:03:14.591210Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.73, 'cpu_percent': 0.0} description=SWIFT-PSO参数演化轨迹的t-SNE可视化图表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:14.621905Z [info     ] 开始执行最终收敛点聚类分析                  [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.73, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T06:03:14.641895Z [info     ] 开始最终收敛点的聚类分析                   [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.73, 'cpu_percent': 0.0} operation=cluster_analysis_computation total_points=504\n", "2025-07-29T06:03:14.666113Z [info     ] 筛选出 40 个最终收敛点进行分析。             [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.73, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:15.049616Z [info     ] 执行聚类分析，方法: kmeans              [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.8, 'cpu_percent': 0.0} operation=cluster_analysis_computation\n", "2025-07-29T06:03:15.125293Z [info     ] 聚类分析计算完成。                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.83, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:15.145157Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.83, 'cpu_percent': 0.0} description=最终收敛点聚类分析的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:15.179608Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.83, 'cpu_percent': 0.0} description=聚类分析的总体量化指标报告，包括轮廓系数、簇心等。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:15.209995Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_statistics artifact_path=swift_pso_visualization\\tsne_cluster_statistics_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.83, 'cpu_percent': 0.0} description=每个簇内部所有参数的详细统计信息（均值、标准差等）。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:15.236687Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_summary_table artifact_path=swift_pso_visualization\\tsne_cluster_summary_table.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.83, 'cpu_percent': 0.0} description=各簇参数均值和标准差的对比摘要表，便于物理意义解释。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:15.259636Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.83, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization\\tsne_cluster_analysis_profile.json profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T06:03:15.276441Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 665.83, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization\\tsne_cluster_analysis_plot.png profile_name=swift_pso.tsne_cluster_analysis snapshot_path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization\\tsne_cluster_analysis_data.csv\n", "2025-07-29T06:03:15.565525Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.51, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T06:03:15.627119Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_cluster_analysis_plot base_path=output01\\swift_pso_run_20250729_140057\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.51, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:17.073573Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 708.55, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T06:03:17.100904Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 708.55, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_140057\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T06:03:17.131484Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 708.55, 'cpu_percent': 0.0} description=SWIFT-PSO最终收敛点的t-SNE聚类分析图表（带凸包）。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:17.153124Z [info     ] t-SNE可视化步骤完成                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 708.55, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "✅ [Step 3/5] t-SNE 可视化完成！结果: {'status': 'completed', 'clusters_found': 4}\n"]}], "source": ["# 1. 创建可视化配置 (保持不变)\n", "tsne_config = TsneVisualConfig(\n", "    perplexity=15,\n", "    n_iter=2000,\n", "    random_state=42,\n", "    cluster_method=\"kmeans\",\n", "    n_clusters=4,\n", "    dbscan_eps=1,\n", "    dbscan_min_samples=5\n", ")\n", "\n", "# 2. 获取并修改两个绘图配置，然后打包成字典\n", "# 2.1 收敛轨迹图的配置\n", "trajectory_profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Parameter Evolution (Trajectory)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.2 聚类分析图的配置\n", "cluster_profile = plot_registry.get(TsnePlotProfiles.CLUSTER_ANALYSIS).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Final Parameters (Cluster Analysis)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.3 打包成字典\n", "custom_plot_profiles = {\n", "    TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value: trajectory_profile,\n", "    TsnePlotProfiles.CLUSTER_ANALYSIS.value: cluster_profile\n", "}\n", "\n", "# 3. 执行可视化步骤 (加载数据的部分保持不变)\n", "print(\"🚀 [Step 3/5] 开始执行 t-SNE 可视化...\")\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "tsne_source_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)\n", "tsne_source_data = handler.load_dataframe(tsne_source_path)\n", "\n", "# 3.1 更新函数调用\n", "tsne_result = run_tsne_visualization_step(\n", "    config=tsne_config,\n", "    ctx=run_context,\n", "    tsne_source_data=tsne_source_data,\n", "    plot_profiles=custom_plot_profiles # <--- 修改此处\n", ")\n", "\n", "# 4. 更新打印的产物信息\n", "print(f\"✅ [Step 3/5] t-SNE 可视化完成！结果: {tsne_result}\")"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["## 6. 步骤四：PLT 盲井检验\n", "\n", "调用 `run_plt_analysis_step` 对模型的预测结果进行PLT盲井检验。此步骤会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物（包含在 `apply_bundle_with_pred` 中）。\n", "\n", "我们为不同的图表（贡献率交会图、捕获率曲线、洛伦兹曲线）分别获取并定制 `PlotProfile`，然后将它们打包成一个字典传入。"]}, {"cell_type": "code", "execution_count": 33, "id": "w5x6y7z8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:03:17.253551Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx\n", "2025-07-29T06:03:17.282435Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T06:03:17.307326Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} project_name=scape_plt_val\n", "2025-07-29T06:03:17.317570Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_plt_val\n", "2025-07-29T06:03:17.333646Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T06:03:17.367264Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:03:17.386478Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:03:17.404335Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T06:03:17.424512Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T06:03:17.439812Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} shape=(14, 4) sheet_name=PLT\n", "2025-07-29T06:03:17.454289Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T06:03:17.454289Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(14, 4) processing_time=0.054\n", "2025-07-29T06:03:17.480472Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:03:17.507084Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:03:17.525219Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.55, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx processing_time=0.272 project_name=WpIdentifier('scape_plt_val')\n", "2025-07-29T06:03:17.533568Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.56, 'cpu_percent': 0.0} extracted_curve_count=1 operation=extract_curve_dataframe_bundle\n", "2025-07-29T06:03:17.547010Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.56, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['MD_Top', 'WELL_NO', 'MD_Bottom', 'QOZI'] operation=extract_metadata output_curve_count=4 output_curves=['MD_Top', 'WELL_NO', 'MD_Bottom', 'QOZI']\n", "✅ 成功读取PLT验证数据: ./scape_plt_val.wp.xlsx\n", "🚀 [Step 4/5] 开始执行 PLT 盲井检验...\n", "2025-07-29T06:03:17.587161Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.56, 'cpu_percent': 0.0} step_name=plt_analysis\n", "2025-07-29T06:03:17.600295Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.57, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:17.613697Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.57, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:03:17.655062Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:17.693836Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:17.721413Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:17.747206Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:17.782218Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:17.813904Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:17.849107Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:17.885194Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:17.907211Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:03:17.920513Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:17.951106Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:17.991310Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:03:18.007218Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\swift_pso_run_20250729_140057\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 707.61, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:03:18.270200Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.36, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T06:03:18.299205Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.36, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:18.328712Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.36, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:03:18.345681Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.36, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:18.373324Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.36, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:18.419430Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.36, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:03:18.436847Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\swift_pso_run_20250729_140057\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 710.36, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:03:18.694252Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 713.11, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T06:03:18.733204Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 713.11, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:18.760794Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 713.11, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:03:18.773990Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 713.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:18.802021Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 713.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:18.848069Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 713.11, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:03:18.868641Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\swift_pso_run_20250729_140057\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 713.11, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:03:19.121109Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T06:03:19.147681Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:19.174532Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:03:19.200879Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:19.241161Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:19.280574Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:19.307454Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:19.333935Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:19.362202Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:19.396539Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:03:19.440744Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:19.462902Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:03:19.467273Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:19.493969Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:19.521096Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:03:19.547788Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\swift_pso_run_20250729_140057\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 715.86, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:03:19.834815Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 718.61, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T06:03:19.864652Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 718.61, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:19.881257Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 718.61, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:03:19.907965Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 718.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:19.934572Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 718.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:19.964973Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 718.61, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:03:19.983244Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\swift_pso_run_20250729_140057\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 718.61, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:03:20.250649Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T06:03:20.281769Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:20.306918Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:03:20.321055Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:20.365142Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:20.401357Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:03:20.418064Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\swift_pso_run_20250729_140057\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:03:20.695511Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T06:03:20.728452Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:20.761384Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0}\n", "✅ [Step 4/5] PLT 检验完成！结果: {'C-1': {'spearman_rho': 0.5714285714285715, 'gini_capture': 0.6364089205739811, 'gini_lorenz': -0.04091139346906436}, 'C-2': {'spearman_rho': 0.3571428571428572, 'gini_capture': -0.050207321282104145, 'gini_lorenz': 0.3178634789011404}}\n", "   - 分析报告已保存为产物: plt_analysis.reports.analyzed_layers_* \n"]}], "source": ["# --- 加载PLT验证数据 ---\n", "plt_val_path = \"./scape_plt_val.wp.xlsx\"\n", "plt_val_project = reader.read(plt_val_path)\n", "plt_val_bundle = plt_val_project.get_dataset(\"PLT\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取PLT验证数据: {plt_val_path}\")\n", "\n", "# 1. 创建PLT分析配置 (曲线名将作为facade函数的直接参数传入)\n", "plt_config = PltAnalysisConfig()\n", "\n", "# 2. 获取并修改多个绘图配置 (Get -> Modify -> Pass)\n", "contribution_profile = plot_registry.get(PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT).with_updates(\n", "    title_props={\"label\": \"Flow Contribution Crossplot\"}\n", ")\n", "\n", "capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE).with_updates(\n", "    title_props={\"label\": \"Permeability Capture Curve\"}\n", ")\n", "\n", "lorenz_profile = plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE).with_updates(\n", "    title_props={\"label\": \"Lorenz Curve Analysis\"}\n", ")\n", "\n", "plt_plot_profiles = {\n", "    PltPlotTypes.CONTRIBUTION_CROSSPLOT: contribution_profile,\n", "    PltPlotTypes.CAPTURE_CURVE: capture_profile,\n", "    PltPlotTypes.LORENZ_CURVE: lorenz_profile\n", "}\n", "\n", "# 3. 执行PLT分析步骤\n", "print(\"🚀 [Step 4/5] 开始执行 PLT 盲井检验...\")\n", "plt_result = run_plt_analysis_step(\n", "    config=plt_config,\n", "    ctx=run_context,\n", "    prediction_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    plt_bundle=plt_val_bundle,\n", "    permeability_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    flow_rate_curve=\"QOZI\",           # 数据选择器参数\n", "    plot_profiles=plt_plot_profiles\n", ")\n", "\n", "print(f\"✅ [Step 4/5] PLT 检验完成！结果: {plt_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PltAnalysisArtifacts.ANALYZED_LAYERS_TABLE_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["## 7. 步骤五：岩心井渗透率(CT)相关性分析\n", "\n", "调用 `run_perm_correlation_step` 对模型的预测结果进行岩心井检验。此步骤同样会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物。"]}, {"cell_type": "code", "execution_count": 34, "id": "e5f6g7h8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:03:20.841276Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx\n", "2025-07-29T06:03:20.869281Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T06:03:20.894923Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} project_name=scape_core_k_val\n", "2025-07-29T06:03:20.907359Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_core_k_val\n", "2025-07-29T06:03:20.921558Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T06:03:20.934918Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:03:20.948244Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 724.11, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:03:20.979636Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-29T06:03:21.008112Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T06:03:21.038337Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} shape=(44, 9) sheet_name=K_Val\n", "2025-07-29T06:03:21.051301Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-29T06:03:21.060964Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(44, 9) processing_time=0.093\n", "2025-07-29T06:03:21.079247Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:03:21.094680Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:03:21.105359Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val.wp.xlsx processing_time=0.264 project_name=WpIdentifier('scape_core_k_val')\n", "2025-07-29T06:03:21.123653Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} extracted_curve_count=7 operation=extract_curve_dataframe_bundle\n", "2025-07-29T06:03:21.143390Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['POR', 'PERM', 'K_LABEL', 'K_LABEL_TYPE', 'Lithology', 'MD', 'PERM_LT_001_FLAG', 'WELL_NO', 'SAMPLE_TYPE'] operation=extract_metadata output_curve_count=9 output_curves=['POR', 'PERM', 'K_LABEL', 'K_LABEL_TYPE', 'Lithology', 'MD', 'PERM_LT_001_FLAG', 'WELL_NO', 'SAMPLE_TYPE']\n", "✅ 成功读取岩心验证数据: ./scape_core_k_val.wp.xlsx\n", "🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\n", "2025-07-29T06:03:21.169239Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} step_name=ct_perm_corr_analysis\n", "2025-07-29T06:03:21.181000Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:21.191220Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.36, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T06:03:21.215208Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.39, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:21.247714Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.39, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:21.269570Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:21.294675Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:21.334601Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.39, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:21.370143Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_140057\\ct_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 721.39, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:22.855801Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_140057\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T06:03:22.900964Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:22.930830Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0}\n", "✅ [Step 5/5] 岩心井检验完成！结果: {'T-1': {'spearman_rho': 0.5339547855394632, 'conclusion': \"Fail: 5x符合率 12.2% <= 50% (未满足井 'T-1' 的放宽标准)。\"}}\n"]}], "source": ["# --- 加载岩心验证数据 ---\n", "core_val_path = \"./scape_core_k_val.wp.xlsx\"\n", "core_val_project = reader.read(core_val_path)\n", "core_val_bundle = core_val_project.get_dataset(\"K_Val\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取岩心验证数据: {core_val_path}\")\n", "\n", "# 1. 创建渗透率相关性分析配置\n", "perm_corr_config = PermCorrelationConfig(\n", "    relaxed_wells=[\"T-1\"]     # 对T-1井使用放宽的深度对齐标准\n", ")\n", "\n", "# 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": \"Core Perm vs. Predicted Perm\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "\n", "# 3. 执行渗透率相关性分析步骤\n", "print(\"🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\")\n", "perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=core_val_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"PERM\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=\"ct\"\n", ")\n", "\n", "print(f\"✅ [Step 5/5] 岩心井检验完成！结果: {perm_corr_result}\")"]}, {"cell_type": "code", "execution_count": 35, "id": "417ec50b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:03:23.002518Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} file_path=scape_core_k_val_t_1.wp.xlsx\n", "2025-07-29T06:03:23.055842Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} file_path=scape_core_k_val_t_1.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T06:03:23.086927Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} project_name=scape_core_k_val_t_1\n", "2025-07-29T06:03:23.096343Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_core_k_val_t_1\n", "2025-07-29T06:03:23.122529Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T06:03:23.135440Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:03:23.164042Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:03:23.189151Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val_T_1 dataset_type=Point operation=dataset_initialization\n", "2025-07-29T06:03:23.210278Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T06:03:23.234766Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} shape=(27, 9) sheet_name=K_Val_T_1\n", "2025-07-29T06:03:23.242169Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val_T_1')\n", "2025-07-29T06:03:23.255429Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val_T_1 dataset_type=Point df_shape=(27, 9) processing_time=0.071\n", "2025-07-29T06:03:23.273505Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:03:23.306041Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:03:23.316964Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val_t_1.wp.xlsx processing_time=0.314 project_name=WpIdentifier('scape_core_k_val_t_1')\n", "2025-07-29T06:03:23.337341Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} extracted_curve_count=7 operation=extract_curve_dataframe_bundle\n", "2025-07-29T06:03:23.357127Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['POR', 'PERM', 'PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'PERM_LT_001_FLAG', 'WELL_NO', 'SAMPLE_TYPE'] operation=extract_metadata output_curve_count=9 output_curves=['POR', 'PERM', 'PZI', 'K_LABEL', 'K_LABEL_TYPE', 'MD', 'PERM_LT_001_FLAG', 'WELL_NO', 'SAMPLE_TYPE']\n", "✅ 成功读取岩心验证数据: ./scape_core_k_val_t_1.wp.xlsx\n", "🚀 [Step 6/5] 开始执行岩心井渗透率相关性分析...\n", "2025-07-29T06:03:23.388344Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} step_name=t-1_perm_corr_analysis\n", "2025-07-29T06:03:23.389003Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0}\n", "2025-07-29T06:03:23.409406Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T06:03:23.428769Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=t-1_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:23.455691Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_140057\\t-1_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:23.478023Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=t-1_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:23.498283Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=t-1_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:23.536090Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:03:23.562594Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_140057\\t-1_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 726.04, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:03:25.085438Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 730.69, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_140057\\\\t-1_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_140057\\\\t-1_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T06:03:25.123152Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=t-1_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 730.69, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-060057-0554043c\n", "2025-07-29T06:03:25.149809Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 730.69, 'cpu_percent': 0.0}\n", "✅ [Step 6/5] 岩心井检验完成！结果: {'T-1': {'spearman_rho': 0.6853652815125449, 'conclusion': 'Fail: 3x符合率 37.5% <= 70% 且 10x符合率 70.8% <= 90%。'}}\n"]}], "source": ["# --- 加载岩心验证数据 ---\n", "core_val_path = \"./scape_core_k_val_t_1.wp.xlsx\"\n", "core_val_project = reader.read(core_val_path)\n", "core_val_bundle = core_val_project.get_dataset(\"K_Val_T_1\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取岩心验证数据: {core_val_path}\")\n", "\n", "# 1. 创建渗透率相关性分析配置\n", "perm_corr_config = PermCorrelationConfig(\n", ")\n", "\n", "# 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": \"Core Perm vs. Predicted Perm\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "\n", "# 3. 执行渗透率相关性分析步骤\n", "print(\"🚀 [Step 6/5] 开始执行岩心井渗透率相关性分析...\")\n", "perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=core_val_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=\"t-1\"\n", ")\n", "\n", "print(f\"✅ [Step 6/5] 岩心井检验完成！结果: {perm_corr_result}\")"]}, {"cell_type": "markdown", "id": "i9j0k1l2", "metadata": {}, "source": ["## 8. 总结\n", "\n", "🎉 **SWIFT-PSO Case 02 (重构版) 工作流执行完毕！**\n", "\n", "本次重构展示了如何使用新的组件化框架来构建一个清晰、可维护、可追踪的机器学习工作流。所有步骤的输入、输出和配置都得到了规范化管理。\n", "\n", "**关键亮点:**\n", "1.  **统一的运行上下文 (`RunContext`)**: 所有的步骤都在同一个 `RunContext` 中执行，确保了所有产物（模型、数据集、图表、报告）和日志都被集中管理在一个独立的运行目录中：\n", "    - **输出目录**: `./output02/swift_pso_workflow_run`\n", "2.  **清晰的步骤划分**: 每个核心任务（训练、预测、可视化、验证）都被封装成一个独立的 `run_*_step` 函数，职责明确。\n", "3.  **类型安全的配置 (Pydantic)**: 使用 `SwiftPsoTrainingConfig`, `TsneVisualConfig`, `PltAnalysisConfig` 等Pydantic模型替代了易出错的字典，提供了自动验证和清晰的文档。\n", "4.  **自动化的产物管理**: `facade` 函数内部处理了产物的保存和注册，使得工作流代码更简洁。下游步骤可以通过 `RunContext` 自动加载上游产物，无需手动传递文件路径。\n", "5.  **灵活的绘图系统 (`PlotProfile`)**: 通过 **Get -> Modify -> Pass** 模式，我们可以在不修改组件源码的情况下，轻松地定制图表的每一个细节，同时享受高质量的默认模板。"]}, {"cell_type": "markdown", "id": "j8k9l0m1", "metadata": {}, "source": ["## 9. 最终化运行\n", "\n", "在所有步骤完成后，手动调用 `finalize()` 来结束本次运行。这将确保所有日志被刷新，并且运行清单 `manifest.json` 被正确写入。"]}, {"cell_type": "code", "execution_count": 36, "id": "n2o3p4q5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:03:25.243257Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 730.69, 'cpu_percent': 0.0} duration_seconds=148.215 manifest_path=output01\\swift_pso_run_20250729_140057\\manifest.json operation=finalize run_id=20250729-060057-0554043c status=RUNNING\n", "✅ 运行已最终化，所有记录已保存。\n"]}], "source": ["run_context.finalize()\n", "print(\"✅ 运行已最终化，所有记录已保存。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}