# SCAPE项目AI协作规则

**版本**: 1.0
**目的**: 本文件定义了AI编程助手在参与SCAPE项目开发时必须遵循的核心规则和工作流。这些规则旨在确保代码质量、架构一致性、文档同步以及人与AI之间的高效协作。

---

## 1. 核心原则 (Core Principles)

*这些是你在任何时候都必须遵守的最高准则。*

*   **规则1.1：遵循SCAPE编码与通用规范 (CCG)**
    *   **描述**: 你必须严格遵守 `docs/coding/SCAPE_CCG_编码与通用规范.md` 中定义的所有规范，包括命名约定、类型注解、异常处理、日志记录等。这是代码质量的基线。

*   **规则1.2：代码是唯一的“事实来源”**
    *   **描述**: 当计划、文档与最终代码实现不一致时，你必须以最终代码为准。你的核心任务之一就是通过分析代码来更新和修正过时的文档和计划。

*   **规则1.3：使用项目专属领域语言**
    *   **描述**: 你必须学习并使用 `docs/项目专属领域语言与术语表.md` 中定义的术语（如`Component`, `Facade`, `RunContext`, `Artifact`等）进行沟通，以确保我们之间的理解没有歧义。

---

## 2. 工作流与方法论 (Workflows & Methodologies)

*你必须能够识别并遵循用户指定的结构化工作流。*

*   **规则2.1：遵循“计划-执行-核对”(PER)工作流**
    *   **描述**: 当用户以 `[PLAN]`, `[EXECUTE]`, `[RECONCILE]` 标签发起任务时，你必须严格按照 `docs/PER_WORKFLOW_PROMPT.md` 中定义的三个阶段执行相应任务，特别是在`RECONCILE`阶段，你的核心职责是对比差异并生成“竣工报告”和更新后的文档。

*   **规则2.2：遵循“代码驱动的文档更新”(CDDU)工作流**
    *   **描述**: 当用户以 `[DOC_UPDATE]` 标签发起任务时，你必须扮演“逆向技术文档工程师”的角色，通过对比新旧代码，分析语义变更，并生成与最新代码完全同步的文档包。详见 `docs/代码驱动的文档更新工作流.md`。

*   **规则2.3：遵循“API增强与分层开发”工作流**:
    *   **描述**: 当在应用层(`scape/core`)开发时，如果你识别出某个功能更适合放在基础库(`logwp`)，你**必须**暂停实现，并向用户提议一个“API增强支线任务”。在获得明确批准前，**绝不能**擅自修改基础库。详见 `docs/API增强与分层开发工作流.md`。

---

## 3. 开发与重构指令 (Development & Refactoring Directives)

*这些是你日常编码和重构时需要遵守的具体行为准则。*

*   **规则3.1：采用“小步快跑”策略**
    *   **描述**: 当被要求时，你必须将复杂的修改分解为一系列小型的、独立的、可验证的步骤。每完成一步，都应提交`diff`并等待用户确认，然后再进行下一步。

*   **规则3.2：优先保证代码的可读性**
    *   **描述**: 你的代码实现应首先追求清晰、直白和易于维护，其次才是技巧的精妙。避免使用过于晦涩的语法，除非有明确的性能或功能要求。

*   **规则3.3：应用指定的设计模式**
    *   **描述**: 当用户要求使用特定的设计模式（如“策略模式”、“工厂方法”）进行重构时，你必须严格围绕该模式来组织代码，而不是自由发挥。

*   **规则3.4：进行聚焦式代码审查**
    *   **描述**: 当用户要求从特定角度（如“性能”、“安全性”）审查代码时，你的反馈必须严格限制在该领域内，提供有深度的、具体的改进建议。

---

## 4. 架构意识与跨层协作 (Architectural Awareness & Cross-Layer Collaboration)

*你必须表现出对项目整体架构的理解，而不仅仅是孤立地看待代码。*

*   **规则4.1：主动进行变更影响分析**
    *   **描述**: 在修改基础库的核心API之前，如果用户要求，你必须分析此变更对应用层可能产生的所有影响，并生成一份影响报告。

*   **规则4.2：能够阐释架构设计思想**
    *   **描述**: 当被问及某个组件或设计的“为什么”时，你应能够扮演架构师的角色，解释其背后的设计原则、权衡和要解决的问题。

*   **规则4.3：识别并提议“生产化”改造**
    *   **描述**: 当面对一个原型代码时，如果用户要求，你应能识别出它与生产级代码之间的差距，并生成一份详细的“生产化清单”，指导如何将其完善并整合进基础库。

*   **规则4.4：管理API和配置的生命周期**
    *   **描述**: 你必须能够处理API的平滑废弃（添加警告、更新文档）和配置文件格式的迁移（生成迁移脚本），以确保项目的平稳演进。

---

## 5. 沟通与输出格式 (Communication & Output Format)

*你的回答和产出必须遵循以下格式，以保证协作效率。*

*   **规则5.1：解释“为什么”**
    *   **描述**: 在提供代码或解决方案时，应主动附上简要的解释，说明“为什么”这样修改是更好的选择，而不仅仅是“做了什么”。

*   **规则5.2：默认使用`diff`格式**
    *   **描述**: 所有的代码修改建议，都必须以`diff`统一格式提供，并包含完整的文件路径。新文件应以`/dev/null`作为源文件。

*   **规则5.3：生成指定的文档产物**
    *   **描述**: 你必须能够根据要求生成特定格式的文档，如Markdown格式的变更摘要、架构决策记录（ADR）、Mermaid/PlantUML格式的图表等。

*   **规则5.4：提供简明高效的指令**
    *   **描述**: 你应能理解并响应 `docs/简明高效的AI协作指令.md` 中列出的所有短指令。
