#!/usr/bin/env python3
"""简单的PlotProfile导出脚本。

快速导出所有PlotProfile配置到config/plot_profiles目录。

Usage:
    python scripts/plotting/export_profiles_simple.py
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def main():
    """主函数。"""
    print("🔄 正在导出PlotProfile配置...")

    try:
        # 导入所有包含PlotProfile配置的模块，触发注册
        print("📦 加载配置模块...")
        import logwp.extras.ml.log_scout.plot_profiles
        import scape.core.validation.plot_profiles
        import scape.core.swift_pso.plot_profiles
        import scape.core.obmiq.plot_profiles

        # 导入registry
        from logwp.extras.plotting import registry

        # 设置输出目录
        output_dir = Path("config/plot_profiles")
        output_dir.mkdir(parents=True, exist_ok=True)

        # 获取配置统计
        all_profiles = registry.list_profiles()
        base_profiles = registry.list_base_profiles()

        print(f"📊 发现 {len(all_profiles)} 个配置模板")
        print(f"📊 其中 {len(base_profiles)} 个基础模板")

        # 使用registry的save_to_dir方法导出
        print(f"💾 导出到: {output_dir}")
        registry.save_to_dir(output_dir)

        # 生成简单的配置清单
        manifest_content = f"""# PlotProfile 配置清单

## 配置统计
- 总配置数: {len(all_profiles)}
- 基础模板数: {len(base_profiles)}

## 所有配置
"""

        for profile in sorted(all_profiles):
            profile_type = "基础模板" if profile in base_profiles else "具体配置"
            manifest_content += f"- `{profile}.json` - {profile} ({profile_type})\n"

        manifest_content += """
## 使用方法

### 查看配置
```bash
cat validation.contribution_crossplot.json
```

### 修改配置
```bash
cp validation.contribution_crossplot.json my_custom.json
# 编辑 my_custom.json
```

### 在代码中使用
```python
from logwp.extras.plotting import registry, PlotProfile

# 方法1: 使用已注册的配置
profile = registry.get("validation.contribution_crossplot")

# 方法2: 从JSON文件加载
profile = PlotProfile.from_json("config/plot_profiles/my_custom.json")
```
"""

        # 保存清单文件
        manifest_file = output_dir / "README.md"
        with open(manifest_file, 'w', encoding='utf-8') as f:
            f.write(manifest_content)

        # 显示结果
        json_files = list(output_dir.glob('*.json'))
        print(f"\n✅ 导出完成!")
        print(f"📁 输出目录: {output_dir}")
        print(f"📄 配置文件: {len(json_files)} 个")
        print(f"📖 配置清单: {manifest_file}")

        print(f"\n📋 导出的配置文件:")
        for json_file in sorted(json_files):
            print(f"  - {json_file.name}")

    except Exception as e:
        print(f"\n❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
