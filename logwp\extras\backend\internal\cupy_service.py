"""logwp.extras.backend.internal.cupy_service - CuPy后端服务实现

提供基于CuPy的GPU计算后端服务。
"""

from __future__ import annotations

from typing import Any
import numpy as np

from ..service import RandomService

# 全局GPU状态缓存
_CUPY_AVAILABLE = False
_CUPY_MODULE = None
_CUPY_GENERIC = None

try:
    import cupy as cp
    _CUPY_AVAILABLE = True
    _CUPY_MODULE = cp
    _CUPY_GENERIC = cp.generic
except ImportError:
    # CuPy未安装或导入失败，将使用占位符类
    pass


class _CupyRandomService(RandomService):
    """Cupy随机数生成服务实现。

    这是对 `cupy.random` 的一层薄封装，遵循RandomService协议。
    """

    def __init__(self, backend_random_module: Any):
        self._random_module = backend_random_module

    def rand(self, *size: int) -> Any:
        return self._random_module.rand(*size)

    def uniform(self, low: float = 0.0, high: float = 1.0, size: Any = None) -> Any:
        return self._random_module.uniform(low, high, size)

    def normal(self, loc: float = 0.0, scale: float = 1.0, size: Any = None) -> Any:
        return self._random_module.normal(loc, scale, size)


if _CUPY_AVAILABLE and _CUPY_MODULE is not None and _CUPY_GENERIC is not None:
    class CupyService:
        """使用CuPy作为计算后端的GPU服务实现。

        该类实现了BackendService协议，为上层应用提供了统一的、
        基于CuPy的GPU计算接口。
        """

        def __init__(self):
            """初始化CupyService，并创建随机数生成器子服务。"""
            self._backend: Any = _CUPY_MODULE
            self._generic_type: Any = _CUPY_GENERIC
            self.random: RandomService = _CupyRandomService(self._backend.random)

        @property
        def name(self) -> str:
            """返回后端服务的名称。"""
            return 'gpu'

        def as_scalar(self, value: Any) -> int | float | bool:
            """将0维CuPy数组安全地转换为Python原生标量。"""
            if isinstance(value, (self._backend.ndarray, self._generic_type)):
                return value.item()
            return value

        def sum(self, array: Any, **kwargs: Any) -> Any:
            return self._backend.sum(array, **kwargs)

        def min(self, array: Any, **kwargs: Any) -> Any:
            return self._backend.min(array, **kwargs)

        def max(self, array: Any, **kwargs: Any) -> Any:
            return self._backend.max(array, **kwargs)

        def mean(self, array: Any, **kwargs: Any) -> Any:
            return self._backend.mean(array, **kwargs)

        def any(self, array: Any, **kwargs: Any) -> Any:
            return self._backend.any(array, **kwargs)

        def nan_to_num(self, array: Any, **kwargs: Any) -> Any:
            return self._backend.nan_to_num(array, **kwargs)

        def maximum(self, array1: Any, array2: Any) -> Any:
            return self._backend.maximum(array1, array2)

        def isnan(self, array: Any) -> Any:
            return self._backend.isnan(array)

        def isinf(self, array: Any) -> Any:
            return self._backend.isinf(array)

        def argmin(self, array: Any, axis: int | None = None) -> Any:
            """返回数组中最小值的索引。"""
            return self._backend.argmin(array, axis=axis)

        def clip(self, array: Any, a_min: Any, a_max: Any) -> Any:
            """将数组中的值裁剪到指定的最小和最大值之间。"""
            return self._backend.clip(array, a_min, a_max)

        # --- 数组创建方法 ---

        def zeros(self, shape: Any, dtype: Any = None) -> Any:
            """创建一个全为0的数组。"""
            return self._backend.zeros(shape, dtype=dtype)

        def zeros_like(self, array: Any, dtype: Any = None) -> Any:
            """创建一个与给定数组形状和类型相同的全零数组。"""
            return self._backend.zeros_like(array, dtype=dtype)

        def ones(self, shape: Any, dtype: Any = None) -> Any:
            """创建一个全为1的数组。"""
            return self._backend.ones(shape, dtype=dtype)

        def ones_like(self, array: Any, dtype: Any = None) -> Any:
            """创建一个与给定数组形状和类型相同的全一数组。"""
            return self._backend.ones_like(array, dtype=dtype)

        def full(self, shape: Any, fill_value: Any, dtype: Any = None) -> Any:
            """创建一个以指定值填充的数组。"""
            return self._backend.full(shape, fill_value, dtype=dtype)

        def full_like(self, array: Any, fill_value: Any, dtype: Any = None) -> Any:
            """创建一个与给定数组形状和类型相同，并以指定值填充的数组。"""
            return self._backend.full_like(array, fill_value, dtype=dtype)

        def arange(self, start: Any, stop: Any = None, step: Any = 1, dtype: Any = None) -> Any:
            """在给定间隔内返回均匀间隔的值。"""
            return self._backend.arange(start, stop, step, dtype=dtype)

        def linspace(self, start: Any, stop: Any, num: int = 50, dtype: Any = None) -> Any:
            """在指定间隔内返回等间隔的数字。"""
            return self._backend.linspace(start, stop, num, dtype=dtype)

        def logspace(self, start: Any, stop: Any, num: int = 50, base: float = 10.0, dtype: Any = None) -> Any:
            """在指定间隔内返回对数刻度上等间隔的数字。"""
            return self._backend.logspace(start, stop, num, base=base, dtype=dtype)

        # --- 核心数学与统计函数 ---

        def std(self, array: Any, **kwargs: Any) -> Any:
            """计算数组的标准差。"""
            return self._backend.std(array, **kwargs)

        def var(self, array: Any, **kwargs: Any) -> Any:
            """计算数组的方差。"""
            return self._backend.var(array, **kwargs)

        def abs(self, array: Any) -> Any:
            """计算数组的绝对值。"""
            return self._backend.abs(array)

        def sqrt(self, array: Any) -> Any:
            """计算数组的非负平方根。"""
            return self._backend.sqrt(array)

        def log(self, array: Any) -> Any:
            """计算数组的自然对数。"""
            return self._backend.log(array)

        def log10(self, array: Any) -> Any:
            """计算数组的以10为底的对数。"""
            return self._backend.log10(array)

        def exp(self, array: Any) -> Any:
            """计算数组所有元素的指数。"""
            return self._backend.exp(array)

        def trapz(self, y: Any, x: Any = None, **kwargs: Any) -> Any:
            """使用梯形法则沿给定轴积分。"""
            return self._backend.trapz(y, x=x, **kwargs)

        # --- 数据操作与逻辑运算 ---

        def all(self, array: Any, **kwargs: Any) -> Any:
            """检查数组中是否所有元素都为True。"""
            return self._backend.all(array, **kwargs)

        def where(self, condition: Any, x: Any, y: Any) -> Any:
            """根据条件从x或y中返回元素。"""
            return self._backend.where(condition, x, y)

        def reshape(self, array: Any, newshape: Any) -> Any:
            """在不改变数据的情况下为数组赋予新的形状。"""
            return self._backend.reshape(array, newshape)

        def transpose(self, array: Any, axes: Any = None) -> Any:
            """反转或排列数组的轴。"""
            return self._backend.transpose(array, axes)

        def concatenate(self, arrays: Any, axis: int = 0) -> Any:
            """沿现有轴连接一系列数组。"""
            return self._backend.concatenate(arrays, axis=axis)

        def copy(self, array: Any) -> Any:
            """创建数组的副本。"""
            return self._backend.copy(array)

        # --- 数据传输与检查 ---

        def to_cpu(self, array: Any) -> Any:
            """确保数组位于CPU内存中（作为NumPy数组）。"""
            if isinstance(array, self._backend.ndarray):
                return array.get()
            if not isinstance(array, np.ndarray):
                return np.asarray(array)
            return array

        def to_gpu(self, array: Any) -> Any:
            """尝试将数组移动到GPU内存中（作为CuPy数组）。"""
            if not isinstance(array, self._backend.ndarray):
                return self._backend.asarray(array)
            return array

        def is_on_gpu(self, array: Any) -> bool:
            """检查一个数组是否位于GPU上。"""
            return isinstance(array, self._backend.ndarray)

        def to_backend(self, array: Any) -> Any:
            """将数组移动到当前backend对应的设备上。

            对于CupyService（GPU backend），这等同于to_gpu()。
            """
            return self.to_gpu(array)
else:
    class CupyService:
        """CuPy不可用时的占位符服务实现。"""
        def __init__(self, *args: Any, **kwargs: Any) -> None:
            raise ImportError("CuPy is not installed or failed to import. GPU backend is not available.")
