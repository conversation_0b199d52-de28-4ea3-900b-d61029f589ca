"""logwp.testing - 测试工具包

为SCAPE项目提供统一、便捷的测试数据创建与验证工具。

Key Components
--------------
- **Fixtures (`StandardDatasets`)**: 获取标准的、可复用的测试**数据集**。
- **Factories (`TestDataFactory`, `MetadataFactory`, `SyntheticCurveFactory`, `FosterNMRTestDataFactory`)**: 创建预定义的**项目**、**元数据**和**算法测试数据**。
- **Builders (`ProjectBuilder`, `DatasetBuilder`, `CurveBuilder`)**: 灵活地**自定义**项目和数据集。
- **Utils (`quick_*`, `validate_*`, `assert_*`)**: 底层的快速生成器和科学计算验证器。

For detailed usage and best practices, please refer to `logwp/testing/README.md`.

Examples
--------
>>> from logwp.testing import StandardDatasets, TestDataFactory, DatasetBuilder
>>> from logwp.testing import SyntheticCurveFactory, FosterNMRTestDataFactory
>>> from logwp.testing import assert_curves_allclose, assert_physical_constraints
>>>
>>> # 场景1: 获取标准数据集 (最常用)
>>> continuous_ds = StandardDatasets.get_continuous_dataset()
>>>
>>> # 场景2: 获取预定义项目
>>> demo_project = TestDataFactory.santos_demo_project()
>>>
>>> # 场景3: 构建自定义数据集
>>> custom_ds = DatasetBuilder.quick_continuous_dataset(
...     name="custom_test",
...     curves={"GR": -999.25} # 测试边界条件
... )
>>>
>>> # 场景4: 生成算法测试数据
>>> input_ds, expected_perm = FosterNMRTestDataFactory.create_dataset_with_expected_perm()
>>>
>>> # 场景5: 生成合成曲线
>>> t2_dist = SyntheticCurveFactory.nmr_t2_distribution(depth_points=100, t2_bins=64)
>>>
>>> # 场景6: 科学计算验证
>>> assert_curves_allclose(calculated_perm, expected_perm, rtol=1e-6)
>>> assert_physical_constraints(perm_data, "PERM", {"min": 0})

Note
----
这个包仅用于测试目的，不应该在生产代码中使用。所有方法都针对测试场景优化，
可能不适合生产环境的性能要求。

References
----------
- 《SCAPE_STG_软件测试指南.md》- 测试数据生成规范
- 《SCAPE_CCG_编码与通用规范.md》- 测试代码规范
"""

from __future__ import annotations

# 核心构建器
from .builders.dataset_builder import DatasetBuilder
from .builders.project_builder import ProjectBuilder
from .builders.curve_builder import CurveBuilder

# 工厂类
from .factories.data_factory import TestDataFactory
from .factories.metadata_factory import MetadataFactory
from .factories.algorithm_data_factory import FosterNMRTestDataFactory
from .factories.synthetic_curve_factory import SyntheticCurveFactory

# 工具函数
from .utils.quick_generators import (
    quick_continuous_df,
    quick_discrete_df,
    quick_interval_df,
    quick_metadata
)
from .utils.validators import (
    validate_test_dataset,
    assert_dataset_structure,
    check_curve_consistency,
    assert_curves_allclose,
    assert_physical_constraints
)

# 标准夹具
from .fixtures.standard_datasets import StandardDatasets

__all__ = [
    # 构建器
    "DatasetBuilder",
    "ProjectBuilder",
    "CurveBuilder",

    # 工厂
    "TestDataFactory",
    "MetadataFactory",
    "FosterNMRTestDataFactory",
    "SyntheticCurveFactory",

    # 快速生成器
    "quick_continuous_df",
    "quick_discrete_df",
    "quick_interval_df",
    "quick_metadata",

    # 验证器
    "validate_test_dataset",
    "assert_dataset_structure",
    "check_curve_consistency",
    "assert_curves_allclose",
    "assert_physical_constraints",

    # 标准夹具
    "StandardDatasets",
]
