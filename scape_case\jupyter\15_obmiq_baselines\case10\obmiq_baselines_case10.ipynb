{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ Baselines 端到端工作流 (Scikit-learn版)\n", "\n", "本Notebook演示了 `scape.core.obmiq_baselines` 组件的完整端到端工作流。该组件使用经典的机器学习方法（如XGBoost, RandomForest）来预测油基泥浆侵入的影响。\n", "\n", "流程包括：\n", "1. **训练步骤**: 执行嵌套交叉验证来无偏地评估和选择最佳模型组合，然后使用全量数据训练最终的加权融合模型。\n", "2. **预测步骤**: 加载训练好的模型，对新数据进行预测。由于该组件为每个目标独立建模，我们将循环调用预测步骤。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-30T15:07:37.075210Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 126.61, 'cpu_percent': 0.0}\n", "2025-07-30T15:07:37.885564Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.14, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-30T15:07:37.895222Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.16, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-30T15:07:37.907095Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.18, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-30T15:07:37.914046Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.22, 'cpu_percent': 0.0} operation=register_base_profile profile_name=obmiq_baselines.base\n", "2025-07-30T15:07:37.925113Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.25, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq_baselines.crossplot\n", "2025-07-30T15:07:37.925113Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.27, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq_baselines.residuals_plot\n", "2025-07-30T15:07:37.947367Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.3, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq_baselines.residuals_hist\n", "2025-07-30T15:07:37.947367Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.32, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq_baselines.feature_importance\n", "2025-07-30T15:07:38.002267Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.34, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq_baselines.lowo_cv_crossplot\n", "环境设置与导入完成。\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "from pathlib import Path\n", "\n", "# 导入 SCAPE 和 LOGWP 的核心组件\n", "from logwp.io import WpExcelReader\n", "from logwp.extras.tracking import RunContext\n", "from logwp.models.constants import WpDepthRole\n", "\n", "# 导入新的OBMIQ Baselines组件\n", "from scape.core.obmiq_baselines import (\n", "    run_obmiq_baselines_training_step,\n", "    run_obmiq_baselines_prediction_step,\n", "    ObmiqBaselinesTrainingConfig,\n", "    ObmiqBaselinesPredictionConfig,\n", "    ObmiqBaselinesTrainingArtifacts,\n", "    ObmiqBaselinesArtifactHandler\n", ")\n", "\n", "# 设置\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', 50)\n", "\n", "print(\"环境设置与导入完成。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T15:07:38.668819Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 417.03, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx\n", "2025-07-30T15:07:38.685788Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 417.41, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx file_size_mb=1.23 sheet_count=1\n", "2025-07-30T15:07:38.694877Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 417.45, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all\n", "2025-07-30T15:07:38.697557Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 417.46, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all\n", "2025-07-30T15:07:38.710598Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 417.79, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T15:07:38.727172Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 417.86, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=33 well_curves=1\n", "2025-07-30T15:07:39.914034Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.5, 'cpu_percent': 0.0} shape=(2082, 96) sheet_name=nmr_obmiq\n", "2025-07-30T15:07:39.926605Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq')\n", "2025-07-30T15:07:39.937147Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} curve_count=33 dataset_name=nmr_obmiq dataset_type=Continuous df_shape=(2082, 96) processing_time=1.231\n", "2025-07-30T15:07:39.948505Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-30T15:07:39.955448Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-30T15:07:39.983682Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx processing_time=1.317 project_name=WpIdentifier('santos_obmiq_cum_all')\n", "2025-07-30T15:07:40.017995Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} extracted_curve_count=31 operation=extract_curve_dataframe_bundle\n", "2025-07-30T15:07:40.042605Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 430.98, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['PHIT_NMR', 'BFV_NMR', 'BVI_NMR', 'RD_LOG10', 'DRES', 'RS_LOG10', 'VMICRO', 'DT2_P50', 'DT', 'VMESO', 'SMACRO', 'SDR_PROXY', 'PHIE_NMR', 'SMESO', 'VMACRO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'WELL_NO', 'FFV_NMR', 'LT2STDDEV_FFI', 'T2LM_LOG10', 'CN', 'LKURT_FFI', 'T2LM_LONG_LOG10', 'MD', 'SMICRO', 'LSKEW_FFI', 'SWB_NMR', 'T2_P20_LOG10', 'T2_P50_LOG10', 'DEN', 'DPHIT_NMR', 'SFF_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['PHIT_NMR', 'BFV_NMR', 'BVI_NMR', 'RD_LOG10', 'DRES', 'RS_LOG10', 'VMICRO', 'DT2_P50', 'DT', 'VMESO', 'SMACRO', 'SDR_PROXY', 'PHIE_NMR', 'SMESO', 'VMACRO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'WELL_NO', 'FFV_NMR', 'LT2STDDEV_FFI', 'T2LM_LOG10', 'CN', 'LKURT_FFI', 'T2LM_LONG_LOG10', 'MD', 'SMICRO', 'LSKEW_FFI', 'SWB_NMR', 'T2_P20_LOG10', 'T2_P50_LOG10', 'DEN', 'DPHIT_NMR', 'SFF_NMR']\n", "2025-07-30T15:07:40.058987Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 430.98, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx\n", "2025-07-30T15:07:40.082190Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.0, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx file_size_mb=2.56 sheet_count=1\n", "2025-07-30T15:07:40.102440Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.0, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all_apply\n", "2025-07-30T15:07:40.114014Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.0, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all_apply\n", "2025-07-30T15:07:40.126105Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.01, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T15:07:40.143936Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.01, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=33 well_curves=1\n", "2025-07-30T15:07:42.581330Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.07, 'cpu_percent': 0.0} shape=(4503, 96) sheet_name=nmr_obmiq_apply\n", "2025-07-30T15:07:42.598039Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.07, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_apply')\n", "2025-07-30T15:07:42.609332Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.07, 'cpu_percent': 0.0} curve_count=33 dataset_name=nmr_obmiq_apply dataset_type=Continuous df_shape=(4503, 96) processing_time=2.494\n", "2025-07-30T15:07:42.620158Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.07, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-30T15:07:42.658015Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.07, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-30T15:07:42.675109Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.07, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=2.616 project_name=WpIdentifier('santos_obmiq_cum_all_apply')\n", "2025-07-30T15:07:42.691754Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 442.07, 'cpu_percent': 0.0} extracted_curve_count=31 operation=extract_curve_dataframe_bundle\n", "2025-07-30T15:07:42.709211Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.57, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['PHIT_NMR', 'BFV_NMR', 'BVI_NMR', 'RD_LOG10', 'DRES', 'RS_LOG10', 'VMICRO', 'DT2_P50', 'DT', 'VMESO', 'SMACRO', 'SDR_PROXY', 'PHIE_NMR', 'SMESO', 'VMACRO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'WELL_NO', 'FFV_NMR', 'LT2STDDEV_FFI', 'T2LM_LOG10', 'CN', 'LKURT_FFI', 'T2LM_LONG_LOG10', 'MD', 'SMICRO', 'LSKEW_FFI', 'SWB_NMR', 'T2_P20_LOG10', 'T2_P50_LOG10', 'DEN', 'DPHIT_NMR', 'SFF_NMR'] operation=extract_metadata output_curve_count=33 output_curves=['PHIT_NMR', 'BFV_NMR', 'BVI_NMR', 'RD_LOG10', 'DRES', 'RS_LOG10', 'VMICRO', 'DT2_P50', 'DT', 'VMESO', 'SMACRO', 'SDR_PROXY', 'PHIE_NMR', 'SMESO', 'VMACRO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'WELL_NO', 'FFV_NMR', 'LT2STDDEV_FFI', 'T2LM_LOG10', 'CN', 'LKURT_FFI', 'T2LM_LONG_LOG10', 'MD', 'SMICRO', 'LSKEW_FFI', 'SWB_NMR', 'T2_P20_LOG10', 'T2_P50_LOG10', 'DEN', 'DPHIT_NMR', 'SFF_NMR']\n", "数据加载成功。\n", "训练数据束: nmr_obmiq, 形状: (2082, 96), 井名: WELL_NO\n", "预测数据束: nmr_obmiq_apply, 形状: (4503, 96)\n"]}], "source": ["# --- 请在此处填写您的数据路径 ---\n", "# 注意：Baselines组件不使用T2谱序列，但可以使用相同的输入文件\n", "TRAIN_WP_FILE_PATH = \"santos_obmiq_cum_all.wp.xlsx\"\n", "TRAIN_DATASET_NAME = \"nmr_obmiq\"\n", "\n", "PRED_WP_FILE_PATH = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "PREDICTION_DATASET_NAME = \"nmr_obmiq_apply\"\n", "# ---------------------------------\n", "\n", "try:\n", "    # 获取训练和预测数据束\n", "    reader = WpExcelReader()\n", "    project = reader.read(TRAIN_WP_FILE_PATH)\n", "    train_ds = project.get_dataset(TRAIN_DATASET_NAME)\n", "    train_bundle = train_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "    well_no_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "    depth_name = train_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "    pred_reader = WpExcelReader()\n", "    pred_project = pred_reader.read(PRED_WP_FILE_PATH)\n", "    prediction_ds = pred_project.get_dataset(PREDICTION_DATASET_NAME)\n", "    prediction_bundle = prediction_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "\n", "    print(\"数据加载成功。\")\n", "    print(f\"训练数据束: {train_ds.name}, 形状: {train_ds.df.shape}, 井名: {well_no_name}\")\n", "    print(f\"预测数据束: {prediction_ds.name}, 形状: {prediction_ds.df.shape}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 数据加载失败: {e}\")\n", "    project = None\n", "    pred_project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 运行 OBMIQ Baselines 工作流"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T15:07:42.745250Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.6, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\obmiq_baselines_run_20250730_230742 run_id=20250730-150742-172e4c42\n", "--- 开始 OBMIQ Baselines 训练步骤 ---\n", "2025-07-30T15:07:42.758679Z [info     ] ===== OBMIQ Baselines Training Step Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.61, 'cpu_percent': 0.0}\n", "2025-07-30T15:07:42.772081Z [info     ] 曲线名已成功解析为DataFrame列名。          [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.61, 'cpu_percent': 0.0}\n", "2025-07-30T15:07:42.785193Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.configs.training_config artifact_path=obmiq_baselines_training\\training_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.61, 'cpu_percent': 0.0} description=本次训练运行的配置快照。 operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T15:07:42.800720Z [info     ] --- 开始为目标 'DT2_P50' 进行建模 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.62, 'cpu_percent': 0.0}\n", "2025-07-30T15:07:42.807189Z [info     ] 开始为目标 'DT2_P50' 执行嵌套交叉验证...    [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.62, 'cpu_percent': 0.0}\n", "2025-07-30T15:07:42.822668Z [info     ]   外层折叠 1/2: 验证井 'C-1'          [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.75, 'cpu_percent': 0.0}\n", "2025-07-30T15:18:59.340762Z [info     ]     - XGBoost 在井 'C-1' 上的RMSE: 0.3953 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 539.82, 'cpu_percent': 0.0}\n", "2025-07-30T15:30:29.552648Z [info     ]     - RandomForest 在井 'C-1' 上的RMSE: 0.3609 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 551.1, 'cpu_percent': 0.0}\n", "2025-07-30T15:42:00.166597Z [info     ]     - SVR 在井 'C-1' 上的RMSE: 0.6419 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 543.16, 'cpu_percent': 0.0}\n", "2025-07-30T15:42:00.180764Z [info     ]   外层折叠 2/2: 验证井 'T-1'          [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 543.42, 'cpu_percent': 0.0}\n", "2025-07-30T15:50:20.939813Z [info     ]     - XGBoost 在井 'T-1' 上的RMSE: 0.3322 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 549.52, 'cpu_percent': 0.0}\n", "2025-07-30T15:58:48.010249Z [info     ]     - RandomForest 在井 'T-1' 上的RMSE: 0.3487 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 550.89, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:24.949444Z [info     ]     - SVR 在井 'T-1' 上的RMSE: 0.4895 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 544.32, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:24.956389Z [info     ] 所有交叉验证折叠完成，正在聚合结果...           [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 544.32, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:24.962943Z [info     ] 最佳模型组合: RandomForest (权重: 0.51) 和 XGBoost (权重: 0.49) [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 544.32, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:24.969634Z [info     ] 正在为泛化能力评估生成合并的交叉验证预测...        [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 544.32, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:24.987609Z [info     ]   总体泛化性能 (LOWO-CV): RMSE=0.3555, R2=-0.4079 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 542.9, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:24.995337Z [info     ]   正在为目标 'DT2_P50' 生成泛化能力评估产物... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 542.9, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:25.149455Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.data_snapshots.lowo_cv_predictions_DT2_P50 artifact_path=obmiq_baselines_training\\lowo_cv_predictions_DT2_P50.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 542.86, 'cpu_percent': 0.0} description=留一井交叉验证(LOWO-CV)的合并预测结果 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:11:25.190619Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 545.06, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.lowo_cv_crossplot\n", "2025-07-30T16:11:25.219326Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_DT2_P50 base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 545.46, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T16:11:25.527609Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 563.48, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\lowo_cv_crossplot_DT2_P50.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\lowo_cv_crossplot_DT2_P50.svg']\n", "2025-07-30T16:11:25.541345Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.lowo_cv_crossplot_DT2_P50 artifact_path=obmiq_baselines_training\\lowo_cv_crossplot_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 563.48, 'cpu_percent': 0.0} description=基于LOWO-CV结果的泛化能力交会图 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:11:25.597241Z [info     ] 开始为目标 'DT2_P50' 训练最终的融合模型...   [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 563.48, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:25.610221Z [info     ]   正在为最终模型组件 'RandomForest' 进行优化... [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 563.48, 'cpu_percent': 0.0}\n", "2025-07-30T16:11:25.616990Z [info     ]     > 步骤1 & 2: 使用GroupKFold进行最终的特征选择和超参数联合寻优... [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 563.48, 'cpu_percent': 0.0}\n", "2025-07-30T16:18:35.024212Z [info     ]     > 'RandomForest' 最终选定 21 个特征。 [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 574.72, 'cpu_percent': 0.0}\n", "2025-07-30T16:18:35.034598Z [info     ]     > 'RandomForest' 最终最优参数: {'model__max_depth': 14, 'model__max_features': 1.0, 'model__min_samples_leaf': 8, 'model__min_samples_split': 4, 'model__n_estimators': 384, 'selector__min_features_to_select': 8} [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 574.72, 'cpu_percent': 0.0}\n", "2025-07-30T16:18:35.040580Z [info     ]   正在为最终模型组件 'XGBoost' 进行优化...  [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 574.72, 'cpu_percent': 0.0}\n", "2025-07-30T16:18:35.044567Z [info     ]     > 步骤1 & 2: 使用GroupKFold进行最终的特征选择和超参数联合寻优... [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 574.72, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:21.348768Z [info     ]     > 'XGBoost' 最终选定 21 个特征。   [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.9, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:21.357959Z [info     ]     > 'XGBoost' 最终最优参数: {'model__colsample_bytree': 0.6955072955386102, 'model__gamma': 0.4285541684647477, 'model__learning_rate': 0.011872334817229716, 'model__max_depth': 4, 'model__n_estimators': 171, 'model__subsample': 0.7381861693715561, 'selector__min_features_to_select': 14} [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.9, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:21.381106Z [info     ] 所有模型组件训练完成，正在组装最终的融合模型...      [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.9, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:21.597200Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.models.assets_sklearn_DT2_P50 artifact_path=obmiq_baselines_training\\model_assets_DT2_P50.joblib context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.96, 'cpu_percent': 0.0} description=包含最终融合模型和元数据的资产包 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:21.630795Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.reports.nested_cv_performance_DT2_P50 artifact_path=obmiq_baselines_training\\nested_cv_report_DT2_P50.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.97, 'cpu_percent': 0.0} description=嵌套交叉验证中各候选模型的性能报告 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:21.663286Z [info     ]   正在为目标 'DT2_P50' 评估最终模型并生成图表... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.97, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:21.749930Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.data_snapshots.final_model_evaluation_DT2_P50 artifact_path=obmiq_baselines_training\\final_model_evaluation_DT2_P50.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.97, 'cpu_percent': 0.0} description=最终模型在全部训练数据上的预测和残差结果 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:21.771298Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.97, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.crossplot\n", "2025-07-30T16:25:21.831958Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_DT2_P50 base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 592.99, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T16:25:22.126897Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 610.05, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_crossplot_DT2_P50.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_crossplot_DT2_P50.svg']\n", "2025-07-30T16:25:22.141223Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.eval_crossplot_DT2_P50 artifact_path=obmiq_baselines_training\\eval_crossplot_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 610.05, 'cpu_percent': 0.0} description=最终模型评估图 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:22.171801Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 610.05, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.residuals_plot\n", "2025-07-30T16:25:22.200792Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_DT2_P50 base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 610.05, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T16:25:22.570771Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 627.08, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_plot_DT2_P50.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_plot_DT2_P50.svg']\n", "2025-07-30T16:25:22.586297Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.eval_residuals_plot_DT2_P50 artifact_path=obmiq_baselines_training\\eval_residuals_plot_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 627.08, 'cpu_percent': 0.0} description=最终模型评估图 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:22.616034Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 627.08, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.residuals_hist\n", "2025-07-30T16:25:22.699666Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_DT2_P50 base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 627.47, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T16:25:23.026938Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 644.51, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_hist_DT2_P50.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_hist_DT2_P50.svg']\n", "2025-07-30T16:25:23.045435Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.eval_residuals_hist_DT2_P50 artifact_path=obmiq_baselines_training\\eval_residuals_hist_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 644.51, 'cpu_percent': 0.0} description=最终模型评估图 (目标: DT2_P50) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:23.122881Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.data_snapshots.feature_importance_DT2_P50 artifact_path=obmiq_baselines_training\\feature_importance_DT2_P50.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 644.52, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:23.150837Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 644.52, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.feature_importance\n", "2025-07-30T16:25:23.249343Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=feature_importance_DT2_P50 base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 644.58, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T16:25:23.614569Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 672.75, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\feature_importance_DT2_P50.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\feature_importance_DT2_P50.svg']\n", "2025-07-30T16:25:23.628778Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.feature_importance_DT2_P50 artifact_path=obmiq_baselines_training\\feature_importance_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 672.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T16:25:23.649145Z [info     ] --- 目标 'DT2_P50' 建模完成 ---      [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 672.75, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:23.656821Z [info     ] --- 开始为目标 'DPHIT_NMR' 进行建模 --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 672.75, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:23.667054Z [info     ] 开始为目标 'DPHIT_NMR' 执行嵌套交叉验证...  [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 672.75, 'cpu_percent': 0.0}\n", "2025-07-30T16:25:23.712522Z [info     ]   外层折叠 1/2: 验证井 'C-1'          [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 672.75, 'cpu_percent': 0.0}\n", "2025-07-30T16:35:49.581902Z [info     ]     - XGBoost 在井 'C-1' 上的RMSE: 0.0205 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 677.35, 'cpu_percent': 0.0}\n", "2025-07-30T16:46:08.819728Z [info     ]     - RandomForest 在井 'C-1' 上的RMSE: 0.0209 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 682.65, 'cpu_percent': 0.0}\n", "2025-07-30T16:56:11.436110Z [info     ]     - SVR 在井 'C-1' 上的RMSE: 0.0297 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.01, 'cpu_percent': 0.0}\n", "2025-07-30T16:56:11.444016Z [info     ]   外层折叠 2/2: 验证井 'T-1'          [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.45, 'cpu_percent': 0.0}\n", "2025-07-30T17:04:13.657061Z [info     ]     - XGBoost 在井 'T-1' 上的RMSE: 0.0216 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 680.46, 'cpu_percent': 0.0}\n", "2025-07-30T17:12:09.972489Z [info     ]     - RandomForest 在井 'T-1' 上的RMSE: 0.0207 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 683.56, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:57.513449Z [info     ]     - SVR 在井 'T-1' 上的RMSE: 0.0229 [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.96, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:57.525556Z [info     ] 所有交叉验证折叠完成，正在聚合结果...           [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.97, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:57.540618Z [info     ] 最佳模型组合: RandomForest (权重: 0.50) 和 XGBoost (权重: 0.50) [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.97, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:57.553536Z [info     ] 正在为泛化能力评估生成合并的交叉验证预测...        [scape.core.obmiq_baselines.internal.nested_cv_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.97, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:57.562476Z [info     ]   总体泛化性能 (LOWO-CV): RMSE=0.0204, R2=-0.1775 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.1, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:57.571598Z [info     ]   正在为目标 'DPHIT_NMR' 生成泛化能力评估产物... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.1, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:57.656149Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.data_snapshots.lowo_cv_predictions_DPHIT_NMR artifact_path=obmiq_baselines_training\\lowo_cv_predictions_DPHIT_NMR.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 673.33, 'cpu_percent': 0.0} description=留一井交叉验证(LOWO-CV)的合并预测结果 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:19:57.686191Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 674.76, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.lowo_cv_crossplot\n", "2025-07-30T17:19:57.724415Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_DPHIT_NMR base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 674.76, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T17:19:58.079225Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 691.93, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\lowo_cv_crossplot_DPHIT_NMR.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\lowo_cv_crossplot_DPHIT_NMR.svg']\n", "2025-07-30T17:19:58.104888Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.lowo_cv_crossplot_DPHIT_NMR artifact_path=obmiq_baselines_training\\lowo_cv_crossplot_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 691.93, 'cpu_percent': 0.0} description=基于LOWO-CV结果的泛化能力交会图 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:19:58.117733Z [info     ] 开始为目标 'DPHIT_NMR' 训练最终的融合模型... [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 691.93, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:58.129241Z [info     ]   正在为最终模型组件 'RandomForest' 进行优化... [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 691.97, 'cpu_percent': 0.0}\n", "2025-07-30T17:19:58.134449Z [info     ]     > 步骤1 & 2: 使用GroupKFold进行最终的特征选择和超参数联合寻优... [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 691.97, 'cpu_percent': 0.0}\n", "2025-07-30T17:26:24.292391Z [info     ]     > 'RandomForest' 最终选定 21 个特征。 [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 705.09, 'cpu_percent': 0.0}\n", "2025-07-30T17:26:24.303409Z [info     ]     > 'RandomForest' 最终最优参数: {'model__max_depth': 18, 'model__max_features': 'log2', 'model__min_samples_leaf': 7, 'model__min_samples_split': 9, 'model__n_estimators': 291, 'selector__min_features_to_select': 12} [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 705.09, 'cpu_percent': 0.0}\n", "2025-07-30T17:26:24.320067Z [info     ]   正在为最终模型组件 'XGBoost' 进行优化...  [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 705.09, 'cpu_percent': 0.0}\n", "2025-07-30T17:26:24.334015Z [info     ]     > 步骤1 & 2: 使用GroupKFold进行最终的特征选择和超参数联合寻优... [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 705.09, 'cpu_percent': 0.0}\n", "2025-07-30T17:32:57.472959Z [info     ]     > 'XGBoost' 最终选定 21 个特征。   [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.58, 'cpu_percent': 0.0}\n", "2025-07-30T17:32:57.481975Z [info     ]     > 'XGBoost' 最终最优参数: {'model__colsample_bytree': 0.9836347950450453, 'model__gamma': 0.09562109243507816, 'model__learning_rate': 0.2565694303165187, 'model__max_depth': 8, 'model__n_estimators': 293, 'model__subsample': 0.7053697561925005, 'selector__min_features_to_select': 12} [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.58, 'cpu_percent': 0.0}\n", "2025-07-30T17:32:57.494320Z [info     ] 所有模型组件训练完成，正在组装最终的融合模型...      [scape.core.obmiq_baselines.internal.final_training_procedure] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.58, 'cpu_percent': 0.0}\n", "2025-07-30T17:32:57.701754Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.models.assets_sklearn_DPHIT_NMR artifact_path=obmiq_baselines_training\\model_assets_DPHIT_NMR.joblib context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.8, 'cpu_percent': 0.0} description=包含最终融合模型和元数据的资产包 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:57.726924Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.reports.nested_cv_performance_DPHIT_NMR artifact_path=obmiq_baselines_training\\nested_cv_report_DPHIT_NMR.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.8, 'cpu_percent': 0.0} description=嵌套交叉验证中各候选模型的性能报告 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:57.739016Z [info     ]   正在为目标 'DPHIT_NMR' 评估最终模型并生成图表... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.8, 'cpu_percent': 0.0}\n", "2025-07-30T17:32:57.850884Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.data_snapshots.final_model_evaluation_DPHIT_NMR artifact_path=obmiq_baselines_training\\final_model_evaluation_DPHIT_NMR.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.83, 'cpu_percent': 0.0} description=最终模型在全部训练数据上的预测和残差结果 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:57.872045Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.85, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.crossplot\n", "2025-07-30T17:32:57.904283Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_DPHIT_NMR base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 719.91, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T17:32:58.256202Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 736.97, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_crossplot_DPHIT_NMR.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_crossplot_DPHIT_NMR.svg']\n", "2025-07-30T17:32:58.280259Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.eval_crossplot_DPHIT_NMR artifact_path=obmiq_baselines_training\\eval_crossplot_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 736.97, 'cpu_percent': 0.0} description=最终模型评估图 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:58.315517Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 736.97, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.residuals_plot\n", "2025-07-30T17:32:58.366934Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_DPHIT_NMR base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 736.99, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T17:32:58.709216Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 754.02, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_plot_DPHIT_NMR.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_plot_DPHIT_NMR.svg']\n", "2025-07-30T17:32:58.737337Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.eval_residuals_plot_DPHIT_NMR artifact_path=obmiq_baselines_training\\eval_residuals_plot_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 754.02, 'cpu_percent': 0.0} description=最终模型评估图 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:58.770354Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 754.02, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.residuals_hist\n", "2025-07-30T17:32:58.862808Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_DPHIT_NMR base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 754.02, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T17:32:59.174296Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 771.08, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_hist_DPHIT_NMR.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\eval_residuals_hist_DPHIT_NMR.svg']\n", "2025-07-30T17:32:59.189081Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.eval_residuals_hist_DPHIT_NMR artifact_path=obmiq_baselines_training\\eval_residuals_hist_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 771.08, 'cpu_percent': 0.0} description=最终模型评估图 (目标: DPHIT_NMR) operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:59.347935Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.data_snapshots.feature_importance_DPHIT_NMR artifact_path=obmiq_baselines_training\\feature_importance_DPHIT_NMR.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 771.08, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:59.366822Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 771.08, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq_baselines.feature_importance\n", "2025-07-30T17:32:59.460709Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=feature_importance_DPHIT_NMR base_path=output01\\obmiq_baselines_run_20250730_230742\\obmiq_baselines_training context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 771.08, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-30T17:32:59.838461Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 799.25, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\feature_importance_DPHIT_NMR.png', 'output01\\\\obmiq_baselines_run_20250730_230742\\\\obmiq_baselines_training\\\\feature_importance_DPHIT_NMR.svg']\n", "2025-07-30T17:32:59.872565Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.plots.feature_importance_DPHIT_NMR artifact_path=obmiq_baselines_training\\feature_importance_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 799.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:32:59.888144Z [info     ] --- 目标 'DPHIT_NMR' 建模完成 ---    [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 799.25, 'cpu_percent': 0.0}\n", "2025-07-30T17:32:59.896758Z [info     ] ===== OBMIQ Baselines Training Step Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 799.25, 'cpu_percent': 0.0}\n", "训练步骤完成。\n", "\n", "--- 开始 OBMIQ Baselines 预测步骤 ---\n", "  正在为目标 'DT2_P50' 进行预测...\n", "2025-07-30T17:33:00.022607Z [info     ] ===== OBMIQ Baselines Prediction Step Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 828.41, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.037835Z [info     ] 已根据模型元数据准备了 28 个特征用于预测。        [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 829.37, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.042819Z [info     ] 正在执行预测...                      [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 829.37, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.173458Z [info     ] 正在格式化预测结果并保存产物...              [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 829.84, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.402722Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.datasets.predictions artifact_path=obmiq_baselines_prediction\\predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 834.27, 'cpu_percent': 0.0} description=包含原始输入和模型预测结果的数据集。 operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:33:00.416629Z [info     ] ===== OBMIQ Baselines Prediction Step Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 834.27, 'cpu_percent': 0.0}\n", "  正在为目标 'DPHIT_NMR' 进行预测...\n", "2025-07-30T17:33:00.552677Z [info     ] ===== OBMIQ Baselines Prediction Step Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 825.99, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.562697Z [info     ] 已根据模型元数据准备了 28 个特征用于预测。        [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 826.96, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.571725Z [info     ] 正在执行预测...                      [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 826.96, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.725383Z [info     ] 正在格式化预测结果并保存产物...              [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 830.15, 'cpu_percent': 0.0}\n", "2025-07-30T17:33:00.947474Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_baselines.datasets.predictions artifact_path=obmiq_baselines_prediction\\predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 835.86, 'cpu_percent': 0.0} description=包含原始输入和模型预测结果的数据集。 operation=register_artifact run_id=20250730-150742-172e4c42\n", "2025-07-30T17:33:00.961859Z [info     ] ===== OBMIQ Baselines Prediction Step Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 835.86, 'cpu_percent': 0.0}\n", "所有目标的预测均已完成。\n", "\n", "最终合并预测结果预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL_NO</th>\n", "      <th>MD</th>\n", "      <th>DT2_P50</th>\n", "      <th>DT2_P50_PRED</th>\n", "      <th>DPHIT_NMR</th>\n", "      <th>DPHIT_NMR_PRED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C-1</td>\n", "      <td>6310.4268</td>\n", "      <td>0.104076</td>\n", "      <td>-0.124826</td>\n", "      <td>0.065477</td>\n", "      <td>0.007475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C-1</td>\n", "      <td>6310.5792</td>\n", "      <td>-0.148789</td>\n", "      <td>-0.280539</td>\n", "      <td>0.061988</td>\n", "      <td>0.007074</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C-1</td>\n", "      <td>6310.7316</td>\n", "      <td>-0.211292</td>\n", "      <td>-0.301782</td>\n", "      <td>0.052548</td>\n", "      <td>0.006556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C-1</td>\n", "      <td>6310.8840</td>\n", "      <td>-0.245636</td>\n", "      <td>-0.208996</td>\n", "      <td>0.038409</td>\n", "      <td>0.002294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C-1</td>\n", "      <td>6311.0364</td>\n", "      <td>0.112414</td>\n", "      <td>-0.094685</td>\n", "      <td>0.051449</td>\n", "      <td>0.006816</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  WELL_NO         MD   DT2_P50  DT2_P50_PRED  DPHIT_NMR  DPHIT_NMR_PRED\n", "0     C-1  6310.4268  0.104076     -0.124826   0.065477        0.007475\n", "1     C-1  6310.5792 -0.148789     -0.280539   0.061988        0.007074\n", "2     C-1  6310.7316 -0.211292     -0.301782   0.052548        0.006556\n", "3     C-1  6310.8840 -0.245636     -0.208996   0.038409        0.002294\n", "4     C-1  6311.0364  0.112414     -0.094685   0.051449        0.006816"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-30T17:33:01.052110Z [info     ] Run marked as successful       [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 837.16, 'cpu_percent': 0.0} operation=mark_success run_id=20250730-150742-172e4c42\n", "2025-07-30T17:33:01.070162Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 837.16, 'cpu_percent': 0.0} duration_seconds=8718.318 manifest_path=output01\\obmiq_baselines_run_20250730_230742\\manifest.json operation=finalize run_id=20250730-150742-172e4c42 status=COMPLETED\n"]}], "source": ["if project:\n", "    output_dir = Path(\"./output01\")\n", "    run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"obmiq_baselines_run\")\n", "\n", "    with RunContext(run_dir=output_dir / run_dir_name, overwrite=True) as ctx:\n", "        # --- 1. 训练步骤 ---\n", "        print(\"--- 开始 OBMIQ Baselines 训练步骤 ---\")\n", "        training_config = ObmiqBaselinesTrainingConfig(\n", "            n_iter_random_search=20,\n", "            inner_cv_folds=5\n", "        )\n", "        training_kwargs = {\n", "            \"tabular_features\": ['DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "                                 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "                                 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "                                 'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "                                 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY'],\n", "            \"target_features\": [\"DT2_P50\", \"DPHIT_NMR\"],\n", "            \"grouping_feature\": well_no_name\n", "        }\n", "        training_results = run_obmiq_baselines_training_step(\n", "            config=training_config, ctx=ctx, train_bundle=train_bundle, **training_kwargs\n", "        )\n", "        print(\"训练步骤完成。\")\n", "\n", "        # --- 2. 预测步骤 (为每个目标独立调用) ---\n", "        print(\"\\n--- 开始 OBMIQ Baselines 预测步骤 ---\")\n", "        handler = ObmiqBaselinesArtifactHandler()\n", "        final_predictions_df = prediction_bundle.data.copy()\n", "\n", "        for target_name in training_kwargs[\"target_features\"]:\n", "            print(f\"  正在为目标 '{target_name}' 进行预测...\")\n", "            model_artifact_name = f\"{ObmiqBaselinesTrainingArtifacts.MODEL_ASSETS.value}_{target_name}\"\n", "            model_assets_path = ctx.get_artifact_path(model_artifact_name)\n", "            model_assets = handler.load_model_assets(model_assets_path)\n", "\n", "            output_curve_name = f\"{target_name}_PRED\"\n", "            prediction_results = run_obmiq_baselines_prediction_step(\n", "                config=ObmiqBaselinesPredictionConfig(),\n", "                ctx=ctx,\n", "                model_assets=model_assets,\n", "                prediction_bundle=prediction_bundle,\n", "                output_curve_name=output_curve_name\n", "            )\n", "            # 从当次预测结果中读取预测列，并合并到最终结果中\n", "            single_pred_df = pd.read_csv(prediction_results[\"output_path\"])\n", "            final_predictions_df[output_curve_name] = single_pred_df[output_curve_name]\n", "\n", "        print(\"所有目标的预测均已完成。\")\n", "\n", "        # --- 3. 结果检查 ---\n", "        print(\"\\n最终合并预测结果预览:\")\n", "        display(final_predictions_df[[\"WELL_NO\", \"MD\", \"DT2_P50\", \"DT2_P50_PRED\", \"DPHIT_NMR\", \"DPHIT_NMR_PRED\"]].head())\n", "else:\n", "    print(\"因数据加载失败，跳过工作流执行。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}