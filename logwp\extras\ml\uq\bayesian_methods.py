"""
提供基于贝叶斯近似方法的不确定性量化工具。
"""
from __future__ import annotations

from typing import Any, Tuple
import pandas as pd
import numpy as np


class MCDropoutPredictor:
    """
    一个使用蒙特卡洛丢弃 (MC Dropout) 为深度学习模型生成预测区间的封装器。

    MC Dropout 是一种近似贝叶斯推断的技术，它通过在预测时多次启用Dropout层
    来进行前向传播，从而得到一个预测结果的分布。这个分布的标准差可以被
    视为模型认知不确定性的一种度量。

    注意：此封装器要求输入的模型是一个包含Dropout层的深度学习模型
    （如 Keras 或 PyTorch 模型），并且该模型在调用 `predict` 时支持
    `training=True` 参数（对于Keras）或等效机制来激活Dropout。
    """

    def __init__(self, model: Any, n_passes: int = 50):
        """
        初始化 MCDropoutPredictor。

        Args:
            model: 一个已训练的、包含Dropout层的深度学习模型实例。
                   该模型必须有一个 `predict` 方法。
            n_passes (int, optional): 前向传播的次数，用于生成预测分布。
                                      次数越多，结果越稳定，但计算成本越高。
                                      默认为 50。
        """
        if not hasattr(model, 'predict'):
            raise TypeError("模型对象必须有一个 'predict' 方法。")
        self.model = model
        self.n_passes = n_passes

    def predict(
        self,
        X_test: pd.DataFrame | np.ndarray,
        alpha: float = 0.05
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        对新数据进行预测，并返回点预测和基于MC Dropout的预测区间。

        Args:
            X_test (pd.DataFrame | np.ndarray): 需要预测的特征数据。
            alpha (float, optional): 显著性水平，用于定义置信度。
                                     alpha=0.05 对应 95% 的置信区间。默认为 0.05。

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: 一个元组 (y_pred, y_lower, y_upper)，
            分别包含点预测值（预测均值）、预测区间的下界和上界。
        """
        # 收集多次前向传播的预测结果
        predictions = []
        for _ in range(self.n_passes):
            try:
                # 尝试以 Keras 的方式调用，在预测时激活 Dropout
                pred = self.model.predict(X_test, verbose=0)
            except TypeError:
                # 如果模型不支持 training 参数，则正常调用
                # (这要求用户在外部手动设置模型的训练模式，例如 PyTorch 的 model.train())
                pred = self.model.predict(X_test)
            predictions.append(pred)

        # 将预测结果堆叠成一个数组
        # 形状变为 (n_passes, n_samples, n_outputs)
        predictions_stack = np.stack(predictions, axis=0)

        # 计算均值作为点预测
        y_pred = np.mean(predictions_stack, axis=0)

        # 计算百分位数作为预测区间的上下界
        lower_quantile = (alpha / 2) * 100
        upper_quantile = (1 - alpha / 2) * 100
        y_lower = np.percentile(predictions_stack, lower_quantile, axis=0)
        y_upper = np.percentile(predictions_stack, upper_quantile, axis=0)

        # 如果输出是一维的，则去除多余的维度
        return y_pred.squeeze(), y_lower.squeeze(), y_upper.squeeze()
