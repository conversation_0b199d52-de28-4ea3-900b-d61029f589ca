# logwp.infra - 基础工具与日志系统

`logwp/infra` 包提供了项目中最基础、与业务模型无关的横切功能，其中最核心的就是现代化、结构化的日志系统。

## 日志系统核心特性

- **结构化输出**: 所有日志都可输出为JSON或易于阅读的彩色文本，便于机器解析和人工查看。
- **配置驱动**: 日志行为（级别、格式、输出目标）由一个清晰的配置模型 `LoggingConfig` 控制。
- **专为Jupyter优化**: 无需任何手动配置即可直接使用，支持“懒加载”和自动初始化，完美适配交互式分析流程。
- **上下文感知**: 轻松绑定全局或局部上下文信息（如`well_name`, `run_id`），自动附加到每条日志中。
- **性能与环境监控**: 可自动在日志中包含CPU、内存和GPU（如果可用）的上下文信息。

---

## Jupyter Notebook 使用详解与最佳实践

本日志系统特别为 Jupyter Notebook 环境进行了优化，提供了极为便利的使用体验。

### 场景1：快速探索性分析 (推荐用法)

在进行日常的数据探索和快速分析时，您**无需进行任何配置**。只需在任意单元格中获取日志器即可。系统会自动以彩色的、适合人类阅读的格式在单元格下方输出日志。

```python
# 在任何 .py 文件或 Notebook cell 中
from logwp.infra import get_logger

# 获取日志器，它会自动以默认配置完成初始化
logger = get_logger("exploratory_analysis")

# 开始记录日志
logger.info("正在加载数据集...")
logger.warning("发现数据集中有 5% 的缺失值", missing_curve="PHIT")
```

**输出 (彩色控制台格式):**
```
2024-07-26T08:15:27.123Z [info     ] 这是一个信息日志            logger=__main__
2024-07-26T08:15:27.124Z [warning  ] 这是一个警告              logger=__main__ user=test_user reason=data_missing
```

`get_logger()` 在首次调用时会自动使用一套适合交互式环境的默认配置完成初始化。

---

## 2. 进阶用法：自定义配置

当您需要更精细的控制（例如，将日志写入文件、更改日志级别或使用JSON格式）时，可以在程序或Notebook的起始位置调用 `configure_logging` 函数。

### 示例：在Notebook中将日志输出到文件

在Notebook的第一个cell中运行以下代码，即可为本次会话配置全局日志行为。

```python
from logwp.infra import configure_logging, get_logger
from logwp.config import LoggingConfig

# 1. 创建一个自定义配置对象
my_notebook_config = LoggingConfig(
    level="DEBUG",          # 记录更详细的DEBUG级别日志
    log_to_file=True,       # 启用文件日志
    log_file_path="logs/my_analysis_session.log", # 指定日志文件路径
    json_format=True,       # 文件日志推荐使用JSON格式
)

# 2. 应用配置
configure_logging(my_notebook_config)

# 3. 在后续任何cell中获取的logger都将遵循此配置
logger = get_logger("my_notebook.data_processing")
logger.debug("数据预处理开始", dataset_name="Well-A_Logs")
```

**文件 `logs/my_analysis_session.log` 中的内容:**
```json
{"level": "info", "logger": "logwp.infra.logging_config", "event": "日志系统配置完成", "config": {"level": "DEBUG", ...}, "timestamp": "..."}
{"level": "debug", "logger": "my_notebook.data_processing", "event": "数据预处理开始", "dataset_name": "Well-A_Logs", "timestamp": "..."}
```

### `LoggingConfig` 配置项说明

- `level`: 日志级别 (`"DEBUG"`, `"INFO"`, `"WARNING"`, `"ERROR"`, `"CRITICAL"`)。
- `json_format`: `True` 输出JSON，`False` 输出彩色文本。
- `log_to_file`: `True` 启用文件日志。
- `log_file_path`: 日志文件路径。
- `include_performance_context`: `True` 在日志中自动添加CPU和内存使用情况。
- `include_gpu_context`: `True` 在日志中自动添加GPU使用情况（如果GPU可用）。

---

## 3. 核心功能：绑定上下文

`structlog` 的核心优势之一是上下文绑定。您可以使用 `logger.bind()` 来为一个日志器实例添加持久化的键值对，这些键值对会自动出现在该日志器后续的所有日志消息中。

```python
from logwp.infra import get_logger

logger = get_logger("analysis_pipeline")

# 假设我们正在处理一个特定的井
well_logger = logger.bind(well_name="Well-B", run_id="run_20240726_1")

well_logger.info("数据加载成功", file_count=5)

# ... 更多操作 ...

try:
    # 模拟一个错误
    result = 10 / 0
except Exception:
    well_logger.error("计算失败", operation="compute_porosity", exc_info=True)

```

**输出:**
```
2024-07-26T08:30:45.456Z [info     ] 数据加载成功               logger=analysis_pipeline well_name=Well-B run_id=run_20240726_1 file_count=5
2024-07-26T08:30:45.457Z [error    ] 计算失败                   logger=analysis_pipeline well_name=Well-B run_id=run_20240726_1 operation=compute_porosity exc_info=True
... (Traceback) ...
```

如您所见，`well_name` 和 `run_id` 自动出现在了后续的所有日志中，这对于追踪和筛选特定任务的日志非常有用。
