"""PCA异常定义。

定义PCA模块的完整异常体系，遵循SCAPE项目的结构化异常处理规范，
使用Exception Groups和ErrorContext。

Architecture
------------
层次/依赖: PCA异常层，继承自logwp.extras异常体系
设计原则: 结构化异常、上下文信息、异常链
性能特征: 轻量级异常、快速诊断、详细上下文
"""

from __future__ import annotations

from typing import Any

from logwp.extras.exceptions import WpExtrasError
from logwp.infra.exceptions import ErrorContext


# =============================================================================
# PCA异常基类
# =============================================================================

class WpPcaError(WpExtrasError):
    """PCA分析异常基类。

    Architecture
    ------------
    层次/依赖: extras层PCA异常，继承自WpExtrasError
    设计原则: 结构化异常、上下文信息、异常链
    性能特征: 轻量级异常、快速诊断、详细上下文

    所有PCA相关异常的基类，提供统一的异常处理接口。
    支持结构化错误上下文和异常链传播。
    """

    def __init__(
        self,
        message: str,
        context: WpPcaErrorContext | None = None,
        cause: Exception | None = None
    ) -> None:
        """初始化PCA异常。

        Args:
            message: 错误消息
            context: PCA错误上下文
            cause: 原始异常（用于异常链）
        """
        super().__init__(message)
        self.context = context or WpPcaErrorContext()
        self.__cause__ = cause


# =============================================================================
# 具体异常类型
# =============================================================================

class WpPcaDataError(WpPcaError):
    """PCA数据验证异常。

    Architecture
    ------------
    层次/依赖: PCA数据异常，用于数据质量问题
    设计原则: 详细诊断、用户友好、可操作建议
    性能特征: 快速验证、精确定位、清晰反馈

    当输入数据不满足PCA分析要求时抛出，包括：
    - 数据集类型不支持
    - 曲线数量不足
    - 数据包含NaN值
    - 数据完整性不足
    """
    pass


class WpPcaComputationError(WpPcaError):
    """PCA计算异常。

    Architecture
    ------------
    层次/依赖: PCA计算异常，用于算法执行问题
    设计原则: 算法诊断、性能监控、自动恢复
    性能特征: GPU/CPU切换、内存管理、数值稳定

    当PCA计算过程中发生错误时抛出，包括：
    - n_components超出范围
    - 矩阵奇异无法分解
    - GPU内存不足
    - 数值计算溢出
    """
    pass


class WpPcaModelError(WpPcaError):
    """PCA模型异常。

    Architecture
    ------------
    层次/依赖: PCA模型异常，用于模型管理问题
    设计原则: 模型验证、版本兼容、完整性检查
    性能特征: 快速验证、内存优化、类型安全

    当PCA模型操作发生错误时抛出，包括：
    - 模型参数不一致
    - 逆变换维度不匹配
    - 模型序列化失败
    - 版本兼容性问题
    """
    pass


class WpPcaVisualizationError(WpPcaError):
    """PCA可视化异常。

    Architecture
    ------------
    层次/依赖: PCA可视化异常，用于图表生成问题
    设计原则: 图表诊断、格式支持、用户体验
    性能特征: 高分辨率、多格式、快速渲染

    当PCA可视化过程中发生错误时抛出，包括：
    - 保存路径无效
    - 图像格式不支持
    - 数据维度不适合可视化
    - 渲染引擎错误
    """
    pass


class WpPcaPersistenceError(WpPcaError):
    """PCA模型持久化异常。

    Architecture
    ------------
    层次/依赖: PCA持久化异常，用于模型存储问题
    设计原则: 格式支持、版本管理、完整性保证
    性能特征: 高效序列化、压缩存储、快速加载

    当PCA模型保存或加载过程中发生错误时抛出，包括：
    - 文件格式不支持
    - 磁盘空间不足
    - 权限不足
    - 数据损坏
    """
    pass


# =============================================================================
# 错误上下文定义
# =============================================================================

class WpPcaErrorContext(ErrorContext):
    """PCA错误上下文。

    Architecture
    ------------
    层次/依赖: PCA错误上下文，提供详细诊断信息
    设计原则: 结构化信息、快速诊断、用户友好
    性能特征: 轻量级容器、快速访问、内存友好

    提供PCA操作的详细上下文信息，便于问题诊断和用户反馈。
    包含操作类型、数据集信息、算法参数等关键信息。

    Attributes:
        operation: 操作类型（preprocess、compute、transform等）
        dataset_name: 数据集名称
        n_components: 主成分数量
        n_features: 特征数量
        n_samples: 样本数量
        curve_names: 相关曲线名称列表
        compute_device: 计算设备类型
        algorithm: 使用的PCA算法
        stage: 操作阶段
        additional_info: 额外诊断信息
    """

    def __init__(
        self,
        operation: str | None = None,
        dataset_name: str | None = None,
        n_components: int | None = None,
        n_features: int | None = None,
        n_samples: int | None = None,
        curve_names: list[str] | None = None,
        compute_device: str | None = None,
        algorithm: str | None = None,
        stage: str | None = None,
        additional_info: dict[str, Any] | None = None
    ) -> None:
        """初始化PCA错误上下文。

        Args:
            operation: 操作类型
            dataset_name: 数据集名称
            n_components: 主成分数量
            n_features: 特征数量
            n_samples: 样本数量
            curve_names: 相关曲线名称
            compute_device: 计算设备类型
            algorithm: PCA算法类型
            stage: 操作阶段
            additional_info: 额外信息字典
        """
        super().__init__()
        self.operation = operation
        self.dataset_name = dataset_name
        self.n_components = n_components
        self.n_features = n_features
        self.n_samples = n_samples
        self.curve_names = curve_names or []
        self.compute_device = compute_device
        self.algorithm = algorithm
        self.stage = stage
        self.additional_info = additional_info or {}

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式，便于日志记录。

        Returns:
            包含所有上下文信息的字典
        """
        return {
            "operation": self.operation,
            "dataset_name": self.dataset_name,
            "n_components": self.n_components,
            "n_features": self.n_features,
            "n_samples": self.n_samples,
            "curve_names": self.curve_names,
            "compute_device": self.compute_device,
            "algorithm": self.algorithm,
            "stage": self.stage,
            "additional_info": self.additional_info,
        }

    def __str__(self) -> str:
        """返回人类可读的上下文描述。

        Returns:
            格式化的上下文信息字符串
        """
        parts = []

        if self.operation:
            parts.append(f"操作: {self.operation}")

        if self.dataset_name:
            parts.append(f"数据集: {self.dataset_name}")

        if self.n_components is not None:
            parts.append(f"主成分数: {self.n_components}")

        if self.n_features is not None:
            parts.append(f"特征数: {self.n_features}")

        if self.n_samples is not None:
            parts.append(f"样本数: {self.n_samples}")

        if self.curve_names:
            curve_list = ", ".join(self.curve_names[:3])
            if len(self.curve_names) > 3:
                curve_list += f" 等{len(self.curve_names)}个曲线"
            parts.append(f"曲线: {curve_list}")

        if self.compute_device:
            parts.append(f"计算设备: {self.compute_device}")

        if self.algorithm:
            parts.append(f"算法: {self.algorithm}")

        if self.stage:
            parts.append(f"阶段: {self.stage}")

        return " | ".join(parts) if parts else "无上下文信息"


# =============================================================================
# 异常工厂函数
# =============================================================================

def create_data_error(
    message: str,
    dataset_name: str | None = None,
    curve_names: list[str] | None = None,
    cause: Exception | None = None
) -> WpPcaDataError:
    """创建数据验证异常。

    Args:
        message: 错误消息
        dataset_name: 数据集名称
        curve_names: 相关曲线名称
        cause: 原始异常

    Returns:
        配置好的数据异常实例
    """
    context = WpPcaErrorContext(
        operation="data_validation",
        dataset_name=dataset_name,
        curve_names=curve_names,
        stage="validation"
    )
    return WpPcaDataError(message, context, cause)


def create_computation_error(
    message: str,
    n_components: int | None = None,
    n_features: int | None = None,
    compute_device: str | None = None,
    algorithm: str | None = None,
    cause: Exception | None = None
) -> WpPcaComputationError:
    """创建计算异常。

    Args:
        message: 错误消息
        n_components: 主成分数量
        n_features: 特征数量
        compute_device: 计算设备
        algorithm: PCA算法
        cause: 原始异常

    Returns:
        配置好的计算异常实例
    """
    context = WpPcaErrorContext(
        operation="pca_computation",
        n_components=n_components,
        n_features=n_features,
        compute_device=compute_device,
        algorithm=algorithm,
        stage="computation"
    )
    return WpPcaComputationError(message, context, cause)
