#!/usr/bin/env python3
"""tests.conftest - 全局测试配置。

本模块提供pytest的全局配置和fixture，包括环境变量设置以避免Windows上的警告。

Architecture
------------
层次/依赖: tests层，全局测试配置
设计原则: 统一测试环境配置，避免平台特定问题

遵循CCG规范：
- CS-1: 统一测试环境配置
- EH-1: 避免平台特定警告
"""

from __future__ import annotations

import os


def pytest_configure(config):
    """pytest配置钩子，在测试开始前设置环境变量。
    
    设置环境变量以避免Windows上的joblib和scikit-learn警告：
    - LOKY_MAX_CPU_COUNT: 避免joblib无法检测物理CPU核心数的警告
    - OMP_NUM_THREADS: 避免KMeans在Windows上的内存泄漏警告
    """
    # 设置joblib环境变量以避免Windows上的CPU检测警告
    if os.name == 'nt' and 'LOKY_MAX_CPU_COUNT' not in os.environ:
        os.environ['LOKY_MAX_CPU_COUNT'] = str(os.cpu_count() or 4)
    
    # 设置OpenMP环境变量以避免KMeans内存泄漏警告
    if os.name == 'nt' and 'OMP_NUM_THREADS' not in os.environ:
        os.environ['OMP_NUM_THREADS'] = '1'
