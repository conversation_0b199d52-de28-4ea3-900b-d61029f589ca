# 文件路径: scape/core/swift_pso/pso_optimizer.py

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, Callable

import numpy as np
import pandas as pd

from logwp.extras.backend.internal.factory import create_backend_service_by_name

from .pso_check_early_stopping import check_early_stopping
from .pso_loss_function import compute_loss_vectorized
from .pso_handle_boundary import (
    handle_boundary_hard_reflection_vectorized, handle_boundary_jitter_vectorized
)
from scape.core.foster_nmr.calculate_foster_nmr import calculate_foster_nmr_permeability_for_val
from .pso_calculate_rmse import calculate_rmse

if TYPE_CHECKING:
    from logwp.extras.backend import BackendService

from logwp.infra import get_logger

logger = get_logger(__name__)

def pso_optimizer(
    training_set: pd.DataFrame,
    config: Dict[str, Any],
    backend_service: BackendService
) -> Tuple[Dict[str, float], List[float], Optional[List[np.ndarray]]]:
    """
    使用粒子群优化（PSO）算法寻找FOSTER-NMR模型的最优参数。

    本函数作为一个调度中心，根据传入的配置动态调用不同的
    损失函数、边界处理策略和早停机制，以适应Bootstrap+LOWO和
    Fine-Tuning两个不同阶段的需求。

    Args:
        training_set (pd.DataFrame): 用于计算损失的训练数据集。
        config (dict): 包含PSO所有配置的字典。
        backend_service (BackendService): 用于计算的后端服务实例

    Returns:
        Tuple[Dict[str, float], List[float], Optional[List[np.ndarray]]]:
        - final_params_dict (Dict[str, float]): 找到的最优参数向量。
        - gbest_loss_history (List[float]): 每次迭代的全局最优损失值历史。
        - gbest_pos_history_for_tsne (Optional[List[np.ndarray]]): 可选的全局
          最优位置演化轨迹，仅在'bootstrap'模式下为t-SNE可视化生成。
    """
    # --- 1. 函数定义与配置解析 ---
    # 目标：从传入的 config 字典中解析出所有需要的参数，为后续步骤做好准备。

    # a. 解析PSO核心超参数
    # 新增：设置随机种子以确保可复现性
    seed = config.get('random_seed')
    if seed is not None:
        logger.debug(f"为PSO优化器设置随机种子: {seed}")
        np.random.seed(seed)
        if backend_service.name == 'gpu':
            try:
                import cupy as cp
                cp.random.seed(seed)
            except ImportError:
                logger.warning("CuPy未安装，GPU后端随机种子无法设置。")


    # a. 解析PSO核心超参数
    n_particles = config['n_particles']
    max_iterations = config['max_iterations']
    w_strategy = config.get('w_strategy', ('linear_decay', 0.9, 0.4))
    c1 = config['c1']
    c2 = config['c2']

    # b. 解析模式与策略
    loss_function_mode = config['loss_function_mode']
    boundary_strategy_name = config['boundary_strategy']
    early_stopping_mode = config['early_stopping_mode']
    enable_early_stopping = config.get('enable_early_stopping', True)

    # c. 解析上下文参数
    parameters_boundaries = config['parameters_boundaries']
    param_names = list(parameters_boundaries.keys())
    n_dims = len(param_names)

    # 从 parameters_boundaries 提取硬边界，构造成 (n_dims, 2) 的NumPy数组
    hard_boundaries = np.array([
        [parameters_boundaries[p][0], parameters_boundaries[p][3]] for p in param_names
    ])

     # d. 解析特定模式的参数 (使用.get()以安全处理缺失)
    initial_center = config.get('initial_center')       # finetune
    narrow_boundaries = config.get('narrow_boundaries') # finetune
    parameter_sigmas = config.get('parameter_sigmas')   # finetune

    # e. 解析参考值 (由上层注入到config中)
    t2_p50_ref = config['t2_p50_ref']
    phit_nmr_ref = config['phit_nmr_ref']

    # --- 2. 初始化计算服务和数据 ---
    # 目标：根据配置获取计算服务，并将所有训练数据准备好并移动到目标后端。
    logger.info(f"SWIFT-PSO optimizer using {backend_service.name} backend.")

    # a. 准备一个包含所有训练数组的字典
    # 这是为了与向量化的损失函数接口对齐
    curve_map = config.get('curve_to_columns_map', {})
    t2_value_columns = curve_map.get('T2_VALUE', [])

    train_data_arrays = {
        'phit_nmr': training_set['PHIT_NMR'].to_numpy(),
        't2lm': training_set['T2LM'].to_numpy(),
        'pzi': training_set['PZI'].to_numpy(), # 新增：传递PZI数据
        'dt2_p50': training_set['DT2_P50'].to_numpy(),
        'dphit_nmr': training_set['DPHIT_NMR'].to_numpy(),
        'k_label': training_set['K_LABEL'].to_numpy(),
        'sample_types': training_set['K_LABEL_TYPE'].to_numpy(), # 新增：传递样本类型
        't2_value_array': training_set[t2_value_columns].to_numpy(),
        't2_time': config['t2_time'],
        # 新增：为损失函数注入惩罚项超参数
        'lambda_penalty': config.get('lambda_penalty', 0.5),
        'k_penalty_thresh': config.get('k_penalty_thresh', 1.0),
        # 新增：为损失函数注入PZI加权超参数
        'w_prod': config.get('w_prod', 1.0),
        'w_nonprod': config.get('w_nonprod', 0.1),
        't2_p50_ref': t2_p50_ref, 'phit_nmr_ref': phit_nmr_ref,
        'param_keys': param_names,
        'parameters_boundaries': parameters_boundaries,  # 新增：将边界字典加入训练数据包
        't2_range_min': config.get('t2_range_min'),  # 新增：T2范围最小值
        't2_range_max': config.get('t2_range_max'),   # 新增：T2范围最大值
        # 新增：为损失函数注入参数重组所需信息 (这些键由facade注入到pso_config中)
        'optimization_params': config.get('optimization_params', []),
        'fixed_params': config.get('fixed_params', {}),
        'all_param_names_ordered': config.get('all_param_names_ordered', [])
    }
    # b. 将所有数据一次性移动到目标后端 (CPU或GPU)
    train_data_backend = {
        k: backend_service.to_backend(v) if isinstance(v, np.ndarray) else v
        for k, v in train_data_arrays.items()
    }
    # 目标：根据不同的优化阶段（lowo 或 finetune）创建和初始化粒子群。

    # a. 初始化粒子位置 (particles_pos)
    if loss_function_mode == 'bootstrap':
        # Bootstrap+LOWO 阶段: 在全局硬边界内进行均匀随机初始化
        hard_boundaries_backend = backend_service.to_backend(hard_boundaries)
        lows_backend = hard_boundaries_backend[:, 0]
        highs_backend = hard_boundaries_backend[:, 1]
        particles_pos = backend_service.random.uniform(lows_backend, highs_backend, size=(n_particles, n_dims))
        particles_vel = backend_service.zeros((n_particles, n_dims), dtype=particles_pos.dtype)
    elif loss_function_mode == 'finetune':
        # Fine-Tuning 阶段: 在窄窗内，围绕热启动中心点进行集中式初始化
        # 使用正态分布，标准差为第一阶段统计标准差的一小部分，以实现“集中”
        init_std = parameter_sigmas * 0.1
        # 在CPU上生成，然后移动到后端
        cpu_pos = initial_center + np.random.normal(
            loc=0.0, scale=init_std, size=(n_particles, n_dims)
        )
        # 确保初始化后的粒子仍在窄窗硬边界内
        cpu_pos = np.clip(
            cpu_pos, narrow_boundaries[:, 0], narrow_boundaries[:, 1]
        )
        particles_pos = backend_service.to_backend(cpu_pos)
        particles_vel = backend_service.zeros_like(particles_pos)
    else:
        raise ValueError(f"未知的 loss_function_mode: {loss_function_mode}")

    # c. 初始化个体最优 (pbest)
    pbest_pos = particles_pos.copy()
    # 首次计算损失，完全并行化
    pbest_loss = compute_loss_vectorized(
        pbest_pos, train_data_backend, loss_function_mode, backend_service
    )

    # d. 初始化全局最优 (gbest)
    # 使用 backend_service 提供的统一接口
    best_particle_idx = backend_service.argmin(pbest_loss)
    gbest_loss = pbest_loss[best_particle_idx]
    gbest_pos = pbest_pos[best_particle_idx].copy()

    # e. 初始化历史记录
    gbest_loss_history = []
    # 仅在 'bootstrap' (lowo) 模式下为t-SNE可视化准备轨迹数据
    gbest_pos_history_for_tsne = [] if loss_function_mode == 'bootstrap' else None


    # --- 3. 主优化循环 ---
    # 目标：执行PSO的核心迭代过程，直到满足终止条件。
    t = -1 # 为t提供一个初始值
    for t in range(max_iterations):
        # a. 评估适应度 (Fitness Evaluation)
        # 【关键改造】用一次并行调用替换N次循环
        current_losses = compute_loss_vectorized(
            particles_pos, train_data_backend, loss_function_mode, backend_service
        )

        # b. 更新个体最优 (pbest)
        # 使用 backend_service 提供的统一接口
        update_mask = current_losses < pbest_loss
        pbest_pos = backend_service.where(update_mask[:, None], particles_pos, pbest_pos)
        pbest_loss = backend_service.where(update_mask, current_losses, pbest_loss)

        # c. 更新全局最优 (gbest)
        best_particle_idx = backend_service.argmin(pbest_loss)
        current_best_loss = pbest_loss[best_particle_idx]

        # 关键修复：在GPU上，`if`语句不能直接评估CuPy布尔数组。
        # 必须使用 as_scalar() 方法将0维数组（标量）转换为Python原生类型进行判断。
        if backend_service.as_scalar(current_best_loss) < backend_service.as_scalar(gbest_loss):
            gbest_loss = current_best_loss
            gbest_pos = pbest_pos[best_particle_idx].copy()

        # d. 记录历史数据
        gbest_loss_history.append(backend_service.as_scalar(gbest_loss))
        # (t-SNE数据生成)
        if gbest_pos_history_for_tsne is not None and t % 10 == 0:
            gbest_pos_history_for_tsne.append(backend_service.to_cpu(gbest_pos))

        # e. 检查早停 (Early Stopping)
        if enable_early_stopping:
            if check_early_stopping(np.array(gbest_loss_history), t, early_stopping_mode):
                print(f"INFO: 早停条件在第 {t+1} 代触发。")
                break

        # f. 更新速度和位置
        # 根据策略计算惯性权重 w
        if w_strategy[0] == 'linear_decay':
            w = w_strategy[1] - (w_strategy[1] - w_strategy[2]) * (t / max_iterations)
        else: # 默认或固定权重
            w = w_strategy[0]

        # 向量化更新速度和位置
        r1, r2 = backend_service.random.rand(2, *particles_pos.shape)
        particles_vel = (
            w * particles_vel +
            c1 * r1 * (pbest_pos - particles_pos) +
            c2 * r2 * (gbest_pos - particles_pos)
        )
        particles_pos = particles_pos + particles_vel

        # g. 处理边界 (Boundary Handling)
        # 【关键改造】用一次并行调用替换N次循环
        if boundary_strategy_name == 'hard_reflection':
            particles_pos, particles_vel = handle_boundary_hard_reflection_vectorized(
                particles_pos, particles_vel, backend_service.to_backend(hard_boundaries), backend_service
            )
        elif boundary_strategy_name == 'boundary_jitter':
            particles_pos = handle_boundary_jitter_vectorized(
                particles_pos, backend_service.to_backend(narrow_boundaries),
                backend_service.to_backend(parameter_sigmas), backend_service
            )
        else:
            raise ValueError(f"未知的 boundary_strategy: {boundary_strategy_name}")


    # --- 4. 返回结果 ---
    # 目标：在优化循环结束后，返回最终找到的最优解和相关的历史数据。
    logger.info(f"PSO 优化完成。最终迭代次数: {t+1}, 最优损失: {backend_service.as_scalar(gbest_loss):.6f}")

    # 将最终的参数向量转换为参数名到值的字典，提升可读性
    final_params_cpu = backend_service.to_cpu(gbest_pos)
    final_params_dict = dict(zip(param_names, final_params_cpu))

    # 根据模式返回相应的结果
    # Fine-Tuning 模式下 gbest_pos_history_for_tsne 为 None
    return final_params_dict, gbest_loss_history, gbest_pos_history_for_tsne

def run_bootstrap_lowo_phase(
        all_wells_data: dict,
        config: dict,
        backend_service: BackendService,
        on_fold_complete: Optional[Callable] = None,
        ) -> Tuple[list, list, list, list]:
    """
    执行SWIFT-PSO的第一阶段：Bootstrap + LOWO 双重循环。

    此函数负责执行双重扰动的交叉验证流程，为每个扰动组合调用PSO优化器，
    并收集结果以供下一阶段使用。

    Args:
        all_wells_data (dict): 包含所有井数据的字典 {well_id: data}。
        config (dict): 包含流程所需全部配置的字典。
            此函数期望 `config['pso_config_lowo']` 字典已由上层调用者
            完整填充，包含PSO超参数、目标函数模式、边界、早停策略以及
            T2谱相关参数('t2_time', 't2_range_min', 't2_range_max')。
        backend_service (BackendService): 用于计算的后端服务实例。
        on_fold_complete (Optional[Callable]): 一个可选的回调函数，
            在每次LOWO折叠完成后被调用，用于处理中间结果。

    Returns:
        tuple: (all_theta_hats, all_mu_rmse_per_b, all_thetas_per_b_star, all_gbest_histories)
               - all_theta_hats: 所有 B*W_N 次运行得到的最优参数集列表。
               - all_mu_rmse_per_b: 每轮Bootstrap的平均验证误差列表。
               - all_thetas_per_b_star: 最佳轮次B*对应的W_N组参数。
               - all_gbest_histories: 所有 B*W_N 次运行的全局最优位置演化轨迹。
    """
    B = config['bootstrap_iterations']
    well_ids = list(all_wells_data.keys())
    W_N = len(well_ids)

    all_theta_hats = []
    all_mu_rmse_per_b = []
    all_gbest_histories = []

    # 初始化一个随机数生成器，以确保Bootstrap抽样的可复现性
    seed = config.get('random_seed')
    rng = np.random.default_rng(seed)

    # 外层循环: Bootstrap
    for b in range(B):
        print(f"--- Bootstrap-Runde {b+1}/{B} ---")
        current_bootstrap_val_errors = []

        # 内层循环: Leave-One-Well-Out (LOWO)
        for f in range(W_N):
            # 1. 划分训练集和验证集
            val_well_id = well_ids[f]
            train_well_ids = [wid for wid in well_ids if wid != val_well_id]
            validation_set = all_wells_data[val_well_id]

            # 2. 对训练集进行井内Bootstrap抽样
            train_bootstrap_set = []
            bootstrap_sample_ratio = config.get('bootstrap_sample_ratio', 0.8)
            for train_id in train_well_ids:
                well_data = all_wells_data[train_id]
                n_j = int(np.ceil(bootstrap_sample_ratio * len(well_data)))
                bootstrap_indices = rng.choice(well_data.index, size=n_j, replace=True)
                train_bootstrap_set.append(well_data.loc[bootstrap_indices])
            training_set = pd.concat(train_bootstrap_set)

            # 3. 运行PSO优化器
            # pso_config_lowo 包含了所有需要的静态和上下文参数
            pso_config = config['pso_config_lowo']
            pso_config['random_seed'] = seed # 注入种子
            theta_hat_b_f, loss_history, gbest_history = pso_optimizer(
                training_set=training_set,
                config=pso_config,
                backend_service=backend_service
            )

            # 执行回调函数（如果提供），用于保存每折的详细日志
            if on_fold_complete:
                loss_history_df = pd.DataFrame({
                    "iteration": range(len(loss_history)), "loss": loss_history
                })
                on_fold_complete(
                    bootstrap_run=b,
                    lowo_fold=f,
                    optimal_params=theta_hat_b_f,
                    loss_history_df=loss_history_df
                )

            # 4. 在验证集上评估并存储结果。
            #    验证在CPU上进行，以避免移动大的验证集数据到GPU，并确保与Pandas的兼容性。
            #    theta_hat_b_f 包含N个优化参数，需要与固定参数合并以进行验证。
            fixed_params_for_val = pso_config.get('fixed_params', {})

            # 关键修复：为验证步骤重组完整的10维参数字典
            full_params_for_val = fixed_params_for_val.copy()
            full_params_for_val.update(theta_hat_b_f)

            t2_time_cpu = backend_service.to_cpu(pso_config['t2_time'])

            # 强制在CPU上进行验证计算
            cpu_service = create_backend_service_by_name('cpu')

            k_pred_val = calculate_foster_nmr_permeability_for_val(
                parameters=full_params_for_val,
                well_data=validation_set,
                t2_time=t2_time_cpu,
                config={'curve_to_columns_map': pso_config['curve_to_columns_map']},
                t2_p50_ref=pso_config['t2_p50_ref'],
                phit_nmr_ref=pso_config['phit_nmr_ref'],
                t2_range_min=pso_config.get('t2_range_min'),
                t2_range_max=pso_config.get('t2_range_max'),
                backend_service=cpu_service
            )
            # 修正调用，传入PZI和新的权重参数
            rmse_val = calculate_rmse(
                k_pred=k_pred_val,
                k_label=validation_set['K_LABEL'],
                pzi=validation_set['PZI'],
                w_prod=pso_config.get('w_prod', 1.0),
                w_nonprod=pso_config.get('w_nonprod', 0.1)
            )

            all_theta_hats.append(theta_hat_b_f)
            current_bootstrap_val_errors.append(rmse_val)
            # 使用字典结构化存储，便于后续分析
            if gbest_history is not None:
                all_gbest_histories.append({
                    'bootstrap_run': b,
                    'lowo_fold': f,
                    'history': gbest_history
                })


        # 5. 计算本轮Bootstrap的平均验证误差
        mu_rmse_b = np.mean(current_bootstrap_val_errors)
        # 移除冗余的thetas存储，只记录轮次索引
        all_mu_rmse_per_b.append({'mu_rmse': mu_rmse_b, 'bootstrap_run_index': b})

    # 找到最佳轮次 (b_star) 及其对应的参数
    # 根据最小平均验证误差找到最佳轮次的索引
    best_run_index = min(all_mu_rmse_per_b, key=lambda x: x['mu_rmse'])['bootstrap_run_index']
    # 从所有参数历史中切片出最佳轮次对应的 W_N 组参数
    start_index = best_run_index * W_N
    all_thetas_per_b_star = all_theta_hats[start_index : start_index + W_N]

    return all_theta_hats, all_mu_rmse_per_b, all_thetas_per_b_star, all_gbest_histories


def run_finetuning_phase(
        all_wells_data: dict,
        all_theta_hats: list,
        all_thetas_per_b_star: list,
        config: dict,
        backend_service: BackendService)->Tuple[dict, pd.DataFrame, dict]:
    """
    执行SWIFT-PSO的第二阶段：Warm-Start 与 Fine-Tuning。

    Args:
        all_wells_data (dict): 包含所有井数据的字典。
        all_theta_hats (list): 第一阶段产出的所有参数集。
        all_thetas_per_b_star (list): 第一阶段最佳轮次产出的W_N个参数集。
        config (dict): 流程配置。此函数期望 `config['pso_config_finetune']`
            已由上层调用者填充了静态超参数和全局上下文参数（如边界和T2谱信息）。
            本函数将在此基础上，计算并填充此阶段特有的动态参数（如窄窗边界、
            初始中心等）。
        backend_service (BackendService): 用于计算的后端服务实例。

    Returns:
        Tuple[dict, pd.DataFrame, dict]:
            - theta_final (dict): 最终优化得到的参数向量。
            - ft_loss_history_df (pd.DataFrame): Fine-Tuning阶段的收敛历史。
            - warm_start_params (dict): 用于启动Fine-Tuning的热启动参数。
    """
    # 从配置中获取优化的参数名列表，以确保后续操作的顺序一致性
    # 这是N个优化参数的列表
    param_names = config['optimization_params']

    # 1. 将参数历史从目标后端转换回CPU，以进行统计分析。
    #    这是为了确保统计计算（如np.mean, np.std）的稳定性和通用性。
    #    使用 param_names 保证按键取值，避免依赖字典顺序
    all_thetas_per_b_star_cpu = [
        [backend_service.to_cpu(d[p_name]) for p_name in param_names] for d in all_thetas_per_b_star
    ]
    all_theta_hats_cpu = [
        [backend_service.to_cpu(d[p_name]) for p_name in param_names] for d in all_theta_hats
    ]

    # 将Python列表转换为NumPy数组以进行向量化统计
    # 数组维度现在是 (n_runs, N)，其中N是优化参数的数量
    all_thetas_per_b_star_arr = np.array(all_thetas_per_b_star_cpu)
    all_theta_hats_arr = np.array(all_theta_hats_cpu)

    # 2. 计算Warm-Start参数 (theta_hat_star) 和窄窗边界
    theta_hat_star = np.mean(all_thetas_per_b_star_arr, axis=0)

    # 计算所有参数在第一阶段的整体标准差
    parameter_sigmas = np.std(all_theta_hats_arr, axis=0)
    k = config['narrow_window_factor'] # k=0.3

    # 获取原始硬边界以确保窄窗不会超出物理约束。
    # 注意：此时传入的parameters_boundaries已经是被过滤后的N维边界
    parameters_boundaries = config['pso_config_lowo']['parameters_boundaries']
    hard_boundaries = np.array([
        [parameters_boundaries[p][0], parameters_boundaries[p][3]] for p in param_names
    ])

    # 计算窄窗边界，并与硬边界取交集以确保物理约束
    narrow_boundaries = []
    for i, (p, sigma) in enumerate(zip(theta_hat_star, parameter_sigmas)):
        param_name = param_names[i]

        # 计算理想的窄窗边界
        narrow_lower = p - k * sigma
        narrow_upper = p + k * sigma

        # 与硬边界取交集，确保不超出物理约束
        hard_lower, hard_upper = hard_boundaries[i]
        final_lower = max(narrow_lower, hard_lower)
        final_upper = min(narrow_upper, hard_upper)

        # 确保边界区间有效（下边界 ≤ 上边界）
        if final_lower > final_upper:
            # 如果窄窗与硬边界没有交集，使用硬边界
            final_lower = hard_lower
            final_upper = hard_upper



        narrow_boundaries.append([final_lower, final_upper])

    narrow_boundaries = np.array(narrow_boundaries)

    # 3. 准备全量训练数据
    full_training_set = pd.concat(all_wells_data.values())

    # 4. 运行Fine-Tuning PSO
    # 获取已包含静态和上下文参数的配置
    ft_config = config['pso_config_finetune']
    # 填充本阶段动态计算出的参数
    ft_config['narrow_boundaries'] = narrow_boundaries
    ft_config['initial_center'] = theta_hat_star
    ft_config['parameter_sigmas'] = parameter_sigmas # 用于Jitter策略
    ft_config['random_seed'] = config.get('random_seed') # 注入种子

    try:
        theta_final, ft_loss_history, _ = pso_optimizer(
            training_set=full_training_set,
            config=ft_config,
            backend_service=backend_service
        )

        ft_loss_history_df = pd.DataFrame({
            "iteration": range(len(ft_loss_history)),
            "loss": ft_loss_history
        })

        # 准备热启动参数字典以供返回
        warm_start_params = dict(zip(param_names, theta_hat_star))

        logger.info(f"Fine-Tuning阶段完成，最终损失: {ft_loss_history[-1]:.6f}")
        return theta_final, ft_loss_history_df, warm_start_params

    except Exception as e:
        logger.error(f"Fine-Tuning阶段失败: {e}", exc_info=True)
        # 在失败时返回一个安全的值
        return {}, pd.DataFrame(), {}

def run(
        all_wells_data: dict,
        config: dict,
        backend_service: BackendService,
        on_fold_complete: Optional[Callable] = None,
        )->Dict[str, Any]:
    """
    SWIFT-PSO主控函数。

    本函数是整个SWIFT-PSO流程的顶层协调器，负责依次执行两个核心阶段：
    1. Bootstrap+LOWO阶段：进行全局参数探索。
    2. Fine-Tuning阶段：利用第一阶段的结果进行局部精细微调。

    职责划分:
    - 上层调用者 (如主实验脚本): 负责在 `config` 字典中提供所有**静态超参数**
      和**上下文参数**。
    - 本函数 (`run_swift_pso`): 负责协调执行流程，将完整的 `config` 对象
      传递给子阶段函数。

    Args:
        all_wells_data (dict): 包含所有井数据的字典。
        config (dict): 包含流程所需全部配置的字典。上层调用者必须确保此字典
            具备以下结构和内容：
            {
                "bootstrap_iterations": int,
                "narrow_window_factor": float,

                "pso_config_lowo": {
                    # ... lowo阶段的配置 ...
                },

                "pso_config_finetune": {
                    # ... finetune阶段的配置 ...
                }
            }
        backend_service (BackendService): 用于计算的后端服务实例。
        on_fold_complete (Optional[Callable]): 传递给 `run_bootstrap_lowo_phase` 的回调函数。

    Returns:
        Dict[str, Any]: 包含所有训练结果的字典，包括：
            - "model_assets": 包含最终参数和上下文的模型资产字典
            - "all_optimized_parameters": 所有LOWO参数的DataFrame
            - "convergence_history_finetune": Fine-Tuning收敛历史DataFrame
            - "bootstrap_summary": Bootstrap阶段的评估摘要
            - "warm_start_params": Fine-Tuning阶段的热启动参数
    """
    # --- 阶段一 ---
    # 调用子阶段函数，直接传递完整的config对象
    all_theta_hats, all_mu_rmse_per_b, thetas_b_star, all_gbest_histories = run_bootstrap_lowo_phase(
        all_wells_data,
        config,
        backend_service,
        on_fold_complete=on_fold_complete
    )

    # 准备t-SNE源数据
    param_names = config['optimization_params']
    all_optimized_parameters_df = _prepare_tsne_source_data(all_gbest_histories, param_names)

    # --- 阶段二 ---
    theta_final, ft_convergence_history, warm_start_params = run_finetuning_phase(
        all_wells_data,
        all_theta_hats,
        thetas_b_star,
        config,
        backend_service,
    )

    logger.info("SWIFT-PSO 优化完成。")

    # --- 新增：构建新的 model_assets 结构 ---
    fixed_params = config.get('fixed_params', {})

    # 合并参数，为预测步骤提供便利
    full_final_params = fixed_params.copy()
    full_final_params.update(theta_final)

    model_assets = {
        "optimized_parameters": theta_final,
        "fixed_parameters": fixed_params,
        "parameters": full_final_params, # 关键：为下游提供完整的参数集
        "context": {
            "t2_p50_ref": config['pso_config_lowo']['t2_p50_ref'],
            "phit_nmr_ref": config['pso_config_lowo']['phit_nmr_ref']
        }
    }

    # 返回所有结果
    return {
        "model_assets": model_assets,
        "all_optimized_parameters": all_optimized_parameters_df,
        "convergence_history_finetune": ft_convergence_history,
        "bootstrap_summary": all_mu_rmse_per_b,
        "warm_start_params": warm_start_params,
    }

def _prepare_tsne_source_data(gbest_histories: list, param_names: list) -> pd.DataFrame:
    """准备t-SNE可视化的源数据。

    此函数将所有Bootstrap和LOWO运行的参数演化轨迹合并为一个DataFrame，
    并为每个点添加上下文标签。

    Args:
        gbest_histories (list): 全局最优演化轨迹列表，每个元素是一个包含
                         上下文信息的字典: {'bootstrap_run': b, 'lowo_fold': f, 'history': [...]}
        param_names (list): 参数名称列表

    Returns:
        pd.DataFrame: 用于t-SNE分析的源数据
    """
    if not gbest_histories:
        return pd.DataFrame()

    all_records = []
    for history_item in gbest_histories:
        if not history_item or 'history' not in history_item or history_item['history'] is None:
            continue

        bootstrap_run = history_item['bootstrap_run']
        lowo_fold = history_item['lowo_fold']
        gbest_history = history_item['history']

        for iteration, gbest_pos in enumerate(gbest_history):
            record = {
                'bootstrap_run': bootstrap_run,
                'lowo_fold': lowo_fold,
                'iteration': iteration * 10  # 因为只记录每10次迭代
            }
            record.update(dict(zip(param_names, gbest_pos)))
            all_records.append(record)

    if not all_records:
        return pd.DataFrame()

    return pd.DataFrame(all_records)
