"""
Input validation utilities for petrophysical functions.
"""
import functools
import inspect

from logwp.extras.units import Quantity, DimensionalityError, ureg


def validate_dimensions(**expected_dims):
    """
    A decorator to validate the dimensions of function arguments.

    This ensures that the inputs to petrophysical models are dimensionally
    correct Quantity objects from the `units` package.

    Example:
        @validate_dimensions(Rt='resistivity', Rw='resistivity')
        def calculate_saturation(Rt, Rw, a=1, m=2, n=2):
            ...
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Bind the passed arguments to the function's signature to handle
            # both positional and keyword arguments correctly.
            bound_args = inspect.signature(func).bind(*args, **kwargs)
            bound_args.apply_defaults()

            for param_name, expected_dim_name in expected_dims.items():
                if param_name not in bound_args.arguments:
                    continue  # Argument not provided, skip validation

                arg_value = bound_args.arguments[param_name]

                if not isinstance(arg_value, Quantity):
                    raise TypeError(f"Argument '{param_name}' must be a Quantity object, not {type(arg_value).__name__}.")

                # Get the expected Dimension object from the unit registry
                expected_dimension = getattr(ureg, expected_dim_name)

                if arg_value.dimension != expected_dimension:
                    raise DimensionalityError(f"Argument '{param_name}' has incorrect dimension. Expected '{expected_dim_name}', but got {arg_value.dimension}.")

            return func(*args, **kwargs)
        return wrapper
    return decorator
