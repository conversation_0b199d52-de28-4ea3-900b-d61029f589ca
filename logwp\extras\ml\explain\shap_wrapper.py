"""
SHAP (SHapley Additive exPlanations) 库的封装器。
"""
from __future__ import annotations

from typing import Any, TYPE_CHECKING
import pandas as pd
import numpy as np

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

if TYPE_CHECKING:
    from matplotlib.axes import Axes

def calculate_shap_values(
    model: Any,
    X: pd.DataFrame,
    explainer_type: str = 'auto'
) -> np.ndarray:
    """
    为给定的模型和数据集计算SHAP值。

    此函数是一个智能封装器，能自动为提供的模型选择最合适的SHAP解释器，
    或允许用户手动指定。

    Args:
        model: 一个已训练的、具有 `predict` 方法的机器学习模型对象。
        X (pd.DataFrame): 用于解释预测的特征矩阵。
        explainer_type (str, optional): 要使用的SHAP解释器类型。
            可选值为 'auto', 'tree', 'kernel'。默认为 'auto'。

    Returns:
        np.ndarray: SHAP值的数组。对于多分类问题，这将是一个数组列表，
                    每个类别对应一个数组。
    """
    if not SHAP_AVAILABLE:
        raise ImportError("此功能需要 'shap' 库。请运行 'pip install shap' 进行安装。")

    if not isinstance(X, pd.DataFrame):
        raise TypeError("输入数据 X 必须是 pandas DataFrame。")

    explainer = None

    # --- 解释器选择逻辑 ---
    if explainer_type == 'auto':
        # 简单的启发式规则：如果模型有 'feature_importances_' 属性，则很可能是树模型。
        if hasattr(model, 'feature_importances_'):
            explainer = shap.TreeExplainer(model)
        else:
            # 回退到与模型无关但速度较慢的 KernelExplainer。
            # 使用一个经过聚类的背景数据集来提升性能。
            background_data = shap.kmeans(X, 10)
            explainer = shap.KernelExplainer(model.predict, background_data)
    elif explainer_type == 'tree':
        explainer = shap.TreeExplainer(model)
    elif explainer_type == 'kernel':
        background_data = shap.kmeans(X, 10)
        explainer = shap.KernelExplainer(model.predict, background_data)
    else:
        raise ValueError(f"不支持的 explainer_type: '{explainer_type}'。 "
                         "支持的值为 'auto', 'tree', 'kernel'。")

    # --- 计算 SHAP 值 ---
    shap_values = explainer.shap_values(X)

    return shap_values


def plot_shap_summary(
    shap_values: np.ndarray,
    X: pd.DataFrame,
    ax: "Axes | None" = None,
    **kwargs: Any
) -> "Axes":
    """
    绘制一个SHAP摘要图（通常是蜜蜂蜂群图），以可视化特征的重要性。

    此图显示了每个特征对模型输出的贡献。每个点代表一个样本，
    其在x轴上的位置表示该特征对该样本的SHAP值（影响大小和方向），
    颜色表示特征值本身的大小（高或低）。

    Args:
        shap_values (np.ndarray): 从 `calculate_shap_values` 函数获得的SHAP值数组。
        X (pd.DataFrame): 用于解释的原始特征矩阵。
        ax (matplotlib.axes.Axes | None, optional): 要在其上绘图的现有matplotlib轴对象。
                                                    如果为None，将创建一个新的图和轴。
                                                    默认为 None。
        **kwargs: 其他传递给 `shap.summary_plot` 的关键字参数。

    Returns:
        matplotlib.axes.Axes: 包含了摘要图的matplotlib轴对象。
    """
    if not SHAP_AVAILABLE or not MATPLOTLIB_AVAILABLE:
        raise ImportError(
            "此功能需要 'shap' 和 'matplotlib' 库。请运行 'pip install shap matplotlib' 进行安装。"
        )

    if not isinstance(X, pd.DataFrame):
        raise TypeError("输入数据 X 必须是 pandas DataFrame。")
    if shap_values.shape[0] != X.shape[0]:
        raise ValueError("shap_values 和 X 的样本数量（行数）必须一致。")

    # 如果没有提供ax，则创建一个
    if ax is None:
        fig, ax = plt.subplots(figsize=kwargs.pop('figsize', (8, 6)))
    else:
        fig = ax.get_figure()

    # SHAP的summary_plot函数有一个show参数，我们不希望它自动显示
    kwargs.setdefault('show', False)

    # 调用SHAP的绘图函数
    shap.summary_plot(shap_values, X, ax=ax, **kwargs)

    # 增强图表的可读性
    fig.tight_layout()

    return ax
