"""scape.core.obmiq.prediction_facade - OBMIQ预测步骤门面

本模块提供了OBMIQ预测步骤的公共入口函数 `run_obmiq_prediction_step`。

Architecture
------------
层次/依赖: scape/core/obmiq层，是外部工作流调用OBMIQ预测功能的唯一入口
设计原则: 遵循《可追踪机器学习组件开发框架》的门面模式，负责编排预测流程

Functions:
    run_obmiq_prediction_step: 执行完整的预测工作流

References:
    - 《scape_core_obmiq_pytorch版开发计划.md》§2.2, §4
"""
from __future__ import annotations
from pathlib import Path
from logwp.models.curve import CurveExpansionMode
from typing import Any, Dict, List, Tuple

import numpy as np
import pandas as pd
import torch
from logwp.infra import get_logger
from logwp.extras.plotting import registry
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.extras.nmr.resampling import resample_t2_spectrum
from logwp.extras.tracking import RunContext

from .config import ObmiqPredictionConfig
from .constants import ObmiqPlotProfiles, ObmiqPredictionArtifacts
from .artifact_handler import ObmiqArtifactHandler
from .internal.plotting_utils import generate_and_register_plots
from .internal.model_builder import OBMIQPyTorchModel
from .plotting import (
    replot_crossplot,
    replot_residuals_hist,
    replot_residuals_plot,
)

logger = get_logger()


def _preprocess_prediction_data(
    prediction_bundle: WpDataFrameBundle,
    source_t2_axis: np.ndarray,
    assets: Dict[str, Any],
) -> Dict[str, np.ndarray]:
    """对预测数据进行预处理。"""
    metadata = assets["metadata"]
    preprocessors = assets["preprocessors"]

    df = prediction_bundle.data

    # 1. 从模型资产中读取“逻辑”曲线名
    sequence_feature = metadata["sequence_feature"]
    normalization_feature = metadata["normalization_feature"]
    tabular_features = metadata["tabular_features"]
    standard_t2_axis = metadata["standard_t2_time_axis"]

    # 2. 使用预测数据的元数据，将逻辑名实时解析为物理列名
    try:
        seq_cols = prediction_bundle.curve_metadata.expand_curve_names(
            [sequence_feature], mode=CurveExpansionMode.DATAFRAME
        )
        norm_col = prediction_bundle.curve_metadata.expand_curve_names(
            [normalization_feature], mode=CurveExpansionMode.DATAFRAME
        )[0]
        tab_cols = prediction_bundle.curve_metadata.expand_curve_names(
            tabular_features, mode=CurveExpansionMode.DATAFRAME
        )
    except Exception as e:
        raise ValueError(
            "无法在预测数据中解析模型所需的曲线名。请确保预测Bundle的元数据正确。"
        ) from e

    logger.info("成功将模型所需的逻辑曲线名解析为预测数据的物理列名。")

    # 3. T2谱重采样
    original_sequences = df[seq_cols].values
    resampled_sequences = resample_t2_spectrum(
        original_sequences, source_t2_axis, standard_t2_axis
    )

    # 4. 序列数据逐样本归一化
    norm_values = df[norm_col].values[:, np.newaxis]
    norm_values[norm_values == 0] = 1e-6
    normalized_sequences = resampled_sequences / norm_values

    # 5. 表格数据标准化
    tabular_scaler = preprocessors["tabular_scaler"]
    scaled_tabular = tabular_scaler.transform(df[tab_cols])

    # 6. 验证重采样后的序列长度是否正确
    if normalized_sequences.shape[1] != standard_t2_axis.shape[0]:
        logger.warning(f"Resampled sequence length {normalized_sequences.shape[1]} does not match standard T2 axis length {standard_t2_axis.shape[0]}.")

    return {
        "sequence_input": normalized_sequences.astype(np.float32),
        "tabular_input": scaled_tabular.astype(np.float32),
    }

def _validate_prediction_inputs(
    model_assets: Dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    source_t2_time_axis: np.ndarray,
    output_curve_names: Tuple[str, str],
):
    """Validates the inputs to the `run_obmiq_prediction_step` function."""
    if not isinstance(model_assets, dict) or not all(
        k in model_assets for k in ["model_state_dict", "model_hyperparameters", "metadata"]
    ):
        raise ValueError("model_assets must be a dictionary with 'model_state_dict', 'model_hyperparameters', and 'metadata' keys.")

    if not isinstance(prediction_bundle, WpDataFrameBundle):
        raise TypeError("prediction_bundle must be a WpDataFrameBundle instance.")

    if not isinstance(source_t2_time_axis, np.ndarray):
        raise TypeError("source_t2_time_axis must be a NumPy array.")

    if len(output_curve_names) != 2 or not all(isinstance(s, str) for s in output_curve_names):
        raise ValueError("output_curve_names must be a tuple of two strings.")

    logger.debug("All prediction input parameters validation passed.")


def _generate_and_save_evaluation_artifacts(
    ctx: RunContext,
    step_dir: Path,
    prediction_df: pd.DataFrame,
    predictions_path: Path,
    actual_target_cols: List[str],
    output_curve_names: Tuple[str, str],
):
    """计算残差，生成并保存所有评估图表和相关产物。

    这是一个辅助函数，旨在保持主预测流程的整洁。
    """
    # 计算残差
    pred_cols = list(output_curve_names)
    for i, col in enumerate(actual_target_cols):
        prediction_df[f"{col}_residual"] = prediction_df[col] - prediction_df[pred_cols[i]]

    # 关键修复：在生成图表前，将包含残差列的DataFrame重新保存到快照路径
    # 这样，后续的绘图函数才能加载到包含所有必需列的最新数据。
    handler = ObmiqArtifactHandler()
    handler.save_dataframe(prediction_df, predictions_path)
    logger.info(f"Updated prediction snapshot with residual columns at: {predictions_path}")

    # 绘制所有评估图表
    target_1, target_2 = actual_target_cols[0], actual_target_cols[1]
    plot_map = {
        ObmiqPredictionArtifacts.CROSSPLOT_DT2_P50: (
            replot_crossplot,
            {"actual_col": target_1, "predicted_col": pred_cols[0]},
            ObmiqPlotProfiles.CROSSPLOT,
        ),
        ObmiqPredictionArtifacts.CROSSPLOT_DPHIT_NMR: (
            replot_crossplot,
            {"actual_col": target_2, "predicted_col": pred_cols[1]},
            ObmiqPlotProfiles.CROSSPLOT,
        ),
        ObmiqPredictionArtifacts.RESIDUALS_PLOT_DT2_P50: (
            replot_residuals_plot,
            {"predicted_col": pred_cols[0], "residual_col": f"{target_1}_residual"},
            ObmiqPlotProfiles.RESIDUALS_PLOT,
        ),
        ObmiqPredictionArtifacts.RESIDUALS_PLOT_DPHIT_NMR: (
            replot_residuals_plot,
            {"predicted_col": pred_cols[1], "residual_col": f"{target_2}_residual"},
            ObmiqPlotProfiles.RESIDUALS_PLOT,
        ),
        ObmiqPredictionArtifacts.RESIDUALS_HIST_DT2_P50: (
            replot_residuals_hist,
            {"residual_col": f"{target_1}_residual"},
            ObmiqPlotProfiles.RESIDUALS_HIST,
        ),
        ObmiqPredictionArtifacts.RESIDUALS_HIST_DPHIT_NMR: (
            replot_residuals_hist,
            {"residual_col": f"{target_2}_residual"},
            ObmiqPlotProfiles.RESIDUALS_HIST,
        ),
    }

    generate_and_register_plots(
        ctx, step_dir, plot_map, predictions_path, "Prediction evaluation plot"
    )


def run_obmiq_prediction_step(
    config: ObmiqPredictionConfig,
    ctx: RunContext,
    model_assets: Dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    *,
    source_t2_time_axis: np.ndarray,
    output_curve_names: Tuple[str, str],
) -> Dict[str, Any]:
    """使用训练好的OBMIQ PyTorch模型进行预测。

    Args:
        config: 预测步骤的Pydantic配置对象。
        ctx: 当前运行的上下文，用于追踪。
        model_assets: 由训练步骤产出的模型资产包字典。
        prediction_bundle: 包含待预测数据的WpDataFrameBundle。
        source_t2_time_axis: 待预测数据的原始T2时间轴。
        output_curve_names: 两个预测目标输出的曲线名称。

    Returns:
        一个包含状态信息的字典。
    """
    logger.info("===== OBMIQ Prediction Step (PyTorch) Started =====")
    step_dir = ctx.get_step_dir("obmiq_prediction_pytorch")
    device = "cuda" if torch.cuda.is_available() else "cpu"

    # 0. 验证输入参数
    logger.info("Validating prediction inputs...")
    _validate_prediction_inputs(
        model_assets=model_assets,
        prediction_bundle=prediction_bundle,
        source_t2_time_axis=source_t2_time_axis,
        output_curve_names=output_curve_names,
    )

    # 1. 加载模型资产并重建模型
    logger.info("Loading model assets and reconstructing model...")
    model_state_dict = model_assets["model_state_dict"]
    model_hyperparameters = model_assets["model_hyperparameters"]
    data_shapes = model_assets["data_shapes"]

    model = OBMIQPyTorchModel(model_hyperparameters, data_shapes)
    model.load_state_dict(model_state_dict)
    model.to(device).eval()  # 移动到设备并设置为评估模式

    # 2. 数据预处理
    logger.info("Preprocessing prediction data...")
    processed_inputs = _preprocess_prediction_data(
         prediction_bundle, source_t2_time_axis, model_assets
    )

    # 3. 执行预测
    logger.info(f"Performing inference on device: {device}...")
    with torch.no_grad():  # 禁用梯度计算以加速并节省内存
        # processed_inputs["sequence_input"] 是一个 (n_samples, sequence_length) 的NumPy数组。
        # 我们将其转换为PyTorch张量，并在第1维插入一个通道维度(C=1)。
        # 最终形状为 (n_samples, 1, sequence_length)，这与模型1D-CNN分支期望的
        # (N, C, L) 输入格式完全匹配。
        seq_tensor = torch.from_numpy(processed_inputs["sequence_input"]).unsqueeze(1).to(device)
        tab_tensor = torch.from_numpy(processed_inputs["tabular_input"]).to(device)

        predictions_tensor = model(
            {"sequence_input": seq_tensor, "tabular_input": tab_tensor}
        )
        predictions_np = predictions_tensor.cpu().numpy()

    # 4. 后处理与产物生成
    logger.info("Formatting predictions and saving artifacts...")
    prediction_df = prediction_bundle.data.copy()
    prediction_df[list(output_curve_names)] = predictions_np

    # 保存预测结果
    handler = ObmiqArtifactHandler()
    predictions_path = step_dir / "predictions.csv"
    handler.save_dataframe(prediction_df, predictions_path)
    ctx.register_artifact(
        predictions_path.relative_to(ctx.run_dir),
        ObmiqPredictionArtifacts.PREDICTIONS.value,
        description="包含原始输入和模型预测结果的数据集"
    )

    # 5. 如果有真实值，则计算残差并绘制评估图表
    # 使用逻辑名进行检查，以支持列名不一致的预测数据
    logical_target_features = model_assets["metadata"]["target_features"]
    if all(prediction_bundle.curve_metadata.has_curve(c) for c in logical_target_features):
        logger.info("Ground truth found in prediction data. Generating evaluation plots...")

        # 将逻辑目标名解析为预测数据中的物理列名
        actual_target_cols = prediction_bundle.curve_metadata.expand_curve_names(
            logical_target_features, mode=CurveExpansionMode.DATAFRAME
        )

        # 调用私有函数来处理所有评估相关的产物生成
        _generate_and_save_evaluation_artifacts(
            ctx=ctx,
            step_dir=step_dir,
            prediction_df=prediction_df,
            predictions_path=predictions_path,
            actual_target_cols=actual_target_cols,
            output_curve_names=output_curve_names,
        )
    else:
        logger.info("No ground truth found in prediction data. Skipping evaluation plots.")

    logger.info("===== OBMIQ Prediction Step (PyTorch) Finished =====")
    return {"status": "completed", "output_path": str(predictions_path)}
