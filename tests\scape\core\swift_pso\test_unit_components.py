"""tests.scape.core.swift_pso.test_unit_components - SWIFT-PSO单元测试

测试SWIFT-PSO包内最基础、最独立的工具组件，确保它们行为正确。

Architecture
------------
层次/依赖: tests/scape/core/swift_pso层，单元测试
设计原则: 独立性、快速反馈、覆盖核心工具

References
----------
- `docs/DDS/refactor/swift_pso重构.md` §5 - 测试计划
"""

from __future__ import annotations

from pathlib import Path

import numpy as np
import pandas as pd
import pytest
from logwp.extras.plotting import PlotProfile, ProfileNotFoundError, registry
from logwp.extras.tracking import RunContext
from logwp.models.datasets.bundle import WpDataFrameBundle

from scape.core.swift_pso.artifact_handler import SwiftPsoArtifactHandler
from scape.core.swift_pso.config import SwiftPsoTrainingConfig
from scape.core.swift_pso.constants import (
    SwiftPsoTrainingArtifacts,
    TsnePlotProfiles,
)
from scape.core.swift_pso.training_facade import run_swift_pso_training_step
from scape.core.swift_pso.visualization_facade import (
    get_swift_pso_base_profile,
    get_tsne_convergence_profile,
)


class TestSwiftPsoUnitComponents:
    """测试SWIFT-PSO包的核心工具类和独立组件。"""

    def test_artifact_handler_io(self, tmp_path: Path):
        """测试SwiftPsoArtifactHandler的保存/加载方法。"""
        handler = SwiftPsoArtifactHandler()

        # 1. 测试参数 (dict)
        params_path = tmp_path / "params.json"
        original_params = {"alpha": 0.5, "beta": [1, 2], "gamma": "test"}
        handler.save_parameters(original_params, params_path)
        assert params_path.exists()
        loaded_params = handler.load_parameters(params_path)
        assert original_params == loaded_params

        # 2. 测试DataFrame
        df_path = tmp_path / "data.csv"
        original_df = pd.DataFrame({"col_a": [10, 20], "col_b": ["X", "Y"]})
        handler.save_dataframe(original_df, df_path)
        assert df_path.exists()
        loaded_df = handler.load_dataframe(df_path)
        pd.testing.assert_frame_equal(original_df, loaded_df)

        # 3. 测试PlotProfile
        profile_path = tmp_path / "profile.json"
        original_profile = PlotProfile(name="test_profile", figure_props={"figsize": (8, 6)})
        handler.save_plot_profile(original_profile, profile_path)
        assert profile_path.exists()
        loaded_profile = handler.load_plot_profile(profile_path)
        assert original_profile.name == loaded_profile.name
        # JSON序列化会将tuple转换为list，这是正常的
        assert loaded_profile.figure_props["figsize"] == [8, 6]

    def test_plotting_profiles_registration_and_retrieval(self):
        """测试PlotProfile的注册和获取。

        此测试隐式地验证了`scape.core.swift_pso.__init__`是否正确导入了`plot_profiles`模块，
        从而触发了配置的自动注册。
        """
        try:
            # 验证具体图表模板
            profile = get_tsne_convergence_profile()
            assert profile.name == TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value

            # 验证模块级基础模板
            base_profile = get_swift_pso_base_profile()
            assert base_profile.name == TsnePlotProfiles.SWIFT_PSO_BASE.value

        except ProfileNotFoundError as e:
            pytest.fail(
                f"PlotProfile未找到: {e}. "
                "请检查`scape.core.swift_pso.__init__.py`是否导入了`plot_profiles`模块。"
            )

    def test_training_step_produces_artifacts(
        self, run_context: RunContext, swift_pso_train_bundle: WpDataFrameBundle
    ):
        """测试训练步骤是否能成功执行并生成预期的产物。"""
        # 根据方法说明书，动态计算数据依赖性参数
        t2_p50_ref = swift_pso_train_bundle.get_curve_statistic('T2_P50', 'median')
        phit_nmr_ref = swift_pso_train_bundle.get_curve_statistic('DPHIT_NMR', 'median')
        curve_map = swift_pso_train_bundle.curve_to_columns_map
        # 根据方法说明书，显式生成T2时间轴 (0.1ms to 10000ms, 64 bins)
        t2_time = np.logspace(np.log10(0.1), np.log10(10000), 64)

        # 准备共享的数据依赖参数和配置
        parameters_boundaries = SwiftPsoTrainingConfig.create_default().pso_config_lowo["parameters_boundaries"]
        shared_data_params = {
            "t2_p50_ref": t2_p50_ref,
            "phit_nmr_ref": phit_nmr_ref,
            "curve_to_columns_map": curve_map,
            "t2_time": t2_time,
            "t2_range_min": t2_time.min(),
            "t2_range_max": t2_time.max(),
            "parameters_boundaries": parameters_boundaries,
        }

        # 为LOWO和Fine-Tuning阶段创建独立的、具有不同超参数的测试配置
        pso_config_lowo_test = {
            "n_particles": 10,
            "max_iterations": 5,
            "w_strategy": ("linear_decay", 0.9, 0.4),
            "c1": 1.8,
            "c2": 1.8,
            "loss_function_mode": "bootstrap",
            "boundary_strategy": "hard_reflection",
            "enable_early_stopping": True,
            "early_stopping_mode": "lowo",
            **shared_data_params
        }

        pso_config_finetune_test = {
            "n_particles": 8,
            "max_iterations": 3,
            "w_strategy": ("linear_decay", 0.8, 0.3),
            "c1": 1.6,
            "c2": 1.6,
            "loss_function_mode": "finetune",
            "boundary_strategy": "boundary_jitter",
            "enable_early_stopping": True,
            "early_stopping_mode": "finetune",
            **shared_data_params
        }

        # 1. 创建一个最小化的配置以加速测试
        minimal_config = SwiftPsoTrainingConfig(
            bootstrap_iterations=1,  # 仅运行1次Bootstrap
            narrow_window_factor=0.3,
            # 为两个阶段注入各自独立的配置
            pso_config_lowo=pso_config_lowo_test,
            pso_config_finetune=pso_config_finetune_test,
        )

        # 2. 运行训练步骤
        run_swift_pso_training_step(
            config=minimal_config,
            ctx=run_context,
            train_bundle=swift_pso_train_bundle,
            backend="cpu",  # 强制使用CPU以保证测试环境一致性
        )

        # 3. 验证产物
        manifest = run_context.manifest
        assert SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value in manifest["artifacts"]
        assert SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value in manifest["artifacts"]

        final_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.FINAL_PARAMETERS)
        assert final_params_path.exists()
        all_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)
        assert all_params_path.exists()
