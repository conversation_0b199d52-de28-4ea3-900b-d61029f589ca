from __future__ import annotations

from logwp.extras.plotting import PlotProfile, SaveConfig, registry

from .constants import ObmiqBaselinesPlotProfiles


def _create_base_profile() -> PlotProfile:
    """创建OBMIQ Baselines模块的基础绘图配置。"""
    return PlotProfile(
        name=ObmiqBaselinesPlotProfiles.BASE.value,
        rc_params={"font.family": "sans-serif", "font.sans-serif": ["Arial"]},
        figure_props={"figsize": (8, 6), "dpi": 150, "layout": "constrained"},
        title_props={"fontsize": 16, "fontweight": "bold", "pad": 15},
        label_props={"fontsize": 12},
        save_config=SaveConfig(format=["png", "svg"], dpi=300, transparent=False),
    )


def _create_crossplot_profile() -> PlotProfile:
    """创建交会图的绘图配置。"""
    return PlotProfile(
        name=ObmiqBaselinesPlotProfiles.CROSSPLOT.value,
        artist_props={
            "scatter": {"s": 40, "alpha": 0.6, "edgecolor": "w", "linewidth": 0.5},
            "reference_line": {"color": "red", "linestyle": "--", "linewidth": 1.5},
        },
    )


def _create_residuals_plot_profile() -> PlotProfile:
    """创建残差图的绘图配置。"""
    return PlotProfile(
        name=ObmiqBaselinesPlotProfiles.RESIDUALS_PLOT.value,
        artist_props={
            "scatter": {"s": 40, "alpha": 0.6, "edgecolor": "w", "linewidth": 0.5},
            "zero_line": {"color": "red", "linestyle": "--", "linewidth": 1.5},
        },
    )


def _create_residuals_hist_profile() -> PlotProfile:
    """创建残差直方图的绘图配置。"""
    return PlotProfile(
        name=ObmiqBaselinesPlotProfiles.RESIDUALS_HIST.value,
        artist_props={
            "hist": {"bins": 30, "kde": True, "edgecolor": "k"},
            "mean_line": {"color": "red", "linestyle": "--", "linewidth": 2},
            "median_line": {"color": "green", "linestyle": ":", "linewidth": 2},
        },
    )


def _create_feature_importance_profile() -> PlotProfile:
    """创建特征重要性图的绘图配置。"""
    return PlotProfile(
        name=ObmiqBaselinesPlotProfiles.FEATURE_IMPORTANCE_PLOT.value,
        figure_props={"figsize": (10, 8)},
        artist_props={"barplot": {"palette": "viridis"}},
    )


def _create_lowo_cv_crossplot_profile() -> PlotProfile:
    """创建LOWO-CV交会图的绘图配置。"""
    # 该图的样式与常规交会图完全相同，因此我们直接复用其配置，
    # 仅更新名称以进行注册。
    return PlotProfile(
        name=ObmiqBaselinesPlotProfiles.LOWO_CV_CROSSPLOT.value,
        artist_props={
            "scatter": {"s": 40, "alpha": 0.6, "edgecolor": "w", "linewidth": 0.5},
            "reference_line": {"color": "red", "linestyle": "--", "linewidth": 1.5},
        },
    )


# 在模块加载时，立即执行注册
registry.register_base(_create_base_profile())
registry.register(_create_crossplot_profile())
registry.register(_create_residuals_plot_profile())
registry.register(_create_residuals_hist_profile())
registry.register(_create_feature_importance_profile())
registry.register(_create_lowo_cv_crossplot_profile())
