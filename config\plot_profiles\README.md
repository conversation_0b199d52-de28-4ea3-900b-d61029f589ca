# PlotProfile 配置清单

## 配置统计
- 总配置数: 18
- 基础模板数: 5

## 所有配置
- `log_scout.boxplot.json` - log_scout.boxplot (具体配置)
- `log_scout.clustermap.json` - log_scout.clustermap (具体配置)
- `log_scout.heatmap.json` - log_scout.heatmap (具体配置)
- `log_scout.pairplot.json` - log_scout.pairplot (具体配置)
- `log_scout.regplot.json` - log_scout.regplot (具体配置)
- `obmiq.captum_ig_summary.json` - obmiq.captum_ig_summary (具体配置)
- `obmiq.crossplot.json` - obmiq.crossplot (具体配置)
- `obmiq.grad_cam.json` - obmiq.grad_cam (具体配置)
- `obmiq.residuals_hist.json` - obmiq.residuals_hist (具体配置)
- `obmiq.residuals_plot.json` - obmiq.residuals_plot (具体配置)
- `obmiq.shap_summary.json` - obmiq.shap_summary (具体配置)
- `obmiq.training_history.json` - obmiq.training_history (具体配置)
- `swift_pso.tsne_cluster_analysis.json` - swift_pso.tsne_cluster_analysis (具体配置)
- `swift_pso.tsne_convergence.json` - swift_pso.tsne_convergence (具体配置)
- `validation.perm_corr.permeability_crossplot.json` - validation.perm_corr.permeability_crossplot (具体配置)
- `validation.plt.capture_curve.json` - validation.plt.capture_curve (具体配置)
- `validation.plt.contribution_crossplot.json` - validation.plt.contribution_crossplot (具体配置)
- `validation.plt.lorenz_curve.json` - validation.plt.lorenz_curve (具体配置)

## 使用方法

### 查看配置
```bash
cat validation.contribution_crossplot.json
```

### 修改配置
```bash
cp validation.contribution_crossplot.json my_custom.json
# 编辑 my_custom.json
```

### 在代码中使用
```python
from logwp.extras.plotting import registry, PlotProfile

# 方法1: 使用已注册的配置
profile = registry.get("validation.contribution_crossplot")

# 方法2: 从JSON文件加载
profile = PlotProfile.from_json("config/plot_profiles/my_custom.json")
```
