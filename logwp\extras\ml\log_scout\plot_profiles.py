"""Plotting profiles for the LogScout step.

This module defines and registers the default `PlotProfile` templates for all
visualizations produced by LogScout. These templates ensure a consistent and
professional look and feel for all generated charts and can be customized by
the user.

This file follows the best practices outlined in the `logwp.extras.plotting`
framework, including the use of a module-level base profile for inheritance.
"""

from __future__ import annotations

import logging

from logwp.extras.plotting import PlotProfile, SaveConfig, registry

from .constants import LogScoutPlotProfiles

from logwp.infra import get_logger
logger = get_logger(__name__)


def register_log_scout_plot_profiles() -> None:
    """Defines and registers all PlotProfile templates for the LogScout component.

    This function is called automatically when the module is imported.
    """
    if registry.is_registered(LogScoutPlotProfiles.BASE.value):
        logger.debug("LogScout plot profiles already registered. Skipping.")
        return

    logger.debug("Registering LogScout plot profiles...")

    # 1. Define and register the module-level base template.
    # This profile will be inherited by all other profiles in this module.
    log_scout_base = PlotProfile(
        name=LogScoutPlotProfiles.BASE.value,
        rc_params={
            "font.family": "sans-serif",
            "font.sans-serif": ["Arial", "DejaVu Sans"],
            "font.size": 12,
            "axes.grid": True,
            "grid.alpha": 0.5,
            "grid.linestyle": "--",
        },
        figure_props={"figsize": (10, 8), "dpi": 150, "layout": "constrained"},
        save_config=SaveConfig(
            format=["png"], dpi=300, bbox_inches="tight", transparent=False
        ),
    )
    registry.register_base(log_scout_base)

    # 2. Define and register specific plot templates.
    # Each of these will automatically inherit from 'log_scout.base'.

    # Heatmap Profile
    heatmap_profile = PlotProfile(
        name=LogScoutPlotProfiles.HEATMAP.value,
        figure_props={"figsize": (12, 10)},
        title_props={"fontsize": 16, "fontweight": "bold", "pad": 20},
        artist_props={
            "cmap": "coolwarm",
            "annot": False,  # Annotations can make dense heatmaps unreadable
            "fmt": ".2f",
            "linewidths": 0.5,
            "linecolor": "white",
        },
    )
    registry.register(heatmap_profile)

    # Clustermap Profile (often needs more space)
    clustermap_profile = PlotProfile(
        name=LogScoutPlotProfiles.CLUSTERMAP.value,
        figure_props={"figsize": (14, 12)},
        title_props={"fontsize": 16, "fontweight": "bold", "pad": 20},
        artist_props={
            "cmap": "coolwarm",
            "annot": False,
            "fmt": ".2f",
            "linewidths": 0.5,
            "linecolor": "white",
        },
    )
    registry.register(clustermap_profile)

    # Pairplot Profile
    pairplot_profile = PlotProfile(
        name=LogScoutPlotProfiles.PAIRPLOT.value,
        figure_props={"figsize": (15, 15)},
        title_props={"fontsize": 20, "fontweight": "bold", "y": 1.02},
        artist_props={
            "diag_kind": "kde",
            "plot_kws": {"alpha": 0.6, "s": 20, "edgecolor": "k", "linewidth": 0.5},
            "corner": False,
        },
    )
    registry.register(pairplot_profile)

    # Regression Plot Profile (for feature vs. target)
    regplot_profile = PlotProfile(
        name=LogScoutPlotProfiles.REGPLOT.value,
        figure_props={"figsize": (8, 6)},
        title_props={"fontsize": 14, "fontweight": "bold"},
        label_props={"fontsize": 12},
        artist_props={
            "scatter_kws": {"alpha": 0.5, "s": 40, "color": "royalblue"},
            "line_kws": {"color": "red", "linewidth": 2.5},
        },
    )
    registry.register(regplot_profile)

    # Boxplot Profile (for feature vs. target)
    boxplot_profile = PlotProfile(
        name=LogScoutPlotProfiles.BOXPLOT.value,
        figure_props={"figsize": (10, 7)},
        title_props={"fontsize": 14, "fontweight": "bold"},
        label_props={"fontsize": 12},
        artist_props={
            "showfliers": False,  # Hide outliers for cleaner plots
            "palette": "Set2",
        },
    )
    registry.register(boxplot_profile)

    logger.debug(
        "LogScout plot profiles registered successfully."
    )


# --- Module-level execution ---
# This ensures that the plot profiles are registered as soon as this module
# is imported anywhere in the project.
try:
    register_log_scout_plot_profiles()
except Exception as e:
    # Log an error if registration fails, but don't crash the application
    # on import. This makes the system more robust.
    logger.error(
        "Failed to register LogScout plot profiles on import.",
        error=str(e),
        exc_info=True,
    )
