"""
曲线基本属性管理（格式无关）。

实现曲线基本属性的结构化管理，包括名称、单位、类型、类别、备注、维数等核心信息。
完全遵循FAP原则，与扩展属性系统分离，专注于曲线的必备基本属性。

Architecture
------------
层次/依赖: curve模块曲线元数据管理，依赖logwp/constants和logwp/exceptions
设计原则: 格式无关、职责分离、类型安全、CIIA支持
性能特征: 轻量级对象、高效查询、内存优化

职责分工：
- CurveBasicAttributes: 单个曲线的基本属性封装
- CurveMetadata: 曲线元数据管理器，支持CRUD和查询
- 基本属性 vs 扩展属性：基本属性是每条曲线必备的，扩展属性是用户自定义的

Classes:
    CurveBasicAttributes: 曲线基本属性封装
    CurveMetadata: 曲线元数据管理器

Examples:
    >>> # 创建曲线基本属性
    >>> gr_attrs = CurveBasicAttributes(
    ...     name="GR",
    ...     unit="API",
    ...     data_type=WpDataType.FLOAT,
    ...     category=WpCurveCategory.LOGGING,
    ...     description="自然伽马射线",
    ...     dimension=WpCurveDimension.ONE_D
    ... )
    >>>
    >>> # 创建曲线元数据管理器
    >>> metadata = CurveMetadata()
    >>> metadata.add_curve(gr_attrs)
    >>>
    >>> # 查询曲线信息
    >>> curve = metadata.get_curve("GR")
    >>> well_curves = metadata.get_well_identifier_curves()

References:
    - 《SCAPE_SAD_软件架构设计.md》FAP-1到FAP-11 - 格式无关原则
    - 《SCAPE_CCG_编码与通用规范.md》CS-2 - 包前缀命名规范
"""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import Any, TYPE_CHECKING
from datetime import datetime

from logwp.models.constants import (
    WpDataType, WpCurveCategory, WpCurveDimension, WpCurveClass, WpDepthRole, WpDepthUnit,
    WpStandardColumn, WpApiKeys, WpStatisticsKeys, WpDsType, WpCurveDescription, WpLogMessage
)
from enum import Enum
from logwp.models.types import WpCurveName, WpCurveMetadataDict
from logwp.models.utils import CaseInsensitiveDict
from logwp.infra import get_logger
from logwp.models.exceptions import WpCurveMetadataError
from logwp.infra.exceptions import ErrorContext

if TYPE_CHECKING:
    import pandas as pd


# 使用WpApiKeys常量，避免硬编码字符串（遵循CCG规范）

# 获取结构化日志记录器
logger = get_logger(__name__)


class CurveExpansionMode(str, Enum):
    """曲线展开模式枚举。

    用于指定expand_curve_names方法的输出格式。
    """

    EXPANDED = "expanded"
    """展开曲线列表：二维组合曲线展开为带索引的元素名称，如 ['GR', 'T2_VALUE[1]', 'T2_VALUE[2]']"""

    DATAFRAME = "dataframe"
    """DataFrame列名列表：适用于pandas DataFrame和机器学习库的友好列名，如 ['GR', 'T2_VALUE_1', 'T2_VALUE_2']"""


class CurveItemType(str, Enum):
    """单个曲线项的类型枚举。

    用于标识曲线列表中每个曲线项的具体类型。
    """
    ONE_DIMENSIONAL = "1D"               # 一维曲线，如 "GR"
    TWO_DIMENSIONAL = "2D"               # 二维组合曲线基础名称，如 "T2_VALUE"
    TWO_DIMENSIONAL_ELEMENT = "2D_ELEMENT"  # 二维组合曲线元素，如 "T2_VALUE[2]"
    NOT_FOUND = "NOT_FOUND"              # 曲线不存在


class CurveListType(str, Enum):
    """曲线列表的整体类型枚举。

    用于标识曲线列表的整体特征和组成类型。
    """
    PURE_1D = "PURE_1D"                 # 纯一维曲线列表
    PURE_COMPACT = "PURE_COMPACT"       # 纯紧凑列表（含二维基础名称）
    WITH_2D_ELEMENTS = "WITH_2D_ELEMENTS"  # 含有二维元素
    MIXED = "MIXED"                      # 混合类型（一维+二维+二维元素）


@dataclass
class CurveItemAnalysis:
    """单个曲线项的分析结果。"""
    curve_name: str                    # 原始曲线名称
    item_type: CurveItemType          # 曲线项类型
    parent_curve: str | None = None   # 父曲线名称（仅对2D_ELEMENT有效）
    element_index: int | None = None   # 元素索引（仅对2D_ELEMENT有效）


@dataclass
class CurveConflict:
    """二维组合曲线表示方法冲突。"""
    base_curve_name: str              # 二维组合曲线基础名称
    compact_form: str                 # 紧凑形式，如 "T2_VALUE"
    element_forms: list[str]          # 元素形式列表，如 ["T2_VALUE[2]", "T2_VALUE[5]"]


@dataclass
class CurveListAnalysis:
    """曲线列表的完整分析结果。"""
    curve_items: list[CurveItemAnalysis]  # 每个曲线项的分析结果
    list_type: CurveListType             # 列表整体类型
    has_conflicts: bool                  # 是否存在二维组合曲线表示冲突
    conflicts: list[CurveConflict]       # 冲突详情列表
    not_found_curves: list[str]          # 不存在的曲线列表


@dataclass(frozen=True)
class CurveBasicAttributes:
    """曲线基本属性（格式无关）。

    封装单个曲线的所有基本属性信息。这些属性是每条曲线必备的核心信息，
    与用户自定义的扩展属性完全分离。

    Architecture
    ------------
    层次/依赖: 曲线属性封装，不可变数据容器
    设计原则: 不可变对象、类型安全、格式无关
    性能特征: 轻量级对象、快速访问、内存友好

    Attributes:
        name: 曲线名称
        unit: 单位（可选）
        data_type: 数据类型（INT、FLOAT、STR、BOOL）
        category: 曲线类别（测井、计算、标识等）
        description: 备注信息（可选）
        dimension: 维数（1D、2D）
        curve_class: 曲线类别（CATEGORICAL或NORMAL，可选）
        is_well_identifier: 是否井名曲线
        depth_role: 深度曲线角色（SINGLE、TOP、BOTTOM，可选）
        element_names: 二维组合曲线元素名称列表（仅二维组合曲线使用）
        dataframe_column_name: pandas DataFrame友好的列名（自动生成）

    Examples:
        >>> # 一维测井曲线
        >>> gr_attrs = CurveBasicAttributes(
        ...     name="GR",
        ...     unit="API",
        ...     data_type=WpDataType.FLOAT,
        ...     category=WpCurveCategory.LOGGING,
        ...     description="自然伽马射线",
        ...     dimension=WpCurveDimension.ONE_D
        ... )
        >>> # dataframe_column_name 自动设置为 "GR"
        >>>
        >>> # 井名标识曲线
        >>> well_attrs = CurveBasicAttributes(
        ...     name="WELL",
        ...     unit=None,
        ...     data_type=WpDataType.STR,
        ...     category=WpCurveCategory.IDENTIFIER,
        ...     description="井名",
        ...     dimension=WpCurveDimension.ONE_D,
        ...     is_well_identifier=True
        ... )
        >>> # dataframe_column_name 自动设置为 "WELL"
        >>>
        >>> # 二维组合曲线
        >>> t2_attrs = CurveBasicAttributes(
        ...     name="T2_VALUE",
        ...     unit="ms",
        ...     data_type=WpDataType.FLOAT,
        ...     category=WpCurveCategory.LOGGING,
        ...     description="T2谱数据",
        ...     dimension=WpCurveDimension.TWO_D,
        ...     element_names=["T2_VALUE[1]", "T2_VALUE[2]", "T2_VALUE[3]"]
        ... )
        >>> # dataframe_column_name 自动设置为 "T2_VALUE"
        >>> # element_names 对应的 DataFrame 友好名称为 ["T2_VALUE_1", "T2_VALUE_2", "T2_VALUE_3"]
        >>>
        >>> # 类别型曲线（用于机器学习分类）
        >>> facies_attrs = CurveBasicAttributes(
        ...     name="FACIES",
        ...     unit=None,
        ...     data_type=WpDataType.STR,
        ...     category=WpCurveCategory.COMPUTED,
        ...     description="岩相分类",
        ...     dimension=WpCurveDimension.ONE_D,
        ...     curve_class=WpCurveClass.CATEGORICAL
        ... )
        >>> # dataframe_column_name 自动设置为 "FACIES"
        >>>
        >>> # 区间深度曲线
        >>> top_depth_attrs = CurveBasicAttributes(
        ...     name="MD_Top",
        ...     unit="m",
        ...     data_type=WpDataType.FLOAT,
        ...     category=WpCurveCategory.IDENTIFIER,
        ...     description="顶界深度",
        ...     dimension=WpCurveDimension.ONE_D,
        ...     depth_role=WpDepthRole.TOP
        ... )
        >>> # dataframe_column_name 自动设置为 "MD_Top"
    """

    name: WpCurveName
    """曲线名称"""

    unit: str | None
    """单位（可选）"""

    data_type: WpDataType
    """数据类型（INT、FLOAT、STR、BOOL）"""

    category: WpCurveCategory
    """曲线类别（测井、计算、标识等）"""

    description: str | None
    """备注信息（可选）"""

    dimension: WpCurveDimension
    """维数（1D、2D）"""

    curve_class: WpCurveClass | None = None
    """曲线类别（CATEGORICAL或NORMAL，可选）"""

    is_well_identifier: bool = False
    """是否井名曲线"""

    depth_role: WpDepthRole | None = None
    """深度曲线角色（SINGLE、TOP、BOTTOM，可选）"""

    element_names: list[str] | None = None
    """二维组合曲线元素名称列表（二维组合曲线必须提供或使用便捷方法创建）"""

    dataframe_column_name: str = field(init=False)
    """一维曲线或二维组合曲线基础名称的DataFrame友好列名（自动生成）"""

    dataframe_element_names: list[str] | None = field(init=False)
    """二维组合曲线元素的DataFrame友好列名列表（自动生成，一维曲线为None）"""

    def __post_init__(self) -> None:
        """验证属性一致性并自动生成DataFrame友好名称。"""
        # 验证二维组合曲线必须有元素名称
        if self.dimension == WpCurveDimension.TWO_D_COMP and not self.element_names:
            raise WpCurveMetadataError(
                f"二维组合曲线 '{self.name}' 必须提供元素名称列表。"
                f"建议使用 CurveBasicAttributes.create_2d_composite_curve() 便捷方法创建。",
                context=ErrorContext(
                    operation=WpStatisticsKeys.OPERATION_VALIDATE_CURVE_ATTRIBUTES,
                    column_name=self.name,
                    additional_info={
                        WpApiKeys.CURVE_DIMENSION: self.dimension.value,
                        "suggestion": "use_create_2d_curve_method"
                    }
                )
            )

        # 验证一维曲线不应有元素名称
        if self.dimension == WpCurveDimension.ONE_D and self.element_names:
            raise WpCurveMetadataError(
                "一维曲线不应提供元素名称列表",
                context=ErrorContext(
                    operation=WpStatisticsKeys.OPERATION_VALIDATE_CURVE_ATTRIBUTES,
                    column_name=self.name,
                    additional_info={WpApiKeys.CURVE_DIMENSION: self.dimension.value}
                )
            )

        # 生成DataFrame友好的基础列名
        friendly_name = self._generate_dataframe_friendly_name(self.name)
        object.__setattr__(self, 'dataframe_column_name', friendly_name)

        # 生成二维组合曲线元素的DataFrame友好名称
        if self.is_2d_composite_curve() and self.element_names:
            friendly_elements = [
                self._generate_dataframe_friendly_name(element_name)
                for element_name in self.element_names
            ]
            object.__setattr__(self, 'dataframe_element_names', friendly_elements)
        else:
            object.__setattr__(self, 'dataframe_element_names', None)

    @staticmethod
    def _generate_dataframe_friendly_name(name: str) -> str:
        """生成机器学习库友好的DataFrame列名。

        转换规则（基于XGBoost/LightGBM等库的严格要求）：
        1. 移除或替换所有特殊字符（保留字母、数字、下划线）
        2. 确保首字符是字母
        3. 将连续的特殊字符替换为单个下划线
        4. 移除首尾的下划线

        Args:
            name: 原始曲线名称

        Returns:
            str: DataFrame友好的列名

        Examples:
            >>> CurveBasicAttributes._generate_dataframe_friendly_name("T2_VALUE[1]")
            'T2_VALUE_1'
            >>> CurveBasicAttributes._generate_dataframe_friendly_name("GR-CORR")
            'GR_CORR'
            >>> CurveBasicAttributes._generate_dataframe_friendly_name("123_START")
            'COL_123_START'
        """
        import re

        # 1. 处理二维组合曲线模式 NAME[INDEX] -> NAME_INDEX
        if '[' in name and name.endswith(']'):
            base_name = name[:name.find('[')]
            index_part = name[name.find('[')+1:-1]
            name = f"{base_name}_{index_part}"

        # 2. 替换所有非字母数字下划线的字符为下划线
        cleaned = re.sub(r'[^A-Za-z0-9_]+', '_', name)

        # 3. 移除首尾下划线
        cleaned = cleaned.strip('_')

        # 4. 确保首字符是字母
        if cleaned and not cleaned[0].isalpha():
            cleaned = f"COL_{cleaned}"

        # 5. 处理空字符串情况
        if not cleaned:
            cleaned = "UNNAMED_COL"

        return cleaned

    def to_dict(self) -> WpCurveMetadataDict:
        """转换为字典格式。

        Returns:
            WpCurveMetadataDict: 曲线元数据字典
        """
        return {
            WpApiKeys.CURVE_NAME: self.name,
            WpApiKeys.CURVE_UNIT: self.unit,
            WpApiKeys.CURVE_DATA_TYPE: self.data_type.value,
            WpApiKeys.CURVE_CATEGORY: self.category.value,
            WpApiKeys.CURVE_DESCRIPTION: self.description,
            WpApiKeys.CURVE_DIMENSION: self.dimension.value,
            WpApiKeys.CURVE_CLASS: self.curve_class.value if self.curve_class else None,
            WpApiKeys.IS_WELL_IDENTIFIER: self.is_well_identifier,
            WpApiKeys.DEPTH_ROLE: self.depth_role.value if self.depth_role else None,
            WpApiKeys.ELEMENT_NAMES: self.element_names.copy() if self.element_names else None
        }

    def _to_summary_dict(self) -> dict[str, Any]:
        """转换为概况字典格式，包含所有字段信息用于调试报告。

        Returns:
            dict[str, Any]: 包含所有字段的完整字典

        Examples:
            >>> curve_attrs = CurveBasicAttributes.create_1d_curve("GR", unit="API")
            >>> summary = curve_attrs._to_summary_dict()
            >>> assert "dataframe_column_name" in summary
            >>> assert "dataframe_element_names" in summary
        """
        return {
            "name": str(self.name),
            "unit": self.unit,
            "data_type": self.data_type.value,
            "category": self.category.value,
            "description": self.description,
            "dimension": self.dimension.value,
            "curve_class": self.curve_class.value if self.curve_class else None,
            "is_well_identifier": self.is_well_identifier,
            "depth_role": self.depth_role.value if self.depth_role else None,
            "element_names": self.element_names.copy() if self.element_names else None,
            "dataframe_column_name": self.dataframe_column_name,
            "dataframe_element_names": self.dataframe_element_names.copy() if self.dataframe_element_names else None
        }

    @classmethod
    def from_dict(cls, data: WpCurveMetadataDict) -> 'CurveBasicAttributes':
        """从字典创建曲线基本属性。

        Args:
            data: 曲线元数据字典

        Returns:
            CurveBasicAttributes: 曲线基本属性实例
        """
        return cls(
            name=data[WpApiKeys.CURVE_NAME],
            unit=data.get(WpApiKeys.CURVE_UNIT),
            data_type=WpDataType(data[WpApiKeys.CURVE_DATA_TYPE]),
            category=WpCurveCategory(data[WpApiKeys.CURVE_CATEGORY]),
            description=data.get(WpApiKeys.CURVE_DESCRIPTION),
            dimension=WpCurveDimension(data[WpApiKeys.CURVE_DIMENSION]),
            curve_class=WpCurveClass(data[WpApiKeys.CURVE_CLASS]) if data.get(WpApiKeys.CURVE_CLASS) else None,
            is_well_identifier=data.get(WpApiKeys.IS_WELL_IDENTIFIER, False),
            depth_role=WpDepthRole(data[WpApiKeys.DEPTH_ROLE]) if data.get(WpApiKeys.DEPTH_ROLE) else None,
            element_names=data.get(WpApiKeys.ELEMENT_NAMES)
        )

    def is_numeric(self) -> bool:
        """检查是否为数值类型曲线。

        Returns:
            bool: 是否为数值类型
        """
        return self.data_type in WpDataType.numeric_types()

    def is_1d_curve(self) -> bool:
        """检查是否为一维曲线"""
        return self.dimension == WpCurveDimension.ONE_D

    def is_2d_composite_curve(self) -> bool:
        """检查是否为二维组合曲线"""
        return self.dimension == WpCurveDimension.TWO_D_COMP
    def is_depth_curve(self) -> bool:
        """检查是否为深度曲线。

        Returns:
            bool: 是否为深度曲线（depth_role不为None）

        Examples:
            >>> # 普通曲线
            >>> gr_attrs = CurveBasicAttributes(name="GR", ...)
            >>> assert not gr_attrs.is_depth_curve()

            >>> # 深度曲线
            >>> md_attrs = CurveBasicAttributes(name="MD", depth_role=WpDepthRole.SINGLE, ...)
            >>> assert md_attrs.is_depth_curve()

            >>> # 区间深度曲线
            >>> top_attrs = CurveBasicAttributes(name="MD_Top", depth_role=WpDepthRole.TOP, ...)
            >>> assert top_attrs.is_depth_curve()
        """
        return self.depth_role is not None

    def is_system_curve(self) -> bool:
        """检查是否为系统曲线。

        Returns:
            bool: 是否为系统曲线
        """
        return self.category in WpCurveCategory.system_curves()


    def get_recommended_interpolation_method(self, user_specified_method: str) -> str:
        """根据曲线属性和用户指定方法确定最终插值方法。

        实现插值方法的三级优先级：强制方法 > 数据类型约束 > 用户指定方法。

        Args:
            user_specified_method: 用户指定的插值方法

        Returns:
            str: 最终使用的插值方法

        Note:
            - CATEGORICAL曲线强制使用"nearest"插值
            - STR/BOOL数据类型强制使用"nearest"插值
            - 其他情况使用用户指定的方法

        Examples:
            >>> # CATEGORICAL曲线强制使用nearest
            >>> facies_attrs = CurveBasicAttributes(
            ...     name="FACIES",
            ...     curve_class=WpCurveClass.CATEGORICAL,
            ...     data_type=WpDataType.STR
            ... )
            >>> method = facies_attrs.get_recommended_interpolation_method("linear")
            >>> assert method == "nearest"  # 强制使用nearest

            >>> # 字符串类型强制使用nearest
            >>> comment_attrs = CurveBasicAttributes(
            ...     name="COMMENT",
            ...     data_type=WpDataType.STR
            ... )
            >>> method = comment_attrs.get_recommended_interpolation_method("spline")
            >>> assert method == "nearest"  # STR类型强制使用nearest

            >>> # 数值型普通曲线使用用户指定方法
            >>> gr_attrs = CurveBasicAttributes(
            ...     name="GR",
            ...     data_type=WpDataType.FLOAT,
            ...     curve_class=WpCurveClass.NORMAL
            ... )
            >>> method = gr_attrs.get_recommended_interpolation_method("spline")
            >>> assert method == "spline"  # 使用用户指定的spline

        References:
            《SCAPE_DDS_logwp_离散转连续数据集.md》§3.2 - 插值策略设计
        """
        # 1. 优先检查强制插值方法
        if self.curve_class is not None:
            mandatory_method = self.curve_class.get_mandatory_interpolation_method()
            if mandatory_method is not None:
                return mandatory_method

        # 2. 根据数据类型检查是否需要特殊处理
        if self.data_type in [WpDataType.STR, WpDataType.BOOL]:
            return "nearest"  # 字符串和布尔类型强制使用最近邻

        # 3. 最后返回用户指定的方法
        return user_specified_method

    @classmethod
    def create_2d_composite_curve(
        cls,
        name: str,
        element_count: int,
        unit: str | None = None,
        data_type: WpDataType = WpDataType.FLOAT,
        category: WpCurveCategory = WpCurveCategory.LOGGING,
        description: str | None = None,
        curve_class: WpCurveClass | None = None,
        is_well_identifier: bool = False,
        depth_role: WpDepthRole | None = None
    ) -> 'CurveBasicAttributes':
        """创建二维组合曲线的便捷方法。

        Args:
            name: 曲线基础名称
            element_count: 元素数量
            unit: 单位（可选）
            data_type: 数据类型，默认为FLOAT
            category: 曲线类别，默认为LOGGING
            description: 备注信息（可选）
            curve_class: 曲线类别（可选）
            is_well_identifier: 是否井名曲线，默认为False
            depth_role: 深度曲线角色（可选）

        Returns:
            CurveBasicAttributes: 二维组合曲线属性对象

        Raises:
            ValueError: 当element_count <= 0时抛出

        Examples:
            >>> # 创建50个元素的T2谱曲线
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve(
            ...     name="T2_VALUE",
            ...     element_count=50,
            ...     unit="ms",
            ...     description="T2谱数据"
            ... )
            >>> len(t2_attrs.element_names)  # 50
            >>> t2_attrs.element_names[0]    # "T2_VALUE[1]"
            >>> t2_attrs.dataframe_element_names[0]  # "T2_VALUE_1"
        """
        if element_count <= 0:
            raise ValueError(f"element_count 必须大于0，当前值: {element_count}")

        # 生成元素名称列表
        element_names = [f"{name}[{i}]" for i in range(1, element_count + 1)]

        return cls(
            name=name,
            unit=unit,
            data_type=data_type,
            category=category,
            description=description,
            dimension=WpCurveDimension.TWO_D_COMP,
            curve_class=curve_class,
            is_well_identifier=is_well_identifier,
            depth_role=depth_role,
            element_names=element_names
        )

    def clone_with_unit(self, new_unit: str) -> 'CurveBasicAttributes':
        """克隆曲线属性并修改单位。

        Args:
            new_unit: 新的单位

        Returns:
            CurveBasicAttributes: 克隆后的曲线属性

        Examples:
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", unit="API")
            >>> gr_gapi = gr_attrs.clone_with_unit("GAPI")
            >>> assert gr_gapi.unit == "GAPI"
            >>> assert gr_gapi.name == "GR"  # 其他属性保持不变
        """
        return CurveBasicAttributes(
            name=self.name,
            unit=new_unit,
            data_type=self.data_type,
            category=self.category,
            description=self.description,
            dimension=self.dimension,
            curve_class=self.curve_class,
            is_well_identifier=self.is_well_identifier,
            depth_role=self.depth_role,
            element_names=self.element_names.copy() if self.element_names else None
        )

    def clone_with_category(self, new_category: WpCurveCategory) -> 'CurveBasicAttributes':
        """克隆曲线属性并修改类别。

        Args:
            new_category: 新的曲线类别

        Returns:
            CurveBasicAttributes: 克隆后的曲线属性

        Examples:
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", category=WpCurveCategory.LOGGING)
            >>> gr_computed = gr_attrs.clone_with_category(WpCurveCategory.COMPUTED)
            >>> assert gr_computed.category == WpCurveCategory.COMPUTED
            >>> assert gr_computed.name == "GR"  # 其他属性保持不变
        """
        return CurveBasicAttributes(
            name=self.name,
            unit=self.unit,
            data_type=self.data_type,
            category=new_category,
            description=self.description,
            dimension=self.dimension,
            curve_class=self.curve_class,
            is_well_identifier=self.is_well_identifier,
            depth_role=self.depth_role,
            element_names=self.element_names.copy() if self.element_names else None
        )

    def clone_with_description(self, new_description: str) -> 'CurveBasicAttributes':
        """克隆曲线属性并修改描述。

        Args:
            new_description: 新的描述

        Returns:
            CurveBasicAttributes: 克隆后的曲线属性

        Examples:
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", description="原始描述")
            >>> gr_new = gr_attrs.clone_with_description("新的描述")
            >>> assert gr_new.description == "新的描述"
            >>> assert gr_new.name == "GR"  # 其他属性保持不变
        """
        return CurveBasicAttributes(
            name=self.name,
            unit=self.unit,
            data_type=self.data_type,
            category=self.category,
            description=new_description,
            dimension=self.dimension,
            curve_class=self.curve_class,
            is_well_identifier=self.is_well_identifier,
            depth_role=self.depth_role,
            element_names=self.element_names.copy() if self.element_names else None
        )

    @classmethod
    def create_1d_curve(
        cls,
        name: str,
        unit: str | None = None,
        data_type: WpDataType = WpDataType.FLOAT,
        category: WpCurveCategory = WpCurveCategory.LOGGING,
        description: str | None = None,
        curve_class: WpCurveClass | None = None,
        is_well_identifier: bool = False,
        depth_role: WpDepthRole | None = None
    ) -> 'CurveBasicAttributes':
        """创建一维曲线的便捷方法（为了API一致性）。

        Args:
            name: 曲线名称
            unit: 单位（可选）
            data_type: 数据类型，默认为FLOAT
            category: 曲线类别，默认为LOGGING
            description: 备注信息（可选）
            curve_class: 曲线类别（可选）
            is_well_identifier: 是否井名曲线，默认为False
            depth_role: 深度曲线角色（可选）

        Returns:
            CurveBasicAttributes: 一维曲线属性对象

        Examples:
            >>> # 创建一维测井曲线
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve(
            ...     name="GR",
            ...     unit="API",
            ...     category=WpCurveCategory.LOGGING,
            ...     description="自然伽马射线"
            ... )
            >>> gr_attrs.dataframe_column_name  # "GR"
            >>> gr_attrs.dataframe_element_names  # None
        """
        return cls(
            name=name,
            unit=unit,
            data_type=data_type,
            category=category,
            description=description,
            dimension=WpCurveDimension.ONE_D,
            curve_class=curve_class,
            is_well_identifier=is_well_identifier,
            depth_role=depth_role,
            element_names=None
        )

    @classmethod
    def create_well_identifier_curve(
        cls,
        name: str | None = None,
        description: str | None = None
    ) -> 'CurveBasicAttributes':
        """创建井名标识曲线的便捷方法。

        使用标准化井名列名和属性创建井名曲线元数据。

        Args:
            name: 井名曲线名称，默认为WpStandardColumn.WELL_NAME
            description: 备注信息，默认为"井名标识曲线"

        Returns:
            CurveBasicAttributes: 井名曲线属性对象

        Examples:
            >>> # 创建标准井名曲线
            >>> well_attrs = CurveBasicAttributes.create_well_identifier_curve()
            >>> assert well_attrs.name == WpStandardColumn.WELL_NAME
            >>> assert well_attrs.is_well_identifier is True
            >>> assert well_attrs.category == WpCurveCategory.IDENTIFIER
            >>> assert well_attrs.data_type == WpDataType.STR
            >>>
            >>> # 创建自定义名称的井名曲线
            >>> custom_well = CurveBasicAttributes.create_well_identifier_curve(name="WELL_NO")
            >>> assert custom_well.name == "WELL_NO"
            >>> assert custom_well.is_well_identifier is True
        """
        return cls.create_1d_curve(
            name=name or WpStandardColumn.WELL_NAME,
            unit=None,
            data_type=WpDataType.STR,
            category=WpCurveCategory.IDENTIFIER,
            description=description or WpCurveDescription.WELL_IDENTIFIER_CURVE,
            is_well_identifier=True
        )

    @classmethod
    def create_depth_reference_curve(
        cls,
        name: str | None = None,
        unit: str | None = None,
        description: str | None = None
    ) -> 'CurveBasicAttributes':
        """创建单深度参考曲线的便捷方法。

        用于continuous和discrete数据集的单一深度索引曲线。

        Args:
            name: 深度曲线名称，默认为WpStandardColumn.DEPTH
            unit: 深度单位，默认为WpDepthUnit.METER
            description: 备注信息，默认为"深度参考曲线"

        Returns:
            CurveBasicAttributes: 深度曲线属性对象

        Examples:
            >>> # 创建标准深度曲线（米）
            >>> depth_attrs = CurveBasicAttributes.create_depth_reference_curve()
            >>> assert depth_attrs.name == WpStandardColumn.DEPTH
            >>> assert depth_attrs.depth_role == WpDepthRole.SINGLE
            >>> assert depth_attrs.unit == WpDepthUnit.METER
            >>>
            >>> # 创建自定义名称的深度曲线（英尺）
            >>> md_attrs = CurveBasicAttributes.create_depth_reference_curve(
            ...     name="MD", unit=WpDepthUnit.FEET
            ... )
            >>> assert md_attrs.name == "MD"
            >>> assert md_attrs.unit == WpDepthUnit.FEET
        """
        return cls.create_1d_curve(
            name=name or WpStandardColumn.DEPTH,
            unit=unit or WpDepthUnit.METER,
            data_type=WpDataType.FLOAT,
            category=WpCurveCategory.IDENTIFIER,
            description=description or WpCurveDescription.DEPTH_REFERENCE_CURVE,
            depth_role=WpDepthRole.SINGLE
        )

    @classmethod
    def create_interval_depth_reference_curves(
        cls,
        top_name: str | None = None,
        bottom_name: str | None = None,
        unit: str | None = None,
        top_description: str | None = None,
        bottom_description: str | None = None
    ) -> tuple['CurveBasicAttributes', 'CurveBasicAttributes']:
        """创建区间深度参考曲线对的便捷方法。

        用于interval数据集的顶深和底深索引曲线。

        Args:
            top_name: 顶深曲线名称，默认为WpStandardColumn.DEPTH_TOP
            bottom_name: 底深曲线名称，默认为WpStandardColumn.DEPTH_BOTTOM
            unit: 深度单位，默认为WpDepthUnit.METER
            top_description: 顶深曲线备注信息，默认为"区间顶深参考曲线"
            bottom_description: 底深曲线备注信息，默认为"区间底深参考曲线"

        Returns:
            tuple[CurveBasicAttributes, CurveBasicAttributes]: (顶深曲线, 底深曲线)

        Examples:
            >>> # 创建标准区间深度曲线对（米）
            >>> top_attrs, bottom_attrs = CurveBasicAttributes.create_interval_depth_reference_curves()
            >>> assert top_attrs.name == WpStandardColumn.DEPTH_TOP
            >>> assert top_attrs.depth_role == WpDepthRole.TOP
            >>> assert bottom_attrs.name == WpStandardColumn.DEPTH_BOTTOM
            >>> assert bottom_attrs.depth_role == WpDepthRole.BOTTOM
            >>> assert top_attrs.unit == bottom_attrs.unit == WpDepthUnit.METER
            >>>
            >>> # 创建自定义名称的区间深度曲线对（英尺）
            >>> top_md, bottom_md = CurveBasicAttributes.create_interval_depth_reference_curves(
            ...     top_name="MD_Top", bottom_name="MD_Bottom", unit=WpDepthUnit.FEET
            ... )
            >>> assert top_md.name == "MD_Top"
            >>> assert bottom_md.name == "MD_Bottom"
            >>> assert top_md.unit == bottom_md.unit == WpDepthUnit.FEET
        """
        top_attrs = cls.create_1d_curve(
            name=top_name or WpStandardColumn.DEPTH_TOP,
            unit=unit or WpDepthUnit.METER,
            data_type=WpDataType.FLOAT,
            category=WpCurveCategory.IDENTIFIER,
            description=top_description or WpCurveDescription.INTERVAL_TOP_DEPTH_CURVE,
            depth_role=WpDepthRole.TOP
        )

        bottom_attrs = cls.create_1d_curve(
            name=bottom_name or WpStandardColumn.DEPTH_BOTTOM,
            unit=unit or WpDepthUnit.METER,
            data_type=WpDataType.FLOAT,
            category=WpCurveCategory.IDENTIFIER,
            description=bottom_description or WpCurveDescription.INTERVAL_BOTTOM_DEPTH_CURVE,
            depth_role=WpDepthRole.BOTTOM
        )

        return top_attrs, bottom_attrs


@dataclass
class CurveMetadata:
    """曲线元数据管理器（格式无关）。

    管理数据集中所有曲线的基本属性，提供CRUD操作和查询功能。
    支持动态维护，响应DataFrame的曲线变化。

    Architecture
    ------------
    层次/依赖: curve模块曲线元数据管理器
    设计原则: CIIA支持、格式无关、高效查询
    性能特征: 大小写不敏感查询、索引优化、批量操作

    职责：
    1. 管理曲线的基本属性元数据
    2. 响应数据集的曲线修改操作，动态维护元数据
    3. 提供曲线元数据的查询和过滤功能

    注意：不管理实际数据，数据仍在pd.DataFrame中。

    Attributes:
        curves: 曲线基本属性字典（大小写不敏感）
        created_at: 创建时间
        modified_at: 修改时间

    Examples:
        >>> # 创建曲线元数据管理器
        >>> metadata = CurveMetadata()
        >>>
        >>> # 添加曲线
        >>> gr_attrs = CurveBasicAttributes(name="GR", ...)
        >>> metadata.add_curve(gr_attrs)
        >>>
        >>> # 查询曲线
        >>> curve = metadata.get_curve("gr")  # 大小写不敏感
        >>> well_curves = metadata.get_well_identifier_curves()
        >>> two_d_curves = metadata.get_2d_curves()
    """

    curves: CaseInsensitiveDict = field(
        default_factory=CaseInsensitiveDict
    )
    """曲线基本属性字典（大小写不敏感）"""

    created_at: datetime = field(default_factory=datetime.now)
    """创建时间"""

    modified_at: datetime = field(default_factory=datetime.now)
    """修改时间"""

    def add_curve(self, attributes: CurveBasicAttributes) -> None:
        """添加曲线基本属性。

        Args:
            attributes: 曲线基本属性

        Raises:
            WpCurveMetadataError: 曲线已存在时抛出

        Examples:
            >>> metadata = CurveMetadata()
            >>> gr_attrs = CurveBasicAttributes(name="GR", ...)
            >>> metadata.add_curve(gr_attrs)
        """
        if attributes.name in self.curves:
            raise WpCurveMetadataError(
                f"曲线 '{attributes.name}' 已存在",
                context=ErrorContext(
                    operation=WpStatisticsKeys.OPERATION_ADD_CURVE,
                    column_name=attributes.name,
                    additional_info={WpStatisticsKeys.REASON_DUPLICATE_CURVE: WpStatisticsKeys.REASON_DUPLICATE_CURVE}
                )
            )

        self.curves[attributes.name] = attributes
        self.modified_at = datetime.now()

        logger.debug(
            "曲线基本属性添加成功",
            operation=WpStatisticsKeys.OPERATION_ADD_CURVE,
            curve_name=attributes.name,
            curve_category=attributes.category.value,
            curve_dimension=attributes.dimension.value,
            data_type=attributes.data_type.value,
            dataframe_column_name=attributes.dataframe_column_name,
            has_element_names=attributes.dataframe_element_names is not None,
            total_curves=len(self.curves)
        )

    def get_curve(self, name: WpCurveName) -> CurveBasicAttributes | None:
        """获取曲线基本属性。

        Args:
            name: 曲线名称（大小写不敏感）

        Returns:
            CurveBasicAttributes | None: 曲线基本属性，不存在时返回None

        Examples:
            >>> curve = metadata.get_curve("GR")
            >>> curve = metadata.get_curve("gr")  # 大小写不敏感
        """
        return self.curves.get(name)

    def update_curve(self, name: WpCurveName, **updates: Any) -> None:
        """更新曲线基本属性。

        Args:
            name: 曲线名称
            **updates: 要更新的属性字段

        Raises:
            WpCurveMetadataError: 曲线不存在时抛出

        Examples:
            >>> metadata.update_curve("GR", unit="GAPI", description="新的描述")
        """
        if name not in self.curves:
            raise WpCurveMetadataError(
                f"曲线 '{name}' 不存在",
                context=ErrorContext(
                    operation=WpStatisticsKeys.OPERATION_UPDATE_CURVE,
                    column_name=name,
                    additional_info={WpStatisticsKeys.REASON_CURVE_NOT_FOUND: WpStatisticsKeys.REASON_CURVE_NOT_FOUND}
                )
            )

        current_attrs = self.curves[name]
        # 创建新的属性对象（因为是frozen dataclass）
        attrs_dict = current_attrs.to_dict()
        attrs_dict.update(updates)

        # 重新验证数据类型
        if WpApiKeys.CURVE_DATA_TYPE in updates and isinstance(updates[WpApiKeys.CURVE_DATA_TYPE], str):
            attrs_dict[WpApiKeys.CURVE_DATA_TYPE] = WpDataType(updates[WpApiKeys.CURVE_DATA_TYPE])
        if WpApiKeys.CURVE_CATEGORY in updates and isinstance(updates[WpApiKeys.CURVE_CATEGORY], str):
            attrs_dict[WpApiKeys.CURVE_CATEGORY] = WpCurveCategory(updates[WpApiKeys.CURVE_CATEGORY])
        if WpApiKeys.CURVE_DIMENSION in updates and isinstance(updates[WpApiKeys.CURVE_DIMENSION], str):
            attrs_dict[WpApiKeys.CURVE_DIMENSION] = WpCurveDimension(updates[WpApiKeys.CURVE_DIMENSION])

        new_attrs = CurveBasicAttributes.from_dict(attrs_dict)
        self.curves[name] = new_attrs
        self.modified_at = datetime.now()

        logger.debug(
            "曲线基本属性更新成功",
            operation=WpStatisticsKeys.OPERATION_UPDATE_CURVE,
            curve_name=name,
            updated_fields=list(updates.keys()),
            curve_category=new_attrs.category.value,
            curve_dimension=new_attrs.dimension.value
        )

    def _is_2d_element_name(self, name: str) -> tuple[bool, str | None]:
        """检查是否为二维组合曲线元素名称。

        Args:
            name: 曲线名称

        Returns:
            tuple[bool, str | None]: (是否为元素, 父曲线名称)

        Examples:
            >>> metadata._is_2d_element_name("T2_VALUE[1]")
            (True, "T2_VALUE")
            >>> metadata._is_2d_element_name("GR")
            (False, None)
        """
        if '[' in name and name.endswith(']'):
            # 提取基础名称：T2_VALUE[1] -> T2_VALUE
            base_name = name[:name.find('[')]
            return True, base_name
        return False, None

    def _find_parent_2d_curve(self, element_name: str) -> CurveBasicAttributes | None:
        """根据元素名称找到父二维组合曲线。

        Args:
            element_name: 元素名称，如 "T2_VALUE[1]"

        Returns:
            CurveBasicAttributes | None: 父二维组合曲线属性，如果不存在则返回None
        """
        is_element, parent_name = self._is_2d_element_name(element_name)
        if not is_element or not parent_name:
            return None

        parent_curve = self.curves.get(parent_name)
        if parent_curve and parent_curve.is_2d_composite_curve():
            # 验证元素确实存在于父曲线中
            if parent_curve.element_names and element_name in parent_curve.element_names:
                return parent_curve
        return None

    def _create_1d_from_2d_element(self, element_name: str, parent_curve: CurveBasicAttributes) -> CurveBasicAttributes:
        """从二维组合曲线元素创建一维曲线属性。

        Args:
            element_name: 元素名称，如 "T2_VALUE[1]"
            parent_curve: 父二维组合曲线属性

        Returns:
            CurveBasicAttributes: 新的一维曲线属性
        """
        return CurveBasicAttributes(
            name=element_name,
            unit=parent_curve.unit,
            data_type=parent_curve.data_type,
            category=parent_curve.category,
            description=f"{parent_curve.description} (元素)" if parent_curve.description else None,
            dimension=WpCurveDimension.ONE_D,
            curve_class=parent_curve.curve_class,
            is_well_identifier=parent_curve.is_well_identifier,
            depth_role=parent_curve.depth_role,
            element_names=None
        )

    def _get_curve_insertion_position(self, curve_name: str) -> int:
        """获取曲线在有序字典中的位置。

        Args:
            curve_name: 曲线名称

        Returns:
            int: 位置索引，如果不存在则返回-1
        """
        curve_names = list(self.curves.keys())
        try:
            return curve_names.index(curve_name)
        except ValueError:
            return -1

    def remove_curve(self, name: WpCurveName) -> None:
        """移除曲线基本属性（支持二维组合曲线元素删除）。

        支持三种删除模式：
        1. 删除一维曲线：直接删除CurveBasicAttributes
        2. 删除整个二维组合曲线：直接删除CurveBasicAttributes
        3. 删除二维组合曲线元素：拆分二维组合曲线，剩余元素降级为一维曲线

        Args:
            name: 曲线名称或二维组合曲线元素名称

        Raises:
            WpCurveMetadataError: 曲线不存在时抛出

        Examples:
            >>> # 删除一维曲线
            >>> metadata.remove_curve("GR")
            >>>
            >>> # 删除整个二维组合曲线
            >>> metadata.remove_curve("T2_VALUE")
            >>>
            >>> # 删除二维组合曲线元素（剩余元素降级为一维）
            >>> metadata.remove_curve("T2_VALUE[2]")
        """
        # 检查是否为二维组合曲线元素
        is_element, parent_name = self._is_2d_element_name(name)

        if is_element:
            # 情况：删除二维组合曲线元素
            parent_curve = self._find_parent_2d_curve(name)
            if not parent_curve:
                raise WpCurveMetadataError(
                    f"二维组合曲线元素 '{name}' 不存在或其父曲线不存在",
                    context=ErrorContext(
                        operation=WpStatisticsKeys.OPERATION_REMOVE_CURVE_ELEMENT,
                        column_name=name,
                        additional_info={
                            "parent_curve": parent_name,
                            "available_curves": list(self.curves.keys()),
                            "is_element": True
                        }
                    )
                )

            self._split_2d_curve_remove_element(name, parent_curve)

        else:
            # 情况：删除一维曲线或整个二维组合曲线
            if name not in self.curves:
                raise WpCurveMetadataError(
                    f"曲线 '{name}' 不存在",
                    context=ErrorContext(
                        operation=WpStatisticsKeys.OPERATION_REMOVE_CURVE,
                        column_name=name,
                        additional_info={
                            WpStatisticsKeys.REASON_CURVE_NOT_FOUND: WpStatisticsKeys.REASON_CURVE_NOT_FOUND,
                            "available_curves": list(self.curves.keys()),
                            "is_element": False
                        }
                    )
                )

            curve_attrs = self.curves[name]
            del self.curves[name]
            self.modified_at = datetime.now()

            logger.debug(
                "曲线基本属性移除成功",
                operation=WpStatisticsKeys.OPERATION_REMOVE_CURVE,
                curve_name=name,
                curve_dimension=curve_attrs.dimension.value,
                remaining_curves=len(self.curves)
            )

    def _split_2d_curve_remove_element(self, element_to_remove: str, parent_curve: CurveBasicAttributes) -> None:
        """拆分二维组合曲线，移除指定元素，剩余元素降级为一维曲线。

        Args:
            element_to_remove: 要删除的元素名称，如 "T2_VALUE[2]"
            parent_curve: 父二维组合曲线属性
        """
        parent_name = parent_curve.name

        # 获取原位置
        insertion_position = self._get_curve_insertion_position(parent_name)

        # 获取剩余元素
        remaining_elements = [
            elem for elem in parent_curve.element_names
            if elem != element_to_remove
        ]

        # 删除原二维组合曲线
        del self.curves[parent_name]

        # 创建新的有序字典，在原位置插入剩余的一维曲线
        new_curves = CaseInsensitiveDict()
        curve_items = list(self.curves.items())

        # 插入原位置之前的曲线
        for i in range(insertion_position):
            if i < len(curve_items):
                key, value = curve_items[i]
                new_curves[key] = value

        # 在原位置插入剩余元素作为一维曲线
        for element_name in remaining_elements:
            new_1d_curve = self._create_1d_from_2d_element(element_name, parent_curve)
            new_curves[element_name] = new_1d_curve

        # 插入原位置之后的曲线
        for i in range(insertion_position, len(curve_items)):
            key, value = curve_items[i]
            new_curves[key] = value

        # 更新curves字典
        self.curves = new_curves
        self.modified_at = datetime.now()

        logger.debug(
            "二维组合曲线元素删除完成，剩余元素降级为一维曲线",
            operation=WpStatisticsKeys.OPERATION_SPLIT_2D_CURVE,
            parent_curve=parent_name,
            removed_element=element_to_remove,
            remaining_elements=remaining_elements,
            new_1d_curves_count=len(remaining_elements),
            total_curves=len(self.curves)
        )

    def list_curves(self, **filters: Any) -> list[CurveBasicAttributes]:
        """列出曲线基本属性（支持过滤）。

        Args:
            **filters: 过滤条件，支持的字段：
                - category: 曲线类别
                - dimension: 曲线维数
                - data_type: 数据类型
                - is_well_identifier: 是否井名曲线
                - depth_role: 深度曲线角色
                - curve_class: WFS曲线类别
                - is_numeric: 是否数值类型

        Returns:
            list[CurveBasicAttributes]: 符合条件的曲线列表

        Examples:
            >>> # 获取所有曲线
            >>> all_curves = metadata.list_curves()
            >>>
            >>> # 获取测井曲线
            >>> logging_curves = metadata.list_curves(category=WpCurveCategory.LOGGING)
            >>>
            >>> # 获取二维组合曲线
            >>> two_d_curves = metadata.list_curves(dimension=WpCurveDimension.TWO_D)
            >>>
            >>> # 获取数值类型曲线
            >>> numeric_curves = metadata.list_curves(is_numeric=True)
        """
        result = list(self.curves.values())

        # 应用过滤条件（使用WpApiKeys常量，避免硬编码字符串）
        for key, value in filters.items():
            if key == WpApiKeys.FILTER_CATEGORY:
                result = [c for c in result if c.category == value]
            elif key == WpApiKeys.FILTER_DIMENSION:
                result = [c for c in result if c.dimension == value]
            elif key == WpApiKeys.FILTER_DATA_TYPE:
                result = [c for c in result if c.data_type == value]
            elif key == WpApiKeys.FILTER_IS_WELL_IDENTIFIER:
                result = [c for c in result if c.is_well_identifier == value]
            elif key == WpApiKeys.FILTER_DEPTH_ROLE:
                result = [c for c in result if c.depth_role == value]
            elif key == WpApiKeys.FILTER_IS_NUMERIC:
                result = [c for c in result if c.is_numeric() == value]
            elif key == WpApiKeys.FILTER_CURVE_CLASS:
                result = [c for c in result if c.curve_class == value]

        return result

    def get_well_identifier_curves(self) -> list[str]:
        """获取井名标识曲线名称列表。

        Returns:
            list[str]: 井名曲线名称列表

        Examples:
            >>> well_curves = metadata.get_well_identifier_curves()
            >>> print(well_curves)  # ['WELL', 'WELL_NAME']
        """
        return [
            curve.name for curve in self.curves.values()
            if curve.is_well_identifier
        ]

    def get_depth_reference_curves(self) -> list[str]:
        """获取深度参考曲线名称列表。

        Returns:
            list[str]: 深度曲线名称列表

        Examples:
            >>> depth_curves = metadata.get_depth_reference_curves()
            >>> print(depth_curves)  # ['MD', 'TVD', 'MD_Top', 'MD_Bottom']
        """
        return [
            curve.name for curve in self.curves.values()
            if curve.is_depth_curve()
        ]

    def get_system_curves(self) -> list[str]:
        """获取系统曲线名称列表（井名+深度）。

        系统曲线包括井名标识曲线和深度参考曲线，这些曲线通常不参与数据分析算法。

        Returns:
            list[str]: 系统曲线名称列表

        Examples:
            >>> system_curves = metadata.get_system_curves()
            >>> print(system_curves)  # ['WELL', 'MD', 'MD_Top', 'MD_Bottom']
        """
        well_curves = self.get_well_identifier_curves()
        depth_curves = self.get_depth_reference_curves()
        return well_curves + depth_curves

    def get_data_curves(
        self,
        dimensions: list[WpCurveDimension] | None = None,
        data_types: list[WpDataType] | None = None,
    ) -> list[str]:
        """获取数据曲线名称列表（非系统曲线），支持按维数和数据类型过滤。

        数据曲线包括测井曲线、计算曲线和自定义曲线，排除了系统曲线（如井名、深度）。
        此方法允许进一步根据曲线的维数和数据类型进行筛选。

        Args:
            dimensions (list[WpCurveDimension] | None, optional):
                要包含的曲线维数列表。如果为None，则不按维数过滤。
                默认为 None。
            data_types (list[WpDataType] | None, optional):
                要包含的曲线数据类型列表。如果为None，则不按数据类型过滤。
                默认为 None。

        Returns:
            list[str]: 符合条件的数据曲线名称列表。

        Examples:
            >>> # 假设 metadata 中包含 GR(1D, FLOAT), PHIT(1D, FLOAT), T2_VALUE(2D, FLOAT), FACIES(1D, STR)
            >>>
            >>> # 获取所有数据曲线
            >>> all_data_curves = metadata.get_data_curves()
            >>> print(sorted(all_data_curves))
            ['FACIES', 'GR', 'PHIT', 'T2_VALUE']

            >>> # 只获取一维(1D)数据曲线
            >>> one_d_curves = metadata.get_data_curves(dimensions=[WpCurveDimension.ONE_D])
            >>> print(sorted(one_d_curves))
            ['FACIES', 'GR', 'PHIT']

            >>> # 只获取浮点型(FLOAT)数据曲线
            >>> float_curves = metadata.get_data_curves(data_types=[WpDataType.FLOAT])
            >>> print(sorted(float_curves))
            ['GR', 'PHIT', 'T2_VALUE']

            >>> # 获取所有二维(2D)且为浮点型(FLOAT)的数据曲线
            >>> two_d_float_curves = metadata.get_data_curves(
            ...     dimensions=[WpCurveDimension.TWO_D_COMP],
            ...     data_types=[WpDataType.FLOAT]
            ... )
            >>> print(two_d_float_curves)
            ['T2_VALUE']
        """
        # 1. 获取所有系统曲线的名称，用于排除，这是比检查category更可靠的方法
        system_curves_set = set(self.get_system_curves())

        # 2. 为了提高查找效率，如果提供了过滤器，则转换为集合
        dimensions_set = set(dimensions) if dimensions is not None else None
        data_types_set = set(data_types) if data_types is not None else None

        filtered_curves = []
        for curve in self.curves.values():
            # 条件1: 必须是数据曲线 (非系统曲线)
            if curve.name in system_curves_set:
                continue

            # 条件2: 检查维数 (如果提供了维数过滤器)
            if dimensions_set is not None and curve.dimension not in dimensions_set:
                continue

            # 条件3: 检查数据类型 (如果提供了数据类型过滤器)
            if data_types_set is not None and curve.data_type not in data_types_set:
                continue

            filtered_curves.append(curve.name)

        return filtered_curves

    def get_numeric_curves(self) -> list[str]:
        """获取数值型曲线名称列表。

        Returns:
            list[str]: 数值型曲线名称列表

        Examples:
            >>> numeric_curves = metadata.get_numeric_curves()
            >>> print(numeric_curves)  # ['GR', 'PHIT', 'PERM']
        """
        return [
            curve.name for curve in self.curves.values()
            if curve.is_numeric()
        ]

    def get_analysis_suitable_curves(self) -> list[str]:
        """获取适合数据分析的曲线名称列表。

        自动过滤出适合进行数据分析（如PCA、聚类、回归等）的曲线：
        - 排除系统曲线（井名、深度等标识曲线）
        - 排除类别型曲线（CATEGORICAL类型）
        - 只保留数值型曲线（FLOAT、INT类型）
        - 只保留数据曲线（LOGGING、COMPUTED、CUSTOM类别）

        Returns:
            list[str]: 适合数据分析的曲线名称列表

        Examples:
            >>> analysis_curves = metadata.get_analysis_suitable_curves()
            >>> print(analysis_curves)  # ['GR', 'PHIT', 'PERM', 'T2_VALUE']
        """
        data_categories = WpCurveCategory.data_curves()
        return [
            curve.name for curve in self.curves.values()
            if (curve.category in data_categories and
                curve.is_numeric() and
                curve.curve_class != WpCurveClass.CATEGORICAL)
        ]

    def validate_required_normal_curves(self, required_curves: list[str]) -> list[str]:
        """验证数据集包含必需的普通曲线（在普通曲线范围内验证）。

        用户提供的required_curves列表本身就是普通曲线（不包括井名和深度曲线），
        验证时只在元数据的普通曲线中进行检验，返回缺失的曲线列表。
        支持大小写不敏感的曲线名称匹配。

        Args:
            required_curves: 必需的普通曲线名称列表（不包括井名和深度曲线）

        Returns:
            list[str]: 缺失的曲线名称列表，如果所有曲线都存在则返回空列表

        Examples:
            >>> # 验证SCAPE Stage-II算法所需曲线
            >>> required_curves = ['PHIT_NMR', 'T2LM', 'DT2_P50', 'DPHIT_NMR',
            ...                    'T2_P50', 'K_CORE_MD', 'K_LABEL_TYPE', 'T2_DIST']
            >>> missing = metadata.validate_required_normal_curves(required_curves)
            >>> if missing:
            ...     raise ValueError(f"数据集缺少必需曲线: {missing}")
            >>>
            >>> # 验证基础测井曲线
            >>> basic_curves = ['GR', 'PHIT', 'PERM']
            >>> missing = metadata.validate_required_normal_curves(basic_curves)
            >>> print(f"缺失曲线: {missing}")  # []
            >>>
            >>> # 如果某些曲线不存在
            >>> test_curves = ['GR', 'PHIT', 'NON_EXISTENT_CURVE']
            >>> missing = metadata.validate_required_normal_curves(test_curves)
            >>> print(f"缺失曲线: {missing}")  # ['NON_EXISTENT_CURVE']

        Note:
            - 使用大小写不敏感匹配，'GR' 和 'gr' 被视为相同曲线
            - 只在普通数据曲线范围内验证，不考虑系统曲线（井名、深度）
            - 返回缺失曲线列表，调用者可以根据需要决定如何处理
            - 适用于算法执行前的数据完整性检查
        """
        # 获取元数据中的普通数据曲线（排除系统曲线）
        available_normal_curves = set(self.get_data_curves())

        # 检查必需曲线中哪些在普通曲线范围内缺失
        missing_curves = [
            curve for curve in required_curves
            if curve not in available_normal_curves
        ]

        logger.debug(
            "普通曲线验证完成",
            operation="validate_required_normal_curves",
            required_count=len(required_curves),
            available_normal_count=len(available_normal_curves),
            missing_count=len(missing_curves),
            missing_curves=missing_curves,
            validation_passed=len(missing_curves) == 0
        )

        return missing_curves

    def get_curves_by_depth_role(self, role: WpDepthRole) -> list[str]:
        """按深度角色获取曲线名称列表。

        Args:
            role: 深度角色

        Returns:
            list[str]: 指定深度角色的曲线名称列表

        Examples:
            >>> # 获取单一深度曲线
            >>> single_depths = metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
            >>> print(single_depths)  # ['MD', 'TVD']

            >>> # 获取顶界深度曲线
            >>> top_depths = metadata.get_curves_by_depth_role(WpDepthRole.TOP)
            >>> print(top_depths)  # ['MD_Top', 'TOP_DEPTH']

            >>> # 获取底界深度曲线
            >>> bottom_depths = metadata.get_curves_by_depth_role(WpDepthRole.BOTTOM)
            >>> print(bottom_depths)  # ['MD_Bottom', 'BOTTOM_DEPTH']
        """
        return [
            curve.name for curve in self.curves.values()
            if curve.depth_role == role
        ]

    def get_depth_role_mapping(self) -> dict[WpDepthRole, list[str]]:
        """获取深度角色到曲线名称的映射。

        Returns:
            dict[WpDepthRole, list[str]]: 深度角色映射字典

        Examples:
            >>> role_mapping = metadata.get_depth_role_mapping()
            >>> print(role_mapping)
            # {
            #     WpDepthRole.SINGLE: ['MD', 'TVD'],
            #     WpDepthRole.TOP: ['MD_Top'],
            #     WpDepthRole.BOTTOM: ['MD_Bottom']
            # }
        """
        mapping: dict[WpDepthRole, list[str]] = {}
        for curve in self.curves.values():
            if curve.depth_role is None:
                continue
            if curve.depth_role not in mapping:
                mapping[curve.depth_role] = []
            mapping[curve.depth_role].append(curve.name)
        return mapping

    def rename_depth_reference_curves(
        self,
        target_depth_curves: list[str],
        *,
        update_dataframe_column_names: bool = True
    ) -> dict[str, str]:
        """重命名深度参考曲线以匹配目标深度曲线列表。

        **设计原则**：
        - 自动处理单深度和双深度情形
        - 保持深度曲线的相对顺序和角色
        - 支持DataFrame列名同步更新

        Args:
            target_depth_curves: 目标深度曲线名称列表
            update_dataframe_column_names: 是否同时更新dataframe_column_name属性

        Returns:
            dict[str, str]: 重命名映射 {原名称: 新名称}

        Raises:
            WpCurveMetadataError: 深度曲线数量不匹配时抛出异常

        Examples:
            >>> # 单深度情形：MD -> DEPTH
            >>> metadata.rename_depth_reference_curves(['DEPTH'])
            {'MD': 'DEPTH'}

            >>> # 双深度情形：TOP_MD, BOT_MD -> TOP_DEPTH, BOT_DEPTH
            >>> metadata.rename_depth_reference_curves(['TOP_DEPTH', 'BOT_DEPTH'])
            {'TOP_MD': 'TOP_DEPTH', 'BOT_MD': 'BOT_DEPTH'}

        Note:
            - 转发到service层实现具体逻辑
            - 按深度曲线在元数据中的顺序进行一对一映射
            - 自动更新曲线的name和dataframe_column_name属性

        References:
            《SCAPE_DDS_logwp_数据集合并.md》§3.3 - 深度列名统一处理
        """
        from logwp.models.curve.internal import metadata_rename

        return metadata_rename.rename_depth_reference_curves(
            metadata=self,
            target_depth_curves=target_depth_curves,
            update_dataframe_column_names=update_dataframe_column_names
        )

    def rename_well_identifier_curves(
        self,
        target_well_curves: list[str],
        *,
        update_dataframe_column_names: bool = True
    ) -> dict[str, str]:
        """重命名井标识符曲线以匹配目标井标识符曲线列表。

        **设计原则**：
        - 自动处理多井标识符情形
        - 保持井标识符曲线的相对顺序
        - 支持DataFrame列名同步更新

        Args:
            target_well_curves: 目标井标识符曲线名称列表
            update_dataframe_column_names: 是否同时更新dataframe_column_name属性

        Returns:
            dict[str, str]: 重命名映射 {原名称: 新名称}

        Raises:
            WpCurveMetadataError: 井标识符曲线数量不匹配时抛出异常

        Examples:
            >>> # 单井标识符：WELL -> WELL_NAME
            >>> metadata.rename_well_identifier_curves(['WELL_NAME'])
            {'WELL': 'WELL_NAME'}

            >>> # 多井标识符：WELL, FIELD -> WELL_NAME, FIELD_NAME
            >>> metadata.rename_well_identifier_curves(['WELL_NAME', 'FIELD_NAME'])
            {'WELL': 'WELL_NAME', 'FIELD': 'FIELD_NAME'}

        Note:
            - 转发到service层实现具体逻辑
            - 按井标识符曲线在元数据中的顺序进行一对一映射
            - 自动更新曲线的name和dataframe_column_name属性

        References:
            《SCAPE_DDS_logwp_数据集合并.md》§3.3 - 井名列统一处理
        """
        from logwp.models.curve.internal import metadata_rename

        return metadata_rename.rename_well_identifier_curves(
            metadata=self,
            target_well_curves=target_well_curves,
            update_dataframe_column_names=update_dataframe_column_names
        )

    def add_standard_system_curves(
        self,
        well_name: str | None = None,
        depth_name: str | None = None,
        depth_unit: str | None = None,
        dataset_type: WpDsType = WpDsType.CONTINUOUS
    ) -> None:
        """添加标准系统曲线（井名+深度）的便捷方法。

        为数据集添加最基本的系统曲线：井名标识曲线和深度参考曲线。
        这是所有测井数据集都需要的通用系统曲线组合。

        Args:
            well_name: 井名曲线名称，默认为WpStandardColumn.WELL_NAME
            depth_name: 深度曲线名称，默认为WpStandardColumn.DEPTH
            depth_unit: 深度单位，默认为WpDepthUnit.METER
            dataset_type: 数据集类型，CONTINUOUS/POINT使用单深度，INTERVAL使用双深度

        Examples:
            >>> # 添加标准系统曲线（连续型数据集）
            >>> metadata = CurveMetadata()
            >>> metadata.add_standard_system_curves()
            >>> assert metadata.has_curve(WpStandardColumn.WELL_NAME)
            >>> assert metadata.has_curve(WpStandardColumn.DEPTH)
            >>>
            >>> # 添加自定义名称的系统曲线
            >>> metadata.add_standard_system_curves(
            ...     well_name="WELL_NO", depth_name="MD", depth_unit=WpDepthUnit.FEET
            ... )
            >>> assert metadata.has_curve("WELL_NO")
            >>> assert metadata.has_curve("MD")
            >>>
            >>> # 添加区间型数据集的系统曲线
            >>> metadata.add_standard_system_curves(dataset_type=WpDsType.INTERVAL)
            >>> assert metadata.has_curve(WpStandardColumn.DEPTH_TOP)
            >>> assert metadata.has_curve(WpStandardColumn.DEPTH_BOTTOM)

        Note:
            - 这是通用的业务需求，所有数据集都需要井名和深度曲线
            - 支持连续型/离散型（单深度）和区间型（双深度）数据集
            - 如果曲线已存在，会跳过添加并记录警告日志
        """
        # 添加井名曲线
        well_curve = CurveBasicAttributes.create_well_identifier_curve(
            name=well_name, description=WpCurveDescription.WELL_IDENTIFIER_CURVE
        )

        if not self.has_curve(well_curve.name):
            self.add_curve(well_curve)
            logger.debug(WpLogMessage.CURVE_ADDED, curve_name=well_curve.name)
        else:
            logger.warning(WpLogMessage.CURVE_EXISTS_SKIP, curve_name=well_curve.name)

        # 根据数据集类型添加深度曲线
        if dataset_type == WpDsType.INTERVAL:
            # 区间型数据集：添加顶深和底深曲线
            top_curve, bottom_curve = CurveBasicAttributes.create_interval_depth_reference_curves(
                unit=depth_unit,
                top_description=WpCurveDescription.INTERVAL_TOP_DEPTH_CURVE,
                bottom_description=WpCurveDescription.INTERVAL_BOTTOM_DEPTH_CURVE
            )

            if not self.has_curve(top_curve.name):
                self.add_curve(top_curve)
                logger.debug(WpLogMessage.TOP_DEPTH_CURVE_ADDED, curve_name=top_curve.name)
            else:
                logger.warning(WpLogMessage.CURVE_EXISTS_SKIP, curve_name=top_curve.name)

            if not self.has_curve(bottom_curve.name):
                self.add_curve(bottom_curve)
                logger.debug(WpLogMessage.BOTTOM_DEPTH_CURVE_ADDED, curve_name=bottom_curve.name)
            else:
                logger.warning(WpLogMessage.CURVE_EXISTS_SKIP, curve_name=bottom_curve.name)
        else:
            # 连续型/离散型数据集：添加单深度曲线
            depth_curve = CurveBasicAttributes.create_depth_reference_curve(
                name=depth_name, unit=depth_unit, description=WpCurveDescription.DEPTH_REFERENCE_CURVE
            )

            if not self.has_curve(depth_curve.name):
                self.add_curve(depth_curve)
                logger.debug(WpLogMessage.DEPTH_CURVE_ADDED, curve_name=depth_curve.name)
            else:
                logger.warning(WpLogMessage.CURVE_EXISTS_SKIP, curve_name=depth_curve.name)

    def get_2d_composite_curves(self) -> dict[str, list[str]]:
        """获取二维组合曲线映射。

        Returns:
            dict[str, list[str]]: 二维组合曲线映射，键为基础名称，值为元素名称列表

        Examples:
            >>> two_d_curves = metadata.get_2d_curves()
            >>> print(two_d_curves)  # {'T2_VALUE': ['T2_VALUE[1]', 'T2_VALUE[2]']}
        """
        result = {}
        for curve in self.curves.values():
            if curve.is_2d_composite_curve() and curve.element_names:
                result[curve.name] = curve.element_names.copy()
        return result

    def has_curve(self, name: WpCurveName) -> bool:
        """检查曲线是否存在。

        Args:
            name: 曲线名称（大小写不敏感）

        Returns:
            bool: 曲线是否存在

        Examples:
            >>> exists = metadata.has_curve("GR")
            >>> exists = metadata.has_curve("gr")  # 大小写不敏感
        """
        return name in self.curves

    def validate_curves_exist(self, curve_names: list[str]) -> None:
        """验证曲线列表中的所有曲线都存在。

        Args:
            curve_names: 要验证的曲线名称列表

        Raises:
            WpCurveMetadataError: 当有曲线不存在时抛出

        Examples:
            >>> metadata.validate_curves_exist(["GR", "PHIT"])  # 正常情况
            >>> metadata.validate_curves_exist(["GR", "NONEXISTENT"])  # 抛出异常
        """
        missing_curves = [name for name in curve_names if not self.has_curve(name)]
        if missing_curves:
            raise WpCurveMetadataError(
                f"以下曲线不存在: {missing_curves}",
                context=ErrorContext(
                    operation="validate_curves_exist",
                    additional_info={
                        "missing_curves": missing_curves,
                        "total_requested": len(curve_names),
                        "available_curves": list(self.curves.keys())
                    }
                )
            )

    def get_curve_count(self) -> int:
        """获取曲线数量。

        Returns:
            int: 曲线数量
        """
        return len(self.curves)

    def translate_query_for_dataframe(self, query_string: str) -> str:
        """将包含占位符的查询字符串转换为DataFrame友好列名。

        支持pandas DataFrame.query()方法，将用户使用占位符格式的查询条件转换为
        实际的DataFrame列名（友好名称）。要求查询字符串中的曲线名必须使用
        ${curve_name} 格式的占位符。

        占位符格式要求：
        - 一维曲线：${CURVE_NAME}，例如 ${GR}、${DEN}
        - 二维组合曲线元素：${CURVE_NAME[index]}，例如 ${T2_VALUE[1]}、${PHI_T2_DIST[3]}
        - 不允许使用二维组合曲线基础名称：${T2_VALUE} 是错误的，必须使用 ${T2_VALUE[1]}

        转换规则：
        - 一维曲线占位符转换为对应的dataframe_column_name
        - 二维组合曲线元素占位符转换为对应的dataframe_element_names中的友好名称
        - 其他内容（操作符、数值、括号等）保持不变

        Args:
            query_string: 包含占位符的查询条件字符串

        Returns:
            str: 转换后的查询字符串，使用DataFrame友好列名

        Raises:
            WpCurveMetadataError: 当占位符中包含二维组合曲线基础名称时
            WpCurveMetadataError: 当占位符中包含不存在的曲线名时
            WpCurveMetadataError: 当二维组合曲线元素索引无效时

        Examples:
            >>> # 创建曲线元数据
            >>> metadata = CurveMetadata()
            >>> md_attrs = CurveBasicAttributes.create_1d_curve(name="MD", unit="m")
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve(name="GR", unit="API")
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve(name="T2_VALUE", element_count=3, unit="ms")
            >>> metadata.add_curve(md_attrs)
            >>> metadata.add_curve(gr_attrs)
            >>> metadata.add_curve(t2_attrs)
            >>>
            >>> # 一维曲线查询
            >>> query = "${GR} > 50 and ${MD} < 2000"
            >>> result = metadata.translate_query_for_dataframe(query)
            >>> print(result)  # "GR > 50 and MD < 2000"
            >>>
            >>> # 二维组合曲线元素查询
            >>> query = "${T2_VALUE[1]} > 10 and ${T2_VALUE[2]} < 20"
            >>> result = metadata.translate_query_for_dataframe(query)
            >>> print(result)  # "T2_VALUE_1 > 10 and T2_VALUE_2 < 20"
            >>>
            >>> # 复合查询
            >>> query = "(${GR} > 50 or ${MD} < 1500) and ${T2_VALUE[1]} > 10"
            >>> result = metadata.translate_query_for_dataframe(query)
            >>> print(result)  # "(GR > 50 or MD < 1500) and T2_VALUE_1 > 10"
            >>>
            >>> # 错误示例：使用二维组合曲线基础名称
            >>> query = "${T2_VALUE} > 10"  # 错误！应该使用 ${T2_VALUE[1]}
            >>> metadata.translate_query_for_dataframe(query)  # 抛出 WpCurveMetadataError
            >>>
            >>> # 错误示例：曲线不存在
            >>> query = "${UNKNOWN_CURVE} > 10"
            >>> metadata.translate_query_for_dataframe(query)  # 抛出 WpCurveMetadataError

        Usage with DataFrame.query():
            >>> # 完整使用示例
            >>> query = "${GR} > 50 and ${T2_VALUE[1]} > 10"
            >>> translated_query = metadata.translate_query_for_dataframe(query)
            >>> filtered_df = dataset.df.query(translated_query)
            >>>
            >>> # 或者结合其他pandas功能
            >>> query = "${GR} > @threshold and ${T2_VALUE[1]} > 10"
            >>> translated_query = metadata.translate_query_for_dataframe(query)
            >>> filtered_df = dataset.df.query(translated_query, local_dict={'threshold': 50})

        Performance Notes:
            - 时间复杂度：O(n×m)，其中n为查询字符串长度，m为占位符数量
            - 空间复杂度：O(n)，需要构建新的查询字符串
            - 建议缓存转换结果以提高重复查询的性能

        References:
            pandas DataFrame.query() 文档：
            https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.query.html
            《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata服务层重构
        """
        from logwp.models.curve.internal import query_translation

        # 转发到服务层
        return query_translation.translate_query_for_dataframe(
            query_string=query_string,
            curves_dict=self.curves
        )

    def __len__(self) -> int:
        """返回曲线数量。

        支持使用内置的 len() 函数获取曲线元数据中的曲线总数。

        Returns:
            int: 曲线总数

        Examples:
            >>> metadata = CurveMetadata()
            >>> len(metadata)  # 0
            0
            >>>
            >>> # 添加一些曲线
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve(name="GR", unit="API")
            >>> metadata.add_curve(gr_attrs)
            >>> len(metadata)  # 1
            1
            >>>
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve(name="T2_VALUE", element_count=3, unit="ms")
            >>> metadata.add_curve(t2_attrs)
            >>> len(metadata)  # 2
            2

        Note:
            - 一维曲线和二维组合曲线都计为1个曲线
            - 二维组合曲线的元素不单独计数
            - 等价于 len(metadata.curves)
        """
        return len(self.curves)

    def clear(self) -> None:
        """清空所有曲线元数据。

        Examples:
            >>> metadata.clear()
            >>> assert metadata.get_curve_count() == 0
        """
        cleared_count = len(self.curves)
        self.curves.clear()
        self.modified_at = datetime.now()

        logger.info(
            "曲线元数据已清空",
            operation=WpStatisticsKeys.OPERATION_CLEAR_CURVES,
            cleared_count=cleared_count
        )

    def get_all_dataframe_column_names(self) -> list[str]:
        """获取所有DataFrame友好的列名。

        包含一维曲线的dataframe_column_name和二维组合曲线的dataframe_element_names。

        Returns:
            list[str]: 包含一维曲线名称和二维组合曲线元素名称的完整列表

        Examples:
            >>> metadata = CurveMetadata()
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", unit="API")
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve("T2_VALUE", element_count=3)
            >>> metadata.add_curve(gr_attrs)
            >>> metadata.add_curve(t2_attrs)
            >>> column_names = metadata.get_all_dataframe_column_names()
            >>> print(column_names)  # ['GR', 'T2_VALUE_1', 'T2_VALUE_2', 'T2_VALUE_3']
        """
        column_names = []
        for curve in self.curves.values():
            if curve.is_2d_composite_curve() and curve.dataframe_element_names:
                column_names.extend(curve.dataframe_element_names)
            else:
                column_names.append(curve.dataframe_column_name)
        return column_names

    def get_dataframe_columns_for_curves(self, curve_names: list[str]) -> list[str]:
        """获取曲线对应的DataFrame列名列表。

        支持一维曲线和二维组合曲线的批量映射。

        Args:
            curve_names: 曲线名称列表

        Returns:
            list[str]: DataFrame列名列表

        Raises:
            WpCurveMetadataError: 当曲线不存在时

        Examples:
            >>> metadata = CurveMetadata()
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", unit="API")
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve("T2_VALUE", element_count=2)
            >>> metadata.add_curve(gr_attrs)
            >>> metadata.add_curve(t2_attrs)
            >>>
            >>> # 一维曲线映射
            >>> columns = metadata.get_dataframe_columns_for_curves(["GR"])
            >>> print(columns)  # ['GR']
            >>>
            >>> # 二维组合曲线映射
            >>> columns = metadata.get_dataframe_columns_for_curves(["T2_VALUE"])
            >>> print(columns)  # ['T2_VALUE_1', 'T2_VALUE_2']
            >>>
            >>> # 混合映射
            >>> columns = metadata.get_dataframe_columns_for_curves(["GR", "T2_VALUE"])
            >>> print(columns)  # ['GR', 'T2_VALUE_1', 'T2_VALUE_2']
        """
        column_names = []
        for curve_name in curve_names:
            curve = self.get_curve(curve_name)
            if curve is None:
                raise WpCurveMetadataError(
                    f"曲线 '{curve_name}' 不存在",
                    context=ErrorContext(
                        operation="get_dataframe_columns_for_curves",
                        additional_info={
                            "curve_name": curve_name,
                            "available_curves": list(self.curves.keys()),
                            "requested_curves": curve_names
                        }
                    )
                )

            if curve.is_2d_composite_curve() and curve.dataframe_element_names:
                column_names.extend(curve.dataframe_element_names)
            else:
                column_names.append(curve.dataframe_column_name)

        return column_names

    def get_curves_for_dataframe_columns(self, column_names: list[str]) -> list[str]:
        """获取DataFrame列名对应的曲线名称列表。

        支持一维曲线和二维组合曲线元素的批量反向映射。

        Args:
            column_names: DataFrame列名列表

        Returns:
            list[str]: 曲线名称列表

        Raises:
            WpCurveMetadataError: 当DataFrame列名无法映射到曲线时

        Examples:
            >>> metadata = CurveMetadata()
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", unit="API")
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve("T2_VALUE", element_count=2)
            >>> metadata.add_curve(gr_attrs)
            >>> metadata.add_curve(t2_attrs)
            >>>
            >>> # 一维曲线反向映射
            >>> curves = metadata.get_curves_for_dataframe_columns(["GR"])
            >>> print(curves)  # ['GR']
            >>>
            >>> # 二维组合曲线元素反向映射
            >>> curves = metadata.get_curves_for_dataframe_columns(["T2_VALUE_1", "T2_VALUE_2"])
            >>> print(curves)  # ['T2_VALUE', 'T2_VALUE']
            >>>
            >>> # 混合反向映射
            >>> curves = metadata.get_curves_for_dataframe_columns(["GR", "T2_VALUE_1"])
            >>> print(curves)  # ['GR', 'T2_VALUE']
        """
        curve_names = []
        for column_name in column_names:
            curve = self.get_curve_by_dataframe_name(column_name)
            if curve is None:
                raise WpCurveMetadataError(
                    f"DataFrame列名 '{column_name}' 无法映射到曲线",
                    context=ErrorContext(
                        operation="get_curves_for_dataframe_columns",
                        column_name=column_name,
                        additional_info={
                            "available_columns": self.get_all_dataframe_column_names(),
                            "requested_columns": column_names
                        }
                    )
                )
            curve_names.append(curve.name)

        return curve_names

    def get_curve_by_dataframe_name(self, column_name: str) -> CurveBasicAttributes | None:
        """根据DataFrame列名获取对应的曲线元数据信息。

        Args:
            column_name: DataFrame列名

        Returns:
            CurveBasicAttributes | None: 对应的曲线信息，如果找不到则返回None

        Examples:
            >>> metadata = CurveMetadata()
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", unit="API")
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve("T2_VALUE", element_count=3)
            >>> metadata.add_curve(gr_attrs)
            >>> metadata.add_curve(t2_attrs)
            >>>
            >>> # 查询一维曲线
            >>> curve = metadata.get_curve_by_dataframe_name("GR")
            >>> print(curve.name)  # "GR"
            >>>
            >>> # 查询二维组合曲线的元素
            >>> curve = metadata.get_curve_by_dataframe_name("T2_VALUE_1")
            >>> print(curve.name)  # "T2_VALUE"
            >>> print(curve.is_2d_composite_curve())  # True
        """
        # 首先尝试直接匹配曲线名称
        for curve in self.curves.values():
            if curve.dataframe_column_name == column_name:
                return curve
            # 对于二维组合曲线，检查是否匹配元素名称
            if curve.dataframe_element_names and column_name in curve.dataframe_element_names:
                return curve

        return None

    def expand_curve_names(
        self,
        curve_list: list[str],
        mode: CurveExpansionMode = CurveExpansionMode.EXPANDED
    ) -> list[str]:
        """展开混合紧凑曲线名称列表。

        将用户提供的混合紧凑曲线列表展开为指定模式的列表。支持一维曲线、二维组合曲线基础名称
        和二维组合曲线元素的混合输入。

        Args:
            curve_list: 混合紧凑曲线列表，支持：
                - 一维曲线：['GR', 'DEN']
                - 二维组合曲线基础名称：['T2_VALUE'] （会展开为所有元素）
                - 二维组合曲线元素：['T2_VALUE[1]'] （不会展开，直接使用）
                - 混合格式：['GR', 'T2_VALUE', 'T2_TIME[1]']
            mode: 展开模式
                - EXPANDED: 展开曲线列表，如 ['GR', 'T2_VALUE[1]', 'T2_VALUE[2]', ...]
                - DATAFRAME: DataFrame列名列表，如 ['GR', 'T2_VALUE_1', 'T2_VALUE_2', ...]

        Returns:
            list[str]: 展开后的曲线名称列表

        Raises:
            WpCurveMetadataError: 当曲线列表中包含未知曲线或存在二维组合曲线表示冲突时抛出

        Examples:
            >>> metadata = CurveMetadata()
            >>> # 假设已添加GR、DEN（一维）和T2_VALUE（4维二维组合曲线）
            >>>
            >>> # 传统紧凑列表（保持向后兼容）
            >>> compact_list = ['GR', 'DEN', 'T2_VALUE']
            >>> expanded = metadata.expand_curve_names(compact_list, CurveExpansionMode.EXPANDED)
            >>> print(expanded)  # ['GR', 'DEN', 'T2_VALUE[1]', 'T2_VALUE[2]', 'T2_VALUE[3]', 'T2_VALUE[4]']
            >>>
            >>> # 混合列表（新功能）
            >>> mixed_list = ['GR', 'T2_VALUE', 'T2_TIME[1]']
            >>> expanded = metadata.expand_curve_names(mixed_list, CurveExpansionMode.EXPANDED)
            >>> print(expanded)  # ['GR', 'T2_VALUE[1]', 'T2_VALUE[2]', 'T2_VALUE[3]', 'T2_VALUE[4]', 'T2_TIME[1]']
            >>>
            >>> # 冲突检测
            >>> conflict_list = ['GR', 'T2_VALUE', 'T2_VALUE[2]']
            >>> metadata.expand_curve_names(conflict_list)  # 抛出 WpCurveMetadataError
        """
        from logwp.models.curve.internal import curve_expansion
        from logwp.models.curve.internal.query_translation import translate_2d_curve_element

        # 转发到服务层，传递必要的函数引用
        return curve_expansion.expand_curve_names(
            curve_list=curve_list,
            mode=mode,
            curves_dict=self.curves,
            analyze_curve_list_func=self.analyze_curve_list,
            translate_2d_curve_element_func=lambda element_ref: translate_2d_curve_element(element_ref, self.curves)
        )

    def analyze_curve_list(self, curve_names: list[str]) -> CurveListAnalysis:
        """分析混合紧凑曲线列表。

        分析用户提供的曲线列表，识别每个曲线项的类型，判断列表的整体类型，
        检测二维组合曲线的表示冲突，并提供详细的分析结果。

        Args:
            curve_names: 混合紧凑曲线列表
                - 支持一维曲线：["GR", "DEN"]
                - 支持二维组合曲线基础名称：["T2_VALUE"]
                - 支持二维组合曲线元素：["T2_VALUE[2]"]
                - 支持混合格式：["GR", "T2_VALUE", "T2_TIME[10]"]

        Returns:
            CurveListAnalysis: 完整的分析结果

        Examples:
            >>> # 纯一维曲线
            >>> result = metadata.analyze_curve_list(["GR", "DEN", "PHIT"])
            >>> assert result.list_type == CurveListType.PURE_1D

            >>> # 混合格式
            >>> result = metadata.analyze_curve_list(["GR", "T2_VALUE", "T2_TIME[10]"])
            >>> assert result.list_type == CurveListType.MIXED

            >>> # 检测冲突
            >>> result = metadata.analyze_curve_list(["GR", "T2_VALUE", "T2_VALUE[2]"])
            >>> assert result.has_conflicts is True
            >>> assert len(result.conflicts) == 1

        References:
            《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata服务层重构
        """
        from logwp.models.curve.internal import curve_analysis

        # 转发到服务层，传递必要的函数引用
        return curve_analysis.analyze_curve_list(
            curve_names=curve_names,
            curves_dict=self.curves,
            is_2d_element_name_func=self._is_2d_element_name,
            find_parent_2d_curve_func=self._find_parent_2d_curve
        )

    def rename_curves_for_ml(self, name_mapping: CaseInsensitiveDict) -> CaseInsensitiveDict:
        """
        根据提供的映射表，重命名曲线以适配机器学习算法的输入要求。

        此方法会就地修改当前的CurveMetadata对象，并返回一个用于
        同步更新DataFrame列名的映射字典。它确保了曲线名和其对应的
        DataFrame列名都是机器学习友好的。

        Args:
            name_mapping (Dict[str, str]): 映射字典，格式为 {'旧曲线名': '新曲线名'}。

        Returns:
            CaseInsensitiveDict: DataFrame列名映射，格式为 {'旧列名': '新列名'}。

        Raises:
            WpCurveMetadataError: 如果映射中指定的旧曲线名在元数据中不存在。

        Examples:
            >>> rename_map = {'Porosity_Log': 'PHIT_NMR', 'Core_Perm': 'K_LABEL'}
            >>> column_map = metadata.rename_curves_for_ml(rename_map)
            >>> print(column_map)
            {'Porosity_Log': 'PHIT_NMR', 'Core_Perm': 'K_LABEL'}
        """
        from logwp.models.curve.internal import metadata_rename_ml
        return metadata_rename_ml.rename_curves_for_ml_service(self, name_mapping)

    @staticmethod
    def from_dataframe(df: "pd.DataFrame") -> tuple["CurveMetadata", bool]:
        """从DataFrame推导并创建曲线元数据。

        这是一个便捷的静态方法，用于从一个pandas DataFrame的列结构中
        自动推断出曲线元数据。它会识别标准列（井名、深度）、
        二维组合曲线，并按“井名、深度、其他”的顺序排列。

        Args:
            df: 包含测井数据的pandas DataFrame。

        Returns:
            一个元组，包含:
            - CurveMetadata: 推断出的曲线元数据对象。
            - bool: 一个布尔值，如果推断出是区间型数据（双深度），则为True。
        """
        from logwp.models.curve.internal import dataframe_inference

        return dataframe_inference.from_dataframe_internal(df)

    def create_curve_to_columns_map(self) -> dict[str, list[str]]:
        """
        根据当前的元数据，生成从逻辑曲线名到DataFrame列名的映射。

        这个便捷方法对于构建WpDataFrameBundle的`curve_to_columns_map`属性
        非常有用。

        Returns:
            一个字典，键是逻辑曲线名，值是对应的DataFrame列名列表。
            - 对于一维曲线: {'GR': ['GR']}
            - 对于二维组合曲线: {'T2_VALUE': ['T2_VALUE_1', 'T2_VALUE_2', ...]}
        """
        mapping = {}
        for curve in self.curves.values():
            # 检查是否为二维组合曲线且有对应的DataFrame元素列名
            if curve.is_2d_composite_curve() and curve.dataframe_element_names:
                mapping[curve.name] = curve.dataframe_element_names
            else:
                # 否则，视为一维曲线
                mapping[curve.name] = [curve.dataframe_column_name]
        return mapping














    def extract_metadata(self, curve_list: list[str]) -> CurveMetadata:
        """根据曲线列表提取曲线元数据子集。

        从当前曲线元数据中提取指定曲线的元数据，支持二维组合曲线元素的智能降级处理。
        当曲线列表中包含二维组合曲线元素时，会将其降级为一维曲线。

        Args:
            curve_list: 曲线列表，支持：
                - 一维曲线：["GR", "DEN"] （直接复制）
                - 二维组合曲线基础名称：["T2_VALUE"] （完整复制）
                - 二维组合曲线元素：["T2_VALUE[1]"] （降级为一维曲线）
                - 混合格式：["GR", "T2_VALUE", "T2_TIME[1]"]

        Returns:
            CurveMetadata: 提取的曲线元数据子集

        Raises:
            WpCurveMetadataError: 当曲线列表中包含未知曲线或存在二维组合曲线表示冲突时抛出

        Examples:
            >>> # 基本提取
            >>> subset = metadata.extract_metadata(["GR", "DEN", "PHIT"])
            >>> assert len(subset.curves) == 3

            >>> # 二维组合曲线完整提取
            >>> subset = metadata.extract_metadata(["GR", "T2_VALUE"])
            >>> assert subset.curves["T2_VALUE"].is_2d_composite_curve()

            >>> # 二维组合曲线元素降级
            >>> subset = metadata.extract_metadata(["GR", "T2_VALUE[1]", "T2_VALUE[3]"])
            >>> assert len(subset.curves) == 3
            >>> assert not subset.curves["T2_VALUE[1]"].is_2d_composite_curve()  # 降级为一维
            >>> assert not subset.curves["T2_VALUE[3]"].is_2d_composite_curve()  # 降级为一维

            >>> # 混合提取
            >>> subset = metadata.extract_metadata(["GR", "T2_VALUE", "T2_TIME[1]"])
            >>> assert len(subset.curves) == 3
            >>> assert subset.curves["T2_VALUE"].is_2d_composite_curve()      # 完整二维组合曲线
            >>> assert not subset.curves["T2_TIME[1]"].is_2d_composite_curve()  # 降级一维曲线

        Note:
            - 二维组合曲线元素降级时会继承父曲线的基本属性（单位、数据类型、描述等）
            - 降级后的一维曲线使用元素名称作为曲线名（如 "T2_VALUE[1]"）
            - DataFrame列名会自动转换为友好格式（如 "T2_VALUE_1"）
            - 保持曲线的添加顺序与输入列表顺序一致

        References:
            《SCAPE_DDS_详细设计_logwp.md》§4.3 - CurveMetadata元数据提取重构
        """
        from logwp.models.curve.internal import metadata_extraction

        # 转发到服务层，传递必要的函数引用
        return metadata_extraction.extract_metadata(
            curve_list=curve_list,
            source_metadata=self,
            analyze_curve_list_func=self.analyze_curve_list
        )

    def generate_summary(self) -> dict[str, Any]:
        """生成曲线元数据概况。

        生成包含曲线数量、维数分布、类别分布、数据类型分布等统计信息的概况。

        Returns:
            dict[str, Any]: 曲线元数据概况数据
                - total_curves: 总曲线数
                - by_dimension: 按维数分布
                - by_category: 按类别分布
                - by_data_type: 按数据类型分布
                - by_curve_class: 按曲线类别分布
                - curve_attributes: 详细的曲线属性列表

        Examples:
            >>> metadata = CurveMetadata()
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve("GR", unit="API")
            >>> metadata.add_curve(gr_attrs)
            >>> summary = metadata.generate_summary()
            >>> print(f"总曲线数: {summary['total_curves']}")
            >>> print(f"一维曲线: {summary['by_dimension']['1d_curves']}")
            >>> print(f"详细属性: {len(summary['curve_attributes'])} 条")

        References:
            《SCAPE_DDS_logwp_generate_summary.md》§4.6 - 曲线统计服务设计
        """
        from logwp.models.constants import WpCurveCategory, WpDataType, WpCurveDimension, WpCurveClass
        from logwp.models.internal.summary_constants import SummaryKeys

        summary = {
            SummaryKeys.TOTAL_CURVES: len(self.curves),
            SummaryKeys.BY_DIMENSION: {
                SummaryKeys.ONE_D_CURVES: len([c for c in self.curves.values() if c.dimension == WpCurveDimension.ONE_D]),
                SummaryKeys.TWO_D_CURVES: len([c for c in self.curves.values() if c.dimension == WpCurveDimension.TWO_D_COMP])
            },
            SummaryKeys.BY_CATEGORY: {
                category.value: len([c for c in self.curves.values() if c.category == category])
                for category in WpCurveCategory
            },
            SummaryKeys.BY_DATA_TYPE: {
                data_type.value: len([c for c in self.curves.values() if c.data_type == data_type])
                for data_type in WpDataType
            },
            SummaryKeys.BY_CURVE_CLASS: {
                SummaryKeys.CATEGORICAL: len([c for c in self.curves.values() if c.curve_class == WpCurveClass.CATEGORICAL]),
                SummaryKeys.NORMAL: len([c for c in self.curves.values() if c.curve_class == WpCurveClass.NORMAL or c.curve_class is None])
            },
            "curve_attributes": [
                curve_attrs._to_summary_dict() for curve_attrs in self.curves.values()
            ]
        }

        return summary
