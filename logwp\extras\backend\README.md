# BackendService: 统一计算后端服务

> **版本**: 1.0
> **状态**: 已实现

## 一、 `BackendService` 设计与使用

### 1.1 设计哲学

`BackendService` 是 SCAPE 项目中用于统一CPU和GPU计算的核心抽象层。其设计遵循以下原则：

- **统一接口**: 为上层应用（如 `swift_pso`）提供一个与具体计算库（NumPy/CuPy）无关的、一致的API。所有计算操作都通过 `BackendService` 实例进行，而不是直接调用 `np` 或 `cp`。
- **依赖注入**: `BackendService` 实例通过依赖注入的方式传递给需要它的函数或类。这使得代码更易于测试和维护，并实现了计算逻辑与底层实现的解耦。
- **类型安全**: 接口由 `Protocol` 定义，确保了静态类型检查的完整性和IDE的智能提示。

### 1.2 如何获取服务实例

获取 `BackendService` 实例的唯一入口是 `logwp.extras.backend.internal.factory` 中的工厂函数。

**推荐方式：按名称创建**

这是最常用和最明确的方式。

```python
from logwp.extras.backend.internal.factory import create_backend_service_by_name

# 获取CPU后端服务
cpu_service = create_backend_service_by_name('cpu')

# 获取GPU后端服务 (如果GPU可用)
gpu_service = create_backend_service_by_name('gpu')
```

### 1.3 基本使用方法

一旦获取了服务实例，就可以像使用 `numpy` 或 `cupy` 模块一样使用它。

```python
import numpy as np

def my_computation(data_np: np.ndarray, service: BackendService):
    """一个使用BackendService的示例函数。"""

    # 1. 将数据移动到目标后端
    data_backend = service.to_backend(data_np)

    # 2. 使用服务接口进行计算
    data_sum = service.sum(data_backend)
    data_mean = service.mean(data_backend)

    # 3. 将结果安全地转换为Python标量
    sum_scalar = service.as_scalar(data_sum)
    mean_scalar = service.as_scalar(data_mean)

    print(f"使用 {service.name} 后端计算完成。")
    print(f"Sum: {sum_scalar}, Mean: {mean_scalar}")

    # 4. 将最终数组结果传回CPU
    return service.to_cpu(data_backend)
```

---

## 二、 CPU/GPU 混合编程注意事项与技巧

在 `BackendService` 框架下进行开发时，遵循以下最佳实践可以有效避免常见的错误和陷阱。

### 陷阱 1: 数据未在目标设备上 (`TypeError`)

这是最常见也是最核心的错误。当使用GPU服务 (`CupyService`) 时，所有传递给其计算方法的数组参数**必须**是位于GPU上的CuPy数组。

**❌ 错误示例**:
```python
# service 是一个 CupyService 实例
# np_array 是一个 NumPy 数组

service.sum(np_array)  # 将会抛出 TypeError: Unsupported type <class 'numpy.ndarray'>
```

**✅ 正确做法**: 在计算前，使用 `to_backend()` 转换数据。
```python
# service 是一个 CupyService 实例
gpu_array = service.to_backend(np_array)

# 现在可以安全地进行计算
service.sum(gpu_array)
```

**最佳实践**: 遵循“**先准备数据，再进行计算**”的模式。在进入计算密集型函数或循环之前，将所有需要用到的数据一次性转换到目标后端。

### 陷阱 2: `if` 语句中的GPU标量 (`TypeError`)

一个GPU上的0维数组（标量）不能被Python的 `if` 语句直接评估。

**❌ 错误示例**:
```python
gpu_scalar = service.sum(gpu_array) # 这是一个0维CuPy数组

if gpu_scalar > 10: # 将会抛出 TypeError
    # ...
```

**✅ 正确做法**: 使用 `as_scalar()` 将GPU标量安全地转换为Python原生类型。
```python
gpu_scalar = service.sum(gpu_array)

if service.as_scalar(gpu_scalar) > 10: # 正确
    # ...
```

**关键技巧**: 任何时候需要将一个可能在GPU上的值用于条件判断、日志记录、JSON序列化或传递给非GPU感知的库时，都应使用 `as_scalar()`。

### 陷阱 3: 转换非数值数据到GPU (`ValueError`)

GPU无法处理某些数据类型，例如字符串数组。在批量转换数据时，必须进行筛选。

**❌ 错误示例**:
```python
train_data = {
    'features': np.array([1, 2, 3]),
    'param_keys': ['p1', 'p2', 'p3']  # 这是一个字符串列表
}

# 这行代码会失败，因为它试图将 'param_keys' 列表转换为CuPy数组
train_data_gpu = {k: service.to_backend(v) for k, v in train_data.items()}
```

**✅ 正确做法**: 只转换 `numpy.ndarray` 类型的数值数据。
```python
train_data_gpu = {
    k: service.to_backend(v) if isinstance(v, np.ndarray) else v
    for k, v in train_data.items()
}
```

**最佳实践**: 数据转换逻辑应具备识别数值数组的能力，避免对非数值类型（如字符串列表、字典、配置对象等）进行转换。

### 技巧 1: 善用 `to_backend()` 的幂等性

`to_backend()` 方法是幂等的，这意味着对一个已经在目标设备上的数组再次调用它，不会产生任何开销。

```python
# 假设 service 是 CupyService
gpu_array = service.to_backend(np_array)

# 再次调用是安全的，且几乎没有性能损耗
same_gpu_array = service.to_backend(gpu_array)

assert same_gpu_array is gpu_array # 返回的是同一个对象
```
这使得编写通用代码变得容易，无需在每次转换前都检查 `is_on_gpu()`。

### 技巧 2: 最小化CPU/GPU数据传输

CPU和GPU之间的数据传输是整个流程中最昂贵的操作之一。应遵循以下模式以获得最佳性能：

1.  **一次性上传**: 在计算开始前，将所有需要的数据批量传输到GPU。
2.  **在GPU上完成所有计算**: 尽可能多地在GPU上执行连续的计算步骤，避免中间结果返回CPU。
3.  **一次性下载**: 只将最终的、需要用于存储、可视化或与CPU库交互的结果传回CPU。

### 技巧 3: 保持API参数名的一致性

在整个项目中，我们约定所有接收 `BackendService` 实例的参数都命名为 `backend_service` 或 `service`。在重构和开发新功能时，保持这种一致性可以避免因参数名不匹配而导致的 `TypeError`，正如我们在之前的测试中遇到的那样。

---

通过遵循以上指南和最佳实践，您可以更高效、更安全地利用 `BackendService` 框架进行开发，并充分发挥GPU加速的潜力。
