from __future__ import annotations

import pandas as pd
import pytest

from logwp.models.constants import WpDataType, WpDepthRole, WpCurveDimension
from logwp.models.curve.metadata import CurveMetadata
from logwp.models.datasets.bundle import WpDataFrameBundle


class TestDataFrameInference:
    """
    测试从DataFrame推断CurveMetadata的功能。
    """

    def test_from_dataframe_simple_continuous(self):
        """
        测试: 从一个简单的连续型DataFrame推断元数据。
        场景: 井名、单深度、两个数据列。
        预期:
        - 正确识别井名和深度曲线。
        - is_interval应为False。
        - 曲线顺序应为：WELL, DEPT, GR, RHOB。
        """
        df = pd.DataFrame({
            "WELL": ["W1"] * 3,
            "DEPT": [100, 101, 102],
            "GR": [45.0, 46.0, 47.0],
            "RHOB": [2.5, 2.5, 2.6],
        })

        metadata, is_interval = CurveMetadata.from_dataframe(df)

        assert not is_interval
        assert len(metadata.curves) == 4
        assert list(metadata.curves.keys()) == ["WELL", "DEPT", "GR", "RHOB"]

        well_curve = metadata.get_curve("WELL")
        assert well_curve.is_well_identifier
        assert well_curve.data_type == WpDataType.STR

        depth_curve = metadata.get_curve("DEPT")
        assert depth_curve.depth_role == WpDepthRole.SINGLE
        assert depth_curve.data_type == WpDataType.FLOAT

        gr_curve = metadata.get_curve("GR")
        assert not gr_curve.is_system_curve()
        assert gr_curve.data_type == WpDataType.FLOAT

    def test_from_dataframe_interval_data(self):
        """
        测试: 从一个区间型DataFrame推断元数据。
        场景: 井名、顶深、底深、一个数据列。
        预期:
        - 正确识别井名、顶深和底深。
        - is_interval应为True。
        - 曲线顺序应为：WellName, MD_TOP, MD_BOTTOM, FORMATION。
        """
        df = pd.DataFrame({
            "WellName": ["W1"] * 2,
            "MD_TOP": [2500.0, 2505.0],
            "MD_BOTTOM": [2505.0, 2510.0],
            "FORMATION": ["FM_A", "FM_B"],
        })

        metadata, is_interval = CurveMetadata.from_dataframe(df)

        assert is_interval
        assert len(metadata.curves) == 4
        assert list(metadata.curves.keys()) == ["WellName", "MD_TOP", "MD_BOTTOM", "FORMATION"]

        top_curve = metadata.get_curve("MD_TOP")
        assert top_curve.depth_role == WpDepthRole.TOP

        bottom_curve = metadata.get_curve("MD_BOTTOM")
        assert bottom_curve.depth_role == WpDepthRole.BOTTOM

    def test_from_dataframe_with_2d_composite_curve(self):
        """
        测试: 从包含二维组合曲线的DataFrame推断元数据。
        场景: 井名、深度、一维曲线、二维组合曲线。
        预期:
        - 正确识别所有曲线类型。
        - T2_VALUE_1, T2_VALUE_2, T2_VALUE_3被组合成一个名为T2_VALUE的二维曲线。
        - 曲线顺序正确。
        """
        df = pd.DataFrame({
            "WELL": ["W1"] * 3,
            "DEPTH": [100, 101, 102],
            "GR": [45.0, 46.0, 47.0],
            "T2_VALUE_1": [0.1, 0.2, 0.3],
            "T2_VALUE_2": [1.1, 1.2, 1.3],
            "T2_VALUE_3": [10.1, 10.2, 10.3],
        })

        metadata, is_interval = CurveMetadata.from_dataframe(df)

        assert not is_interval
        assert len(metadata.curves) == 4  # WELL, DEPTH, GR, T2_VALUE
        assert "T2_VALUE" in metadata.curves
        assert "T2_VALUE_1" not in metadata.curves  # 元素列不应作为独立曲线存在

        t2_curve = metadata.get_curve("T2_VALUE")
        assert t2_curve.is_2d_composite_curve()
        assert t2_curve.dimension == WpCurveDimension.TWO_D_COMP
        assert len(t2_curve.element_names) == 3


class TestBundleFromDataFrame:
    """
    测试从DataFrame创建WpDataFrameBundle的集成功能。
    """

    def test_bundle_from_dataframe_integration(self):
        """
        测试: WpDataFrameBundle.from_dataframe 的端到端集成。
        场景: 混合一维和二维曲线的DataFrame。
        预期:
        - 正确创建WpDataFrameBundle实例。
        - metadata 和 curve_to_columns_map 被正确设置。
        - is_interval_bundle 属性正确。
        """
        df = pd.DataFrame({
            "WELL": ["W1"] * 3,
            "DEPTH": [100, 101, 102],
            "GR": [45.0, 46.0, 47.0],
            "T2_VALUE_1": [0.1, 0.2, 0.3],
            "T2_VALUE_2": [1.1, 1.2, 1.3],
        })

        bundle = WpDataFrameBundle.from_dataframe(name="test_bundle", df=df)

        # 验证Bundle基本属性
        assert isinstance(bundle, WpDataFrameBundle)
        assert bundle.name == "test_bundle"
        assert not bundle.is_interval_bundle

        # 验证元数据
        assert bundle.curve_metadata is not None
        assert bundle.curve_metadata.has_curve("GR")
        assert bundle.curve_metadata.has_curve("T2_VALUE")
        assert len(bundle.curve_metadata.curves) == 4

        # 验证曲线到列的映射
        expected_map = {
            "WELL": ["WELL"],
            "DEPTH": ["DEPTH"],
            "GR": ["GR"],
            "T2_VALUE": ["T2_VALUE_1", "T2_VALUE_2"],
        }
        assert bundle.curve_to_columns_map == expected_map
