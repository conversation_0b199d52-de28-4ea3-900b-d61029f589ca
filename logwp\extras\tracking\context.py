"""logwp.extras.tracking.context - 运行上下文管理器

实现RunContext类，管理单次实验运行的完整生命周期。

Architecture
------------
层次/依赖: logwp.extras.tracking包核心层
设计原则: 上下文管理器模式、原子性操作、状态一致性
性能特征: 内存中构建、原子性写入、延迟I/O

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- EH-1: Exception Groups支持
- LG-1: structlog结构化日志
- PF-2: 异步I/O支持（大文件处理）

References
----------
- 《logwp_extras_tracking设计.md》§3.1 - RunContext详细设计
- 《SCAPE_CCG_编码与通用规范》- 编码规范要求
"""

from __future__ import annotations

import yaml
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union

from .config import TrackingConfig
from .exceptions import (
    ArtifactIOError,
    ArtifactNotFoundError,
    ManifestError,
    RunExistsError,
    RunNotFoundError,
)
from .utils import (
    atomic_write_json,
    calculate_file_hash,
    copy_artifact_file,
    ensure_directory,
    generate_run_id,
    get_current_utc_timestamp,
    get_file_size,
    load_json_file,
)

# 延迟导入logger避免循环依赖
def _get_logger():
    try:
        from logwp.infra import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)


class RunContext:
    """管理一次实验运行的完整生命周期。

    通过上下文管理器 (`with`语句) 使用，确保运行的开始和结束
    都有相应的处理，并自动保存追踪清单。

    Architecture
    ------------
    层次/依赖: 实验追踪核心组件，管理运行生命周期
    设计原则: 上下文管理器、内存构建器、原子性持久化
    性能特征: 延迟I/O、批量写入、内存友好

    核心设计思想是作为 `manifest.json` 文件的一个**构建器（Builder）**。
    它在内存中维护一个与 `manifest.json` 结构完全对应的字典，并在运行期间
    不断填充这个字典。当运行结束时，这个内存中的字典被一次性序列化到磁盘。

    Attributes:
        run_dir (Path): 运行的根目录，所有文件操作的基准
        run_id (str): 运行的唯一标识符
        manifest (Dict[str, Any]): 内存中的清单数据，核心状态
        config (TrackingConfig): 追踪系统配置
        _is_finalized (bool): 是否已完成状态标志
        _config_data (Optional[Dict[str, Any]]): 原始配置数据

    Examples:
        >>> # 基本使用
        >>> with RunContext(run_dir="output/run-001") as ctx:
        ...     ctx.log_parameter("learning_rate", 0.01)
        ...     ctx.log_metrics({"rmse": 0.85})
        ...     ctx.log_artifact("model.pkl", "models/final_model.pkl")
        >>>
        >>> # 带配置的使用
        >>> config_data = {"project": "SCAPE", "algorithm": "SWIFT-PSO"}
        >>> with RunContext(
        ...     run_dir="output/run-002",
        ...     config=config_data,
        ...     metadata={"git_commit": "abc123"}
        ... ) as ctx:
        ...     # 实验逻辑
        ...     pass
    """

    def __init__(
        self,
        run_dir: Union[str, Path],
        run_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        *,
        overwrite: bool = False,
        tracking_config: Optional[TrackingConfig] = None
    ) -> None:
        """初始化一个运行上下文。

        如果`run_dir`指向一个已存在的运行目录，并且`overwrite`设置为`False`
        (默认值)，将抛出`RunExistsError`以防止意外覆盖。
        要强制覆盖现有运行，请设置 `overwrite=True`。

        Args:
            run_dir: 本次运行的根目录
            run_id: 运行的唯一ID。如果为None，将自动生成
            config: 本次运行的配置字典，将被快照保存
            metadata: 额外的元数据，如git commit hash
            overwrite: 如果为True，即使目录已存在，也会强制创建新运行
            tracking_config: 追踪系统配置，如果为None则使用默认配置

        Raises:
            RunExistsError: 运行目录已存在且overwrite=False时抛出
            RunNotFoundError: 尝试加载不存在的运行时抛出
            ManifestError: 清单文件格式错误时抛出
        """
        self.run_dir = Path(run_dir)
        self.config = tracking_config or TrackingConfig()
        self._is_finalized = False
        self._config_data = config
        self._logger = _get_logger()

        # 检查运行目录状态
        manifest_path = self.run_dir / self.config.manifest_filename
        run_exists = manifest_path.exists()

        if run_exists and not overwrite:
            # 运行已存在且不允许覆盖，抛出异常
            try:
                # 尝试加载清单以获取现有运行ID
                existing_manifest = load_json_file(manifest_path)
                existing_run_id = existing_manifest.get("run_id")
            except Exception:
                existing_run_id = None

            raise RunExistsError(
                f"Run directory '{self.run_dir}' already exists and overwrite is False.",
                run_dir=str(self.run_dir),
                existing_run_id=existing_run_id,
            )
        else:
            # 创建新运行或覆盖
            if run_exists and overwrite:
                self._logger.warning(
                    "Overwriting existing run",
                    run_dir=str(self.run_dir),
                    operation="overwrite_run"
                )

            self.run_id = run_id or generate_run_id()
            self._initialize_new_run(metadata)

        self._logger.info(
            "RunContext initialized",
            run_id=self.run_id,
            run_dir=str(self.run_dir),
            operation="init_context"
        )

    def _initialize_new_run(self, metadata: Optional[Dict[str, Any]]) -> None:
        """初始化新运行的清单结构。"""
        self.manifest = {
            "run_id": self.run_id,
            "start_time_utc": get_current_utc_timestamp(),
            "end_time_utc": None,
            "duration_seconds": None,
            "status": "RUNNING",
            "lineage": {
                "inputs": {},
                "code_version": {}
            },
            "config_snapshot_path": None,
            "parameters": {},
            "metrics": {},
            "artifacts": {}
        }

        # 添加元数据
        if metadata:
            self.manifest["lineage"].update(metadata)

        # 保存配置快照
        if self._config_data:
            self._save_config_snapshot()

    def _save_config_snapshot(self) -> None:
        """保存配置快照到文件。"""
        if not self._config_data:
            return

        ensure_directory(self.run_dir)
        config_path = self.run_dir / self.config.config_snapshot_filename

        try:
            with open(config_path, "w", encoding="utf-8") as f:
                yaml.dump(
                    self._config_data,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2
                )

            self.manifest["config_snapshot_path"] = self.config.config_snapshot_filename

            self._logger.debug(
                "Config snapshot saved",
                config_path=str(config_path),
                operation="save_config"
            )

        except Exception as e:
            self._logger.warning(
                "Failed to save config snapshot",
                error=str(e),
                config_path=str(config_path),
                operation="save_config"
            )

    def __enter__(self) -> RunContext:
        """进入上下文管理器。"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """退出上下文管理器，自动完成运行。"""
        if exc_type is not None:
            # 发生异常，标记为失败
            self.failure(f"Run failed with {exc_type.__name__}: {exc_val}")
        else:
            # 正常完成，标记为成功
            if self.manifest["status"] == "RUNNING":
                self.success()

        # 无论如何都要完成清单写入
        self.finalize()

    def log_parameter(
        self,
        key: str,
        value: Any,
        *,
        step_name: Optional[str] = None
    ) -> None:
        """记录单个超参数。

        Args:
            key: 参数名称
            value: 参数值，必须是JSON可序列化的
            step_name: (可选) 步骤名称，用于分组参数

        Examples:
            >>> # 记录全局参数
            >>> ctx.log_parameter("project_version", "1.2.0")
            >>>
            >>> # 记录特定步骤的参数
            >>> ctx.log_parameter("learning_rate", 0.01, step_name="training")
        """
        if self._is_finalized:
            raise RuntimeError("Cannot log parameters after run is finalized")

        if step_name:
            step_params = self.manifest["parameters"].setdefault(step_name, {})
            step_params[key] = value
        else:
            self.manifest["parameters"][key] = value

        self._logger.debug(
            "Parameter logged",
            parameter_key=key,
            parameter_value=value,
            step_name=step_name,
            run_id=self.run_id,
            operation="log_parameter"
        )

    def log_metrics(
        self,
        metrics_dict: Dict[str, float],
        step_name: Optional[str] = None
    ) -> None:
        """记录一个或多个指标。

        支持按步骤记录（用于追踪训练过程中的loss变化）。

        Args:
            metrics_dict: 指标字典，键为指标名，值为指标值
            step_name: 可选的步骤名称，用于分组指标

        Examples:
            >>> # 记录最终指标
            >>> ctx.log_metrics({"rmse": 0.85, "r2": 0.92})
            >>>
            >>> # 记录分步骤指标
            >>> ctx.log_metrics({"loss": 0.123}, step_name="swift_pso_training")
            >>> ctx.log_metrics({"spearman_rho": 0.85}, step_name="plt_validation")
        """
        if self._is_finalized:
            raise RuntimeError("Cannot log metrics after run is finalized")

        if step_name is None:
            # 记录到摘要部分
            summary_metrics = self.manifest["metrics"].setdefault("summary", {})
            summary_metrics.update(metrics_dict)
        else:
            # 记录到指定步骤
            step_metrics = self.manifest["metrics"].setdefault(step_name, {})
            step_metrics.update(metrics_dict)

        self._logger.debug(
            "Metrics logged",
            metrics=metrics_dict,
            step_name=step_name,
            run_id=self.run_id,
            operation="log_metrics"
        )

    def log_artifact(
        self,
        local_path: Union[str, Path],
        artifact_name: str,
        artifact_path: Optional[str] = None,
        *,
        description: Optional[str] = None,
    ) -> None:
        """记录一个产物文件。

        将本地文件复制到运行目录中，并在清单中注册该产物。

        Args:
            local_path: 本地文件系统中的文件路径
            artifact_name: 产物的逻辑名称，用于在清单中唯一标识
            artifact_path: 文件在运行目录内部的相对路径。
            description: (可选) 对产物的详细描述
                         如果为None，则使用artifact_name作为路径

        Raises:
            ArtifactIOError: 文件操作失败时抛出

        Examples:
            >>> # 使用默认路径
            >>> ctx.log_artifact("temp/model.pkl", "my_model")  # 保存为 my_model
            >>>
            >>> # 指定目标路径
            >>> ctx.log_artifact("temp/model.pkl", "best_model", "models/final_model.pkl")
        """
        if self._is_finalized:
            raise RuntimeError("Cannot log artifacts after run is finalized")

        local_path = Path(local_path)
        if artifact_path is None:
            artifact_path = local_path.name

        # 目标路径
        target_path = self.run_dir / artifact_path

        # 复制文件
        copy_artifact_file(local_path, target_path)

        # 计算文件元数据
        metadata = {}
        if self.config.enable_file_hashing:
            try:
                hash_value = calculate_file_hash(
                    target_path, algorithm=self.config.hash_algorithm
                )
                metadata["hash"] = hash_value
                metadata["hash_algorithm"] = self.config.hash_algorithm
            except Exception as e:
                self._logger.warning(
                    "Failed to calculate file hash",
                    error=str(e),
                    artifact_path=artifact_path,
                    operation="log_artifact"
                )

        try:
            metadata["size_bytes"] = get_file_size(target_path)
        except Exception as e:
            self._logger.warning(
                "Failed to get file size",
                error=str(e),
                artifact_path=artifact_path,
                operation="log_artifact"
            )

        # 注册到清单
        artifact_entry = {
            "path": artifact_path,
            "type": self._infer_artifact_type(local_path),
            "metadata": metadata
        }

        if description:
            artifact_entry["description"] = description

        self.manifest["artifacts"][artifact_name] = artifact_entry

        self._logger.info(
            "Artifact logged",
            artifact_name=artifact_name,
            artifact_path=artifact_path,
            source_path=str(local_path),
            description=description,
            run_id=self.run_id,
            operation="log_artifact"
        )

    def register_artifact(
        self,
        artifact_path: Union[str, Path],
        artifact_name: str,
        *,
        description: Optional[str] = None,
        ) -> None:
        """注册运行目录中已存在的产物文件。

        Args:
            artifact_path: 文件在运行目录内的相对路径
            artifact_name: 产物的逻辑名称
            description: (可选) 对产物的详细描述

        Raises:
            ArtifactIOError: 获取文件信息失败时抛出
        """
        if self._is_finalized:
            raise RuntimeError("Cannot register artifacts after run is finalized")

        artifact_path = Path(artifact_path)
        target_path = self.run_dir / artifact_path

        # 计算文件元数据
        metadata = {}
        if self.config.enable_file_hashing:
            try:
                hash_value = calculate_file_hash(
                    target_path, algorithm=self.config.hash_algorithm
                )
                metadata["hash"] = hash_value
                metadata["hash_algorithm"] = self.config.hash_algorithm
            except Exception as e:
                self._logger.warning(
                    "Failed to calculate file hash",
                    error=str(e),
                    artifact_path=str(artifact_path),
                    operation="register_artifact"
                )

        try:
            metadata["size_bytes"] = get_file_size(target_path)
        except Exception as e:
            self._logger.warning(
                "Failed to get file size",
                error=str(e),
                artifact_path=str(artifact_path),
                operation="register_artifact"
            )

        # 注册到清单
        artifact_entry = {
            "path": str(artifact_path).replace("\\", "/"),  # 统一使用正斜杠
            "type": self._infer_artifact_type(target_path),
            "metadata": metadata
        }

        if description:
            artifact_entry["description"] = description

        self.manifest["artifacts"][artifact_name] = artifact_entry

        self._logger.info(
            "Artifact registered",
            artifact_name=artifact_name,
            artifact_path=str(artifact_path),
            description=description,
            run_id=self.run_id,
            operation="register_artifact"
        )


    def _infer_artifact_type(self, file_path: Path) -> str:
        """推断产物文件类型。"""
        suffix = file_path.suffix.lower()
        type_mapping = {
            ".pkl": "model",
            ".joblib": "model",
            ".json": "data",
            ".csv": "data",
            ".yaml": "config",
            ".yml": "config",
            ".png": "image",
            ".jpg": "image",
            ".jpeg": "image",
            ".pdf": "document",
            ".txt": "text",
            ".log": "log"
        }
        return type_mapping.get(suffix, "unknown")

    def get_artifact_path(self, artifact_name: str) -> Path:
        """获取已注册产物的绝对路径。

        Args:
            artifact_name: 产物名称

        Returns:
            Path: 产物的绝对路径

        Raises:
            ArtifactNotFoundError: 产物不存在时抛出

        Examples:
            >>> model_path = ctx.get_artifact_path("model.pkl")
            >>> print(model_path)  # /path/to/output/run-001/models/model.pkl
        """
        if artifact_name not in self.manifest["artifacts"]:
            raise ArtifactNotFoundError(
                f"Artifact '{artifact_name}' not found in run",
                artifact_name=artifact_name,
                run_id=self.run_id,
                registered_artifacts=list(self.manifest["artifacts"].keys())
            )

        artifact_info = self.manifest["artifacts"][artifact_name]
        relative_path = artifact_info["path"]
        return self.run_dir / relative_path

    def get_step_dir(self, step_name: str) -> Path:
        """为流程中的一个特定"步骤"获取一个专属的子目录。

        Args:
            step_name: 步骤名称

        Returns:
            Path: 步骤目录的路径

        Examples:
            >>> pso_dir = ctx.get_step_dir("swift_pso_training")
            >>> print(pso_dir)  # /path/to/output/run-001/swift_pso_training
        """
        step_dir = self.run_dir / step_name
        ensure_directory(step_dir)
        return step_dir

    def success(self) -> None:
        """标记运行为成功状态。

        Examples:
            >>> ctx.success()  # 手动标记成功
        """
        if self._is_finalized:
            return

        self.manifest["status"] = "COMPLETED"
        self._logger.info(
            "Run marked as successful",
            run_id=self.run_id,
            operation="mark_success"
        )

    def failure(self, error_message: str) -> None:
        """标记运行为失败状态。

        Args:
            error_message: 失败原因描述

        Examples:
            >>> ctx.failure("Model training failed due to data issues")
        """
        if self._is_finalized:
            return

        self.manifest["status"] = "FAILED"
        self.manifest["error_message"] = error_message
        self._logger.error(
            "Run marked as failed",
            run_id=self.run_id,
            error_message=error_message,
            operation="mark_failure"
        )

    def finalize(self) -> None:
        """将内存中的完整清单原子性地写入磁盘。

        这个方法会：
        1. 更新运行结束时间和持续时间
        2. 原子性地写入manifest.json
        3. 设置完成标志

        Examples:
            >>> ctx.finalize()  # 通常由__exit__自动调用
        """
        if self._is_finalized:
            return

        # 更新时间信息
        end_time = get_current_utc_timestamp()
        self.manifest["end_time_utc"] = end_time

        # 计算持续时间
        try:
            from datetime import datetime
            start_time = datetime.fromisoformat(
                self.manifest["start_time_utc"].replace("Z", "+00:00")
            )
            end_time_dt = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
            duration = (end_time_dt - start_time).total_seconds()
            self.manifest["duration_seconds"] = round(duration, 3)
        except Exception as e:
            self._logger.warning(
                "Failed to calculate duration",
                error=str(e),
                operation="finalize"
            )

        # 写入清单文件
        manifest_path = self.run_dir / self.config.manifest_filename
        try:
            atomic_write_json(self.manifest, manifest_path)
            self._is_finalized = True

            self._logger.info(
                "Run finalized",
                run_id=self.run_id,
                manifest_path=str(manifest_path),
                status=self.manifest["status"],
                duration_seconds=self.manifest.get("duration_seconds"),
                operation="finalize"
            )

        except Exception as e:
            self._logger.error(
                "Failed to finalize run",
                run_id=self.run_id,
                error=str(e),
                operation="finalize"
            )
            raise

    @staticmethod
    def generate_timestamped_run_name(prefix: str) -> str:
        """根据前缀和当前时间戳生成一个唯一的运行目录名称。

        Args:
            prefix: 目录名称的前缀。

        Returns:
            str: 格式为 "prefix_YYYYMMDD_HHMMSS" 的唯一名称。

        Examples:
            >>> # 生成一个用于SWIFT-PSO实验的运行目录名
            >>> run_name = RunContext.generate_timestamped_run_name("swift_pso_experiment")
            >>> # run_name -> 'swift_pso_experiment_20250720_103000'
        """
        run_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{prefix}_{run_timestamp}"

    @classmethod
    def load(cls, run_dir: Union[str, Path]) -> RunContext:
        """从已完成的运行目录中加载RunContext。

        Args:
            run_dir: 运行目录路径

        Returns:
            RunContext: 加载的运行上下文（只读模式）

        Raises:
            RunNotFoundError: 运行不存在时抛出
            ManifestError: 清单文件格式错误时抛出

        Examples:
            >>> # 加载已完成的运行
            >>> ctx = RunContext.load("output/run-001")
            >>> print(ctx.run_id)
            >>> print(ctx.manifest["status"])
        """
        run_dir = Path(run_dir)
        config = TrackingConfig()
        manifest_path = run_dir / config.manifest_filename

        if not manifest_path.exists():
            raise RunNotFoundError(
                f"Run manifest not found: {manifest_path}",
                run_dir=str(run_dir),
                missing_component="manifest"
            )

        # 创建实例并加载数据
        instance = cls.__new__(cls)
        instance.run_dir = run_dir
        instance.config = config
        instance._is_finalized = True
        instance._config_data = None
        instance._logger = _get_logger()

        # 加载清单
        instance.manifest = load_json_file(manifest_path)
        instance.run_id = instance.manifest.get("run_id", "unknown")

        instance._logger.info(
            "Run loaded from disk",
            run_id=instance.run_id,
            run_dir=str(run_dir),
            status=instance.manifest.get("status"),
            operation="load_run"
        )

        return instance

    @property
    def is_completed(self) -> bool:
        """检查运行是否已完成。"""
        return self.manifest["status"] in ("COMPLETED", "FAILED")

    @property
    def is_successful(self) -> bool:
        """检查运行是否成功完成。"""
        return self.manifest["status"] == "COMPLETED"

    @property
    def artifacts(self) -> Dict[str, Dict[str, Any]]:
        """获取所有已注册的产物信息。"""
        return self.manifest["artifacts"].copy()

    @property
    def parameters(self) -> Dict[str, Any]:
        """获取所有已记录的参数。"""
        return self.manifest["parameters"].copy()

    @property
    def metrics(self) -> Dict[str, Any]:
        """获取所有已记录的指标。"""
        return self.manifest["metrics"].copy()
