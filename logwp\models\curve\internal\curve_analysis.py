from __future__ import annotations

"""logwp.models.curve.service.curve_analysis - 曲线列表分析服务

实现曲线列表的智能分析功能，包括类型识别、冲突检测和结构分析。
遵循SAD文档的内部服务层设计模式（Utility/Helper Pattern）。

Architecture
------------
层次/依赖: curve模块服务层，依赖curve.metadata、constants、exceptions
设计原则: 无状态服务、职责分离、类型安全
性能特征: 高效算法、内存优化、批量处理

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
- 《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata服务层重构
"""

from typing import TYPE_CHECKING

from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.curve.metadata import (
        CurveListAnalysis, CurveItemAnalysis, CurveConflict,
        CurveItemType, CurveListType, CurveBasicAttributes
    )

logger = get_logger(__name__)


def analyze_curve_list(
    curve_names: list[str],
    curves_dict: dict[str, 'CurveBasicAttributes'],
    is_2d_element_name_func,
    find_parent_2d_curve_func
) -> 'CurveListAnalysis':
    """分析混合紧凑曲线列表。

    分析用户提供的曲线列表，识别每个曲线项的类型，判断列表的整体类型，
    检测二维组合曲线的表示冲突，并提供详细的分析结果。

    Args:
        curve_names: 混合紧凑曲线列表
            - 支持一维曲线：["GR", "DEN"]
            - 支持二维组合曲线基础名称：["T2_VALUE"]
            - 支持二维组合曲线元素：["T2_VALUE[2]"]
            - 支持混合格式：["GR", "T2_VALUE", "T2_TIME[10]"]
        curves_dict: 曲线字典，用于查找曲线信息
        is_2d_element_name_func: 判断是否为二维组合曲线元素名称的函数
        find_parent_2d_curve_func: 查找父二维组合曲线的函数

    Returns:
        CurveListAnalysis: 完整的分析结果

    Examples:
        >>> # 纯一维曲线
        >>> result = analyze_curve_list(["GR", "DEN", "PHIT"], curves_dict, ...)
        >>> assert result.list_type == CurveListType.PURE_1D

        >>> # 混合格式
        >>> result = analyze_curve_list(["GR", "T2_VALUE", "T2_TIME[10]"], curves_dict, ...)
        >>> assert result.list_type == CurveListType.MIXED

        >>> # 检测冲突
        >>> result = analyze_curve_list(["GR", "T2_VALUE", "T2_VALUE[2]"], curves_dict, ...)
        >>> assert result.has_conflicts is True
        >>> assert len(result.conflicts) == 1

    References:
        《SCAPE_DDS_详细设计_logwp.md》§4.2 - CurveMetadata曲线列表分析
    """
    # 导入需要的类型（避免循环导入）
    from logwp.models.curve.metadata import (
        CurveListAnalysis, CurveItemType, CurveListType
    )

    curve_items = []
    not_found_curves = []

    # 第一步：分析每个曲线项
    for curve_name in curve_names:
        item_analysis = _analyze_single_curve_item(
            curve_name, curves_dict, is_2d_element_name_func, find_parent_2d_curve_func
        )
        curve_items.append(item_analysis)

        if item_analysis.item_type == CurveItemType.NOT_FOUND:
            not_found_curves.append(curve_name)

    # 第二步：判断列表整体类型
    list_type = _determine_list_type(curve_items)

    # 第三步：检测二维组合曲线表示冲突
    conflicts = _detect_2d_conflicts(curve_items)

    # 构建分析结果
    analysis = CurveListAnalysis(
        curve_items=curve_items,
        list_type=list_type,
        has_conflicts=len(conflicts) > 0,
        conflicts=conflicts,
        not_found_curves=not_found_curves
    )

    logger.debug(
        "曲线列表分析完成",
        operation="analyze_curve_list",
        input_curve_count=len(curve_names),
        list_type=list_type.value,
        has_conflicts=analysis.has_conflicts,
        conflict_count=len(conflicts),
        not_found_count=len(not_found_curves),
        input_curves=curve_names
    )

    return analysis


def _analyze_single_curve_item(
    curve_name: str,
    curves_dict: dict[str, 'CurveBasicAttributes'],
    is_2d_element_name_func,
    find_parent_2d_curve_func
) -> 'CurveItemAnalysis':
    """分析单个曲线项。

    Args:
        curve_name: 曲线名称
        curves_dict: 曲线字典
        is_2d_element_name_func: 判断是否为二维组合曲线元素名称的函数
        find_parent_2d_curve_func: 查找父二维组合曲线的函数

    Returns:
        CurveItemAnalysis: 单个曲线项的分析结果
    """
    from logwp.models.curve.metadata import CurveItemAnalysis, CurveItemType

    # 检查是否为二维组合曲线元素
    is_element, parent_name = is_2d_element_name_func(curve_name)

    if is_element and parent_name:
        # 二维组合曲线元素
        parent_curve = find_parent_2d_curve_func(curve_name)
        if parent_curve:
            # 提取元素索引
            element_index = _extract_element_index(curve_name)
            return CurveItemAnalysis(
                curve_name=curve_name,
                item_type=CurveItemType.TWO_DIMENSIONAL_ELEMENT,
                parent_curve=parent_name,
                element_index=element_index
            )
        else:
            # 父曲线不存在
            return CurveItemAnalysis(
                curve_name=curve_name,
                item_type=CurveItemType.NOT_FOUND
            )
    else:
        # 检查是否为已知曲线
        if curve_name in curves_dict:
            curve_attrs = curves_dict[curve_name]
            if curve_attrs.is_2d_composite_curve():
                # 二维组合曲线基础名称
                return CurveItemAnalysis(
                    curve_name=curve_name,
                    item_type=CurveItemType.TWO_DIMENSIONAL
                )
            else:
                # 一维曲线
                return CurveItemAnalysis(
                    curve_name=curve_name,
                    item_type=CurveItemType.ONE_DIMENSIONAL
                )
        else:
            # 曲线不存在
            return CurveItemAnalysis(
                curve_name=curve_name,
                item_type=CurveItemType.NOT_FOUND
            )


def _extract_element_index(element_name: str) -> int | None:
    """从二维组合曲线元素名称中提取索引。

    Args:
        element_name: 元素名称，如 "T2_VALUE[1]"

    Returns:
        int | None: 元素索引，如果解析失败则返回None
    """
    try:
        if '[' in element_name and element_name.endswith(']'):
            bracket_pos = element_name.rfind('[')
            index_str = element_name[bracket_pos+1:-1]
            return int(index_str)
    except (ValueError, IndexError):
        pass
    return None


def _determine_list_type(curve_items: list['CurveItemAnalysis']) -> 'CurveListType':
    """判断曲线列表的整体类型。

    Args:
        curve_items: 曲线项分析结果列表

    Returns:
        CurveListType: 列表整体类型
    """
    from logwp.models.curve.metadata import CurveItemType, CurveListType

    # 统计各种类型的数量（排除NOT_FOUND）
    valid_items = [item for item in curve_items if item.item_type != CurveItemType.NOT_FOUND]

    if not valid_items:
        # 如果没有有效曲线，默认为PURE_1D
        return CurveListType.PURE_1D

    has_1d = any(item.item_type == CurveItemType.ONE_DIMENSIONAL for item in valid_items)
    has_2d = any(item.item_type == CurveItemType.TWO_DIMENSIONAL for item in valid_items)
    has_2d_elements = any(item.item_type == CurveItemType.TWO_DIMENSIONAL_ELEMENT for item in valid_items)

    # 判断列表类型
    if has_1d and has_2d and has_2d_elements:
        return CurveListType.MIXED
    elif has_1d and has_2d_elements:
        return CurveListType.MIXED
    elif has_2d and has_2d_elements:
        return CurveListType.MIXED
    elif has_2d_elements:
        return CurveListType.WITH_2D_ELEMENTS
    elif has_2d:
        return CurveListType.PURE_COMPACT
    else:
        return CurveListType.PURE_1D


def _detect_2d_conflicts(curve_items: list['CurveItemAnalysis']) -> list['CurveConflict']:
    """检测二维组合曲线表示方法冲突。

    Args:
        curve_items: 曲线项分析结果列表

    Returns:
        list[CurveConflict]: 冲突列表
    """
    from logwp.models.curve.metadata import CurveConflict, CurveItemType

    conflicts = []

    # 按父曲线分组
    base_curves = {}  # base_name -> compact_form
    element_curves = {}  # base_name -> [element_forms]

    for item in curve_items:
        if item.item_type == CurveItemType.TWO_DIMENSIONAL:
            # 二维组合曲线基础名称
            base_curves[item.curve_name] = item.curve_name
        elif item.item_type == CurveItemType.TWO_DIMENSIONAL_ELEMENT and item.parent_curve:
            # 二维组合曲线元素
            if item.parent_curve not in element_curves:
                element_curves[item.parent_curve] = []
            element_curves[item.parent_curve].append(item.curve_name)

    # 检测冲突：同时存在基础名称和元素形式
    for base_name in base_curves:
        if base_name in element_curves:
            conflicts.append(CurveConflict(
                base_curve_name=base_name,
                compact_form=base_curves[base_name],
                element_forms=element_curves[base_name]
            ))

    return conflicts
