from __future__ import annotations

"""logwp.models.utils - 模型层工具函数

提供数据模型层专用的工具函数，包括字符串规范化、标识符处理等。

Architecture
------------
层次/依赖: models层工具模块，依赖constants、types
设计原则: 纯函数设计、类型安全、CIIA架构支持
性能特征: 高性能字符串处理、Unicode标准化

Core Features
-------------
- **字符串规范化**: Unicode NFKC + Case Folding标准化
- **标识符处理**: 大小写不敏感标识符管理
- **容器类**: 大小写不敏感字典和集合
- **验证工具**: 数据模型验证辅助函数

Package Structure
-----------------
- string_normalizer: 字符串规范化工具
- case_insensitive: 大小写不敏感容器
- validators: 模型验证工具

Examples
--------
>>> from logwp.models.utils import WpStringNormalizer, CaseInsensitiveDict
>>>
>>> # 字符串规范化
>>> normalizer = WpStringNormalizer()
>>> normalized = normalizer.normalize("Well_Name")
>>>
>>> # 大小写不敏感字典
>>> ci_dict = CaseInsensitiveDict({"Well_A": "data1"})
>>> value = ci_dict["well_a"]  # 可以找到

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§4.3 - CIIA架构设计
- 《SCAPE_CCG_编码与通用规范.md》§3 - 字符串处理规范
"""

__all__ = [
    # 字符串规范化
    "WpStringNormalizer",

    # 大小写不敏感容器
    "CaseInsensitiveDict",
    "CaseInsensitiveSet",
]


def __getattr__(name: str) -> object:
    """延迟导入模型工具函数。

    Architecture
    ------------
    层次/依赖: models层工具门面
    设计原则: 延迟导入、性能优化
    性能特征: 按需加载、类型安全
    """
    # 字符串规范化工具
    if name == "WpStringNormalizer":
        from .string_normalizer import WpStringNormalizer
        return WpStringNormalizer

    # 大小写不敏感容器
    elif name in ("CaseInsensitiveDict", "CaseInsensitiveSet"):
        from .case_insensitive_collections import CaseInsensitiveDict, CaseInsensitiveSet
        return locals()[name]


    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
