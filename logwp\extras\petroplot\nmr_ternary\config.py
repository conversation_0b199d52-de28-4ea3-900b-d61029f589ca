"""logwp.extras.petroplot.nmr_ternary.config - NMR三元图配置模型

本模块定义了NMR三元图组件所需的所有Pydantic配置模型，严格遵循
“数据选择”与“绘图逻辑”分离的设计原则。

- NmrTernaryDataSelectors: 用户层API，定义逻辑曲线名。
- NmrTernaryColumnSelectors: 内部API，定义物理列名。
- NmrTernaryPlotConfig: 纯粹的逻辑与表现层配置。

Architecture
------------
层次/依赖: petroplot/nmr_ternary配置层，被facade层使用
设计原则: 关注点分离、类型安全、用户友好
"""
from typing import List, Optional, Dict, Any, Tuple, Union, Literal
from pydantic import BaseModel, Field, conlist, model_validator
from logwp.extras.petroplot.common import (
    ContinuousColorConfig,
    CategoricalColorConfig,
    SymbolConfig,
    LegendConfig,
    DataMarkerStyle,
    NullMarkerStyle,
    ColorBarConfig
)
from .constants import NMR_TERNARY_REGION_DEFINITIONS, NMR_TERNARY_REGION_COLOR_SCHEMES


class NmrTernaryDataSelectors(BaseModel):
    """【用户API】定义绘图所需的数据源（逻辑曲线名）。"""
    macro_curve: str = Field(..., description="宏孔隙度组分的逻辑曲线名。")
    micro_curve: str = Field(..., description="微孔隙度组分的逻辑曲线名。")
    meso_curve: str = Field(..., description="中孔隙度组分的逻辑曲线名。")
    color_curve: Optional[str] = Field(None, description="用于颜色映射的逻辑曲线名 (如渗透率)。")
    symbol_curve: Optional[str] = Field(None, description="用于标记符号映射的类别曲线名 (如岩性)。")
    split_by_curve: Optional[str] = Field(None, description="用于拆分数据集并生成多个子图的逻辑曲线名。")
    hover_extra_curves: Optional[List[str]] = Field(None, description="在悬停提示框中额外显示的逻辑曲线名列表。")


class NmrTernaryColumnSelectors(BaseModel):
    """【内部API】定义绘图所需的数据列（物理列名）。由步骤门面自动解析生成。"""
    macro_col: str
    micro_col: str
    meso_col: str
    color_col: Optional[str] = None
    symbol_col: Optional[str] = None
    well_col: Optional[str] = None
    depth_col: Optional[str] = None
    split_col: Optional[str] = None
    hover_extra_cols: Optional[List[str]] = None


# --- Nested Configuration Models for better organization ---

class AxisStyleConfig(BaseModel):
    """定义三元图单个坐标轴的逻辑和内容。"""
    label: str = Field(..., description="顶点的显示标签文本。")
    color: str = Field(..., description="该轴和顶点的基础颜色（用于标题、刻度、网格线）。")
    dtick: int = Field(10, description="坐标轴刻度和网格线的间隔。")
    bold_title: bool = Field(True, description="顶点标题是否加粗显示。")

class BackgroundRegionConfig(BaseModel):
    """背景分区配置。"""
    enable: bool = Field(True, description="是否绘制背景分区。")
    legend_title: str = Field("岩石物理分区", description="图例中背景分区组的标题。")
    opacity: float = Field(0.5, description="背景分区透明度。", ge=0, le=1)
    regions: Optional[Dict[str, List[Tuple[float, float, float]]]] = Field(
        default_factory=lambda: NMR_TERNARY_REGION_DEFINITIONS,
        description="要绘制的背景分区定义。默认为标准分区。用户可提供自定义字典或通过设为None来禁用特定分区。"
    )
    region_colors: Optional[Dict[str, str]] = Field(
        default_factory=lambda: NMR_TERNARY_REGION_COLOR_SCHEMES['CNLC'],
        description="分区的颜色映射。默认为'CNLC'方案。用户可提供自定义字典来覆盖颜色。"
    )

class NmrTernaryPlotConfig(BaseModel):
    """核磁孔隙度三元图的逻辑/表现层配置。"""
    # --- 1. Display Labels & Axis Logic ---
    title: str = Field("NMR Porosity Partitioning Ternary Plot", description="图表主标题。")
    macro_axis: AxisStyleConfig = Field(
        default_factory=lambda: AxisStyleConfig(label="Macro", color="red", dtick=10, bold_title=True),
        description="Macro轴（顶端）的标签和颜色配置。"
    )
    micro_axis: AxisStyleConfig = Field(
        default_factory=lambda: AxisStyleConfig(label="Micro", color="blue", dtick=10, bold_title=True),
        description="Micro轴（左下角）的标签和颜色配置。"
    )
    meso_axis: AxisStyleConfig = Field(
        default_factory=lambda: AxisStyleConfig(label="Meso", color="green", dtick=10, bold_title=True),
        description="Meso轴（右下角）的标签和颜色配置。"
    )

    # --- 2. Plotting Logic & Customization ---
    # a. Data Pre-processing Logic
    normalize_data: bool = Field(True, description="是否在绘图前自动将三个组分归一化（使其和为100）。如果为False，将直接使用原始值。")
    normalization_total: float = Field(100.0, description="归一化时期望的总和值 (通常是 100 或 1.0)。")

    # b. Color & Symbol Mapping Logic
    color_mapping: Optional[Union[ContinuousColorConfig, CategoricalColorConfig]] = Field(
        default_factory=ContinuousColorConfig,
        description="颜色映射配置。可以是连续数值型或分类名义型。仅当 'color_col' 被提供时生效。",
        discriminator="mapping_type"
    )
    symbol_mapping: Optional[SymbolConfig] = Field(
        default_factory=SymbolConfig,
        description="标记符号映射配置。仅当 'symbol_col' 被提供时生效。"
    )
    # c. Handling of Nulls in Color Column
    distinguish_null_color: bool = Field(True, description="是否区分并独立显示颜色列中的空值点。如果为False，将直接丢弃空值行。")
    data_marker_style: DataMarkerStyle = Field(default_factory=DataMarkerStyle, description="主数据点的图例配置。")
    null_marker_style: NullMarkerStyle = Field(default_factory=NullMarkerStyle, description="当区分空值时，空值点的样式配置。")
    # d. Background Regions
    background_regions: BackgroundRegionConfig = Field(default_factory=BackgroundRegionConfig, description="背景分区的配置。")
    # e. General Display Control
    show_title: bool = Field(True, description="是否显示图表主标题。")
    show_well_name: bool = Field(True, description="是否在主标题下方显示井名副标题。仅当well_col被提供且数据中包含井名时生效。")
    legend: LegendConfig = Field(default_factory=LegendConfig, description="图例的显示配置。")
    plot_bgcolor: str = Field("white", description="三元图区域的背景颜色。")
    margin: Optional[Dict[str, int]] = Field(None, description="图表的边距(l, r, b, t)。如果为None，则由Plotly自动确定。")
    color_bar: Optional[ColorBarConfig] = Field(default_factory=ColorBarConfig, description="（可选）配置全局共享的颜色条布局。")
    legend_position: Literal['right', 'bottom'] = Field(
        'right',
        description="图例和颜色条的整体布局策略。'right'为垂直排列在右侧，'bottom'为标准图例在下方水平排列。"
    )
    # f. Multi-plot Control
    split_subtitle_template: str = Field("{split_col}: {value}", description="当使用split_col时，用于生成子图副标题的模板。支持 {split_col} 和 {value} 占位符。")
    max_split_plots: int = Field(100, description="当使用 split_col 时，允许生成的最大子图数量。超过此数量将引发异常。", gt=0)
    # --- 3. Artifact Control ---
    output_formats: List[str] = Field(default_factory=lambda: ["html", "png"], description="输出图表格式。")

    @model_validator(mode='after')
    def _auto_configure_layouts(self) -> 'NmrTernaryPlotConfig':
        """根据 legend_position 自动配置 legend 和 color_bar 的坐标。"""
        if self.legend_position == 'right':
            # 应用“右侧”布局：标准图例在右上角，颜色条在其下方（如果同时存在）
            if self.legend:
                self.legend.x = 1.02
                self.legend.y = 1.0
                self.legend.xanchor = 'left'
                self.legend.yanchor = 'top'
                self.legend.orientation = 'v'
            if self.color_bar:
                self.color_bar.x = 0.92
                self.color_bar.y = 1.0
                self.color_bar.xanchor = 'left'
                self.color_bar.yanchor = 'top'
                self.color_bar.len = 0.55
                self.color_bar.thickness = 10

        elif self.legend_position == 'bottom':
            # 应用“下方”布局
            if self.legend:
                self.legend.x = 0.5
                self.legend.y = -0.2  # 移动到图表下方
                self.legend.xanchor = 'center'
                self.legend.yanchor = 'top'
                self.legend.orientation = 'h' # 水平排列
            if self.color_bar:
                # 当图例在下方时，颜色条可以占据完整的右侧空间
                self.color_bar.x = 1.02
                self.color_bar.y = 0.5
                self.color_bar.xanchor = 'left'
                self.color_bar.yanchor = 'middle'
                self.color_bar.len = 0.9 # 可以更长
                self.color_bar.thickness = 15

        return self
