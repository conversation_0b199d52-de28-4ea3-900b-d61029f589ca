"""测试固件，用于 validation 包的测试。

定义了所有 validation 测试共享的 pytest 固件，
包括 RunContext 和预加载的 WpDataFrameBundle 数据。
"""

from __future__ import annotations

from pathlib import Path

import pytest
from logwp.extras.tracking import RunContext
from logwp.io import WpExcelReader
from logwp.models.datasets.bundle import WpDataFrameBundle

TEST_DATA_ROOT = Path(__file__).parent.parent.parent.parent / "test_data"


@pytest.fixture(scope="function")
def run_context(tmp_path: Path) -> RunContext:
    """提供一个临时的、隔离的RunContext实例，用于测试。"""
    run_dir = tmp_path / "validation_test_run"
    run_dir.mkdir()
    with RunContext(run_dir=run_dir) as ctx:
        yield ctx


@pytest.fixture(scope="session")
def validation_pred_bundle() -> WpDataFrameBundle:
    """从测试数据文件加载包含模型预测渗透率的数据。"""
    data_file_path = TEST_DATA_ROOT / "real" / "swift_pso" / "swift_pso_pred_apply.wp.xlsx"
    reader = WpExcelReader()
    project = reader.read(data_file_path)
    return project.get_dataset("apply_predictions").extract_curve_dataframe_bundle(
        curve_names=["K_PRED_SWIFT"], include_system_columns=True
    )


@pytest.fixture(scope="session")
def validation_plt_bundle() -> WpDataFrameBundle:
    """从测试数据文件加载包含PLT解释结果的数据。"""
    data_file_path = TEST_DATA_ROOT / "real" / "swift_pso" / "swift_pso_val_plt.wp.xlsx"
    reader = WpExcelReader()
    project = reader.read(data_file_path)
    return project.get_dataset("PLT").extract_curve_dataframe_bundle(
        curve_names=["QOZI"], include_system_columns=True
    )


@pytest.fixture(scope="session")
def validation_core_bundle() -> WpDataFrameBundle:
    """从测试数据文件加载包含岩心渗透率的数据。"""
    data_file_path = TEST_DATA_ROOT / "real" / "swift_pso" / "swift_pso_val_cored_perm.wp.xlsx"
    reader = WpExcelReader()
    project = reader.read(data_file_path)
    return project.get_dataset("K_Val").extract_curve_dataframe_bundle(
        curve_names=["PERM"], include_system_columns=True
    )
