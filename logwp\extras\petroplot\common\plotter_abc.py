"""logwp.extras.petroplot.common.plotter_abc - 绘图器抽象基类

本模块定义了所有 `petroplot` 绘图器必须遵循的、与具体绘图库无关的
抽象基类（Abstract Base Classes）。

这是整个 `petroplot` 绘图框架的顶层设计，它只定义“做什么”，而不关心
“如何做”。具体的实现（如使用Plotly或Matplotlib）由子类完成。

Architecture
------------
层次/依赖: petroplot/common抽象层，被所有petroplot绘图器实现所继承
设计原则: 接口隔离、依赖倒置、里氏替换原则
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

import pandas as pd
from logwp.extras.plotting import PlotProfile


class PetroPlotter(ABC):
    """所有 `petroplot` 绘图器的顶层抽象基类。"""

    def __init__(
        self,
        config: Any,
        selectors: Any,
        plot_profile: PlotProfile,
        data_dict: Dict[str, pd.DataFrame],
        subtitle: Optional[str] = None,
    ):
        self.config = config
        self.selectors = selectors
        self.plot_profile = plot_profile
        self.data_dict = data_dict
        self.subtitle = subtitle
        # 注意：具体的Figure对象（如 go.Figure）将在子类中创建
        self.fig: Any = None

    def plot(self) -> Any:
        """
        执行完整的绘图流程并返回一个绘图库的Figure对象。
        这是模板方法，定义了绘图的骨架。
        """
        self._setup_figure()
        self._draw_main_plot()
        self._add_data_traces()
        self._apply_layout()
        return self.fig

    @abstractmethod
    def _setup_figure(self) -> None:
        """【抽象方法】初始化具体的Figure对象。"""
        pass

    @abstractmethod
    def _draw_main_plot(self) -> None:
        """【抽象方法】绘制图表的主要背景和非数据元素。"""
        pass

    @abstractmethod
    def _add_data_traces(self) -> None:
        """【抽象方法】添加所有数据轨迹。"""
        pass

    @abstractmethod
    def _apply_layout(self) -> None:
        """【抽象方法】应用图表的整体布局和样式。"""
        pass


class CartesianPlotter(PetroPlotter):
    """笛卡尔坐标系绘图器的抽象基类。"""

    @abstractmethod
    def _setup_cartesian_axes(self) -> None:
        """【抽象方法】设置笛卡尔坐标轴（xaxis, yaxis）。"""
        pass


class TernaryPlotter(PetroPlotter):
    """三元图绘图器的抽象基类。"""

    @abstractmethod
    def _setup_ternary_axes(self) -> None:
        """【抽象方法】设置三元图坐标轴（aaxis, baxis, caxis）。"""
        pass
