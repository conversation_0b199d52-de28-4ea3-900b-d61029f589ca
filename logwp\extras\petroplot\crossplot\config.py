"""logwp.extras.petroplot.crossplot.config - 交会图配置模型

本模块定义了交会图组件所需的所有Pydantic配置模型，严格遵循
“数据选择”与“绘图逻辑”分离的设计原则。支持在同一图表上绘制多个数据系列。

- SeriesConfig: 【用户API】定义单个数据系列的逻辑曲线名和样式。
- CrossPlotConfig: 【用户API】定义整个图表的共享配置和系列列表。
- SeriesColumnSelectors, CrossPlotColumnSelectors: 【内部API】包含已解析的物理列名。

Architecture
------------
层次/依赖: petroplot/crossplot配置层，被facade层使用
设计原则: 关注点分离、类型安全、用户友好
"""
from typing import List, Optional, Dict, Any, Union, Literal
from pydantic import BaseModel, Field
from logwp.extras.petroplot.common import (
    ContinuousColorConfig,
    CategoricalColorConfig,
    SymbolConfig,
    LegendConfig,
    DataMarkerStyle,
    NullMarkerStyle,
    LineStyle,
    DiagonalLineConfig,
    MarginalPlotConfig,
    AxisConfig,
    CartesianPlotConfig,
)

class SeriesConfig(BaseModel):
    """【用户API】定义单个数据系列的配置。"""
    name: str = Field(..., description="数据系列的名称，用于图例显示。")
    bundle_name: str = Field(..., description="此系列数据源的逻辑名称，对应于传入步骤的数据源字典的键。")
    plot_type: Literal["scatter", "line"] = Field("scatter", description="数据系列的绘图类型。")

    # --- Data Selectors (Logical Names) for this series ---
    x_curve: str = Field(..., description="此系列X轴的逻辑曲线名。")
    y_curve: str = Field(..., description="此系列Y轴的逻辑曲线名。")
    color_curve: Optional[str] = Field(None, description="此系列用于颜色映射的逻辑曲线名。")
    symbol_curve: Optional[str] = Field(None, description="此系列用于标记符号映射的类别曲线名（仅对scatter有效）。")
    split_by_curve: Optional[str] = Field(None, description="用于拆分此系列数据的逻辑曲线名。")
    hover_extra_curves: Optional[List[str]] = Field(None, description="在悬停提示框中为此系列额外显示的逻辑曲线名列表。")

    # --- Per-series styling and logic overrides ---
    color_mapping: Optional[Union[ContinuousColorConfig, CategoricalColorConfig]] = Field(
        None, description="此系列的颜色映射配置。如果为None，将使用全局配置或默认值。", discriminator="mapping_type"
    )
    symbol_mapping: Optional[SymbolConfig] = Field(None, description="此系列的标记符号映射配置。")
    marker_style: Optional[DataMarkerStyle] = Field(None, description="散点图的标记点样式。")
    line_style: Optional[LineStyle] = Field(None, description="折线图的线条样式。")

    # --- Per-series null handling ---
    distinguish_null_color: bool = Field(True, description="是否区分并独立显示此系列颜色列中的空值点。")
    null_marker_style: NullMarkerStyle = Field(default_factory=NullMarkerStyle, description="当区分空值时，此系列空值点的样式配置。")
    include_in_marginals: bool = Field(True, description="如果显示边缘分布图，是否将此系列的数据包含在内。")


class SeriesColumnSelectors(BaseModel):
    """【内部API】包含单个系列已解析的物理列名和样式。"""
    name: str
    bundle_name: str
    plot_type: Literal["scatter", "line"]
    x_col: str
    y_col: str
    well_col: str
    depth_col: str
    color_col: Optional[str] = None
    symbol_col: Optional[str] = None
    split_col: Optional[str] = None
    hover_extra_cols: Optional[List[str]] = None
    color_mapping: Optional[Union[ContinuousColorConfig, CategoricalColorConfig]] = None
    symbol_mapping: Optional[SymbolConfig] = None
    marker_style: Optional[DataMarkerStyle] = None
    line_style: Optional[LineStyle] = None
    distinguish_null_color: bool
    null_marker_style: NullMarkerStyle
    include_in_marginals: bool

class CrossPlotColumnSelectors(BaseModel):
    """【内部API】包含所有已解析的系列选择器。"""
    series: List[SeriesColumnSelectors]

class CrossPlotConfig(CartesianPlotConfig):
    """多系列二维交会图的逻辑/表现层配置。"""
    # 继承自 CartesianPlotConfig，获得了 title, x_axis, y_axis, plot_bgcolor, diagonal_line,
    # marginal_x, marginal_y, legend, color_bar, legend_position 等所有通用配置。

    # --- 2. 数据系列定义 ---
    series: List[SeriesConfig] = Field(..., description="要在图表上绘制的数据系列列表。")

    # --- 3. 全局绘图逻辑与定制 ---
    show_title: bool = Field(True, description="是否显示图表主标题。")
    show_well_name: bool = Field(True, description="是否在主标题下方显示井名副标题。")
    margin: Optional[Dict[str, int]] = Field(None, description="图表的边距(l, r, b, t)。")
    split_subtitle_template: str = Field("{split_col}: {value}", description="当使用split_col时，用于生成子图副标题的模板。")
    max_split_plots: int = Field(100, description="允许生成的最大子图数量。", gt=0)

    # --- 4. 产物控制 ---
    output_formats: List[str] = Field(default_factory=lambda: ["html", "png"])
