"""曲线构建器 - 测试专用

提供流畅的API来构建测试用的曲线元数据和曲线数据。

Examples
--------
>>> # 构建曲线元数据
>>> metadata = CurveBuilder.metadata() \
...     .add_well_curve("WELL") \
...     .add_depth_curve("MD") \
...     .add_logging_curve("GR", unit="API") \
...     .add_logging_curve("PHIT", unit="v/v") \
...     .build()
>>>
>>> # 构建曲线数据
>>> curve_data = CurveBuilder.data() \
...     .with_constant("GR", 50.0) \
...     .with_linear("PHIT", start=0.10, end=0.20) \
...     .with_function("SW", lambda d: 0.3 + 0.1 * np.sin(d/100)) \
...     .build(depths)
"""

from __future__ import annotations

import numpy as np
from typing import Any, Callable, TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.models.curve import CurveMetadata, CurveBasicAttributes


class CurveMetadataBuilder:
    """曲线元数据构建器。"""
    
    def __init__(self):
        """初始化元数据构建器。"""
        self._curves: list[dict[str, Any]] = []
    
    def add_well_curve(self, name: str = "WELL", description: str | None = None) -> 'CurveMetadataBuilder':
        """添加井名曲线。
        
        Args:
            name: 曲线名称
            description: 描述信息
            
        Returns:
            CurveMetadataBuilder: 构建器实例
        """
        self._curves.append({
            "type": "well_identifier",
            "name": name,
            "description": description
        })
        return self
    
    def add_depth_curve(self, name: str = "MD", unit: str = "m", description: str | None = None) -> 'CurveMetadataBuilder':
        """添加深度曲线。
        
        Args:
            name: 曲线名称
            unit: 单位
            description: 描述信息
            
        Returns:
            CurveMetadataBuilder: 构建器实例
        """
        self._curves.append({
            "type": "depth_reference",
            "name": name,
            "unit": unit,
            "description": description
        })
        return self
    
    def add_interval_depth_curves(
        self, 
        top_name: str = "MD_Top", 
        bottom_name: str = "MD_Bottom",
        unit: str = "m"
    ) -> 'CurveMetadataBuilder':
        """添加区间深度曲线对。
        
        Args:
            top_name: 顶深曲线名称
            bottom_name: 底深曲线名称
            unit: 单位
            
        Returns:
            CurveMetadataBuilder: 构建器实例
        """
        self._curves.append({
            "type": "interval_depth",
            "top_name": top_name,
            "bottom_name": bottom_name,
            "unit": unit
        })
        return self
    
    def add_logging_curve(
        self, 
        name: str, 
        unit: str = "", 
        description: str | None = None
    ) -> 'CurveMetadataBuilder':
        """添加测井曲线。
        
        Args:
            name: 曲线名称
            unit: 单位
            description: 描述信息
            
        Returns:
            CurveMetadataBuilder: 构建器实例
        """
        self._curves.append({
            "type": "logging",
            "name": name,
            "unit": unit,
            "description": description
        })
        return self
    
    def add_categorical_curve(
        self, 
        name: str, 
        description: str | None = None
    ) -> 'CurveMetadataBuilder':
        """添加类别型曲线。
        
        Args:
            name: 曲线名称
            description: 描述信息
            
        Returns:
            CurveMetadataBuilder: 构建器实例
        """
        self._curves.append({
            "type": "categorical",
            "name": name,
            "description": description
        })
        return self
    
    def build(self) -> 'CurveMetadata':
        """构建曲线元数据对象。
        
        Returns:
            CurveMetadata: 构建的元数据对象
            
        Note:
            需要完整的logwp环境才能工作。
        """
        try:
            from logwp.testing.utils import quick_metadata
            
            # 提取曲线名称
            curve_names = []
            for curve_def in self._curves:
                if curve_def["type"] == "interval_depth":
                    curve_names.extend([curve_def["top_name"], curve_def["bottom_name"]])
                else:
                    curve_names.append(curve_def["name"])
            
            return quick_metadata(*curve_names)
            
        except ImportError:
            raise RuntimeError(
                "CurveMetadataBuilder.build() requires full logwp environment."
            )
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式。
        
        Returns:
            dict: 构建器状态字典
        """
        return {
            "curves": self._curves.copy()
        }


class CurveDataBuilder:
    """曲线数据构建器。"""
    
    def __init__(self):
        """初始化数据构建器。"""
        self._curve_generators: dict[str, Any] = {}
    
    def with_constant(self, name: str, value: float) -> 'CurveDataBuilder':
        """添加常数曲线。
        
        Args:
            name: 曲线名称
            value: 常数值
            
        Returns:
            CurveDataBuilder: 构建器实例
        """
        self._curve_generators[name] = value
        return self
    
    def with_linear(self, name: str, start: float, end: float) -> 'CurveDataBuilder':
        """添加线性变化曲线。
        
        Args:
            name: 曲线名称
            start: 起始值
            end: 结束值
            
        Returns:
            CurveDataBuilder: 构建器实例
        """
        def linear_func(depths):
            if len(depths) == 0:
                return []
            min_depth, max_depth = min(depths), max(depths)
            if min_depth == max_depth:
                return [start] * len(depths)
            return [start + (end - start) * (d - min_depth) / (max_depth - min_depth) for d in depths]
        
        self._curve_generators[name] = linear_func
        return self
    
    def with_function(self, name: str, func: Callable[[float], float]) -> 'CurveDataBuilder':
        """添加函数生成的曲线。
        
        Args:
            name: 曲线名称
            func: 生成函数，接受深度返回值
            
        Returns:
            CurveDataBuilder: 构建器实例
        """
        self._curve_generators[name] = func
        return self
    
    def with_pattern(self, name: str, pattern: list[Any]) -> 'CurveDataBuilder':
        """添加重复模式曲线。
        
        Args:
            name: 曲线名称
            pattern: 重复模式列表
            
        Returns:
            CurveDataBuilder: 构建器实例
        """
        self._curve_generators[name] = pattern
        return self
    
    def build(self, depths: list[float]) -> dict[str, list[Any]]:
        """构建曲线数据。
        
        Args:
            depths: 深度列表
            
        Returns:
            dict: 曲线数据字典
        """
        result = {}
        
        for curve_name, generator in self._curve_generators.items():
            if callable(generator):
                if hasattr(generator, '__code__') and generator.__code__.co_argcount == 1:
                    # 单参数函数：逐点计算
                    result[curve_name] = [generator(d) for d in depths]
                else:
                    # 多参数函数：传入整个深度列表
                    result[curve_name] = generator(depths)
            elif isinstance(generator, (list, tuple)):
                # 重复模式
                n_points = len(depths)
                if len(generator) >= n_points:
                    result[curve_name] = list(generator[:n_points])
                else:
                    repeats = (n_points // len(generator)) + 1
                    extended = (list(generator) * repeats)[:n_points]
                    result[curve_name] = extended
            else:
                # 常数值
                result[curve_name] = [generator] * len(depths)
        
        return result
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式。
        
        Returns:
            dict: 构建器状态字典
        """
        return {
            "curve_generators": {
                name: str(gen) if callable(gen) else gen 
                for name, gen in self._curve_generators.items()
            }
        }


class CurveBuilder:
    """曲线构建器主类。"""
    
    @staticmethod
    def metadata() -> CurveMetadataBuilder:
        """创建曲线元数据构建器。
        
        Returns:
            CurveMetadataBuilder: 元数据构建器实例
        """
        return CurveMetadataBuilder()
    
    @staticmethod
    def data() -> CurveDataBuilder:
        """创建曲线数据构建器。
        
        Returns:
            CurveDataBuilder: 数据构建器实例
        """
        return CurveDataBuilder()
