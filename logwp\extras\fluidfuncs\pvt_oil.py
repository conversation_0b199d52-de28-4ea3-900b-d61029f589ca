"""
原油PVT性质关联式模型
"""
import numpy as np

from logwp.extras.units import Quantity, ureg
from logwp.extras.petrofuncs import validate_dimensions


@validate_dimensions(temp='temperature')
def standing_pb(
    rs: float,
    gamma_g: float,
    api_gravity: float,
    temp: Quantity
) -> Quantity:
    """
    使用 Standing 关联式计算饱和压力 (Bubble Point Pressure, Pb)。

    这是一个广泛使用的经验公式，用于估算原油的饱和压力。

    参数:
        rs (float): 溶解气油比 (Solution Gas-Oil Ratio), 单位为 scf/STB。
        gamma_g (float): 天然气相对密度 (Gas Specific Gravity)，无量纲。
        api_gravity (float): 原油的API重度，无量纲。
        temp (Quantity): 油藏温度，必须是带有 `temperature` 量纲的 Quantity 对象。

    返回:
        Quantity: 计算出的饱和压力，单位为 psi。
    """
    # Standing 关联式是基于特定单位系统的，因此我们需要将输入的温度转换为华氏度。
    # 我们的 units 包使得这个转换非常安全和简单。
    temp_f = temp.to('degF').value

    # 计算中间变量 y_g
    y_g = 0.00091 * temp_f - 0.0125 * api_gravity

    # 计算饱和压力 Pb，单位为 psig
    pb_value = 18.2 * ( (rs / gamma_g)**0.83 * (10**y_g) - 1.4 )

    # 确保压力不为负
    pb_value = max(pb_value, 0.0)

    # 将计算结果封装成一个带有单位的 Quantity 对象返回
    return ureg.Quantity(pb_value, 'psi')
