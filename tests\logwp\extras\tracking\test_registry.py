"""测试ModelRegistry类的功能。

测试模型注册表的核心功能，包括：
- 模型注册和版本管理
- 阶段转换和生命周期管理
- 查询和摘要功能
- 错误处理和数据完整性
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict, List

import pytest

from logwp.extras.tracking import (
    ModelRegistry,
    ModelNotRegisteredError,
    RegistryError,
)


class TestModelRegistryBasic:
    """测试ModelRegistry的基本功能。"""

    def test_registry_creation_new_file(self, registry_path: Path):
        """测试创建新的注册表文件。"""
        registry = ModelRegistry(registry_path)

        assert registry_path.exists()
        assert registry.list_registered_models() == []

    def test_registry_creation_existing_file(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试加载已存在的注册表文件。"""
        # 先创建一个注册表文件
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        # 加载注册表
        registry = ModelRegistry(registry_path)
        models = registry.list_registered_models()

        assert "test-model" in models
        assert len(models) == 1

    def test_registry_invalid_file(self, registry_path: Path):
        """测试加载无效的注册表文件。"""
        # 创建无效的JSON文件
        registry_path.write_text("invalid json content")

        with pytest.raises(RegistryError):
            ModelRegistry(registry_path)


class TestModelRegistration:
    """测试模型注册功能。"""

    def test_register_first_model(self, registry_path: Path):
        """测试注册第一个模型。"""
        registry = ModelRegistry(registry_path)

        model_info = registry.register_model(
            model_name="test-model",
            run_id="run-001",
            artifact_path="models/model.pkl",
            description="Test model",
            metrics={"rmse": 0.85, "r2": 0.92},
            tags=["test", "baseline"]
        )

        assert model_info["name"] == "test-model"
        assert model_info["version"] == "1.0.0"
        assert model_info["stage"] == "Staging"  # 默认阶段
        assert model_info["source_run"]["run_id"] == "run-001"
        assert model_info["metrics"]["rmse"] == 0.85

    def test_register_multiple_versions(self, registry_path: Path):
        """测试注册同一模型的多个版本。"""
        registry = ModelRegistry(registry_path)

        # 注册第一个版本
        v1 = registry.register_model(
            model_name="test-model",
            run_id="run-001",
            artifact_path="models/v1.pkl"
        )

        # 注册第二个版本
        v2 = registry.register_model(
            model_name="test-model",
            run_id="run-002",
            artifact_path="models/v2.pkl"
        )

        assert v1["version"] == "1.0.0"
        assert v2["version"] == "2.0.0"

        # 验证两个版本都存在
        models = registry.list_registered_models()
        assert len(models) == 1
        assert models[0] == "test-model"

    def test_register_with_custom_version(self, registry_path: Path):
        """测试使用自定义版本号注册。"""
        registry = ModelRegistry(registry_path)

        model_info = registry.register_model(
            model_name="test-model",
            run_id="run-001",
            artifact_path="models/model.pkl",
            version="1.5.0-beta"
        )

        assert model_info["version"] == "1.5.0-beta"

    def test_register_with_invalid_stage(self, registry_path: Path):
        """测试使用无效阶段注册。"""
        registry = ModelRegistry(registry_path)

        with pytest.raises(ValueError) as exc_info:
            registry.register_model(
                model_name="test-model",
                run_id="run-001",
                artifact_path="models/model.pkl",
                stage="InvalidStage"
            )

        assert "Invalid stage" in str(exc_info.value)

    @pytest.mark.parametrize(
        "existing_versions, expected_next_version",
        [
            ([], "1.0.0"),  # 无现有版本
            (["1.0.0", "2.0.0"], "3.0.0"),  # 简单递增
            (["1.5.0", "2.1.0-beta", "2.0.0"], "3.0.0"),  # 复杂版本与预发布标签
            (["10.2.3", "5.0.0"], "11.0.0"),  # 较大主版本号
            (["1.0", "invalid-version", "2.0.0"], "3.0.0"),  # 忽略无效和不完整版本
            (["1.0.0-alpha", "1.0.0-beta", "1.0.0"], "2.0.0"),  # 各种预发布标签
            (["v1", "3.2.1"], "4.0.0"), # 处理 'v' 前缀和其他格式
        ],
    )
    def test_automatic_version_generation(
        self,
        registry_path: Path,
        existing_versions: list[str],
        expected_next_version: str,
    ):
        """测试自动版本号生成逻辑。"""
        registry = ModelRegistry(registry_path)
        model_name = "version-gen-model"

        # 预填充注册表现有版本
        if existing_versions:
            registry._registry_data[model_name] = [
                {"version": v, "name": model_name, "registered_at_utc": "2023-01-01T12:00:00Z"} for v in existing_versions
            ]
            registry._save_registry()

        # 注册新模型以触发自动版本生成
        model_info = registry.register_model(
            model_name=model_name, run_id="run-new", artifact_path="models/new.pkl"
        )

        assert model_info["version"] == expected_next_version

    def test_version_generation_with_complex_versions(self, registry_path: Path):
        """测试复杂版本号的生成逻辑。"""
        registry = ModelRegistry(registry_path)
        model_name = "complex-version-model"

        # 预填充一些复杂的版本号
        complex_versions = [
            "1.0.0",
            "1.5.0-beta",
            "2.0.0-alpha.1",
            "2.1.0",
            "3.0.0-rc.1",
            "invalid-version",  # 这个应该被忽略
        ]

        registry._registry_data[model_name] = [
            {"version": v, "name": model_name, "registered_at_utc": "2023-01-01T12:00:00Z"}
            for v in complex_versions
        ]
        registry._save_registry()

        # 注册新模型，应该生成 4.0.0（基于最大版本 3.0.0-rc.1）
        model_info = registry.register_model(
            model_name=model_name,
            run_id="run-new",
            artifact_path="models/new.pkl"
        )

        assert model_info["version"] == "4.0.0"

    def test_version_generation_ignores_invalid_versions(self, registry_path: Path):
        """测试版本生成逻辑忽略无效版本号。"""
        registry = ModelRegistry(registry_path)
        model_name = "invalid-version-model"

        # 只有无效版本号
        invalid_versions = ["not-a-version", "invalid", ""]

        registry._registry_data[model_name] = [
            {"version": v, "name": model_name, "registered_at_utc": "2023-01-01T12:00:00Z"}
            for v in invalid_versions
        ]
        registry._save_registry()

        # 应该回退到默认版本 1.0.0
        model_info = registry.register_model(
            model_name=model_name,
            run_id="run-new",
            artifact_path="models/new.pkl"
        )

        assert model_info["version"] == "1.0.0"


class TestModelQuerying:
    """测试模型查询功能。"""

    def test_get_model_version(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试获取特定模型版本。"""
        # 设置注册表数据
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        # 获取存在的版本
        version_info = registry.get_model_version("test-model", "1.0.0")
        assert version_info is not None
        assert version_info["version"] == "1.0.0"
        assert version_info["stage"] == "Staging"

        # 获取不存在的版本
        version_info = registry.get_model_version("test-model", "3.0.0")
        assert version_info is None

        # 获取不存在的模型
        version_info = registry.get_model_version("nonexistent", "1.0.0")
        assert version_info is None

    def test_get_latest_versions_all(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试获取所有最新版本。"""
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        latest_versions = registry.get_latest_versions("test-model")

        assert len(latest_versions) == 2
        # 应该按注册时间降序排列
        assert latest_versions[0]["version"] == "2.0.0"  # 更新的版本
        assert latest_versions[1]["version"] == "1.0.0"

    def test_get_latest_versions_by_stage(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试按阶段获取最新版本。"""
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        # 获取生产阶段版本
        prod_versions = registry.get_latest_versions("test-model", stages=["Production"])
        assert len(prod_versions) == 1
        assert prod_versions[0]["version"] == "2.0.0"

        # 获取暂存阶段版本
        staging_versions = registry.get_latest_versions("test-model", stages=["Staging"])
        assert len(staging_versions) == 1
        assert staging_versions[0]["version"] == "1.0.0"

        # 获取不存在的阶段
        archived_versions = registry.get_latest_versions("test-model", stages=["Archived"])
        assert len(archived_versions) == 0

    def test_list_registered_models(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试列出已注册的模型。"""
        registry_data = {
            "model-a": sample_model_versions,
            "model-b": sample_model_versions[:1]
        }
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)
        models = registry.list_registered_models()

        assert len(models) == 2
        assert "model-a" in models
        assert "model-b" in models


class TestModelStageTransition:
    """测试模型阶段转换功能。"""

    def test_transition_stage_success(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试成功的阶段转换。"""
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        # 将Staging版本提升到Production
        registry.transition_stage("test-model", "1.0.0", "Production")

        # 验证阶段已更改
        version_info = registry.get_model_version("test-model", "1.0.0")
        assert version_info["stage"] == "Production"

    def test_transition_stage_invalid_stage(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试转换到无效阶段。"""
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        with pytest.raises(ValueError):
            registry.transition_stage("test-model", "1.0.0", "InvalidStage")

    def test_transition_stage_model_not_found(self, registry_path: Path):
        """测试转换不存在模型的阶段。"""
        registry = ModelRegistry(registry_path)

        with pytest.raises(ModelNotRegisteredError) as exc_info:
            registry.transition_stage("nonexistent", "1.0.0", "Production")

        assert exc_info.value.model_name == "nonexistent"
        assert exc_info.value.available_models == [] # 确保为空，因为模型不存在
        assert exc_info.value.available_versions == [] # 确保为空

    def test_transition_stage_version_not_found(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试转换不存在版本的阶段。"""
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        with pytest.raises(ModelNotRegisteredError) as exc_info:
            registry.transition_stage("test-model", "3.0.0", "Production")

        assert exc_info.value.version == "3.0.0"  # 仍然是请求的版本
        assert exc_info.value.available_versions == ["1.0.0", "2.0.0"]

    def test_model_not_registered_error_attributes(self, registry_path: Path):
        """测试 ModelNotRegisteredError 异常属性的正确使用。"""
        registry = ModelRegistry(registry_path)

        # 先注册一个模型
        registry.register_model(
            model_name="existing-model",
            run_id="run-001",
            artifact_path="models/v1.pkl"
        )

        # 测试请求不存在的模型（使用 transition_stage）
        with pytest.raises(ModelNotRegisteredError) as exc_info:
            registry.transition_stage("nonexistent-model", "1.0.0", "Production")

        error = exc_info.value
        assert error.model_name == "nonexistent-model"
        assert len(error.available_models) == 1  # 应该有一个可用模型
        assert "existing-model" in error.available_models
        assert error.available_versions == []  # 模型不存在时版本列表为空

        # 测试请求存在模型的不存在版本（使用 transition_stage）
        with pytest.raises(ModelNotRegisteredError) as exc_info:
            registry.transition_stage("existing-model", "2.0.0", "Production")

        error = exc_info.value
        assert error.model_name == "existing-model"
        assert error.version == "2.0.0"
        assert len(error.available_versions) == 1  # 应该有一个可用版本
        assert "1.0.0" in error.available_versions


class TestModelSummary:
    """测试模型摘要功能。"""

    def test_get_model_summary(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试获取模型摘要。"""
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)
        summary = registry.get_model_summary("test-model")

        assert summary is not None
        assert summary["model_name"] == "test-model"
        assert summary["total_versions"] == 2
        assert summary["stage_counts"]["Staging"] == 1
        assert summary["stage_counts"]["Production"] == 1
        assert summary["latest_version"] == "2.0.0"  # 按时间排序的最新版本

    def test_get_model_summary_not_found(self, registry_path: Path):
        """测试获取不存在模型的摘要。"""
        registry = ModelRegistry(registry_path)
        summary = registry.get_model_summary("nonexistent")

        assert summary is None


class TestModelDeletion:
    """测试模型删除功能。"""

    def test_delete_model_version(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试删除模型版本。"""
        registry_data = {"test-model": sample_model_versions}
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        # 删除一个版本
        registry.delete_model_version("test-model", "1.0.0")

        # 验证版本已删除
        version_info = registry.get_model_version("test-model", "1.0.0")
        assert version_info is None

        # 验证其他版本仍存在
        version_info = registry.get_model_version("test-model", "2.0.0")
        assert version_info is not None

    def test_delete_last_version(
        self,
        registry_path: Path,
        sample_model_versions: List[Dict[str, Any]]
    ):
        """测试删除最后一个版本时删除整个模型条目。"""
        registry_data = {"test-model": sample_model_versions[:1]}  # 只有一个版本
        with open(registry_path, "w") as f:
            json.dump(registry_data, f)

        registry = ModelRegistry(registry_path)

        # 删除唯一的版本
        registry.delete_model_version("test-model", "1.0.0")

        # 验证整个模型条目已删除
        models = registry.list_registered_models()
        assert "test-model" not in models

    def test_delete_nonexistent_model(self, registry_path: Path):
        """测试删除不存在的模型版本。"""
        registry = ModelRegistry(registry_path)

        with pytest.raises(ModelNotRegisteredError):
            registry.delete_model_version("nonexistent", "1.0.0")
