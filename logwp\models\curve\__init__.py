"""
logwp.models.curve - 曲线基本属性管理模块

提供曲线基本属性的结构化管理，包括名称、单位、类型、类别、备注、维数等核心信息。
完全遵循FAP原则，与扩展属性系统分离，专注于曲线的必备基本属性。

Architecture
------------
层次/依赖: logwp包曲线管理模块，依赖constants层（格式无关常量）
设计原则: 格式无关、职责分离、类型安全、CIIA支持
性能特征: 轻量级对象、高效查询、内存优化

Package Structure
-----------------
- metadata: 曲线基本属性和元数据管理器
- utils: 曲线相关工具函数（如类型推断、维数检测等）

职责分工：
- 基本属性管理：每条曲线必备的核心信息（名称、单位、类型等）
- 扩展属性管理：用户自定义的动态属性（通过logwp/ext_attr/机制）
- 数据内容管理：实际曲线数据（存储在pd.DataFrame中）

Examples
--------
>>> from logwp.models.curve import CurveBasicAttributes, CurveMetadata, CurveExpansionMode
>>> from logwp.constants import WpDataType, WpCurveCategory, WpCurveDimension
>>>
>>> # 创建曲线基本属性（推荐使用便捷方法）
>>> gr_attrs = CurveBasicAttributes.create_1d_curve(
...     name="GR",
...     unit="API",
...     category=WpCurveCategory.LOGGING,
...     description="自然伽马射线"
... )
>>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve(
...     name="T2_VALUE",
...     element_count=4,
...     unit="ms",
...     description="T2谱数据"
... )
>>>
>>> # 创建曲线元数据管理器
>>> metadata = CurveMetadata()
>>> metadata.add_curve(gr_attrs)
>>> metadata.add_curve(t2_attrs)
>>>
>>> # 查询曲线信息
>>> curve = metadata.get_curve("GR")
>>> well_curves = metadata.get_well_identifier_curves()
>>>
>>> # 展开曲线名称列表
>>> compact_list = ['GR', 'T2_VALUE']
>>> expanded = metadata.expand_curve_names(compact_list, CurveExpansionMode.EXPANDED)
>>> # ['GR', 'T2_VALUE[1]', 'T2_VALUE[2]', 'T2_VALUE[3]', 'T2_VALUE[4]']
>>> df_columns = metadata.expand_curve_names(compact_list, CurveExpansionMode.DATAFRAME)
>>> # ['GR', 'T2_VALUE_1', 'T2_VALUE_2', 'T2_VALUE_3', 'T2_VALUE_4']

References
----------
- 《SCAPE_SAD_软件架构设计.md》FAP-1到FAP-11 - 格式无关原则
- 《SCAPE_CCG_编码与通用规范.md》CS-2 - 包前缀命名规范
"""

from __future__ import annotations

# 核心曲线元数据组件
from .metadata import (
    CurveBasicAttributes,
    CurveMetadata,
    CurveExpansionMode,
    CurveItemType,
    CurveListType,
    CurveItemAnalysis,
    CurveConflict,
    CurveListAnalysis,
)

__all__ = [
    # 核心组件
    "CurveBasicAttributes",
    "CurveMetadata",
    "CurveExpansionMode",
    # 曲线列表分析
    "CurveItemType",
    "CurveListType",
    "CurveItemAnalysis",
    "CurveConflict",
    "CurveListAnalysis",
]
