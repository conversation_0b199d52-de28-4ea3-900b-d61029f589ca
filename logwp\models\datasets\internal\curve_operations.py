"""数据集曲线操作服务。

无状态的服务函数，直接操作DataFrame和CurveMetadata。
专供datasets模块内部使用，不对业务层开放。
"""

from __future__ import annotations
import pandas as pd

from logwp.models.curve.metadata import CurveMetadata
from logwp.models.exceptions import WpDataError
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger

logger = get_logger(__name__)


def remove_curve_inplace(df: pd.DataFrame, curve_metadata: CurveMetadata, curve_name: str) -> None:
    """就地删除曲线元数据和数据体。

    支持三种删除模式：
    1. 删除一维曲线：删除对应的DataFrame列和CurveBasicAttributes
    2. 删除整个二维组合曲线：删除所有相关DataFrame列和CurveBasicAttributes
    3. 删除二维组合曲线元素：删除对应DataFrame列，拆分二维组合曲线为一维曲线

    Args:
        df: 数据集DataFrame（会被就地修改）
        curve_metadata: 曲线元数据（会被就地修改）
        curve_name: 曲线名称或二维组合曲线元素名称

    Raises:
        WpDatasetError: 当曲线不存在或删除失败时

    Examples:
        >>> # 删除一维曲线
        >>> remove_curve_inplace(df, metadata, "GR")
        >>>
        >>> # 删除整个二维组合曲线
        >>> remove_curve_inplace(df, metadata, "T2_VALUE")
        >>>
        >>> # 删除二维组合曲线元素
        >>> remove_curve_inplace(df, metadata, "T2_VALUE[2]")
    """
    logger.debug(
        "开始就地删除曲线",
        operation="remove_curve_inplace",
        curve_name=curve_name,
        df_shape=df.shape,
        curve_count=len(curve_metadata.curves)
    )

    if df is None or curve_metadata is None:
        raise WpDataError(
            "数据集为空，无法删除曲线",
            context=ErrorContext(
                operation="remove_curve_inplace",
                additional_info={
                    "curve_name": curve_name,
                    "has_df": df is not None,
                    "has_metadata": curve_metadata is not None
                }
            )
        )

    # 确定要删除的DataFrame列
    columns_to_remove = _get_columns_to_remove(curve_metadata, curve_name)

    # 验证列存在
    missing_columns = [col for col in columns_to_remove if col not in df.columns]
    if missing_columns:
        raise WpDataError(
            f"要删除的DataFrame列不存在: {missing_columns}",
            context=ErrorContext(
                operation="remove_curve_inplace",
                additional_info={
                    "curve_name": curve_name,
                    "missing_columns": missing_columns,
                    "available_columns": list(df.columns),
                    "expected_columns": columns_to_remove
                }
            )
        )

    # 原子性删除操作
    try:
        # 1. 就地删除DataFrame列
        df.drop(columns=columns_to_remove, inplace=True)

        # 2. 删除曲线元数据
        curve_metadata.remove_curve(curve_name)

        logger.info(
            "曲线就地删除成功",
            operation="remove_curve_inplace",
            curve_name=curve_name,
            removed_columns=columns_to_remove,
            remaining_columns=len(df.columns),
            remaining_curves=len(curve_metadata.curves)
        )

    except Exception as e:
        # 如果删除过程中出现错误，确保数据一致性
        raise WpDataError(
            f"删除曲线 '{curve_name}' 时发生错误: {str(e)}",
            context=ErrorContext(
                operation="remove_curve_inplace",
                additional_info={
                    "curve_name": curve_name,
                    "columns_to_remove": columns_to_remove,
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
            )
        ) from e


def _get_columns_to_remove(curve_metadata: CurveMetadata, curve_name: str) -> list[str]:
    """确定要删除的DataFrame列名。

    Args:
        curve_metadata: 曲线元数据
        curve_name: 曲线名称或二维组合曲线元素名称

    Returns:
        list[str]: 要删除的DataFrame列名列表（友好名称）

    Raises:
        WpDatasetError: 曲线不存在时抛出
    """
    # 检查是否为二维组合曲线元素
    is_element, parent_name = curve_metadata._is_2d_element_name(curve_name)

    if is_element:
        # 删除二维组合曲线元素：只删除对应的友好名称列
        parent_curve = curve_metadata._find_parent_2d_curve(curve_name)
        if not parent_curve:
            raise WpDataError(
                f"二维组合曲线元素 '{curve_name}' 不存在或其父曲线不存在",
                context=ErrorContext(
                    operation="get_columns_to_remove",
                    additional_info={
                        "curve_name": curve_name,
                        "parent_curve": parent_name,
                        "is_element": True
                    }
                )
            )

        # 找到对应的友好名称
        if parent_curve.element_names and parent_curve.dataframe_element_names:
            try:
                element_index = parent_curve.element_names.index(curve_name)
                friendly_name = parent_curve.dataframe_element_names[element_index]
                return [friendly_name]
            except (ValueError, IndexError):
                raise WpDataError(
                    f"无法找到元素 '{curve_name}' 对应的友好名称",
                    context=ErrorContext(
                        operation="get_columns_to_remove",
                        additional_info={
                            "curve_name": curve_name,
                            "parent_elements": parent_curve.element_names,
                            "parent_friendly_names": parent_curve.dataframe_element_names
                        }
                    )
                )
        else:
            raise WpDataError(
                f"父曲线 '{parent_name}' 的元素信息不完整",
                context=ErrorContext(
                    operation="get_columns_to_remove",
                    additional_info={
                        "curve_name": curve_name,
                        "parent_curve": parent_name,
                        "has_element_names": parent_curve.element_names is not None,
                        "has_friendly_names": parent_curve.dataframe_element_names is not None
                    }
                )
            )
    else:
        # 删除一维曲线或整个二维组合曲线
        if curve_name not in curve_metadata.curves:
            raise WpDataError(
                f"曲线 '{curve_name}' 不存在",
                context=ErrorContext(
                    operation="get_columns_to_remove",
                    additional_info={
                        "curve_name": curve_name,
                        "available_curves": list(curve_metadata.curves.keys()),
                        "is_element": False
                    }
                )
            )

        curve_attrs = curve_metadata.curves[curve_name]
        if curve_attrs.is_2d_composite_curve():
            # 删除整个二维组合曲线：删除所有元素的友好名称
            return curve_attrs.dataframe_element_names or []
        else:
            # 删除一维曲线：删除友好名称
            return [curve_attrs.dataframe_column_name]


def validate_curves_consistency(df: pd.DataFrame, curve_metadata: CurveMetadata) -> None:
    """验证DataFrame列与曲线元数据的友好名称一致性。

    验证DataFrame的列名是否与CurveMetadata中所有曲线的DataFrame友好名称完全匹配。
    这确保DataFrame使用的是机器学习库兼容的列名，而不是原始曲线名称。

    Args:
        df: DataFrame数据（列名必须是友好名称）
        curve_metadata: 曲线元数据

    Raises:
        WpDataError: DataFrame列名与曲线元数据的友好名称不一致

    References:
        《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§4.2 - 曲线元数据与数据体的共同进退原则
        《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§4.3 - DataFrame友好名称原则
    """
    logger.debug(
        "开始验证曲线一致性",
        operation="validate_curves_consistency",
        df_shape=df.shape,
        curve_count=len(curve_metadata.curves)
    )

    df_columns = set(df.columns)

    # 获取所有曲线的DataFrame友好名称
    expected_friendly_names = set()
    for curve in curve_metadata.curves.values():
        if curve.is_2d_composite_curve():
            # 二维组合曲线：添加所有元素的友好名称
            if curve.dataframe_element_names:
                expected_friendly_names.update(curve.dataframe_element_names)
        else:
            # 一维曲线：添加基础友好名称
            expected_friendly_names.add(curve.dataframe_column_name)

    if df_columns != expected_friendly_names:
        missing_in_df = list(expected_friendly_names - df_columns)
        unexpected_in_df = list(df_columns - expected_friendly_names)

        logger.error(
            "DataFrame列名与曲线元数据不一致",
            operation="validate_curves_consistency",
            df_columns=list(df_columns),
            expected_friendly_names=list(expected_friendly_names),
            missing_in_df=missing_in_df,
            unexpected_in_df=unexpected_in_df
        )

        raise WpDataError(
            "DataFrame列名与曲线元数据的友好名称不一致。DataFrame必须使用机器学习库兼容的友好列名。",
            context=ErrorContext(
                operation="validate_curves_consistency",
                additional_info={
                    "df_columns": list(df_columns),
                    "expected_friendly_names": list(expected_friendly_names),
                    "missing_in_df": missing_in_df,
                    "unexpected_in_df": unexpected_in_df,
                    "curve_count": len(curve_metadata.curves),
                    "validation_type": "dataframe_friendly_names"
                }
            )
        )

    logger.debug(
        "曲线一致性验证通过",
        operation="validate_curves_consistency",
        df_columns_count=len(df_columns),
        expected_names_count=len(expected_friendly_names)
    )


# 未来可以添加更多就地操作函数
def merge_curves_inplace(df: pd.DataFrame, curve_metadata: CurveMetadata,
                        source_curves: list[str], target_name: str,
                        strategy: str = "average") -> None:
    """就地合并多个曲线（未来实现）。

    Args:
        df: 数据集DataFrame（会被就地修改）
        curve_metadata: 曲线元数据（会被就地修改）
        source_curves: 源曲线名称列表
        target_name: 目标曲线名称
        strategy: 合并策略
    """
    pass


def rename_curve_inplace(df: pd.DataFrame, curve_metadata: CurveMetadata,
                        old_name: str, new_name: str) -> None:
    """就地重命名曲线（未来实现）。

    Args:
        df: 数据集DataFrame（会被就地修改）
        curve_metadata: 曲线元数据（会被就地修改）
        old_name: 原曲线名称
        new_name: 新曲线名称
    """
    pass
