from __future__ import annotations

import numpy as np
import pandas as pd
import pytest

from logwp.models.constants import WpDataType
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.models.exceptions import WpCurveMetadataError, WpDataError
from logwp.testing.builders import DatasetBuilder


@pytest.fixture
def sample_bundle() -> WpDataFrameBundle:
    """
    创建一个标准的WpDataFrameBundle用于测试。

    此Fixture包含:
    - 一条名为 'K_LABEL_TYPE' 的字符串类型曲线，用于测试替换。
    - 一条名为 'T2_VALUE' 的二维组合曲线，用于测试边界情况。
    """
    df = pd.DataFrame(
        {
            "MD": np.arange(100, 105, 1.0),
            "GR": np.array([50, 51, 52, 53, 54]),
            "K_LABEL_TYPE": ["CORE", "MDT", "CORE", "UNKNOWN", "MDT"],
            "T2_VALUE_1": np.random.rand(5),
            "T2_VALUE_2": np.random.rand(5),
        }
    )
    # 使用便捷的 from_dataframe 类方法创建Bundle，它会自动推断元数据
    bundle = WpDataFrameBundle.from_dataframe(name="test_bundle", df=df)
    return bundle


def test_replace_curve_updates_data_and_metadata(sample_bundle: WpDataFrameBundle):
    """
    测试(Happy Path): replace_curve 正确更新数据、元数据和内部映射。
    """
    bundle = sample_bundle
    curve_name = "K_LABEL_TYPE"

    # 1. 准备新数据和元数据
    label_type_mapping = {"CORE": 1, "MDT": 2}
    new_data_series = (
        bundle.data[curve_name].map(label_type_mapping).fillna(0).astype(int)
    )
    new_data_np = new_data_series.to_numpy()
    new_description = "Encoded label type (1: CORE, 2: MDT, 0: UNKNOWN)"

    # 获取旧属性用于后续比较
    old_attrs = bundle.curve_metadata.get_curve(curve_name)
    assert old_attrs.data_type == WpDataType.STR

    # 2. 调用被测方法
    bundle.replace_curve(
        curve_name=curve_name,
        new_data=new_data_np,
        new_data_type=WpDataType.INT,
        new_description=new_description,
    )

    # 3. 断言
    # 3a. 数据正确性
    assert curve_name in bundle.data.columns
    assert pd.api.types.is_integer_dtype(bundle.data[curve_name])
    np.testing.assert_array_equal(bundle.data[curve_name].to_numpy(), new_data_np)

    # 3b. 元数据正确性
    new_attrs = bundle.curve_metadata.get_curve(curve_name)
    assert new_attrs is not None
    assert new_attrs.data_type == WpDataType.INT
    assert new_attrs.description == new_description
    # 检查其他属性是否被保留
    assert new_attrs.category == old_attrs.category
    assert new_attrs.unit == old_attrs.unit

    # 3c. 内部映射正确性
    assert curve_name in bundle.curve_to_columns_map
    assert bundle.curve_to_columns_map[curve_name] == [curve_name]


def test_replace_curve_non_existent_curve_raises_error(sample_bundle: WpDataFrameBundle):
    """测试: 尝试替换不存在的曲线时，应抛出 WpCurveMetadataError。"""
    with pytest.raises(WpCurveMetadataError, match="该曲线在元数据中不存在"):
        sample_bundle.replace_curve(
            curve_name="NON_EXISTENT_CURVE",
            new_data=np.array([1, 2, 3, 4, 5]),
            new_data_type=WpDataType.INT,
        )


def test_extract_dataframe_bundle_with_none_curves_preserves_interval_type():
    """
    GIVEN: 一个 WpIntervalDataset 实例。
    WHEN: 调用 extract_curve_dataframe_bundle，curve_names为None，但 include_system_columns=True。
    THEN: 返回的 WpDataFrameBundle 应该包含所有数据曲线，并被正确识别为区间类型。
    """
    # ARRANGE: 使用测试构建器创建一个标准的 WpIntervalDataset，包含多条数据曲线。
    interval_dataset = DatasetBuilder.quick_interval_dataset(
        name="test_zones_all_curves",
        intervals=[(2500, 2505), (2505, 2510)],
        curves={"FORMATION": ["FM_A", "FM_B"], "NET_PAY": [4.5, 6.2]},
    )

    # 前置条件检查，确保我们的测试数据是正确的区间数据集
    assert interval_dataset.is_interval_dataset() is True
    original_data_curves = interval_dataset.curve_metadata.get_data_curves()
    assert "FORMATION" in original_data_curves
    assert "NET_PAY" in original_data_curves

    # ACT: 提取所有数据曲线 (curve_names=None) 和系统列
    bundle = interval_dataset.extract_curve_dataframe_bundle(
        curve_names=None, include_system_columns=True
    )

    # ASSERT: 验证类型和元数据
    assert bundle.is_interval_bundle is True, (
        "is_interval_bundle 应该为 True，即使 curve_names 为 None"
    )

    # 检查是否包含了所有数据曲线
    assert "FORMATION" in bundle.curve_to_columns_map
    assert "NET_PAY" in bundle.curve_to_columns_map
    assert len(bundle.curve_to_columns_map) == 2, "应包含所有原始数据曲线"

    # 检查系统曲线元数据是否被正确保留
    top_curve, bottom_curve = interval_dataset.get_interval_depth_reference_curves()
    assert bundle.curve_metadata.has_curve(top_curve.name)
    assert bundle.curve_metadata.get_curve(top_curve.name).depth_role == "TOP"
    assert bundle.curve_metadata.has_curve(bottom_curve.name)
    assert bundle.curve_metadata.get_curve(bottom_curve.name).depth_role == "BOTTOM"


def test_extract_dataframe_bundle_from_interval_dataset_preserves_interval_type():
    """
    GIVEN: 一个 WpIntervalDataset 实例。
    WHEN: 调用 extract_curve_dataframe_bundle 并设置 include_system_columns=True。
    THEN: 返回的 WpDataFrameBundle 应该被正确识别为区间类型。

    这个测试专门用于分析为什么即使包含了系统列，is_interval_bundle 属性也未被正确设置。
    """
    # ARRANGE: 使用测试构建器创建一个标准的 WpIntervalDataset。
    # DatasetBuilder 会自动创建正确的元数据，包括为顶/底深度曲线设置 TOP/BOTTOM 角色。
    interval_dataset = DatasetBuilder.quick_interval_dataset(
        name="test_zones",
        intervals=[(2500, 2505), (2505, 2510), (2510, 2515)],
        curves={"FORMATION": ["FM_A", "FM_B", "FM_C"], "NET_PAY": [4.5, 3.2, 5.1]},
    )

    # 前置条件检查，确保我们的测试数据是正确的区间数据集
    assert interval_dataset.is_interval_dataset() is True

    # ACT: 提取一个包含数据曲线和系统曲线（井名、顶/底深度）的Bundle
    bundle = interval_dataset.extract_curve_dataframe_bundle(
        curve_names=["FORMATION"], include_system_columns=True
    )

    # ASSERT
    # 核心断言：验证 is_interval_bundle 是否为 True。
    # 根据用户反馈，这一步会失败，从而验证了问题的存在。
    assert bundle.is_interval_bundle is True, (
        "is_interval_bundle 属性为 False，表明顶/底深度曲线的元数据在提取过程中丢失或损坏。"
    )

    # 诊断性断言：如果核心断言失败，这些断言能帮助我们定位具体原因。
    # 1. 检查 Bundle 的元数据中是否存在顶/底深度曲线。
    top_curve, bottom_curve = interval_dataset.get_interval_depth_reference_curves()
    assert bundle.curve_metadata.has_curve(
        top_curve.name
    ), f"Bundle的元数据中缺少顶深曲线 '{top_curve.name}'。"
    assert bundle.curve_metadata.has_curve(
        bottom_curve.name
    ), f"Bundle的元数据中缺少底深曲线 '{bottom_curve.name}'。"

    # 2. 检查这些曲线的 depth_role 属性是否被正确保留。
    #    如果这一步失败，说明问题出在元数据提取（extract_metadata）的过程中。
    from logwp.models.constants import WpDepthRole

    new_top_attrs = bundle.curve_metadata.get_curve(top_curve.name)
    new_bottom_attrs = bundle.curve_metadata.get_curve(bottom_curve.name)
    assert new_top_attrs.depth_role == WpDepthRole.TOP, (
        f"顶深曲线的 depth_role 未被保留。期望值: TOP, 实际值: {new_top_attrs.depth_role}。"
    )
    assert new_bottom_attrs.depth_role == WpDepthRole.BOTTOM, (
        f"底深曲线的 depth_role 未被保留。期望值: BOTTOM, 实际值: {new_bottom_attrs.depth_role}。"
    )

    # 3. 检查便捷属性是否正确设置
    assert bundle.depth_top_curve_map is not None and top_curve.name in bundle.depth_top_curve_map
    assert bundle.depth_curve_map is None, "depth_curve_map 应该为 None，因为这是区间型Bundle。"


def test_replace_curve_length_mismatch_raises_error(sample_bundle: WpDataFrameBundle):
    """测试: 提供长度不匹配的新数据时，应抛出 WpDataError。"""
    with pytest.raises(WpDataError, match="新数据长度 .* 与Bundle的DataFrame长度 .* 不匹配"):
        sample_bundle.replace_curve(
            curve_name="GR",
            new_data=np.array([1, 2, 3]),  # 长度不正确
            new_data_type=WpDataType.INT,
        )


def test_replace_curve_on_2d_curve_raises_error(sample_bundle: WpDataFrameBundle):
    """测试: 尝试替换二维曲线时，应抛出 WpDataError。"""
    curve_name_2d = "T2_VALUE"
    assert not sample_bundle.curve_metadata.get_curve(curve_name_2d).is_1d_curve()

    with pytest.raises(WpDataError, match="目前仅支持替换一维曲线"):
        sample_bundle.replace_curve(
            curve_name=curve_name_2d,
            new_data=np.array([1, 2, 3, 4, 5]),
            new_data_type=WpDataType.INT,
        )
