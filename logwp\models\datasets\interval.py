from __future__ import annotations

"""logwp.models.datasets.interval - 区间型数据集

WpIntervalDataset处理区间型测井数据，支持层状数据的顶界和底界深度。

Architecture
------------
层次/依赖: datasets层具体实现，继承WpDepthIndexedDatabaseBase
设计原则: 区间数据特化、双深度索引、层状数据优化
性能特征: 内存优化、区间查询、GPU支持

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- SC-1: 算法正确性，包含区间约束验证

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§3.2.4 - WpIntervalDataset设计
- 《SCAPE_WFS_WP文件规范.md》A.3.3 - 区间型数据集规范
"""

import copy
from datetime import datetime
from typing import Any

import numpy as np
import pandas as pd

from logwp.models.constants import WpDsType
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata
from logwp.models.exceptions import ErrorContext, WpDataError
from logwp.models.types import WpDatasetName, WpIdentifier
from logwp.infra import get_logger

from logwp.models.datasets.base import WpDepthIndexedDatasetBase

logger = get_logger(__name__)


class WpIntervalDataset(WpDepthIndexedDatasetBase):
    """区间型数据集。

    处理区间型测井数据，如测井解释结论等层状数据。
    支持双深度索引（顶界和底界）、区间验证和层状数据查询。

    DataFrame约定：
    - TOP_MD和BOT_MD列作为普通列存储，不作为索引
    - 用户可调用 create_interval_index() 等方法创建所需索引

    Architecture
    ------------
    层次/依赖: datasets层区间型数据集，继承WpDepthIndexedDatabaseBase
    设计原则: 区间数据特化、双深度索引、层状优化
    性能特征: 区间查询优化、内存管理、GPU支持

    Interval Dataset Requirements（基于WFS A.3.3节）：
    - **双深度索引**: 必须包含顶界深度列（MD_Top）和底界深度列（MD_Bottom）
    - **层状数据**: 每行代表一个地质层段或解释单元
    - **区间约束**: 顶界深度必须小于底界深度
    - **物理约束**: 层厚>0、孔隙度[0,1]、饱和度[0,1]等

    Examples:
        >>> df = pd.DataFrame({
        ...     "MD_Top": [2500.0, 2505.0, 2510.0],
        ...     "MD_Bottom": [2505.0, 2510.0, 2515.0],
        ...     "FACIES": ["砂岩", "泥岩", "砂岩"],
        ...     "PHIT_AVG": [0.15, 0.08, 0.18],
        ...     "WELL": ["C-1", "C-1", "C-1"]
        ... })
        >>> dataset = WpIntervalDataset(name="PLT", df=df)
        >>> assert dataset.validate()
        >>>
        >>> # 区间查询
        >>> layers = dataset.find_intervals_at_depth(2507.5)
        >>> assert len(layers) == 1

    References:
        《SCAPE_WFS_WP文件规范.md》A.3.3 - 区间型数据集规范
    """

    @property
    def dataset_type(self) -> WpDsType:
        """数据集类型。

        Returns:
            WpDsType: 区间型数据集类型
        """
        return WpDsType.INTERVAL


    @classmethod
    def create_empty(cls, name: str) -> WpIntervalDataset:
        """创建空的区间型数据集（io层调用）。

        Args:
            name: 数据集名称

        Returns:
            WpIntervalDataset: 空的区间型数据集实例

        Examples:
            >>> dataset = WpIntervalDataset.create_empty("PLT")
            >>> assert dataset.dataset_type == WpDsType.INTERVAL
            >>> assert dataset.df.empty

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.2 - 创建空数据集
        """
        return cls(name=WpIdentifier(name))

    @classmethod
    def create_with_data(
        cls,
        name: WpDatasetName | WpIdentifier,
        df: pd.DataFrame,
        curve_metadata: CurveMetadata,
        *,
        depth_sampling_rate: float = 0
    ) -> WpIntervalDataset:
        """创建区间型数据集并附加数据。

        工厂方法，创建新的区间型数据集实例并同时附加DataFrame和曲线元数据。

        Args:
            name: 数据集名称
            df: DataFrame数据体
            curve_metadata: 曲线元数据
            depth_sampling_rate: 深度采样间隔，区间型数据集忽略此参数

        Returns:
            WpIntervalDataset: 创建的区间型数据集实例

        Examples:
            >>> df = pd.DataFrame({'WELL': ['C-1'], 'TOP_MD': [2500.0], 'BOT_MD': [2510.0], 'ZONE': ['RES_A']})
            >>> metadata = CurveMetadata()
            >>> # ... 配置metadata ...
            >>> dataset = WpIntervalDataset.create_with_data("zones", df, metadata)
            >>> assert dataset.name == "zones"
            >>> assert dataset.dataset_type == WpDsType.INTERVAL

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.3 - 工厂方法模式
        """
        # 创建空数据集实例
        instance = cls.create_empty(name)

        # 附加数据和元数据
        instance.direct_attach_curves_data(df, curve_metadata)

        return instance

    def clone_dataset(self) -> WpIntervalDataset:
        """克隆区间型数据集，返回新的数据集实例。

        创建当前数据集的深拷贝，包括：
        - 数据体（DataFrame）的完整拷贝
        - 曲线元数据的完整拷贝
        - 所有属性的拷贝（除时间戳外）

        Returns:
            WpIntervalDataset: 克隆后的新区间型数据集实例

        Note:
            - 克隆后的数据集具有新的时间戳（created_at, modified_at）
            - 数据体和元数据完全独立，修改不会相互影响

        Examples:
            >>> original = dataset
            >>> cloned = dataset.clone_dataset()
            >>> assert cloned.name == original.name
            >>> assert cloned.df.equals(original.df)
            >>> assert cloned is not original  # 不同的对象
            >>> assert cloned.df is not original.df  # 不同的DataFrame
            >>> assert cloned.dataset_type == WpDsType.INTERVAL
        """
        # 深拷贝DataFrame
        cloned_df = self.df.copy(deep=True)

        # 深拷贝曲线元数据
        cloned_curve_metadata = copy.deepcopy(self.curve_metadata)

        # 创建新的数据集实例，使用新的时间戳
        now = datetime.now()
        cloned = WpIntervalDataset(
            name=self.name,  # WpIdentifier是不可变的，可以共享
            df=cloned_df,
            curve_metadata=cloned_curve_metadata,
            created_at=now,
            modified_at=now
        )

        return cloned

    # ------------------------------------------------------------
    # 深度参考曲线便捷服务方法实现
    # ------------------------------------------------------------

    def get_depth_reference_count(self) -> int:
        """获取区间型数据集深度参考曲线的条数。

        Returns:
            int: 深度参考曲线数量，区间型数据集固定返回2

        Examples:
            >>> interval_dataset = WpIntervalDataset(...)
            >>> assert interval_dataset.get_depth_reference_count() == 2
        """
        return 2

    def get_single_depth_reference_curve(self) -> CurveBasicAttributes:
        """区间型数据集不支持单一深度索引。

        Raises:
            WpDataError: 区间型数据集不支持此操作

        Note:
            - 区间型数据集有双深度索引（顶界和底界）
            - 应使用get_interval_depth_reference_curves()方法
        """
        raise WpDataError(
            "区间型数据集不支持单一深度索引操作",
            context=ErrorContext(
                operation="get_single_depth_reference_curve",
                dataset_name=str(self.name),
                additional_info={
                    "dataset_type": self.dataset_type.value,
                    "supported_operation": "get_interval_depth_reference_curves",
                    "reason": "interval_dataset_dual_depth_only"
                }
            )
        )

    def get_interval_depth_reference_curves(self) -> tuple[CurveBasicAttributes, CurveBasicAttributes]:
        """获取区间型数据集的双深度曲线。

        Returns:
            tuple[CurveBasicAttributes, CurveBasicAttributes]: (顶界深度曲线, 底界深度曲线)

        Raises:
            WpDataError: 当没有找到足够的深度参考曲线时抛出

        Note:
            - 利用CurveMetadata.get_depth_reference_curves()方法
            - WFS规范保证返回列表的第一个和第二个分别是TOP和BOTTOM深度参考曲线

        Examples:
            >>> interval_dataset = WpIntervalDataset(...)
            >>> top_curve, bottom_curve = interval_dataset.get_interval_depth_reference_curves()
            >>> assert top_curve.depth_role == WpDepthRole.TOP
            >>> assert bottom_curve.depth_role == WpDepthRole.BOTTOM
        """
        depth_curve_names = self.curve_metadata.get_depth_reference_curves()

        if len(depth_curve_names) < 2:
            raise WpDataError(
                "区间型数据集缺少足够的深度参考曲线",
                context=ErrorContext(
                    operation="get_interval_depth_reference_curves",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "required_depth_curves": 2,
                        "found_depth_curves": len(depth_curve_names),
                        "available_curves": list(self.curve_metadata.curves.keys())
                    }
                )
            )

        # WFS规范保证第一个是TOP，第二个是BOTTOM
        top_curve_name = depth_curve_names[0]
        bottom_curve_name = depth_curve_names[1]

        top_curve = self.curve_metadata.get_curve(top_curve_name)
        bottom_curve = self.curve_metadata.get_curve(bottom_curve_name)

        return (top_curve, bottom_curve)

    def get_wells_depth_ranges(self) -> dict[str, tuple[float, float]]:
        """获取每口井的深度范围。

        Returns:
            dict[str, tuple[float, float]]: {井名: (最小深度, 最大深度)}

        Note:
            - 基于顶界深度的最小值和底界深度的最大值
            - 空数据集返回空字典
            - 按井名分组计算每口井的深度范围

        Examples:
            >>> interval_dataset = WpIntervalDataset(...)
            >>> wells_ranges = interval_dataset.get_wells_depth_ranges()
            >>> # {'C-1': (2500.0, 2600.0), 'C-2': (2550.0, 2650.0)}
        """
        if self.df.empty:
            return {}

        try:
            top_curve, bottom_curve = self.get_interval_depth_reference_curves()
            top_column = top_curve.dataframe_column_name
            bottom_column = bottom_curve.dataframe_column_name

            if top_column not in self.df.columns or bottom_column not in self.df.columns:
                return {}

            # 获取井名曲线
            well_curves = self.curve_metadata.get_well_identifier_curves()
            if not well_curves or well_curves[0] not in self.df.columns:
                # 没有井名曲线，视为单井数据
                top_series = self.df[top_column].dropna()
                bottom_series = self.df[bottom_column].dropna()

                if top_series.empty or bottom_series.empty:
                    return {}

                min_depth = float(top_series.min())
                max_depth = float(bottom_series.max())
                return {"Unknown": (min_depth, max_depth)}

            well_column = well_curves[0]
            wells_ranges = {}

            # 按井分组计算深度范围
            for well_name in self.df[well_column].dropna().unique():
                well_data = self.df[self.df[well_column] == well_name]
                well_top_depths = well_data[top_column].dropna()
                well_bottom_depths = well_data[bottom_column].dropna()

                if not well_top_depths.empty and not well_bottom_depths.empty:
                    min_depth = float(well_top_depths.min())
                    max_depth = float(well_bottom_depths.max())
                    wells_ranges[str(well_name)] = (min_depth, max_depth)

            return wells_ranges

        except WpDataError:
            # 没有深度曲线时返回空字典
            return {}

    def get_depth_range_for_well(self, well_name: str) -> tuple[float, float]:
        """获取指定井的深度范围。

        Args:
            well_name: 井名

        Returns:
            tuple[float, float]: (最小深度, 最大深度)

        Raises:
            WpDataError: 当指定井不存在时抛出

        Examples:
            >>> interval_dataset = WpIntervalDataset(...)
            >>> min_depth, max_depth = interval_dataset.get_depth_range_for_well("C-1")
            >>> assert min_depth <= max_depth
        """
        wells_ranges = self.get_wells_depth_ranges()

        if well_name not in wells_ranges:
            available_wells = list(wells_ranges.keys())
            raise WpDataError(
                f"井 '{well_name}' 不存在于数据集中",
                context=ErrorContext(
                    operation="get_depth_range_for_well",
                    dataset_name=str(self.name),
                    additional_info={
                        "requested_well": well_name,
                        "available_wells": available_wells,
                        "total_wells": len(available_wells)
                    }
                )
            )

        return wells_ranges[well_name]

    def get_depth_reference_unit(self) -> str | None:
        """获取区间型数据集深度参考曲线的单位。

        Returns:
            str | None: 深度曲线的单位，如果没有单位则返回None

        Note:
            - 利用get_interval_depth_reference_curves()获取顶界和底界深度曲线
            - 返回顶界深度曲线的单位（WFS规范保证顶界和底界单位一致）

        Examples:
            >>> interval_dataset = WpIntervalDataset(...)
            >>> unit = interval_dataset.get_depth_reference_unit()
            >>> assert unit == "m"  # 顶界和底界单位一致
        """
        try:
            top_curve, bottom_curve = self.get_interval_depth_reference_curves()
            # WFS规范保证顶界和底界深度曲线单位一致，返回顶界单位
            return top_curve.unit
        except WpDataError:
            # 没有深度曲线时返回None
            return None

    def calculate_depth_sampling_rate(self) -> float:
        """区间型数据集不支持深度间隔计算。

        Raises:
            WpDataError: 区间型数据集不支持此操作

        Note:
            - 区间型数据集表示层状数据，没有固定的深度间隔概念
            - 每个区间代表一个地质层段，层厚度可能差异很大
            - 应使用其他方法分析区间数据的特征

        Examples:
            >>> interval_dataset = WpIntervalDataset(...)
            >>> try:
            ...     sampling_rate = interval_dataset.calculate_depth_sampling_rate()
            ... except WpDataError:
            ...     print("区间型数据集不支持深度采样间隔计算")
        """
        raise WpDataError(
            "区间型数据集不支持深度采样间隔计算操作",
            context=ErrorContext(
                operation="calculate_depth_sampling_rate",
                dataset_name=str(self.name),
                additional_info={
                    "dataset_type": self.dataset_type.value,
                    "reason": "interval_dataset_no_sampling_rate_concept",
                    "alternative_methods": [
                        "get_depth_range",
                        "analyze_layer_thickness",
                        "get_interval_depth_reference_curves"
                    ]
                }
            )
        )

    def to_continuous_dataset(
        self,
        sampling_interval: float,
        *,
        depth_range: tuple[float, float] | None = None,
        fill_value: Any = np.nan,
        out_of_range_fill_value: Any = np.nan,
        new_dataset_name: str | None = None
    ) -> WpContinuousDataset:
        """转换为连续型数据集。

        将当前区间型数据集转换为连续型数据集，支持自定义深度采样间隔和范围。

        Args:
            sampling_interval: 深度采样间隔（必须 > 0）
            depth_range: 输出深度范围，None表示使用原数据集范围
            fill_value: 原数据集未覆盖区域的填充值
            out_of_range_fill_value: 超出原数据集范围的填充值
            new_dataset_name: 新数据集名称，None表示自动生成

        Returns:
            WpContinuousDataset: 转换后的连续型数据集

        Raises:
            WpValidationError: 当参数验证失败时抛出
            WpDataError: 当数据集状态异常时抛出

        Note:
            - 转换后的深度曲线名称为WpStandardColumn.DEPTH
            - 保留原有的井名曲线，使用WpStandardColumn.WELL_NAME
            - 区间值不进行重采样，直接使用原始层段值
            - 深度范围会按采样间隔对齐
            - 转换过程中曲线值不需要重采集，使用原始层段对应的值

        Examples:
            >>> # 基本转换
            >>> continuous_dataset = interval_dataset.to_continuous_dataset(
            ...     sampling_interval=0.5
            ... )
            >>> assert continuous_dataset.dataset_type == WpDsType.CONTINUOUS
            >>> assert "depth" in continuous_dataset.df.columns
            >>> assert "well_name" in continuous_dataset.df.columns

            >>> # 自定义深度范围转换
            >>> extended_dataset = interval_dataset.to_continuous_dataset(
            ...     sampling_interval=1.0,
            ...     depth_range=(2490.0, 2520.0),  # 扩展范围
            ...     fill_value=0.0,                # 原范围内未覆盖区域填充0
            ...     out_of_range_fill_value=-999,  # 扩展区域填充-999
            ...     new_dataset_name="extended_continuous"
            ... )

        References:
            《SCAPE_DDS_logwp_层段转连续数据集.md》§2.2 - WpIntervalDataset API方法
        """
        # 导入放在方法内部避免循环导入
        from logwp.models.datasets.continuous import WpContinuousDataset
        from logwp.models.datasets.internal.interval_to_continuous import (
            convert_interval_to_continuous,
        )

        logger.info(
            "开始区间型数据集转连续型数据集",
            operation="to_continuous_dataset",
            dataset_name=str(self.name),
            sampling_interval=sampling_interval,
            custom_depth_range=depth_range is not None,
            custom_dataset_name=new_dataset_name is not None
        )

        # 调用服务层进行转换
        converted_df, converted_metadata, actual_depth_range = convert_interval_to_continuous(
            self,
            sampling_interval,
            depth_range=depth_range,
            fill_value=fill_value,
            out_of_range_fill_value=out_of_range_fill_value
        )

        # 确定新数据集名称
        if new_dataset_name is None:
            new_dataset_name = f"{self.name}_continuous"

        # 创建连续型数据集
        continuous_dataset = WpContinuousDataset.create_with_data(
            name=new_dataset_name,
            df=converted_df,
            curve_metadata=converted_metadata
        )

        # 设置深度采样间隔
        continuous_dataset.depth_sampling_rate = sampling_interval

        logger.info(
            "区间型数据集转连续型数据集完成",
            operation="to_continuous_dataset",
            original_dataset=str(self.name),
            new_dataset=str(continuous_dataset.name),
            original_rows=len(self.df),
            converted_rows=len(converted_df),
            actual_depth_range=actual_depth_range,
            sampling_interval=sampling_interval
        )

        return continuous_dataset
