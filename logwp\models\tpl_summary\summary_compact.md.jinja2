# 数据概况 - {{ project_info.name }}

**生成时间**: {{ generation_info.generated_at }}

## 基本信息
- 数据集: {{ project_info.dataset_count }} 个
- 井头属性: {{ head_attributes.total_attributes }} 个
{% if head_attributes.by_wfs_category %}
  ({% for category, count in head_attributes.by_wfs_category.items() %}{{ category }}:{{ count }}{% if not loop.last %}, {% endif %}{% endfor %})
{% endif %}
- 井名映射: {{ well_mappings.total_mappings }} 个

## 数据集
{% for dataset_name, dataset_info in datasets.datasets.items() %}
### {{ dataset_name }}
{% if dataset_info.error is defined %}
❌ {{ dataset_info.error }}
{% else %}
{% set basic_info = dataset_info.basic_info %}
- 类型: {{ basic_info.type }}
- 行数: {{ basic_info.total_rows }}
- 曲线: {{ basic_info.curve_count }}
{% if dataset_info.well_statistics and dataset_info.well_statistics.error is not defined %}
- 井数: {{ dataset_info.well_statistics|length }}
{% endif %}
{% if dataset_info.curve_statistics and dataset_info.curve_statistics.summary %}
{% set summary = dataset_info.curve_statistics.summary %}
- 曲线类型: 数值{{ summary.numeric_count or 0 }}/类别{{ summary.categorical_count or 0 }}/标识{{ summary.identifier_count or 0 }}
{% endif %}
{% endif %}
{% endfor %}

---
*{{ generation_info.generator }} v{{ generation_info.version }}*
