"""logwp.extras.tracking - 实验追踪与产物管理系统

提供轻量级、通用的实验追踪与产物管理框架，支持以"运行"为中心的管理模式。

Architecture
------------
层次/依赖: logwp包扩展层，依赖models、utils、constants
设计原则: 轻量级实用性、以运行为中心、约定优于配置、通用性与可扩展性
性能特征: 本地文件系统、JSON序列化、原子性操作

Core Features
-------------
- **RunContext**: 运行上下文管理器，管理单次实验运行的完整生命周期
- **ModelRegistry**: 模型注册表，管理跨运行的模型版本和生命周期跟踪
- **轻量级设计**: 本地文件系统存储，无重度依赖或复杂服务器部署
- **完全可复现性**: 记录完整血缘信息（输入数据+代码版本+配置→输出产物与指标）

Package Structure
-----------------
- context.py: RunContext类，管理单次运行
- registry.py: ModelRegistry类，管理模型生命周期
- config.py: 基础配置模型
- exceptions.py: 专属异常类
- utils.py: 内部工具函数

Examples
--------
>>> from logwp.extras.tracking import RunContext, ModelRegistry
>>>
>>> # 基本运行追踪
>>> with RunContext(run_dir="output/run-001", config=config_dict) as ctx:
...     ctx.log_parameter("learning_rate", 0.01)
...     ctx.log_metrics({"rmse": 0.85}, step_name="validation")
...     ctx.log_artifact("model.joblib", "models/final_model.joblib")
>>>
>>> # 模型注册管理
>>> registry = ModelRegistry("model_registry.json")
>>> registry.register_model(
...     model_name="foster-nmr",
...     run_id="run-001",
...     artifact_path="models/final_model.joblib"
... )

References
----------
- 《logwp_extras_tracking设计.md》- 完整架构设计文档
- 《SCAPE_CCG_编码与通用规范》- 编码规范要求
- 《SCAPE_MS_方法说明书》- SCAPE项目科学方法定义
"""

# 核心组件导入
from .context import RunContext
from .registry import ModelRegistry
from .config import BaseRunConfig, TrackingConfig

# 异常类导入
from .exceptions import (
    TrackingError,
    RunExistsError,
    RunNotFoundError,
    ArtifactNotFoundError,
    ModelNotRegisteredError,
    RegistryError,
    ManifestError,
    ArtifactIOError,
)

# 工具函数导入（可选，供高级用户使用）
from .utils import (
    generate_run_id,
    get_current_utc_timestamp,
    calculate_file_hash,
    atomic_write_json,
    load_json_file,
)

# 公共API导出
__all__ = [
    # 核心组件
    "RunContext",
    "ModelRegistry",

    # 配置模型
    "BaseRunConfig",
    "TrackingConfig",

    # 异常类
    "TrackingError",
    "RunExistsError",
    "RunNotFoundError",
    "ArtifactNotFoundError",
    "ModelNotRegisteredError",
    "RegistryError",
    "ManifestError",
    "ArtifactIOError",

    # 工具函数
    "generate_run_id",
    "get_current_utc_timestamp",
    "calculate_file_hash",
    "atomic_write_json",
    "load_json_file",
]
