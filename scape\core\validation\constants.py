"""Constants for the validation package.

This module defines standardized, logical names for all artifacts produced
by the validation steps, following the component development framework.
"""

from __future__ import annotations

from enum import Enum


class PltPlotTypes(str, Enum):
    """定义PLT分析中不同图表类型的标准字典键。"""
    CONTRIBUTION_CROSSPLOT = "contribution_crossplot"
    CAPTURE_CURVE = "capture_curve"
    LORENZ_CURVE = "lorenz_curve"


class PltAnalysisPlotProfiles(str, Enum):
    """定义PLT分析步骤的PlotProfile注册名称。"""
    BASE = "validation.plt.base"
    CONTRIBUTION_CROSSPLOT = "validation.plt.contribution_crossplot"
    CAPTURE_CURVE = "validation.plt.capture_curve"
    LORENZ_CURVE = "validation.plt.lorenz_curve"


class PermCorrelationPlotProfiles(str, Enum):
    """定义渗透率相关性分析步骤的PlotProfile注册名称。"""
    BASE = "validation.perm_corr.base"
    PERMEABILITY_CROSSPLOT = "validation.perm_corr.permeability_crossplot"


class PltAnalysisArtifacts(str, Enum):
    """Defines logical name prefixes for artifacts from the PLT Analysis step.

    Since artifacts are generated per well, these are defined as prefixes.
    The final artifact name will be a combination of the prefix and the well name,
    e.g., f"{PltAnalysisArtifacts.CAPTURE_CURVE_PLOT_PREFIX}{well_name}".
    """
    # Step name: plt_analysis

    # Reports
    ANALYZED_LAYERS_TABLE_PREFIX = "plt_analysis.reports.analyzed_layers_"

    # Capture Curve related artifacts
    CAPTURE_CURVE_PLOT_PREFIX = "plt_analysis.plots.capture_curve_"
    CAPTURE_CURVE_PLOT_DATA_PREFIX = "plt_analysis.data_snapshots.capture_curve_"
    CAPTURE_CURVE_PLOT_PROFILE_PREFIX = "plt_analysis.configs.capture_curve_profile_"

    # Lorenz Curve related artifacts
    LORENZ_CURVE_PLOT_PREFIX = "plt_analysis.plots.lorenz_curve_"
    LORENZ_CURVE_PLOT_DATA_PREFIX = "plt_analysis.data_snapshots.lorenz_curve_"
    LORENZ_CURVE_PLOT_PROFILE_PREFIX = "plt_analysis.configs.lorenz_curve_profile_"

    # Contribution Crossplot related artifacts
    CONTRIBUTION_CROSSPLOT_PREFIX = "plt_analysis.plots.contribution_crossplot_"
    CONTRIBUTION_CROSSPLOT_DATA_PREFIX = "plt_analysis.data_snapshots.contribution_crossplot_"
    CONTRIBUTION_CROSSPLOT_PROFILE_PREFIX = "plt_analysis.configs.contribution_crossplot_profile_"


class PermCorrelationArtifacts(str, Enum):
    """Defines logical name prefixes for artifacts from the Permeability Correlation step.

    Since artifacts are generated per well, these are defined as prefixes.
    The final artifact name will be a combination of the prefix and the well name.
    """
    # Step name: perm_corr_analysis

    # Datasets
    ALIGNED_DATA_PREFIX = "perm_corr_analysis.datasets.aligned_data_"

    # Crossplot related artifacts
    CROSSPLOT_PREFIX = "perm_corr_analysis.plots.crossplot_"
    CROSSPLOT_DATA_PREFIX = "perm_corr_analysis.data_snapshots.crossplot_"
    CROSSPLOT_PROFILE_PREFIX = "perm_corr_analysis.configs.crossplot_profile_"
