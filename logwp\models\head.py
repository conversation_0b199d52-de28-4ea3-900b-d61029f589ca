from __future__ import annotations

"""logwp.models.head - 井头信息管理

WpHead管理井头信息，实现WFS v1.0规范的6种属性类别和复杂作用域查找机制。

Architecture
------------
层次/依赖: models层值对象，依赖types、constants、exceptions
设计原则: WFS规范兼容、作用域查找、属性继承机制
性能特征: 快速查找、内存优化、类型安全

WFS v1.0属性类别（基于WFS第5章）：
- V（版本）：全局唯一版本属性
- WP（工区）：工区全局属性
- DS（数据集）：数据集级别属性
- W（井）：井级别属性，支持继承逻辑
- C（曲线）：曲线级别属性，支持复杂继承
- O（其它）：其它属性，精确匹配查找

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- CT-2: 使用枚举常量
- SC-1: 算法正确性，包含WFS规范引用

References
----------
- 《SCAPE_WFS_WP文件规范.md》第5章 - 井头信息格式规范
- 《SCAPE_DDS_详细设计_logwp.md》§4.3 - WpHead设计
"""

from typing import Any, TypedDict, NotRequired
from dataclasses import dataclass, field
from enum import Enum

from logwp.models.constants import WpApiKeys, WpStandardColumn, WpExtAttributeCategory

from logwp.models.ext_attr import ExtAttributeRecord

from logwp.models.exceptions import WpAttributeError
from logwp.infra.exceptions import ErrorContext
from logwp.models.types import WpExtAttributeName, WpDatasetName, WpWellName, WpCurveName, WpDataDict, WpIdentifier
from logwp.infra import get_logger
from logwp.models.utils import CaseInsensitiveDict
from .base import WpProjectComponent


logger = get_logger(__name__)


@dataclass
class WpHead(WpProjectComponent):
    """测井项目属性管理器（格式无关的通用模型）。

    WpHead是logwp包中格式无关的测井项目属性管理器，提供统一的属性存储、
    查找和管理功能。虽然以WFS规范作为设计模板，但实现完全与数据格式解耦。

    Architecture Design Philosophy
    -----------------------------
    🎯 **格式无关原则**：
    - logwp是与数据格式无关的测井数据模型
    - WpHead不是WFS _Head_Info工作表的直接映射
    - 支持WP、LAS、JSON等多种格式的属性数据

    🏗️ **架构分层**：
    - I/O层：负责具体格式解析（wp_reader, las_reader等）
    - models层：格式无关的业务模型（WpHead等）
    - 应用层：基于通用模型的业务逻辑

    📚 **WFS规范的作用**：
    - 设计参考：WFS的6种属性类别提供分层管理思想
    - 主要格式：WP格式是主要支持格式，但不是唯一格式
    - 最佳实践：WFS的查找机制是属性管理的最佳实践

    🔧 **设计目标**：
    - 通用属性存储：支持任意格式的属性数据
    - 作用域查找：借鉴WFS分层思想，提供灵活查找
    - 类型安全：现代化类型系统和验证
    - 扩展性：支持未来新的数据格式和属性类型

    Architecture
    ------------
    层次/依赖: models层值对象，格式无关的属性管理
    设计原则: 格式解耦、通用接口、分层查找、类型安全
    性能特征: 快速查找、内存优化、缓存友好

    Attributes:
        attributes: 属性字典，键为属性名，值为属性值
        metadata: 属性元数据信息（类型、单位、描述等）

    Examples:
        >>> # 基本属性管理
        >>> head = WpHead()
        >>> head.set_attribute("project_name", "Santos_Field")
        >>> head.set_attribute("version", "1.0")
        >>>
        >>> # 作用域查找（借鉴WFS思想）
        >>> value = head.get_attribute("T2_AXIS", dataset="NMR_logs")
        >>>
        >>> # 支持多种数据格式的属性
        >>> head.set_attribute("config", {"param1": "value1"})  # JSON风格
        >>> head.set_attribute("las_header", las_header_dict)   # LAS风格

    References:
        《SCAPE_SAD_软件架构设计.md》§3 - 格式无关架构设计
        《SCAPE_WFS_WP文件规范.md》第5章 - 属性管理最佳实践参考
    """

    # 属性管理器（延迟初始化）
    _attribute_manager: Any = field(default=None, init=False, repr=False)

    @property
    def ext_attribute_manager(self):
        """获取属性管理器实例（延迟初始化）。

        Returns:
            ExtAttributeManager: 属性管理器实例

        Examples:
            >>> head = WpHead()
            >>> manager = head.ext_attribute_manager
            >>> manager.add_attribute("V", None, None, None, "Version", "STR", None, "1.0")
        """
        if self._attribute_manager is None:
            from .ext_attr.manager import ExtAttributeManager
            self._attribute_manager = ExtAttributeManager()
        return self._attribute_manager

    @property
    def attribute_records(self) -> list[ExtAttributeRecord]:
        """获取所有属性记录。

        Returns:
            list[ExtAttributeRecord]: 属性记录列表

        Examples:
            >>> head = WpHead()
            >>> head.add_attribute("V", "Version", "1.0")
            >>> records = head.attribute_records
            >>> assert len(records) == 1
        """
        return self.ext_attribute_manager.records

    def add_attribute(
        self,
        category: str,
        attribute: WpExtAttributeName,
        value: Any,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None,
        data_type: str = "STR",
        unit: str | None = None,
        description: str | None = None
    ) -> None:
        """添加扩展属性（转发到ExtAttributeManager）。

        Args:
            category: 属性类别（V、WP、DS、W、C、O）
            attribute: 属性名称
            value: 属性值
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）
            data_type: 数据类型，默认"STR"
            unit: 单位（可选）
            description: 描述信息（可选）

        Examples:
            >>> head = WpHead()
            >>> head.add_attribute("V", "Version", "1.0")
            >>> head.add_attribute("DS", "T2_AXIS", t2_config, dataset="NMR_logs")
        """
        self.ext_attribute_manager.add_attribute(
            category=category,
            dataset=dataset,
            well=well,
            curve=curve,
            attribute=attribute,
            data_type=data_type,
            unit=unit,
            value=value,
            description=description
        )

    def get_version(self) -> str | None:
        """获取版本号（转发到ExtAttributeManager）。

        Returns:
            str | None: 版本号字符串，未找到时返回None

        Examples:
            >>> head = WpHead()
            >>> head.add_attribute("V", "Version", "1.0")
            >>> version = head.get_version()
            >>> assert version == "1.0"
        """
        return self.ext_attribute_manager.get_version()

    def find_scoped_attributes(
        self,
        attribute: WpExtAttributeName | None = None,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> list:
        """查找作用域属性（转发到ExtAttributeManager）。

        Args:
            attribute: 属性名称（可选）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            list[ExtAttributeRecord]: 匹配的属性记录列表

        Examples:
            >>> records = head.find_scoped_attributes("T2_AXIS", dataset="NMR_logs")
        """
        return self.ext_attribute_manager.find_scoped_attributes(
            attribute, dataset=dataset, well=well, curve=curve
        )

    def get_scoped_attribute_value(
        self,
        attribute: WpExtAttributeName,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> Any:
        """获取作用域属性值（转发到ExtAttributeManager）。

        Args:
            attribute: 属性名称（必需）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            Any: 属性值，未找到时返回None

        Examples:
            >>> value = head.get_scoped_attribute_value("T2_AXIS", dataset="NMR_logs")
        """
        return self.ext_attribute_manager.get_scoped_attribute_value(
            attribute, dataset=dataset, well=well, curve=curve
        )


    def find_attributes_in_category(
        self,
        category: str,
        attribute: WpExtAttributeName | None = None,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> list:
        """在指定类别下精确匹配属性（转发到ExtAttributeManager）。

        Args:
            category: 属性类别（V、WP、DS、W、C、O）
            attribute: 属性名称（可选）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            list[ExtAttributeRecord]: 匹配的属性记录列表

        Examples:
            >>> records = head.find_attributes_in_category("DS", "T2_AXIS", dataset="NMR_logs")
        """
        return self.ext_attribute_manager.find_attributes_in_category(
            category, attribute, dataset=dataset, well=well, curve=curve
        )

    def get_all_category_attributes(
        self,
        category: str
    ) -> list:
        """获取指定类别的所有属性（转发到ExtAttributeManager）。

        Args:
            category: 属性类别（V、WP、DS、W、C、O）

        Returns:
            list[ExtAttributeRecord]: 指定类别的所有属性记录

        Examples:
            >>> records = head.get_all_category_attributes("DS")
        """
        return self.ext_attribute_manager.get_all_category_attributes(category)

    def find_other_attributes(
        self,
        attribute: WpExtAttributeName | None = None,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> list:
        """查找其它属性（转发到ExtAttributeManager）。

        Args:
            attribute: 属性名称（可选）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            list[ExtAttributeRecord]: 匹配的属性记录列表

        Examples:
            >>> records = head.find_other_attributes("custom_attr", dataset="test")
        """
        return self.ext_attribute_manager.find_other_attributes(
            attribute, dataset=dataset, well=well, curve=curve
        )

    def get_other_attribute_value(
        self,
        attribute: WpExtAttributeName,
        *,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None
    ) -> Any:
        """获取其它属性值（转发到ExtAttributeManager）。

        Args:
            attribute: 属性名称（必需）
            dataset: 数据集作用域（可选）
            well: 井作用域（可选）
            curve: 曲线作用域（可选）

        Returns:
            Any: 属性值，未找到时返回None

        Examples:
            >>> value = head.get_other_attribute_value("custom_attr", dataset="test")
        """
        return self.ext_attribute_manager.get_other_attribute_value(
            attribute, dataset=dataset, well=well, curve=curve
        )

    def get_predefined_ext_attr_t2_axis(
        self,
        dataset: WpDatasetName | None = None,
        well: WpWellName | None = None,
        curve: WpCurveName | None = None,
        *,
        return_json: bool = False
    ) -> Any:
        """获取T2_AXIS预定义属性（支持JSON和领域对象两种返回格式）。

        Args:
            dataset: 数据集名称（可选）
            well: 井名（可选）
            curve: 曲线名（可选）
            return_json: 是否返回原始JSON数据，默认False返回领域对象

        Returns:
            - return_json=False: T2AxisLog10 | T2AxisExp2 | None（领域对象）
            - return_json=True: dict[str, Any] | None（原始JSON数据）

        Examples:
            >>> # 获取T2轴领域对象（默认）
            >>> t2_axis = head.get_predefined_attr_t2_axis()
            >>> if t2_axis:
            ...     values = t2_axis.calculate_values()
            ...     assert len(values) == t2_axis.n_bins
            >>>
            >>> # 获取原始JSON数据
            >>> t2_json = head.get_predefined_attr_t2_axis(return_json=True)
            >>> if t2_json:
            ...     axis_type = t2_json.get("Axis_Type")
            >>>
            >>> # 特定作用域查找
            >>> t2_axis = head.get_predefined_attr_t2_axis(dataset="NMR_logs", well="WELL_001")
            >>> if isinstance(t2_axis, T2AxisLog10):
            ...     print(f"log10轴: {t2_axis.t2_start} - {t2_axis.t2_end}")
        """
        try:
            # 使用枚举常量，避免硬编码字符串（遵循CCG CT-2规范）
            from logwp.models.constants import WpPredefinedAttributes

            # 获取所有T2轴属性名变体（使用常量枚举）
            t2_attr_names = WpPredefinedAttributes.t2_axis_variants()

            # 查找T2轴JSON数据
            t2_json = None
            for attr_name in t2_attr_names:
                # 使用完整的作用域查找机制
                # 使用新的转发方法
                t2_data = self.get_scoped_attribute_value(attr_name, dataset=dataset, well=well, curve=curve)
                if t2_data is not None and isinstance(t2_data, dict):
                    t2_json = t2_data
                    break

            if t2_json is None:
                return None

            # 根据return_json参数决定返回格式
            if return_json:
                return t2_json
            else:
                # 使用工厂创建领域对象
                from .ext_attr.predefined import T2AxisProcessor

                processor = T2AxisProcessor()
                return processor.create_domain_object(t2_json)

        except Exception:
            return None

    def _get_system_defaults(self) -> dict[WpExtAttributeName, Any]:
        """获取系统默认属性（格式无关）。

        Returns:
            dict[WpAttributeName, Any]: 系统默认属性字典
        """
        return {
            WpStandardColumn.VERSION: "1.0",
            WpStandardColumn.CREATED_BY: "SCAPE_logwp",
            WpStandardColumn.FORMAT: "logwp_v1.0",
            WpStandardColumn.ENCODING: "utf-8",
            WpStandardColumn.TIMESTAMP: None  # 将在实际使用时设置
        }

    def generate_summary(self) -> dict[str, Any]:
        """生成井头信息概况。

        生成包含所有扩展属性的概况信息，用于数据了解和调试。

        Returns:
            dict[str, Any]: 井头信息概况数据
                - total_attributes: 总属性数
                - by_wfs_category: 按WFS类别分组的属性统计
                - attribute_types: 属性数据类型统计
                - attribute_records: 详细的属性记录列表

        Examples:
            >>> head = WpHead()
            >>> head.add_attribute("V", "Version", "1.0")
            >>> head.add_attribute("WP", "Field", "Santos")
            >>> summary = head.generate_summary()
            >>> print(f"总属性数: {summary['total_attributes']}")
            >>> print(f"WFS类别统计: {summary['by_wfs_category']}")
            >>> print(f"数据类型统计: {summary['attribute_types']}")
            >>> print(f"详细记录数: {len(summary['attribute_records'])}")

        References:
            《SCAPE_DDS_logwp_generate_summary.md》§4.3 - 井头信息服务设计
        """
        from logwp.models.internal.head_summary import generate_head_summary
        return generate_head_summary(self)

