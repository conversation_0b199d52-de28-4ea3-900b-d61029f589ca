"""
logwp.extras.physconsts

A package for providing reliable physical and petroleum engineering constants.
"""
from .containers import Constant
from .registry import ConstantRegistry

# Import the singleton unit registry from the units package
from logwp.extras.units import ureg

# Create a default, global instance of the ConstantRegistry.
# It is initialized with the unit registry to create Quantity objects for the constants.
pconsts = ConstantRegistry(unit_registry=ureg)

__all__ = ["Constant", "ConstantRegistry", "pconsts"]
