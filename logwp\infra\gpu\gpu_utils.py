from __future__ import annotations

from ..constants import WpGpuDefaults

from ..exceptions import GpuContext, WpGpuDeviceError, WpGpuError, WpGpuMemoryError

"""logwp.infra.gpu_utils - GPU计算环境检测和工具

实现GPU可用性检测、自动CPU回退机制和统一计算接口。

Architecture
------------
层次/依赖: logwp包工具层，GPU计算支持
设计原则: GPU优先、CPU回退、延迟导入、内存管理
性能特征: 自动检测、智能调度、内存优化、性能监控

Core Features
-------------
- **GPU可用性检测**: 检测cupy、cudf、numba等GPU库
- **自动CPU回退**: GPU不可用时自动切换到CPU计算
- **延迟导入**: 避免GPU库导入失败影响程序启动
- **内存管理**: 智能GPU内存分配和释放
- **设备信息**: 获取GPU设备详细信息

Examples
--------
>>> from logwp.infra import is_gpu_available, get_gpu_info, to_gpu
>>>
>>> # 检查GPU可用性
>>> if is_gpu_available():
...     print("GPU计算可用")
...     info = get_gpu_info()
...     print(f"GPU设备: {info['device_name']}")
>>>
>>> # 数据转换
>>> import numpy as np
>>> data = np.array([1, 2, 3, 4, 5])
>>> gpu_data = to_gpu(data)  # 自动检测并转换
"""

import sys
import warnings
from typing import Any, Dict, Optional, Union, Tuple, TYPE_CHECKING
from pathlib import Path

from scape.constants import WpEnvironmentVars
from logwp.infra.exceptions import ErrorContext

# 类型提示
if TYPE_CHECKING:
    import numpy as np
    try:
        import cupy as cp
        import cudf
        import numba.cuda as cuda
        ArrayLike = Union[np.ndarray, cp.ndarray]
        DataFrameLike = Union["pd.DataFrame", cudf.DataFrame]
    except ImportError:
        ArrayLike = "np.ndarray"
        DataFrameLike = "pd.DataFrame"

__all__ = [
    # GPU检测
    "is_gpu_available",
    "get_gpu_info",
    "detect_gpu_libraries",
    "check_gpu_capability",

    # 数据转换
    "to_gpu",
    "to_cpu",
    "auto_select_device",

    # 内存管理
    "get_gpu_memory_info",
    "optimize_gpu_memory",
    "clear_gpu_cache",

    # 设备管理
    "set_gpu_device",
    "get_current_device",
    "list_gpu_devices",
]


# 全局GPU状态缓存
_gpu_status_cache: Optional[Dict[str, Any]] = None
_gpu_libraries_cache: Optional[Dict[str, Any]] = None


def detect_gpu_libraries() -> Dict[str, Any]:
    """检测GPU库可用性。

    Returns:
        Dict[str, Any]: GPU库检测结果

    Examples:
        >>> libs = detect_gpu_libraries()
        >>> print(f"CuPy可用: {libs['cupy']['available']}")
        >>> print(f"CuDF可用: {libs['cudf']['available']}")
    """
    global _gpu_libraries_cache

    if _gpu_libraries_cache is not None:
        return _gpu_libraries_cache

    libraries = {
        "cupy": {"available": False, "version": None, "error": None},
        "cudf": {"available": False, "version": None, "error": None},
        "numba": {"available": False, "version": None, "error": None},
    }

    # 检测CuPy
    try:
        import cupy as cp
        libraries["cupy"]["available"] = True
        libraries["cupy"]["version"] = cp.__version__

        # 测试基本GPU操作
        test_array = cp.array([1.0, 2.0, 3.0])
        _ = cp.sum(test_array)
        del test_array

    except ImportError as e:
        libraries["cupy"]["error"] = f"导入失败: {e}"
    except Exception as e:
        libraries["cupy"]["error"] = f"GPU操作失败: {e}"
        libraries["cupy"]["available"] = False

    # 检测CuDF
    try:
        import cudf
        libraries["cudf"]["available"] = True
        libraries["cudf"]["version"] = cudf.__version__

        # 测试基本DataFrame操作
        test_df = cudf.DataFrame({"a": [1, 2, 3]})
        _ = test_df.sum()
        del test_df

    except ImportError as e:
        libraries["cudf"]["error"] = f"导入失败: {e}"
    except Exception as e:
        libraries["cudf"]["error"] = f"DataFrame操作失败: {e}"
        libraries["cudf"]["available"] = False

    # 检测Numba CUDA
    try:
        import numba
        import numba.cuda as cuda
        libraries["numba"]["available"] = cuda.is_available()
        libraries["numba"]["version"] = numba.__version__

        if not cuda.is_available():
            libraries["numba"]["error"] = "CUDA不可用"

    except ImportError as e:
        libraries["numba"]["error"] = f"导入失败: {e}"
    except Exception as e:
        libraries["numba"]["error"] = f"CUDA检测失败: {e}"

    _gpu_libraries_cache = libraries
    return libraries


def is_gpu_available() -> bool:
    """检查GPU是否可用。

    Returns:
        bool: GPU是否可用

    Examples:
        >>> if is_gpu_available():
        ...     print("可以使用GPU加速")
        ... else:
        ...     print("将使用CPU计算")
    """
    try:
        libraries = detect_gpu_libraries()

        # 至少需要CuPy可用
        cupy_available = libraries["cupy"]["available"]

        if not cupy_available:
            return False

        # 检查GPU设备
        import cupy as cp
        device_count = cp.cuda.runtime.getDeviceCount()

        return device_count > 0

    except Exception:
        return False


def get_gpu_info() -> Dict[str, Any]:
    """获取GPU设备信息。

    Returns:
        Dict[str, Any]: GPU设备详细信息

    Raises:
        WpGpuError: GPU不可用或信息获取失败

    Examples:
        >>> info = get_gpu_info()
        >>> print(f"设备数量: {info['device_count']}")
        >>> print(f"当前设备: {info['current_device']}")
    """
    global _gpu_status_cache

    if _gpu_status_cache is not None:
        return _gpu_status_cache

    if not is_gpu_available():
        raise WpGpuError(
            "GPU不可用",
            context=ErrorContext(
                operation="get_gpu_info",
                gpu_libraries=detect_gpu_libraries()
            )
        )

    try:
        import cupy as cp

        device_count = cp.cuda.runtime.getDeviceCount()
        current_device = cp.cuda.runtime.getDevice()

        # 获取当前设备详细信息
        device_props = cp.cuda.runtime.getDeviceProperties(current_device)
        memory_info = cp.cuda.runtime.memGetInfo()

        gpu_info = {
            "available": True,
            "device_count": device_count,
            "current_device": current_device,
            "device_name": device_props["name"].decode(),
            "compute_capability": f"{device_props['major']}.{device_props['minor']}",
            "memory_total_mb": round(memory_info[1] / 1024 / 1024, 2),
            "memory_free_mb": round(memory_info[0] / 1024 / 1024, 2),
            "memory_used_mb": round((memory_info[1] - memory_info[0]) / 1024 / 1024, 2),
            "cuda_version": cp.cuda.runtime.runtimeGetVersion(),
            "driver_version": cp.cuda.runtime.driverGetVersion(),
            "libraries": detect_gpu_libraries(),
        }

        # 添加所有设备信息
        devices = []
        for i in range(device_count):
            props = cp.cuda.runtime.getDeviceProperties(i)
            devices.append({
                "id": i,
                "name": props["name"].decode(),
                "compute_capability": f"{props['major']}.{props['minor']}",
                "memory_total_mb": round(props["totalGlobalMem"] / 1024 / 1024, 2),
                "multiprocessor_count": props["multiProcessorCount"],
            })

        gpu_info["devices"] = devices

        _gpu_status_cache = gpu_info
        return gpu_info

    except Exception as e:
        raise WpGpuError(
            f"获取GPU信息失败: {e}",
            context=ErrorContext(
                operation="get_gpu_info",
                additional_info={"error_type": type(e).__name__}
            )
        ) from e


def check_gpu_capability(min_compute_capability: str = "6.0") -> bool:
    """检查GPU计算能力是否满足要求。

    Args:
        min_compute_capability: 最低计算能力要求

    Returns:
        bool: 是否满足计算能力要求

    Examples:
        >>> if check_gpu_capability("7.0"):
        ...     print("GPU支持Tensor Core")
    """
    if not is_gpu_available():
        return False

    try:
        info = get_gpu_info()
        current_capability = info["compute_capability"]

        # 比较版本号
        current_major, current_minor = map(int, current_capability.split("."))
        min_major, min_minor = map(int, min_compute_capability.split("."))

        return (current_major > min_major or
                (current_major == min_major and current_minor >= min_minor))

    except Exception:
        return False


def to_gpu(data: Any, device_id: Optional[int] = None) -> Any:
    """将数据转换到GPU。

    Args:
        data: 要转换的数据（numpy数组、pandas DataFrame等）
        device_id: 目标GPU设备ID

    Returns:
        Any: GPU上的数据，如果GPU不可用则返回原数据

    Examples:
        >>> import numpy as np
        >>> cpu_array = np.array([1, 2, 3, 4, 5])
        >>> gpu_array = to_gpu(cpu_array)
    """
    if not is_gpu_available():
        return data

    try:
        import cupy as cp
        import pandas as pd

        # 设置设备
        if device_id is not None:
            with cp.cuda.Device(device_id):
                return _convert_to_gpu(data, cp, pd)
        else:
            return _convert_to_gpu(data, cp, pd)

    except Exception:
        # GPU转换失败，返回原数据
        return data


def _convert_to_gpu(data: Any, cp: Any, pd: Any) -> Any:
    """内部GPU转换函数。"""
    if hasattr(data, "__array__"):  # numpy数组
        return cp.asarray(data)
    elif isinstance(data, pd.DataFrame):  # pandas DataFrame
        try:
            import cudf
            return cudf.from_pandas(data)
        except ImportError:
            return data
    elif isinstance(data, (list, tuple)):  # 列表或元组
        return cp.array(data)
    else:
        return data


def to_cpu(data: Any) -> Any:
    """将数据转换到CPU。

    Args:
        data: 要转换的数据（GPU数组、GPU DataFrame等）

    Returns:
        Any: CPU上的数据

    Examples:
        >>> cpu_array = to_cpu(gpu_array)
    """
    try:
        # 检查是否是CuPy数组
        if hasattr(data, "__array_interface__") and hasattr(data, "get"):
            return data.get()  # CuPy数组转numpy

        # 检查是否是CuDF DataFrame
        elif hasattr(data, "to_pandas"):
            return data.to_pandas()  # CuDF转pandas

        else:
            return data  # 已经是CPU数据

    except Exception:
        return data


def auto_select_device(data_size_mb: float, prefer_gpu: bool = True) -> str:
    """自动选择计算设备。

    Args:
        data_size_mb: 数据大小(MB)
        prefer_gpu: 是否优先使用GPU

    Returns:
        str: 选择的设备 ("gpu" 或 "cpu")

    Examples:
        >>> device = auto_select_device(data_size_mb=100.5)
        >>> print(f"选择设备: {device}")
    """
    if not prefer_gpu or not is_gpu_available():
        return "cpu"

    try:
        gpu_info = get_gpu_info()
        available_memory_mb = gpu_info["memory_free_mb"]

        # 预留一些内存空间
        memory_threshold = available_memory_mb * 0.8

        if data_size_mb <= memory_threshold:
            return "gpu"
        else:
            return "cpu"

    except Exception:
        return "cpu"


def get_gpu_memory_info() -> Dict[str, float]:
    """获取GPU内存使用信息。

    Returns:
        Dict[str, float]: GPU内存信息(MB)

    Raises:
        WpGpuError: GPU不可用

    Examples:
        >>> memory = get_gpu_memory_info()
        >>> print(f"已用内存: {memory['used_mb']:.1f} MB")
        >>> print(f"可用内存: {memory['free_mb']:.1f} MB")
    """
    if not is_gpu_available():
        raise WpGpuError(
            "GPU不可用",
            context=ErrorContext(operation="get_gpu_memory_info")
        )

    try:
        import cupy as cp
        memory_info = cp.cuda.runtime.memGetInfo()

        return {
            "free_mb": round(memory_info[0] / 1024 / 1024, 2),
            "total_mb": round(memory_info[1] / 1024 / 1024, 2),
            "used_mb": round((memory_info[1] - memory_info[0]) / 1024 / 1024, 2),
            "usage_percent": round((memory_info[1] - memory_info[0]) / memory_info[1] * 100, 2),
        }

    except Exception as e:
        raise WpGpuMemoryError(
            f"获取GPU内存信息失败: {e}",
            context=ErrorContext(operation="get_gpu_memory_info"),
            gpu_context=GpuContext(operation="memory_info_query")
        ) from e


def optimize_gpu_memory() -> bool:
    """优化GPU内存使用。

    Returns:
        bool: 优化是否成功

    Examples:
        >>> if optimize_gpu_memory():
        ...     print("GPU内存优化成功")
    """
    if not is_gpu_available():
        return False

    try:
        import cupy as cp

        # 清理内存池
        memory_pool = cp.get_default_memory_pool()
        memory_pool.free_all_blocks()

        # 清理固定内存池
        pinned_memory_pool = cp.get_default_pinned_memory_pool()
        pinned_memory_pool.free_all_blocks()

        return True

    except Exception:
        return False


def clear_gpu_cache() -> bool:
    """清理GPU缓存。

    Returns:
        bool: 清理是否成功

    Examples:
        >>> clear_gpu_cache()
        True
    """
    return optimize_gpu_memory()


def set_gpu_device(device_id: int) -> bool:
    """设置当前GPU设备。

    Args:
        device_id: GPU设备ID

    Returns:
        bool: 设置是否成功

    Raises:
        WpGpuDeviceError: 设备设置失败

    Examples:
        >>> set_gpu_device(0)  # 使用第一个GPU
        True
    """
    if not is_gpu_available():
        raise WpGpuDeviceError(
            "GPU不可用",
            context=ErrorContext(
                operation="set_gpu_device",
                device_id=device_id
            )
        )

    try:
        import cupy as cp

        device_count = cp.cuda.runtime.getDeviceCount()
        if device_id >= device_count:
            raise WpGpuDeviceError(
                f"设备ID {device_id} 超出范围，可用设备: 0-{device_count-1}",
                context=ErrorContext(
                    operation="set_gpu_device",
                    device_id=device_id,
                    available_devices=device_count
                )
            )

        cp.cuda.runtime.setDevice(device_id)
        return True

    except Exception as e:
        raise WpGpuDeviceError(
            f"设置GPU设备失败: {e}",
            context=ErrorContext(
                operation="set_gpu_device",
                device_id=device_id
            )
        ) from e


def get_current_device() -> int:
    """获取当前GPU设备ID。

    Returns:
        int: 当前设备ID

    Raises:
        WpGpuError: GPU不可用

    Examples:
        >>> device_id = get_current_device()
        >>> print(f"当前使用GPU设备: {device_id}")
    """
    if not is_gpu_available():
        raise WpGpuError(
            "GPU不可用",
            context=ErrorContext(operation="get_current_device")
        )

    try:
        import cupy as cp
        return cp.cuda.runtime.getDevice()

    except Exception as e:
        raise WpGpuError(
            f"获取当前设备失败: {e}",
            context=ErrorContext(operation="get_current_device")
        ) from e


def list_gpu_devices() -> list[Dict[str, Any]]:
    """列出所有GPU设备。

    Returns:
        list[Dict[str, Any]]: GPU设备列表

    Examples:
        >>> devices = list_gpu_devices()
        >>> for device in devices:
        ...     print(f"设备{device['id']}: {device['name']}")
    """
    if not is_gpu_available():
        return []

    try:
        gpu_info = get_gpu_info()
        return gpu_info.get("devices", [])

    except Exception:
        return []
