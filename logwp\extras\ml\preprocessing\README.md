# Data Preprocessing & Feature Engineering (`logwp.extras.ml.preprocessing`)

一个提供用于测井数据预处理和特征工程的工具包。

这个包的核心设计理念是提供与 `scikit-learn` 类似、但又专门为测井数据（特别是 `WpDataFrameBundle` 对象）优化的工具。它解决了在处理多井数据时常见的挑战，如按井归一化、按井填充缺失值等。

## 核心特性

- **Scikit-learn 风格 API**: 预处理器（如 `WellwiseScaler`, `WellwiseImputer`）遵循 `fit` 和 `transform` 的设计模式，易于集成到现有的机器学习工作流中。
- **按井处理 (Well-wise)**: 核心预处理器能够按井学习参数和进行转换，这对于消除井间系统性差异、保留井内相对特征至关重要。
- **与 `logwp.models` 无缝集成**: 所有工具都直接操作 `WpDataFrameBundle` 对象，充分利用其丰富的元数据。
- **特征工程**: 提供测井领域特有的特征工程函数，如深度对齐。

## 可用工具

### 1. `WellwiseScaler`

按井对指定的测井曲线进行数据缩放（标准化或归一化）。

**使用场景**: 当不同井的测井曲线存在基线漂移或量纲差异时，使用按井缩放可以有效地将它们置于可比较的尺度上，同时保留每口井内部的形态特征。

```python
from logwp.testing.builders import DatasetBuilder
from logwp.extras.preprocessing import WellwiseScaler

# 1. 创建一个包含两口井的测试数据集，W2的GR值系统性偏高
dataset = DatasetBuilder.quick_continuous_dataset(
    name="multi_well_logs",
    curves={
        "GR": [10, 20, 30] + [110, 120, 130],
        "PHIT": [0.1, 0.2, 0.3] * 2
    },
    well_names=["W1"]*3 + ["W2"]*3
)
bundle = dataset.extract_curve_dataframe_bundle(curve_names=["GR", "PHIT"])

# 2. 初始化并拟合 WellwiseScaler
scaler = WellwiseScaler(curves=["GR"], method='standard')
scaler.fit(bundle)

# 3. 转换数据
scaled_bundle = scaler.transform(bundle)

# 4. 查看结果
#    可以看到，尽管原始GR值差异巨大，但按井标准化后，它们的分布变得一致。
print(scaled_bundle.data)
```

### 2. `WellwiseImputer`

按井使用不同的策略（如均值、中位数）填充缺失值。

**使用场景**: 当测井数据存在缺失时，使用每口井自身的统计特征来填充，比使用全局统计值更合理、更精确。

```python
import numpy as np
from logwp.testing.builders import DatasetBuilder
from logwp.extras.preprocessing import WellwiseImputer

# 1. 创建一个包含缺失值的测试数据集
dataset = DatasetBuilder.quick_continuous_dataset(
    name="multi_well_missing",
    curves={
        "GR": [10, 20, np.nan] + [110, np.nan, 130],
        "PHIT": [0.1, 0.2, 0.3] * 2
    },
    well_names=["W1"]*3 + ["W2"]*3
)
bundle = dataset.extract_curve_dataframe_bundle(curve_names=["GR", "PHIT"])

# 2. 初始化并拟合 WellwiseImputer
imputer = WellwiseImputer(curves=["GR"], strategy='mean')
imputer.fit(bundle)

# 3. 转换数据
imputed_bundle = imputer.transform(bundle)

# 4. 查看结果
#    W1的NaN被其均值(15.0)填充，W2的NaN被其均值(120.0)填充。
print(imputed_bundle.data)
```

### 3. `depth_aligner`

使用互相关算法，自动计算并校正两条曲线之间的深度偏移。

**使用场景**: 在进行多曲线计算或对比分析前，确保所有曲线在深度上是对齐的。例如，对齐不同测量趟次的GR曲线。

```python
import numpy as np
import pandas as pd
from logwp.extras.preprocessing import depth_aligner

# 1. 创建两条有深度偏移的模拟曲线
depth = np.arange(1000, 1100, 0.5)
ref_signal = np.sin(depth / 10) + np.random.rand(len(depth)) * 0.1
tar_signal = np.sin((depth - 2.5) / 10) # 目标曲线向下偏移了 2.5m

reference_series = pd.Series(ref_signal, index=depth, name="GR_ref")
target_series = pd.Series(tar_signal, index=depth, name="GR_tar")

# 2. 调用对齐函数
aligned_series, shift, corr = depth_aligner(
    reference_series=reference_series,
    target_series=target_series,
    max_shift=5.0 # 在 +/- 5m 范围内搜索
)

# 3. 查看结果
print(f"计算出的最佳深度偏移量: {shift:.2f} m")
print(f"对齐后的最大相关系数: {corr:.4f}")
```
