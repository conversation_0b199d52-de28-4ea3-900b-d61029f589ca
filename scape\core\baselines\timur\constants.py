"""scape.core.baselines.timur.constants - <PERSON><PERSON>/Coates基准模型常量"""

from enum import Enum


class TimurArtifacts(str, Enum):
    """定义Timur/Coates基准模型步骤的产物逻辑名称。

    遵循框架的 `step_name.<category>.<specific_name>` 命名规范。
    """
    # 训练步骤 (timur_baseline_training) 的产物
    MODEL_ASSETS = "timur_baseline_training.models.timur_assets"
    TRAINING_CONFIG_SNAPSHOT = "timur_baseline_training.configs.training_config_snapshot"

    # 预测步骤 (timur_baseline_prediction) 的产物
    PREDICTED_DATA = "timur_baseline_prediction.datasets.timur_predictions"
    PREDICTION_CONFIG_SNAPSHOT = "timur_baseline_prediction.configs.prediction_config_snapshot"
