"""测井项目数据概况生成服务。

提供WpWellProject数据概况报告生成的核心服务实现。
支持结构化数据收集和多格式输出（JSON、Markdown）。

Architecture
------------
层次/依赖: models/internal服务层，无状态服务函数
设计原则: 数据与展示分离、Service Layer模式、模板化输出
性能特征: 内存优化、批量处理、错误容错、格式灵活

Examples:
    >>> # 生成Markdown报告
    >>> report_path = generate_project_summary_report(project, "report.md", "markdown")
    >>>
    >>> # 生成JSON数据
    >>> json_path = generate_project_summary_report(project, "data.json", "json")
    >>>
    >>> # 仅收集结构化数据
    >>> data = collect_structured_summary(project)

References:
    《SCAPE_DDS_logwp_generate_summary.md》§4.2 - 核心服务层设计
"""

from __future__ import annotations

import json
import os
from datetime import datetime
from typing import TYPE_CHECKING, Any, Literal

import numpy as np

if TYPE_CHECKING:
    from logwp.models.well_project import WpWellProject

from logwp.infra import get_logger
from .summary_constants import SummaryKeys, ReportTemplates, LogMessages

logger = get_logger(__name__)


def generate_project_summary_report(
    project: WpWellProject,
    output_path: str | None = None,
    format: Literal["json", "markdown"] = "markdown",
    template: str = "default"
) -> str:
    """生成项目数据概况报告（无状态服务函数）。

    Args:
        project: WpWellProject实例
        output_path: 输出文件路径，None表示自动命名
        format: 输出格式，"json"为结构化数据，"markdown"为人类可读报告
        template: 模板名称，仅在format="markdown"时有效

    Returns:
        str: 生成的报告文件路径

    Raises:
        OSError: 文件写入失败
        ValueError: 不支持的格式
        ImportError: 模板系统未安装（仅在使用模板时）

    Examples:
        >>> # 生成默认Markdown报告
        >>> report_path = generate_project_summary_report(project, format="markdown")
        >>>
        >>> # 生成英文Markdown报告
        >>> report_path = generate_project_summary_report(project, format="markdown", template="english")
        >>>
        >>> # 生成JSON数据
        >>> json_path = generate_project_summary_report(project, format="json")
    """
    logger.info(LogMessages.MSG_SUMMARY_START,
               project_name=str(project.name),
               format=format)

    try:
        # 1. 收集结构化数据
        structured_data = collect_structured_summary(project)

        # 2. 根据格式生成内容
        if format == "json":
            content = _format_as_json(structured_data)
            default_ext = ".json"
        elif format == "markdown":
            content = _format_as_markdown(structured_data, template)
            default_ext = ReportTemplates.MARKDOWN_EXT
        else:
            raise ValueError(f"不支持的格式: {format}")

        # 3. 确定输出路径
        if output_path is None:
            output_path = f"{project.name}{ReportTemplates.DEFAULT_SUFFIX}{default_ext}"

        if content:
            # 4. 保存文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)

        logger.info(LogMessages.MSG_SUMMARY_COMPLETE,
                   output_path=output_path,
                   format=format,
                   file_size=len(content))

        return output_path

    except Exception as e:
        logger.error(LogMessages.MSG_SUMMARY_FAILED,
                    project_name=str(project.name),
                    format=format,
                    error=str(e))
        raise


def collect_structured_summary(project: WpWellProject) -> dict[str, Any]:
    """收集项目的纯结构化概况数据，无任何展示文本。

    Args:
        project: WpWellProject实例

    Returns:
        dict: 结构化概况数据，包含：
            - project_info: 项目基本信息
            - head_attributes: 井头属性信息
            - well_mappings: 井名映射信息
            - datasets: 数据集信息
            - generation_info: 生成信息

    Examples:
        >>> data = collect_structured_summary(project)
        >>> print(f"项目包含 {data['project_info']['dataset_count']} 个数据集")
        >>>
        >>> # 保存为JSON
        >>> with open("summary.json", "w") as f:
        ...     json.dump(data, f, ensure_ascii=False, indent=2)
    """
    logger.debug("开始收集结构化概况数据", project_name=str(project.name))

    # 收集项目基本信息
    project_info = {
        SummaryKeys.NAME: str(project.name),
        "project_id": str(project.project_id),
        "created_at": project.created_at.isoformat(),
        "modified_at": project.modified_at.isoformat(),
        "dataset_count": len(project.datasets),
        "default_depth_unit": str(project.default_depth_reference_unit)
    }

    # 收集各组件数据
    head_attributes = project.head.generate_summary()
    well_mappings = project.well_map.generate_summary()
    datasets = _collect_datasets_summary(project)

    # 调试日志：检查head_attributes中的attribute_records
    logger.debug("井头属性数据收集完成",
                total_attributes=head_attributes.get("total_attributes", 0),
                has_attribute_records="attribute_records" in head_attributes,
                attribute_records_count=len(head_attributes.get("attribute_records", [])))

    # 生成信息
    generation_info = {
        "generated_at": datetime.now().isoformat(),
        "generator": "SCAPE_logwp_summary_service",
        "version": "2.0"
    }

    structured_data = {
        "project_info": project_info,
        "head_attributes": head_attributes,
        "well_mappings": well_mappings,
        "datasets": datasets,
        "generation_info": generation_info
    }

    logger.debug("结构化概况数据收集完成",
                project_name=str(project.name),
                datasets_count=len(datasets.get(SummaryKeys.DATASETS, {})))

    return structured_data


def _collect_datasets_summary(project: WpWellProject) -> dict[str, Any]:
    """收集所有数据集的概况信息。

    Args:
        project: WpWellProject实例

    Returns:
        dict: 数据集概况汇总
    """
    datasets_summary = {
        SummaryKeys.TOTAL_COUNT: len(project.datasets),
        SummaryKeys.DATASETS: {}
    }

    for dataset_name, dataset in project.datasets.items():
        try:
            datasets_summary[SummaryKeys.DATASETS][dataset_name] = dataset.generate_summary()
        except Exception as e:
            logger.warning(LogMessages.MSG_DATASET_SUMMARY_FAILED,
                          dataset_name=dataset_name,
                          error=str(e))
            datasets_summary[SummaryKeys.DATASETS][dataset_name] = {
                SummaryKeys.ERROR: f"{LogMessages.ERROR_SUMMARY_FAILED}: {str(e)}"
            }

    return datasets_summary


def _format_as_json(structured_data: dict[str, Any]) -> str:
    """将结构化数据格式化为JSON字符串。

    Args:
        structured_data: 结构化概况数据

    Returns:
        str: JSON格式的字符串
    """
    # 预处理数据，确保所有字典键都是JSON兼容的类型
    processed_data = _convert_dict_keys_to_json_compatible(structured_data)
    # 使用自定义编码器处理numpy数据类型
    return json.dumps(processed_data, ensure_ascii=False, indent=2, cls=NumpyEncoder)


def _convert_dict_keys_to_json_compatible(data: Any) -> Any:
    """递归转换数据结构中的字典键为JSON兼容类型。

    Args:
        data: 要转换的数据

    Returns:
        Any: 转换后的数据，所有字典键都是JSON兼容类型
    """
    if isinstance(data, dict):
        # 转换字典的键和值
        converted_dict = {}
        for key, value in data.items():
            # 转换键为JSON兼容类型
            if isinstance(key, np.integer):
                json_key = int(key)
            elif isinstance(key, np.floating):
                json_key = float(key)
            elif isinstance(key, (np.str_, np.bytes_)):
                json_key = str(key)
            else:
                json_key = key

            # 递归转换值
            converted_dict[json_key] = _convert_dict_keys_to_json_compatible(value)
        return converted_dict
    elif isinstance(data, (list, tuple)):
        # 递归转换列表/元组中的元素
        return [_convert_dict_keys_to_json_compatible(item) for item in data]
    else:
        # 其他类型直接返回
        return data


class NumpyEncoder(json.JSONEncoder):
    """处理numpy数据类型的JSON编码器。"""

    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'isoformat'):  # datetime对象
            return obj.isoformat()
        return super().default(obj)


def _format_as_markdown(structured_data: dict[str, Any], template: str = "default") -> str:
    """将结构化数据格式化为Markdown报告。

    Args:
        structured_data: 结构化概况数据
        template: 模板名称

    Returns:
        str: Markdown格式的报告内容

    Raises:
        ImportError: 模板系统未安装
        FileNotFoundError: 模板文件不存在
    """
    try:
        # 尝试使用模板系统
        from .template_renderer import get_default_renderer
        renderer = get_default_renderer()
        return renderer.render_markdown(structured_data, template)

    except ImportError:
        # 回退到内置模板
        logger.warning("模板系统未安装，使用内置模板", template=template)
        return None




