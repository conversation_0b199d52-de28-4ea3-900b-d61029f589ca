"""scape.core.obmiq.internal.model_builder - OBMIQ PyTorch模型构建器

本模块负责构建OBMIQ混合输入深度学习模型及其相关的自定义损失函数。

Architecture
------------
层次/依赖: scape/core/obmiq/internal层，被tuning和training规程调用
设计原则: 遵循方法说明书，将模型架构与训练逻辑解耦

Classes:
    AdaptiveLossModule: 自适应损失加权模块
    OBMIQPyTorchModel: 混合输入模型 (CNN + MLP)

References:
    - 《SCAPE_方法说明书_V5_OBMIQ建模_Pytorch版.md》§2.4, §2.8
"""
from __future__ import annotations

from typing import Any, Dict

import torch
import torch.nn as nn


class AdaptiveLossModule(nn.Module):
    """自适应损失加权模块。

    该模块实现了一个可学习的损失函数，用于平衡多任务回归问题中不同
    目标的损失贡献。它基于论文 "Multi-Task Learning Using Uncertainty to
    Weigh Losses for Scene Geometry and Semantics"。

    Attributes:
        log_vars (nn.Parameter): 两个可学习的对数方差参数，用于加权。
    """

    def __init__(self, num_tasks: int = 2):
        """初始化自适应损失模块。

        Args:
            num_tasks: 预测任务的数量，默认为2。
        """
        super().__init__()
        # 初始化两个可学习的对数方差参数，初始值为0意味着初始权重相等
        self.log_vars = nn.Parameter(torch.zeros(num_tasks))

    def forward(self, y_pred: torch.Tensor, y_true: torch.Tensor) -> torch.Tensor:
        """计算加权损失。

        公式: Loss = Σ (exp(-log_var) * MSE + log_var)

        Args:
            y_pred: 模型的预测值张量，形状 (batch_size, num_tasks)。
            y_true: 真实目标值张量，形状 (batch_size, num_tasks)。

        Returns:
            一个标量张量，代表该批次的平均加权损失。
        """
        mse_loss = (y_pred - y_true) ** 2
        precision = torch.exp(-self.log_vars)
        weighted_loss = torch.sum(precision * mse_loss + self.log_vars, dim=-1)
        return torch.mean(weighted_loss)


class OBMIQPyTorchModel(nn.Module):
    """OBMIQ混合输入深度学习模型 (PyTorch版)。

    该模型包含两个分支：
    1. 一个1D-CNN分支，用于处理序列数据（如T2谱累积曲线）。
    2. 一个MLP分支，用于处理表格化数据（如常规测井曲线）。
    两个分支的输出被融合后，通过一个回归头进行最终预测。
    """

    def __init__(self, hp: Dict[str, Any], data_shapes: Dict[str, int]):
        """初始化OBMIQ模型。

        Args:
            hp: 包含模型超参数的字典，由Optuna提供。
            data_shapes: 包含数据维度信息的字典，如输入特征数和目标数。
        """
        super().__init__()

        # --- 类型转换以增强稳健性 ---
        # 确保从配置中获取的超参数是正确的整数类型，以防止因上游
        # 数据处理（如Pandas聚合）导致的类型变化（例如，int变为float）。
        cnn_filters = int(hp["cnn_filters"])
        cnn_kernel_size = int(hp["cnn_kernel_size"])
        # 2024-07-01: 调整为两层MLP，分别读取两层的超参数
        mlp_units_1 = int(hp["mlp_units_1"])
        mlp_units_2 = int(hp["mlp_units_2"])
        dropout_rate = float(hp["dropout_rate"])

        # CNN 分支
        self.cnn_branch = nn.Sequential(
            nn.Conv1d(
                in_channels=1,
                out_channels=cnn_filters,
                kernel_size=cnn_kernel_size,
            ),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(cnn_filters, 16),
            nn.ReLU(),
        )

        # 2024-07-01: 升级MLP分支为更强大的两层结构
        # 新架构包含批标准化(BatchNorm1d)和Dropout，以学习更复杂的特征交互并防止过拟合。
        # - BatchNorm1d: 在线性变换后、激活函数前使用，用于稳定和加速训练。
        # - Dropout: 在激活函数后使用，作为一种强大的正则化手段。
        self.mlp_branch = nn.Sequential(
            # 第一个隐藏层
            nn.Linear(data_shapes["num_tabular_features"], mlp_units_1),
            nn.BatchNorm1d(mlp_units_1),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            # 第二个隐藏层 (其输出将用于融合)
            nn.Linear(mlp_units_1, mlp_units_2),
            nn.BatchNorm1d(mlp_units_2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
        )

        # 回归头
        # 2024-07-01: 更新融合维度以匹配新的MLP分支输出
        # cnn_output_dim (固定为16) + mlp_output_dim (现在是 mlp_units_2)
        fusion_dim = 16 + mlp_units_2
        self.regression_head = nn.Sequential(
            nn.BatchNorm1d(fusion_dim),
            nn.Dropout(dropout_rate),
            nn.Linear(fusion_dim, 16),
            nn.ReLU(),
            nn.Linear(16, data_shapes["num_targets"]),  # 最终输出层
        )

    def forward(self, x: Dict[str, torch.Tensor]) -> torch.Tensor:
        """定义模型的前向传播路径。"""
        sequence_input = x["sequence_input"]
        tabular_input = x["tabular_input"]

        cnn_out = self.cnn_branch(sequence_input)
        mlp_out = self.mlp_branch(tabular_input)

        combined = torch.cat([cnn_out, mlp_out], dim=1)
        prediction = self.regression_head(combined)

        return prediction
