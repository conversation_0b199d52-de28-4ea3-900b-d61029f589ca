from __future__ import annotations

"""logwp.attr.predefined.t2_axis_domain - T2_Axis领域对象

T2_Axis属性的领域对象定义，提供强类型的业务对象供scape层使用。

Architecture
------------
层次/依赖: attr层领域对象，依赖numpy、dataclasses
设计原则: 领域驱动设计、强类型、业务友好
性能特征: 高效计算、数值稳定性、内存优化

领域对象设计：
- T2AxisLog10：log10类型的T2轴领域对象
- T2AxisExp2：exp2类型的T2轴领域对象

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- SC-1: 算法正确性，包含数学公式和参考文献

References
----------
- 《SCAPE_MS_方法说明书.md》§1.3 - T2谱相关参数
- 《SCAPE_WFS_WP文件规范.md》§5.4.3 - T2_AXIS属性规范
"""

from typing import Any
from dataclasses import dataclass
import numpy as np

from logwp.models.exceptions import WpCompValidationError
from logwp.infra.exceptions import ErrorContext


@dataclass(frozen=True)
class T2AxisLog10:
    """T2轴log10类型的领域对象。

    实现log10类型T2轴的业务逻辑，提供强类型的接口供scape层使用。

    Architecture
    ------------
    层次/依赖: attr层领域对象
    设计原则: 不可变对象、业务友好、数学正确性
    性能特征: 向量化计算、数值稳定性

    数学公式（符合WFS规范）：
    ```
    T2[i] = T2_Start × 10^((i-1) × log10(T2_End/T2_Start)/(N-1))
    其中 i = 1, 2, ..., N （WFS使用1-based索引）
    ```

    Attributes:
        t2_start: T2轴起始值（ms）
        t2_end: T2轴结束值（ms）
        n: T2轴点数
        unit: 单位（默认ms）

    Examples:
        >>> # 从JSON数据创建
        >>> json_data = {
        ...     "Axis_Type": "log10",
        ...     "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"},
        ...     "T2_End": {"v": 10000, "u": "ms", "t": "FLOAT"},
        ...     "N": {"v": 64, "t": "INT"}
        ... }
        >>> t2_axis = T2AxisLog10.from_json(json_data)
        >>>
        >>> # 计算T2轴值
        >>> t2_values = t2_axis.calculate_values()
        >>> assert len(t2_values) == 64
        >>> assert t2_values[0] == 0.1
        >>> assert t2_values[-1] == 10000

    References:
        《SCAPE_WFS_WP文件规范.md》§5.4.3 - T2_AXIS属性规范
    """

    t2_start: float
    """T2轴起始值（ms）"""

    t2_end: float
    """T2轴结束值（ms）"""

    n: int
    """T2轴点数"""

    unit: str = "ms"
    """单位（默认ms）"""

    def __post_init__(self) -> None:
        """验证参数有效性。

        Raises:
            WpCompValidationError: 参数无效
        """
        if self.t2_start <= 0:
            raise WpCompValidationError(
                f"T2起始值必须大于0，当前值: {self.t2_start}",
                context=ErrorContext(
                    operation="T2AxisLog10.__post_init__",
                    stage="parameter_validation",
                    details={"t2_start": self.t2_start}
                )
            )

        if self.t2_end <= 0:
            raise WpCompValidationError(
                f"T2结束值必须大于0，当前值: {self.t2_end}",
                context=ErrorContext(
                    operation="T2AxisLog10.__post_init__",
                    stage="parameter_validation",
                    details={"t2_end": self.t2_end}
                )
            )

        if self.t2_end <= self.t2_start:
            raise WpCompValidationError(
                f"T2结束值必须大于起始值，起始值: {self.t2_start}，结束值: {self.t2_end}",
                context=ErrorContext(
                    operation="T2AxisLog10.__post_init__",
                    stage="parameter_validation",
                    details={"t2_start": self.t2_start, "t2_end": self.t2_end}
                )
            )

        if self.n < 2:
            raise WpCompValidationError(
                f"T2轴点数必须至少为2，当前值: {self.n}",
                context=ErrorContext(
                    operation="T2AxisLog10.__post_init__",
                    stage="parameter_validation",
                    details={"n": self.n}
                )
            )

    @classmethod
    def from_json(cls, json_data: dict[str, Any]) -> T2AxisLog10:
        """从JSON数据创建T2AxisLog10对象。

        Args:
            json_data: T2_AXIS的JSON数据

        Returns:
            T2AxisLog10: T2轴对象

        Raises:
            WpCompValidationError: JSON数据无效

        Examples:
            >>> json_data = {
            ...     "Axis_Type": "log10",
            ...     "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"},
            ...     "T2_End": {"v": 10000, "u": "ms", "t": "FLOAT"},
            ...     "N": {"v": 64, "t": "INT"}
            ... }
            >>> t2_axis = T2AxisLog10.from_json(json_data)
        """
        try:
            # 提取T2起始值
            t2_start_data = json_data.get("T2_Start", {})
            t2_start = float(t2_start_data.get("v", 0))

            # 提取T2结束值
            t2_end_data = json_data.get("T2_End", {})
            t2_end = float(t2_end_data.get("v", 0))

            # 提取点数
            n_data = json_data.get("N", {})
            n = int(n_data.get("v", 0))

            # 提取单位（优先使用T2_Start的单位）
            unit = t2_start_data.get("u", "ms")

            return cls(
                t2_start=t2_start,
                t2_end=t2_end,
                n=n,
                unit=unit
            )

        except (ValueError, TypeError, KeyError) as e:
            raise WpCompValidationError(
                f"T2AxisLog10 JSON数据解析失败: {e}",
                context=ErrorContext(
                    operation="T2AxisLog10.from_json",
                    stage="json_parsing",
                    details={
                        "json_data": json_data,
                        "error": str(e)
                    }
                )
            ) from e

    def calculate_values(self) -> np.ndarray:
        """计算T2轴值（log10分布）。

        使用WFS规范的log10公式计算T2轴值。

        Returns:
            np.ndarray: T2轴值数组

        Examples:
            >>> t2_axis = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)
            >>> t2_values = t2_axis.calculate_values()
            >>> assert len(t2_values) == 64
            >>> assert np.isclose(t2_values[0], 0.1)
            >>> assert np.isclose(t2_values[-1], 10000)
        """
        # WFS规范的log10公式：T2[i] = T2_Start × 10^((i-1) × log10(T2_End/T2_Start)/(N-1))
        # 其中 i = 1, 2, ..., N （1-based索引）

        # 转换为0-based索引：i = 0, 1, ..., N-1
        indices = np.arange(self.n, dtype=np.float64)

        # 计算log10分布
        log_ratio = np.log10(self.t2_end / self.t2_start)
        exponents = indices * log_ratio / (self.n - 1)
        t2_values = self.t2_start * (10.0 ** exponents)

        return t2_values

    def get_axis_info(self) -> dict[str, Any]:
        """获取T2轴信息。

        Returns:
            dict[str, Any]: T2轴信息字典

        Examples:
            >>> t2_axis = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)
            >>> info = t2_axis.get_axis_info()
            >>> assert info["axis_type"] == "log10"
            >>> assert info["t2_start"] == 0.1
        """
        return {
            "axis_type": "log10",
            "t2_start": self.t2_start,
            "t2_end": self.t2_end,
            "n": self.n,
            "unit": self.unit,
            "log_ratio": np.log10(self.t2_end / self.t2_start),
            "step_factor": 10.0 ** (np.log10(self.t2_end / self.t2_start) / (self.n - 1))
        }


@dataclass(frozen=True)
class T2AxisExp2:
    """T2轴exp2类型的领域对象。

    实现exp2类型T2轴的业务逻辑，提供强类型的接口供scape层使用。

    Architecture
    ------------
    层次/依赖: attr层领域对象
    设计原则: 不可变对象、业务友好、数学正确性
    性能特征: 向量化计算、数值稳定性

    数学公式（符合WFS规范）：
    ```
    T2[i] = T2_Start × 2^((i-1) × T2_Step)
    其中 i = 1, 2, ..., N （WFS使用1-based索引）
    ```

    Attributes:
        t2_start: T2轴起始值（ms）
        t2_step: T2轴步长（2的指数）
        n: T2轴点数
        unit: 单位（默认ms）

    Examples:
        >>> # 从JSON数据创建
        >>> json_data = {
        ...     "Axis_Type": "exp2",
        ...     "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"},
        ...     "T2_Step": {"v": 0.1, "t": "FLOAT"},
        ...     "N": {"v": 64, "t": "INT"}
        ... }
        >>> t2_axis = T2AxisExp2.from_json(json_data)
        >>>
        >>> # 计算T2轴值
        >>> t2_values = t2_axis.calculate_values()
        >>> assert len(t2_values) == 64
        >>> assert t2_values[0] == 0.1

    References:
        《SCAPE_WFS_WP文件规范.md》§5.4.3 - T2_AXIS属性规范
    """

    t2_start: float
    """T2轴起始值（ms）"""

    t2_step: float
    """T2轴步长（2的指数）"""

    n: int
    """T2轴点数"""

    unit: str = "ms"
    """单位（默认ms）"""

    def __post_init__(self) -> None:
        """验证参数有效性。

        Raises:
            WpCompValidationError: 参数无效
        """
        if self.t2_start <= 0:
            raise WpCompValidationError(
                f"T2起始值必须大于0，当前值: {self.t2_start}",
                context=ErrorContext(
                    operation="T2AxisExp2.__post_init__",
                    stage="parameter_validation",
                    details={"t2_start": self.t2_start}
                )
            )

        if self.t2_step <= 0:
            raise WpCompValidationError(
                f"T2步长必须大于0，当前值: {self.t2_step}",
                context=ErrorContext(
                    operation="T2AxisExp2.__post_init__",
                    stage="parameter_validation",
                    details={"t2_step": self.t2_step}
                )
            )

        if self.n < 2:
            raise WpCompValidationError(
                f"T2轴点数必须至少为2，当前值: {self.n}",
                context=ErrorContext(
                    operation="T2AxisExp2.__post_init__",
                    stage="parameter_validation",
                    details={"n": self.n}
                )
            )

    @classmethod
    def from_json(cls, json_data: dict[str, Any]) -> T2AxisExp2:
        """从JSON数据创建T2AxisExp2对象。

        Args:
            json_data: T2_AXIS的JSON数据

        Returns:
            T2AxisExp2: T2轴对象

        Raises:
            WpCompValidationError: JSON数据无效

        Examples:
            >>> json_data = {
            ...     "Axis_Type": "exp2",
            ...     "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"},
            ...     "T2_Step": {"v": 0.1, "t": "FLOAT"},
            ...     "N": {"v": 64, "t": "INT"}
            ... }
            >>> t2_axis = T2AxisExp2.from_json(json_data)
        """
        try:
            # 提取T2起始值
            t2_start_data = json_data.get("T2_Start", {})
            t2_start = float(t2_start_data.get("v", 0))

            # 提取T2步长
            t2_step_data = json_data.get("T2_Step", {})
            t2_step = float(t2_step_data.get("v", 0))

            # 提取点数
            n_data = json_data.get("N", {})
            n = int(n_data.get("v", 0))

            # 提取单位（优先使用T2_Start的单位）
            unit = t2_start_data.get("u", "ms")

            return cls(
                t2_start=t2_start,
                t2_step=t2_step,
                n=n,
                unit=unit
            )

        except (ValueError, TypeError, KeyError) as e:
            raise WpCompValidationError(
                f"T2AxisExp2 JSON数据解析失败: {e}",
                context=ErrorContext(
                    operation="T2AxisExp2.from_json",
                    stage="json_parsing",
                    details={
                        "json_data": json_data,
                        "error": str(e)
                    }
                )
            ) from e

    def calculate_values(self) -> np.ndarray:
        """计算T2轴值（exp2分布）。

        使用WFS规范的exp2公式计算T2轴值。

        Returns:
            np.ndarray: T2轴值数组

        Examples:
            >>> t2_axis = T2AxisExp2(t2_start=0.1, t2_step=0.1, n=64)
            >>> t2_values = t2_axis.calculate_values()
            >>> assert len(t2_values) == 64
            >>> assert np.isclose(t2_values[0], 0.1)
        """
        # WFS规范的exp2公式：T2[i] = T2_Start × 2^((i-1) × T2_Step)
        # 其中 i = 1, 2, ..., N （1-based索引）

        # 转换为0-based索引：i = 0, 1, ..., N-1
        indices = np.arange(self.n, dtype=np.float64)

        # 计算exp2分布
        exponents = indices * self.t2_step
        t2_values = self.t2_start * (2.0 ** exponents)

        return t2_values

    def get_axis_info(self) -> dict[str, Any]:
        """获取T2轴信息。

        Returns:
            dict[str, Any]: T2轴信息字典

        Examples:
            >>> t2_axis = T2AxisExp2(t2_start=0.1, t2_step=0.1, n=64)
            >>> info = t2_axis.get_axis_info()
            >>> assert info["axis_type"] == "exp2"
            >>> assert info["t2_start"] == 0.1
        """
        return {
            "axis_type": "exp2",
            "t2_start": self.t2_start,
            "t2_step": self.t2_step,
            "n": self.n,
            "unit": self.unit,
            "step_factor": 2.0 ** self.t2_step,
            "t2_end": self.t2_start * (2.0 ** ((self.n - 1) * self.t2_step))
        }
