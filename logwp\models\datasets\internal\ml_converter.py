from __future__ import annotations

"""logwp.models.datasets.internal.ml_converter - ML数据转换器

提供将WpDataFrameBundle转换为机器学习就绪格式的内部服务。

Architecture
------------
层次/依赖: datasets层内部服务，被bundle.py调用
设计原则: 纯函数、无副作用、计算专注
性能特征: 高效的NumPy转换和重塑

遵循CCG规范：
- SC-1: 算法正确性
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_DDS_obmiq_开发计划.md》 - 功能下沉需求
"""

from typing import TYPE_CHECKING

import numpy as np

from logwp.infra import get_logger
from logwp.models.exceptions import WpCurveMetadataError, WpDataError
from logwp.infra.exceptions import ErrorContext


if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpDataFrameBundle

logger = get_logger(__name__)


def convert_bundle_to_structured_ml_inputs(
    bundle: WpDataFrameBundle,
    input_map: dict[str, list[str]],
    target_features: list[str],
) -> tuple[dict[str, np.ndarray], np.ndarray]:
    """
    将WpDataFrameBundle转换为结构化的、用于多输入ML模型的Numpy数组。

    此函数是 `WpDataFrameBundle.to_structured_ml_inputs` 的核心实现。

    Args:
        bundle: 要转换的WpDataFrameBundle实例。
        input_map: 一个字典，键是ML模型的输入名称，值是对应输入的曲线名列表。
        target_features: 目标特征的曲线名列表。

    Returns:
        一个元组 (X, y)，其中X是特征字典，y是目标数组。

    Raises:
        WpCurveMetadataError: 如果指定的曲线在元数据或列映射中不存在。
        WpDataError: 如果元数据中定义的列在DataFrame中不存在。
    """
    logger.info(
        "开始将Bundle转换为结构化ML输入",
        operation="convert_bundle_to_structured_ml_inputs",
        bundle_name=bundle.name,
        input_groups=list(input_map.keys()),
        target_count=len(target_features),
    )

    X_structured: dict[str, np.ndarray] = {}

    # 1. 处理特征输入
    for input_name, curve_list in input_map.items():
        if not curve_list:
            logger.warning(f"输入组 '{input_name}' 的曲线列表为空，已跳过。", operation="convert_bundle_to_structured_ml_inputs")
            continue

        df_columns_for_input: list[str] = []
        for curve_name in curve_list:
            columns = bundle.curve_to_columns_map.get(curve_name)
            if not columns:
                raise WpCurveMetadataError(f"在curve_to_columns_map中找不到曲线 '{curve_name}'。")
            df_columns_for_input.extend(columns)

        try:
            input_array = bundle.data[df_columns_for_input].to_numpy()
        except KeyError:
            missing_cols = set(df_columns_for_input) - set(bundle.data.columns)
            raise WpDataError(
                f"数据转换失败：DataFrame中缺少必要的列: {list(missing_cols)}",
                context=ErrorContext(
                    operation="convert_bundle_to_structured_ml_inputs",
                    bundle_name=bundle.name,
                    additional_info={"missing_columns": list(missing_cols)}
                )
            )

        # 启发式规则：如果单个逻辑曲线扩展为多个DataFrame列，则视为序列数据
        is_sequence = len(curve_list) == 1 and len(df_columns_for_input) > 1
        if is_sequence:
            # 对于CNN，期望的输入形状是 (n_samples, sequence_length, n_channels)
            # 这里我们假设通道数为1
            input_array = input_array.reshape(input_array.shape[0], input_array.shape[1], 1)
            logger.debug(f"输入组 '{input_name}' 被识别为序列，已重塑为 {input_array.shape}")

        X_structured[input_name] = input_array

    # 2. 处理目标特征
    target_df_columns: list[str] = []
    for curve_name in target_features:
        columns = bundle.curve_to_columns_map.get(curve_name)
        if not columns:
            raise WpCurveMetadataError(f"在curve_to_columns_map中找不到目标曲线 '{curve_name}'。")
        target_df_columns.extend(columns)

    try:
        y_array = bundle.data[target_df_columns].to_numpy()
    except KeyError:
        missing_cols = set(target_df_columns) - set(bundle.data.columns)
        raise WpDataError(
            f"目标数据转换失败：DataFrame中缺少必要的列: {list(missing_cols)}",
            context=ErrorContext(
                operation="convert_bundle_to_structured_ml_inputs",
                bundle_name=bundle.name,
                additional_info={"missing_target_columns": list(missing_cols)}
            )
        )

    logger.info(
        "Bundle转换完成",
        operation="convert_bundle_to_structured_ml_inputs",
        bundle_name=bundle.name,
        feature_shapes={k: v.shape for k, v in X_structured.items()},
        target_shape=y_array.shape,
    )

    return X_structured, y_array
