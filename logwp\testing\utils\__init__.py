"""logwp.testing.utils - 测试工具函数

提供快速生成测试数据的工具函数。

Examples
--------
>>> from logwp.testing.utils import quick_continuous_df, quick_metadata
>>>
>>> # 快速生成DataFrame
>>> df = quick_continuous_df(
...     well_name="W-1",
...     start_depth=2500,
...     end_depth=2510,
...     interval=0.5,
...     GR=50,
...     PHIT=0.15
... )
>>>
>>> # 快速生成元数据
>>> metadata = quick_metadata("WELL", "MD", "GR", "PHIT")
"""

from .quick_generators import (
    quick_continuous_df,
    quick_discrete_df,
    quick_interval_df,
    quick_metadata
)
from .validators import (
    validate_test_dataset,
    assert_dataset_structure,
    check_curve_consistency,
    assert_curves_allclose,
    assert_physical_constraints
)

__all__ = [
    # 快速生成器
    "quick_continuous_df",
    "quick_discrete_df",
    "quick_interval_df",
    "quick_metadata",

    # 验证器
    "validate_test_dataset",
    "assert_dataset_structure",
    "check_curve_consistency",
    "assert_curves_allclose",
    "assert_physical_constraints",
]
