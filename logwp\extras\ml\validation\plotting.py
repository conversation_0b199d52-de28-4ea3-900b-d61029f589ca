"""
提供模型评估的可视化功能。
"""
from __future__ import annotations

from typing import Any
import numpy as np
import pandas as pd

try:
    import matplotlib.pyplot as plt
    from sklearn.metrics import r2_score, mean_squared_error
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    # 定义虚拟类和函数以允许在没有matplotlib/sklearn的情况下导入
    Axes = object
    def r2_score(*args, **kwargs): raise ImportError("matplotlib and scikit-learn are required.")
    def mean_squared_error(*args, **kwargs): raise ImportError("matplotlib and scikit-learn are required.")


def plot_regression_crossplot(
    y_true: np.ndarray | pd.Series,
    y_pred: np.ndarray | pd.Series,
    groups: pd.Series | None = None,
    title: str | None = "Regression Crossplot",
    ax: "Axes | None" = None,
    **kwargs: Any
) -> "Axes":
    """
    绘制一个高质量的回归模型预测值 vs. 真实值交会图。

    此图是评估回归模型性能的核心可视化工具。它直观地展示了模型的
    准确性、偏差和异常点。

    Args:
        y_true (np.ndarray | pd.Series): 真实的标签值。
        y_pred (np.ndarray | pd.Series): 模型预测的标签值。
        groups (pd.Series | None, optional): 用于对散点进行颜色分组的Series，
                                              例如井名。默认为 None。
        title (str | None, optional): 图表标题。默认为 "Regression Crossplot"。
        ax (matplotlib.axes.Axes | None, optional): 要在其上绘图的现有matplotlib轴对象。
                                                    如果为None，将创建一个新的图和轴。
                                                    默认为 None。
        **kwargs: 其他传递给 `matplotlib.pyplot.scatter` 的关键字参数。

    Returns:
        matplotlib.axes.Axes: 包含了交会图的matplotlib轴对象。
    """
    if not MATPLOTLIB_AVAILABLE:
        raise ImportError("此功能需要 'matplotlib' 和 'scikit-learn' 库。请运行 'pip install matplotlib scikit-learn' 进行安装。")

    if ax is None:
        fig, ax = plt.subplots(figsize=(8, 8))

    y_true_arr, y_pred_arr = np.asarray(y_true), np.asarray(y_pred)

    # 计算性能指标
    r2 = r2_score(y_true_arr, y_pred_arr)
    rmse = np.sqrt(mean_squared_error(y_true_arr, y_pred_arr))

    # 绘制散点图
    if groups is not None:
        df = pd.DataFrame({'y_true': y_true_arr, 'y_pred': y_pred_arr, 'group': groups})
        unique_groups = df['group'].unique()
        colors = plt.cm.viridis(np.linspace(0, 1, len(unique_groups)))
        group_color_map = dict(zip(unique_groups, colors))

        for name, group_df in df.groupby('group'):
            ax.scatter(group_df['y_true'], group_df['y_pred'], label=name, color=group_color_map[name], alpha=0.7, **kwargs)
        ax.legend(title="Groups")
    else:
        ax.scatter(y_true_arr, y_pred_arr, alpha=0.7, **kwargs)

    # 绘制 1:1 对角线
    lims = [
        min(ax.get_xlim()[0], ax.get_ylim()[0]),
        max(ax.get_xlim()[1], ax.get_ylim()[1]),
    ]
    ax.plot(lims, lims, 'k--', alpha=0.75, zorder=0, label="1:1 Line")
    ax.set_xlim(lims)
    ax.set_ylim(lims)

    # 设置图表属性
    ax.set_aspect('equal', adjustable='box')
    ax.set_xlabel("Actual Values")
    ax.set_ylabel("Predicted Values")
    if title:
        ax.set_title(f"{title}\n$R^2 = {r2:.4f}$ | RMSE = {rmse:.4f}")

    ax.grid(True, linestyle='--', alpha=0.6)

    return ax
