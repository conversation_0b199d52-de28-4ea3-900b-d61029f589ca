"""验证包，提供用于评估模型输出结果的各种服务。

此包遵循《可追踪机器学习组件开发框架》规范，将不同的验证功能
（如PLT盲井检验、渗透率相关性分析）封装为独立的、可追踪的步骤(Step)。

公共API
----------
Step Functions (主要接口):
    - run_plt_analysis_step: 执行PLT盲井检验步骤。
    - run_perm_correlation_step: 执行渗透率相关性分析步骤。

Configuration Models:
    - PltAnalysisConfig: PLT分析步骤的配置模型。
    - PermCorrelationConfig: 渗透率相关性分析步骤的配置模型。

Artifact Constants:
    - PltAnalysisArtifacts: PLT分析步骤的产物常量。
    - PermCorrelationArtifacts: 渗透率相关性分析步骤的产物常量。

Plotting Profile Constants:
    - PltPlotTypes: PLT分析中不同图表类型的标准字典键。
    - PltAnalysisPlotProfiles: PLT分析步骤的PlotProfile注册名称。
    - PermCorrelationPlotProfiles: 渗透率相关性分析步骤的PlotProfile注册名称。

Utility Classes and Functions:
    - ValidationArtifactHandler: 产物处理器。
    - replot_plt_capture_curve_from_snapshot: 从快照复现捕获曲线图。
    - replot_plt_lorenz_curve_from_snapshot: 从快照复现洛伦兹曲线图。
    - replot_plt_contribution_crossplot_from_snapshot: 从快照复现贡献率交会图。
    - replot_perm_corr_crossplot_from_snapshot: 从快照复现渗透率交会图。

Exception Classes:
    - ValidationError: 验证包的根异常。
"""

# Step Functions
from .plt_analysis_facade import run_plt_analysis_step
from .perm_corr_facade import run_perm_correlation_step

# Configuration Models
from .config import PltAnalysisConfig, PermCorrelationConfig

# Artifact Constants
from .constants import (
    PltAnalysisArtifacts,
    PermCorrelationArtifacts,
    PltPlotTypes,
    PltAnalysisPlotProfiles,
    PermCorrelationPlotProfiles,
)

# Artifact Handler
from .artifact_handler import ValidationArtifactHandler

# Plotting Functions
from .plotting import (
    replot_plt_capture_curve_from_snapshot,
    replot_plt_lorenz_curve_from_snapshot,
    replot_plt_contribution_crossplot_from_snapshot,
    replot_perm_corr_crossplot_from_snapshot,
)

# Exceptions
from .exceptions import ValidationError

# Import plot_profiles to trigger registration of default profiles
from . import plot_profiles


__all__ = [
    # Step Functions
    "run_plt_analysis_step",
    "run_perm_correlation_step",
    # Configuration Models
    "PltAnalysisConfig",
    "PermCorrelationConfig",
    # Artifact Constants
    "PltAnalysisArtifacts",
    "PermCorrelationArtifacts",
    # Plotting Profile Constants
    "PltPlotTypes",
    "PltAnalysisPlotProfiles",
    "PermCorrelationPlotProfiles",
    # Artifact Handler
    "ValidationArtifactHandler",
    # Plotting Functions
    "replot_plt_capture_curve_from_snapshot",
    "replot_plt_lorenz_curve_from_snapshot",
    "replot_plt_contribution_crossplot_from_snapshot",
    "replot_perm_corr_crossplot_from_snapshot",
    # Exceptions
    "ValidationError",
]
