"""
Defines the Quantity class, which represents a value with a unit.
"""
from typing import Union, TYPE_CHECKING

from .containers import Dimension, Unit
from .exceptions import DimensionalityError

if TYPE_CHECKING:
    from .registry import UnitRegistry


class Quantity:
    """
    Represents a physical quantity, combining a numerical value with a unit.

    Instances of this class are the primary objects users will interact with.
    """

    def __init__(self, value: Union[int, float], unit: Unit, registry: "UnitRegistry" = None):
        if not isinstance(unit, Unit):
            raise TypeError(f"The 'unit' must be a Unit object, not {type(unit).__name__}")
        self.value = float(value)
        self.unit = unit
        self._registry = registry

    @property
    def dimension(self) -> Dimension:
        """The dimension of the quantity."""
        return self.unit.dimension

    def to(self, other_unit: Union[Unit, str]) -> "Quantity":
        """
        Converts the quantity to another unit.

        The target unit must have the same dimension as the original unit.

        Args:
            other_unit: The target unit to convert to. Can be a Unit object
                        or a string that exists in the registry (e.g., 'cm').

        Returns:
            A new Quantity object representing the value in the target unit.

        Raises:
            DimensionalityError: If the units do not have the same dimension.
        """
        if isinstance(other_unit, str):
            if self._registry is None:
                raise ValueError("Cannot convert to a string-defined unit without a registry.")
            target_unit = self._registry[other_unit]
        elif isinstance(other_unit, Unit):
            target_unit = other_unit
        else:
            raise TypeError(f"Target for conversion must be a Unit or string, not {type(other_unit).__name__}")

        if self.dimension != target_unit.dimension:
            raise DimensionalityError(
                f"Cannot convert from '{self.unit.name}' (dimension {self.dimension}) "
                f"to '{target_unit.name}' (dimension {target_unit.dimension})"
            )

        context = self._registry.get_context_for_dimension(self.dimension) if self._registry else None

        if context:
            # Use the context for conversion if one exists for this dimension.
            # This handles non-linear conversions like temperature.
            base_value = context.to_base(self.value, self.unit)
            new_value = context.from_base(base_value, target_unit)
        else:
            # Fallback to the standard linear conversion for all other units.
            # Formula: y = ( (x * scale_x + offset_x) - offset_y) / scale_y

            # 1. Convert original value to the base unit value
            base_value = self.value * self.unit.scale + self.unit.offset

            # 2. Convert base unit value to the target unit value
            new_value = (base_value - target_unit.offset) / target_unit.scale

        return Quantity(new_value, target_unit, registry=self._registry)

    def __repr__(self) -> str:
        return f"<Quantity({self.value}, '{self.unit.name}')>"

    def __str__(self) -> str:
        return f"{self.value} {self.unit.name}"

    def __add__(self, other: "Quantity") -> "Quantity":
        """Adds two quantities. They must have the same dimension."""
        if not isinstance(other, Quantity):
            return NotImplemented

        context = self._registry.get_context_for_dimension(self.dimension) if self._registry else None
        if context:
            raise TypeError(f"Addition is not a well-defined operation for contextual quantities "
                            f"like '{context.name}'. Use delta quantities for temperature differences.")

        if self.dimension != other.dimension:
            raise DimensionalityError(
                f"Cannot add quantity with dimension {self.dimension} "
                f"to quantity with dimension {other.dimension}"
            )

        # Convert other to self's unit and add values
        other_in_my_unit = other.to(self.unit)
        new_value = self.value + other_in_my_unit.value
        return Quantity(new_value, self.unit, self._registry)

    def __sub__(self, other: "Quantity") -> "Quantity":
        """Subtracts two quantities. They must have the same dimension."""
        if not isinstance(other, Quantity):
            return NotImplemented

        context = self._registry.get_context_for_dimension(self.dimension) if self._registry else None
        if context:
            raise TypeError(f"Subtraction is not a well-defined operation for contextual quantities "
                            f"like '{context.name}'. Use delta quantities for temperature differences.")

        # For subtraction, the logic is identical to addition
        other_in_my_unit = other.to(self.unit)
        new_value = self.value - other_in_my_unit.value
        return Quantity(new_value, self.unit, self._registry)

    def __mul__(self, other: Union["Quantity", int, float]):
        """Multiplies the quantity by another quantity or a scalar."""
        if isinstance(other, (int, float)):
            return Quantity(self.value * other, self.unit, self._registry)

        if isinstance(other, Quantity):
            new_value = self.value * other.value
            new_dimension = self.dimension * other.dimension
            new_scale = self.unit.scale * other.unit.scale

            # Create a new composite unit on the fly.
            # A more advanced system would simplify this unit.
            new_unit = Unit(
                name=f"({self.unit.name})*({other.unit.name})",
                symbol=f"({self.unit.symbol})*({other.unit.symbol})",
                dimension=new_dimension,
                scale=new_scale,
            )
            return Quantity(new_value, new_unit, self._registry)

        return NotImplemented

    def __rmul__(self, other: Union[int, float]):
        """Handles right-multiplication (e.g., `2 * quantity`)."""
        return self.__mul__(other)

    def __truediv__(self, other: Union["Quantity", int, float]):
        """Divides the quantity by another quantity or a scalar."""
        if isinstance(other, (int, float)):
            if other == 0:
                raise ZeroDivisionError("Cannot divide a quantity by zero.")
            return Quantity(self.value / other, self.unit, self._registry)

        if isinstance(other, Quantity):
            if other.value == 0:
                raise ZeroDivisionError("Cannot divide by a quantity with zero value.")
            new_value = self.value / other.value
            new_dimension = self.dimension / other.dimension
            new_scale = self.unit.scale / other.unit.scale

            new_unit = Unit(
                name=f"({self.unit.name})/({other.unit.name})",
                symbol=f"({self.unit.symbol})/({other.unit.symbol})",
                dimension=new_dimension,
                scale=new_scale,
            )
            return Quantity(new_value, new_unit, self._registry)

        return NotImplemented

    def __pow__(self, power: Union[int, float]):
        """Raises the quantity to a power."""
        if not isinstance(power, (int, float)):
            return NotImplemented

        new_value = self.value ** power
        new_dimension = self.dimension ** power
        new_scale = self.unit.scale ** power

        new_unit = Unit(
            name=f"({self.unit.name})**{power}",
            symbol=f"({self.unit.symbol})**{power}",
            dimension=new_dimension,
            scale=new_scale,
        )
        return Quantity(new_value, new_unit, self._registry)
