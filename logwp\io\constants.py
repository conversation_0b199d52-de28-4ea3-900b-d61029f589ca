"""I/O层格式特定常量定义

包含各种文件格式的特定常量，严格按照FAP原则组织。

Architecture
------------
层次/依赖: I/O层格式适配器专用常量
设计原则: 格式解耦、端口适配器模式、类型安全
性能特征: 编译时检查、零运行时开销

Constants
---------
- WpXlsxKey: WP Excel文件格式特定常量
- WpFileFormat: 支持的文件格式枚举

References
----------
- 《SCAPE_SAD_软件架构设计.md》FAP-2 - 四层架构分离
- 《SCAPE_DDS_详细设计_logwp.md》§2.6 - I/O层设计
"""

from __future__ import annotations

from enum import Enum

__all__ = [
    "WpXlsxKey",
    "WpFileFormat",
]


class WpXlsxKey(str, Enum):
    """WP Excel 文件相关保留字、Sheet 名及列别名。

    **使用范围**：该枚举**仅**在 ``logwp.io`` 层读取/写入 ``*.wp.xlsx`` 时使用，
    领域模型 (``logwp.models``) 与业务算法层 **不得** 直接依赖文件格式，
    以保持模型与持久化技术解耦（见 SAD §4.3 Port-Adapter）。

    **架构位置**：I/O层文件格式适配器专用常量
    **依赖关系**：仅被logwp.io模块使用，不被models/datasets/types使用
    **设计原则**：文件格式解耦、端口适配器模式、类型安全
    **性能特征**：编译时检查、零运行时开销

    基于 WFS v1.0 规范更新，支持：
    - COMP类型复合数据结构
    - T2_AXIS预定义属性
    - 二维组合曲线命名模式
    - 5层作用域查找机制
    - 新增数据类型和验证规则

    命名约定：
    - Header 键以 ``HDR_`` 前缀。
    - Sheet 名以 ``SHEET_`` 前缀。
    - 列别名以 ``COL_`` 前缀。
    - head_info SECTION 枚举以 ``SEC_`` 前缀。
    - 预定义 PROP / 值保持原始含义前缀 ``PROP_`` / ``VAL_``。
    - COMP类型字段以 ``COMP_`` 前缀。
    - 数据类型以 ``TYPE_`` 前缀。
    - 验证规则以 ``VALID_`` 前缀。

    Examples:
        >>> # ❌ 错误用法：在数据集中使用
        >>> # from logwp.constants import WpXlsxKey  # 违反架构原则
        >>>
        >>> # ✅ 正确用法：仅在I/O层使用
        >>> # 在logwp.io.excel_reader中：
        >>> from logwp.io.constants import WpXlsxKey
        >>> sheet_name = WpXlsxKey.SHEET_HEAD_INFO
        >>> depth_col = WpXlsxKey.COL_MD

    References:
        《SCAPE_SAD_软件架构设计》§4.3 - Port-Adapter模式
        《SCAPE_DDS_详细设计_logwp》§2.6 - I/O层设计
    """

    # ----- 五行表头保留键 -----
    HDR_DS_TYPE: str = "wp_ds_Type"
    HDR_SAMPLE_RATE: str = "wp_ds_SR"
    HDR_DS_INDEX_TYPE: str = "wp_ds_Index"
    HDR_DS_DESC: str = "wp_ds_Desc"

    # ----- 保留 Sheet 名（基于WFS A.1.1节）-----
    SHEET_HEAD_INFO: str = "_Head_Info"
    SHEET_WELL_MAP: str = "_Well_Map"
    SHEET_SUMMARY: str = "_Summary"
    SHEET_OVERVIEW: str = "_Overview"
    SHEET_REMARKS: str = "_Remarks"
    SHEET_REMARK: str = "_Remark"

    # ----- 深度索引列别名 (Continuous / Point) - 基于WFS A.6.1节 -----
    COL_MD: str = "MD"
    COL_DEPTH: str = "DEPTH"
    COL_DEP: str = "DEP"
    COL_DEPT: str = "DEPT"

    # ----- Interval 顶 / 底深列别名 - 基于WFS A.6.2节 -----
    COL_MD_TOP: str = "MD_Top"
    COL_MDTOP: str = "MDTop"
    COL_TOP: str = "Top"
    COL_MD_BOTTOM: str = "MD_Bottom"
    COL_MDBOTTOM: str = "MDBottom"
    COL_MD_BOT: str = "MD_Bot"
    COL_BOTTOM: str = "Bottom"
    COL_BOT: str = "Bot"

    # ----- 井名列别名 - 基于WFS A.7.1节 -----
    COL_WELL_NO: str = "WELL_NO"
    COL_WELL: str = "WELL"
    COL_WELLNAME: str = "WELLNAME"

    # ----- 深度单位 - 基于WFS A.6.4节 -----
    UNIT_METERS: str = "METERS"
    UNIT_METER: str = "METER"
    UNIT_M: str = "M"
    UNIT_FT: str = "FT"
    UNIT_F: str = "F"

    # ----- head_info SECTION 枚举 - 基于WFS A.8.2节 -----
    SEC_VERSION: str = "V"
    SEC_WORKAREA: str = "WP"
    SEC_DATASET: str = "DS"
    SEC_WELL: str = "W"
    SEC_CURVE: str = "C"
    SEC_OTHER: str = "O"

    # ----- head_info 列名 - 基于WFS A.8.1节 -----
    COL_S: str = "S"
    COL_DATASET: str = "DATASET"
    COL_CURVE: str = "CURVE"
    COL_ATTRIBUTE: str = "ATTRIBUTE"
    COL_TYPE: str = "TYPE"
    COL_UNIT: str = "UNIT"
    COL_VALUE: str = "VALUE"
    COL_DESCRIPTION: str = "DESCRIPTION"

    WELLHEAD_MAX_COLUMN: int = 9

    WELLMAP_MAX_COLUMN: int = 2

    # ----- 预定义属性名 - 基于WFS A.8.3节 -----
    ATTR_VERSION: str = "Version"
    ATTR_T2_AXIS: str = "T2_AXIS"

    # ----- 数据类型 - 基于WFS A.5.1节 -----
    TYPE_INT: str = "INT"
    TYPE_FLOAT: str = "FLOAT"
    TYPE_STR: str = "STR"
    TYPE_BOOL: str = "BOOL"
    TYPE_COMP: str = "COMP"

    # ----- 曲线类别 - 基于WFS A.5.2节 -----
    CLASS_CAT: str = "CAT"
    CLASS_NORMAL: str = ""

    # ----- 布尔值表示 - 基于WFS A.5.3节 -----
    BOOL_TRUE_T: str = "T"
    BOOL_TRUE_TRUE: str = "TRUE"
    BOOL_TRUE_Y: str = "Y"
    BOOL_TRUE_YES: str = "Yes"
    BOOL_TRUE_1: str = "1"

    # ----- T2_AXIS 轴类型 - 基于WFS A.10.1节 -----
    T2_AXIS_LOG10: str = "log10"
    T2_AXIS_EXP2: str = "exp2"

    # ----- T2_AXIS 属性名 - 基于WFS A.10.2节 -----
    T2_PROP_AXIS_TYPE: str = "Axis_Type"
    T2_PROP_T2_START: str = "T2_Start"
    T2_PROP_T2_END: str = "T2_End"
    T2_PROP_T2_STEP: str = "T2_Step"
    T2_PROP_N: str = "N"
    T2_PROP_VALUES: str = "Values"

    # ----- T2时间单位 - 基于WFS A.10.3节 -----
    T2_UNIT_MS: str = "ms"

    # ----- COMP类型JSON字段 - 基于WFS A.11.1节 -----
    COMP_FIELD_V: str = "v"
    COMP_FIELD_U: str = "u"
    COMP_FIELD_T: str = "t"

    # ----- 二维组合曲线索引模式 - 基于WFS A.12.1节 -----
    CURVE_2D_INDEX_START: int = 1
    CURVE_2D_INDEX_PATTERN: str = r"\[(\d+)\]$"

    # ----- 井名映射列名 - 基于WFS A.9.1节 -----
    WELLMAP_COL_WELL: str = "WELL"
    WELLMAP_COL_MAP_WELL: str = "MAP_WELL"

    # ----- 曲线定义行标题 - 基于WFS A.4.1节 -----
    ROW_TITLE_NAME: str = "Name"
    ROW_TITLE_UNIT: str = "Unit"
    ROW_TITLE_TYPE: str = "Type"
    ROW_TITLE_CLASS: str = "Class"
    ROW_TITLE_COMMENT: str = "Comment"
    ROW_TITLE_DATA: str = "Data"

    # ----- 曲线定义行号 - 基于WFS A.4.2节 -----
    ROW_DATASET_TYPE: int = 1
    ROW_SAMPLING_RATE: int = 2
    ROW_NAME: int = 3
    ROW_UNIT: int = 4
    ROW_TYPE: int = 5
    ROW_CLASS: int = 6
    ROW_COMMENT: int = 7
    ROW_DATA_START: int = 8

    # ----- 数据集类型标识 - 基于WFS A.3.1节 -----
    DATASET_TYPE_CONTINUOUS: str = "Continuous"
    DATASET_TYPE_POINT: str = "Point"
    DATASET_TYPE_INTERVAL: str = "Interval"

    # ----- 工作表前缀规则 - 基于WFS A.2.2节 -----
    PREDEFINED_SHEET_PREFIX: str = "_"
    HIDDEN_SHEET_PREFIX: str = "__"

    # ----- 文件扩展名与标识 - 基于WFS A.1.1节 -----
    WP_FILE_EXTENSION: str = ".wp.xlsx"
    EXCEL_EXTENSION: str = ".xlsx"
    WP_IDENTIFIER: str = ".wp"

    # ----- 文件格式限制 - 基于WFS A.1.2节 -----
    MAX_WORKSHEETS: int = 255
    MAX_SHEET_NAME_LENGTH: int = 31
    MAX_ROWS: int = 1048576
    MAX_COLUMNS: int = 16384
    RECOMMENDED_MAX_SHEETS: int = 50
    RECOMMENDED_MAX_DATA_ROWS: int = 1000000

    # ----- 当前版本号 - 基于WFS A.8.4节 -----
    CURRENT_VERSION: str = "1.0"

    # ----- 数据集索引类型常量 -----
    DATASET_INDEX_TYPE_DEPTH: str = "Depth"

    # ----- 便捷方法 -------------------------------------------------
    @classmethod
    def depth_aliases(cls) -> set[str]:
        """返回所有深度索引列别名（含 Interval 相关）。"""
        return {
            cls.COL_MD.value,
            cls.COL_DEPTH.value,
            cls.COL_DEP.value,
            cls.COL_DEPT.value,
            cls.COL_MD_TOP.value,
            cls.COL_MDTOP.value,
            cls.COL_TOP.value,
            cls.COL_MD_BOTTOM.value,
            cls.COL_MDBOTTOM.value,
            cls.COL_MD_BOT.value,
            cls.COL_BOTTOM.value,
            cls.COL_BOT.value,
        }

    @classmethod
    def well_name_aliases(cls) -> set[str]:
        """返回所有井名列别名。"""
        return {
            cls.COL_WELL_NO.value,
            cls.COL_WELL.value,
            cls.COL_WELLNAME.value,
        }

    @classmethod
    def expected_head_headers(cls) -> list[str]:
        """返回_Head_Info表单预期的表头行。"""
        return [
            cls.COL_S.value,
            cls.COL_DATASET.value,
            cls.COL_WELL.value,
            cls.COL_CURVE.value,
            cls.COL_ATTRIBUTE.value,
            cls.COL_TYPE.value,
            cls.COL_UNIT.value,
            cls.COL_VALUE.value,
            cls.COL_DESCRIPTION.value,
        ]

    @classmethod
    def head_info_sections(cls) -> set[str]:
        """返回所有支持的 Head Info SECTION。"""
        return {
            cls.SEC_VERSION.value,
            cls.SEC_WORKAREA.value,
            cls.SEC_DATASET.value,
            cls.SEC_WELL.value,
            cls.SEC_CURVE.value,
            cls.SEC_OTHER.value,
        }

    @classmethod
    def bool_true_values(cls) -> set[str]:
        """返回所有布尔真值表示。"""
        return {
            cls.BOOL_TRUE_T.value,
            cls.BOOL_TRUE_TRUE.value,
            cls.BOOL_TRUE_Y.value,
            cls.BOOL_TRUE_YES.value,
            cls.BOOL_TRUE_1.value,
        }

    @classmethod
    def data_types(cls) -> set[str]:
        """返回所有支持的数据类型。"""
        return {
            cls.TYPE_INT.value,
            cls.TYPE_FLOAT.value,
            cls.TYPE_STR.value,
            cls.TYPE_BOOL.value,
            cls.TYPE_COMP.value,
        }

    @classmethod
    def predefined_sheets(cls) -> set[str]:
        """返回所有预定义工作表名。"""
        return {
            cls.SHEET_HEAD_INFO.value,
            cls.SHEET_WELL_MAP.value,
            cls.SHEET_SUMMARY.value,
            cls.SHEET_OVERVIEW.value,
            cls.SHEET_REMARKS.value,
            cls.SHEET_REMARK.value,
        }

    @classmethod
    def dataset_types(cls) -> set[str]:
        """返回所有支持的数据集类型。"""
        return {
            cls.DATASET_TYPE_CONTINUOUS.value,
            cls.DATASET_TYPE_POINT.value,
            cls.DATASET_TYPE_INTERVAL.value,
        }

    @classmethod
    def depth_units(cls) -> set[str]:
        """返回所有支持的深度单位。"""
        return {
            cls.UNIT_METERS.value,
            cls.UNIT_METER.value,
            cls.UNIT_M.value,
            cls.UNIT_FT.value,
            cls.UNIT_F.value,
        }


class WpFileFormat(str, Enum):
    """WP文件格式枚举。

    Architecture
    ------------
    层次/依赖: I/O层文件格式识别
    设计原则: 格式标识、扩展性、向后兼容
    性能特征: 轻量级字符串枚举
    """

    WP_XLSX = ".wp.xlsx"
    LAS = ".las"
    DLIS = ".dlis"  # 规划中扩展


class WpIOResponseKeys(str, Enum):
    """I/O操作响应键名。

    Architecture
    ------------
    层次/依赖: I/O层专用响应格式
    设计原则: 性能监控、资源使用追踪
    性能特征: 支持异步I/O状态、GPU内存监控
    """

    # 基础I/O指标
    BYTES_READ = "bytes_read"
    BYTES_WRITTEN = "bytes_written"
    CHUNKS_PROCESSED = "chunks_processed"
    PROCESSING_TIME_SEC = "processing_time_sec"

    # 内存使用指标
    PEAK_MEMORY_MB = "peak_memory_mb"
    CURRENT_MEMORY_MB = "current_memory_mb"
    MEMORY_EFFICIENCY = "memory_efficiency"

    # 异步I/O指标
    ASYNC_TASKS_COMPLETED = "async_tasks_completed"
    ASYNC_TASKS_PENDING = "async_tasks_pending"
    ASYNC_THROUGHPUT_MBS = "async_throughput_mbs"

    # 文件处理指标
    FILES_PROCESSED = "files_processed"
    FILES_FAILED = "files_failed"
    AVERAGE_FILE_SIZE_MB = "average_file_size_mb"


class WpSystemDefaults(Enum):
    """现代化系统默认参数，可在运行时被配置覆盖。

    Architecture
    ------------
    层次/依赖: 基础配置层，被所有模块使用
    设计原则: 现代化默认值、性能优化、资源管理
    性能特征: 支持GPU/CPU自适应、异步I/O优化
    """

    # 基础I/O配置
    CHUNK_SIZE = 10_000  # Reader 默认 chunk 行数
    MAX_MEMORY_GB = 4.0  # 最大内存使用限制（GB）
    CACHE_DIR = "~/.scape_cache"  # joblib 缓存路径

    # 现代化并行配置
    NUM_WORKERS = 4  # joblib 默认并发度（替代dask）
    ASYNC_IO_WORKERS = 2  # 异步I/O工作线程数

    # 文件处理配置
    FILE_ENCODING = "utf-8"  # 默认文件编码
    EXCEL_ENGINE = "openpyxl"  # Excel处理引擎

    # 超时配置
    IO_TIMEOUT_SEC = 300  # I/O操作超时（秒）
    ASYNC_TIMEOUT_SEC = 600  # 异步操作超时（秒）

    # 数据集处理常量
    MEMORY_UNIT_MB = 1024 * 1024  # MB转换单位
    MEMORY_UNIT_GB = 1024 * 1024 * 1024  # GB转换单位
    DATASET_COPY_SUFFIX = "_filtered"  # 数据集副本后缀


class WpPerformanceLimits(Enum):
    """性能限制和基准常量。

    Architecture
    ------------
    层次/依赖: 性能监控层，用于性能验证和限制
    设计原则: 现代化性能目标、GPU/CPU基准
    性能特征: 支持性能回归检测、资源限制
    """

    # CPU性能基准
    CPU_STAGE1_MAX_SEC_PER_WELL = 300  # Stage-1 OBMIQ ≤ 5min/井
    CPU_STAGE2_MAX_SEC_PER_CURVE = 1   # Stage-2 FOSTER-NMR ≤ 1s/曲线
    CPU_PSO_MAX_SEC = 120               # SWIFT-PSO ≤ 2min

    # GPU性能基准（10-15倍加速目标）
    GPU_STAGE1_MAX_SEC_PER_WELL = 30   # Stage-1 OBMIQ ≤ 30s/井
    GPU_STAGE2_MAX_SEC_PER_CURVE = 0.1 # Stage-2 FOSTER-NMR ≤ 0.1s/曲线
    GPU_PSO_MAX_SEC = 8                # SWIFT-PSO ≤ 8s

    # 内存限制
    MAX_CPU_MEMORY_GB = 8.0             # CPU最大内存使用
    MAX_GPU_MEMORY_GB = 20.0            # GPU最大显存使用

    # 文件大小限制
    MAX_EXCEL_FILE_MB = 500             # Excel文件大小限制
    MAX_DATASET_ROWS = 1_000_000        # 单数据集最大行数


class WpAsyncIODefaults(Enum):
    """异步I/O默认配置。

    Architecture
    ------------
    层次/依赖: 异步I/O层专用配置
    设计原则: 高并发、内存优化、错误恢复
    性能特征: 支持大文件处理、批量操作
    """

    # 异步I/O配置
    MAX_CONCURRENT_FILES = 4            # 最大并发文件数
    ASYNC_CHUNK_SIZE = 50_000           # 异步读取chunk大小
    BUFFER_SIZE_MB = 64                 # I/O缓冲区大小

    # 连接池配置
    CONNECTION_POOL_SIZE = 10           # 连接池大小
    CONNECTION_TIMEOUT_SEC = 30         # 连接超时
    READ_TIMEOUT_SEC = 300              # 读取超时

    # 重试策略
    MAX_RETRIES = 3                     # 最大重试次数
    RETRY_DELAY_SEC = 1.0               # 重试延迟
    EXPONENTIAL_BACKOFF = True          # 指数退避


class WpErrorCode(str, Enum):
    """错误代码枚举。

    Architecture
    ------------
    层次/依赖: 异常层错误分类
    设计原则: 错误分类、诊断友好、国际化支持
    性能特征: 轻量级标识、便于日志分析
    """

    # 文件格式错误 (E1xxx)
    E1001 = "E1001"  # 无效文件名格式
    E1002 = "E1002"  # 缺少版本属性
    E1003 = "E1003"  # 版本号不匹配
    E1004 = "E1004"  # 工作表结构错误

    # 数据验证错误 (E2xxx)
    E2001 = "E2001"  # 井名为空
    E2002 = "E2002"  # 深度非单调递增
    E2003 = "E2003"  # 循环井名映射
    E2004 = "E2004"  # 数据类型不匹配

    # COMP类型错误 (E3xxx)
    E3001 = "E3001"  # COMP JSON格式无效
    E3002 = "E3002"  # COMP结构不完整
    E3003 = "E3003"  # T2_AXIS定义无效
    E3004 = "E3004"  # 二维组合曲线索引错误

    # 属性查找错误 (E4xxx)
    E4001 = "E4001"  # 属性不存在
    E4002 = "E4002"  # 作用域冲突
    E4003 = "E4003"  # 查找参数无效

    # GPU计算错误 (E5xxx)
    E5001 = "E5001"  # GPU设备不可用
    E5002 = "E5002"  # GPU内存不足
    E5003 = "E5003"  # GPU计算失败
