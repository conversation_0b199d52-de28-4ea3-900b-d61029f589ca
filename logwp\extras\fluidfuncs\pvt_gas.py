"""
天然气PVT性质关联式模型
"""
import numpy as np

from logwp.extras.units import Quantity, ureg
from logwp.extras.petrofuncs import validate_dimensions


@validate_dimensions(temp='temperature', pressure='pressure')
def lee_gonzalez_gas_viscosity(
    temp: Quantity,
    pressure: Quantity,
    gas_gravity: float,
    n2_mol_frac: float = 0.0,
    co2_mol_frac: float = 0.0,
    h2s_mol_frac: float = 0.0
) -> Quantity:
    """
    使用 Lee-Gonzalez-Eakin 关联式计算气体粘度。

    参数:
        temp (Quantity): 温度，带有 `temperature` 量纲。
        pressure (Quantity): 压力，带有 `pressure` 量纲。
        gas_gravity (float): 气体相对密度 (空气=1)，无量纲。
        n2_mol_frac (float, optional): 氮气的摩尔分数，默认为 0.0。
        co2_mol_frac (float, optional): 二氧化碳的摩尔分数，默认为 0.0。
        h2s_mol_frac (float, optional): 硫化氢的摩尔分数，默认为 0.0。

    返回:
        Quantity: 计算出的气体粘度，单位为 cP (厘泊)。
    """
    # 公式基于特定单位，进行安全转换
    temp_R = temp.to('degR').value # 兰氏度
    pressure_psia = pressure.to('psi').value # 绝对磅力/平方英寸

    # 1. 计算气体分子量 (M)
    M = 28.97 * gas_gravity

    # 2. 计算气体密度 (rho_g)，单位为 g/cm³
    # 这是一个简化的气体密度估算，更精确的计算需要Z因子
    # Z因子通常由另一个复杂的关联式计算得出，此处为简化，假设 Z=1
    Z = 1.0
    rho_g_g_cm3 = (pressure_psia * M) / (Z * 10.73 * temp_R) * 0.0160185

    # 3. 计算经验参数 K, X, Y
    K = (9.4 + 0.02 * M) * temp_R**1.5 / (209 + 19 * M + temp_R)
    X = 3.5 + (986 / temp_R) + 0.01 * M
    Y = 2.4 - 0.2 * X

    # 4. 计算气体粘度，单位为 cP
    viscosity_cp = (1e-4) * K * np.exp(X * rho_g_g_cm3**Y)

    return ureg.Quantity(viscosity_cp, 'cP')
