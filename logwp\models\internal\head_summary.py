"""井头信息概况生成服务。

提供WpHead井头信息概况生成的服务实现。

Architecture
------------
层次/依赖: models/internal服务层，无状态服务函数
设计原则: Service Layer模式、无状态设计、类型安全
性能特征: 轻量级处理、内存优化、错误容错

Examples:
    >>> from logwp.models.internal.head_summary import generate_head_summary
    >>> summary = generate_head_summary(head)
    >>> print(f"总属性数: {summary['total_attributes']}")

References:
    《SCAPE_DDS_logwp_generate_summary.md》§4.3 - 井头信息服务设计
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from logwp.models.head import WpHead

from logwp.infra import get_logger
from .summary_constants import SummaryKeys, LogMessages

logger = get_logger(__name__)


def generate_head_summary(head: WpHead) -> dict[str, Any]:
    """生成井头信息概况（无状态服务函数）。

    Args:
        head: WpHead实例

    Returns:
        dict: 井头信息概况数据
            - total_attributes: 总属性数
            - by_wfs_category: 按WFS类别分组的属性统计
            - attribute_types: 属性数据类型统计
            - attribute_records: 详细的属性记录列表

    Examples:
        >>> summary = generate_head_summary(head)
        >>> print(f"总属性: {summary['total_attributes']}")
        >>> print(f"WFS类别统计: {summary['by_wfs_category']}")
        >>> print(f"数据类型统计: {summary['attribute_types']}")
        >>> print(f"详细记录: {len(summary['attribute_records'])} 条")
    """
    # 获取所有属性记录
    attribute_records = head.attribute_records
    logger.debug(LogMessages.MSG_HEAD_SUMMARY_START, total_attrs=len(attribute_records))

    summary = {
        SummaryKeys.TOTAL_ATTRIBUTES: len(attribute_records),
        SummaryKeys.ATTRIBUTE_TYPES: {},
        SummaryKeys.BY_WFS_CATEGORY: {},
        SummaryKeys.ATTRIBUTE_RECORDS: []  # 新增：详细的属性记录列表
    }

    # 统计属性类型
    type_counts = {}
    wfs_category_counts = {}
    detailed_records = []

    # 按类别分组和类型统计
    for record in attribute_records:
        # WFS类别统计
        wfs_category = record.category.value
        wfs_category_counts[wfs_category] = wfs_category_counts.get(wfs_category, 0) + 1

        # 统计属性类型
        value_type = type(record.value).__name__
        type_counts[value_type] = type_counts.get(value_type, 0) + 1

        # 添加详细记录
        detailed_records.append(record.to_summary_dict())

    summary[SummaryKeys.ATTRIBUTE_TYPES] = type_counts
    summary[SummaryKeys.BY_WFS_CATEGORY] = wfs_category_counts
    summary[SummaryKeys.ATTRIBUTE_RECORDS] = detailed_records

    logger.debug(LogMessages.MSG_HEAD_SUMMARY_COMPLETE,
                wfs_categories=len(summary[SummaryKeys.BY_WFS_CATEGORY]),
                attribute_records_count=len(detailed_records))

    # 调试日志：检查attribute_records字段
    logger.debug("井头概况生成完成",
                total_attributes=summary[SummaryKeys.TOTAL_ATTRIBUTES],
                attribute_records_key=SummaryKeys.ATTRIBUTE_RECORDS,
                attribute_records_count=len(summary[SummaryKeys.ATTRIBUTE_RECORDS]))

    return summary

