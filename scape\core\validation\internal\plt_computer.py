"""PLT盲井检验的核心计算服务。

此模块包含所有PLT检验的底层计算逻辑，遵循无状态服务设计。
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, Tuple

import numpy as np
import pandas as pd
from sklearn.metrics import auc
from scipy.stats import pearsonr, spearmanr
from sklearn.metrics import mean_absolute_error, mean_squared_error
from logwp.models.constants import WpDepthRole
from logwp.models.exceptions import WpDataError

if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpDataFrameBundle

def compute_all_metrics_for_well(
    well_name: str,
    prediction_bundle: WpDataFrameBundle,
    permeability_curve: str,
    plt_bundle: WpDataFrameBundle,
    flow_rate_curve: str,
) -> Dict[str, Any]:
    """
    为单口井计算所有PLT检验指标。

    这是PLT检验的内部计算主入口，按顺序执行所有分析步骤。
    """
    # 步骤 1: 计算层段属性
    analyzed_layers_df = _calculate_layer_properties(
        well_name, prediction_bundle, permeability_curve, plt_bundle, flow_rate_curve
    )

    # 步骤 2 & 3: 计算相对贡献、相关性和误差
    analyzed_layers_df, corr_metrics, err_metrics = (
        calculate_relative_contributions_and_metrics(
            analyzed_layers_df
        )
    )

    # 步骤 4: 计算捕获曲线
    capture_metrics = calculate_capture_curve_metrics(
        analyzed_layers_df
    )

    # 步骤 5: 计算洛伦兹曲线
    lorenz_metrics = calculate_lorenz_curve_metrics(
        analyzed_layers_df
    )

    # 步骤 6: 组装最终结果
    result: Dict[str, Any] = {
        "well_name": well_name,
        "analyzed_layers_df": analyzed_layers_df,
        "correlations": corr_metrics,
        "errors": err_metrics,
        "capture_curve": capture_metrics,
        "lorenz_curve": lorenz_metrics,
    }

    return result


def _calculate_layer_properties(
    well_name: str,
    prediction_bundle: WpDataFrameBundle,
    permeability_curve: str,
    plt_bundle: WpDataFrameBundle,
    flow_rate_curve: str,
) -> pd.DataFrame:
    """
    计算每个PLT层段的预测属性 (S_pred,i, k_bar_pred,i)。

    这是PLT分析流程的第一步，为后续所有指标计算准备基础数据。
    """

    # 1. 获取PLT分层数据
    plt_layers_data = plt_bundle.get_interval_curve_values(
        well_name, [flow_rate_curve]
    )
    if not plt_layers_data:
        return pd.DataFrame()

    # 2. 获取预测曲线的深度和井名列

    # 3. 遍历每个PLT层段进行计算
    layer_records = []
    for top, bottom, flow_rate in plt_layers_data:
        try:
            # --- 情况A: 层段内有数据点 (正常流程) ---
            # 3.1. 计算层段代表性渗透率 k_bar_pred,i (直接使用统计方法)
            k_bar_pred = prediction_bundle.get_curve_statistic_with_zone(
                well_name, top, bottom, permeability_curve, statistic="mean"
            )

            # 3.2. 计算层段预测强度 S_pred,i (积分)
            # 采用更稳健的方式，先提取层段数据，再计算积分
            zone_values = prediction_bundle.get_curve_values_with_zone(
                well_name, top, bottom, permeability_curve
            )
            # 假设深度采样是均匀的，获取采样间隔
            is_uniform, delta_d = prediction_bundle.check_uniform_depth_sampling()
            if not is_uniform:
                # 对于非等间隔，更精确的计算需要深度值，但此处简化处理
                delta_d = (bottom - top) / len(zone_values) if zone_values else 0

            s_pred = sum(zone_values) * (delta_d or 0.0)

        except WpDataError:
            s_pred = 0.0
            k_bar_pred = 0.0

        layer_records.append({
            "top": top, "bottom": bottom, "h": bottom - top,
            "q_plt": flow_rate, "s_pred": s_pred, "k_bar_pred": k_bar_pred,
        })

    return pd.DataFrame(layer_records)

def calculate_capture_curve_metrics(
    layers_df: pd.DataFrame, alpha: float = 0.3
) -> Dict[str, Any]:
    """
    计算捕获曲线及其相关指标。

    Args:
        layers_df (pd.DataFrame): 包含 'k_bar_pred', 'h', 'q_plt', 'top', 'bottom' 列的层段数据。
        alpha (float): 用于计算CR@alpha和Lift@alpha的百分比阈值。

    Returns:
        PltCaptureCurveMetrics: 包含捕获曲线所有指标的字典。
    """
    if layers_df.empty or "k_bar_pred" not in layers_df.columns:
        coords = (np.array([0.0, 1.0]), np.array([0.0, 1.0]))
        return {"auc": 0.5, "gini": 0.0, "capture_at_30_percent": alpha, "lift_at_30_percent": 1.0, "coords": coords}

    # --- 修正：严格遵循方法说明书§4.5.5的计算流程 ---
    layers = layers_df.copy()

    # 步骤 1 & 2: 准备数据并定义高产层
    # 确保h列存在，以防万一
    if 'h' not in layers.columns:
        layers['h'] = layers['bottom'] - layers['top']

    layers['q_density'] = layers['q_plt'] / layers['h'].replace(0, np.nan)
    layers.dropna(subset=['q_density'], inplace=True)
    if layers.empty:
        coords = (np.array([0.0, 1.0]), np.array([0.0, 1.0]))
        return {"auc": 0.5, "gini": 0.0, "capture_at_30_percent": alpha, "lift_at_30_percent": 1.0, "coords": coords}

    high_prod_threshold = layers['q_density'].quantile(0.70)
    layers['is_high_prod'] = (layers['q_density'] >= high_prod_threshold).astype(int)
    layers['high_prod_h'] = layers['h'] * layers['is_high_prod']

    # 步骤 3: 按模型预测的渗透率降序排列
    df_sorted = layers.sort_values(by="k_bar_pred", ascending=False).reset_index(drop=True)

    # 步骤 4 & 5: 计算累积值并归一化
    total_h = df_sorted["h"].sum()
    total_high_prod_h = df_sorted["high_prod_h"].sum()

    x_coords = np.insert(df_sorted["h"].cumsum().values / total_h if total_h > 0 else 0.0, 0, 0)
    y_coords = np.insert(df_sorted["high_prod_h"].cumsum().values / total_high_prod_h if total_high_prod_h > 0 else 0.0, 0, 0)

    auc_val = auc(x_coords, y_coords)
    gini = (auc_val - 0.5) / 0.5 if auc_val is not None else 0.0
    capture_at_alpha = np.interp(alpha, x_coords, y_coords)
    lift_at_alpha = capture_at_alpha / alpha if alpha > 0 else 1.0

    return {
        "auc": float(auc_val), "gini": float(gini),
        "capture_at_30_percent": float(capture_at_alpha),
        "lift_at_30_percent": float(lift_at_alpha),
        "coords": (x_coords, y_coords),
    }


def calculate_lorenz_curve_metrics(layers_df: pd.DataFrame) -> Dict[str, Any]:
    """
    计算洛伦兹曲线及其Gini系数。

    Args:
        layers_df (pd.DataFrame): 包含 'k_bar_pred', 's_pred', 'q_plt' 列的
            层段数据。

    Returns:
        PltLorenzCurveMetrics: 包含洛伦兹曲线指标的字典。
    """
    if layers_df.empty or "k_bar_pred" not in layers_df.columns:
        coords = (np.array([0.0, 1.0]), np.array([0.0, 1.0]))
        return {"gini": 0.0, "coords": coords}

    # 修正1: 按方法说明书要求，使用降序排列
    df_sorted = layers_df.sort_values(by="k_bar_pred", ascending=False).reset_index(drop=True)

    # 修正2: X轴使用预测强度(s_pred)，Y轴使用真实产量(q_plt)
    total_p = df_sorted["s_pred"].sum()
    total_q = df_sorted["q_plt"].sum()

    df_sorted["cum_p_pct"] = df_sorted["s_pred"].cumsum() / total_p if total_p > 0 else 0.0
    df_sorted["cum_q_pct"] = df_sorted["q_plt"].cumsum() / total_q if total_q > 0 else 0.0

    x_coords = np.insert(df_sorted["cum_p_pct"].values, 0, 0)
    y_coords = np.insert(df_sorted["cum_q_pct"].values, 0, 0)

    auc_val = auc(x_coords, y_coords)
    gini = 1 - 2 * auc_val if auc_val is not None else 0.0

    return {"gini": float(gini), "coords": (x_coords, y_coords)}


def calculate_relative_contributions_and_metrics(
    layers_df: pd.DataFrame,
) -> Tuple[pd.DataFrame, Dict[str, Any], Dict[str, Any]]:
    """
    计算相对贡献率，并基于此计算相关性和误差指标。

    Args:
        layers_df (pd.DataFrame): 包含 'q_plt' 和 's_pred' 列的层段数据。

    Returns:
        tuple[pd.DataFrame, PltCorrelationMetrics, PltErrorMetrics]:
            包含新列 ('r_plt', 'r_pred') 的DataFrame、相关性指标和误差指标。
    """
    if layers_df.empty:
        # 处理空DataFrame的情况
        corr_metrics: Dict[str, Any] = {"pearson_r": np.nan, "spearman_rho": np.nan}
        err_metrics: Dict[str, Any] = {"mae": np.nan, "mape": np.nan, "rmse": np.nan}
        return layers_df, corr_metrics, err_metrics

    df = layers_df.copy()

    # 步骤 4.5.2: 计算相对贡献
    total_q_plt = df["q_plt"].sum()
    total_s_pred = df["s_pred"].sum()

    df["r_plt"] = df["q_plt"] / total_q_plt if total_q_plt > 0 else 0.0
    df["r_pred"] = df["s_pred"] / total_s_pred if total_s_pred > 0 else 0.0

    r_plt, r_pred = df["r_plt"], df["r_pred"]

    # 步骤 4.5.3: 计算相关性 (至少需要2个点)
    pearson_r, spearman_rho = (np.nan, np.nan)
    if len(df) > 1:
        pearson_r, _ = pearsonr(r_pred, r_plt)
        spearman_rho, _ = spearmanr(r_pred, r_plt)

    corr_metrics: Dict[str, Any] = {
        "pearson_r": float(pearson_r), "spearman_rho": float(spearman_rho)
    }

    # 步骤 4.5.4: 计算误差
    err_metrics: Dict[str, Any] = {
        "mae": float(mean_absolute_error(r_plt, r_pred)),
        "mape": float(np.mean(np.abs((r_plt - r_pred) / np.where(r_plt == 0, 1, r_plt))) * 100),
        "rmse": float(np.sqrt(mean_squared_error(r_plt, r_pred))),
    }

    return df, corr_metrics, err_metrics
