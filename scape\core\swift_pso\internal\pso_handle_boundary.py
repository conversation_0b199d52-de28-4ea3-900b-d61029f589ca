"""scape.core.swift_pso.internal.pso_handle_boundary - PSO边界处理策略

实现PSO优化过程中的边界约束处理，支持硬反射和边界抖动两种策略。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部边界处理
设计原则: 纯函数、向量化计算、后端无关
性能特征: GPU/CPU兼容、向量化操作、内存优化

遵循CCG规范：
- GP-1: 自动检测回退
- PF-1: 内存控制
- TS-1: 完整类型注解

References
----------
- 《SCAPE_MS_方法说明书》§4.2 - 边界处理策略定义
- 迁移自 scape/core/swift_pso_backup/internal/pso_handle_boundary.py
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Tuple

if TYPE_CHECKING:
    from logwp.extras.backend import BackendService


def handle_boundary_hard_reflection_vectorized(
    positions: Any,
    velocities: Any,
    hard_boundaries: Any,
    backend_service: BackendService
) -> Tu<PERSON>[Any, Any]:
    """对整个粒子群应用向量化的"硬反射"策略。

    此函数是后端无关的，可用于CPU或GPU。当粒子位置超出边界时，
    将其反射回边界内，并反转对应的速度分量。

    Args:
        positions: 粒子位置矩阵 (n_particles, n_dims)
        velocities: 粒子速度矩阵 (n_particles, n_dims)
        hard_boundaries: 硬边界数组 (n_dims, 2)，每行为 [下界, 上界]
        backend_service: 用于计算的后端服务实例

    Returns:
        tuple: (更新后的 positions, 更新后的 velocities)

    Note:
        硬反射策略适用于Bootstrap+LOWO阶段，确保粒子严格遵守物理约束。

        修复说明 (2025-07-27):
        针对Vmacro_min等小数值范围参数在Bootstrap-LOWO阶段出现负值的问题，
        在硬反射计算后添加边界钳位保险，防止数值不稳定导致的越界现象。
        这是一个防御性编程措施，确保100%的边界约束遵守。

    References:
        《SCAPE_MS_方法说明书》§4.2.1 - 硬反射边界处理
    """
    new_positions = positions.copy()
    new_velocities = velocities.copy()

    lb = hard_boundaries[:, 0]  # 下边界向量
    ub = hard_boundaries[:, 1]  # 上边界向量

    # === 第一步：下边界硬反射处理 ===
    # 检测越过下边界的粒子
    lower_mask = new_positions < lb

    # 对越界粒子进行镜面反射：new_pos = 2 * boundary - old_pos
    # 物理意义：粒子撞到边界后以相同角度反弹
    new_positions = backend_service.where(lower_mask, 2 * lb - new_positions, new_positions)

    # 反转越界粒子在对应维度的速度，模拟弹性碰撞
    new_velocities = backend_service.where(lower_mask, -new_velocities, new_velocities)

    # === 第二步：上边界硬反射处理 ===
    # 检测越过上边界的粒子
    upper_mask = new_positions > ub

    # 对越界粒子进行镜面反射
    new_positions = backend_service.where(upper_mask, 2 * ub - new_positions, new_positions)

    # 反转越界粒子在对应维度的速度
    new_velocities = backend_service.where(upper_mask, -new_velocities, new_velocities)

    # === 第三步：边界钳位保险 (修复关键) ===
    # 问题背景：
    # 在处理Vmacro_min等小数值范围参数时，硬反射策略可能因为数值计算误差、
    # 连续反射振荡等原因导致粒子仍然越界。特别是当下界接近零(如0.01)时，
    # 反射计算 2*lb - pos 容易产生数值不稳定。
    #
    # 解决方案：
    # 在硬反射后添加边界钳位作为最后的安全网，确保任何情况下粒子都不会越界。
    # 对于正常情况，钳位操作不会改变反射结果；
    # 对于异常情况，强制将越界值钳位到边界内。
    #
    # 性能影响：clip是高效的向量化操作，性能开销可忽略
    new_positions = backend_service.clip(new_positions, lb, ub)

    return new_positions, new_velocities


def handle_boundary_jitter_vectorized(
    positions: Any,
    narrow_hard_boundaries: Any,
    parameter_sigmas: Any,
    backend_service: BackendService
) -> Any:
    """对整个粒子群应用向量化的"边界抖动"策略。

    此函数是后端无关的，可用于CPU或GPU。适用于Fine-Tuning阶段，
    当粒子撞到窄窗边界时，先钳位到边界内，再施加小幅随机扰动。

    Args:
        positions: 粒子位置矩阵 (n_particles, n_dims)
        narrow_hard_boundaries: Fine-Tuning阶段的窄窗硬边界 (n_dims, 2)
        parameter_sigmas: 各参数在Bootstrap阶段统计得到的标准差 (n_dims,)
        backend_service: 用于计算的后端服务实例

    Returns:
        Any: 更新后的 positions 矩阵

    Note:
        边界抖动策略适用于Fine-Tuning阶段，在窄窗内提供适度的探索能力

    References:
        《SCAPE_MS_方法说明书》§4.2.2 - 边界抖动处理
    """
    new_positions = positions.copy()

    lb = narrow_hard_boundaries[:, 0]
    ub = narrow_hard_boundaries[:, 1]

    # 钳位：首先将所有粒子拉回边界
    clamped_pos = backend_service.clip(new_positions, lb, ub)

    # 扰动：从边界向界内施加一个小的随机扰动
    # 扰动的标准差为参数本身标准差的10%
    jitter_std = 0.1 * parameter_sigmas
    jitter = backend_service.abs(backend_service.random.normal(0, jitter_std, size=positions.shape))

    # 识别哪些粒子撞到了哪个边界
    lower_mask = new_positions < lb
    upper_mask = new_positions > ub

    # 应用抖动：撞到下边界的向内（正方向）加，撞到上边界的向内（负方向）减
    jittered_pos = backend_service.where(lower_mask, clamped_pos + jitter, clamped_pos)
    jittered_pos = backend_service.where(upper_mask, jittered_pos - jitter, jittered_pos)

    # 最终校验：确保抖动后的位置仍在界内
    final_positions = backend_service.clip(jittered_pos, lb, ub)

    return final_positions
