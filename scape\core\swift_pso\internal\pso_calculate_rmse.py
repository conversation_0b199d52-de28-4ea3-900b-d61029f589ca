"""scape.core.swift_pso.internal.pso_calculate_rmse - RMSE计算核心逻辑

实现加权对数均方根误差计算，严格遵循SCAPE方法说明书的数学定义。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部RMSE计算
设计原则: 纯函数、数值稳定性、公式准确性
性能特征: NumPy优化、内存友好、数值安全

遵循CCG规范：
- SC-1: 算法正确性
- SC-2: 数值稳定性
- TS-1: 完整类型注解

References
----------
- 《SCAPE_MS_方法说明书》§4.4.1 - 公式4-7 加权对数RMSE定义
- 迁移自 scape/core/swift_pso_backup/internal/pso_calculate_rmse.py
"""

from __future__ import annotations

import numpy as np
import pandas as pd


def calculate_rmse(
    k_pred: np.ndarray,
    k_label: np.ndarray,
    pzi: np.ndarray | pd.Series,
    w_prod: float,
    w_nonprod: float
) -> float:
    """计算加权的对数均方根误差 (Weighted Root Mean Squared Logarithmic Error)。

    此函数严格遵循《SCAPE_MS_方法说明书》4.4.1节中公式4-7的定义，
    用于评估模型在验证集上的性能。

    公式:
    RMSE_Val = sqrt( (1 / sum(w_i)) * sum(w_i * (log10(k_pred) - log10(k_label))^2) )
    其中，权重 w_i 由产层指示PZI和配置参数 w_prod, w_nonprod 决定。

    Args:
        k_pred: 模型预测的渗透率值
        k_label: 渗透率的真值标签
        pzi: 产层指示数组 (1=产层, 0=非产层)
        w_prod: 产层样本的权重
        w_nonprod: 非产层样本的权重

    Returns:
        float: 计算出的加权对数RMSE值

    Raises:
        ValueError: 输入数组长度不一致时抛出

    References:
        《SCAPE_MS_方法说明书》§4.4.1 - 公式4-7 加权对数RMSE定义
    """
    # 1. 输入验证和准备
    if len(k_pred) != len(k_label) or len(k_pred) != len(pzi):
        raise ValueError("输入数组 k_pred, k_label, 和 pzi 的长度必须一致。")

    if len(k_pred) == 0:
        return 0.0

    # 2. 为对数计算增加数值稳定性
    # 与 loss.py 中的处理方式保持一致
    epsilon = 1e-9
    k_pred_safe = np.maximum(k_pred, epsilon)
    k_label_safe = np.maximum(k_label, epsilon)

    # 3. 定义样本权重
    # 根据PZI和传入的权重参数定义权重
    weights = np.where(np.asarray(pzi) == 1, w_prod, w_nonprod)

    # 4. 计算加权对数误差
    log_errors = np.log10(k_pred_safe) - np.log10(k_label_safe)
    weighted_squared_log_errors = weights * (log_errors ** 2)

    sum_of_weights = np.sum(weights)
    if sum_of_weights == 0:
        return 0.0

    # 计算加权均方对数误差
    mean_weighted_squared_log_error = np.sum(weighted_squared_log_errors) / sum_of_weights

    # 5. 返回均方根对数误差
    rmse = np.sqrt(mean_weighted_squared_log_error)

    return rmse
