"""tests.scape.core.foster_nmr.test_calculate_foster_nmr

对FOSTER-NMR模型计算函数的全面测试，重点验证CPU和GPU后端的一致性。
"""

import pytest
import numpy as np
import pandas as pd

# 导入待测试的函数
from scape.core.foster_nmr.calculate_foster_nmr import (
    _calculate_foster_nmr_core,
    calculate_foster_nmr_permeability_vectorized_for_loss,
    calculate_foster_nmr_permeability_for_prediction
)

# 导入后端服务和工厂函数
from logwp.extras.backend import BackendService
from logwp.extras.backend.internal.factory import create_backend_service_by_name, is_gpu_available

# Pytest标记，如果GPU (CuPy) 不可用，则跳过相关测试
skip_if_no_gpu = pytest.mark.skipif(not is_gpu_available(), reason="CuPy (GPU backend) is not available")


@pytest.fixture(scope="module")
def foster_nmr_test_data() -> dict:
    """提供一套用于FOSTER-NMR模型计算的标准化测试数据。"""
    # 1. 创建测试DataFrame
    df = pd.DataFrame({
        'PHIT_NMR':  [0.15, 0.20, 0.18],
        'T2LM':      [50.0, 150.0, 100.0],
        'DT2_P50':   [1.1, 0.9, 1.0],
        'DPHIT_NMR': [0.01, -0.01, 0.0],
        'K_LABEL_TYPE': [1, 2, 0],  # CORE, MDT, UNKNOWN
        'T2_VALUE_1': [0.01, 0.02, 0.015],
        'T2_VALUE_2': [0.05, 0.08, 0.065],
        'T2_VALUE_3': [0.09, 0.10, 0.100],
    })

    # 2. 创建T2时间轴
    t2_time = np.array([10.0, 100.0, 1000.0])

    # 3. 创建模型参数
    parameters = {
        'log10_KSDR_A': 0.5, 'PHIT_EXP': 4.0, 'RHO_NMR': 10.0,
        'log10_KMACRO_A': 0.8, 'Vmacro_min': 0.05,
        'log10_T2cutoff_short': 1.8, 'log10_T2cutoff_long': 3.3,
        'beta_1': 0.1, 'beta_2': 1.5, 'delta_MDT': 0.2
    }

    # 4. 创建配置
    config = {
        'curve_to_columns_map': {
            'T2_VALUE': ['T2_VALUE_1', 'T2_VALUE_2', 'T2_VALUE_3']
        }
    }

    # 5. 创建上下文参考值
    context = {
        "t2_p50_ref": 120.0,
        "phit_nmr_ref": 0.19,
    }

    return {
        "well_data": df,
        "t2_time": t2_time,
        "parameters": parameters,
        "config": config,
        "context": context
    }


@pytest.fixture(scope="module")
def cpu_service() -> BackendService:
    """提供CPU后端服务实例。"""
    return create_backend_service_by_name('cpu')


@pytest.fixture(scope="module")
@skip_if_no_gpu
def gpu_service() -> BackendService:
    """提供GPU后端服务实例。"""
    return create_backend_service_by_name('gpu')


class TestFosterNmrConsistency:
    """测试FOSTER-NMR核心计算函数在CPU和GPU上的一致性。"""

    @skip_if_no_gpu
    def test_core_engine_consistency(self, foster_nmr_test_data, cpu_service, gpu_service):
        """测试 _calculate_foster_nmr_core 在CPU和GPU上的结果是否一致。"""
        # 准备输入数据
        data = foster_nmr_test_data
        df = data['well_data']
        t2_value_cols = data['config']['curve_to_columns_map']['T2_VALUE']

        # 准备传递给核心引擎的参数字典
        core_args = {
            "phit_nmr": df['PHIT_NMR'].to_numpy(),
            "t2lm": df['T2LM'].to_numpy(),
            "t2_value_array": df[t2_value_cols].to_numpy(),
            "dt2_p50": df['DT2_P50'].to_numpy(),
            "dphit_nmr": df['DPHIT_NMR'].to_numpy(),
            "sample_types": df['K_LABEL_TYPE'].to_numpy(),
            "t2_time": data['t2_time'],
            "t2_p50_ref": data['context']['t2_p50_ref'],
            "phit_nmr_ref": data['context']['phit_nmr_ref'],
            "t2_range_min": None,
            "t2_range_max": None,
            **data['parameters']
        }

        # 在CPU上计算
        k_cpu = _calculate_foster_nmr_core(**core_args, backend_service=cpu_service)

        # 在GPU上计算前，必须将所有输入数组移动到GPU
        # 这是修复测试失败的关键步骤
        core_args_gpu = {
            key: gpu_service.to_backend(value) if isinstance(value, np.ndarray) else value
            for key, value in core_args.items()
        }

        k_gpu_device = _calculate_foster_nmr_core(**core_args_gpu, backend_service=gpu_service)
        k_gpu_host = gpu_service.to_cpu(k_gpu_device)

        # 验证结果一致性
        assert k_cpu.shape == k_gpu_host.shape
        np.testing.assert_allclose(k_cpu, k_gpu_host, rtol=1e-6, atol=1e-6)

    @skip_if_no_gpu
    def test_vectorized_engine_consistency(self, foster_nmr_test_data, cpu_service, gpu_service):
        """测试 calculate_foster_nmr_permeability_vectorized_for_loss 在CPU和GPU上的结果是否一致。"""
        data = foster_nmr_test_data
        df = data['well_data']
        t2_value_cols = data['config']['curve_to_columns_map']['T2_VALUE']

        # 创建一个包含2个粒子（行）的参数矩阵
        params1 = data['parameters']
        params2 = {k: v * 0.9 for k, v in params1.items()}
        param_keys = list(params1.keys())
        parameters_matrix_np = np.array([list(params1.values()), list(params2.values())])

        # 准备传递给向量化引擎的数据字典
        well_data_arrays = {
            'phit_nmr': df['PHIT_NMR'].to_numpy(),
            't2lm': df['T2LM'].to_numpy(),
            'sample_types': df['K_LABEL_TYPE'].to_numpy(),
            'dt2_p50': df['DT2_P50'].to_numpy(),
            'dphit_nmr': df['DPHIT_NMR'].to_numpy(),
            't2_value_array': df[t2_value_cols].to_numpy(),
            't2_time': data['t2_time'],
            'param_keys': param_keys,
            **data['context']
        }

        # 在CPU上计算
        k_matrix_cpu = calculate_foster_nmr_permeability_vectorized_for_loss(
            parameters_matrix_np, well_data_arrays, cpu_service
        )

        # 在GPU上计算
        parameters_matrix_gpu = gpu_service.to_backend(parameters_matrix_np)
        well_data_arrays_gpu = {k: gpu_service.to_backend(v) for k, v in well_data_arrays.items() if isinstance(v, np.ndarray)}
        well_data_arrays_gpu.update({k: v for k, v in well_data_arrays.items() if not isinstance(v, np.ndarray)})

        k_matrix_gpu_device = calculate_foster_nmr_permeability_vectorized_for_loss(
            parameters_matrix_gpu, well_data_arrays_gpu, gpu_service
        )
        k_matrix_gpu_host = gpu_service.to_cpu(k_matrix_gpu_device)

        # 验证结果一致性
        assert k_matrix_cpu.shape == k_matrix_gpu_host.shape
        np.testing.assert_allclose(k_matrix_cpu, k_matrix_gpu_host, rtol=1e-6, atol=1e-6)


class TestFosterNmrApplication:
    """测试面向应用的高级函数。"""

    @pytest.mark.parametrize("backend_name", ['cpu', pytest.param('gpu', marks=skip_if_no_gpu)])
    def test_predict_permeability(self, foster_nmr_test_data, backend_name):
        """测试 predict_permeability 函数的行为。

        - 验证它在不同后端上都能运行。
        - 验证它始终返回一个位于CPU上的NumPy数组。
        - 验证返回结果的形状正确。
        """
        data = foster_nmr_test_data
        backend_service = create_backend_service_by_name(backend_name)

        model_assets = {
            "parameters": data['parameters'],
            "context": data['context']
        }

        # 测试返回渗透率
        k_pred = calculate_foster_nmr_permeability_for_prediction(
            well_data=data['well_data'],
            model_assets=model_assets,
            t2_time=data['t2_time'],
            config=data['config'],
            backend_service=backend_service,
            return_porosity_components=False
        )

        assert isinstance(k_pred, np.ndarray)
        assert not backend_service.is_on_gpu(k_pred)
        assert k_pred.shape == (len(data['well_data']),)

        # 测试返回孔隙度组分
        results_tuple = calculate_foster_nmr_permeability_for_prediction(
            well_data=data['well_data'],
            model_assets=model_assets,
            t2_time=data['t2_time'],
            config=data['config'],
            backend_service=backend_service,
            return_porosity_components=True
        )

        assert isinstance(results_tuple, tuple)
        assert len(results_tuple) == 4
        for component in results_tuple:
            assert isinstance(component, np.ndarray)
            assert not backend_service.is_on_gpu(component)
            assert component.shape == (len(data['well_data']),)
