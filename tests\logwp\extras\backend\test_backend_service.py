"""tests.logwp.extras.backend.test_backend_service

对后端服务（BackendService）及其工厂函数的全面测试。
"""

import pytest
import numpy as np

# 导入待测试的工厂函数和服务类
from logwp.extras.backend.internal.factory import (
    create_backend_service_by_name,
    create_backend_service_from_data,
    create_backend_service_from_module,
    is_gpu_available
)
from logwp.extras.backend.internal.numpy_service import NumpyService
from logwp.extras.backend.internal.cupy_service import CupyService, _CUPY_AVAILABLE, _CUPY_MODULE

# Pytest标记，如果GPU (CuPy) 不可用，则跳过相关测试
skip_if_no_gpu = pytest.mark.skipif(not _CUPY_AVAILABLE, reason="CuPy (GPU backend) is not available")


# --- 1. 测试工厂函数 ---

class TestBackendFactory:
    """测试后端服务工厂函数的行为。"""

    def test_create_by_name_cpu(self):
        """测试通过名称 'cpu' 创建服务。"""
        service = create_backend_service_by_name('cpu')
        assert isinstance(service, NumpyService)
        assert service.name == 'cpu'

    @skip_if_no_gpu
    def test_create_by_name_gpu(self):
        """测试通过名称 'gpu' 创建服务。"""
        service = create_backend_service_by_name('gpu')
        assert isinstance(service, CupyService)
        assert service.name == 'gpu'

    def test_create_by_name_invalid(self):
        """测试使用无效名称创建服务时应抛出异常。"""
        with pytest.raises(ValueError, match="无效的后端名称"):
            create_backend_service_by_name('tpu')

    @skip_if_no_gpu
    def test_create_from_data(self):
        """测试根据输入数据类型自动创建服务。"""
        np_array = np.array([1, 2, 3])
        cp_array = _CUPY_MODULE.array([1, 2, 3])

        # 仅NumPy数组，应创建NumpyService
        service_np = create_backend_service_from_data(np_array)
        assert isinstance(service_np, NumpyService)

        # 仅CuPy数组，应创建CupyService
        service_cp = create_backend_service_from_data(cp_array)
        assert isinstance(service_cp, CupyService)

        # 混合数组，应优先创建CupyService
        service_mixed = create_backend_service_from_data(np_array, cp_array)
        assert isinstance(service_mixed, CupyService)

    @skip_if_no_gpu
    def test_create_from_module(self):
        """测试根据后端模块（numpy/cupy）创建服务。"""
        service_np = create_backend_service_from_module(np)
        assert isinstance(service_np, NumpyService)

        service_cp = create_backend_service_from_module(_CUPY_MODULE)
        assert isinstance(service_cp, CupyService)

        with pytest.raises(ValueError, match="无效的后端模块"):
            create_backend_service_from_module("not a module")

    def test_is_gpu_available(self):
        """测试GPU可用性检查函数。"""
        assert is_gpu_available() == _CUPY_AVAILABLE


# --- 2. 测试BackendService的具体实现 ---

@pytest.fixture(params=['cpu', pytest.param('gpu', marks=skip_if_no_gpu)])
def backend_service(request):
    """Pytest Fixture: 参数化地提供CPU和GPU后端服务实例。"""
    return create_backend_service_by_name(request.param)


class TestBackendService:
    """对BackendService所有方法的综合性测试。

    通过参数化的 `backend_service` fixture，此测试集会自动在
    NumpyService和CupyService上运行，确保两者行为一致。
    """

    def test_name_property(self, backend_service):
        """测试 .name 属性是否正确返回 'cpu' 或 'gpu'。"""
        if isinstance(backend_service, NumpyService):
            assert backend_service.name == 'cpu'
        else:
            assert backend_service.name == 'gpu'

    def test_as_scalar(self, backend_service):
        """测试 as_scalar() 能否将0维数组正确转换为Python标量。"""
        zero_dim_array = backend_service.to_backend(np.array(42.0))
        assert isinstance(backend_service.as_scalar(zero_dim_array), float)
        assert backend_service.as_scalar(zero_dim_array) == 42.0
        assert isinstance(backend_service.as_scalar(123), int)

    def test_data_transfer(self, backend_service):
        """测试 to_cpu, to_gpu, to_backend, is_on_gpu 等数据传输方法的正确性。"""
        np_array = np.arange(5, dtype=np.float32)

        if backend_service.name == 'cpu':
            # 测试 NumpyService
            backend_array = backend_service.to_backend(np_array)
            assert isinstance(backend_array, np.ndarray)
            assert not backend_service.is_on_gpu(backend_array)

            # to_gpu 在 NumpyService 上应不执行任何操作
            gpu_array_noop = backend_service.to_gpu(backend_array)
            assert isinstance(gpu_array_noop, np.ndarray)
            assert not backend_service.is_on_gpu(gpu_array_noop)
        else:  # backend_service.name == 'gpu'
            # 测试 CupyService
            backend_array = backend_service.to_backend(np_array)
            assert hasattr(backend_array, 'device')  # 检查是否为CuPy数组
            assert backend_service.is_on_gpu(backend_array)

            cpu_array = backend_service.to_cpu(backend_array)
            assert isinstance(cpu_array, np.ndarray)
            assert not backend_service.is_on_gpu(cpu_array)

    def test_array_creation(self, backend_service):
        """测试 zeros, ones, full 等数组创建方法。"""
        shape = (2, 3)
        # zeros
        zeros_arr = backend_service.zeros(shape, dtype=np.int32)
        assert zeros_arr.shape == shape
        assert backend_service.as_scalar(backend_service.sum(zeros_arr)) == 0
        if backend_service.name == 'gpu':
            assert backend_service.is_on_gpu(zeros_arr)

        # full
        full_arr = backend_service.full(shape, 7.7)
        np.testing.assert_allclose(backend_service.to_cpu(full_arr), np.full(shape, 7.7))

        # zeros_like
        zeros_like_arr = backend_service.zeros_like(full_arr)
        assert zeros_like_arr.shape == shape
        assert zeros_like_arr.dtype == full_arr.dtype
        assert backend_service.as_scalar(backend_service.sum(zeros_like_arr)) == 0.0

        # ones_like
        ones_like_arr = backend_service.ones_like(full_arr)
        assert ones_like_arr.shape == shape
        assert ones_like_arr.dtype == full_arr.dtype
        np.testing.assert_allclose(backend_service.to_cpu(ones_like_arr), np.ones(shape))

        # full_like
        full_like_arr = backend_service.full_like(full_arr, -1.0)
        np.testing.assert_allclose(backend_service.to_cpu(full_like_arr), np.full(shape, -1.0))

        # copy
        copied_arr = backend_service.copy(full_arr)
        assert copied_arr is not full_arr  # 确保是副本，而不是引用
        np.testing.assert_allclose(backend_service.to_cpu(copied_arr), backend_service.to_cpu(full_arr))


    def test_math_operations(self, backend_service):
        """测试 sum, mean, std, exp 等数学运算。"""
        data_np = np.array([1, 2, 3, 4], dtype=np.float32)
        data_backend = backend_service.to_backend(data_np)

        assert backend_service.as_scalar(backend_service.sum(data_backend)) == 10.0
        assert backend_service.as_scalar(backend_service.mean(data_backend)) == 2.5
        np.testing.assert_allclose(backend_service.as_scalar(backend_service.std(data_backend)), np.std(data_np))

    def test_logical_operations(self, backend_service):
        """测试 argmin, clip, where 等逻辑和操作函数。"""
        data_np = np.array([-1, 0, 5, 2], dtype=np.float32)
        data_backend = backend_service.to_backend(data_np)

        assert backend_service.as_scalar(backend_service.argmin(data_backend)) == 0

        clipped_arr = backend_service.clip(data_backend, 0, 3)
        expected_clipped = np.array([0, 0, 3, 2], dtype=np.float32)
        np.testing.assert_allclose(backend_service.to_cpu(clipped_arr), expected_clipped)

    def test_random_service(self, backend_service):
        """测试 .random 子服务的行为。"""
        shape = (10, 20)
        rand_arr = backend_service.random.rand(*shape)
        assert rand_arr.shape == shape
        # 验证生成的数组是否在正确的设备上
        assert backend_service.is_on_gpu(rand_arr) == (backend_service.name == 'gpu')
