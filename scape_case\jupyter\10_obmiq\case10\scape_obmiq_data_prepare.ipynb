{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ数据准备 \n", "\n", "- 使用DS_F==1挑选有效层段\n", "- 去掉T-1井下段致密段（6630以下)\n", "- 表格数据使用更多的输入曲线\n", "  - DEN, CN, DT, RD_LOG10, RS_LOG10, DRES\n", "  - T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10\n", "  - PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR\n", "  - VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO\n", "  - LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-30T05:47:46.617335Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.34, 'cpu_percent': 0.0} file_path=santos_data_v1.wp.xlsx\n", "2025-07-30T05:47:46.677660Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.34, 'cpu_percent': 0.0} file_path=santos_data_v1.wp.xlsx file_size_mb=28.86 sheet_count=8\n", "2025-07-30T05:47:46.694806Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.34, 'cpu_percent': 0.0} project_name=santos_data_v1\n", "2025-07-30T05:47:46.714743Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.34, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data_v1\n", "2025-07-30T05:47:46.739205Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.34, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-30T05:47:46.751599Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.35, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-30T05:47:46.771253Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.35, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-07-30T05:47:46.794883Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.35, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-30T05:47:46.818312Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.35, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:47:46.861293Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 600.35, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=74 well_curves=1\n", "2025-07-30T05:48:27.406827Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.83, 'cpu_percent': 0.0} shape=(16303, 263) sheet_name=Logs\n", "2025-07-30T05:48:27.496853Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.11, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-07-30T05:48:27.514373Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.11, 'cpu_percent': 0.0} curve_count=74 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 263) processing_time=40.707\n", "2025-07-30T05:48:27.550513Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.16, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=OBMIQ_Pred dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:27.587476Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.16, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=91 well_curves=1\n", "2025-07-30T05:48:32.326249Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 558.79, 'cpu_percent': 0.0} shape=(4502, 91) sheet_name=OBMIQ_Pred\n", "2025-07-30T05:48:32.356395Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.79, 'cpu_percent': 0.0} dataset_name=WpIdentifier('OBMIQ_Pred')\n", "2025-07-30T05:48:32.366511Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.79, 'cpu_percent': 0.0} curve_count=91 dataset_name=OBMIQ_Pred dataset_type=Continuous df_shape=(4502, 91) processing_time=4.82\n", "2025-07-30T05:48:32.386101Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.81, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-07-30T05:48:32.404528Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.81, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-30T05:48:32.515763Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.84, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-07-30T05:48:32.536278Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.84, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-07-30T05:48:32.554761Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.84, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.174\n", "2025-07-30T05:48:32.586874Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.84, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-30T05:48:32.614783Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.84, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-30T05:48:32.651183Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.84, 'cpu_percent': 0.0} shape=(15, 4) sheet_name=PLT\n", "2025-07-30T05:48:32.676431Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.84, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-30T05:48:32.691570Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(15, 4) processing_time=0.109\n", "2025-07-30T05:48:32.728031Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-30T05:48:32.762234Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-30T05:48:32.793056Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-07-30T05:48:32.814650Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-30T05:48:32.833178Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.112\n", "2025-07-30T05:48:32.857149Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} dataset_count=5 has_head_info=True has_well_map=True\n", "2025-07-30T05:48:32.875782Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} dataset_count=5\n", "2025-07-30T05:48:32.887235Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 556.84, 'cpu_percent': 0.0} dataset_count=5 file_path=santos_data_v1.wp.xlsx processing_time=46.27 project_name=WpIdentifier('santos_data_v1')\n", "2025-07-30T05:48:32.918069Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.5, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data_v1'}\n", "2025-07-30T05:48:32.968388Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.44, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 5}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data_v1\n", "📅 创建时间: 2025-07-30 13:47:46.714743\n", "🔧 默认深度单位: WpDepthUnit.METER\n"]}], "source": ["data_file_path = \"./santos_data_v1.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 创建时间: {project.created_at}\")\n", "    print(f\"🔧 默认深度单位: {project.default_depth_reference_unit}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 生成测井数据概况报告"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 提取OBMIQ累积分布数据集"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 准备提取OBMIQ相关曲线，共31条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR, VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO, LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY, PHI_T2_DIST_CUM, DT2_P50, DPHIT_NMR\n", "📊 准备提取OBMIQ相关曲线（dropna），共29条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM_LOG10, T2LM_LONG_LOG10, T2_P50_LOG10, T2_P20_LOG10, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, FFV_NMR, SWB_NMR, SWI_NMR, SFF_NMR, VMICRO, VMESO, VMACRO, SMICRO, SMESO, SMACRO, LT2STDDEV_FFI, LSKEW_FFI, LKURT_FFI, SDR_PROXY, PHI_T2_DIST_CUM\n"]}], "source": ["# 定义要提取的曲线列表\n", "# obmiq_curves = [\n", "#    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "#    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',\n", "#    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',\n", "#    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "#]\n", "\n", "#log_scout分析结果\n", "obmiq_curves = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "]\n", "\n", "obmiq_curves_dropna = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','FFV_NMR','SWB_NMR', 'SWI_NMR', 'SFF_NMR',\n", "    'VMICRO', 'VME<PERSON>', 'VMACR<PERSON>', 'SMICRO', 'SMESO', 'SMACRO',\n", "    'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM'\n", "]\n", "\n", "print(f\"📊 准备提取OBMIQ相关曲线，共{len(obmiq_curves)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves)}\")\n", "print(f\"📊 准备提取OBMIQ相关曲线（dropna），共{len(obmiq_curves_dropna)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves_dropna)}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始提取OBMIQ数据集(训练)...\n", "\n", "📍 提取C-1井数据(训练)...\n", "2025-07-30T05:48:33.180046Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.66, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T05:48:33.206658Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 499.66, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI'] operation=extract_metadata output_curve_count=33 output_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI']\n", "2025-07-30T05:48:33.520281Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.61, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:33.544676Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.62, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T05:48:33.571104Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.62, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-30T05:48:33.609857Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.62, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:33.643773Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 493.62, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=910\n", "2025-07-30T05:48:33.669547Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.86, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_c_1\n", "2025-07-30T05:48:33.694942Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=910 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.52, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=910 removed_rows=0\n", "2025-07-30T05:48:33.718844Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.52, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T05:48:33.738545Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.52, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:33.756491Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.52, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T05:48:33.770935Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.52, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_c1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_C1') save_head_info=True save_well_map=True\n", "2025-07-30T05:48:33.794353Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.52, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(910, 96)\n", "2025-07-30T05:48:35.082340Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.54, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=1.289\n", "2025-07-30T05:48:35.109222Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.54, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T05:48:35.249958Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.54, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_c1.wp.xlsx processing_time=1.479 project_name=WpIdentifier('Santos_OBMIQ_C1')\n", "✅ C-1井数据已保存: santos_obmiq_cum_c1.wp.xlsx\n", "   数据形状: (910, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取T-1井数据(训练)...\n", "2025-07-30T05:48:35.284249Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.54, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T05:48:35.315335Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 491.54, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI'] operation=extract_metadata output_curve_count=33 output_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI']\n", "2025-07-30T05:48:35.568782Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.3, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:35.606759Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.3, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T05:48:35.643748Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.3, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-30T05:48:35.681265Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.3, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:35.700192Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.3, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=1250\n", "2025-07-30T05:48:35.723641Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.3, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_t_1\n", "2025-07-30T05:48:35.746560Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1250 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.96, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1250 removed_rows=0\n", "2025-07-30T05:48:35.773197Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.96, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T05:48:35.790398Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.96, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:35.804450Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 495.96, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T05:48:35.816380Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 494.84, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_t1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_T1') save_head_info=True save_well_map=True\n", "2025-07-30T05:48:35.845483Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 494.84, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1250, 96)\n", "2025-07-30T05:48:37.475829Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 494.94, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=1.63\n", "2025-07-30T05:48:37.499881Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 494.94, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T05:48:37.700174Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 494.94, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_t1.wp.xlsx processing_time=1.884 project_name=WpIdentifier('Santos_OBMIQ_T1')\n", "✅ T-1井数据已保存: santos_obmiq_cum_t1.wp.xlsx\n", "   数据形状: (1250, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取所有井数据(训练)...\n", "2025-07-30T05:48:37.742319Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 494.94, 'cpu_percent': 0.0} curve_count=31 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-30T05:48:37.774655Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 494.94, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI'] operation=extract_metadata output_curve_count=33 output_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI']\n", "2025-07-30T05:48:38.061923Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 530.02, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:38.108742Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 530.78, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-30T05:48:38.136987Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 530.32, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-30T05:48:38.165013Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 530.32, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:38.189314Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 530.32, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=14362\n", "2025-07-30T05:48:38.226056Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 501.79, 'cpu_percent': 0.0} curve_count=31 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all\n", "2025-07-30T05:48:38.262463Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=2160 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 503.43, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=14362 removed_rows=12202\n", "2025-07-30T05:48:38.281383Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 503.49, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T05:48:38.298311Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 503.49, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:38.315239Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 503.49, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T05:48:38.329180Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 503.49, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All') save_head_info=True save_well_map=True\n", "2025-07-30T05:48:38.358568Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 503.49, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(2160, 96)\n", "2025-07-30T05:48:39.354428Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.996\n", "2025-07-30T05:48:39.384676Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.5, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T05:48:39.415050Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.5, 'cpu_percent': 0.0}\n", "2025-07-30T05:48:39.426220Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.5, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-30T05:48:45.614314Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.54, 'cpu_percent': 0.0}\n", "2025-07-30T05:48:45.633865Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 514.54, 'cpu_percent': 0.0}\n", "2025-07-30T05:48:48.844598Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.53, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx processing_time=10.515 project_name=WpIdentifier('Santos_OBMIQ_All')\n", "✅ 所有井数据已保存: santos_obmiq_cum_all.wp.xlsx\n", "   数据形状: (2160, 96)\n", "   数据集类型: WpContinuousDataset\n", "   井名分布: {'T-1': 1250, 'C-1': 910}\n", "\n", "📍 提取所有井数据(预测)...\n", "2025-07-30T05:48:48.895160Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.51, 'cpu_percent': 0.0} curve_count=31 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq_all_apply\n", "2025-07-30T05:48:48.911142Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 515.51, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=33 input_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI'] operation=extract_metadata output_curve_count=33 output_curves=['BFV_NMR', 'SWB_NMR', 'LSKEW_FFI', 'BVI_NMR', 'DT2_P50', 'T2_P50_LOG10', 'DEN', 'MD', 'T2_P20_LOG10', 'FFV_NMR', 'CN', 'RD_LOG10', 'VMESO', 'DPHIT_NMR', 'RS_LOG10', 'VMICRO', 'PHIT_NMR', 'T2LM_LONG_LOG10', 'DRES', 'PHI_T2_DIST_CUM', 'SDR_PROXY', 'SMACRO', 'SWI_NMR', 'PHIE_NMR', 'T2LM_LOG10', 'WELL_NO', 'SMICRO', 'SMESO', 'DT', 'VMACRO', 'SFF_NMR', 'LT2STDDEV_FFI', 'LKURT_FFI']\n", "2025-07-30T05:48:49.021771Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.11, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:49.044234Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.8, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-30T05:48:49.074484Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.42, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq_all_apply target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-30T05:48:49.099757Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.42, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:49.110957Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.42, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM_LOG10', 'T2LM_LONG_LOG10', 'T2_P50_LOG10', 'T2_P20_LOG10', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'FFV_NMR', 'SWB_NMR', 'SWI_NMR', 'SFF_NMR', 'VMICRO', 'VMESO', 'VMACRO', 'SMICRO', 'SMESO', 'SMACRO', 'LT2STDDEV_FFI', 'LSKEW_FFI', 'LKURT_FFI', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=96 source_rows=16303 target_rows=16303\n", "2025-07-30T05:48:49.146870Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 518.92, 'cpu_percent': 0.0} curve_count=29 dropna_how=any new_dataset=nmr_obmiq_all_apply_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all_apply\n", "2025-07-30T05:48:49.178110Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=4504 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.29, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=11799\n", "2025-07-30T05:48:49.217413Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.45, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-30T05:48:49.240572Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.45, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-30T05:48:49.244435Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.45, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-30T05:48:49.268633Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.45, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All_Apply') save_head_info=True save_well_map=True\n", "2025-07-30T05:48:49.309957Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.45, 'cpu_percent': 0.0} curve_count=33 dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') dataset_type=Continuous df_shape=(4504, 96)\n", "2025-07-30T05:48:51.178977Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 550.18, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') processing_time=1.869\n", "2025-07-30T05:48:51.207013Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 546.93, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-30T05:48:51.232355Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 546.93, 'cpu_percent': 0.0}\n", "2025-07-30T05:48:51.245478Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 546.93, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-30T05:49:03.890383Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 598.38, 'cpu_percent': 0.0}\n", "2025-07-30T05:49:03.917471Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 598.39, 'cpu_percent': 0.0}\n", "2025-07-30T05:49:10.448686Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.07, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=21.18 project_name=WpIdentifier('Santos_OBMIQ_All_Apply')\n", "2025-07-30T05:49:10.466102Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.07, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All_Apply\n", "2025-07-30T05:49:11.333278Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.25, 'cpu_percent': 0.0} file_size=41421 format=markdown output_path=santos_obmiq_cum_all_apply_report.md\n", "✅ 所有井数据(预测）已保存: santos_obmiq_cum_all_apply.wp.xlsx\n", "   数据形状: (4504, 96)\n", "   数据集类型: WpContinuousDataset\n", "\n", "🎉 OBMIQ数据集提取完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "    from logwp.models.well_project import WpWellProject\n", "\n", "    print(\"🔧 开始提取OBMIQ数据集(训练)...\")\n", "\n", "    try:\n", "        # 1. 提取C-1井的数据\n", "        print(\"\\n📍 提取C-1井数据(训练)...\")\n", "        c1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'C-1' and DS_F == 1 \"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_c_1\", c1_dataset)\n", "        c1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_c_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_c1 = WpWellProject(name=\"Santos_OBMIQ_C1\")\n", "        temp_project_c1.add_dataset(\"nmr_obmiq\", c1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        c1_path = \"santos_obmiq_cum_c1.wp.xlsx\"\n", "        writer.write(temp_project_c1, c1_path, apply_formatting=False)\n", "\n", "        print(f\"✅ C-1井数据已保存: {c1_path}\")\n", "        print(f\"   数据形状: {c1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(c1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ C-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 2. 提取T-1井的数据\n", "        print(\"\\n📍 提取T-1井数据(训练)...\")\n", "        t1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'T-1' and DS_F == 1 \"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_t_1\", t1_dataset)\n", "        t1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_t_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_t1 = WpWellProject(name=\"Santos_OBMIQ_T1\")\n", "        temp_project_t1.add_dataset(\"nmr_obmc\", t1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        t1_path = \"santos_obmiq_cum_t1.wp.xlsx\"\n", "        writer.write(temp_project_t1, t1_path, apply_formatting=False)\n", "\n", "        print(f\"✅ T-1井数据已保存: {t1_path}\")\n", "        print(f\"   数据形状: {t1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(t1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ T-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 3. 提取所有井的数据\n", "        print(\"\\n📍 提取所有井数据(训练)...\")\n", "        all_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"DS_F == 1\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all\", all_dataset)\n", "        all_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq\", all_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "\n", "        print(f\"✅ 所有井数据已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_dataset).__name__}\")\n", "\n", "        # 显示井名统计\n", "        if 'WELL_NO' in all_dataset.df.columns:\n", "            well_counts = all_dataset.df['WELL_NO'].value_counts()\n", "            print(f\"   井名分布: {dict(well_counts)}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "\n", "    try:\n", "        # 4. 提取所有井的数据(预测)\n", "        print(\"\\n📍 提取所有井数据(预测)...\")\n", "        all_apply_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves #需要包含真值，这样方便对比\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all_apply\", all_apply_dataset)\n", "        all_apply_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves_dropna, # dropna时不考虑真值\n", "            new_dataset_name=\"nmr_obmiq_all_apply_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All_Apply\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq_apply\", all_apply_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_apply_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据(预测）已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_apply_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_apply_dataset).__name__}\")\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据(预测）提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 OBMIQ数据集提取完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过OBMIQ数据集提取\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}