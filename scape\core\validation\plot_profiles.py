"""validation包的绘图配置模板。

定义并注册 `validation` 模块所有图表的PlotProfile模板。
此模块在 `scape.core.validation` 包被导入时自动执行，
将配置注册到全局 `logwp.extras.plotting.registry` 中。
"""

from __future__ import annotations

from logwp.extras.plotting import PlotProfile, SaveConfig, registry

from .constants import PermCorrelationPlotProfiles, PltAnalysisPlotProfiles

# 1. 定义`validation`模块的【模块级基础模板】
#    它会隐式继承全局的 "base" 模板。
#    我们整合了旧的plt和perm_corr的基础配置，形成一个统一的模块基础。
validation_base_profile = PlotProfile(
    name=PltAnalysisPlotProfiles.BASE.value,  # 使用PLT的BASE作为统一基础
    rc_params={
            "font.family": "Arial",
            "font.size": 11,
            "axes.grid": True,
            "grid.alpha": 0.3,
            "grid.linestyle": "-",
            "axes.edgecolor": "black",
            "axes.linewidth": 1.0,
        },
    figure_props={
        "figsize": (7, 7),
        "dpi": 150,
        "layout": "constrained"
    },
    save_config=SaveConfig(
        format=["png", "svg"],
        dpi=300,
        transparent=False,
        bbox_inches="tight"
    )
)

# 2. 定义PLT分析的【具体图表模板】
#    它们会继承自上面定义的 "validation.plt.base" 模板

contribution_crossplot_profile = PlotProfile(
    name=PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT.value,
    title_props={
            "fontsize": 14,
            "fontweight": "bold",
            "pad": 15
        },
    label_props={
        "xlabel": "Predicted Relative Contribution (R_pred)",
        "ylabel": "PLT Relative Contribution (R_PLT)",
        "fontsize": 12
    },
    artist_props={
        # 散点样式
        "scatter": {
            "s": 60,
            "alpha": 0.7,
            "edgecolor": "white",
            "linewidth": 0.5
        },
        # 1:1参考线样式
        "reference_line": {
            "color": "red",
            "linestyle": "--",
            "linewidth": 1.5,
            "label": "1:1 Line"
        },
        # 文本框样式
        "text_box": {
            "fontsize": 10,
            "bbox": {
                "boxstyle": "round,pad=0.5",
                "facecolor": "wheat",
                "alpha": 0.8
            }
        },
        # 图例配置
        "legend": {
            "loc": "upper left",
            "fontsize": 10
        }
    }
)

capture_curve_profile = PlotProfile(
    name=PltAnalysisPlotProfiles.CAPTURE_CURVE.value,
    title_props={
        "fontsize": 14,
        "fontweight": "bold",
        "pad": 15
    },
    label_props={
        "xlabel": "Cumulative Thickness Fraction (sorted by k_pred)",
        "ylabel": "Cumulative High-Productivity Thickness Fraction",
        "fontsize": 12
    },
    artist_props={
        # 捕获曲线样式
        "capture_line": {
            "color": "blue",
            "linestyle": "-",
            "linewidth": 2.0,
            "marker": "o",
            "markersize": 4,
            "label": "Capture Curve"
        },
        # 随机线样式
        "random_line": {
            "color": "red",
            "linestyle": "--",
            "linewidth": 1.5,
            "label": "Random"
        },
        # 文本框样式
        "text_box": {
            "fontsize": 10,
            "bbox": {
                "boxstyle": "round,pad=0.5",
                "facecolor": "lightblue",
                "alpha": 0.8
            }
        },
        # 图例配置
        "legend": {
            "loc": "lower right",
            "fontsize": 10
        }
    }
)

lorenz_curve_profile = PlotProfile(
    name=PltAnalysisPlotProfiles.LORENZ_CURVE.value,
    title_props={
            "fontsize": 14,
            "fontweight": "bold",
            "pad": 15
        },
    label_props={
        "xlabel": "Cumulative Predicted Contribution (sorted by k_pred)",
        "ylabel": "Cumulative True Contribution (PLT)",
        "fontsize": 12
    },
    artist_props={
        # 洛伦兹曲线样式
        "lorenz_line": {
            "color": "green",
            "linestyle": "-",
            "linewidth": 2.0,
            "marker": "o",
            "markersize": 4,
            "label": "Lorenz Curve"
        },
        # 完美一致线样式
        "perfect_line": {
            "color": "red",
            "linestyle": "--",
            "linewidth": 1.5,
            "label": "Perfect Agreement"
        },
        # 文本框样式
        "text_box": {
            "fontsize": 10,
            "bbox": {
                "boxstyle": "round,pad=0.5",
                "facecolor": "lightgreen",
                "alpha": 0.8
            }
        },
        # 图例配置
        "legend": {
            "loc": "lower right",
            "fontsize": 10
        }
    }
)

# 3. 定义渗透率相关性分析的【具体图表模板】

permeability_crossplot_profile = PlotProfile(
    name=PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT.value,
    # 此模板将继承自 "validation.plt.base"
    title_props={
            "fontsize": 14,
            "fontweight": "bold",
            "pad": 15
        },
    label_props={
        "xlabel": "True Permeability (mD) - from right_bundle",
        "ylabel": "Predicted Permeability (mD) - from left_bundle",
        "fontsize": 12
    },
    artist_props={
        # 散点样式
        "scatter": {
            "alpha": 0.7,
            "s": 60,
            "edgecolor": "white",
            "linewidth": 0.5,
            "label": "Data Points"
        },
        # 1:1线样式
        "line_1to1": {
            "color": "red",
            "linestyle": "-",
            "linewidth": 2.0,
            "label": "1:1 Line"
        },
        # 3x误差边界线样式
        "line_3x": {
            "color": "black",
            "linestyle": "--",
            "linewidth": 1.5,
            "label": "3x Error Bound"
        },
        # 5x误差边界线样式
        "line_5x": {
            "color": "blue",
            "linestyle": "--",
            "linewidth": 1.5,
            "label": "5x Error Bound"
        },
        # 10x误差边界线样式
        "line_10x": {
            "color": "green",
            "linestyle": "--",
            "linewidth": 1.5,
            "label": "10x Error Bound"
        },
        # 文本框样式
        "text_box": {
            "fontsize": 10,
            "bbox": {
                "boxstyle": "round,pad=0.5",
                "facecolor": "aliceblue",
                "alpha": 0.8
            }
        },
        # 图例配置
        "legend": {
            "loc": "lower right",
            "fontsize": 10
        },
        # 坐标轴配置
        "axes": {
            "xscale": "log",
            "yscale": "log",
            "aspect": "equal",
            "adjustable": "box",
            "xlim": [0.01, 1000],
            "ylim": [0.01, 1000]
        },
        # 网格配置
        "grid": {
            "visible": True,
            "which": "both",
            "linestyle": "--",
            "linewidth": 0.5
        }
    }
)

# 4. 向全局注册表注册所有模板
#    注册表会根据名称自动处理继承关系
registry.register_base(validation_base_profile)
registry.register(contribution_crossplot_profile)
registry.register(capture_curve_profile)
registry.register(lorenz_curve_profile)
registry.register(permeability_crossplot_profile)
