"""scape.core.obmiq.internal.plotting_utils - OBMIQ绘图工具函数

提供被多个facade共享的、用于生成和注册图表产物的辅助函数。

Architecture
------------
层次/依赖: scape/core/obmiq/internal层，被facade调用
设计原则: 遵循DRY原则，封装通用逻辑
"""
from __future__ import annotations

from pathlib import Path
from typing import Any, Callable, Dict, Tuple

from logwp.extras.plotting import registry
from logwp.extras.tracking import RunContext

from ..constants import ObmiqPlotProfiles


def generate_and_register_plots(
    ctx: RunContext,
    step_dir: Path,
    plot_map: Dict[str, Tuple[Callable, Dict[str, Any], ObmiqPlotProfiles]],
    snapshot_path: Path,
    description_prefix: str,
) -> None:
    """
    一个通用的绘图生成和注册函数。

    它遍历一个绘图配置字典，为每个条目生成图表，并将其注册为产物。
    """
    for artifact_name, (plot_func, kwargs, profile_name) in plot_map.items():
        plot_path = step_dir / f"{artifact_name.value.split('.')[-1]}.png"
        profile = registry.get(profile_name.value)
        plot_func(snapshot_path=snapshot_path, profile=profile, output_path=plot_path, **kwargs)
        ctx.register_artifact(
            plot_path.relative_to(ctx.run_dir),
            artifact_name.value,
            description=f"{description_prefix}: {artifact_name.value.replace('_', ' ').title()}",
        )
