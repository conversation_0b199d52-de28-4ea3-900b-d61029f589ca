"""scape.core.baselines.hybrid_dnn.facade - Hybrid DNN基准模型门面

实现Hybrid DNN基准模型训练和预测步骤的公共接口。

"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, List

import numpy as np
import pandas as pd
import torch
from logwp.infra import get_logger
from logwp.models.curve import CurveExpansionMode

from .artifact_handler import DnnArtifactHandler
from .config import DnnPredictionConfig, DnnTrainingConfig
from .constants import DnnArtifacts
from .exceptions import DnnDataError
from .internal import final_training_procedure, tuning_procedure
from .internal.model_builder import HybridDnnPermModel

if TYPE_CHECKING:
    from logwp.extras.tracking import RunContext
    from logwp.models.datasets.bundle import WpDataFrameBundle

logger = get_logger(__name__)


def _resolve_feature_selectors(
    bundle: "WpDataFrameBundle",
    sequence_feature: str,
    normalization_feature: str,
    tabular_features: List[str],
    target_feature: str,
    grouping_feature: str,
) -> Dict[str, Any]:
    """将用户提供的逻辑曲线名解析为DataFrame的物理列名。"""
    sequence_cols = bundle.curve_metadata.expand_curve_names(
        [sequence_feature], mode=CurveExpansionMode.DATAFRAME
    )
    normalization_col = bundle.curve_metadata.expand_curve_names(
        [normalization_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    grouping_col = bundle.curve_metadata.expand_curve_names(
        [grouping_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    target_col = bundle.curve_metadata.expand_curve_names(
        [target_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    tabular_cols = bundle.curve_metadata.expand_curve_names(
        tabular_features, mode=CurveExpansionMode.DATAFRAME
    )

    logger.info("曲线名已成功解析为DataFrame列名。")
    return {
        "sequence_cols": sequence_cols,
        "normalization_col": normalization_col,
        "tabular_cols": tabular_cols,
        "target_col": target_col,
        "grouping_col": grouping_col,
    }

def _resolve_feature_selectors_prediction(
    bundle: "WpDataFrameBundle",
    sequence_feature: str,
    normalization_feature: str,
    tabular_features: List[str],
    grouping_feature: str,
) -> Dict[str, Any]:
    """Resolve logical curve names to physical DataFrame column names for prediction."""
    sequence_cols = bundle.curve_metadata.expand_curve_names(
        [sequence_feature], mode=CurveExpansionMode.DATAFRAME
    )
    normalization_col = bundle.curve_metadata.expand_curve_names(
        [normalization_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    grouping_col = bundle.curve_metadata.expand_curve_names(
        [grouping_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    tabular_cols = bundle.curve_metadata.expand_curve_names(
        tabular_features, mode=CurveExpansionMode.DATAFRAME
    )

    logger.info("Curve names successfully resolved to DataFrame column names (Prediction).")
    return {
        "sequence_cols": sequence_cols,
        "normalization_col": normalization_col,
        "tabular_cols": tabular_cols,
        "grouping_col": grouping_col,
    }


def run_dnn_training_step(
    config: DnnTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    *,
    sequence_feature: str,
    normalization_feature: str,
    tabular_features: list[str],
    target_feature: str,
    grouping_feature: str,
    t2_time_axis: np.ndarray,
) -> dict[str, Any]:
    """执行Hybrid DNN基准模型训练、超参数寻优和最终模型交付。"""
    logger.info("===== Hybrid DNN Training Step Started =====")
    step_dir = ctx.get_step_dir("dnn_hybrid_training")
    handler = DnnArtifactHandler()

    # 将本次运行的训练配置保存为产物，用于追溯和复现
    # Pydantic的model_dump()方法可以轻松地将配置转换为可序列化的字典
    config_path = step_dir / "training_config.json"
    handler.save_parameters(config.model_dump(), config_path)
    ctx.register_artifact(
        config_path.relative_to(ctx.run_dir),
        DnnArtifacts.TRAINING_CONFIG.value,
        description="本次DNN训练运行的完整配置参数。"
    )
    logger.info("训练配置已作为产物保存。")

    # 0. 解析与验证
    resolved_selectors = _resolve_feature_selectors(
        bundle=train_bundle,
        sequence_feature=sequence_feature,
        normalization_feature=normalization_feature,
        tabular_features=tabular_features,
        target_feature=target_feature,
        grouping_feature=grouping_feature,
    )

    # 1. 超参数寻优
    logger.info("--- Stage 1: Hyperparameter Tuning using LOWO-CV ---")
    best_hps, cv_report_df, cv_evaluation_results = tuning_procedure.run_hyperparameter_tuning_cv(
        train_df=train_bundle.data, config=config, feature_selectors=resolved_selectors
    )
    logger.info(f"Best hyperparameters found: {best_hps}")

    # 2. 保存交叉验证和超参数寻优报告
    cv_report_path = step_dir / "cv_performance_report.csv"
    handler.save_dataframe(cv_report_df, cv_report_path)
    ctx.register_artifact(
        cv_report_path.relative_to(ctx.run_dir),
        DnnArtifacts.CV_PERFORMANCE_REPORT.value,
    )

    tuning_report_path = step_dir / "hyperparameter_tuning_report.json"
    handler.save_parameters(best_hps, tuning_report_path)
    ctx.register_artifact(
        tuning_report_path.relative_to(ctx.run_dir),
        DnnArtifacts.TUNING_REPORT.value,
    )

    # 3. 最终模型训练
    logger.info("--- Stage 2: Final Model Training ---")
    metadata = {
        "sequence_feature": sequence_feature,
        "normalization_feature": normalization_feature,
        "tabular_features_ordered": resolved_selectors["tabular_cols"],
        "target_feature": target_feature,
        "standard_t2_time_axis": t2_time_axis,
        "grouping_feature": grouping_feature,
    }
    model_assets, history_df, _ = final_training_procedure.train_final_dnn_model(
        train_df=train_bundle.data,
        best_hps=best_hps,
        config=config,
        feature_selectors=resolved_selectors,
        metadata=metadata,
        tb_log_dir=str(step_dir / "tensorboard_logs"),
    )

    # 4. 保存核心产物
    assets_path = step_dir / "model_assets_pytorch.joblib"
    handler.save_model_assets(model_assets, assets_path)
    ctx.register_artifact(
        assets_path.relative_to(ctx.run_dir),
        DnnArtifacts.MODEL_ASSETS.value,
    )

    history_snapshot_path = step_dir / "final_training_history.csv"
    handler.save_dataframe(history_df, history_snapshot_path)
    ctx.register_artifact(
        history_snapshot_path.relative_to(ctx.run_dir),
        DnnArtifacts.FINAL_TRAINING_HISTORY_DATA.value,
    )

    logger.info("===== Hybrid DNN Training Step Finished =====")
    return {"status": "completed", "best_hyperparameters": best_hps}


def run_dnn_prediction_step(
    config: DnnPredictionConfig,
    ctx: RunContext,
    model_assets: dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    *,
    output_curve_name: str,
) -> dict[str, Any]:
    """使用训练好的Hybrid DNN模型进行渗透率预测。"""
    logger.info("===== Hybrid DNN Prediction Step Started =====")
    step_dir = ctx.get_step_dir("dnn_hybrid_prediction")
    handler = DnnArtifactHandler()
    device = "cuda" if torch.cuda.is_available() else "cpu"

    # 1. 解包模型资产
    try:
        model_state_dict = model_assets["model_state_dict"]
        hps = model_assets["model_hyperparameters"]
        preprocessors = model_assets["preprocessors"]
        metadata = model_assets["metadata"]
        tabular_scaler = preprocessors["tabular_scaler"]
        target_transformer = preprocessors.get("target_transformer") # e.g., "log10"
    except KeyError as e:
        raise DnnDataError(f"模型资产中缺少必需的键: {e}")

    # 2. 解析特征选择器
    pred_selectors = _resolve_feature_selectors_prediction(
        bundle=prediction_bundle,
        sequence_feature=metadata["sequence_feature"],
        normalization_feature=metadata["normalization_feature"],
        tabular_features=metadata["tabular_features_ordered"],
        grouping_feature=metadata["grouping_feature"], # Retrieve from model assets metadata
    )

    # 3. 重建模型
    num_tabular_features = len(pred_selectors["tabular_cols"])
    model = HybridDnnPermModel(hps, num_tabular_features).to(device)
    model.load_state_dict(model_state_dict)
    model.eval()

    # 4. 准备预测数据
    df_pred = prediction_bundle.data.copy()
    df_pred[pred_selectors["tabular_cols"]] = tabular_scaler.transform(
        df_pred[pred_selectors["tabular_cols"]]
    )

    sequence_values = df_pred[pred_selectors["sequence_cols"]].values
    norm_values = df_pred[pred_selectors["normalization_col"]].values[:, np.newaxis]
    norm_values[norm_values == 0] = 1e-6
    normalized_sequence = sequence_values / norm_values

    tabular_values = df_pred[pred_selectors["tabular_cols"]].values

    # 5. 执行预测
    all_predictions = []
    with torch.no_grad():
        # 简单的批量预测，未来可优化为DataLoader
        for i in range(len(df_pred)):
            seq_tensor = torch.from_numpy(normalized_sequence[i].astype(np.float32)).unsqueeze(0).unsqueeze(0).to(device)
            tab_tensor = torch.from_numpy(tabular_values[i].astype(np.float32)).unsqueeze(0).to(device)
            inputs = {"sequence_input": seq_tensor, "tabular_input": tab_tensor}
            prediction = model(inputs)
            all_predictions.append(prediction.cpu().numpy())

    predictions_np = np.concatenate(all_predictions, axis=0).flatten()

    # 6. 逆变换
    if target_transformer == "log10":
        final_predictions = 10**predictions_np
    else:
        final_predictions = predictions_np # No transformation

    # 7. 准备输出DataFrame
    identifier_df = prediction_bundle.get_identifier_dataframe()
    results_df = pd.DataFrame({output_curve_name: final_predictions})
    prediction_df = pd.concat([identifier_df, results_df], axis=1)

    # 8. 将预测曲线添加回Bundle
    prediction_bundle.add_1d_curve(
        curve_name=output_curve_name,
        curve_data=final_predictions,
        unit="mD",
        description="Permeability from Hybrid DNN baseline model",
        overwrite=True,
    )

    # 9. 保存和注册产物
    prediction_path = step_dir / "dnn_hybrid_predictions.csv"
    handler.save_dataframe(prediction_df, prediction_path)
    ctx.register_artifact(
        prediction_path.relative_to(ctx.run_dir),
        DnnArtifacts.PREDICTIONS.value,
    )

    # 10. 记录指标
    ctx.log_metrics(
        {
            "predicted_samples": len(prediction_df),
            "predicted_permeability_mean": np.nanmean(final_predictions),
            "predicted_permeability_min": np.nanmin(final_predictions),
            "predicted_permeability_max": np.nanmax(final_predictions),
        },
        step_name="dnn_hybrid_prediction",
    )

    logger.info(
        "===== Hybrid DNN Prediction Step Finished =====",
        predicted_samples=len(prediction_df),
        output_curve=output_curve_name,
    )
    return {"status": "completed", "predicted_samples": len(prediction_df)}
