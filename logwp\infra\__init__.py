from __future__ import annotations

"""logwp.infra - 基础工具包

提供项目中最基础、与业务模型无关的横切功能。

Architecture
------------
层次/依赖: `logwp`包的最底层工具层，不依赖`logwp/models`。
设计原则: 纯函数、无状态、高内聚、低耦合。

Core Features
-------------
- **日志系统**: 提供基于structlog的结构化日志 (`get_logger`, `configure_logging`)。
- **性能监控**: 提供用于监控函数执行时间、内存和GPU使用的装饰器 (`performance_monitor`)。
- **深度单位处理**: 提供深度单位的标准化和转换工具 (`standardize_depth_unit`)。
- **GPU工具**: 提供GPU环境检测和基础操作 (`is_gpu_available`, `get_gpu_info`)。
- **颜色处理**: 提供颜色表示和转换的工具 (`hex_to_rgba`)。

Package Structure
-----------------
- `logging_config.py`: 结构化日志配置。
- `performance.py`: 性能监控工具。
- `depth_utils.py`: 深度单位处理工具。
- `gpu_utils.py`: GPU环境检测和辅助函数。
- `color_utils.py`: 颜色处理工具。
- `exceptions.py`: `utils`层特定的异常。
"""

__all__ = [
    # 日志系统
    "configure_logging",
    "get_logger",
    # 性能监控
    "performance_monitor",
    "memory_monitor",
    "gpu_monitor",
    "PerformanceStats",
    # 深度单位处理
    "standardize_depth_unit",
    "are_depth_units_equivalent",
    "get_supported_depth_units",
    "validate_depth_unit",
    "get_unit_conversion_factor",
    "format_depth_value",
    # 颜色处理
    "hex_to_rgba",
]




def __getattr__(name: str) -> object:
    """延迟导入工具函数，提升启动性能。"""
    if name == "hex_to_rgba":
        from .color_utils import hex_to_rgba
        return hex_to_rgba

    if name in ("configure_logging", "get_logger"):
        from .logging_config import configure_logging, get_logger
        return locals()[name]

    if name in ("performance_monitor", "memory_monitor", "gpu_monitor", "PerformanceStats"):
        from .performance import performance_monitor, memory_monitor, gpu_monitor, PerformanceStats
        return locals()[name]

    if name in ("standardize_depth_unit", "are_depth_units_equivalent", "get_supported_depth_units",
                "validate_depth_unit", "get_unit_conversion_factor", "format_depth_value"):
        from .depth_utils import (
            standardize_depth_unit, are_depth_units_equivalent, get_supported_depth_units,
            validate_depth_unit, get_unit_conversion_factor, format_depth_value
        )
        return locals()[name]

    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
