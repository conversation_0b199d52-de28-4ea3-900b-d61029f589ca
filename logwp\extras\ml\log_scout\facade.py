"""Facade for the LogScout step.

This module provides the public entry point `run_log_scout_step` for executing
the feature analysis workflow. It orchestrates calls to internal modules for
computation and plotting, and handles artifact registration with the tracking context.
"""

from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Literal

import matplotlib.pyplot as plt

if TYPE_CHECKING:
    from pathlib import Path

    import pandas as pd

    from logwp.extras.plotting import PlotProfile
    from logwp.extras.tracking import RunContext

from logwp.extras.plotting import registry as plot_registry

from .artifact_handler import LogScoutArtifactHandler
from .config import LogScoutConfig
from .constants import LogScoutArtifacts, LogScoutPlotProfiles, LogScoutPlotTypes
from .internal import computer, plotter


from logwp.infra import get_logger
logger = get_logger(__name__)

def run_log_scout_step(
    config: LogScoutConfig,
    ctx: RunContext,
    data: pd.DataFrame,
    features: list[str],
    target: str,
    *,
    task_type: Literal["regression", "classification"],
    prefix: str,
    plot_profiles: dict[str, PlotProfile] | None = None,
) -> dict[str, str]:
    """Executes the LogScout feature analysis step.

    This function performs a systematic analysis of a dataset to diagnose
    relationships between input features and the target, identify collinearity,
    and generate a comprehensive set of artifacts for decision support in
    machine learning modeling.

    Args:
        config: The Pydantic configuration object for the step.
        ctx: The current run context for tracking.
        data: A pandas DataFrame containing all feature and target columns.
        features: A list of column names to be used as input features.
        target: The column name of the target variable.
        task_type: The type of machine learning task, either 'regression' or
            'classification'. This affects the type of target relationship
            plots generated.
        prefix: A prefix for the artifact output directory, allowing multiple
            LogScout runs within the same workflow to be distinguished.
        plot_profiles: An optional dictionary to override default plot styles.
            The keys should be plot type names (e.g., 'heatmap', 'regplot')
            and values should be `PlotProfile` instances.

    Returns:
        A dictionary containing the status of the analysis.
    """
    logger.info(
        "开始 LogScout 特征分析步骤",
        prefix=prefix,
        task_type=task_type,
        num_features=len(features),
        target=target,
    )

    # 1. 设置产物目录
    step_dir = ctx.get_step_dir(f"{prefix}_log_scout")
    quantitative_report_dir = step_dir / "quantitative_report"
    visual_report_dir = step_dir / "visual_report"
    feature_corr_dir = visual_report_dir / "feature_correlation"
    target_rel_dir = visual_report_dir / "target_relationship"

    # 确保所有目录都存在
    quantitative_report_dir.mkdir(parents=True, exist_ok=True)
    feature_corr_dir.mkdir(parents=True, exist_ok=True)
    target_rel_dir.mkdir(parents=True, exist_ok=True)

    # 2. 数据预处理 (删除NaN)
    initial_rows = len(data)
    columns_to_check = [*features, target]
    clean_data = data[columns_to_check].dropna().reset_index(drop=True)
    dropped_rows = initial_rows - len(clean_data)

    if dropped_rows > 0:
        logger.warning(
            f"数据预处理: 删除了 {dropped_rows} 行含有NaN值的样本。",
            initial_rows=initial_rows,
            final_rows=len(clean_data),
        )
    if len(clean_data) == 0:
        logger.error("预处理后无有效数据, 步骤终止。")
        ctx.failure("No valid data after preprocessing.")
        return {"status": "error", "message": "No valid data after preprocessing."}

    # 3. 调用 internal.computer 进行定量分析
    logger.info("开始定量分析计算...")
    pearson_corr, spearman_corr = computer.compute_correlations(clean_data, features)
    logger.info("相关系数矩阵计算完成。")

    vif_df = computer.compute_vif(clean_data, features)
    logger.info("VIF分数计算完成。")

    mi_df = computer.compute_mutual_information(
        clean_data, features, target, task_type
    )
    logger.info("互信息分数计算完成。")

    # 4. 保存并注册定量分析产物
    handler = LogScoutArtifactHandler()

    # Pearson Correlation (作为报告和数据快照注册)
    pearson_path = quantitative_report_dir / "pearson_correlation.csv"
    handler.save_dataframe(pearson_corr, pearson_path)
    ctx.register_artifact(
        artifact_path=pearson_path.relative_to(ctx.run_dir),
        artifact_name=LogScoutArtifacts.PEARSON_CORR.value,
        description="输入特征间的皮尔逊相关系数矩阵。"
    )
    ctx.register_artifact(
        artifact_path=pearson_path.relative_to(ctx.run_dir),
        artifact_name=LogScoutArtifacts.PEARSON_CORR_DATA.value,
        description="用于生成皮尔逊相关性热力图的数据快照。"
    )

    # Spearman Correlation (作为报告和数据快照注册)
    spearman_path = quantitative_report_dir / "spearman_correlation.csv"
    handler.save_dataframe(spearman_corr, spearman_path)
    ctx.register_artifact(
        artifact_path=spearman_path.relative_to(ctx.run_dir),
        artifact_name=LogScoutArtifacts.SPEARMAN_CORR.value,
        description="输入特征间的斯皮尔曼相关系数矩阵。"
    )
    ctx.register_artifact(
        artifact_path=spearman_path.relative_to(ctx.run_dir),
        artifact_name=LogScoutArtifacts.SPEARMAN_CORR_DATA.value,
        description="用于生成斯皮尔曼相关性热力图的数据快照。"
    )

    # VIF Scores
    vif_path = quantitative_report_dir / "vif_scores.csv"
    handler.save_dataframe(vif_df, vif_path)
    ctx.register_artifact(
        artifact_path=vif_path.relative_to(ctx.run_dir),
        artifact_name=LogScoutArtifacts.VIF_SCORES.value,
        description="每个输入特征的方差膨胀因子(VIF)得分。"
    )

    # Mutual Information
    mi_path = quantitative_report_dir / "mutual_information.csv"
    handler.save_dataframe(mi_df, mi_path)
    ctx.register_artifact(
        artifact_path=mi_path.relative_to(ctx.run_dir),
        artifact_name=LogScoutArtifacts.MUTUAL_INFO.value,
        description="每个输入特征与目标特征之间的互信息得分。"
    )

    logger.info("所有定量分析产物已保存并注册。")

    # 5. 可视化分析
    logger.info("开始可视化分析...")

    # 5.1 Pearson 相关性图表
    _generate_correlation_plots(
        ctx=ctx,
        corr_matrix=pearson_corr,
        corr_type="pearson",
        output_dir=feature_corr_dir,
        handler=handler,
        user_profiles=plot_profiles,
    )

    # 5.2 Spearman 相关性图表
    _generate_correlation_plots(
        ctx=ctx,
        corr_matrix=spearman_corr,
        corr_type="spearman",
        output_dir=feature_corr_dir,
        handler=handler,
        user_profiles=plot_profiles,
    )

    logger.info("特征相关性图表已生成。")

    # 5.3 Pairplot (散点图矩阵)
    if len(features) > config.pairplot_max_features:
        logger.warning(
            f"输入特征数量 ({len(features)}) 超过配置上限 ({config.pairplot_max_features}), "
            "跳过 Pairplot 生成以避免性能问题。",
            extra={
                "feature_count": len(features),
                "max_features": config.pairplot_max_features,
            },
        )
    else:
        logger.info("正在生成 Pairplot...")
        # 保存数据快照
        pairplot_data_path = feature_corr_dir / "pairplot_data.csv"
        handler.save_dataframe(clean_data[features], pairplot_data_path)
        ctx.register_artifact(
            artifact_path=pairplot_data_path.relative_to(ctx.run_dir),
            artifact_name=LogScoutArtifacts.PAIRPLOT_DATA.value,
            description="用于生成散点图矩阵(pairplot)的数据快照。",
        )

        # 获取绘图配置
        pairplot_profile = _get_plot_profile(
            LogScoutPlotProfiles.PAIRPLOT.value,
            LogScoutPlotTypes.PAIRPLOT.value,
            plot_profiles,
        )

        # 生成、保存并注册图表
        pairplot_fig = plotter.plot_pairplot(
            clean_data, features, pairplot_profile, "Feature Pairplot"
        )
        pairplot_path = feature_corr_dir / "pairplot.png"
        handler.save_figure(pairplot_fig, pairplot_path)
        ctx.register_artifact(
            artifact_path=pairplot_path.relative_to(ctx.run_dir),
            artifact_name=LogScoutArtifacts.PAIRPLOT.value,
            description="输入特征间的散点图矩阵。",
        )
        plt.close(pairplot_fig)
        logger.info("Pairplot 已生成。")

    # 5.4 Feature vs. Target Plots
    _generate_target_relationship_plots(
        ctx=ctx,
        data=clean_data,
        features=features,
        target=target,
        task_type=task_type,
        target_rel_dir=target_rel_dir,
        handler=handler,
        user_profiles=plot_profiles,
    )

    # 6. 返回状态
    return {
        "status": "analysis_completed",
        "message": "LogScout分析已成功完成, 所有产物已生成。",
    }


def _get_plot_profile(
    profile_name: str,
    profile_type: str,
    user_profiles: dict[str, PlotProfile] | None = None,
) -> PlotProfile:
    """获取正确的绘图配置, 优先使用用户传入的配置."""
    user_profiles = user_profiles or {}
    if profile_type in user_profiles:
        logger.debug(f"使用用户自定义的绘图配置: {profile_type}")
        return user_profiles[profile_type]
    logger.debug(f"使用默认的绘图配置: {profile_name}")
    return plot_registry.get(profile_name)


def _generate_correlation_plots(
    ctx: RunContext,
    corr_matrix: pd.DataFrame,
    corr_type: str,
    output_dir: Path,
    handler: LogScoutArtifactHandler,
    user_profiles: dict[str, PlotProfile] | None,
) -> None:
    """生成、保存并注册指定类型的相关性热力图和聚类图."""
    # --- Heatmap ---
    heatmap_profile = _get_plot_profile(
        LogScoutPlotProfiles.HEATMAP.value,
        getattr(LogScoutPlotTypes, f"{corr_type.upper()}_HEATMAP").value,
        user_profiles,
    )
    heatmap_fig = plotter.plot_heatmap(corr_matrix, heatmap_profile, f"{corr_type.capitalize()} Correlation Heatmap")
    heatmap_path = output_dir / f"{corr_type}_heatmap.png"
    handler.save_figure(heatmap_fig, heatmap_path)
    ctx.register_artifact(artifact_path=heatmap_path.relative_to(ctx.run_dir), artifact_name=getattr(LogScoutArtifacts, f"{corr_type.upper()}_HEATMAP").value)
    plt.close(heatmap_fig)  # 关闭图像以释放内存

    # --- Clustermap ---
    clustermap_profile = _get_plot_profile(
        LogScoutPlotProfiles.CLUSTERMAP.value,
        getattr(LogScoutPlotTypes, f"{corr_type.upper()}_CLUSTERMAP").value,
        user_profiles,
    )
    clustermap_fig = plotter.plot_clustermap(corr_matrix, clustermap_profile, f"{corr_type.capitalize()} Correlation Clustermap")
    clustermap_path = output_dir / f"{corr_type}_clustermap.png"
    handler.save_figure(clustermap_fig, clustermap_path)
    ctx.register_artifact(artifact_path=clustermap_path.relative_to(ctx.run_dir), artifact_name=getattr(LogScoutArtifacts, f"{corr_type.upper()}_CLUSTERMAP").value)
    plt.close(clustermap_fig)


def _generate_target_relationship_plots(
    ctx: RunContext,
    data: pd.DataFrame,
    features: list[str],
    target: str,
    task_type: Literal["regression", "classification"],
    target_rel_dir: Path,
    handler: LogScoutArtifactHandler,
    user_profiles: dict[str, PlotProfile] | None,
) -> None:
    """为每个输入特征生成与目标的关系图."""
    logger.info("正在为每个输入特征生成与目标的关系图...")

    for feature in features:
        if task_type == "regression":
            plot_type = LogScoutPlotTypes.REGRESSION_PLOT.value
            profile_name = LogScoutPlotProfiles.REGPLOT.value
            plot_func = plotter.plot_regression_target
            plot_name_prefix = f"regplot_{feature}_vs_{target}"
            title = f"Feature vs. Target: {feature} vs. {target}"
        else:  # classification
            plot_type = LogScoutPlotTypes.BOX_PLOT.value
            profile_name = LogScoutPlotProfiles.BOXPLOT.value
            plot_func = plotter.plot_classification_target
            plot_name_prefix = f"boxplot_{feature}_by_{target}"
            title = f"Feature Distribution by Target: {feature} by {target}"

        # 1. 获取绘图配置
        profile = _get_plot_profile(profile_name, plot_type, user_profiles)

        # 2. 保存数据快照
        snapshot_data = data[[feature, target]]
        snapshot_path = target_rel_dir / f"{plot_name_prefix}_data.csv"
        handler.save_dataframe(snapshot_data, snapshot_path)

        # 3. 注册数据快照产物
        data_artifact_name = (
            f"{LogScoutArtifacts.TARGET_RELATIONSHIP_DATA_PREFIX.value}."
            f"{plot_name_prefix}"
        )
        ctx.register_artifact(
            artifact_path=snapshot_path.relative_to(ctx.run_dir),
            artifact_name=data_artifact_name,
            description=f"Data snapshot for {plot_name_prefix} plot.",
        )

        # 4. 生成、保存并注册图表
        fig = plot_func(data, feature, target, profile, title)
        plot_path = target_rel_dir / f"{plot_name_prefix}.png"
        handler.save_figure(fig, plot_path)

        plot_artifact_name = (
            f"{LogScoutArtifacts.TARGET_RELATIONSHIP_PLOT_PREFIX.value}."
            f"{plot_name_prefix}"
        )
        ctx.register_artifact(
            artifact_path=plot_path.relative_to(ctx.run_dir),
            artifact_name=plot_artifact_name,
            description=f"Plot of {feature} vs. {target}.",
        )
        plt.close(fig)

    logger.info("特征与目标关系图已全部生成。")
