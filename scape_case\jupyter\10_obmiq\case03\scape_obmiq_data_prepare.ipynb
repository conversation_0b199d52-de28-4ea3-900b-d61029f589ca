{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ数据准备 - 挑选更多的曲线"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-24T15:06:22.564010Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 126.89, 'cpu_percent': 0.0}\n", "库导入完成!\n"]}], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:06:43.695949Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 350.56, 'cpu_percent': 0.0} file_path=..\\..\\01_data\\santos_data.wp.xlsx\n", "2025-07-24T15:06:43.730703Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.51, 'cpu_percent': 0.0} file_path=..\\..\\01_data\\santos_data.wp.xlsx file_size_mb=24.05 sheet_count=7\n", "2025-07-24T15:06:43.741190Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.52, 'cpu_percent': 0.0} project_name=santos_data\n", "2025-07-24T15:06:43.749676Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.52, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_data\n", "2025-07-24T15:06:43.757060Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.53, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-24T15:06:43.763290Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.53, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-24T15:06:43.796145Z [warning  ] 井头信息行缺少必填字段，已跳过                [logwp.io.wp_excel.internal.head_info_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.54, 'cpu_percent': 0.0} row_data=(None, None, None, None, None, None, '`', None, None) row_index=8 sheet_name=_Head_Info\n", "2025-07-24T15:06:43.803868Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.54, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-24T15:06:43.822699Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 351.68, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=Logs dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:06:43.857278Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 352.04, 'cpu_percent': 0.0} curve_2d_groups=3 depth_curves=1 total_curves=62 well_curves=1\n", "2025-07-24T15:07:05.145598Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 426.97, 'cpu_percent': 0.0} shape=(16303, 251) sheet_name=Logs\n", "2025-07-24T15:07:05.174368Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.48, 'cpu_percent': 0.0} dataset_name=WpIdentifier('Logs')\n", "2025-07-24T15:07:05.179483Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.48, 'cpu_percent': 0.0} curve_count=62 dataset_name=Logs dataset_type=Continuous df_shape=(16303, 251) processing_time=21.359\n", "2025-07-24T15:07:05.191153Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Label dataset_type=Point operation=dataset_initialization\n", "2025-07-24T15:07:05.238544Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.52, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-24T15:07:05.292677Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} shape=(506, 9) sheet_name=K_Label\n", "2025-07-24T15:07:05.301683Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Label')\n", "2025-07-24T15:07:05.310482Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Label dataset_type=Point df_shape=(506, 9) processing_time=0.122\n", "2025-07-24T15:07:05.324388Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-24T15:07:05.335128Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-24T15:07:05.351549Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} shape=(8, 4) sheet_name=PLT\n", "2025-07-24T15:07:05.392411Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-24T15:07:05.404061Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(8, 4) processing_time=0.081\n", "2025-07-24T15:07:05.420684Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-24T15:07:05.432955Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.66, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-24T15:07:05.446536Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.7, 'cpu_percent': 0.0} shape=(45, 9) sheet_name=K_Val\n", "2025-07-24T15:07:05.458958Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.7, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-24T15:07:05.470104Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.7, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(45, 9) processing_time=0.052\n", "2025-07-24T15:07:05.508354Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.7, 'cpu_percent': 0.0} dataset_count=4 has_head_info=True has_well_map=True\n", "2025-07-24T15:07:05.519922Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.7, 'cpu_percent': 0.0} dataset_count=4\n", "2025-07-24T15:07:05.529856Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.7, 'cpu_percent': 0.0} dataset_count=4 file_path=..\\..\\01_data\\santos_data.wp.xlsx processing_time=21.834 project_name=WpIdentifier('santos_data')\n", "2025-07-24T15:07:05.546611Z [info     ] 开始井名映射                         [logwp.models.internal.well_mapping] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.7, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'create_new': False, 'project_name': 'santos_data'}\n", "2025-07-24T15:07:05.576623Z [info     ] 井名映射完成（就地模式）                   [logwp.models.internal.well_mapping] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 428.05, 'cpu_percent': 0.0} extra={'operation': 'apply_well_mapping', 'processed_datasets': 4}\n", "✅ 成功读取原始wp文件\n", "📊 项目名称: santos_data\n", "📅 创建时间: 2025-07-24 23:06:43.749676\n", "🔧 默认深度单位: WpDepthUnit.METER\n"]}], "source": ["data_file_path = \"../../01_data/santos_data.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 创建时间: {project.created_at}\")\n", "    print(f\"🔧 默认深度单位: {project.default_depth_reference_unit}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 生成测井数据概况报告"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 提取OBMIQ累积分布数据集"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 准备提取OBMIQ相关曲线，共20条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM, T2LM_LONG, T2_P50, T2_P20, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, SWB_NMR, SWI_NMR, SDR_PROXY, PHI_T2_DIST_CUM, DT2_P50, DPHIT_NMR\n", "📊 准备提取OBMIQ相关曲线（dropna），共18条曲线\n", "曲线列表: DEN, CN, DT, RD_LOG10, RS_LOG10, DRES, T2LM, T2LM_LONG, T2_P50, T2_P20, PHIT_NMR, PHIE_NMR, BFV_NMR, BVI_NMR, SWB_NMR, SWI_NMR, SDR_PROXY, PHI_T2_DIST_CUM\n"]}], "source": ["# 定义要提取的曲线列表\n", "# obmiq_curves = [\n", "#    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "#    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',\n", "#    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',\n", "#    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "#]\n", "\n", "#log_scout分析结果\n", "obmiq_curves = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM', 'DT2_P50', 'DPHIT_NMR'\n", "]\n", "\n", "obmiq_curves_dropna = [\n", "    'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "    'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20',\n", "    'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',\n", "    'PHI_T2_DIST_CUM'\n", "]\n", "\n", "print(f\"📊 准备提取OBMIQ相关曲线，共{len(obmiq_curves)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves)}\")\n", "print(f\"📊 准备提取OBMIQ相关曲线（dropna），共{len(obmiq_curves_dropna)}条曲线\")\n", "print(f\"曲线列表: {', '.join(obmiq_curves_dropna)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 开始提取OBMIQ数据集(训练)...\n", "\n", "📍 提取C-1井数据(训练)...\n", "2025-07-24T15:07:30.218838Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.17, 'cpu_percent': 0.0} curve_count=20 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-24T15:07:30.238785Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 427.17, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=22 input_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY'] operation=extract_metadata output_curve_count=22 output_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY']\n", "2025-07-24T15:07:30.371425Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.96, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:30.385990Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-24T15:07:30.398512Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.07, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-24T15:07:30.412897Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.07, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:30.422914Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.07, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'SWB_NMR', 'SWI_NMR', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=85 source_rows=16303 target_rows=1085\n", "2025-07-24T15:07:30.442263Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.08, 'cpu_percent': 0.0} curve_count=20 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_c_1\n", "2025-07-24T15:07:30.456881Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1056 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.09, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1085 removed_rows=29\n", "2025-07-24T15:07:30.499665Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.09, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-24T15:07:30.517926Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.09, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:30.528789Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.09, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-24T15:07:30.534561Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.09, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_c1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_C1') save_head_info=True save_well_map=True\n", "2025-07-24T15:07:30.550708Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.1, 'cpu_percent': 0.0} curve_count=22 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1056, 85)\n", "2025-07-24T15:07:31.200299Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.29, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.65\n", "2025-07-24T15:07:31.209669Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.29, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-24T15:07:31.285737Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.39, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_c1.wp.xlsx processing_time=0.751 project_name=WpIdentifier('Santos_OBMIQ_C1')\n", "2025-07-24T15:07:31.291879Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 432.39, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_C1\n", "2025-07-24T15:07:31.526873Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.88, 'cpu_percent': 0.0} file_size=13219 format=markdown output_path=santos_obmiq_cum_c1_report.md\n", "✅ C-1井数据已保存: santos_obmiq_cum_c1.wp.xlsx\n", "   数据形状: (1056, 85)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取T-1井数据(训练)...\n", "2025-07-24T15:07:31.547816Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.88, 'cpu_percent': 0.0} curve_count=20 has_query=True operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-24T15:07:31.562558Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 431.88, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=22 input_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY'] operation=extract_metadata output_curve_count=22 output_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY']\n", "2025-07-24T15:07:31.712728Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.8, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:31.724604Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.8, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-24T15:07:31.733949Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.8, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.15239999999994325 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.15239999999994325\n", "2025-07-24T15:07:31.751114Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.8, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:31.761269Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.8, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'SWB_NMR', 'SWI_NMR', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=85 source_rows=16303 target_rows=1661\n", "2025-07-24T15:07:31.772457Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 435.8, 'cpu_percent': 0.0} curve_count=20 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_t_1\n", "2025-07-24T15:07:31.789864Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=1596 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.83, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=1661 removed_rows=65\n", "2025-07-24T15:07:31.802182Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-24T15:07:31.815547Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.86, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:31.823562Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.86, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-24T15:07:31.833510Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=False context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.86, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_t1.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_T1') save_head_info=True save_well_map=True\n", "2025-07-24T15:07:31.882310Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.86, 'cpu_percent': 0.0} curve_count=22 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(1596, 85)\n", "2025-07-24T15:07:32.869612Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 437.94, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.987\n", "2025-07-24T15:07:32.879047Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.91, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-24T15:07:33.001229Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.96, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_t1.wp.xlsx processing_time=1.168 project_name=WpIdentifier('Santos_OBMIQ_T1')\n", "2025-07-24T15:07:33.008766Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.96, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_T1\n", "2025-07-24T15:07:33.151620Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.98, 'cpu_percent': 0.0} file_size=13199 format=markdown output_path=santos_obmiq_cum_t1_report.md\n", "✅ T-1井数据已保存: santos_obmiq_cum_t1.wp.xlsx\n", "   数据形状: (1596, 85)\n", "   数据集类型: WpContinuousDataset\n", "\n", "📍 提取所有井数据(训练)...\n", "2025-07-24T15:07:33.167546Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.98, 'cpu_percent': 0.0} curve_count=20 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq\n", "2025-07-24T15:07:33.181735Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 436.98, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=22 input_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY'] operation=extract_metadata output_curve_count=22 output_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY']\n", "2025-07-24T15:07:33.236073Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 478.49, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:33.252796Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 479.12, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-24T15:07:33.267888Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 479.13, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-24T15:07:33.286539Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 479.13, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:33.297721Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 479.13, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'SWB_NMR', 'SWI_NMR', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=85 source_rows=16303 target_rows=16303\n", "2025-07-24T15:07:33.309118Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 448.53, 'cpu_percent': 0.0} curve_count=20 dropna_how=any new_dataset=nmr_obmiq_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all\n", "2025-07-24T15:07:33.325651Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=2652 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 450.29, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=13651\n", "2025-07-24T15:07:33.340312Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 450.36, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-24T15:07:33.358167Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 450.36, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:33.378111Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 450.36, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-24T15:07:33.385644Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 450.36, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All') save_head_info=True save_well_map=True\n", "2025-07-24T15:07:33.438326Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 450.36, 'cpu_percent': 0.0} curve_count=22 dataset_name=WpIdentifier('nmr_obmiq_cleaned') dataset_type=Continuous df_shape=(2652, 85)\n", "2025-07-24T15:07:34.048326Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 470.11, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_cleaned') processing_time=0.61\n", "2025-07-24T15:07:34.057600Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 468.41, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-24T15:07:34.075864Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 468.41, 'cpu_percent': 0.0}\n", "2025-07-24T15:07:34.086791Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 468.41, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-24T15:07:37.688688Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 496.33, 'cpu_percent': 0.0}\n", "2025-07-24T15:07:37.695573Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 496.34, 'cpu_percent': 0.0}\n", "2025-07-24T15:07:39.571127Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 495.28, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx processing_time=6.185 project_name=WpIdentifier('Santos_OBMIQ_All')\n", "2025-07-24T15:07:39.582125Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 495.28, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All\n", "2025-07-24T15:07:39.895485Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 495.44, 'cpu_percent': 0.0} file_size=28987 format=markdown output_path=santos_obmiq_cum_all_report.md\n", "✅ 所有井数据已保存: santos_obmiq_cum_all.wp.xlsx\n", "   数据形状: (2652, 85)\n", "   数据集类型: WpContinuousDataset\n", "   井名分布: {'T-1': 1596, 'C-1': 1056}\n", "\n", "📍 提取所有井数据(预测)...\n", "2025-07-24T15:07:39.924232Z [info     ] 开始曲线提取                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 495.44, 'cpu_percent': 0.0} curve_count=20 has_query=False operation=extract_curves source_dataset=logs target_dataset=nmr_obmiq_all_apply\n", "2025-07-24T15:07:39.944854Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 495.44, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=22 input_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY'] operation=extract_metadata output_curve_count=22 output_curves=['T2_P20', 'BVI_NMR', 'T2LM', 'PHIT_NMR', 'WELL_NO', 'RD_LOG10', 'DRES', 'RS_LOG10', 'PHIE_NMR', 'DT2_P50', 'MD', 'T2LM_LONG', 'SWB_NMR', 'DPHIT_NMR', 'SWI_NMR', 'T2_P50', 'CN', 'PHI_T2_DIST_CUM', 'DEN', 'DT', 'BFV_NMR', 'SDR_PROXY']\n", "2025-07-24T15:07:40.036590Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.11, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=temp_nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:40.050913Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.74, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.03809999999975844\n", "2025-07-24T15:07:40.064758Z [info     ] 智能类型检测完成                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.74, 'cpu_percent': 0.0} is_uniform_sampling=True operation=smart_type_detection sampling_interval=0.03809999999975844 source_type=WpContinuousDataset target_dataset=nmr_obmiq_all_apply target_type=WpContinuousDataset type_reason=等间隔采样，间隔=0.03809999999975844\n", "2025-07-24T15:07:40.077552Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.74, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:40.089629Z [info     ] 曲线提取完成                         [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 533.74, 'cpu_percent': 0.0} dataframe_columns=['WELL_NO', 'MD', 'DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES', 'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20', 'PHIT_NMR', 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR', 'SWB_NMR', 'SWI_NMR', 'SDR_PROXY', 'PHI_T2_DIST_CUM_1', 'PHI_T2_DIST_CUM_2', 'PHI_T2_DIST_CUM_3', 'PHI_T2_DIST_CUM_4', 'PHI_T2_DIST_CUM_5', 'PHI_T2_DIST_CUM_6', 'PHI_T2_DIST_CUM_7', 'PHI_T2_DIST_CUM_8', 'PHI_T2_DIST_CUM_9', 'PHI_T2_DIST_CUM_10', 'PHI_T2_DIST_CUM_11', 'PHI_T2_DIST_CUM_12', 'PHI_T2_DIST_CUM_13', 'PHI_T2_DIST_CUM_14', 'PHI_T2_DIST_CUM_15', 'PHI_T2_DIST_CUM_16', 'PHI_T2_DIST_CUM_17', 'PHI_T2_DIST_CUM_18', 'PHI_T2_DIST_CUM_19', 'PHI_T2_DIST_CUM_20', 'PHI_T2_DIST_CUM_21', 'PHI_T2_DIST_CUM_22', 'PHI_T2_DIST_CUM_23', 'PHI_T2_DIST_CUM_24', 'PHI_T2_DIST_CUM_25', 'PHI_T2_DIST_CUM_26', 'PHI_T2_DIST_CUM_27', 'PHI_T2_DIST_CUM_28', 'PHI_T2_DIST_CUM_29', 'PHI_T2_DIST_CUM_30', 'PHI_T2_DIST_CUM_31', 'PHI_T2_DIST_CUM_32', 'PHI_T2_DIST_CUM_33', 'PHI_T2_DIST_CUM_34', 'PHI_T2_DIST_CUM_35', 'PHI_T2_DIST_CUM_36', 'PHI_T2_DIST_CUM_37', 'PHI_T2_DIST_CUM_38', 'PHI_T2_DIST_CUM_39', 'PHI_T2_DIST_CUM_40', 'PHI_T2_DIST_CUM_41', 'PHI_T2_DIST_CUM_42', 'PHI_T2_DIST_CUM_43', 'PHI_T2_DIST_CUM_44', 'PHI_T2_DIST_CUM_45', 'PHI_T2_DIST_CUM_46', 'PHI_T2_DIST_CUM_47', 'PHI_T2_DIST_CUM_48', 'PHI_T2_DIST_CUM_49', 'PHI_T2_DIST_CUM_50', 'PHI_T2_DIST_CUM_51', 'PHI_T2_DIST_CUM_52', 'PHI_T2_DIST_CUM_53', 'PHI_T2_DIST_CUM_54', 'PHI_T2_DIST_CUM_55', 'PHI_T2_DIST_CUM_56', 'PHI_T2_DIST_CUM_57', 'PHI_T2_DIST_CUM_58', 'PHI_T2_DIST_CUM_59', 'PHI_T2_DIST_CUM_60', 'PHI_T2_DIST_CUM_61', 'PHI_T2_DIST_CUM_62', 'PHI_T2_DIST_CUM_63', 'PHI_T2_DIST_CUM_64', 'DT2_P50', 'DPHIT_NMR'] operation=extract_curves selected_columns=85 source_rows=16303 target_rows=16303\n", "2025-07-24T15:07:40.137290Z [info     ] 开始数据集dropna操作                  [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 503.14, 'cpu_percent': 0.0} curve_count=18 dropna_how=any new_dataset=nmr_obmiq_all_apply_cleaned operation=dropna_dataset source_dataset=nmr_obmiq_all_apply\n", "2025-07-24T15:07:40.165502Z [info     ] dropna操作完成                     [logwp.models.datasets.internal.dataset_dropna] cleaned_rows=4504 context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 506.14, 'cpu_percent': 0.0} operation=dropna_dataset original_rows=16303 removed_rows=11799\n", "2025-07-24T15:07:40.177738Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 506.28, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-24T15:07:40.189419Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 506.28, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_all_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:07:40.199468Z [info     ] 深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。 [logwp.models.datasets.internal.dataset_dropna] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 506.28, 'cpu_percent': 0.0} operation=dropna_dataset\n", "2025-07-24T15:07:40.208099Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 506.28, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx project_name=WpIdentifier('Santos_OBMIQ_All_Apply') save_head_info=True save_well_map=True\n", "2025-07-24T15:07:40.221420Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 506.28, 'cpu_percent': 0.0} curve_count=22 dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') dataset_type=Continuous df_shape=(4504, 85)\n", "2025-07-24T15:07:41.335200Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 523.06, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_all_apply_cleaned') processing_time=1.114\n", "2025-07-24T15:07:41.346351Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 520.18, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-24T15:07:41.356759Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 520.19, 'cpu_percent': 0.0}\n", "2025-07-24T15:07:41.368279Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 520.19, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-24T15:07:47.548223Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 567.5, 'cpu_percent': 0.0}\n", "2025-07-24T15:07:47.553870Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 567.51, 'cpu_percent': 0.0}\n", "2025-07-24T15:07:50.628687Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 570.59, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=10.421 project_name=WpIdentifier('Santos_OBMIQ_All_Apply')\n", "2025-07-24T15:07:50.638594Z [info     ] 开始生成数据概况报告                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 570.59, 'cpu_percent': 0.0} format=markdown project_name=Santos_OBMIQ_All_Apply\n", "2025-07-24T15:07:51.069499Z [info     ] 数据概况报告生成完成                     [logwp.models.internal.summary_service] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 571.49, 'cpu_percent': 0.0} file_size=36986 format=markdown output_path=santos_obmiq_cum_all_apply_report.md\n", "✅ 所有井数据(预测）已保存: santos_obmiq_cum_all_apply.wp.xlsx\n", "   数据形状: (4504, 85)\n", "   数据集类型: WpContinuousDataset\n", "\n", "🎉 OBMIQ数据集提取完成！\n"]}], "source": ["if project is not None:\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "    from logwp.models.well_project import WpWellProject\n", "\n", "    print(\"🔧 开始提取OBMIQ数据集(训练)...\")\n", "\n", "    try:\n", "        # 1. 提取C-1井的数据\n", "        print(\"\\n📍 提取C-1井数据(训练)...\")\n", "        c1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'C-1'\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_c_1\", c1_dataset)\n", "        c1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_c_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_c1 = WpWellProject(name=\"Santos_OBMIQ_C1\")\n", "        temp_project_c1.add_dataset(\"nmr_obmiq\", c1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        c1_path = \"santos_obmiq_cum_c1.wp.xlsx\"\n", "        writer.write(temp_project_c1, c1_path, apply_formatting=False)\n", "        report_path = temp_project_c1.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_c1_report.md\"\n", "        )\n", "\n", "        print(f\"✅ C-1井数据已保存: {c1_path}\")\n", "        print(f\"   数据形状: {c1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(c1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ C-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 2. 提取T-1井的数据\n", "        print(\"\\n📍 提取T-1井数据(训练)...\")\n", "        t1_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves,\n", "            query_condition=\"${WELL_NO} == 'T-1'\"\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_t_1\", t1_dataset)\n", "        t1_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_t_1\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_t1 = WpWellProject(name=\"Santos_OBMIQ_T1\")\n", "        temp_project_t1.add_dataset(\"nmr_obmc\", t1_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        t1_path = \"santos_obmiq_cum_t1.wp.xlsx\"\n", "        writer.write(temp_project_t1, t1_path, apply_formatting=False)\n", "        report_path = temp_project_t1.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_t1_report.md\"\n", "        )\n", "\n", "        print(f\"✅ T-1井数据已保存: {t1_path}\")\n", "        print(f\"   数据形状: {t1_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(t1_dataset).__name__}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ T-1井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    try:\n", "        # 3. 提取所有井的数据\n", "        print(\"\\n📍 提取所有井数据(训练)...\")\n", "        all_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq\",\n", "            curve_names=obmiq_curves\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all\", all_dataset)\n", "        all_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all\",\n", "            curve_names=obmiq_curves,\n", "            new_dataset_name=\"nmr_obmiq_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq\", all_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_dataset).__name__}\")\n", "\n", "        # 显示井名统计\n", "        if 'WELL_NO' in all_dataset.df.columns:\n", "            well_counts = all_dataset.df['WELL_NO'].value_counts()\n", "            print(f\"   井名分布: {dict(well_counts)}\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "\n", "    try:\n", "        # 4. 提取所有井的数据(预测)\n", "        print(\"\\n📍 提取所有井数据(预测)...\")\n", "        all_apply_dataset = project.extract_curves(\n", "            source_dataset=\"logs\",\n", "            target_dataset=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves #需要包含真值，这样方便对比\n", "        )\n", "        project.add_dataset(\"nmr_obmiq_all_apply\", all_apply_dataset)\n", "        all_apply_dataset = project.dropna_dataset(\n", "            source_dataset_name=\"nmr_obmiq_all_apply\",\n", "            curve_names=obmiq_curves_dropna, # dropna时不考虑真值\n", "            new_dataset_name=\"nmr_obmiq_all_apply_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        # 创建临时项目并保存\n", "        temp_project_all = WpWellProject(name=\"Santos_OBMIQ_All_Apply\")\n", "        temp_project_all.add_dataset(\"nmr_obmiq_apply\", all_apply_dataset)\n", "\n", "        writer = WpExcelWriter()\n", "        all_path = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "        writer.write(temp_project_all, all_path, apply_formatting=True)\n", "        report_path = temp_project_all.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"santos_obmiq_cum_all_apply_report.md\"\n", "        )\n", "\n", "        print(f\"✅ 所有井数据(预测）已保存: {all_path}\")\n", "        print(f\"   数据形状: {all_apply_dataset.df.shape}\")\n", "        print(f\"   数据集类型: {type(all_apply_dataset).__name__}\")\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 所有井数据(预测）提取失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 OBMIQ数据集提取完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过OBMIQ数据集提取\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}