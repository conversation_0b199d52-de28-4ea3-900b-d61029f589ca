"""数据格式化服务。

提供数据类型转换、曲线名称映射、Excel格式化等核心转换功能。
支持WFS规范的数据格式要求和Excel写入优化。

Architecture
------------
层次/依赖: I/O层内部服务，被dataset_writer等调用
设计原则: 无状态函数、类型安全、格式转换
性能特征: 高效转换、内存优化、批量处理

Functions
---------
- extract_excel_curve_order: 提取Excel写入的曲线顺序
- format_value_for_excel: 格式化单个值用于Excel写入
- format_dataframe_for_excel: 格式化DataFrame用于Excel写入
- create_curve_name_mapping: 创建曲线名称映射关系

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》§4 - 数据转换策略
- 《SCAPE_WFS_WP文件规范.md》- WFS v1.0规范
"""

from __future__ import annotations

import json
from typing import Any, Sequence

import numpy as np
import pandas as pd
import structlog

from logwp.models.constants import WpDataType, WpDsType, WpCurveCategory, WpCurveDimension
from logwp.models.curve.metadata import CurveMetadata, CurveBasicAttributes
from logwp.models.datasets.base import WpDepthIndexedDatasetBase
from logwp.models.exceptions import WpValidationError
from logwp.infra.exceptions import ErrorContext
from logwp.io.constants import WpXlsxKey
from ..config import ExcelFormattingConfig

logger = structlog.get_logger(__name__)

__all__ = [
    "extract_excel_curve_order",
    "format_value_for_excel",
    "format_dataframe_for_excel",
    "create_curve_name_mapping",
]


def extract_excel_curve_order(
    metadata: CurveMetadata,
    dataset_type: WpDsType
) -> list[tuple[str, str]]:
    """提取Excel写入的曲线顺序。

    按照WFS规范要求的顺序：井名列 → 深度列 → 其它曲线。
    使用CurveBasicAttributes.name和element_names，与DataFrame列名无关。

    Args:
        metadata: 曲线元数据管理器
        dataset_type: 数据集类型

    Returns:
        list[tuple[str, str]]: [(excel_curve_name, dataframe_column_name), ...]

    Examples:
        >>> # Continuous数据集
        >>> order = extract_excel_curve_order(metadata, WpDsType.CONTINUOUS)
        >>> # [("WELL", "WELL"), ("MD", "MD"), ("GR", "GR"), ("T2_VALUE[1]", "T2_VALUE_1"), ...]
        >>>
        >>> # Interval数据集
        >>> order = extract_excel_curve_order(metadata, WpDsType.INTERVAL)
        >>> # [("WELL", "WELL"), ("MD_Top", "MD_Top"), ("MD_Bottom", "MD_Bottom"), ("GR", "GR"), ...]

    References:
        《SCAPE_DDS_logwp_io_write_wp_excel.md》§4.1 - 曲线名称和顺序策略
    """
    well_curves = []    # 井名曲线
    depth_curves = []   # 深度曲线
    other_curves = []   # 其它曲线

    for curve in metadata.curves.values():
        # 井名曲线
        if curve.is_well_identifier:
            if curve.dimension == WpCurveDimension.TWO_D_COMP:
                # 二维组合曲线使用元素名
                for i, element_name in enumerate(curve.element_names or []):
                    df_col_name = curve.dataframe_element_names[i] if curve.dataframe_element_names else element_name
                    well_curves.append((element_name, df_col_name))
            else:
                well_curves.append((curve.name, curve.dataframe_column_name))

        # 深度曲线
        elif curve.depth_role is not None:
            if curve.dimension == WpCurveDimension.TWO_D_COMP:
                # 二维组合曲线使用元素名
                for i, element_name in enumerate(curve.element_names or []):
                    df_col_name = curve.dataframe_element_names[i] if curve.dataframe_element_names else element_name
                    depth_curves.append((element_name, df_col_name))
            else:
                depth_curves.append((curve.name, curve.dataframe_column_name))

        # 其它曲线
        else:
            if curve.dimension == WpCurveDimension.TWO_D_COMP:
                # 二维组合曲线使用元素名
                for i, element_name in enumerate(curve.element_names or []):
                    df_col_name = curve.dataframe_element_names[i] if curve.dataframe_element_names else element_name
                    other_curves.append((element_name, df_col_name))
            else:
                other_curves.append((curve.name, curve.dataframe_column_name))

    # 按WFS顺序合并
    result = well_curves + depth_curves + other_curves

    logger.debug(
        "提取曲线写入顺序",
        dataset_type=dataset_type.value,
        total_curves=len(result),
        well_curves=len(well_curves),
        depth_curves=len(depth_curves),
        other_curves=len(other_curves)
    )

    return result


def format_value_for_excel(
    value: Any,
    data_type: WpDataType,
    config: ExcelFormattingConfig
) -> Any:
    """格式化单个值用于Excel写入。

    根据数据类型和配置进行适当的格式化转换。

    Args:
        value: 要格式化的值
        data_type: 数据类型
        config: 格式化配置

    Returns:
        Any: 格式化后的值

    Examples:
        >>> config = ExcelFormattingConfig()
        >>>
        >>> # 布尔值转换
        >>> format_value_for_excel(True, WpDataType.BOOL, config)  # "T"
        >>> format_value_for_excel(False, WpDataType.BOOL, config)  # "N"
        >>>
        >>> # 空值处理
        >>> format_value_for_excel(np.nan, WpDataType.FLOAT, config)  # None
        >>>
        >>> # COMP类型转JSON字符串
        >>> comp_data = {"axis": [1, 2, 3], "values": [10, 20, 30]}
        >>> format_value_for_excel(comp_data, WpDataType.COMP, config)  # JSON字符串
    """
    # 处理空值
    if pd.isna(value) or value is None:
        return None

    # 布尔值转换
    if data_type == WpDataType.BOOL:
        if isinstance(value, bool):
            return config.bool_true_value if value else config.bool_false_value
        elif isinstance(value, str):
            # 字符串布尔值转换
            true_values = {v.upper() for v in WpXlsxKey.bool_true_values()}
            if value.upper() in true_values:
                return config.bool_true_value
            else:
                return config.bool_false_value
        else:
            # 数值布尔值转换
            return config.bool_true_value if value else config.bool_false_value

    # COMP类型转JSON字符串（WFS格式特有）
    elif data_type == WpDataType.COMP:
        if isinstance(value, (dict, list)):
            try:
                return json.dumps(value, ensure_ascii=False, indent=2)
            except (TypeError, ValueError) as e:
                logger.warning("COMP类型JSON序列化失败", value=str(value), error=str(e))
                return str(value)
        else:
            return str(value)

    # 字符串类型
    elif data_type == WpDataType.STR:
        return str(value) if value is not None else None

    # 数值类型保持原样（Excel会自动处理格式）
    elif data_type in (WpDataType.INT, WpDataType.FLOAT):
        return value

    # 其他类型转字符串
    else:
        return str(value)


def format_dataframe_for_excel(
    df: pd.DataFrame,
    curve_order: list[tuple[str, str]],
    metadata: CurveMetadata,
    config: ExcelFormattingConfig
) -> pd.DataFrame:
    """格式化DataFrame用于Excel写入。

    按照指定的曲线顺序重新排列列，并进行数据类型转换。

    Args:
        df: 原始DataFrame
        curve_order: 曲线顺序列表 [(excel_curve_name, dataframe_column_name), ...]
        metadata: 曲线元数据管理器
        config: 格式化配置

    Returns:
        pd.DataFrame: 格式化后的DataFrame，列名为Excel曲线名

    Raises:
        WpValidationError: DataFrame列不匹配
    """
    # 检查DataFrame列是否存在
    missing_columns = []
    for excel_name, df_col_name in curve_order:
        if df_col_name not in df.columns:
            missing_columns.append(df_col_name)

    if missing_columns:
        raise WpValidationError(
            f"DataFrame缺少必需的列: {missing_columns}",
            context=ErrorContext(
                operation="format_dataframe_for_excel",
                additional_info={
                    "missing_columns": missing_columns,
                    "available_columns": list(df.columns),
                    "required_columns": [col for _, col in curve_order]
                }
            )
        )

    # 创建新的DataFrame，按Excel曲线顺序排列
    formatted_data = {}

    for excel_name, df_col_name in curve_order:
        # 获取曲线元数据
        curve = metadata.get_curve_by_dataframe_name(df_col_name)
        if curve is None:
            logger.warning("未找到曲线元数据", dataframe_column=df_col_name)
            # 使用原始数据
            formatted_data[excel_name] = df[df_col_name]
            continue

        # 格式化列数据
        original_series = df[df_col_name]
        formatted_series = original_series.apply(
            lambda x: format_value_for_excel(x, curve.data_type, config)
        )

        formatted_data[excel_name] = formatted_series

    # 创建格式化后的DataFrame
    formatted_df = pd.DataFrame(formatted_data)

    logger.debug(
        "DataFrame格式化完成",
        original_shape=df.shape,
        formatted_shape=formatted_df.shape,
        column_count=len(curve_order)
    )

    return formatted_df


def create_curve_name_mapping(
    metadata: CurveMetadata
) -> dict[str, str]:
    """创建曲线名称映射关系。

    建立Excel曲线名（CurveBasicAttributes.name/element_names）到
    DataFrame列名（dataframe_column_name/dataframe_element_names）的映射。

    Args:
        metadata: 曲线元数据管理器

    Returns:
        dict[str, str]: {excel_curve_name: dataframe_column_name}

    Examples:
        >>> mapping = create_curve_name_mapping(metadata)
        >>> # {
        >>> #     "GR": "GR",
        >>> #     "T2_VALUE[1]": "T2_VALUE_1",
        >>> #     "T2_VALUE[2]": "T2_VALUE_2",
        >>> #     ...
        >>> # }
    """
    mapping = {}

    for curve in metadata.curves.values():
        if curve.dimension == WpCurveDimension.TWO_D_COMP:
            # 二维组合曲线：元素名 → DataFrame元素列名
            if curve.element_names and curve.dataframe_element_names:
                for excel_name, df_name in zip(curve.element_names, curve.dataframe_element_names):
                    mapping[excel_name] = df_name
        else:
            # 一维曲线：曲线名 → DataFrame列名
            mapping[curve.name] = curve.dataframe_column_name

    logger.debug(
        "创建曲线名称映射",
        total_mappings=len(mapping),
        curve_count=len(metadata.curves)
    )

    return mapping
