# `scape.core.baselines.hybrid_dnn` 组件说明

**版本: 1.0**

## 1. 引言

### 1.1. 目标与定位

`hybrid_dnn` 组件是为 `SWIFT-PSO` 渗透率模型提供的一个先进的、纯数据驱动的**基准模型 (Baseline)**。它的核心目标是采用“棋逢对手”的策略，为评估 `SWIFT-PSO` 物理约束模型的性能提供一个高水准的参照。

该组件实现了一个**混合输入深度学习模型 (Hybrid Input DNN)**，其架构灵感来源于 `scape.core.obmiq` 组件，旨在最大化地利用不同类型测井数据的独特信息，特别是使用1D-CNN来处理T2核磁共振谱。

通过将精心设计的 `FOSTER-NMR` (物理约束) 与同样精心设计的 `Hybrid DNN` (纯数据驱动)进行对比，我们可以更清晰地评估物理知识在渗透率建模任务中的实际贡献。

### 1.2. 核心特性

*   **混合架构**: 结合1D-CNN和MLP，分别处理序列和表格化测井数据。
*   **自动化寻优**: 使用 `Optuna` 和 `LOWO-CV` (留一井交叉验证) 进行稳健的超参数搜索。
*   **PyTorch后端**: 完全基于PyTorch实现，支持GPU加速。
*   **完全可追踪**: 深度集成`logwp`框架，所有配置、参数、指标和产物均可被精确追踪和复现。

---

## 2. 核心概念对照表

下表将《框架》中的核心概念与`hybrid_dnn`包中的具体实现一一对应：

| 框架概念 | `hybrid_dnn` 中的具体实现 |
| :--- | :--- |
| **多步骤包** | `scape.core.baselines.hybrid_dnn` 整个包 |
| **步骤 (Step)** | 1. DNN训练步骤 <br> 2. DNN预测步骤 |
| **门面 (Facade)** | `facade.py` |
| **主执行函数** | 1. `run_dnn_training_step()` <br> 2. `run_dnn_prediction_step()` |
| **配置 (Config)** | 1. `config.DnnTrainingConfig` <br> 2. `config.DnnPredictionConfig` |
| **内部逻辑 (Internal)** | `internal/tuning_procedure.py` <br> `internal/final_training_procedure.py` |
| **产物常量** | `constants.DnnArtifacts` |
| **产物处理器** | `artifact_handler.DnnArtifactHandler` |

---

## 3. 组件架构与目录结构

`hybrid_dnn`作为一个标准的多步骤包，其目录结构如下：

```
scape/core/baselines/hybrid_dnn/
├── __init__.py               # 导出公共API
├── README.md                 # 本文档
├── facade.py                 # 【必须】定义 run_dnn_training_step, run_dnn_prediction_step
├── config.py                 # 【必须】定义 DnnTrainingConfig, DnnPredictionConfig
├── constants.py              # 【推荐】产物常量
├── exceptions.py             # (可选) 自定义异常
├── artifact_handler.py       # 【推荐】产物处理器
├── plotting.py               # (可选) 绘图复现功能
├── plot_profiles.py          # (可选) 注册绘图模板
└── internal/                 # 【必须】存放所有内部实现细节的目录
    ├── __init__.py
    ├── model_builder.py      # PyTorch模型定义 (HybridDnnPermModel)
    ├── data_handler.py       # PyTorch Dataset/DataLoader 创建
    ├── tuning_procedure.py   # Optuna + LOWO-CV 寻优规程
    └── final_training_procedure.py # 最终模型训练规程
```

---

## 4. API 使用指南

### 4.1. 训练步骤

#### **函数签名**
```python
def run_dnn_training_step(
    config: DnnTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    *,
    sequence_feature: str,
    normalization_feature: str,
    tabular_features: list[str],
    target_feature: str,
    grouping_feature: str,
    t2_time_axis: np.ndarray,
) -> dict[str, Any]:
```

#### **参数详解**
*   `config`: `DnnTrainingConfig` 的实例，控制训练流程（如迭代次数、批大小等）。
*   `ctx`: `RunContext` 实例，用于追踪。
*   `train_bundle`: `WpDataFrameBundle` 实例，包含所有训练数据。
*   `sequence_feature`: **序列特征**的逻辑名，例如 `'PHI_T2_DIST'`。
*   `normalization_feature`: 用于对序列特征进行**逐样本归一化**的逻辑名，例如 `'PHIT_NMR'`。
*   `tabular_features`: **表格化特征**的逻辑名列表，例如 `['PHIT_NMR', 'T2LM']`。
*   `target_feature`: **目标特征**的逻辑名，例如 `'K_LABEL'`。
*   `grouping_feature`: 用于**LOWO-CV分组**的列的逻辑名，例如 `'WELL_NO'`。
*   `t2_time_axis`: 标准的T2时间轴 `np.ndarray`，将随模型一同保存。

### 4.2. 预测步骤

#### **函数签名**
```python
def run_dnn_prediction_step(
    config: DnnPredictionConfig,
    ctx: RunContext,
    model_assets: dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    *,
    output_curve_name: str,
) -> dict[str, Any]:
```

#### **参数详解**
*   `config`: `DnnPredictionConfig` 的实例。
*   `ctx`: `RunContext` 实例。
*   `model_assets`: 从训练步骤产出的**模型资产包**，通常通过 `ctx.load_artifact()` 加载。
*   `prediction_bundle`: 包含待预测数据的 `WpDataFrameBundle`。
*   `output_curve_name`: 输出的渗透率曲线的名称。

### 4.3. 工作流示例

```python
# 在一个更高层的工作流脚本中
from scape.core.baselines import run_dnn_training_step, run_dnn_prediction_step
from scape.core.baselines.hybrid_dnn import DnnTrainingConfig, DnnArtifacts
from logwp.extras.tracking import RunContext

# ... (假设 train_bundle 和 pred_bundle 已加载) ...

with RunContext(run_dir="./dnn_run_01") as ctx:
    # --- 1. 训练步骤 ---
    training_config = DnnTrainingConfig(n_trials=50, final_train_epochs=100)
    train_results = run_dnn_training_step(
        config=training_config,
        ctx=ctx,
        train_bundle=train_bundle,
        sequence_feature='PHI_T2_DIST',
        normalization_feature='PHIT_NMR',
        tabular_features=['PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR'],
        target_feature='K_LABEL',
        grouping_feature='WELL_NO',
        t2_time_axis=standard_t2_axis,
    )

    # --- 2. 预测步骤 ---
    # 加载训练步骤产出的模型资产
    trained_model_assets = ctx.load_artifact(DnnArtifacts.MODEL_ASSETS.value)

    pred_results = run_dnn_prediction_step(
        config=DnnPredictionConfig(),
        ctx=ctx,
        model_assets=trained_model_assets,
        prediction_bundle=pred_bundle,
        output_curve_name='K_DNN_PRED',
    )

    print("DNN基准模型工作流执行完毕。")
```

---

## 5. 技术实现细节

### 5.1. 目标变量变换

由于渗透率 `K_LABEL` 的值域跨越多个数量级，直接对其进行回归会导致模型被高值样本主导。为解决此问题，本组件采用标准最佳实践：
*   **训练时**: 模型实际预测的是渗透率的对数值 `log10(K_LABEL)`。
*   **损失函数**: 使用标准的均方误差损失 `MSELoss`，这等效于最小化均方根对数误差 (RMSLE)。
*   **预测时**: 对模型的输出应用 `10**x` 的逆变换，以得到最终的、符合物理尺度的渗透率预测值。

### 5.2. 模型资产包

训练步骤的核心产物是一个由 `joblib` 保存的Python字典，其结构如下：

```python
model_assets = {
    "model_state_dict": ...,      # PyTorch模型的权重
    "model_hyperparameters": ..., # 最佳超参数字典
    "preprocessors": {
        "tabular_scaler": ...,    # fit好的StandardScaler对象
        "target_transformer": "log10" # 明确记录目标变量的变换
    },
    "metadata": { ... }            # 包含特征名、T2轴等元数据
}
```

### 5.3. 关键产物

*   `dnn_hybrid_training.models.assets_pytorch`: 核心模型资产包。
*   `dnn_hybrid_training.reports.hyperparameter_tuning`: JSON格式的最佳超参数报告。
*   `dnn_hybrid_training.reports.cv_performance`: CSV格式的交叉验证性能报告。
*   `dnn_hybrid_prediction.datasets.predictions`: CSV格式的最终预测结果。

---

## 6. 总结

`hybrid_dnn` 组件提供了一个强大、灵活且完全可追踪的数据驱动基准模型。通过与 `SWIFT-PSO` 的对比，可以为渗透率建模方法的选择提供关键的、量化的决策依据。
