#!/usr/bin/env python3
"""深度采样间隔检查服务

提供深度采样间隔一致性检查功能，支持多种算法。

Architecture
------------
层次/依赖: logwp.models.datasets.service层，服务于WpDepthIndexedDatabaseBase
设计模式: Utility/Helper Pattern，无状态服务函数
性能特征: 数值计算优化，支持大数据集

Core Features
-------------
- **等间隔检查**: 检查深度采样是否为等间隔
- **多种算法**: 支持标准差、MAD等检测算法
- **稳健性**: MAD算法减少极端值影响
- **类型安全**: 完整的类型注解和验证

References
----------
- 《SCAPE_SAD_软件架构设计.md》§5.1 - 内部服务层设计
"""

from __future__ import annotations

import numpy as np
import pandas as pd
from typing import Tuple, Literal
from enum import Enum
import structlog

from logwp.models.exceptions import WpDataError
from logwp.infra.exceptions import ErrorContext
from logwp.models.curve.metadata import CurveBasicAttributes

# 获取结构化日志记录器
logger = structlog.get_logger(__name__)


class DepthSamplingAlgorithm(str, Enum):
    """深度采样间隔检查算法枚举。

    Attributes:
        STD_DEV: 标准差方法
        MAD: 中位数绝对偏差方法（默认推荐）
    """
    STD_DEV = "std_dev"
    MAD = "mad"


def check_uniform_depth_sampling(
    depth_column: pd.Series,
    depth_curve: CurveBasicAttributes,
    *,
    algorithm: DepthSamplingAlgorithm = DepthSamplingAlgorithm.MAD,
    tolerance: float = 1e-6
) -> Tuple[bool, float | None]:
    """检查深度采样是否为等间隔。

    使用指定算法检查深度列的采样间隔是否一致。支持标准差和MAD两种算法，
    MAD算法对极端值更稳健，是默认推荐方法。

    Args:
        depth_column: 深度数据列
        depth_curve: 深度曲线基本属性
        algorithm: 检查算法，默认为MAD
        tolerance: 容差阈值，用于判断是否为等间隔

    Returns:
        Tuple[bool, float | None]: (是否等间隔, 采样间隔)
            - 如果是等间隔，返回(True, 采样间隔值)
            - 如果不是等间隔，返回(False, None)

    Raises:
        WpDataError: 深度数据无效或计算失败
        ValueError: 参数无效

    Examples:
        >>> # 等间隔深度数据
        >>> depth_data = pd.Series([1000.0, 1000.5, 1001.0, 1001.5, 1002.0])
        >>> is_uniform, interval = check_uniform_depth_sampling(
        ...     depth_data, depth_curve, algorithm=DepthSamplingAlgorithm.MAD
        ... )
        >>> assert is_uniform == True
        >>> assert interval == 0.5

        >>> # 不等间隔深度数据
        >>> depth_data = pd.Series([1000.0, 1000.3, 1001.0, 1001.8, 1002.0])
        >>> is_uniform, interval = check_uniform_depth_sampling(
        ...     depth_data, depth_curve
        ... )
        >>> assert is_uniform == False
        >>> assert interval is None

    References:
        《SCAPE_DDS_详细设计_logwp.md》§6.3 - 深度采样间隔检查
    """
    try:
        # 参数验证
        _validate_depth_sampling_parameters(depth_column, depth_curve, algorithm, tolerance)

        logger.debug(
            "开始深度采样间隔检查",
            operation="check_uniform_depth_sampling",
            algorithm=algorithm.value,
            depth_count=len(depth_column),
            tolerance=tolerance,
            curve_name=depth_curve.name
        )

        # 数据预处理
        if len(depth_column) < 2:
            logger.warning(
                "深度数据点不足，无法检查采样间隔",
                operation="check_uniform_depth_sampling",
                depth_count=len(depth_column)
            )
            return False, None

        # 计算相邻点的深度差异
        depth_diffs = depth_column.diff().dropna()

        if len(depth_diffs) == 0:
            return False, None

        # 根据算法选择检查方法
        if algorithm == DepthSamplingAlgorithm.STD_DEV:
            is_uniform, interval = _check_by_std_dev(depth_diffs, tolerance)
        elif algorithm == DepthSamplingAlgorithm.MAD:
            is_uniform, interval = _check_by_mad(depth_diffs, tolerance)
        else:
            raise ValueError(f"不支持的算法: {algorithm}")

        logger.info(
            "深度采样间隔检查完成",
            operation="check_uniform_depth_sampling",
            algorithm=algorithm.value,
            is_uniform=is_uniform,
            sampling_interval=interval,
            curve_name=depth_curve.name
        )

        return is_uniform, interval

    except Exception as e:
        raise WpDataError(
            f"深度采样间隔检查失败: {str(e)}",
            context=ErrorContext(
                operation="check_uniform_depth_sampling",
                additional_info={
                    "algorithm": algorithm.value,
                    "curve_name": depth_curve.name,
                    "depth_count": len(depth_column),
                    "error_type": type(e).__name__
                }
            )
        ) from e


def _validate_depth_sampling_parameters(
    depth_column: pd.Series,
    depth_curve: CurveBasicAttributes,
    algorithm: DepthSamplingAlgorithm,
    tolerance: float
) -> None:
    """验证深度采样检查参数。"""
    if not isinstance(depth_column, pd.Series):
        raise ValueError("depth_column必须是pandas Series")

    if not isinstance(depth_curve, CurveBasicAttributes):
        raise ValueError("depth_curve必须是CurveBasicAttributes实例")

    if not isinstance(algorithm, DepthSamplingAlgorithm):
        raise ValueError("algorithm必须是DepthSamplingAlgorithm枚举值")

    if not isinstance(tolerance, (int, float)) or tolerance < 0:
        raise ValueError("tolerance必须是非负数值")

    if depth_column.empty:
        raise ValueError("深度数据不能为空")


def _check_by_std_dev(depth_diffs: pd.Series, tolerance: float) -> Tuple[bool, float | None]:
    """使用标准差方法检查采样间隔一致性。

    Args:
        depth_diffs: 深度差异序列
        tolerance: 容差阈值

    Returns:
        Tuple[bool, float | None]: (是否等间隔, 采样间隔)
    """
    std_dev = depth_diffs.std()
    mean_interval = depth_diffs.mean()

    logger.debug(
        "标准差方法检查结果",
        operation="check_by_std_dev",
        std_dev=std_dev,
        mean_interval=mean_interval,
        tolerance=tolerance
    )

    # 如果标准差小于容差，认为是等间隔
    if std_dev <= tolerance:
        return True, float(mean_interval)
    else:
        return False, None


def _check_by_mad(depth_diffs: pd.Series, tolerance: float) -> Tuple[bool, float | None]:
    """使用MAD（中位数绝对偏差）方法检查采样间隔一致性。

    MAD是一种稳健的离群值检测方法，能减少极端值对结果的影响。

    Args:
        depth_diffs: 深度差异序列
        tolerance: 容差阈值

    Returns:
        Tuple[bool, float | None]: (是否等间隔, 采样间隔)
    """
    median_interval = depth_diffs.median()

    # 计算MAD：|x_i - median(x)|的中位数
    absolute_deviations = np.abs(depth_diffs - median_interval)
    mad_value = absolute_deviations.median()

    logger.debug(
        "MAD方法检查结果",
        operation="check_by_mad",
        mad_value=mad_value,
        median_interval=median_interval,
        tolerance=tolerance
    )

    # 如果MAD值小于容差，认为是等间隔
    if mad_value <= tolerance:
        return True, float(median_interval)
    else:
        return False, None
