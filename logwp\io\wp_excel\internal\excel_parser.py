"""Excel文件基础解析服务。

提供Excel工作簿和工作表的基础操作功能，包括安全加载、工作表识别、
项目名提取等核心功能。

本模块遵循WFS v1.0规范，专注于Excel格式的基础解析，不涉及业务逻辑。
"""

from __future__ import annotations

import logging
from pathlib import Path
from typing import Any

import openpyxl
import structlog

from logwp.io.exceptions import WpFileFormatError
from logwp.io.exceptions import WpIOError
from logwp.io.constants import WpXlsxKey

logger = structlog.get_logger(__name__)

__all__ = [
    "load_workbook_safely",
    "stream_load_workbook_safely",
    "get_worksheet_safely", "extract_project_name", "validate_workbook_structure"
]

def stream_load_workbook_safely(file_path: Path) -> openpyxl.Workbook:
    """以只读流模式安全加载Excel工作簿，优化大文件性能。

    此模式下，openpyxl不会将整个文件加载到内存，而是以流式方式处理，
    显著降低内存消耗并提升大文件的读取速度。

    Args:
        file_path: Excel文件路径

    Returns:
        openpyxl.Workbook: 以只读模式加载的工作簿对象

    Raises:
        WpFileFormatError: 文件格式不符合要求
        WpIOError: 文件读取错误
        FileNotFoundError: 文件不存在
        PermissionError: 文件访问权限不足

    Note:
        - 只读模式下无法随机访问单元格，必须通过迭代器顺序访问。
        - `data_only=True`确保只读取单元格的值，忽略公式。
    """
    logger.debug("开始以只读流模式加载Excel工作簿", file_path=str(file_path))

    try:
        # 验证文件路径
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        if not file_path.is_file():
            raise WpFileFormatError(f"路径不是文件: {file_path}")

        # 以只读模式加载工作簿
        workbook = openpyxl.load_workbook(file_path, read_only=True, data_only=True)

        logger.info(
            "Excel工作簿以只读流模式加载成功",
            file_path=str(file_path),
            file_size_mb=round(file_path.stat().st_size / 1024 / 1024, 2),
            sheet_count=len(workbook.sheetnames)
        )

        return workbook

    except FileNotFoundError:
        logger.error("文件不存在", file_path=str(file_path))
        raise
    except PermissionError:
        logger.error("文件访问权限不足", file_path=str(file_path))
        raise
    except Exception as e:
        logger.error(
            "Excel工作簿加载失败（流模式）",
            file_path=str(file_path),
            error_type=type(e).__name__,
            error_message=str(e)
        )
        raise WpIOError(f"无法以流模式加载Excel文件: {e}") from e


def get_worksheet_safely(workbook: openpyxl.Workbook,
                        sheet_name: str) -> openpyxl.Worksheet | None:
    """安全获取工作表（大小写不敏感）。

    Args:
        workbook: Excel工作簿对象
        sheet_name: 工作表名称（大小写不敏感）

    Returns:
        openpyxl.Worksheet | None: 工作表对象，不存在时返回None

    Examples:
        >>> worksheet = get_worksheet_safely(workbook, "_head_info")
        >>> if worksheet:
        ...     print("找到井头信息表单")

    References:
        《SCAPE_WFS_WP文件规范.md》7.2节 - 大小写不敏感处理
    """
    # 大小写不敏感匹配
    sheet_name_upper = sheet_name.upper()

    for existing_name in workbook.sheetnames:
        if existing_name.upper() == sheet_name_upper:
            logger.debug(
                "找到工作表",
                requested_name=sheet_name,
                actual_name=existing_name
            )
            return workbook[existing_name]

    logger.debug("工作表不存在", sheet_name=sheet_name)
    return None


def extract_project_name(file_path: Path) -> str:
    """从文件路径提取项目名称。

    Args:
        file_path: WP文件路径

    Returns:
        str: 项目名称（去除.wp.xlsx扩展名）

    Examples:
        >>> name = extract_project_name(Path("santos_data.wp.xlsx"))
        >>> assert name == "santos_data"

    References:
        《SCAPE_WFS_WP文件规范.md》2.1节 - 文件命名规范
    """
    file_name = file_path.name

    # 移除.wp.xlsx扩展名（不区分大小写）
    if file_name.lower().endswith(WpXlsxKey.WP_FILE_EXTENSION.lower()):
        project_name = file_name[:-len(WpXlsxKey.WP_FILE_EXTENSION)]
    else:
        # 备用方案：移除所有扩展名
        project_name = file_path.stem

    logger.debug("提取项目名称", file_path=str(file_path), project_name=project_name)
    return project_name





def validate_workbook_structure(workbook: openpyxl.Workbook) -> None:
    """验证工作簿基础结构。

    Args:
        workbook: Excel工作簿对象

    Raises:
        WpFileFormatError: 工作簿结构不符合WFS规范

    References:
        《SCAPE_WFS_WP文件规范.md》2.2节 - 工作簿限制
    """

    # 检查是否有数据集工作表（简单判断：不以_开头的工作表，不区分大小写）
    dataset_count = sum(1 for name in workbook.sheetnames if not name.startswith("_"))

    if dataset_count == 0:
        raise WpFileFormatError("工作簿必须包含至少一个数据集工作表（名称不以下划线'_'开头）")

    logger.debug(
        "工作簿结构验证通过",
        sheet_count=len(workbook.sheetnames),
        dataset_count=dataset_count
    )
