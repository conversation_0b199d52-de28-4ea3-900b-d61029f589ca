"""
饱和度计算模型
"""
import numpy as np

from logwp.extras.units import Quantity
from .validators import validate_dimensions


@validate_dimensions(Rt='resistivity', Rw='resistivity')
def archie_sw(
    Rt: Quantity,
    Rw: Quantity,
    phi: float,
    a: float = 1.0,
    m: float = 2.0,
    n: float = 2.0
) -> float:
    """
    使用经典的阿尔奇(Archie)公式计算含水饱和度(Sw)。

    公式: Sw = ((a * Rw) / (phi**m * Rt))**(1/n)

    参数:
        Rt (Quantity): 地层的真实电阻率，带有电阻率量纲。
        Rw (Quantity): 地层水的电阻率，带有电阻率量纲。
        phi (float): 有效孔隙度，无量纲小数 (例如 0.15)。
        a (float, optional): 岩性系数 (或称 tortuosity factor)，默认为 1.0。
        m (float, optional): 胶结指数，默认为 2.0。
        n (float, optional): 饱和度指数，默认为 2.0。

    返回:
        float: 计算出的含水饱和度(Sw)，无量纲小数。
    """
    # Rt 和 Rw 拥有相同的量纲，可以直接进行除法运算。
    # `units` 包会自动处理单位换算，除法结果是一个无量纲的 Quantity 对象。
    rw_over_rt = Rw / Rt

    # 计算地层因素 (Formation Factor)
    F = a / (phi ** m)

    # 避免对负数开方导致返回复数，这在物理上无意义
    sw_pow_n = F * rw_over_rt.value
    if sw_pow_n < 0:
        return np.nan

    sw = sw_pow_n ** (1 / n)

    # 饱和度值应在0和1之间，但实际计算可能略微超出，进行裁剪以保证物理意义
    return np.clip(sw, 0.0, 1.0)
