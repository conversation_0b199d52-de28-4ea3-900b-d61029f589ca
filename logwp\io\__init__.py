"""logwp I/O层主入口。

提供对不同测井数据格式进行读写的统一接口。
遵循SAD文档的"I/O层格式特定处理器"模式，将具体实现委托给子包处理。

Architecture
------------
层次/依赖: I/O层，被业务层和应用层调用
设计原则: 格式解耦、接口统一、异常处理
核心组件:
- WpExcelReader: 从符合WFS规范的Excel文件读取为WpWellProject
- WpExcelWriter: 将WpWellProject写入为符合WFS规范的Excel文件
- WpIOError: I/O操作相关的通用异常
- WpFileFormatError: 特定文件格式处理相关的异常

Examples
--------
>>> from pathlib import Path
>>> from logwp.io import WpExcelReader, WpExcelWriter
>>> # from logwp.models import WpWellProject # 假设project已创建
>>>
>>> # 1. 使用读取器从文件加载项目
>>> reader = WpExcelReader()
>>> input_path = Path("existing_data.wp.xlsx")
>>> try:
...     # project = reader.read(input_path)
...     print(f"文件读取成功: {input_path}")
... except WpIOError as e:
...     print(f"文件读取失败: {e}")
>>>
>>> # 2. 使用写入器将项目保存到文件
>>> writer = WpExcelWriter()
>>> output_path = Path("new_output.wp.xlsx")
>>> try:
...     # writer.write(project, output_path)
...     print(f"文件写入成功: {output_path}")
... except WpIOError as e:
...     print(f"文件写入失败: {e}")

References
----------
- 《SCAPE_SAD_软件架构设计.md》§4.2 - I/O层设计
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》 - WP Excel写入器详细设计
"""

from __future__ import annotations

# 导出核心I/O异常
from .exceptions import WpIOError, WpFileFormatError

# 导出核心读写器
# 注意：这里只导出高级别的读写器类，具体实现由子包管理
from .wp_excel import WpExcelReader, WpExcelWriter

# 定义包的公共API
__all__ = [
    # 核心读写器 (按读、写顺序)
    "WpExcelReader",
    "WpExcelWriter",

    # 核心I/O异常
    "WpIOError",
    "WpFileFormatError",
]
