"""scape.core.swift_pso.internal.backend_utils - 计算后端工具

提供计算后端初始化和GPU可用性检查的工具函数。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部工具层
设计原则: 工具函数、异常安全、自动回退
性能特征: GPU检测、CPU回退、错误处理

遵循CCG规范：
- GP-1: 自动检测回退
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 迁移自 scape/core/swift_pso_backup/internal/pso_compute_service.py
"""

from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.extras.backend import BackendService

from logwp.infra import get_logger
from logwp.infra.exceptions import WpGpuError, ErrorContext
from logwp.models.exceptions import WpDataError
from logwp.extras.backend import create_backend_service_by_name, is_gpu_available

logger = get_logger(__name__)



def create_backend_service(backend: str) -> BackendService:
    """创建并验证计算后端服务。

    根据用户指定的后端类型创建BackendService实例，并验证其可用性。
    如果请求了GPU但不可用，将抛出明确的异常。
    这是一个转发函数，调用 logwp.extras.backend 中的核心服务。

    Args:
        backend: 后端类型，'cpu' 或 'gpu'

    Returns:
        BackendService: 初始化好的后端服务实例

    Raises:
        WpGpuError: 请求GPU但不可用时抛出
        WpDataError: 后端初始化失败时抛出

    Note:
        此函数是facade层前置检查的核心工具，确保计算环境满足要求。
    """
    # 1. 前置检查：如果请求GPU，先快速检查其是否可用
    if backend == 'gpu' and not is_gpu_available():
        raise WpGpuError("请求使用GPU后端，但当前环境GPU不可用或未正确安装。")

    try:
        # 2. 直接调用核心库的工厂函数
        backend_service = create_backend_service_by_name(backend)
        logger.info(
            "计算后端服务已成功创建",
            operation="backend_creation",
            backend_type=backend_service.name
        )
        return backend_service
    except Exception as e:
        logger.error(
            "后端服务创建失败",
            operation="backend_creation",
            requested_backend=backend,
            error=str(e)
        )
        # 3. 重新包装异常，提供更多上下文
        if backend == 'gpu':
            raise WpGpuError(f"GPU后端初始化失败: {e}") from e
        else:
            raise WpDataError(f"CPU后端初始化失败: {e}") from e
