"""曲线解析服务。

解析曲线定义，支持曲线元数据创建、二维组合曲线识别、深度角色识别、
井名标识识别等功能。实现WFS 4章规范，为曲线元数据构造提供核心能力。
"""

from __future__ import annotations

import re
from typing import Any, Dict, List

import structlog

from logwp.models.constants import WpCurveCategory, WpCurveClass, WpDataType, WpDepthRole, WpDsType
from logwp.models.exceptions import WpValidationError
from logwp.io.constants import WpXlsxKey
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata

logger = structlog.get_logger(__name__)


def create_curve_metadata_from_definitions(curve_definitions: List[Dict[str, Any]],
                                          dataset_type: WpDsType) -> CurveMetadata:
    """从曲线定义创建CurveMetadata对象。

    实现SAD 4.13节的曲线名称多层次架构设计：
    1. 识别二维组合曲线组，创建完整的二维组合曲线对象
    2. 处理一维曲线和二维元素（降级处理）
    3. 构建分层对象管理的元数据结构

    Args:
        curve_definitions: 曲线定义列表
        dataset_type: 数据集类型

    Returns:
        CurveMetadata: 构造的曲线元数据对象

    Examples:
        >>> metadata = create_curve_metadata_from_definitions(curve_defs, WpDsType.CONTINUOUS)
        >>> print(f"曲线数量: {len(metadata.curves)}")

    References:
        《SCAPE_WFS_WP文件规范.md》4章 - 曲线定义规范
        《SCAPE_SAD_软件架构设计.md》4.13节 - 曲线名称多层次架构设计
    """
    logger.debug("开始创建曲线元数据", curve_count=len(curve_definitions))

    # 第一步：识别二维组合曲线组
    curve_names = [curve_def["name"] for curve_def in curve_definitions]
    curve_2d_groups = identify_2d_curves(curve_names)

    logger.debug("识别二维组合曲线组", groups=curve_2d_groups)

    metadata = CurveMetadata()
    depth_curve_count = 0
    well_curve_count = 0
    processed_curves = set()  # 跟踪已处理的曲线

    # 2025.7.16: Gemini Code Assist - Start of fix
    # Bug: The old logic used the absolute column position ('i') to assign TOP/BOTTOM roles,
    # which is incorrect for Interval datasets.
    # Fix: Maintain a separate counter specifically for depth curves. This ensures that
    # for an Interval dataset, the first identified depth curve is assigned the TOP role
    # and the second is assigned the BOTTOM role, regardless of their actual column
    # positions in the Excel file.
    depth_curve_counter_for_interval = 0
    # 2025.7.16: Gemini Code Assist - End of fix

    # 第二步：处理二维组合曲线组（创建完整的二维组合曲线对象）
    for base_name, element_names in curve_2d_groups.items():
        try:
            # 找到第一个元素的定义作为模板
            template_def = None
            for curve_def in curve_definitions:
                if curve_def["name"] == element_names[0]:
                    template_def = curve_def
                    break

            if template_def is None:
                logger.warning("未找到二维组合曲线模板定义", base_name=base_name)
                continue

            # 创建完整的二维组合曲线对象
            curve_attrs = _create_2d_curve_from_group(
                base_name, element_names, template_def
            )

            # 统计特殊曲线（二维组合曲线通常不是深度或井名曲线）
            if curve_attrs.depth_role is not None:
                depth_curve_count += 1
            if curve_attrs.is_well_identifier:
                well_curve_count += 1

            # 添加到元数据
            metadata.add_curve(curve_attrs)

            # 标记所有元素为已处理
            processed_curves.update(element_names)

            logger.debug(
                "创建二维组合曲线对象",
                base_name=base_name,
                element_count=len(element_names)
            )

        except Exception as e:
            logger.warning(
                "跳过无效二维组合曲线组",
                base_name=base_name,
                error=str(e)
            )

    # 第三步：处理剩余的一维曲线
    for i, curve_def in enumerate(curve_definitions):
        curve_name = curve_def["name"]

        # 跳过已处理的二维组合曲线元素
        if curve_name in processed_curves:
            continue

        try:
            # 2025.7.16: Gemini Code Assist - Start of fix
            # Pass the dedicated depth curve counter instead of the absolute position 'i'.
            # This counter is only relevant for INTERVAL datasets to correctly assign
            # TOP/BOTTOM roles based on the order of depth curves, not their column position.
            curve_attrs = create_curve_from_wfs_data(curve_def, dataset_type, depth_curve_counter_for_interval)

            # Increment the counter *after* creating the attributes if it was a depth curve.
            # This ensures the counter reflects "this is the N-th depth curve".
            if curve_attrs.is_depth_curve() and dataset_type == WpDsType.INTERVAL:
                 depth_curve_counter_for_interval += 1
            # 2025.7.16: Gemini Code Assist - End of fix

            # 统计特殊曲线
            if curve_attrs.depth_role is not None:
                depth_curve_count += 1
            if curve_attrs.is_well_identifier:
                well_curve_count += 1

            # 添加到元数据
            metadata.add_curve(curve_attrs)

        except Exception as e:
            logger.warning(
                "跳过无效曲线定义",
                curve_name=curve_name,
                error=str(e)
            )

    # 验证曲线约束
    _validate_curve_constraints(metadata, dataset_type, depth_curve_count, well_curve_count)

    logger.info(
        "曲线元数据创建完成",
        total_curves=len(metadata.curves),
        depth_curves=depth_curve_count,
        well_curves=well_curve_count,
        curve_2d_groups=len(curve_2d_groups)
    )

    return metadata


def create_curve_from_wfs_data(curve_data: Dict[str, Any],
                              dataset_type: WpDsType,
                              position: int) -> CurveBasicAttributes:
    """从WFS数据创建一维曲线基本属性。

    注意：此函数仅用于创建一维曲线，二维组合曲线应使用_create_2d_curve_from_group函数。
    根据SAD 4.13节设计，单个曲线定义只能创建一维曲线对象。

    Args:
        curve_data: WFS曲线数据字典
        dataset_type: 数据集类型
        position: 曲线在数据集中的位置（用于深度角色识别）

    Returns:
        CurveBasicAttributes: 一维曲线基本属性对象

    References:
        《SCAPE_WFS_WP文件规范.md》4.1节 - 曲线属性定义
        《SCAPE_SAD_软件架构设计.md》4.13节 - 曲线名称多层次架构设计
    """
    name = curve_data["name"]
    unit = curve_data["unit"]
    data_type = curve_data["type"]
    class_value = curve_data["class"]
    comment = curve_data["comment"]

    # 转换数据类型
    wp_data_type = _convert_wfs_data_type(data_type)

    # 识别深度角色
    depth_role = _identify_depth_role(name, dataset_type, position)

    # 识别井名标识
    is_well_identifier = _identify_well_identifier(name)

    # 强制类型转换
    if depth_role is not None:
        wp_data_type = WpDataType.FLOAT  # 深度曲线强制FLOAT
    if is_well_identifier:
        wp_data_type = WpDataType.STR  # 井名曲线强制STR

    # 转换曲线类别
    wp_curve_class = _convert_wfs_curve_class(class_value) if class_value else None

    # 创建一维曲线属性
    curve_attrs = CurveBasicAttributes.create_1d_curve(
        name=name,
        unit=unit,
        data_type=wp_data_type,
        category=WpCurveCategory.LOGGING,  # 默认为测井曲线
        depth_role=depth_role,
        is_well_identifier=is_well_identifier,
        description=comment,
        curve_class=wp_curve_class
    )

    logger.debug(
        "创建一维曲线属性",
        name=name,
        depth_role=depth_role.value if depth_role else None,
        is_well_identifier=is_well_identifier,
        data_type=wp_data_type.value
    )

    return curve_attrs


def _create_2d_curve_from_group(base_name: str,
                               element_names: List[str],
                               template_def: Dict[str, Any]) -> CurveBasicAttributes:
    """从二维组合曲线组创建完整的二维组合曲线对象。

    实现SAD 4.13节的分层对象管理设计：
    - 创建包含所有元素的完整二维组合曲线对象
    - 自动生成element_names和dataframe_element_names属性
    - 继承模板曲线的属性（单位、数据类型等）

    Args:
        base_name: 二维组合曲线基础名称（如"T2_VALUE"）
        element_names: 元素名称列表（如["T2_VALUE[1]", "T2_VALUE[2]", ...]）
        template_def: 模板曲线定义（用于继承属性）

    Returns:
        CurveBasicAttributes: 完整的二维组合曲线对象

    References:
        《SCAPE_SAD_软件架构设计.md》4.13节 - 曲线名称多层次架构设计
    """
    unit = template_def["unit"]
    data_type = template_def["type"]
    class_value = template_def["class"]
    comment = template_def["comment"]

    # 转换数据类型
    wp_data_type = _convert_wfs_data_type(data_type)

    # 二维组合曲线通常不是深度或井名曲线
    depth_role = None
    is_well_identifier = False

    # 转换曲线类别
    wp_curve_class = _convert_wfs_curve_class(class_value) if class_value else None

    # 创建完整的二维组合曲线对象
    curve_attrs = CurveBasicAttributes.create_2d_composite_curve(
        name=base_name,
        element_count=len(element_names),
        unit=unit,
        data_type=wp_data_type,
        category=WpCurveCategory.LOGGING,  # 默认为测井曲线
        depth_role=depth_role,
        is_well_identifier=is_well_identifier,
        description=comment,
        curve_class=wp_curve_class
    )

    logger.debug(
        "创建二维组合曲线对象",
        base_name=base_name,
        element_count=len(element_names),
        data_type=wp_data_type.value,
        element_names=element_names[:3] + ["..."] if len(element_names) > 3 else element_names
    )

    return curve_attrs


def _convert_wfs_curve_class(wfs_class: str) -> WpCurveClass | None:
    """转换WFS曲线类别到Models层枚举。

    Args:
        wfs_class: WFS曲线类别字符串

    Returns:
        WpCurveClass | None: Models层曲线类别枚举
    """
    if not wfs_class:
        return None

    class_upper = str(wfs_class).strip().upper()

    if class_upper == WpXlsxKey.CLASS_CAT:
        return WpCurveClass.CATEGORICAL
    elif class_upper == WpXlsxKey.CLASS_NORMAL:
        return WpCurveClass.NORMAL
    else:
        logger.warning(f"未知曲线类别: {wfs_class}, 返回None")
        return None


def _convert_wfs_data_type(wfs_type: str) -> WpDataType:
    """转换WFS数据类型到Models层枚举。

    Args:
        wfs_type: WFS数据类型字符串

    Returns:
        WpDataType: Models层数据类型枚举
    """
    if not wfs_type:
        return WpDataType.FLOAT  # 默认类型

    type_upper = str(wfs_type).strip().upper()

    if type_upper == WpXlsxKey.TYPE_INT:
        return WpDataType.INT
    elif type_upper == WpXlsxKey.TYPE_FLOAT:
        return WpDataType.FLOAT
    elif type_upper == WpXlsxKey.TYPE_STR:
        return WpDataType.STR
    elif type_upper == WpXlsxKey.TYPE_BOOL:
        return WpDataType.BOOL
    else:
        logger.warning(f"未知数据类型: {wfs_type}, 使用默认FLOAT")
        return WpDataType.FLOAT



def _identify_depth_role(curve_name: str, dataset_type: WpDsType, position: int) -> WpDepthRole | None:
    """识别深度曲线角色。

    Args:
        curve_name: 曲线名称
        dataset_type: 数据集类型
        position: 曲线位置

    Returns:
        WpDepthRole | None: 深度角色枚举，非深度曲线返回None

    References:
        《SCAPE_WFS_WP文件规范.md》4.2节 - 深度索引规范
    """
    # 检查是否为深度曲线别名
    depth_aliases = WpXlsxKey.depth_aliases()
    curve_upper = curve_name.upper()

    is_depth_curve = any(curve_upper == alias.upper() for alias in depth_aliases)

    if not is_depth_curve:
        return None

    # 根据数据集类型确定角色
    if dataset_type in {WpDsType.CONTINUOUS, WpDsType.POINT}:
        return WpDepthRole.SINGLE
    elif dataset_type == WpDsType.INTERVAL:
        # Interval类型：第一个深度曲线为TOP，第二个为BOTTOM
        return WpDepthRole.TOP if position == 0 else WpDepthRole.BOTTOM

    return None


def _identify_well_identifier(curve_name: str) -> bool:
    """识别井名标识曲线。

    Args:
        curve_name: 曲线名称

    Returns:
        bool: 是否为井名标识曲线

    References:
        《SCAPE_WFS_WP文件规范.md》4.3节 - 井名标识规范
    """
    well_aliases = WpXlsxKey.well_name_aliases()
    curve_upper = curve_name.upper()

    return any(curve_upper == alias.upper() for alias in well_aliases)


def _validate_curve_constraints(metadata: CurveMetadata,
                               dataset_type: WpDsType,
                               depth_curve_count: int,
                               well_curve_count: int) -> None:
    """验证曲线约束。

    Args:
        metadata: 曲线元数据对象
        dataset_type: 数据集类型
        depth_curve_count: 深度曲线数量
        well_curve_count: 井名曲线数量

    Raises:
        WpValidationError: 曲线约束验证失败

    References:
        《SCAPE_WFS_WP文件规范.md》4.4节 - 必需保留列
    """
    # 验证井名曲线
    if well_curve_count != 1:
        raise WpValidationError(f"每个数据集必须恰好包含1个井名曲线，实际: {well_curve_count}")

    # 验证深度曲线
    if dataset_type in {WpDsType.CONTINUOUS, WpDsType.POINT}:
        if depth_curve_count != 1:
            raise WpValidationError(
                f"{dataset_type.value}数据集必须恰好包含1个深度曲线，实际: {depth_curve_count}"
            )
    elif dataset_type == WpDsType.INTERVAL:
        if depth_curve_count != 2:
            raise WpValidationError(f"Interval数据集必须恰好包含2个深度曲线，实际: {depth_curve_count}")

    # 验证深度单位一致性
    _validate_depth_unit_consistency(metadata)


def _validate_depth_unit_consistency(metadata: CurveMetadata) -> None:
    """验证深度单位一致性。

    Args:
        metadata: 曲线元数据对象

    Raises:
        WpValidationError: 深度单位不一致

    References:
        《SCAPE_WFS_WP文件规范.md》4.5.2节 - 深度单位一致性
    """
    depth_curves = [
        curve for curve in metadata.curves.values()
        if curve.depth_role is not None
    ]

    if len(depth_curves) <= 1:
        return  # 单个或无深度曲线，无需检查

    # 检查单位一致性
    units = {curve.unit for curve in depth_curves}
    if len(units) > 1:
        raise WpValidationError(f"深度曲线单位不一致: {units}")


def identify_2d_curves(curve_names: List[str]) -> Dict[str, List[str]]:
    """识别二维组合曲线并分组。

    Args:
        curve_names: 曲线名称列表

    Returns:
        Dict[str, List[str]]: 二维组合曲线分组 {基础名称: [元素名称列表]}

    Examples:
        >>> curves = ["T2_VALUE[0]", "T2_VALUE[1]", "GR", "T2_VALUE[2]"]
        >>> groups = identify_2d_curves(curves)
        >>> print(groups)  # {"T2_VALUE": ["T2_VALUE[0]", "T2_VALUE[1]", "T2_VALUE[2]"]}
    """
    curve_groups = {}
    pattern = WpXlsxKey.CURVE_2D_INDEX_PATTERN

    for curve_name in curve_names:
        match = re.search(pattern, curve_name)
        if match:
            base_name = curve_name[:match.start()]
            if base_name not in curve_groups:
                curve_groups[base_name] = []
            curve_groups[base_name].append(curve_name)

    # 排序元素名称
    for base_name in curve_groups:
        curve_groups[base_name].sort(key=lambda x: int(re.search(pattern, x).group(1)))

    logger.debug("识别二维组合曲线分组", groups=curve_groups)
    return curve_groups
