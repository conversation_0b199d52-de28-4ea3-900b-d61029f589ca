# 利用Conda实现Python项目 “项目漫游，环境落地”多机同步

本文档旨在指导如何在多台计算机上，为本项目配置一套一致且独立的开发环境。

### 一、 首次配置 (在每台电脑上仅需执行一次)

这些步骤为环境奠定基础，请在您的办公室和家里的电脑上分别完整操作一遍。

1.  **创建 Conda 虚拟环境**
    > **目的**: 创建一个名为 `scape_env` 的独立沙盒环境，以避免项目间的包冲突。

    ```bash
    conda create --name scape_env python=3.12
    ```

2.  **安装项目依赖**
    > **目的**: 激活环境，并根据项目根目录下的 `pyproject.toml` 文件，安装所有必需的库。

    ```bash
    # 激活环境
    conda activate scape_env

    # 升级 pip 到最新版本 (推荐)
    python -m pip install --upgrade pip

    # 安装依赖 (注意后面的点号)
    pip install -e .
    ```

    * **提示**: `-e` 参数代表“可编辑模式”，它允许您在修改源代码后无需重新安装即可立即生效。

3.  **关联 VS Code 工作区 (核心配置)**
    > **目的**: 让 VS Code “记住”本项目应使用 `scape_env` 环境，并确保终端被**真正地**激活。
    >
    > **原因详解**: VS Code 的 Python 插件自带的终端自动激活功能 (`"python.terminal.activateEnvironment": true`) 有时并不可靠。它可能只在命令行前显示了环境前缀 `(scape_env)`，但并未正确修改终端的 `PATH` 环境变量，导致所谓的**“虚假激活”**——看起来激活了，但实际运行的 `python` 或 `pip` 命令仍然是 `base` 环境或其他系统路径下的版本。
    >
    > **我们的解决方案**: 我们采用一种更稳定、更底层的“手动注入”方式。首先，我们通过 `"python.terminal.activateEnvironment": false` 禁用 VS Code 不稳定的自动激活功能。然后，我们通过 `"terminal.integrated.profiles.windows"` 自定义一个终端启动配置文件。这相当于直接告诉 VS Code：“启动终端时，不要自己做任何激活操作，而是直接运行 Conda 官方的激活脚本 (`activate.bat` 或 `conda-hook.ps1`)”。这种方法直接利用 Conda 自身最核心的激活机制，因此最为可靠，能确保每次打开终端时，环境都被完美激活。

    * **第一步：创建系统环境变量 (推荐)**
        > 为了让配置具有完全的可移植性，我们创建两个环境变量：一个指向 Anaconda 的安装目录，另一个指向我们的虚拟环境。
        1. 在您的每台电脑上，创建以下两个环境变量。
            * **在 Windows 上**: 搜索并打开“编辑系统环境变量” -> 点击“环境变量...” -> 在“用户变量”下点击“新建...”。
            * **变量 1：Anaconda 主目录**
                * **变量名**: `CONDA_HOME`
                * **变量值**: `C:\Users\<USER>\anaconda3` (替换为您的 Anaconda 安装根目录)
            * **变量 2：项目虚拟环境**
                * **变量名**: `SCAPE_ENV_PATH`
                * **变量值**: `C:\Users\<USER>\anaconda3\envs\scape_env` (替换为您的真实路径)
        2. **重启 VS Code**: 创建或修改环境变量后，必须完全重启 VS Code 才能让它读取到新变量。

    * **第二步：配置 `.vscode/settings.json`**
        > 这是最关键的一步。它会告诉 VS Code 如何正确地找到解释器，并如何配置终端来完美激活环境。
        1. 按下 `Ctrl+Shift+P`，运行 `Preferences: Open Workspace Settings (JSON)` (首选项: 打开工作区设置(JSON))。
        2. 这会创建并打开 `.vscode/settings.json` 文件。将以下内容**完整地**粘贴进去：

            ```json
            {
                // 1. 告诉 VS Code Python 解释器在哪里 (用于调试、Linting等)
                //    我们使用环境变量来保持可移植性。
                "python.defaultInterpreterPath": "${env:SCAPE_ENV_PATH}\\python.exe",

                // 2. 禁用 VS Code 自身的、不稳定的终端激活功能
                "python.terminal.activateEnvironment": false,

                // 3. 设置我们自定义的 Conda 终端为默认终端
                "terminal.integrated.defaultProfile.windows": "CondaCMD",

                // 4. 定义我们的自定义终端配置文件
                "terminal.integrated.profiles.windows": {
                    // 为 Command Prompt 定义一个配置文件
                    "CondaCMD": {
                        "path": "C:\\Windows\\System32\\cmd.exe",
                        "args": [
                            "/K",
                            // 使用 CONDA_HOME 环境变量来调用激活脚本
                            "${env:CONDA_HOME}\\Scripts\\activate.bat scape_env"
                        ]
                    },
                    // (可选) 为 PowerShell 定义一个配置文件
                    "CondaPowerShell": {
                        "source": "PowerShell",
                        "icon": "terminal-powershell",
                        "args": [
                            "-NoExit",
                            "-Command",
                            // 使用 CONDA_HOME 环境变量来调用激活脚本
                            "& '${env:CONDA_HOME}\\shell\\condabin\\conda-hook.ps1'; conda activate scape_env"
                        ]
                    }
                }
            }
            ```
        3. **说明**: 现在，此配置文件完全依赖于您在本地电脑上设置的 `CONDA_HOME` 和 `SCAPE_ENV_PATH` 环境变量，不包含任何写死的路径，具有完全的可移植性。

### 二、 日常开发工作流

完成首次配置后，日常工作流程将非常顺畅：

1.  用 VS Code 打开位于移动硬盘上的项目文件夹。
2.  打开新终端，它将自动、完美地激活 `scape_env` 环境。
3.  直接开始编码、运行和调试。

### 三、 更新环境依赖

当项目需要新的依赖包时，请遵循以下流程以确保所有环境同步。

1.  **修改声明文件**
    > 在 `pyproject.toml` 的 `[project.dependencies]` 列表中，添加、删除或修改包的版本。

2.  **应用更新**
    > 在您**当前使用**的电脑上，打开终端并运行以下命令来同步更改。

    ```bash
    conda activate scape_env
    pip install -e .
    ```

3.  **同步到其他电脑**
    > 当您换到另一台电脑时，只需重复第2步的操作，即可将该电脑的环境也更新至最新状态。

### 四、 附录：常用命令与排错

1.  **查看所有 Conda 环境**
    > 列出您电脑上所有的 Conda 环境。

    ```bash
    conda env list
    ```

2.  **查看当前激活的环境 (新增)**
    > 除了查看命令行前缀 `(scape_env)`，还可以用以下命令确认。

    * **方法一：`conda env list`**
      > 当前激活的环境前面会有一个星号 (`*`)。

    * **方法二：查看环境变量**
      > Conda 会将当前激活的环境名存储在 `CONDA_DEFAULT_ENV` 变量中。
      * 在 **PowerShell** 或 **Git Bash** 中: `echo $CONDA_DEFAULT_ENV`
      * 在 **Command Prompt** 中: `echo %CONDA_DEFAULT_ENV%`

3.  **彻底重建环境**
    > 如果环境出现严重问题，最简单的解决方法是删除后重建。

    ```bash
    conda deactivate
    conda env remove --name scape_env
    # 删除后，请返回本文档的“一、首次配置”部分，从第1步开始重新操作。
    ```

4.  **让特定终端支持 Conda**
    > 在使用 `conda activate` 之前，Conda 可能需要为每个不同的终端 Shell 进行一次性初始化。

    * **让 Git Bash 支持 Conda:**
      1. 打开 **Git Bash** 终端。
      2. 执行命令：`conda init bash` (如果提示 `command not found`，请先参考下方的第5点解决)。
      3. 重启 Git Bash 终端。

5.  **解决 `conda: command not found` 问题**
    > 如果在终端中输入 `conda` 提示命令未找到，通常意味着 Conda 的路径没有被添加到该终端的 PATH 环境变量中。

    * **对于 Git Bash:**
        1. 打开 **Git Bash**。
        2. 使用 `nano` 编辑器打开配置文件：`nano ~/.bash_profile`
        3. 在文件末尾添加以下两行 (将 `<你的用户名>` 替换为实际用户名):
            ```bash
            # Add Conda to PATH
            export PATH="/c/Users/<USER>/anaconda3/Scripts:$PATH"
            export PATH="/c/Users/<USER>/anaconda3/condabin:$PATH"
            ```
        4. 按 `Ctrl+O` 保存, 按 `Enter` 确认, 按 `Ctrl+X` 退出。
        5. **完全关闭并重新打开** Git Bash 终端。

6.  **解决 Jupyter 运行旧代码的问题**
    > **问题**: 当您修改了外部的 `.py` 文件后，Jupyter Notebook 仍然运行旧的代码。
    >
    > **解决方案**:
    > * **方法一 (推荐)**: **重启内核 (Restart Kernel)**。点击 Notebook 编辑器顶部工具栏的**圆形箭头图标**。
    > * **方法二 (自动化)**: **使用 `autoreload`**。在 Notebook 的第一个单元格加入并运行以下代码。
    >   ```python
    >   %load_ext autoreload
    >   %autoreload 2
    >
