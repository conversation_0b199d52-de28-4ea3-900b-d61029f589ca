"""scape.core.baselines.hybrid_dnn.constants - Hybrid DNN基准模型常量

定义Hybrid DNN基准模型步骤的所有产物和绘图配置模板的逻辑名称。

"""

from enum import Enum


class DnnArtifacts(str, Enum):
    """
    定义Hybrid DNN基准模型步骤的产物逻辑名称。
    """
    TRAINING_CONFIG = "dnn_hybrid_training.configs.training_config"
    MODEL_ASSETS = "dnn_hybrid_training.models.assets_pytorch"
    TUNING_REPORT = "dnn_hybrid_training.reports.hyperparameter_tuning"
    CV_PERFORMANCE_REPORT = "dnn_hybrid_training.reports.cv_performance"
    FINAL_TRAINING_HISTORY_DATA = "dnn_hybrid_training.data_snapshots.final_training_history"
    PREDICTIONS = "dnn_hybrid_prediction.datasets.predictions"


class DnnPlotProfiles(str, Enum):
    """
    定义用于Hybrid DNN组件的PlotProfile模板名称。
    这些名称用于在全局绘图注册表中进行注册和检索。
    """
    # 模块级基础模板
    BASE = "dnn_hybrid.base"

    # 特定的绘图配置模板
    TRAINING_HISTORY = "dnn_hybrid.training_history"
    CROSSPLOT = "dnn_hybrid.crossplot"
    RESIDUALS_PLOT = "dnn_hybrid.residuals_plot"
    RESIDUALS_HIST = "dnn_hybrid.residuals_hist"
