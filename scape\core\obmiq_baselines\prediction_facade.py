from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, Tuple

import pandas as pd
from logwp.infra import get_logger
from logwp.models.datasets.bundle import WpDataFrameBundle
from logwp.extras.tracking import RunContext

from .artifact_handler import ObmiqBaselinesArtifactHandler
from .config import ObmiqBaselinesPredictionConfig
from .constants import ObmiqBaselinesPredictionArtifacts
from .internal.ensemble_model import WeightedEnsembleModel

logger = get_logger()


def _validate_prediction_inputs(
    model_assets: dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    output_curve_name: str,
):
    """验证预测步骤的输入参数。"""
    if "ensemble_model" not in model_assets or "metadata" not in model_assets:
        raise ValueError("模型资产包 'model_assets' 必须包含 'ensemble_model' 和 'metadata' 键。")
    if not isinstance(model_assets["ensemble_model"], WeightedEnsembleModel):
        raise TypeError("'ensemble_model' 必须是 WeightedEnsembleModel 的实例。")
    if "tabular_features_ordered" not in model_assets["metadata"]:
        raise ValueError("模型资产包的元数据 'metadata' 必须包含 'tabular_features_ordered' 键。")
    if prediction_bundle.data.empty:
        raise ValueError("输入数据 'prediction_bundle.data' 不能为空。")

    required_features = model_assets["metadata"]["tabular_features_ordered"]
    missing_features = set(required_features) - set(prediction_bundle.data.columns)
    if missing_features:
        raise ValueError(
            "预测数据 'prediction_bundle.data' 中缺少模型训练时所需的以下特征: "
            f"{', '.join(sorted(list(missing_features)))}"
        )
    logger.debug("所有预测输入参数验证通过。")


def run_obmiq_baselines_prediction_step(
    config: ObmiqPredictionConfig,
    ctx: RunContext,
    model_assets: dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    *,
    output_curve_name: str,
) -> dict[str, Any]:
    """
    使用训练好的OBMIQ Baselines融合模型进行预测。

    Args:
        config: 预测步骤的Pydantic配置对象。
        ctx: 当前运行的上下文，用于追踪。
        model_assets: 由训练步骤产出的模型资产包字典。
        prediction_bundle: 包含待预测数据的WpDataFrameBundle。
        output_curve_name: 预测目标输出的曲线名称。

    Returns:
        一个包含状态和产物路径的字典。
    """
    logger.info("===== OBMIQ Baselines Prediction Step Started =====")
    step_dir = ctx.get_step_dir("obmiq_baselines_prediction")
    handler = ObmiqBaselinesArtifactHandler()

    # 步骤 0: 验证输入
    _validate_prediction_inputs(
        model_assets=model_assets,
        prediction_bundle=prediction_bundle,
        output_curve_name=output_curve_name,
    )

    # 步骤 1: 准备预测数据
    # 关键：必须使用模型元数据中定义的特征顺序
    ensemble_model = model_assets["ensemble_model"]
    ordered_features = model_assets["metadata"]["tabular_features_ordered"]
    X_pred = prediction_bundle.data[ordered_features]
    logger.info(f"已根据模型元数据准备了 {len(ordered_features)} 个特征用于预测。")

    # 步骤 2: 执行预测
    logger.info("正在执行预测...")
    predictions_np = ensemble_model.predict(X_pred)

    # 步骤 3: 格式化并保存结果
    logger.info("正在格式化预测结果并保存产物...")
    result_df = prediction_bundle.data.copy()
    result_df[output_curve_name] = predictions_np

    predictions_path = step_dir / "predictions.csv"
    handler.save_dataframe(result_df, predictions_path)
    ctx.register_artifact(
        predictions_path.relative_to(ctx.run_dir),
        ObmiqBaselinesPredictionArtifacts.PREDICTIONS.value,
        description="包含原始输入和模型预测结果的数据集。",
    )

    logger.info("===== OBMIQ Baselines Prediction Step Finished =====")
    return {"status": "completed", "output_path": str(predictions_path)}
