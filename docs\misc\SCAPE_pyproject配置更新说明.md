# SCAPE项目pyproject.toml配置更新说明

> 版本：1.0
> 日期：2025-07-03
> 状态：已完成

## 概述

本次更新将pyproject.toml配置文件现代化，使其符合SCAPE项目的技术栈要求和CCG编码规范。

## 主要更新内容

### 1. 核心依赖优化

#### 新增必需依赖
- **psutil>=5.9.0**: 性能监控必需，支持GPU系统和日志系统的性能统计
- **structlog版本更新**: 从23.0.0扩展到<25.0.0，支持更新版本

#### 移除过时依赖
- **python-json-logger**: 已被structlog替代，符合现代化日志要求

#### 版本范围优化
- **cupy**: 扩展到<14.0.0，支持更新的CUDA版本
- **cudf**: 扩展到<25.0.0，支持RAPIDS最新版本
- **pytest**: 扩展到<9.0.0，支持最新测试框架

### 2. 新增可选依赖组

#### config组 - 配置文件支持
```toml
config = [
    "toml>=0.10.2,<1.0.0",
    "pyyaml>=6.0.1,<7.0.0",
    "jsonschema>=4.17.0,<5.0.0",
]
```
支持TOML/YAML配置文件加载和JSON Schema验证。

#### 开发工具增强
- **pytest-asyncio**: 异步测试支持
- **line-profiler**: 行级性能分析
- **rich**: 美化终端输出

### 3. 工具配置现代化

#### Ruff配置增强
```toml
[tool.ruff]
select = [
    "E", "W", "F", "I", "B", "C4", "UP", "RUF",
    "N",   # pep8-naming
    "D",   # pydocstyle
    "ANN", # flake8-annotations
]
```
- 新增命名规范检查(N)
- 新增文档字符串检查(D)
- 新增类型注解检查(ANN)
- 配置Google风格文档字符串

#### MyPy严格模式
```toml
[tool.mypy]
strict = true
show_error_codes = true
show_column_numbers = true
```
- 启用严格模式，符合CCG规范要求
- 增强错误信息显示
- 扩展第三方库忽略列表

#### Pytest配置优化
```toml
markers = [
    "unit: Unit tests - 快速单元测试",
    "gpu: GPU-accelerated tests - GPU加速测试",
    "algorithm: Algorithm tests - 算法正确性测试",
    "performance: Performance tests - 性能测试",
]
```
- 新增算法测试和性能测试标记
- 支持异步测试模式
- 优化GPU相关警告过滤

#### Coverage配置增强
```toml
[tool.coverage.run]
branch = true
parallel = true

[tool.coverage.report]
precision = 2
show_missing = true
```
- 启用分支覆盖率统计
- 支持并行测试覆盖率收集
- 增强覆盖率报告格式

### 4. 入口点扩展

#### 新增计算后端入口点
```toml
[project.entry-points."logwp.compute_backends"]
cpu = "logwp.infra.compute_engine:CpuBackend"
gpu = "logwp.infra.compute_engine:GpuBackend"
```
支持GPU计算引擎的插件化扩展。

### 5. 项目元数据更新

#### 关键词扩展
- 新增"gpu-computing"关键词
- 新增"Topic :: Scientific/Engineering :: Artificial Intelligence"分类

#### URL链接完善
- 新增Changelog链接

## CCG规范符合性

### ✅ 已满足的CCG要求

1. **Python 3.11+**: `requires-python = ">=3.11"`
2. **ruff>=0.1.8**: 开发依赖中包含`ruff>=0.1.8,<1.0.0`
3. **mypy>=1.8.0**: 开发依赖中包含`mypy>=1.8.0,<2.0.0`
4. **structlog>=23.0.0**: 核心依赖中包含`structlog>=23.0.0,<25.0.0`
5. **pydantic>=2.5.0**: 核心依赖中包含`pydantic>=2.5.0,<3.0.0`

### ✅ 可选依赖分组

- `scape[ml]`: 机器学习组件
- `scape[gpu]`: GPU计算支持
- `scape[viz]`: 可视化组件
- `scape[dev]`: 开发工具

## 安装指南

### 基础安装
```bash
pip install -e .
```
仅安装核心依赖，适用于生产环境。

### 开发环境
```bash
pip install -e .[dev]
```
包含代码质量工具、测试框架、性能分析工具。

### GPU支持
```bash
# 基础GPU支持（仅numba）
pip install -e .[gpu]

# CUDA 12.x完整支持
pip install -e .[gpu-cuda12]

# CUDA 11.x完整支持
pip install -e .[gpu-cuda11]

# 自动检测安装（推荐）
python scripts/gpu/install_gpu_support.py
```
获得10-15倍性能提升。


```bash
# 1. 基础安装（已验证通过）
pip install -e .

# 2. 自动GPU支持安装
python scripts/gpu/install_gpu_support.py

# 3. 手动GPU支持安装
pip install -e .[gpu-cuda12]  # CUDA 12.x
pip install -e .[gpu-cuda11]  # CUDA 11.x

# 4. 完整开发环境
pip install -e .[dev]

# 5. 验证安装
python scripts/gpu/verify_installation.py
python scripts/gpu/verify_installation.py --test-gpu --test-dev
```


### 完整环境
```bash
# 完整环境（不含GPU）
pip install -e .[full]

# 完整环境 + CUDA 12.x
pip install -e .[full-cuda12]

# 完整环境 + CUDA 11.x
pip install -e .[full-cuda11]
```
安装所有可选依赖，适用于完整开发和研究环境。

### 特定功能组合
```bash
# 机器学习 + GPU
pip install -e .[ml,gpu]

# 开发 + 可视化
pip install -e .[dev,viz]

# 配置文件支持
pip install -e .[config]
```

## 验证安装

### 验证基础功能
```python
import logwp
print("✓ logwp包导入成功")

# 验证GPU支持
if logwp.is_gpu_available():
    print("✓ GPU计算可用")
else:
    print("ℹ️ 使用CPU计算")
```

### 验证开发工具
```bash
# 代码质量检查
ruff check .
ruff format .

# 类型检查
mypy logwp scape

# 运行测试
pytest tests/ -v
```

## 迁移指南

### 从旧版本迁移

1. **更新安装命令**
   ```bash
   # 旧版本
   pip install -r requirements.txt

   # 新版本
   pip install -e .[dev]
   ```

2. **更新CI/CD配置**
   ```yaml
   # 旧版本
   - run: black --check .
   - run: isort --check .
   - run: flake8 .

   # 新版本
   - run: ruff check .
   - run: ruff format --check .
   ```

3. **更新开发工具配置**
   - 移除`.flake8`、`pyproject.toml[tool.black]`等旧配置
   - 使用统一的`[tool.ruff]`配置

## 性能优化建议

### 依赖安装优化
```bash
# 使用pip缓存加速安装
pip install -e .[full] --cache-dir ~/.pip/cache

# 并行安装（如果支持）
pip install -e .[full] --use-pep517
```

### GPU环境配置
```bash
# 检查CUDA版本
nvidia-smi

# 安装对应版本的cupy
pip install cupy-cuda12x  # CUDA 12.x
pip install cupy-cuda11x  # CUDA 11.x
```

## 故障排除

### 常见问题

1. **依赖冲突**
   ```bash
   pip install --upgrade pip
   pip install -e .[dev] --force-reinstall
   ```

2. **GPU库安装失败**
   ```bash
   # 检查CUDA版本兼容性
   python -c "import torch; print(torch.version.cuda)"

   # 手动安装GPU库
   pip install cupy-cuda12x cudf-cu12 numba
   ```

3. **工具配置问题**
   ```bash
   # 验证ruff配置
   ruff check --show-settings

   # 验证mypy配置
   mypy --config-file pyproject.toml --show-config
   ```

## 参考资料

- [PEP 621 - Storing project metadata in pyproject.toml](https://peps.python.org/pep-0621/)
- [SCAPE_CCG_编码与通用规范.md](SCAPE_CCG_编码与通用规范.md)
- [Ruff配置文档](https://docs.astral.sh/ruff/configuration/)
- [MyPy配置文档](https://mypy.readthedocs.io/en/stable/config_file.html)
