{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SCAPE数据准备,数据源:`santos_data_v1.wp.xlsx`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入logwp包用于读取WP文件\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "pd.set_option('display.max_colwidth', 50)\n", "\n", "print(\"库导入完成!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 读取Santos测井数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_file_path = \"./santos_data_v1.wp.xlsx\" #原始数据\n", "reader = WpExcelReader()\n", "\n", "try:\n", "    project = reader.read(data_file_path)\n", "    project.apply_well_mapping()\n", "    print(\"✅ 成功读取原始wp文件\")\n", "    print(f\"📊 项目名称: {project.name}\")\n", "    print(f\"📅 数据集: {project.datasets.keys()}\")\n", "except Exception as e:\n", "    print(f\"❌ 读取原始wp文件失败: {e}\")\n", "    project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 合并K_Label和Logs数据集生成SCAPE训练数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义K_Label数据集的曲线列表\n", "k_label_curves = [\n", "     'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "]\n", "\n", "# 定义Logs数据集的曲线列表\n", "logs_curves = [\n", "    'T2LM',\n", "    'T2_P50',\n", "    'PHIT_NMR',\n", "    'PHI_T2_DIST'\n", "]\n", "\n", "obmiq_pred_curves = [\n", "    'DT2_P50',\n", "    'DPHIT_NMR',\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if project is not None:\n", "    from logwp.models.well_project import WpWellProject\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "\n", "    print(\"🔧 开始合并K_Label和Logs数据集...\")\n", "\n", "    try:\n", "        # 使用左对齐合并方式\n", "        print(\"\\n📍 执行左对齐合并...\")\n", "        la1_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"K_Label\",\n", "            left_curves=k_label_curves,\n", "            right_dataset=\"Logs\",\n", "            right_curves=logs_curves,\n", "            new_dataset_name=\"k_logs\"\n", "        )\n", "        project.add_dataset(\"k_logs\", la1_ds)\n", "\n", "        la2_ds = project.merge_datasets_left_aligned(\n", "            left_dataset=\"k_logs\",\n", "            left_curves= logs_curves + k_label_curves,\n", "            right_dataset=\"OBMIQ_Pred\",\n", "            right_curves=obmiq_pred_curves,\n", "            new_dataset_name=\"k_logs_obmiq\"\n", "        )\n", "        project.add_dataset(\"swift_pso_train\", la2_ds)\n", "\n", "        print(f\"✅ 数据集合并完成\")\n", "        print(f\"   数据形状: {la2_ds.df.shape}\")\n", "\n", "        # =============================\n", "\n", "        # 创建临时项目并保存\n", "        print(\"\\n💾 保存SCAPE训练数据集...\")\n", "        temp_project = WpWellProject(name=\"swift_pso_train\")\n", "        temp_project.add_dataset(\"swift_pso_train\", la2_ds)\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_swift_pso_train.wp.xlsx\"\n", "        writer.write(temp_project, output_path, apply_formatting=True)\n", "        report_path = temp_project.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"scape_swift_pso_train_report.md\"\n", "        )\n", "\n", "        # =============================\n", "\n", "        swift_pso_train_cleaned_ds = project.dropna_dataset(\n", "            source_dataset_name=\"swift_pso_train\",\n", "            curve_names=[],\n", "            new_dataset_name=\"swift_pso_train_cleaned\",\n", "            dropna_how=\"any\"\n", "        )\n", "\n", "        print(f\"✅ 合并后的数据集去空值处理并完成\")\n", "        print(f\"   数据形状: {swift_pso_train_cleaned_ds.df.shape}\")\n", "        print(f\"   数据集类型: {type(swift_pso_train_cleaned_ds).__name__}\")\n", "\n", "        # =============================\n", "\n", "        temp_project2 = WpWellProject(name=\"Santos_SCAPE_train_cleaned\")\n", "        temp_project2.add_dataset(\"swift_pso_train_cleaned\", swift_pso_train_cleaned_ds)\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_swift_pso_train_cleaned.wp.xlsx\"\n", "        writer.write(temp_project2, output_path, apply_formatting=True)\n", "        report_path = temp_project2.generate_data_summary(\n", "            format=\"markdown\",\n", "            template=\"default\",\n", "            output_path=\"scape_swift_pso_train_cleaned_report.md\"\n", "        )\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 数据集合并/清理失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 SWIFT-PSO 训练数据集生成完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过SWIFT-PSO训练数据集生成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 生成预测数据集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_apply = project.merge_datasets_left_aligned(\n", "    left_dataset=\"Logs\",\n", "    left_curves=logs_curves,\n", "    right_dataset=\"OBMIQ_Pred\",\n", "    right_curves=obmiq_pred_curves,\n", "    new_dataset_name=\"swift_pso_apply\"\n", ")\n", "\n", "project.add_dataset(\"swift_pso_apply\",ds_apply)\n", "\n", "ds_apply_cleaned=project.dropna_dataset(\n", "    source_dataset_name=\"swift_pso_apply\",\n", "    curve_names=[],\n", "    new_dataset_name=\"swift_pso_apply_cleaned\",\n", "    dropna_how=\"any\"\n", ")\n", "temp_project = WpWellProject(name=\"swift_pso_apply_cleaned\")\n", "temp_project.add_dataset(\"swift_pso_apply_cleaned\", ds_apply_cleaned)\n", "\n", "writer = WpExcelWriter()\n", "output_path = \"scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "writer.write(temp_project, output_path, apply_formatting=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. PLT & Core_K"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if project is not None:\n", "    from logwp.models.well_project import WpWellProject\n", "    from logwp.io.wp_excel.wp_excel_writer import WpExcelWriter\n", "\n", "    try:\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_plt_val.wp.xlsx\"\n", "        writer.write(project, output_path,dataset_names=[\"PLT\"],apply_formatting=True)\n", "\n", "        writer = WpExcelWriter()\n", "        output_path = \"scape_core_k_val.wp.xlsx\"\n", "        writer.write(project, output_path,dataset_names=[\"K_Val\"],apply_formatting=True)\n", "\n", "\n", "    except Exception as e:\n", "        print(f\"❌ PLT/K_Val写入失败: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "\n", "    print(\"\\n🎉 PLT/K_Val写入完成！\")\n", "\n", "else:\n", "    print(\"⚠️ 项目未成功加载，跳过PLT/K_Val数据集生成\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 4}