"""渗透率相关性分析的核心计算服务。

此模块包含所有相关性检验的底层计算逻辑，遵循无状态服务设计。
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, Optional

import numpy as np
import pandas as pd
from scipy import stats
from logwp.models.constants import WpDepthRole
from logwp.models.exceptions import WpDataError

if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpDataFrameBundle

def compute_all_metrics_for_well(
    well_name: str,
    left_bundle: WpDataFrameBundle,
    left_curve: str,
    right_bundle: WpDataFrameBundle,
    right_curve: str,
    relaxed_wells: Optional[list[str]] = None,
) -> Dict[str, Any]:
    """
    为单口井计算所有渗透率相关性检验指标。

    这是渗透率相关性检验的内部计算主入口，按顺序执行所有分析步骤。
    """

    # 步骤 1: 对齐数据
    aligned_df = _align_data_for_well(
        well_name, left_bundle, left_curve, right_bundle, right_curve
    )

    if aligned_df.empty:
        # 返回一个包含空结果的结构，以避免下游代码出错
        return {
            "well_name": well_name,
            "aligned_df": pd.DataFrame(),
            "hit_rates": {},
            "correlations": {"pearson_r": np.nan, "spearman_rho": np.nan, "r_squared": np.nan},
            "conclusion": "No data to analyze.",
        }

    # 步骤 2: 计算符合率
    hit_rates = calculate_hit_rates(aligned_df)

    # 步骤 3: 计算相关系数
    correlations = calculate_correlation_coefficients(aligned_df)

    # 步骤 4: 生成检验结论
    conclusion = _get_conclusion(hit_rates, well_name, relaxed_wells)

    # 步骤 5: 组装最终结果
    result: Dict[str, Any] = {
        "well_name": well_name,
        "aligned_df": aligned_df,
        "hit_rates": hit_rates,
        "correlations": correlations,
        "conclusion": conclusion,
    }

    return result

def _align_data_for_well(
    well_name: str,
    left_bundle: WpDataFrameBundle,
    left_curve: str,
    right_bundle: WpDataFrameBundle,
    right_curve: str,
) -> pd.DataFrame:
    """
    为单口井对齐左右两个Bundle的数据。

    以右侧Bundle的深度点为基准，从左侧Bundle中插值获取对应深度的值，
    最终生成一个包含真值和预测值的配对DataFrame。

    Args:
        well_name (str): 要处理的井名。
        left_bundle (WpDataFrameBundle): 左侧数据集（通常是预测值来源）。
        left_curve (str): 左侧数据集中要对比的曲线名。
        right_bundle (WpDataFrameBundle): 右侧数据集（通常是真值来源）。
        right_curve (str): 右侧数据集中要对比的曲线名。

    Returns:
        pd.DataFrame: 对齐后的数据表，包含 'DEPTH', 'TRUE_VALUE', 'PRED_VALUE' 列。
    """

    # --- 修正: 增加更明确的元数据验证，替代通用的try-except ---
    # 1. 从右侧Bundle（真值源）安全地获取列名、深度点和真值
    well_id_curves = right_bundle.curve_metadata.get_well_identifier_curves()
    if not well_id_curves:
        raise WpDataError(f"在右侧Bundle '{right_bundle.name}' 中缺少井名标识曲线。", context={"well_name": well_name})
    well_col_name = right_bundle.curve_metadata.get_curve(well_id_curves[0]).dataframe_column_name

    depth_curves = right_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
    if not depth_curves:
        raise WpDataError(f"在右侧Bundle '{right_bundle.name}' 中缺少单一深度(SINGLE)曲线。", context={"well_name": well_name})
    depth_col_name = right_bundle.curve_metadata.get_curve(depth_curves[0]).dataframe_column_name

    true_value_attrs = right_bundle.curve_metadata.get_curve(right_curve)
    if not true_value_attrs:
        raise WpDataError(f"在右侧Bundle '{right_bundle.name}' 的元数据中未找到曲线 '{right_curve}'。", context={"well_name": well_name})
    true_value_col_name = true_value_attrs.dataframe_column_name

    well_df_right = right_bundle.data[right_bundle.data[well_col_name] == well_name]
    if well_df_right.empty:
        return pd.DataFrame()

    depths_to_align = well_df_right[depth_col_name].tolist()
    true_values = well_df_right[true_value_col_name].tolist()

    # 2. 从左侧Bundle（预测值源）插值获取预测值
    predicted_values = left_bundle.get_curve_values_at_depths(
        well_name=well_name, depths=depths_to_align, curve_name=left_curve, interpolation_method='nearest'
    )

    # 3. 组合并清理数据
    aligned_df = pd.DataFrame({'DEPTH': depths_to_align, 'TRUE_VALUE': true_values, 'PRED_VALUE': predicted_values})
    aligned_df.dropna(inplace=True)

    return aligned_df


def calculate_hit_rates(
    aligned_df: pd.DataFrame, error_multiples: list[int] = [3, 5, 10]
) -> Dict[str, Any]:
    """
    计算落在指定误差倍数边界内的数据点符合率。

    Args:
        aligned_df (pd.DataFrame): 对齐后的数据表，必须包含 'TRUE_VALUE' 和 'PRED_VALUE' 列。
        error_multiples (list[int]): 需要计算的误差倍数列表。

    Returns:
        Dict[str, Any]: 包含各误差倍数符合率的字典,
            e.g., {"3x": 75.0, "5x": 85.5, ...}。
    """
    if aligned_df.empty:
        return {f'{m}x': 0.0 for m in error_multiples}

    k_pred = aligned_df['PRED_VALUE'].values
    k_true = aligned_df['TRUE_VALUE'].values

    hit_rates: Dict[str, Any] = {}
    n_total = len(aligned_df)

    for factor in error_multiples:
        lower_bound = k_true / factor
        upper_bound = k_true * factor

        n_hits = ((k_pred >= lower_bound) & (k_pred <= upper_bound)).sum()
        rate = (n_hits / n_total) * 100 if n_total > 0 else 0.0
        hit_rates[f'{factor}x'] = round(rate, 2)

    return hit_rates


def calculate_correlation_coefficients(
    aligned_df: pd.DataFrame,
) -> Dict[str, Any]:
    """
    计算皮尔逊、斯皮尔曼和决定系数。

    - 皮尔逊相关性 (Pearson R): 在对数变换后的数据上计算，以衡量对数-对数图上的线性关系。
    - 斯皮尔曼相关性 (Spearman rho): 在原始数据上计算，以衡量单调关系，对异常值不敏感。
    - 决定系数 (R-squared): 皮尔逊相关系数的平方。

    Args:
        aligned_df (pd.DataFrame): 对齐后的数据表，必须包含 'TRUE_VALUE' 和 'PRED_VALUE' 列。

    Returns:
        Dict[str, Any]: 包含三个相关性系数值的字典。
    """
    # 如果数据点少于2个，无法计算相关性
    if len(aligned_df) < 2:
        return {"pearson_r": np.nan, "spearman_rho": np.nan, "r_squared": np.nan}

    k_true = aligned_df['TRUE_VALUE'].values
    k_pred = aligned_df['PRED_VALUE'].values

    # --- 斯皮尔曼等级相关性 (Spearman's Rho) ---
    # 直接在原始数据上计算，对异常值和非线性关系稳健
    spearman_rho, _ = stats.spearmanr(k_true, k_pred)

    # --- 皮尔逊相关性 (Pearson's R) ---
    # 在对数变换后的数据上计算，以处理数量级差异
    # 过滤掉非正值，因为log函数无法处理它们
    log_mask = (k_true > 0) & (k_pred > 0)
    if log_mask.sum() < 2:
        # 如果有效数据点少于2个，无法计算
        pearson_r = np.nan
    else:
        log_k_true = np.log10(k_true[log_mask])
        log_k_pred = np.log10(k_pred[log_mask])
        pearson_r, _ = stats.pearsonr(log_k_true, log_k_pred)

    # --- 决定系数 (R-squared) ---
    # R-squared 是 Pearson R 的平方
    r_squared = pearson_r**2 if not np.isnan(pearson_r) else np.nan

    return {
        "pearson_r": float(pearson_r),
        "spearman_rho": float(spearman_rho),
        "r_squared": float(r_squared),
    }


def _get_conclusion(
    hit_rates: Dict[str, Any],
    well_name: str,
    relaxed_wells: Optional[list[str]] = None,
) -> str:
    """
    根据符合率和井名，生成检验结论。

    遵循《方法说明书》§4.6.2的检验标准。
    - 通用标准: 3x符合率 > 70% 且 10x符合率 > 90%
    - 放宽标准: 5x符合率 > 50% (适用于在`relaxed_wells`列表中指定的井)

    Args:
        hit_rates (Dict[str, Any]): 计算出的符合率字典。
        well_name (str): 当前检验的井名。
        relaxed_wells (Optional[list[str]]): 需要应用放宽标准的井名列表。

    Returns:
        str: 检验结论字符串，如 "Pass: ...", "Fail: ..."。
    """
    if well_name in (relaxed_wells or []):
        rate_5x = hit_rates.get('5x', 0.0)
        if rate_5x > 50.0:
            return f"Pass: 5x符合率 {rate_5x:.1f}% > 50% (满足井 '{well_name}' 的放宽标准)。"
        else:
            return f"Fail: 5x符合率 {rate_5x:.1f}% <= 50% (未满足井 '{well_name}' 的放宽标准)。"
    else:
        # 其他井采用通用标准
        rate_3x = hit_rates.get('3x', 0.0)
        rate_10x = hit_rates.get('10x', 0.0)

        pass_3x = rate_3x > 70.0
        pass_10x = rate_10x > 90.0

        if pass_3x and pass_10x:
            return f"Pass: 3x符合率 {rate_3x:.1f}% > 70% 且 10x符合率 {rate_10x:.1f}% > 90%。"
        else:
            reasons = []
            if not pass_3x:
                reasons.append(f"3x符合率 {rate_3x:.1f}% <= 70%")
            if not pass_10x:
                reasons.append(f"10x符合率 {rate_10x:.1f}% <= 90%")
            return f"Fail: {' 且 '.join(reasons)}。"
