{"run_id": "20250724-133114-0ed1aaef", "start_time_utc": "2025-07-24T13:31:14.599078Z", "end_time_utc": "2025-07-24T13:33:14.000432Z", "duration_seconds": 119.401, "status": "COMPLETED", "lineage": {"inputs": {}, "code_version": {}}, "config_snapshot_path": null, "parameters": {}, "metrics": {}, "artifacts": {"obmiq_training.configs.training_config_snapshot": {"path": "obmiq_training_pytorch/training_config.json", "type": "data", "metadata": {"hash": "cd204c1409459ec1d05067dc376eb97b7bbfbb0144b5477c0c5cf33e48a8d86f", "hash_algorithm": "sha256", "size_bytes": 509}, "description": "Snapshot of the training configuration used for this run."}, "obmiq_training.reports.cv_performance": {"path": "obmiq_training_pytorch/cv_performance_report.csv", "type": "data", "metadata": {"hash": "219cecdfebe4dfbb2c1a82331179b93669b98cc80f941c28ce9ff7eabc3d5759", "hash_algorithm": "sha256", "size_bytes": 293}, "description": "LOWO-CV中每一折的最佳验证损失和对应的超参数。"}, "obmiq_training.reports.hyperparameter_tuning": {"path": "obmiq_training_pytorch/hyperparameter_tuning_report.json", "type": "data", "metadata": {"hash": "6179fb6be353acda92b0c408d82de6ea72e571c09989335a2d44574489fc3d43", "hash_algorithm": "sha256", "size_bytes": 198}, "description": "在所有CV折中聚合得到的全局最佳超参数组合。"}, "obmiq_training.logs.tensorboard": {"path": "obmiq_training_pytorch/tensorboard_logs", "type": "unknown", "metadata": {"size_bytes": 0}, "description": "用于TensorBoard可视化的日志文件目录。"}, "obmiq_training.models.assets_pytorch": {"path": "obmiq_training_pytorch/model_assets_pytorch.pkl", "type": "model", "metadata": {"hash": "ff16a0ca8c86d8038e2ee3370909c442b12518b651e82ae0532a71ea86963716", "hash_algorithm": "sha256", "size_bytes": 16323}, "description": "包含模型权重、超参数和预处理器的PyTorch模型资产包。"}, "obmiq_training.data_snapshots.final_training_history": {"path": "obmiq_training_pytorch/final_training_history.csv", "type": "data", "metadata": {"hash": "62cf4d0ac74157c8c1c23802eeb03b44cac272765f228b61fc39facba7fd8573", "hash_algorithm": "sha256", "size_bytes": 1284}, "description": "最终模型训练过程中的损失变化历史。"}, "obmiq_training.plots.final_training_history": {"path": "obmiq_training_pytorch/final_training_history.png", "type": "image", "metadata": {"hash": "94a227ea7d3bb773214e6e873a4edba247f3848f0e69f7101a67f14bd8c1d26c", "hash_algorithm": "sha256", "size_bytes": 213006}, "description": "最终模型训练的损失曲线图。"}, "obmiq_training.data_snapshots.final_model_evaluation": {"path": "obmiq_training_pytorch/final_model_evaluation.csv", "type": "data", "metadata": {"hash": "0c98bcb66e9f9cb791b05f55dd6eb64b11aab10d3008672cc03225a806bf06a3", "hash_algorithm": "sha256", "size_bytes": 2169587}, "description": "最终模型在全部训练数据上的预测和残差结果。"}, "obmiq_training.plots.eval_crossplot_dt2_p50": {"path": "obmiq_training_pytorch/eval_crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "fec6d0bf09801f1a8128ae2beeea5ec3667d2917360cc2bbd8b8791673c4c502", "hash_algorithm": "sha256", "size_bytes": 593527}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50"}, "obmiq_training.plots.eval_crossplot_dphit_nmr": {"path": "obmiq_training_pytorch/eval_crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "3bc031233566f501819af72f3ee65128e48085810cb01e0f67c835b09dd005cc", "hash_algorithm": "sha256", "size_bytes": 630217}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr"}, "obmiq_training.plots.eval_residuals_plot_dt2_p50": {"path": "obmiq_training_pytorch/eval_residuals_plot_dt2_p50.png", "type": "image", "metadata": {"hash": "e12ca81ca91390fcd367bcfe2bc3e56e3abea8e7f688769c8a5bc53fead05901", "hash_algorithm": "sha256", "size_bytes": 760377}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50"}, "obmiq_training.plots.eval_residuals_plot_dphit_nmr": {"path": "obmiq_training_pytorch/eval_residuals_plot_dphit_nmr.png", "type": "image", "metadata": {"hash": "3f0295d06fa7bd3d62b4f7063f24fe1f2e7d706022c598467d033401c54f8537", "hash_algorithm": "sha256", "size_bytes": 883873}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr"}, "obmiq_training.plots.eval_residuals_hist_dt2_p50": {"path": "obmiq_training_pytorch/eval_residuals_hist_dt2_p50.png", "type": "image", "metadata": {"hash": "952f77d3f37ce4a527704466f8d406c40b4899b6b871bd5a4cbdeb9d335cff42", "hash_algorithm": "sha256", "size_bytes": 173529}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50"}, "obmiq_training.plots.eval_residuals_hist_dphit_nmr": {"path": "obmiq_training_pytorch/eval_residuals_hist_dphit_nmr.png", "type": "image", "metadata": {"hash": "a22550675fd29bb2a9a5990d8bc453be37922aeb880ca290439456148ddb78f1", "hash_algorithm": "sha256", "size_bytes": 201289}, "description": "Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr"}, "obmiq_training.plots.captum_ig_summary_DT2_P50": {"path": "obmiq_training_pytorch/captum_ig_summary_DT2_P50.png", "type": "image", "metadata": {}, "description": "Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。"}, "obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50": {"path": "obmiq_training_pytorch/captum_ig_summary_DT2_P50_data.csv", "type": "data", "metadata": {"hash": "87922d949f05c2fa2f06aac728e677c3ac5464127738834278c0470d020acfa4", "hash_algorithm": "sha256", "size_bytes": 367988}, "description": "Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50)"}, "obmiq_training.plots.captum_ig_summary_DPHIT_NMR": {"path": "obmiq_training_pytorch/captum_ig_summary_DPHIT_NMR.png", "type": "image", "metadata": {}, "description": "Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。"}, "obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR": {"path": "obmiq_training_pytorch/captum_ig_summary_DPHIT_NMR_data.csv", "type": "data", "metadata": {"hash": "39b5d5cb898541ef20ac3734a8545ee10016e2987c44f9262094ea899e45a377", "hash_algorithm": "sha256", "size_bytes": 373316}, "description": "Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR)"}, "obmiq_training.data_snapshots.captum_sequence_attributions_dir": {"path": "obmiq_training_pytorch/captum_sequence_attributions_dir", "type": "unknown", "metadata": {"size_bytes": 4096}}, "obmiq_training.plots.captum_saliency_examples_dir": {"path": "obmiq_training_pytorch/captum_saliency_examples_dir", "type": "unknown", "metadata": {"size_bytes": 12288}}, "obmiq_training.models.onnx_model": {"path": "obmiq_training_pytorch/obmiq_model.onnx", "type": "unknown", "metadata": {"hash": "7ab12311b55e33e16d518641d6851b3c7023cae035cc6a1fd9f087daf7ca47f2", "hash_algorithm": "sha256", "size_bytes": 10566}, "description": "可用于跨平台部署的ONNX格式模型。"}, "obmiq_prediction.datasets.predictions": {"path": "obmiq_prediction_pytorch/predictions.csv", "type": "data", "metadata": {"hash": "0307de3350048aa9c9d515975f87ebbbb38285b472f5168b570f68cc65eab435", "hash_algorithm": "sha256", "size_bytes": 3489324}, "description": "包含原始输入和模型预测结果的数据集"}, "obmiq_prediction.plots.crossplot_dt2_p50": {"path": "obmiq_prediction_pytorch/crossplot_dt2_p50.png", "type": "image", "metadata": {"hash": "fec6d0bf09801f1a8128ae2beeea5ec3667d2917360cc2bbd8b8791673c4c502", "hash_algorithm": "sha256", "size_bytes": 593527}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50"}, "obmiq_prediction.plots.crossplot_dphit_nmr": {"path": "obmiq_prediction_pytorch/crossplot_dphit_nmr.png", "type": "image", "metadata": {"hash": "9c1e0f8b329dc87e4ed9b0422be22efe7dbecb364ceded0e8d9b2e8f0014fb80", "hash_algorithm": "sha256", "size_bytes": 302678}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr"}, "obmiq_prediction.plots.residuals_plot_dt2_p50": {"path": "obmiq_prediction_pytorch/residuals_plot_dt2_p50.png", "type": "image", "metadata": {"hash": "21963cf19588cd8c48809c4cc540abd8385b2639b85218e23a3396f5ffe99b13", "hash_algorithm": "sha256", "size_bytes": 760465}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50"}, "obmiq_prediction.plots.residuals_plot_dphit_nmr": {"path": "obmiq_prediction_pytorch/residuals_plot_dphit_nmr.png", "type": "image", "metadata": {"hash": "c76b3c5d7c7aee2835db020fea1d097173ff08e802af3fa57631fbc6c4deed6a", "hash_algorithm": "sha256", "size_bytes": 889778}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr"}, "obmiq_prediction.plots.residuals_hist_dt2_p50": {"path": "obmiq_prediction_pytorch/residuals_hist_dt2_p50.png", "type": "image", "metadata": {"hash": "e52522fb39e82f86613b34396f66e636e581ee748e4c21f1bb8bedb0ac2e1006", "hash_algorithm": "sha256", "size_bytes": 173535}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50"}, "obmiq_prediction.plots.residuals_hist_dphit_nmr": {"path": "obmiq_prediction_pytorch/residuals_hist_dphit_nmr.png", "type": "image", "metadata": {"hash": "7789657dac403b58dd036cf0326f99746815e30d5ddba54db9b6e7f4fca8a845", "hash_algorithm": "sha256", "size_bytes": 201120}, "description": "Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr"}}}