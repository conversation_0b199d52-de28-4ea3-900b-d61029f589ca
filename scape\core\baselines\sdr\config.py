"""scape.core.baselines.sdr.config - SDR基准模型配置

为SDR基准模型的训练和预测步骤定义类型安全的Pydantic配置模型。

"""

from __future__ import annotations

from pydantic import BaseModel, Field

class SdrModelParameters(BaseModel):
    """定义SDR模型优化后参数的结构，用于验证和解析。"""
    KSDR_A: float = Field(..., description="SDR渗透率方程系数")
    PHIT_EXP: float = Field(..., description="孔隙度指数")
    T2LM_EXP: float = Field(..., description="T2对数均值指数")
    RHO_NMR: float = Field(..., description="核磁共振表面弛豫率")

class SdrTrainingConfig(BaseModel):
    """SDR基准模型训练步骤的配置。

    SDR模型的参数 (KSDR_A, PHIT_EXP, RHO_NMR) 是通过优化数据得到的，
    因此本训练步骤没有用户可调的超参数。此配置类为空，为未来扩展保留。
    """
    pass


class SdrPredictionConfig(BaseModel):
    """SDR基准模型预测步骤的配置。

    预测步骤同样没有可配置的参数。
    """
    pass
