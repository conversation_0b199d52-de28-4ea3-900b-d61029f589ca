# `scape.core.validation` 组件开发范例

**版本: 1.0**

## 1. 引言

### 1.1. 目标与定位

本文档旨在作为**可追踪机器学习组件开发框架**的一个完整、详尽的**实例化范例**。它以`scape.core.validation`这个多步骤包（包含PLT盲井检验和渗透率相关性分析两个步骤）为蓝本，展示了如何将框架中的抽象原则、规范和最佳实践，转化为具体的、高质量的工程代码。

本文档不仅是`validation`包的说明书，更是未来开发新组件或重构旧组件时可以遵循的**模板和指南**。

### 1.2. 实现要点

`scape.core.validation`包严格遵循了框架的核心原则，其实现要点包括：

*   **关注点分离**: 严格划分了`facade`（公共接口）、`internal`（核心算法）、`config`（参数配置）、`plotting`（绘图复现）等模块的职责。
*   **清晰的接口**: 通过`plt_analysis_facade.py`和`perm_corr_facade.py`提供稳定、明确的公共API，隐藏所有内部实现。
*   **可追踪与可复现**: 深度集成了`logwp.extras.tracking`的`RunContext`，所有参数、指标和产物都被精确追踪。通过“数据快照”和“配置快照”原则，确保了所有图表的可复现性。
*   **现代化工程实践**: 全面采用Pydantic进行配置管理，通过`PlotProfile`系统实现绘图与样式的解耦，并提供了高质量的默认绘图模板。

---

## 2. 核心概念对照表

下表将《框架》中的核心概念与`scape.core.validation`包中的具体实现一一对应：

| 框架概念 | `scape.core.validation` 中的具体实现 |
| :--- | :--- |
| **多步骤包** | `scape.core.validation` 整个包 |
| **步骤 (Step)** | 1. PLT盲井检验 (PLT Blind Well Analysis) <br> 2. 渗透率相关性分析 (Permeability Correlation Analysis) |
| **门面 (Facade)** | 1. `plt_analysis_facade.py` <br> 2. `perm_corr_facade.py` |
| **主执行函数** | 1. `run_plt_analysis_step()` <br> 2. `run_perm_correlation_step()` |
| **配置 (Config)** | 1. `config.PltAnalysisConfig` <br> 2. `config.PermCorrelationConfig` |
| **内部逻辑 (Internal)** | `internal/plt_computer.py` <br> `internal/perm_corr_computer.py` |
| **产物常量** | 1. `constants.PltAnalysisArtifacts` <br> 2. `constants.PermCorrelationArtifacts` |
| **产物处理器** | `artifact_handler.ValidationArtifactHandler` (服务于所有步骤) |
| **绘图复现** | `plotting.py` 中的 `replot_*_from_snapshot()` 函数 |
| **绘图配置常量** | `constants.PltAnalysisPlotProfiles` <br> `constants.PermCorrelationPlotProfiles` |
| **数据快照** | `..._DATA_PREFIX` (多个CSV文件) |

---

## 3. 组件架构与目录结构

`scape.core.validation`作为一个标准的多步骤包，其目录结构如下：

```text
scape/core/validation/
 ├── init.py # 导出所有步骤的公共API
 ├── README.md # 本文档
 ├── plt_analysis_facade.py # 【必须】PLT分析步骤的门面
 ├── perm_corr_facade.py # 【必须】渗透率相关性分析步骤的门面
 ├── config.py # 【必须】包含所有步骤的Pydantic配置模型
 ├── constants.py # 【推荐】包含所有步骤的产物和绘图配置常量
 ├── artifact_handler.py # 【推荐】服务于所有步骤的无状态产物处理器
 ├── plotting.py # (可选) 定义从数据快照复现图表的功能
 ├── plot_profiles.py # (可选) 注册本模块专属的PlotProfile
 ├── exceptions.py # (可选) 定义包的自定义异常
 └── internal/ # 【必须】存放所有内部实现细节的目录
    ├── init.py
    ├── plt_computer.py # PLT分析的核心算法
    └── perm_corr_computer.py # 渗透率相关性分析的核心算法
```
## 4. 开发细节与最佳实践

### 4.1. 配置 (`config.py`)

**原则：参数的分类与处理**

我们严格遵循框架对参数的分类原则，以`run_perm_correlation_step`为例：

```python
# perm_corr_facade.py
def run_perm_correlation_step(
    config: PermCorrelationConfig,  # <--- 模型/算法参数
    ctx: RunContext,
    left_bundle: WpDataFrameBundle, # <--- 数据输入
    right_bundle: WpDataFrameBundle,
    *,
    left_curve: str,                # <--- 数据选择器参数
    right_curve: str,
    plot_profile: PlotProfile,      # <--- 执行/表现层参数
) -> Dict[str, Any]:
    ...
```

- 模型/算法参数: relaxed_wells等直接影响算法科学逻辑的参数，必须在config.PermCorrelationConfig中用Pydantic模型定义。
- 数据选择器参数: left_curve, right_curve等指定使用哪个数据列的参数，必须作为facade函数的直接、关键字参数传入。
- 执行/表现层参数: plot_profile控制视觉样式，不影响计算数值，必须作为facade函数的直接、关键字参数传入。

### 4.2. 产物、常量与处理器

- 产物常量 (constants.py): 我们为每个Step定义了独立的Enum来管理其产物，并对按井生成的产物使用了前缀模式，例如 PltAnalysisArtifacts.CAPTURE_CURVE_PLOT_PREFIX。
- 产物处理器 (artifact_handler.py): ValidationArtifactHandler是一个无状态的工具类，它服务于包内所有Step，只负责文件I/O。
- 保存与注册原则: facade函数中，总是先调用handler将数据保存到step_dir下的物理文件，然后再调用ctx.register_artifact()将该文件的元数据注册到RunContext中。

### 4.3. 绘图 (PlotProfile与数据快照)

#### 4.3.1. 数据快照优先原则

本包严格遵循“数据快照”原则，确保了可视化的完全可复现性。对每一个图表，facade都执行以下流程：

1. 保存配置快照: 将本次运行使用的PlotProfile对象保存为一个.json文件。
2. 保存数据快照: 将用于绘图的DataFrame保存为一个.csv文件。
3. 复现绘图: 调用plotting.py中的replot_*函数，传入数据和配置快照的路径来生成最终的.png/.svg图表。

这个流程确保了即使需要调整样式，我们也可以从轻量级的快照文件快速、精确地复现图表，而无需重新运行整个分析。

#### 4.3.2. 【重要】`PlotProfile`处理模式：灵活性与易用性的结合

为了在提供高质量默认样式和给予用户最大定制自由度之间取得平衡，本包采用了一种特别的`PlotProfile`处理模式。

- 默认模板注册 (`plot_profiles.py`):

  - 我们提供了一个plot_profiles.py模块，它定义了一套专业、美观的默认绘图模板（如validation.plt.capture_curve）。
  - 这些模板在scape.core.validation包被导入时，会自动注册到全局的logwp.extras.plotting.registry中。

- Facade接口设计 (facade函数):

  - 与swift_pso不同，本包的facade函数（如run_plt_analysis_step）直接接收完全实例化的PlotProfile对象，而不是它们的名称字符串。
  - 例如：plot_profiles: Dict[PltPlotTypes, PlotProfile]

- 推荐使用模式 (Get -> Modify -> Pass): 这种设计催生了一种非常强大且灵活的使用模式：

  - 获取 (Get): 在工作流脚本中，首先从全局注册表plot_registry中获取一个默认的PlotProfile模板。
  - 修改 (Modify): 根据本次运行的特定需求，在运行时动态地修改这个PlotProfile对象的任意属性（如标题、颜色、字体大小）。
  - 传入 (Pass): 将这个被定制后的PlotProfile对象传入facade函数。

示例代码：

```python
# 在一个工作流脚本中
from scape.core.validation import (
    run_plt_analysis_step,
    PltPlotTypes,
    PltAnalysisPlotProfiles,
)
from logwp.extras.plotting import registry as plot_registry

# 1. 从注册表中获取默认模板
capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE)

# 2. 在运行时对其进行动态修改
capture_profile.title_props["label"] = "Custom Title for This Specific Run"
capture_profile.artist_props["capture_curve"]["color"] = "orange"

# 3. 将修改后的对象传入facade函数
run_plt_analysis_step(
    # ... 其他参数 ...
    plot_profiles={
        PltPlotTypes.CAPTURE_CURVE: capture_profile, # 传入修改后的对象
        # ... 其他图表可以使用未修改的默认模板 ...
    }
)
```

这种模式既为用户提供了开箱即用的高质量默认图表，又赋予了他们在需要时进行精细化、临时性定制的全部能力，而无需修改包的源代码。

### 4.4. 门面 (facade) 与内部逻辑 (internal)

- facade的职责 (业务流程编排层): 与外界交互、流程控制、追踪与I/O。
- internal的职责 (纯粹算法实现层): 核心计算，环境无关，高度可移植、可单元测试。

## 5. 总结

scape.core.validation包是可追踪机器学习组件开发框架在实践中的全面体现。通过遵循本范例中展示的架构、原则和编码模式，您可以构建出同样高质量、可维护、可复现的机器学习组件。

关键要点回顾:

1. 分层清晰: facade管流程，internal管算法。
2. 配置分离: Pydantic管“契约”，直接参数管“环境”。
3. 追踪完备: 一切皆可追踪——参数、指标、产物（包括数据和配置快照）。
4. 复现至上: “数据快照”是保证图表可复现性的关键。
5. 灵活的绘图配置: 通过“获取-修改-传入”模式，实现了默认模板与运行时定制的完美结合。
