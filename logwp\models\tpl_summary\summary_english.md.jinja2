# Well Log Data Summary Report

**Project Name**: {{ project_info.name }}
**Generated At**: {{ generation_info.generated_at }}
**Project ID**: {{ project_info.project_id }}

## 1. Project Information
- Created At: {{ project_info.created_at }}
- Modified At: {{ project_info.modified_at }}
- Dataset Count: {{ project_info.dataset_count }}
- Default Depth Unit: {{ project_info.default_depth_unit }}

## 2. Header Attributes
- Total Attributes: {{ head_attributes.total_attributes }}
{% if head_attributes.by_wfs_category %}
- Distribution by WFS Category:
{% for category, count in head_attributes.by_wfs_category.items() %}
  - {{ category }}: {{ count }} attributes
{% endfor %}
{% endif %}
{% if head_attributes.attribute_types %}
- Distribution by Data Type:
{% for type_name, count in head_attributes.attribute_types.items() %}
  - {{ type_name }}: {{ count }} attributes
{% endfor %}
{% endif %}

{% if head_attributes.attribute_records %}
- **Detailed Header Attributes List**:

| No. | Category | Dataset | Well | Curve | Attribute | Data Type | Unit | Value | Description |
|-----|----------|---------|------|-------|-----------|-----------|------|-------|-------------|
{% for record in head_attributes.attribute_records %}
| {{ loop.index }} | {{ record.category }} | {{ record.dataset or '-' }} | {{ record.well or '-' }} | {{ record.curve or '-' }} | {{ record.attribute }} | {{ record.data_type }} | {{ record.unit or '-' }} | {{ record.value }} | {{ record.description or '-' }} |
{% endfor %}
{% endif %}

## 3. Well Mappings
- Total Mappings: {{ well_mappings.total_mappings }}
{% if well_mappings.mappings_list %}
- Mapping Details:
{% for mapping in well_mappings.mappings_list %}
  - {{ mapping.source }} → {{ mapping.target }}
{% endfor %}
{% endif %}

## 4. Dataset Overview

### 4.1 Dataset Summary
| No. | Dataset Name | Type | Sampling Interval | Curve Count | Total Rows |
|-----|--------------|------|-------------------|-------------|------------|
{% for dataset_name, dataset_info in datasets.datasets.items() %}
{% if dataset_info.error is not defined %}
{% set basic_info = dataset_info.basic_info %}
| {{ loop.index }} | {{ basic_info.name or dataset_name }} | {{ basic_info.type or 'N/A' }} | {{ basic_info.sampling_interval or 'N/A' }} | {{ basic_info.curve_count or 'N/A' }} | {{ basic_info.total_rows or 'N/A' }} |
{% else %}
| {{ loop.index }} | {{ dataset_name }} | ERROR | - | - | - |
{% endif %}
{% endfor %}

### 4.2 Dataset Details

{% for dataset_name, dataset_info in datasets.datasets.items() %}
#### 4.2.{{ loop.index }} {{ dataset_name }}

{% if dataset_info.error is defined %}
**Error**: {{ dataset_info.error }}

{% else %}
{% set basic_info = dataset_info.basic_info %}
- **Basic Info**: Type={{ basic_info.type or 'N/A' }}, Sampling Interval={{ basic_info.sampling_interval or 'N/A' }}
- **DataFrame Structure**: {{ basic_info.total_columns or 'N/A' }} columns, {{ basic_info.total_rows or 'N/A' }} rows

{% if dataset_info.metadata_info %}
{% set metadata_info = dataset_info.metadata_info %}
- **Curve Metadata**: {{ metadata_info.total_curves or 0 }} curves
{% if metadata_info.by_category %}
  - Distribution by Category:
{% for category, count in metadata_info.by_category.items() %}
{% if count > 0 %}
    - {{ category }}: {{ count }} curves
{% endif %}
{% endfor %}
{% endif %}

{% if metadata_info.curve_attributes %}
- **Detailed Curve Attributes List**:

| No. | Curve Name | Unit | Data Type | Category | Dimension | Curve Class | Well ID | Depth Role | Element Names | DataFrame Column | DataFrame Elements | Description |
|-----|------------|------|-----------|----------|-----------|-------------|---------|------------|---------------|------------------|--------------------|-----------  |
{% for curve_attr in metadata_info.curve_attributes %}
| {{ loop.index }} | {{ curve_attr.name }} | {{ curve_attr.unit or '-' }} | {{ curve_attr.data_type }} | {{ curve_attr.category }} | {{ curve_attr.dimension }} | {{ curve_attr.curve_class or '-' }} | {{ 'Yes' if curve_attr.is_well_identifier else 'No' }} | {{ curve_attr.depth_role or '-' }} | {% if curve_attr.element_names %}{{ curve_attr.element_names|join(', ') }}{% else %}-{% endif %} | {{ curve_attr.dataframe_column_name }} | {% if curve_attr.dataframe_element_names %}{{ curve_attr.dataframe_element_names|join(', ') }}{% else %}-{% endif %} | {{ curve_attr.description or '-' }} |
{% endfor %}
{% endif %}
{% endif %}

{% if dataset_info.well_statistics and dataset_info.well_statistics.error is not defined %}
- **Statistics by Well**:
{% for well_name, stats in dataset_info.well_statistics.items() %}
{% if stats.get('depth_min') is not none and stats.get('depth_max') is not none %}
  - {{ well_name }}: {{ stats.get('row_count', 'N/A') }} rows, depth range {{ "%.2f"|format(stats['depth_min']) }} - {{ "%.2f"|format(stats['depth_max']) }}
{% else %}
  - {{ well_name }}: {{ stats.get('row_count', 'N/A') }} rows, depth range N/A
{% endif %}
{% endfor %}
{% endif %}

{% if dataset_info.curve_statistics and dataset_info.curve_statistics.error is not defined %}
{% set curve_stats = dataset_info.curve_statistics %}

##### Curve Statistics Analysis

{% if curve_stats.summary %}
{% set summary = curve_stats.summary %}
###### Overall Statistics Overview
- **Numeric Curves**: {{ summary.numeric_count or 0 }}
- **Categorical Curves**: {{ summary.categorical_count or 0 }}
- **Identifier Curves**: {{ summary.identifier_count or 0 }}
{% endif %}

{% if curve_stats.numeric_curves %}
###### Numeric Curves Detailed Statistics

| No. | Curve Name | Total | Missing | Valid | Min | Max | Mean | Median | Std Dev | Outliers |
|-----|------------|-------|---------|-------|-----|-----|------|--------|---------|----------|
{% for curve_name, stats in curve_stats.numeric_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.valid_count or 'N/A' }} | {{ "%.3f"|format(stats.min) if stats.min is not none else 'N/A' }} | {{ "%.3f"|format(stats.max) if stats.max is not none else 'N/A' }} | {{ "%.3f"|format(stats.mean) if stats.mean is not none else 'N/A' }} | {{ "%.3f"|format(stats.median) if stats.median is not none else 'N/A' }} | {{ "%.3f"|format(stats.std) if stats.std is not none else 'N/A' }} | {{ stats.outlier_count or 0 }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if curve_stats.categorical_curves %}
###### Categorical Curves Detailed Statistics

| No. | Curve Name | Total | Missing | Unique | Mode | Top 5 Values |
|-----|------------|-------|---------|--------|------|---------------|
{% for curve_name, stats in curve_stats.categorical_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ stats.mode or 'N/A' }} | {% if stats.top_5_values %}{% for value, count in stats.top_5_values.items() %}{{ value }}({{ count }}){% if not loop.last %}, {% endif %}{% endfor %}{% else %}N/A{% endif %} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if curve_stats.identifier_curves %}
###### Identifier Curves Detailed Statistics

| No. | Curve Name | Total | Missing | Unique | Completeness(%) | Most Common | Count | ID Type |
|-----|------------|-------|---------|--------|-----------------|-------------|-------|---------|
{% for curve_name, stats in curve_stats.identifier_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ "%.1f"|format(stats.completeness) if stats.completeness is not none else 'N/A' }} | {{ stats.most_common or 'N/A' }} | {{ stats.most_common_count or 'N/A' }} | {{ stats.identifier_type or 'N/A' }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if curve_stats.by_well_statistics %}
##### Well-by-Well Curve Statistics Analysis

{% for well_name, well_curve_stats in curve_stats.by_well_statistics.items() %}
###### Well {{ well_name }} Statistics

{% if well_curve_stats.error is not defined and well_curve_stats.summary %}
{% set well_summary = well_curve_stats.summary %}
**Statistics Overview**:
- **Numeric Curves**: {{ well_summary.numeric_count or 0 }}
- **Categorical Curves**: {{ well_summary.categorical_count or 0 }}
- **Identifier Curves**: {{ well_summary.identifier_count or 0 }}

{% if well_curve_stats.numeric_curves %}
**Numeric Curve Statistics**:

| No. | Curve Name | Total | Missing | Valid | Min | Max | Mean | Median | Std Dev | Outliers |
|-----|------------|-------|---------|-------|-----|-----|------|--------|---------|----------|
{% for curve_name, stats in well_curve_stats.numeric_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.valid_count or 'N/A' }} | {{ "%.3f"|format(stats.min) if stats.min is not none else 'N/A' }} | {{ "%.3f"|format(stats.max) if stats.max is not none else 'N/A' }} | {{ "%.3f"|format(stats.mean) if stats.mean is not none else 'N/A' }} | {{ "%.3f"|format(stats.median) if stats.median is not none else 'N/A' }} | {{ "%.3f"|format(stats.std) if stats.std is not none else 'N/A' }} | {{ stats.outlier_count or 0 }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if well_curve_stats.categorical_curves %}
**Categorical Curve Statistics**:

| No. | Curve Name | Total | Missing | Unique | Mode | Top 5 Values |
|-----|------------|-------|---------|--------|------|---------------|
{% for curve_name, stats in well_curve_stats.categorical_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ stats.mode or 'N/A' }} | {% if stats.top_5_values %}{% for value, count in stats.top_5_values.items() %}{{ value }}({{ count }}){% if not loop.last %}, {% endif %}{% endfor %}{% else %}N/A{% endif %} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% if well_curve_stats.identifier_curves %}
**Identifier Curve Statistics**:

| No. | Curve Name | Total | Missing | Unique | Completeness(%) | Most Common | Count | ID Type |
|-----|------------|-------|---------|--------|-----------------|-------------|-------|---------|
{% for curve_name, stats in well_curve_stats.identifier_curves.items() %}
{% if stats.error is not defined %}
| {{ loop.index }} | {{ curve_name }} | {{ stats.count or 'N/A' }} | {{ stats.missing or 0 }} | {{ stats.unique_values or 'N/A' }} | {{ "%.1f"|format(stats.completeness) if stats.completeness is not none else 'N/A' }} | {{ stats.most_common or 'N/A' }} | {{ stats.most_common_count or 'N/A' }} | {{ stats.identifier_type or 'N/A' }} |
{% else %}
| {{ loop.index }} | {{ curve_name }} | ERROR | - | - | - | - | - | - |
{% endif %}
{% endfor %}
{% endif %}

{% else %}
**Status**: {{ well_curve_stats.error or 'Data Error' }}
{% endif %}

{% endfor %}
{% endif %}
{% endif %}

{% endif %}
{% endfor %}

---

*Report generated by {{ generation_info.generator }} v{{ generation_info.version }}*
