# Uncertainty Quantification (`logwp.extras.ml.uq`)

一个提供用于机器学习模型不确定性量化 (Uncertainty Quantification, UQ) 工具的包。

在油气勘探开发等高风险领域，仅仅提供一个点预测（如“孔隙度为15%”）是远远不够的。决策者更需要知道这个预测的置信度（例如，“我们有95%的把握认为孔隙度在12%到18%之间”）。本包提供了生成这种预测区间的先进工具。

## 核心特性

- **模型无关**: `ConformalPredictor` 可以为任何 scikit-learn 兼容的回归模型添加具有统计保障的预测区间。
- **贝叶斯近似**: `MCDropoutPredictor` 使用蒙特卡洛丢弃技术来量化深度学习模型的认知不确定性。
- **直接建模**: `QuantilePredictor` 使用原生支持分位数回归的模型（如LightGBM）直接对不确定性进行建模。
- **标准化API**: 所有预测器都提供了一致的 `fit` 和 `predict` 接口，易于使用。

## 可用工具

### 1. `ConformalPredictor`

**保形预测**。这是一种强大的、模型无关的技术，它通过一个独立的“校准集”来学习模型的误差分布，并以此为任何新预测生成具有严格统计覆盖率保证的区间。

**适用场景**: 当你已经有了一个表现良好的点预测模型（如RandomForest, XGBoost），并希望为它“附加”上可靠的置信区间时，这是最佳选择。

```python
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from logwp.extras.ml.uq import ConformalPredictor

# 1. 准备数据，并划分为训练集、校准集和测试集
X = pd.DataFrame(np.random.rand(1000, 5))
y = pd.Series(np.random.rand(1000) * 10)

X_train, X_temp, y_train, y_temp = train_test_split(X, y, test_size=0.4, random_state=42)
X_calib, X_test, y_calib, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42)

# 2. 初始化一个基础模型
base_model = RandomForestRegressor(n_estimators=50, random_state=42)

# 3. 初始化保形预测器封装器
conformal_predictor = ConformalPredictor(base_estimator=base_model)

# 4. 拟合：在训练集上训练模型，在校准集上校准区间
conformal_predictor.fit(X_train, y_train, X_calib, y_calib)

# 5. 预测：获取点预测和95%置信区间 (alpha=0.05)
y_pred, y_lower, y_upper = conformal_predictor.predict(X_test, alpha=0.05)

# 6. 查看结果
print(f"预测值: {y_pred[0]:.2f}")
print(f"95% 置信区间: [{y_lower[0]:.2f}, {y_upper[0]:.2f}]")
```

### 2. `QuantilePredictor`

**分位数回归**。此方法不依赖于后处理，而是直接训练模型来预测目标值的特定分位数（如5%、50%、95%）。通过组合低分位数和高分位数的预测，我们可以构建出预测区间。

**适用场景**: 当你使用的模型原生支持分位数回归时（如 `LightGBM`, `CatBoost`, `sklearn.linear_model.QuantileRegressor`），这是一种非常高效和直接的方法。

```python
import numpy as np
import pandas as pd
from lightgbm import LGBMRegressor
from logwp.extras.ml.uq import QuantilePredictor

# 1. 准备数据
X_train = pd.DataFrame(np.random.rand(100, 5))
y_train = pd.Series(np.random.rand(100) * 10)
X_test = pd.DataFrame(np.random.rand(20, 5))

# 2. 为上、中、下界分别配置三个模型
alpha = 0.05 # 对应 95% 置信区间

lower_model = LGBMRegressor(objective='quantile', alpha=alpha, random_state=42)
median_model = LGBMRegressor(objective='quantile', alpha=0.5, random_state=42) # 点预测
upper_model = LGBMRegressor(objective='quantile', alpha=1.0 - alpha, random_state=42)

# 3. 初始化分位数预测器封装器
quantile_predictor = QuantilePredictor(
    estimator_lower=lower_model,
    estimator_median=median_model,
    estimator_upper=upper_model
)

# 4. 拟合：同时训练三个模型
quantile_predictor.fit(X_train, y_train)

# 5. 预测
y_pred, y_lower, y_upper = quantile_predictor.predict(X_test)

# 6. 查看结果
print(f"预测值: {y_pred[0]:.2f}")
print(f"95% 置信区间: [{y_lower[0]:.2f}, {y_upper[0]:.2f}]")
```

### 3. `MCDropoutPredictor`

**蒙特卡洛丢弃 (MC Dropout)**。这是一种近似贝叶斯推断的技术，专门用于深度学习模型。它通过在**预测时**多次启用Dropout层，来模拟从模型后验分布中采样。预测结果的离散程度（如标准差）可以被视为模型**认知不确定性**的度量，即模型对自己的预测有多“自信”。

**适用场景**: 当你使用包含Dropout层的深度学习模型（如Keras, PyTorch），并希望识别模型在哪些数据点上“最没有把握”（例如，当输入数据与训练数据分布差异较大时）时，这是一个绝佳的选择。

```python
# 假设您有一个用 Keras/TensorFlow 定义的、包含 Dropout 层的模型
# import tensorflow as tf
# from tensorflow.keras.models import Sequential
# from tensorflow.keras.layers import Dense, Dropout
#
# def create_dropout_model():
#     model = Sequential([Dense(64, activation='relu'), Dropout(0.2), Dense(1)])
#     model.compile(optimizer='adam', loss='mse')
#     return model
#
# keras_model = create_dropout_model()
# # ... 训练 keras_model ...

# from logwp.extras.ml.uq import MCDropoutPredictor
#
# # 1. 初始化预测器
# mc_predictor = MCDropoutPredictor(model=keras_model, n_passes=100)
#
# # 2. 进行不确定性预测
# y_pred, y_lower, y_upper = mc_predictor.predict(X_test)
#
# # 3. 查看结果
# print(f"预测值 (均值): {y_pred[0]:.2f}")
# print(f"95% 置信区间: [{y_lower[0]:.2f}, {y_upper[0]:.2f}]")
```

## 如何选择？

| 特性 | `ConformalPredictor` | `QuantilePredictor` | `MCDropoutPredictor` |
| :--- | :--- | :--- | :--- |
| **模型兼容性** | **极高** (任何scikit-learn兼容模型) | **有限** (需模型支持分位数目标) | **有限** (含Dropout的深度学习模型) |
| **理论保障** | **强** (严格的边际覆盖率保证) | **弱** (依赖模型拟合能力) | **近似贝斯推断** |
| **数据需求** | 需要独立的**校准集** | 无需额外数据集 | 无需额外数据集 |
| **训练开销** | 训练1个模型 + 快速校准 | 训练**3个**独立模型 | 训练1个模型 |
| **不确定性类型** | 偶然不确定性 (Aleatoric) | 偶然不确定性 (Aleatoric) | **认知不确定性 (Epistemic)** |
| **区间宽度** | 通常是**同方差**的（宽度较固定） | 能更好地捕捉**异方差**（宽度随输入变化） | 能更好地捕捉**认知不确定性**（随OOD数据变化） |

**简而言之**:
- 如果你想为现有模型快速添加可靠的置信区间，使用 `ConformalPredictor`。
- 如果你使用的模型是 `LightGBM` 等，并且你认为预测的不确定性会随输入特征的变化而变化，可以尝试 `QuantilePredictor`。
- 如果你使用深度学习模型，并希望识别模型在哪些数据上“最没有把握”，使用 `MCDropoutPredictor`。
