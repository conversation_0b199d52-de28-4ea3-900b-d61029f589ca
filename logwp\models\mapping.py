from __future__ import annotations

"""logwp.models.mapping - 井名映射管理

WpWellMap实现WFS规范的井名映射功能，支持单向映射和循环检测。

Architecture
------------
层次/依赖: models层值对象，依赖types、constants、exceptions
设计原则: WFS规范遵循、单向映射、CIIA大小写不敏感
性能特征: 快速查找、循环检测、内存优化

WFS规范实现：
- 单向映射：WELL → MAP_WELL
- 大小写不敏感：使用CaseInsensitiveDict
- 简单循环检测：防止A→B, B→A直接循环映射

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-1: Exception Groups异常处理
- CT-2: 使用枚举常量

References
----------
- 《SCAPE_WFS_WP文件规范.md》§6.1-6.2 - 井名映射规范
- 《SCAPE_SAD_软件架构设计.md》§3 - CIIA大小写不敏感架构
"""

from dataclasses import dataclass, field

from logwp.models.constants import WpLogMessage
from logwp.models.exceptions import WpWellMappingError, WpCircularMappingError
from logwp.infra.exceptions import ErrorContext
from logwp.models.types import WpWellName
from logwp.models.utils import CaseInsensitiveDict
from logwp.infra import get_logger
from .base import WpProjectComponent

# 获取结构化日志记录器
logger = get_logger(__name__)


@dataclass
class WpWellMap(WpProjectComponent):
    """井名映射管理器（WFS规范实现）。

    实现WFS规范的井名映射功能，支持单向映射WELL→MAP_WELL和循环检测。
    使用CaseInsensitiveDict实现CIIA大小写不敏感架构。

    Architecture
    ------------
    层次/依赖: models层值对象，实现WFS井名映射规范
    设计原则: WFS规范遵循、单向映射、CIIA大小写不敏感
    性能特征: 快速查找、简单循环检测、单步映射

    WFS规范实现：
    - 单向映射：原始井名(WELL) → 映射井名(MAP_WELL)
    - 精确匹配：支持大小写不敏感的精确匹配
    - 简单循环检测：防止A→B, B→A直接循环映射

    Attributes:
        mappings: 井名映射字典，键为原始井名，值为映射后井名（CIIA）

    Examples:
        >>> # 基本映射操作
        >>> well_map = WpWellMap()
        >>> well_map.add_mapping("C-1A", "C-1")
        >>> well_map.add_mapping("c-1b", "C-1")  # 大小写不敏感
        >>>
        >>> # 获取映射结果
        >>> mapped = well_map.get_mapped_name("C-1A")
        >>> assert mapped == "C-1"
        >>>
        >>> # 无映射时返回原名
        >>> unmapped = well_map.get_mapped_name("D-1")
        >>> assert unmapped == "D-1"

    References:
        《SCAPE_WFS_WP文件规范.md》§6.1-6.2 - 井名映射规范
    """

    mappings: CaseInsensitiveDict[WpWellName, WpWellName] = field(
        default_factory=CaseInsensitiveDict
    )

    @classmethod
    def create_with_mappings(cls, mappings: dict[str, str]) -> 'WpWellMap':
        """创建井名映射并批量添加映射关系的便捷方法。

        一次性创建井名映射管理器并添加多个映射关系，避免逐个调用add_mapping。
        这是常见的业务需求，特别是在项目初始化和批量数据处理时。

        Args:
            mappings: 映射关系字典，键为原始井名，值为映射后井名

        Returns:
            WpWellMap: 创建的井名映射管理器实例

        Raises:
            WpWellMappingError: 井名为空或无效
            WpCircularMappingError: 检测到循环映射

        Examples:
            >>> # 批量创建井名映射
            >>> mappings = {
            ...     "C-1A": "C-1",
            ...     "C-1B": "C-1",
            ...     "D-2A": "D-2"
            ... }
            >>> well_map = WpWellMap.create_with_mappings(mappings)
            >>> assert well_map.get_mapped_name("C-1A") == "C-1"
            >>> assert well_map.get_mapped_name("C-1B") == "C-1"
            >>> assert well_map.get_mapped_name("D-2A") == "D-2"
            >>>
            >>> # 空映射字典
            >>> empty_map = WpWellMap.create_with_mappings({})
            >>> assert len(empty_map.mappings) == 0

        Note:
            - 支持大小写不敏感的井名处理
            - 自动进行循环映射检测
            - 如果映射字典为空，创建空的映射管理器
        """
        # 创建实例
        instance = cls()

        # 批量添加映射
        for source_well, target_well in mappings.items():
            instance.add_mapping(source_well, target_well)

        logger.debug(WpLogMessage.BATCH_MAPPING_COMPLETE, mappings_count=len(mappings))
        return instance

    def add_mapping(self, source: WpWellName, target: WpWellName) -> None:
        """添加井名映射（WFS规范实现）。

        实现WFS规范的WELL→MAP_WELL单向映射，支持循环检测。

        Args:
            source: 原始井名（WELL列值）
            target: 映射井名（MAP_WELL列值）

        Raises:
            WpWellMappingError: 井名为空或无效
            WpCircularMappingError: 检测到循环映射

        Examples:
            >>> # 基本映射
            >>> well_map.add_mapping("C-1A", "C-1")
            >>> well_map.add_mapping("c-1b", "C-1")  # 大小写不敏感
            >>>
            >>> # 重复键会抛出异常（WFS规范：以第一个记录为准）
            >>> well_map.add_mapping("C-1A", "C-2")  # 抛出WpWellMappingError
            >>>
            >>> # 使用update_mapping可以更新已存在的映射
            >>> well_map.update_mapping("C-1A", "C-2")  # 允许更新

        References:
            《SCAPE_WFS_WP文件规范.md》§6.1.2 - 井名映射数据要求
        """
        # WFS规范：井名不能为空或纯空格
        if not source or not source.strip():
            raise WpWellMappingError(
                "原始井名不能为空",
                context=ErrorContext(
                    operation="add_mapping",
                    stage="input_validation",
                    details={"source_well": source, "target_well": target}
                )
            )

        if not target or not target.strip():
            raise WpWellMappingError(
                "映射井名不能为空",
                context=ErrorContext(
                    operation="add_mapping",
                    stage="input_validation",
                    details={"source_well": source, "target_well": target}
                )
            )

        # 注意：CaseInsensitiveDict内部会自动调用WpStringNormalizer.normalize()
        # 该方法已包含strip()处理，因此这里不需要手动strip()

        # WFS规范：重复的原始井名记录，以第一个有效记录为准
        if source in self.mappings:
            existing_target = self.mappings[source]
            raise WpWellMappingError(
                f"原始井名 '{source}' 已存在映射: {source} → {existing_target}",
                context=ErrorContext(
                    operation="add_mapping",
                    stage="duplicate_check",
                    details={
                        "source_well": source,
                        "new_target": target,
                        "existing_target": existing_target
                    }
                )
            )

        # 简单循环检测：防止直接的双向映射 A→B, B→A
        if target in self.mappings and self.mappings[target] == source:
            raise WpCircularMappingError(
                f"添加映射 {source} → {target} 会创建循环",
                context=ErrorContext(
                    operation="add_mapping",
                    stage="cycle_detection",
                    details={
                        "source_well": source,
                        "target_well": target,
                        "existing_mapping": f"{target} → {self.mappings[target]}"
                    }
                )
            )

        # 添加映射（CaseInsensitiveDict自动处理大小写不敏感）
        self.mappings[source] = target

    def update_mapping(self, source: WpWellName, target: WpWellName) -> None:
        """更新或添加井名映射。

        如果映射已存在则更新，不存在则添加。

        Args:
            source: 原始井名（WELL列值）
            target: 映射井名（MAP_WELL列值）

        Raises:
            WpWellMappingError: 井名为空或无效
            WpCircularMappingError: 检测到循环映射

        Examples:
            >>> # 更新已存在的映射
            >>> well_map.add_mapping("C-1A", "C-1")
            >>> well_map.update_mapping("C-1A", "Well-C1")  # 更新映射
        """
        # 输入验证（复用add_mapping的验证逻辑）
        if not source or not source.strip():
            raise WpWellMappingError(
                "原始井名不能为空",
                context=ErrorContext(
                    operation="update_mapping",
                    stage="input_validation",
                    details={"source_well": source, "target_well": target}
                )
            )

        if not target or not target.strip():
            raise WpWellMappingError(
                "映射井名不能为空",
                context=ErrorContext(
                    operation="update_mapping",
                    stage="input_validation",
                    details={"source_well": source, "target_well": target}
                )
            )

        # 循环检测（排除自身）
        if target in self.mappings and self.mappings[target] == source and target != source:
            raise WpCircularMappingError(
                f"更新映射 {source} → {target} 会创建循环",
                context=ErrorContext(
                    operation="update_mapping",
                    stage="cycle_detection",
                    details={
                        "source_well": source,
                        "target_well": target,
                        "existing_mapping": f"{target} → {self.mappings[target]}"
                    }
                )
            )

        # 直接设置映射（允许覆盖）
        self.mappings[source] = target

    def get_mapped_name(self, well: WpWellName) -> WpWellName:
        """获取映射后的井名（WFS规范实现）。

        实现WFS规范的简单映射查找逻辑，单步映射。

        Args:
            well: 原始井名

        Returns:
            WpWellName: 映射后的井名，无映射时返回原井名

        Examples:
            >>> # 基本映射查找
            >>> mapped = well_map.get_mapped_name("C-1A")
            >>> assert mapped == "C-1"
            >>>
            >>> # 无映射时返回原名
            >>> unmapped = well_map.get_mapped_name("D-1")
            >>> assert unmapped == "D-1"

        References:
            《SCAPE_WFS_WP文件规范.md》§6.2.1 - 映射查找规则
        """
        if not well:
            return well

        # 简单单步映射：直接查找映射表
        return self.mappings.get(well, well)

    def has_mapping(self, well: WpWellName) -> bool:
        """检查是否存在映射。

        Args:
            well: 井名

        Returns:
            bool: 是否存在映射

        Examples:
            >>> assert well_map.has_mapping("C-1A")
            >>> assert not well_map.has_mapping("D-1")
        """
        if not well:
            return False
        return well in self.mappings  # CaseInsensitiveDict自动处理strip()和大小写

    def remove_mapping(self, source: WpWellName) -> bool:
        """移除井名映射。

        Args:
            source: 原始井名

        Returns:
            bool: 是否成功移除

        Examples:
            >>> success = well_map.remove_mapping("C-1A")
            >>> assert success
        """
        if not source:
            return False

        # CaseInsensitiveDict自动处理strip()和大小写规范化
        if source in self.mappings:
            del self.mappings[source]
            return True
        return False

    def clear(self) -> None:
        """清空所有映射。

        Examples:
            >>> well_map.clear()
            >>> assert len(well_map.mappings) == 0
        """
        self.mappings.clear()

    def generate_summary(self) -> dict[str, Any]:
        """生成井名映射概况。

        生成包含所有井名映射信息的概况，用于数据了解和调试。

        Returns:
            dict[str, Any]: 井名映射概况数据
                - total_mappings: 总映射数
                - mappings_list: 映射列表

        Examples:
            >>> well_map = WpWellMap()
            >>> well_map.add_mapping("C-1A", "C-1")
            >>> well_map.add_mapping("C-1B", "C-1")
            >>> summary = well_map.generate_summary()
            >>> print(f"总映射数: {summary['total_mappings']}")
            >>> print(f"映射详情: {summary['mappings_list']}")

        References:
            《SCAPE_DDS_logwp_generate_summary.md》§4.4 - 井名映射服务设计
        """
        from logwp.models.internal.wellmap_summary import generate_wellmap_summary
        return generate_wellmap_summary(self)




