"""scape.core.swift_pso.training_facade - SWIFT-PSO训练步骤门面

实现SWIFT-PSO训练步骤的公共接口，负责编排、追踪和产物管理。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO训练步骤门面
设计原则: Facade模式、前置检查、追踪集成
性能特征: GPU/CPU自适应、异常处理、内存管理

References
----------
- 《SCAPE_MS_方法说明书》§4.1-4.3 - SWIFT-PSO训练流程
- 《logwp/extras/tracking/机器学习组件开发框架》§3 - Step开发规范
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Callable, Dict

if TYPE_CHECKING:
    from logwp.extras.tracking import RunContext
    from logwp.models.datasets.bundle import WpDataFrameBundle
    from logwp.infra.logging_config import Logger

import pandas as pd
from logwp.models.exceptions import WpDataError

from .config import SwiftPsoTrainingConfig
from .constants import SwiftPsoTrainingArtifacts
from .artifact_handler import SwiftPsoArtifactHandler
from .internal import pso_optimizer
from .internal.backend_utils import create_backend_service
from .internal.data_validator import validate_data_integrity

def _get_logger() -> "Logger":
    """延迟获取logger实例，避免循环导入。"""
    from logwp.infra import get_logger
    return get_logger(__name__)


def run_swift_pso_training_step(
    config: SwiftPsoTrainingConfig,
    ctx: RunContext,
    train_bundle: Any,  # WpDataFrameBundle
    *,
    backend: str = 'cpu'
) -> Dict[str, Any]:
    """执行SWIFT-PSO训练步骤。

    该步骤使用粒子群优化算法执行Bootstrap+LOWO和Fine-Tuning两个阶段，
    寻找FOSTER-NMR模型的最佳参数，并产出所有训练结果作为可追踪的产物。

    Args:
        config: SWIFT-PSO训练步骤的配置对象
        ctx: 当前运行的上下文，用于追踪和产物管理
        train_bundle: 用于训练的数据Bundle
        backend: 计算后端类型，'cpu' 或 'gpu'，默认为 'cpu'

    Returns:
        Dict[str, Any]: 包含训练状态的轻量级字典：
            - "status": "completed"
            - "final_loss": 最终损失值（如果可用）

    Raises:
        WpDataError: 输入数据包含NaN值或格式错误时抛出
        WpGpuError: 请求GPU但不可用时抛出

    Artifacts:
        - swift_pso_training.models.final_parameters: 最终优化后的模型参数（JSON）
        - swift_pso_training.datasets.all_parameters_from_lowo: 所有LOWO参数（CSV）
        - swift_pso_training.reports.convergence_history_finetune: Fine-Tuning收敛历史（CSV）
        - swift_pso_training.reports.bootstrap_summary: Bootstrap阶段的评估摘要（CSV）
        - swift_pso_training.diagnostics.params_warm_start: Fine-Tuning阶段的热启动参数（JSON）
        - swift_pso_training.diagnostics.fold_parameters (optional): 单次折叠的最优参数（JSON）
        - swift_pso_training.diagnostics.fold_loss_history (optional): 单次折叠的收敛历史（CSV）

    References:
        《SCAPE_MS_方法说明书》§4.1-4.3 - SWIFT-PSO训练流程定义
    """
    logger = _get_logger()
    logger.info(
        "开始SWIFT-PSO训练步骤",
        operation="swift_pso_training_step",
        run_id=ctx.run_id,
        backend=backend,
        bootstrap_iterations=config.bootstrap_iterations
    )

    # 1. 获取步骤目录
    step_dir = ctx.get_step_dir("swift_pso_training")

    # 2. 执行前置检查
    _perform_precondition_checks(train_bundle, backend)

    # 3. 初始化计算后端
    backend_service = create_backend_service(backend)

    # 3.5 保存完整的训练配置
    handler = SwiftPsoArtifactHandler()
    config_path = step_dir / "training_config.json"
    # 将Pydantic模型转换为字典进行保存
    handler.save_parameters(config.model_dump(), config_path)
    ctx.register_artifact(
        artifact_path=config_path.relative_to(ctx.run_dir),
        artifact_name=SwiftPsoTrainingArtifacts.TRAINING_CONFIG.value,
        description="本次训练步骤的完整Pydantic配置快照，确保可复现性。"
    )
    logger.info("训练配置已保存为产物", config_path=str(config_path))

    # 4. 记录训练参数
    _log_training_parameters(ctx, config)

    # 5. 调用内部核心逻辑
    logger.info("调用内部PSO优化器", operation="swift_pso_training_step")

    try:
        # --- 新增：参数预处理 ---
        optimization_params = config.optimization_params
        fixed_params = config.fixed_params

        # 验证参数配置无重叠 (双重保险，config层已有validator)
        if set(optimization_params) & set(fixed_params.keys()):
            raise ValueError(f"优化参数和固定参数不能有重叠: {set(optimization_params) & set(fixed_params.keys())}")

        config_dict = config.model_dump()

        # 提取原始的全量边界和参数名顺序，以备后用
        original_lowo_boundaries = config_dict['pso_config_lowo']['parameters_boundaries']
        all_param_names_ordered = list(original_lowo_boundaries.keys())

        # 过滤LOWO阶段的边界
        filtered_lowo_boundaries = {
            p: original_lowo_boundaries[p] for p in optimization_params if p in original_lowo_boundaries
        }
        config_dict['pso_config_lowo']['parameters_boundaries'] = filtered_lowo_boundaries

        # 过滤Fine-Tuning阶段的边界
        original_finetune_boundaries = config_dict['pso_config_finetune']['parameters_boundaries']
        filtered_finetune_boundaries = {
            p: original_finetune_boundaries[p] for p in optimization_params if p in original_finetune_boundaries
        }
        config_dict['pso_config_finetune']['parameters_boundaries'] = filtered_finetune_boundaries

        # 注入参数列表和固定值字典到两个子配置中，确保能被pso_optimizer访问
        for sub_config_key in ['pso_config_lowo', 'pso_config_finetune']:
            config_dict[sub_config_key]['optimization_params'] = optimization_params
            config_dict[sub_config_key]['fixed_params'] = fixed_params
            config_dict[sub_config_key]['all_param_names_ordered'] = all_param_names_ordered

        # 同时在顶层也保留一份，供pso_optimizer.run中的其他逻辑使用
        config_dict['optimization_params'] = optimization_params

        # --- 预处理结束 ---

        # 从bundle准备井数据字典，这是内部算法期望的输入格式
        all_wells_data = train_bundle.to_all_wells_data()

        # 准备回调函数，用于保存每折的详细日志
        fold_results_callback = None
        if config.enable_fold_diagnostics:
            handler = SwiftPsoArtifactHandler()
            step_dir = ctx.get_step_dir("swift_pso_training")

            def _save_fold_results_callback(
                bootstrap_run: int,
                lowo_fold: int,
                optimal_params: Dict[str, Any],
                loss_history_df: pd.DataFrame
            ):
                """在每次LOWO折叠完成后被调用的回调函数。"""
                fold_dir = step_dir / "1_bootstrap_lowo" / f"run_b{bootstrap_run:02d}_f{lowo_fold:02d}"

                # 保存参数
                params_path = fold_dir / "params_optimal.json"
                handler.save_parameters(optimal_params, params_path)
                ctx.register_artifact(params_path.relative_to(ctx.run_dir), f"{SwiftPsoTrainingArtifacts.DIAGNOSTIC_FOLD_PARAMETERS.value}.b{bootstrap_run:02d}_f{lowo_fold:02d}")

                # 保存损失历史
                loss_path = fold_dir / "loss_history.csv"
                handler.save_dataframe(loss_history_df, loss_path)
                ctx.register_artifact(loss_path.relative_to(ctx.run_dir), f"{SwiftPsoTrainingArtifacts.DIAGNOSTIC_FOLD_LOSS_HISTORY.value}.b{bootstrap_run:02d}_f{lowo_fold:02d}")

            fold_results_callback = _save_fold_results_callback

        training_results = pso_optimizer.run(
            all_wells_data=all_wells_data,
            config=config_dict,
            backend_service=backend_service,
            on_fold_complete=fold_results_callback
        )

    except Exception as e:
        logger.error(
            "PSO优化器执行失败",
            operation="swift_pso_training_step",
            error=str(e)
        )
        raise

    # 6. 保存和注册产物
    handler = SwiftPsoArtifactHandler()
    final_loss = _save_and_register_artifacts(ctx, step_dir, handler, training_results)

    # 7. 记录关键指标
    _log_training_metrics(ctx, training_results, final_loss)

    logger.info(
        "SWIFT-PSO训练步骤完成",
        operation="swift_pso_training_step",
        final_loss=final_loss
    )

    # 8. 返回轻量级结果
    return {
        "status": "completed",
        "final_loss": final_loss
    }


def _perform_precondition_checks(train_bundle: Any, backend: str) -> None:
    """执行训练前的前置条件检查。

    Args:
        train_bundle: 训练数据bundle
        backend: 计算后端类型

    Raises:
        WpDataError: 数据完整性检查失败时抛出
        ValueError: 后端类型无效时抛出
    """
    # 验证后端类型
    if backend not in ['cpu', 'gpu']:
        raise ValueError(f"不支持的后端类型: {backend}")

    # 根据SWIFT-PSO方法说明书，这些是核心输入曲线
    required_curves_for_pso = [
        'DPHIT_NMR', 'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50',
        'T2_VALUE', 'K_LABEL_TYPE', 'K_LABEL', 'PZI'
    ]

    if 'K_LABEL_TYPE' in train_bundle.data.columns:
        from logwp.models.constants import WpDataType

        label_type_mapping = {"CORE": 1, "MDT": 2}

        # 先计算出新的整数编码数据
        new_label_data = (
            train_bundle.data["K_LABEL_TYPE"]
            .map(label_type_mapping)
            .fillna(0)
            .astype(int)
        )
        # 使用 WpDataFrameBundle 的 replace_curve 方法安全地替换曲线
        train_bundle.replace_curve(
            curve_name='K_LABEL_TYPE',
            new_data=new_label_data.to_numpy(),
            new_data_type=WpDataType.INT
        )

    # 在K_LABEL_TYPE转换完成后，再执行数据完整性检查
    validate_data_integrity(train_bundle, required_curves_for_pso)


def _log_training_parameters(ctx: RunContext, config: SwiftPsoTrainingConfig) -> None:
    """记录训练参数到RunContext。

    Args:
        ctx: 运行上下文
        config: 训练配置对象
    """
    # 记录主要配置参数
    ctx.log_parameter(
        "bootstrap_iterations", config.bootstrap_iterations, step_name="swift_pso_training"
    )
    ctx.log_parameter(
        "narrow_window_factor", config.narrow_window_factor, step_name="swift_pso_training"
    )
    ctx.log_parameter(
        "bootstrap_sample_ratio", config.bootstrap_sample_ratio, step_name="swift_pso_training"
    )

    # 记录PSO配置摘要
    if config.pso_config_lowo:
        ctx.log_parameter(
            "pso_lowo_particles", config.pso_config_lowo.get("n_particles"), step_name="swift_pso_training"
        )
        ctx.log_parameter(
            "pso_lowo_iterations", config.pso_config_lowo.get("max_iterations"), step_name="swift_pso_training"
        )

    if config.pso_config_finetune:
        ctx.log_parameter(
            "pso_finetune_particles", config.pso_config_finetune.get("n_particles"), step_name="swift_pso_training"
        )
        ctx.log_parameter(
            "pso_finetune_iterations", config.pso_config_finetune.get("max_iterations"), step_name="swift_pso_training"
        )


def _save_and_register_artifacts(
    ctx: RunContext,
    step_dir: Any,  # Path
    handler: SwiftPsoArtifactHandler,
    training_results: Dict[str, Any]
) -> float | None:
    """保存和注册训练产物。

    Args:
        ctx: 运行上下文
        step_dir: 步骤目录
        handler: 产物处理器
        training_results: 训练结果字典

    Returns:
        float | None: 最终损失值（如果可用）
    """
    final_loss = None

    # 1. 保存完整的模型资产（包含参数和上下文）
    if "model_assets" in training_results:
        final_params_path = step_dir / "final_parameters.json"
        handler.save_parameters(training_results["model_assets"], final_params_path)

        ctx.register_artifact(
            artifact_path=final_params_path.relative_to(ctx.run_dir),
            artifact_name=SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value,
            description="最终优化后的模型参数及上下文，可直接用于预测步骤。"
        )

    # 2. 保存所有LOWO参数（供t-SNE可视化使用）
    if "all_optimized_parameters" in training_results:
        all_params_path = step_dir / "all_parameters_from_lowo.csv"
        handler.save_dataframe(training_results["all_optimized_parameters"], all_params_path)

        ctx.register_artifact(
            artifact_path=all_params_path.relative_to(ctx.run_dir),
            artifact_name=SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value,
            description="所有Bootstrap+LOWO迭代产生的优化参数集，用于t-SNE可视化分析。"
        )

    # 3. 保存Fine-Tuning收敛历史
    if "convergence_history_finetune" in training_results:
        convergence_df = training_results["convergence_history_finetune"]
        convergence_path = step_dir / "convergence_history_finetune.csv"
        handler.save_dataframe(convergence_df, convergence_path)

        ctx.register_artifact(
            artifact_path=convergence_path.relative_to(ctx.run_dir),
            artifact_name=SwiftPsoTrainingArtifacts.CONVERGENCE_HISTORY_FINETUNE.value,
            description="Fine-Tuning阶段的损失函数收敛历史。"
        )

        # 提取最终损失值
        if not convergence_df.empty and 'loss' in convergence_df.columns:
            final_loss = float(convergence_df['loss'].iloc[-1])

    # 4. 保存Bootstrap阶段的评估摘要
    if "bootstrap_summary" in training_results and training_results["bootstrap_summary"]:
        summary_list = training_results["bootstrap_summary"]
        summary_df = pd.DataFrame(summary_list)
        # 兼容旧版格式，增加 bootstrap_run 列
        if 'bootstrap_run_index' in summary_df.columns:
            summary_df['bootstrap_run'] = summary_df['bootstrap_run_index'] + 1

        summary_path = step_dir / "summary_bootstrap_mu_rmse.csv"
        handler.save_dataframe(summary_df, summary_path)

        ctx.register_artifact(
            artifact_path=summary_path.relative_to(ctx.run_dir),
            artifact_name=SwiftPsoTrainingArtifacts.BOOTSTRAP_SUMMARY.value,
            description="每轮Bootstrap的平均验证误差（mu_rmse），用于确定最佳轮次b*。"
        )

    # 5. 保存Fine-Tuning阶段的热启动参数
    if "warm_start_params" in training_results and training_results["warm_start_params"]:
        warm_start_params = training_results["warm_start_params"]
        warm_start_path = step_dir / "params_warm_start.json"
        handler.save_parameters(warm_start_params, warm_start_path)

        ctx.register_artifact(
            artifact_path=warm_start_path.relative_to(ctx.run_dir),
            artifact_name=SwiftPsoTrainingArtifacts.PARAMS_WARM_START.value,
            description="用于启动Fine-Tuning阶段的热启动参数（theta_hat_star）。"
        )

    # 6. 其他产物（如每折日志）已通过回调函数在运行中保存和注册
    return final_loss


def _log_training_metrics(
    ctx: RunContext,
    training_results: Dict[str, Any],
    final_loss: float | None
) -> None:
    """记录训练指标到RunContext。

    Args:
        ctx: 运行上下文
        training_results: 训练结果字典
        final_loss: 最终损失值
    """
    metrics = {}

    # 记录最终损失
    if final_loss is not None:
        metrics["final_finetune_loss"] = final_loss

    # 记录收敛迭代次数
    if "convergence_history_finetune" in training_results:
        convergence_df = training_results["convergence_history_finetune"]
        if not convergence_df.empty:
            metrics["convergence_iterations"] = len(convergence_df)

    # 记录参数数量
    if "all_optimized_parameters" in training_results:
        all_params_df = training_results["all_optimized_parameters"]
        if not all_params_df.empty:
            metrics["total_parameter_sets"] = len(all_params_df)

    # 批量记录指标
    if metrics:
        ctx.log_metrics(metrics, step_name="swift_pso_training")
