# SWIFT-PSO 组件开发范例

**版本: 1.0**

## 1. 引言

### 1.1. 目标与定位

本`README.md`文档旨在作为**可追踪机器学习组件开发框架**的一个完整、详尽的**实例化范例**。它以`scape.core.swift_pso`这个多步骤包（包含核心训练、模型预测和t-SNE可视化三个步骤）为蓝本，展示了如何将框架中的抽象原则、规范和最佳实践，转化为具体的、高质量的工程代码。

本文档不仅是`swift_pso`包的说明书，更是未来开发新组件或重构旧组件时可以遵循的**模板和指南**。

### 1.2. 实现要点

`swift_pso`包严格遵循了框架的核心原则，其实现要点包括：

*   **关注点分离**: 严格划分了`facade`（公共接口）、`internal`（核心算法）、`config`（参数配置）、`plotting`（绘图复现）等模块的职责。
*   **清晰的接口**: 通过`training_facade.py`和`visualization_facade.py`提供稳定、明确的公共API，隐藏所有内部实现。
*   **可追踪与可复现**: 深度集成了`logwp.extras.tracking`的`RunContext`，所有参数、指标和产物都被精确追踪。通过“数据快照”原则，确保了所有图表的可复现性。
*   **现代化工程实践**: 全面采用Pydantic进行配置管理，通过`PlotProfile`系统实现绘图与样式的解耦，并设计了支持CPU/GPU的后端抽象。

---

## 2. 核心概念对照表

下表将《框架》中的核心概念与`swift_pso`包中的具体实现一一对应：

| 框架概念 | `swift_pso` 中的具体实现 |
| :--- | :--- |
| **多步骤包** | `scape.core.swift_pso` 整个包 |
| **步骤 (Step)** | 1. SWIFT-PSO训练步骤 <br> 2. SWIFT-PSO预测步骤 <br> 3. t-SNE可视化步骤 |
| **门面 (Facade)** | 1. `training_facade.py` <br> 2. `prediction_facade.py` <br> 3. `visualization_facade.py` |
| **主执行函数** | 1. `run_swift_pso_training_step()` <br> 2. `run_swift_pso_prediction_step()` <br> 3. `run_tsne_visualization_step()` |
| **配置 (Config)** | 1. `config.SwiftPsoTrainingConfig` (支持灵活定义优化/固定参数和损失函数) <br> 2. `config.SwiftPsoPredictionConfig` <br> 3. `config.TsneVisualConfig` |
| **内部逻辑 (Internal)** | `internal/pso_optimizer.py` <br> `internal/tsne_computer.py` <br> `internal/backend_utils.py` |
| **产物常量** | 1. `constants.SwiftPsoTrainingArtifacts` <br> 2. `constants.SwiftPsoPredictionArtifacts` <br> 3. `constants.TsneVisualArtifacts` |
| **产物处理器** | `artifact_handler.SwiftPsoArtifactHandler` (服务于所有步骤) |
| **绘图复现** | `plotting.replot_tsne_from_snapshot()` |
| **绘图配置常量** | `constants.TsnePlotProfiles` |
| **数据快照** | `TsneVisualArtifacts.TSNE_CONVERGENCE_PLOT_DATA` <br> `TsneVisualArtifacts.TSNE_CLUSTER_PLOT_DATA` |
| **报告产物** | `TsneVisualArtifacts.TSNE_CLUSTER_ANALYSIS_REPORT` <br> `TsneVisualArtifacts.TSNE_CLUSTER_STATISTICS_REPORT` <br> `TsneVisualArtifacts.TSNE_CLUSTER_SUMMARY_TABLE` |

---

## 3. 组件架构与目录结构

`swift_pso`作为一个标准的多步骤包，其目录结构如下：

```
scape/core/swift_pso/
├── __init__.py               # 导出所有步骤的公共API
├── README.md                 # 本文档
├── training_facade.py        # 【必须】训练步骤的门面
├── prediction_facade.py      # 【必须】预测步骤的门面
├── visualization_facade.py   # 【必须】可视化步骤的门面
├── config.py                 # 【必须】包含所有步骤的Pydantic配置模型
├── constants.py              # 【推荐】包含所有步骤的产物和绘图配置常量
├── artifact_handler.py       # 【推荐】服务于所有步骤的无状态产物处理器
├── plotting.py               # (可选) 定义从数据快照复现图表的功能
├── plot_profiles.py          # (可选) 注册本模块专属的PlotProfile
└── internal/                 # 【必须】存放所有内部实现细节的目录
    ├── __init__.py
    ├── pso_optimizer.py      # 训练步骤的核心算法
    ├── tsne_computer.py      # 可视化步骤的核心算法
    └── backend_utils.py      # GPU/CPU后端服务的工具函数
```

### 3.1. 设计模式应用
本组件的架构深度整合了多种经典设计模式，以实现高内聚、低耦合的目标：

- 门面模式 (Facade Pattern): 每个Step都通过一个专用的facade文件（如training_facade.py）对外提供唯一的、简化的接口。这隐藏了所有内部实现的复杂性，使得组件易于被更高层的工作流调用。

- 策略模式 (Strategy Pattern) & 工厂模式 (Factory Pattern): 在internal/backend_utils.py中，create_backend_service函数充当一个工厂，根据输入（'cpu'或'gpu'）创建不同的计算后端服务。核心算法（如pso_optimizer）则依赖于后端服务的统一接口，而不关心其具体实现，这是策略模式的体现。

- 注册表模式 (Registry Pattern): plot_profiles.py模块通过向logwp.extras.plotting.registry中心化注册绘图配置模板，实现了配置的解耦和动态获取。使用者只需通过名称即可获取经过完整继承合并的配置对象。

- 无状态工具类 (Stateless Utility): artifact_handler.py被设计为一个完全无状态的工具类，其所有方法均为静态方法。这确保了它只负责I/O操作，不携带任何业务逻辑或状态，从而易于测试和复用。

---

## 4. 开发细节与最佳实践

### 4.1. 配置 (`config.py`)

#### 4.1.1. 【核心功能】灵活的参数优化

本次重构的核心是引入了**动态参数子空间优化**能力。用户现在可以通过`SwiftPsoTrainingConfig`中的`optimization_params`和`fixed_params`字段，精确控制哪些参数参与优化，哪些参数保持固定值。

*   **`optimization_params: list[str]`**: 一个字符串列表，用于指定需要被PSO算法优化的参数名称。默认包含全部10个FOSTER-NMR模型参数。
*   **`fixed_params: dict[str, float]`**: 一个字典，用于指定需要保持固定值的参数及其数值。

**工作原理**:
1.  **分离**: `training_facade`会根据`optimization_params`列表，从完整的参数边界定义中过滤出N个待优化参数的边界，传递给内部优化器。
2.  **优化**: `pso_optimizer`只在这N维子空间中进行搜索。
3.  **重组**: 在计算损失函数时，`pso_loss_function`会将每个粒子的N维优化参数与共享的`fixed_params`动态重组成完整的12维参数，再调用物理模型。

**使用示例：**

假设我们想固定`log10_T2cutoff_short`和`RHO_NMR`两个参数，只优化其余8个参数。

```python
# 在一个工作流脚本中
from scape.core.swift_pso import SwiftPsoTrainingConfig

# 1. 创建默认配置
training_config = SwiftPsoTrainingConfig.create_default()

# 2. 定义需要优化的参数（从默认列表中移除要固定的参数）
default_params = training_config.optimization_params
params_to_fix = ['log10_T2cutoff_short', 'RHO_NMR']
training_config.optimization_params = [p for p in default_params if p not in params_to_fix]

# 3. 为固定参数设置一个在物理边界内的合理值
training_config.fixed_params = {
    'log10_T2cutoff_short': 2.0,  # 必须在[1.5, 2.5]范围内
    'RHO_NMR': 25.0               # 必须在[0.1, 50.0]范围内
}

# 4. 将配置传入训练步骤
# run_swift_pso_training_step(config=training_config, ...)
```

#### 4.1.2. 【核心功能】可配置的复合损失函数

本次重构引入了基于地质先验知识（产层指示`PZI`）的复合损失函数，以实现“优先拟合高渗产层，同时确保非产层渗透率预测值不过高”的双重目标。该损失函数的核心机制现在完全可以通过配置进行微调。
**其详细的数学定义，请参考《SCAPE_方法说明书_V5.md》的§4.3节。**
*   **`w_prod: float`**: 产层样本（`PZI=1`）的权重，用于调整其在加权对数误差中的影响。默认值为 `1.0`。
*   **`w_nonprod: float`**: 非产层样本（`PZI=0`）的权重。默认值为 `0.1`。
*   **`lambda_penalty: float`**: 新增的“非产层惩罚项”的权重系数，用于调节惩罚强度。默认值为 `0.5`。
*   **`K_penalty_thresh: float`**: 非产层渗透率的容忍上限（单位mD）。当模型对非产层的预测值超过此阈值时，将施加惩罚。默认值为 `1.0`。

**工作原理**:
1.  **样本加权**: 优化器不再依赖`K_LABEL_TYPE`，而是使用`PZI`和`w_prod`/`w_nonprod`来计算加权对数误差，将优化焦点集中在产层上。
2.  **非对称惩罚**: 通过`lambda_penalty`和`K_penalty_thresh`控制的惩罚项，防止模型对低权重的非产层样本做出过高的渗透率预测。

**使用示例：**

假设我们希望增强对非产层的惩罚，并略微降低产层的相对权重。

```python
# 在一个工作流脚本中
from scape.core.swift_pso import SwiftPsoTrainingConfig

# 1. 创建默认配置
training_config = SwiftPsoTrainingConfig.create_default()

# 2. 修改LOWO阶段的损失函数参数
training_config.pso_config_lowo['w_prod'] = 0.9
training_config.pso_config_lowo['lambda_penalty'] = 1.0 # 增强惩罚
training_config.pso_config_lowo['K_penalty_thresh'] = 0.8 # 降低容忍上限

# 3. 修改Fine-Tuning阶段的损失函数参数
training_config.pso_config_finetune['lambda_penalty'] = 1.2 # 在微调阶段进一步增强惩罚

# 4. 将配置传入训练步骤
# run_swift_pso_training_step(config=training_config, ...)
```

**原则：参数的分类与处理**

我们严格遵循框架对参数的分类原则，以`run_swift_pso_training_step`为例：

```python
# training_facade.py
def run_swift_pso_training_step(
    config: SwiftPsoTrainingConfig,  # <--- 模型/算法参数
    ctx: RunContext,
    train_bundle: Any,               # <--- 数据输入
    *,
    backend: str = 'cpu'             # <--- 执行/表现层参数
) -> Dict[str, Any]:
    ...
```

*   **模型/算法参数**: `bootstrap_iterations`, `pso_config_lowo`等直接影响算法数学逻辑的参数，**必须**在`config.SwiftPsoTrainingConfig`中用Pydantic模型定义。这确保了算法核心的可移植性和验证。
*   **执行/表现层参数**: `backend`（选择'cpu'或'gpu'）不影响算法的数值结果，但控制其运行方式。这类参数**必须**作为`facade`函数的直接、关键字参数传入，使其与核心算法配置解耦。
*   **数据输入**: `train_bundle`作为主要的数据源，直接传递给`facade`函数。

**实践：Step级配置 vs. 算法内部配置**

*   **Step级配置 (`config.py`)**: 使用Pydantic模型，提供类型安全、验证和清晰的文档。这是`facade`层与外界交互的“契约”。
*   **算法内部配置 (`internal/`)**: `internal`中的核心算法（如`pso_optimizer.py`）可以接收一个简单的字典作为配置。`facade`层负责将Pydantic模型通过`.model_dump()`转换为字典再传入。这给予了内部算法更大的灵活性，使其不强依赖于Pydantic。

### 4.2. 产物、常量与处理器

*   **产物常量 (`constants.py`)**: 我们为每个`Step`定义了独立的`Enum`来管理其产物，并严格遵循`step_name.<category>.<specific_name>`的命名规范。这避免了命名冲突，并使得在工作流中引用产物非常健壮。
    ```python
    class SwiftPsoTrainingArtifacts(str, Enum):
        ALL_OPTIMIZED_PARAMETERS = "swift_pso_training.datasets.all_parameters_from_lowo"

    class TsneVisualArtifacts(str, Enum): # 示例已更新
        TSNE_CONVERGENCE_PLOT_DATA = "swift_pso_visualization.data_snapshots.tsne_convergence_trajectory"
        TSNE_CLUSTER_SUMMARY_TABLE = "swift_pso_visualization.reports.tsne_cluster_summary_table"
    ```
*   **产物处理器 (`artifact_handler.py`)**: `SwiftPsoArtifactHandler`是一个**无状态**的工具类，它服务于包内所有`Step`。它只负责“如何”读写特定格式的文件（如CSV, JSON），而不关心“为什么”要读写。
*   **保存与注册原则**: `facade`函数中，总是先调用`handler`将数据**保存**到`step_dir`下的物理文件，然后再调用`ctx.register_artifact()`将该文件的元数据**注册**到`RunContext`中。

### 4.3. 绘图 (`PlotProfile`与数据快照)

#### 4.3.1. 数据快照优先原则

本包严格遵循“数据快照”原则，确保了可视化的完全可复现性。对t-SNE图表，`visualization_facade`执行以下流程：
1.  **计算**: 调用`tsne_computer.compute_tsne()`执行耗时的降维计算。
2.  **保存数据快照**: 将降维后的`DataFrame`保存为一个`.csv`文件 (例如 `tsne_convergence_trajectory_data.csv`)。
3.  **保存报告产物**: 对于聚类分析，还会将量化指标（如簇心、轮廓系数）和详细统计数据保存为`.json`和`.csv`文件。
4.  **保存配置快照**: 将本次运行使用的`PlotProfile`对象保存为一个`.json`文件。
5.  **复现绘图**: 调用`plotting.replot_tsne_from_snapshot()`，传入数据和配置快照的路径来生成最终的`.png`/`.svg`图表。

这个流程确保了即使需要调整样式，我们也可以从轻量级的快照文件快速、精确地复现图表，而无需重新运行昂贵的t-SNE计算。

#### 4.3.2. 【重要】PlotProfile处理模式：灵活性与易用性的结合

为了在提供高质量默认样式和给予用户最大定制自由度之间取得平衡，本包采用了一种特别的`PlotProfile`处理模式。

*   **默认模板注册 (`plot_profiles.py`)**:
    *   我们提供了一个`plot_profiles.py`模块，它定义了一套专业、美观的默认绘图模板（如`swift_pso.tsne_convergence`）。
    *   这些模板在`scape.core.swift_pso`包被导入时，会自动注册到全局的`logwp.extras.plotting.registry`中。

*   **Facade接口设计 (`facade`函数)**:
    *   本包的`visualization_facade`函数接收一个可选的`plot_profiles`**字典**，键是模板的标准名称，值是完全实例化的`PlotProfile`对象。
    *   例如：`run_tsne_visualization_step(..., plot_profiles: Dict[str, PlotProfile])`

*   **推荐使用模式 (Get -> Modify -> Pass)**:
    这种设计催生了一种非常强大且灵活的使用模式：
    1.  **获取 (Get)**: 在工作流脚本中，首先从全局注册表`plot_registry`中获取一个默认的`PlotProfile`模板。
    2.  **修改 (Modify)**: 根据本次运行的特定需求，在运行时动态地修改这个`PlotProfile`对象的任意属性（如标题、颜色、字体大小）。
    3.  **传入 (Pass)**: 将这个被**定制后**的`PlotProfile`对象传入`facade`函数。

**示例代码：**
```python
# 在一个工作流脚本中
from scape.core.swift_pso import (
    run_tsne_visualization_step,
    TsnePlotProfiles,
)
from logwp.extras.plotting import registry as plot_registry

    # 1. 为需要的图表分别获取并修改配置
    trajectory_profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY).with_updates(
        title_props={"label": "Custom Trajectory Title"},
        save_config={"format": ["png", "svg"]}
    )
    cluster_profile = plot_registry.get(TsnePlotProfiles.CLUSTER_ANALYSIS).with_updates(
        title_props={"label": "Custom Cluster Analysis Title"}
    )

    # 2. 将所有定制后的配置打包成一个字典
    custom_plot_profiles = {
        TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value: trajectory_profile,
        TsnePlotProfiles.CLUSTER_ANALYSIS.value: cluster_profile,
    }

    # 3. 将配置字典传入facade函数
run_tsne_visualization_step(
    # ... 其他参数 ...
        plot_profiles=custom_plot_profiles, # 传入配置字典
)
```
这种模式既为用户提供了开箱即用的高质量默认图表，又赋予了他们在需要时进行精细化、临时性定制的全部能力，而无需修改包的源代码。

### 4.4. 门面 (`facade`) 与内部逻辑 (`internal`)

**原则：明确的职责边界**

*   **`facade`的职责 (业务流程编排层)**:
    *   与外界交互：接收`Config`对象、`RunContext`和数据输入。
    *   流程控制：执行前置检查、初始化后端、调用内部算法、处理产物。
    *   追踪与I/O：负责所有与`RunContext`的交互（记录参数/指标/产物）和文件I/O。
    *   是`Step`的“项目经理”。

*   **`internal`的职责 (纯粹算法实现层)**:
    *   核心计算：包含纯粹的、无副作用的算法逻辑（如PSO优化、t-SNE降维）。
    *   环境无关：完全不知道`RunContext`或文件系统的存在。它的输入是数据和配置字典，输出是计算结果。
    *   高度可移植、可单元测试。
    *   是`Step`的“技术专家”。

### 4.5. 后端 (`Backend`) 与GPU/CPU编程

为了支持不同的计算环境，我们引入了后端服务抽象。

*   **后端参数**: `training_facade`通过`backend: str`参数接收用户的计算后端偏好。
*   **后端服务工厂 (`internal/backend_utils.py`)**: `create_backend_service`函数根据传入的字符串（'cpu'或'gpu'）返回一个实现了统一接口的后端服务对象。
*   **统一接口**: 不同的后端服务（如`CpuBackendService`, `GpuBackendService`）都实现了相同的接口（例如`.array()`, `.to_cpu()`等），使得`pso_optimizer.py`等内部算法可以以一种后端无关的方式编写代码。

### 4.6. 数据传输

*   **工作流 -> Facade**: 更高层的工作流脚本向`facade`传递项目特定的、高层次的数据对象，如`WpDataFrameBundle`。这使得接口语义清晰。
*   **Facade -> Internal**: `facade`在调用`internal`函数前，会将高层数据对象转换为更通用的、纯粹的数据结构，如`dict`或`pd.DataFrame`。例如，`training_facade`调用`train_bundle.to_all_wells_data()`将其转换为字典。这确保了内部算法的独立性。
*   **Step之间**: `Step`之间的产物交接通过`RunContext`进行。生产者`Step`（如训练）产出一个标准格式的`Artifact`（如CSV文件），消费者`Step`（如可视化）通过`RunContext`加载这个`Artifact`。这实现了`Step`之间的解耦。

### 4.7. 使用模式

本组件的所有`facade`函数都设计为在更高层的**`Workflow`驱动脚本**中被调用。`Workflow`脚本负责：
1.  创建并管理`RunContext`的生命周期。
2.  加载数据和解析全局配置文件。
3.  根据全局配置，创建每个`Step`所需的`Config`对象。
4.  按顺序调用各个`Step`的`run_*_step`函数，并处理它们之间的产物依赖。

一个典型的`Workflow`调用示意见`logwp/extras/tracking/机器学习组件开发框架.md`的第9.3节。

### 4.8. 测试策略

一个健壮的组件需要分层的测试策略：

*   **单元测试**:
    *   **`internal`模块**: 对`pso_optimizer.py`和`tsne_computer.py`进行严格的单元测试，使用固定的输入数据和配置，断言其计算结果的正确性。这是测试的核心。
    *   **`facade`模块**: 对`training_facade.py`进行单元测试时，**必须**模拟（mock）`RunContext`和`internal`中的`pso_optimizer.run`函数。测试的重点是验证`facade`是否正确地调用了`ctx.log_parameter`, `ctx.register_artifact`等方法，以及是否正确地组织了调用流程。

*   **集成测试**:
    *   编写一个测试用例，创建一个指向临时目录的**真实`RunContext`**。
    *   **第一步（训练）**: 调用 `run_swift_pso_training_step`，它会产出模型产物（如 `FINAL_PARAMETERS`）和可视化数据产物（如 `ALL_OPTIMIZED_PARAMETERS`）。
    *   **第二步（预测）**: 调用 `run_swift_pso_prediction_step`，并断言它能成功加载训练步骤产出的模型产物。
    *   **第三步（可视化）**: 调用 `run_tsne_visualization_step`，并断言它能成功加载训练步骤产出的可视化数据产物。
    *   这个测试验证了整个组件内所有步骤之间通过`RunContext`进行的产物交接是正确无误的。

*   **契约测试**:
    *   为`ALL_OPTIMIZED_PARAMETERS`这个CSV产物定义一个Schema。
    *   训练步骤的测试应验证其输出的CSV文件符合此Schema。
    *   可视化步骤的测试可以使用此Schema来生成模拟的输入数据，从而解耦对训练步骤的依赖。

---

## 5. 总结

`scape.core.swift_pso`包是**可追踪机器学习组件开发框架**在实践中的全面体现。通过遵循本范例中展示的架构、原则和编码模式，您可以构建出同样高质量、可维护、可复现的机器学习组件，从而加速SCAPE项目的整体研发效率。

**关键要点回顾**:

1.  **分层清晰**: `facade`管流程，`internal`管算法。
2.  **配置分离**: Pydantic管“契约”，字典管“内部”，直接参数管“环境”。
3.  **追踪完备**: 一切皆可追踪——参数、指标、产物。
4.  **复现至上**: “数据快照”是保证图表可复现性的关键。
5.  **测试驱动**: 分层测试是保证组件质量的基石。

请在开发新组件时，将此`README.md`作为您的首要参考。

---

---

## 6. 重大Bug修复记录

本节记录了SWIFT-PSO组件中发现和修复的重大、微妙的Bug，这些Bug往往具有以下特征：
- 影响算法正确性或科学结果的准确性
- 在特定条件下才会触发，难以发现
- 需要深入理解算法原理才能正确诊断和修复

### 6.1. Vmacro_min参数在Bootstrap-LOWO阶段违反边界约束 (2025-07-27)

#### 问题描述
在Bootstrap-LOWO阶段，`Vmacro_min`参数出现负值（如-0.067、-0.152等），明显违反了其物理边界约束`(0.01, 0.033, 0.137, 0.15)`。该问题具有以下特征：

- **阶段特异性**：仅在Bootstrap-LOWO阶段出现，Fine-Tuning阶段正常
- **参数特异性**：只有`Vmacro_min`出现违反，其他9个参数正常
- **系统性问题**：t-SNE聚类分析显示所有cluster的`Vmacro_min`均值都为负值

#### 根本原因分析
通过深入的调用链分析，发现问题根源在于**硬反射边界处理策略的数值不稳定性**：

1. **数值计算不稳定**：当粒子位置远小于下界时，硬反射公式`2 * lb - new_positions`可能产生数值误差
2. **连续反射振荡**：粒子在边界附近高速运动时，可能形成反射-越界-反射的高频振荡
3. **小数值范围敏感性**：`Vmacro_min`的下界0.01接近零，使其对数值误差特别敏感
4. **缺乏最终保险**：硬反射策略缺乏最后的边界验证机制

#### 修复方案
在`scape/core/swift_pso/internal/pso_handle_boundary.py`的`handle_boundary_hard_reflection_vectorized`函数中，采用**防御性编程**策略：

```python
# === 第三步：边界钳位保险 (修复关键) ===
# 在硬反射后添加边界钳位作为最后的安全网，确保任何情况下粒子都不会越界。
# 对于正常情况，钳位操作不会改变反射结果；
# 对于异常情况，强制将越界值钳位到边界内。
new_positions = backend_service.clip(new_positions, lb, ub)
```

#### 修复特点
- **最小侵入性**：只添加一行核心代码，不改变现有算法逻辑
- **向后兼容**：不影响其他参数的正常优化过程
- **100%有效性**：`clip`操作确保绝对不会越界
- **性能友好**：`clip`是高效的向量化操作，性能开销可忽略

#### 验证方法
修复后，可通过以下方式验证：
1. 运行Bootstrap-LOWO阶段，检查所有参数是否严格遵守边界约束
2. 验证t-SNE聚类分析中`Vmacro_min`的统计值是否为正
3. 确认Fine-Tuning阶段的结果不受影响

#### 经验教训
1. **数值稳定性至关重要**：在处理小数值范围参数时，必须考虑数值计算的稳定性
2. **防御性编程的价值**：添加最后的安全检查可以捕获意外的边界情况
3. **全面测试的必要性**：需要针对不同数值范围的参数进行充分测试
4. **物理约束的严格性**：机器学习算法必须严格遵守物理世界的约束条件

---

**相关文档**:

-   `logwp/extras/tracking/机器学习组件开发框架.md`
-   `logwp/extras/plotting/README.md`
-   `docs/coding/SCAPE_CCG_编码与通用规范.md`
