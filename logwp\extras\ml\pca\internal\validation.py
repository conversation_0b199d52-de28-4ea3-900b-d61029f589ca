"""PCA数据验证服务。

提供全面的数据质量检查功能，包括数值型验证、完整性检查、
边界条件处理等。

Architecture
------------
层次/依赖: PCA内部服务层，数据验证
设计原则: 全面检查、快速验证、详细报告
性能特征: 批量验证、内存优化、精确诊断
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any

from logwp.models.constants import WpCurveCategory, WpDataType

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase

def validate_dataset_for_pca(
    dataset: WpDepthIndexedDatasetBase,
    curve_names: list[str] | None = None
) -> dict[str, Any]:
    """验证数据集是否适合PCA分析。

    Architecture
    ------------
    层次/依赖: PCA数据验证器，使用logwp.models新增API
    设计原则: 全面检查、智能诊断、用户友好
    性能特征: 快速验证、批量处理、详细报告

    Args:
        dataset: 要验证的数据集
        curve_names: 要验证的曲线名称，None表示自动获取

    Returns:
        验证结果字典，包含详细的诊断信息

    Raises:
        WpPcaDataError: 数据验证失败
    """
    from ..exceptions import create_data_error
    import structlog

    logger = structlog.get_logger(__name__)

    try:
        # 初始化验证结果
        validation_result = {
            "validation_passed": True,
            "issues": [],
            "warnings": [],
            "dataset_info": {},
            "curve_info": {}
        }

        # 1. 数据集基本检查
        if dataset is None:
            raise create_data_error("数据集不能为None")

        if dataset.df.empty:
            raise create_data_error("数据集为空", dataset_name=dataset.name)

        # 2. 获取要验证的曲线
        if curve_names is None:
            curve_names = dataset.curve_metadata.get_analysis_suitable_curves()

        # 3. 曲线数量验证
        if len(curve_names) < 2:
            validation_result["validation_passed"] = False
            validation_result["issues"].append(
                f"适合PCA分析的曲线数量不足（需要至少2个，实际{len(curve_names)}个）"
            )

        # 4. 样本数量检查
        n_samples = len(dataset.df)
        n_features = len(curve_names)

        validation_result["dataset_info"] = {
            "n_samples": n_samples,
            "n_features": n_features,
            "dataset_name": dataset.name,
            "dataset_type": dataset.get_dataset_type_name()
        }

        if n_samples < 10:
            validation_result["validation_passed"] = False
            validation_result["issues"].append(f"样本数量过少（{n_samples}个，建议至少10个）")

        if n_samples < n_features * 2:
            validation_result["warnings"].append(
                f"样本数量({n_samples})相对特征数量({n_features})较少，建议样本数至少为特征数的2倍"
            )

        # 5. 数据质量验证
        curve_validation = check_curve_suitability(dataset, curve_names)
        validation_result["curve_info"] = curve_validation

        if not curve_validation["all_suitable"]:
            validation_result["validation_passed"] = False
            validation_result["issues"].extend(curve_validation["issues"])

        # 6. 数值型数据质量检查
        numeric_validation = validate_numeric_data_quality(dataset, curve_names)
        validation_result.update(numeric_validation)

        # 7. 数据完整性检查
        completeness_check = check_data_completeness(dataset, curve_names)
        validation_result["completeness"] = completeness_check

        if completeness_check["low_completeness_curves"]:
            validation_result["warnings"].append(
                f"以下曲线数据完整性较低: {', '.join(completeness_check['low_completeness_curves'])}"
            )

        logger.info(
            "PCA数据集验证完成",
            dataset_name=dataset.name,
            validation_passed=validation_result["validation_passed"],
            n_issues=len(validation_result["issues"]),
            n_warnings=len(validation_result["warnings"])
        )

        return validation_result

    except Exception as e:
        if isinstance(e, Exception) and hasattr(e, '__class__') and 'WpPca' in e.__class__.__name__:
            raise
        else:
            raise create_data_error(
                f"数据集验证过程中发生错误: {str(e)}",
                dataset_name=dataset.name if dataset else None,
                curve_names=curve_names,
                cause=e
            )


def check_curve_suitability(
    dataset: WpDepthIndexedDatasetBase,
    curve_names: list[str]
) -> dict[str, Any]:
    """检查曲线是否适合PCA分析。

    Args:
        dataset: 数据集
        curve_names: 曲线名称列表

    Returns:
        曲线适用性检查结果
    """
    result = {
        "all_suitable": True,
        "issues": [],
        "curve_details": {}
    }

    for curve_name in curve_names:
        curve = dataset.curve_metadata.get_curve(curve_name)
        if curve is None:
            result["all_suitable"] = False
            result["issues"].append(f"曲线 {curve_name} 不存在")
            continue

        curve_detail = {
            "name": curve_name,
            "category": curve.category.value if curve.category else "unknown",
            "data_type": curve.data_type.value if curve.data_type else "unknown",
            "is_suitable": True,
            "issues": []
        }

        # 检查曲线类别（排除系统曲线）
        if curve.category in [WpCurveCategory.IDENTIFIER, WpCurveCategory.DEPTH]:
            curve_detail["is_suitable"] = False
            curve_detail["issues"].append("系统曲线不适合PCA分析")
            result["all_suitable"] = False

        # 检查数据类型（只允许数值型）
        if curve.data_type != WpDataType.NUMERIC:
            curve_detail["is_suitable"] = False
            curve_detail["issues"].append("非数值型曲线不适合PCA分析")
            result["all_suitable"] = False

        result["curve_details"][curve_name] = curve_detail
        if not curve_detail["is_suitable"]:
            result["issues"].extend([f"{curve_name}: {issue}" for issue in curve_detail["issues"]])

    return result


def validate_numeric_data_quality(
    dataset: WpDepthIndexedDatasetBase,
    curve_names: list[str]
) -> dict[str, Any]:
    """验证数值型数据质量。

    Args:
        dataset: 数据集
        curve_names: 要验证的曲线名称

    Returns:
        数据质量验证结果
    """
    # 使用logwp.models新增API进行数值型数据验证
    try:
        validation_result = dataset.validate_numeric_data(curve_names)
        return validation_result
    except Exception as e:
        return {
            "validation_passed": False,
            "issues": [f"数值型数据验证失败: {str(e)}"],
            "numeric_curves": [],
            "non_numeric_curves": curve_names
        }


def check_data_completeness(
    dataset: WpDepthIndexedDatasetBase,
    curve_names: list[str],
    min_completeness: float = 0.5
) -> dict[str, Any]:
    """检查数据完整性。

    Args:
        dataset: 数据集
        curve_names: 曲线名称列表
        min_completeness: 最小完整性要求

    Returns:
        数据完整性检查结果
    """
    # 使用logwp.models新增API获取数据完整性
    completeness = dataset.get_data_completeness()

    result = {
        "overall_completeness": 0.0,
        "curve_completeness": {},
        "low_completeness_curves": [],
        "min_completeness_threshold": min_completeness
    }

    total_completeness = 0.0
    valid_curves = 0

    for curve_name in curve_names:
        # 获取曲线对应的DataFrame列
        try:
            df_columns = dataset.curve_metadata.get_dataframe_columns_for_curves([curve_name])
            if df_columns:
                # 对于多列曲线，取平均完整性
                curve_completeness = sum(completeness.get(col, 0.0) for col in df_columns) / len(df_columns)
            else:
                curve_completeness = 0.0
        except:
            curve_completeness = 0.0

        result["curve_completeness"][curve_name] = curve_completeness

        if curve_completeness < min_completeness:
            result["low_completeness_curves"].append(f"{curve_name}({curve_completeness:.1%})")

        total_completeness += curve_completeness
        valid_curves += 1

    if valid_curves > 0:
        result["overall_completeness"] = total_completeness / valid_curves

    return result


def validate_pca_parameters_input(
    n_components: int | None,
    n_features: int,
    n_samples: int
) -> None:
    """验证PCA参数输入。

    Args:
        n_components: 主成分数量
        n_features: 特征数量
        n_samples: 样本数量

    Raises:
        WpPcaDataError: 参数无效
    """
    from ..exceptions import create_data_error

    # 验证特征数量
    if n_features <= 0:
        raise create_data_error(f"特征数量必须大于0，当前值: {n_features}")

    # 验证样本数量
    if n_samples <= 0:
        raise create_data_error(f"样本数量必须大于0，当前值: {n_samples}")

    # 验证主成分数量
    if n_components is not None:
        if n_components <= 0:
            raise create_data_error(f"主成分数量必须大于0，当前值: {n_components}")

        max_components = min(n_samples, n_features)
        if n_components > max_components:
            raise create_data_error(
                f"主成分数量({n_components})超过最大可能值({max_components})"
            )

    # 检查样本数量与特征数量的关系
    if n_samples < n_features:
        # 这不是错误，但需要警告
        import structlog
        logger = structlog.get_logger(__name__)
        logger.warning(
            "样本数量少于特征数量，可能影响PCA结果稳定性",
            n_samples=n_samples,
            n_features=n_features
        )
