"""
Defines the core data container classes: Dimension and Unit.
"""

import collections
from dataclasses import dataclass, field


class Dimension:
    """
    Represents the dimension of a physical quantity.

    Internally, dimensions are stored in a Counter mapping base dimension
    names (e.g., 'length', 'mass') to their powers.
    """

    def __init__(self, *args, **kwargs):
        self._dims = collections.Counter(*args, **kwargs)

    @property
    def is_dimensionless(self) -> bool:
        """Check if the dimension is dimensionless."""
        return not any(self._dims.values())

    def __mul__(self, other: "Dimension") -> "Dimension":
        if not isinstance(other, Dimension):
            return NotImplemented
        new_dims = self._dims + other._dims
        return Dimension(new_dims)

    def __truediv__(self, other: "Dimension") -> "Dimension":
        if not isinstance(other, Dimension):
            return NotImplemented
        new_dims = self._dims - other._dims
        return Dimension(new_dims)

    def __pow__(self, power: float) -> "Dimension":
        if not isinstance(power, (int, float)):
            return NotImplemented
        new_dims = {d: p * power for d, p in self._dims.items()}
        return Dimension(new_dims)

    def __rtruediv__(self, other):
        return self.__pow__(-1) * other

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Dimension):
            return NotImplemented
        return self._dims == other._dims

    def __repr__(self) -> str:
        return f"<Dimension({self._dims})>"


@dataclass(frozen=True)
class Unit:
    """
    Represents a specific unit of measure.

    This is a frozen dataclass, meaning its instances are immutable.

    Attributes:
        name (str): The full name of the unit (e.g., 'meter').
        symbol (str): The standard symbol (e.g., 'm').
        dimension (Dimension): The physical dimension of the unit.
        scale (float): The conversion factor to the reference unit for this dimension.
        offset (float): The offset for linear conversions (e.g., for Celsius).
        aliases (tuple[str, ...]): Alternative names or symbols for the unit.
    """
    name: str
    symbol: str
    dimension: Dimension
    scale: float
    offset: float = 0.0
    aliases: tuple[str, ...] = field(default_factory=tuple)

    def __repr__(self) -> str:
        return f"<Unit('{self.name}')>"
