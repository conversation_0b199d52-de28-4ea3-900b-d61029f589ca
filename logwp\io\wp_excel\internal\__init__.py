"""WP Excel读取服务层。

本模块提供WP Excel文件格式特定的解析服务，实现WFS v1.0规范的完整支持。
遵循SAD文档的"内部服务层设计（Utility/Helper Pattern）"模式。

服务模块：
- excel_parser: Excel文件基础解析服务
- head_info_parser: _Head_Info表单解析服务
- well_map_parser: _Well_Map表单解析服务
- dataset_parser: 数据集表单解析服务
- curve_parser: 曲线元数据解析服务
- data_converter: WFS到Models层转换服务
- validator: WFS规范验证服务

设计原则：
- 无状态函数设计，便于测试和复用
- 严格遵循WFS v1.0规范
- 支持大小写不敏感处理（CIIA架构）
- 完整的错误处理和结构化异常
"""

from __future__ import annotations

__all__ = [
    # 核心解析服务
    "excel_parser",
    "head_info_parser",
    "well_map_parser",
    "dataset_parser",
    "curve_parser",
    "data_converter",
    "validator",
]
