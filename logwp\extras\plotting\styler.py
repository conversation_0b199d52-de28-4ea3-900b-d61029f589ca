"""logwp.extras.plotting.styler - 样式应用工具函数

实现将PlotProfile配置应用到matplotlib对象的核心功能。

Architecture
------------
层次/依赖: logwp.extras.plotting包工具层
设计原则: 单一职责、异常安全、向后兼容
性能特征: 批量样式应用、延迟错误处理、资源安全

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持完整性
- LG-2: 结构化日志信息
- PF-1: 内存控制和资源管理

References
----------
- 《绘图系统全新重构设计文档》§1.4 - styler工具函数设计
- 《SCAPE_CCG_编码与通用规范》§EH - 现代化异常处理
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, Optional

from .exceptions import StyleApplicationError

if TYPE_CHECKING:
    import matplotlib.pyplot as plt
    from .profiles import PlotProfile

# 延迟导入logger避免循环依赖
def _get_logger():
    try:
        from logwp.infra import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)


def apply_profile(ax: plt.Axes, profile: PlotProfile) -> None:
    """将PlotProfile配置应用到matplotlib Axes对象。

    这是新绘图系统的核心函数，负责将配置模板转换为实际的matplotlib样式。
    采用分阶段应用策略，确保即使部分配置失败也能继续执行。

    应用顺序：
    1. matplotlib.rcParams 全局样式
    2. Figure级别属性（尺寸、DPI等）
    3. 标题和标签样式
    4. 坐标轴和网格样式
    5. 图例和其他装饰元素

    Args:
        ax: 目标matplotlib Axes对象
        profile: 绘图配置模板

    Raises:
        StyleApplicationError: 关键样式应用失败

    Examples:
        >>> import matplotlib.pyplot as plt
        >>> from logwp.extras.plotting import registry, apply_profile
        >>>
        >>> fig, ax = plt.subplots()
        >>> profile = registry.get("plt_analyzer.capture_curve")
        >>> apply_profile(ax, profile)
        >>>
        >>> # 现在ax已经应用了所有配置的样式
        >>> ax.plot(x, y, **profile.artist_props)
    """
    _get_logger().debug(
        "开始应用绘图配置",
        operation="apply_profile",
        profile_name=profile.name
    )

    try:
        # 阶段1: 应用全局rcParams
        _apply_rc_params(profile.rc_params, profile.name)

        # 阶段2: 应用Figure级别属性
        _apply_figure_props(ax, profile.figure_props, profile.name)

        # 阶段3: 应用标题样式
        _apply_title_props(ax, profile.title_props, profile.name)

        # 阶段4: 应用坐标轴标签样式
        _apply_label_props(ax, profile.label_props, profile.name)

        # 阶段5: 应用坐标轴和网格样式（从rc_params推导）
        _apply_axes_styling(ax, profile.rc_params, profile.name)

        # 阶段6: 应用来自artist_props的坐标轴样式（如刻度、范围）
        _apply_artist_axes_props(ax, profile.artist_props, profile.name)

        _get_logger().info(
            "绘图配置应用成功",
            operation="apply_profile",
            profile_name=profile.name
        )

    except Exception as e:
        raise StyleApplicationError(
            f"应用绘图配置失败: {e}",
            profile_name=profile.name,
            matplotlib_object="Axes"
        ) from e


def _apply_rc_params(rc_params: Dict[str, Any], profile_name: str) -> None:
    """应用matplotlib全局样式参数。

    Args:
        rc_params: rcParams字典
        profile_name: 配置模板名称（用于日志）
    """
    if not rc_params:
        return

    try:
        import matplotlib.pyplot as plt

        # 批量更新rcParams，提高性能
        plt.rcParams.update(rc_params)

        _get_logger().debug(
            "rcParams已更新",
            profile_name=profile_name,
            param_count=len(rc_params)
        )

    except Exception as e:
        # rcParams错误通常不是致命的，记录警告但继续执行
        _get_logger().warning(
            "rcParams应用部分失败，使用默认样式",
            profile_name=profile_name,
            error=str(e),
            failed_params=list(rc_params.keys())
        )


def _apply_figure_props(ax: plt.Axes, figure_props: Dict[str, Any], profile_name: str) -> None:
    """应用Figure级别属性。

    Args:
        ax: matplotlib Axes对象
        figure_props: Figure属性字典
        profile_name: 配置模板名称
    """
    if not figure_props:
        return

    try:
        fig = ax.get_figure()

        # 处理特殊属性
        if "figsize" in figure_props:
            width, height = figure_props["figsize"]
            fig.set_size_inches(width, height)

        if "dpi" in figure_props:
            fig.set_dpi(figure_props["dpi"])

        if "facecolor" in figure_props:
            fig.patch.set_facecolor(figure_props["facecolor"])

        if "edgecolor" in figure_props:
            fig.patch.set_edgecolor(figure_props["edgecolor"])

        # 处理布局
        if "layout" in figure_props:
            layout = figure_props["layout"]
            if layout == "constrained":
                fig.set_layout_engine("constrained")
            elif layout == "tight":
                fig.set_layout_engine("tight")

        _get_logger().debug(
            "Figure属性已应用",
            profile_name=profile_name,
            applied_props=list(figure_props.keys())
        )

    except Exception as e:
        _get_logger().warning(
            "Figure属性应用失败",
            profile_name=profile_name,
            error=str(e),
            failed_props=list(figure_props.keys())
        )


def _apply_title_props(ax: plt.Axes, title_props: Dict[str, Any], profile_name: str) -> None:
    """应用标题样式。

    Args:
        ax: matplotlib Axes对象
        title_props: 标题属性字典
        profile_name: 配置模板名称
    """
    if not title_props:
        return

    try:
        # 提取标题文本，其余作为样式参数
        title_text = title_props.get("label", "")
        style_props = {k: v for k, v in title_props.items() if k != "label"}

        if title_text:
            ax.set_title(title_text, **style_props)
        elif style_props:
            # 如果没有标题文本但有样式，应用到当前标题
            current_title = ax.get_title()
            if current_title:
                ax.set_title(current_title, **style_props)

        _get_logger().debug(
            "标题样式已应用",
            profile_name=profile_name,
            has_title_text=bool(title_text),
            style_props=list(style_props.keys())
        )

    except Exception as e:
        _get_logger().warning(
            "标题样式应用失败",
            profile_name=profile_name,
            error=str(e)
        )


def _apply_artist_axes_props(ax: plt.Axes, artist_props: Dict[str, Any], profile_name: str) -> None:
    """从 artist_props['axes'] 应用坐标轴属性。

    此函数负责应用更具体的坐标轴设置，如刻度类型（对数/线性）、
    范围限制（xlim/ylim）和宽高比（aspect）。

    Args:
        ax: matplotlib Axes对象
        artist_props: artist_props 字典
        profile_name: 配置模板名称
    """
    axes_props = artist_props.get("axes")
    if not isinstance(axes_props, dict):
        return

    _get_logger().debug(
        "开始应用 artist_props['axes'] 配置",
        profile_name=profile_name,
        props_to_apply=list(axes_props.keys())
    )

    # 应用网格属性
    grid_props = artist_props.get("grid")
    if isinstance(grid_props, dict):
        _get_logger().debug(
            "应用 artist_props['grid'] 配置",
            profile_name=profile_name,
            props_to_apply=list(grid_props.keys())
        )
        try:
            # 如果'visible'显式设置为False，则不显示网格
            if grid_props.get("visible") is False:
                ax.grid(False)
            else:
                # 否则，应用所有提供的网格属性
                ax.grid(**grid_props)
        except Exception as e:
            _get_logger().warning(
                "应用网格属性失败",
                profile_name=profile_name,
                error=str(e)
            )

    # 使用一个更安全的方式来调用setter方法
    for key, value in axes_props.items():
        setter_name = f"set_{key}"
        if hasattr(ax, setter_name):
            try:
                getattr(ax, setter_name)(value)
            except Exception as e:
                _get_logger().warning(
                    f"应用坐标轴属性 '{key}' 失败",
                    profile_name=profile_name,
                    error=str(e)
                )
        else:
            _get_logger().warning(f"未知的坐标轴属性: '{key}'，将被忽略。")


def _apply_label_props(ax: plt.Axes, label_props: Dict[str, Any], profile_name: str) -> None:
    """应用坐标轴标签样式。

    Args:
        ax: matplotlib Axes对象
        label_props: 标签属性字典
        profile_name: 配置模板名称
    """
    if not label_props:
        return

    try:
        # 处理X轴标签
        if "xlabel" in label_props:
            xlabel_text = label_props["xlabel"]
            xlabel_props = {k: v for k, v in label_props.items()
                          if k not in ["xlabel", "ylabel"]}
            ax.set_xlabel(xlabel_text, **xlabel_props)

        # 处理Y轴标签
        if "ylabel" in label_props:
            ylabel_text = label_props["ylabel"]
            ylabel_props = {k: v for k, v in label_props.items()
                          if k not in ["xlabel", "ylabel"]}
            ax.set_ylabel(ylabel_text, **ylabel_props)

        # 如果只有样式没有标签文本，应用到现有标签
        if "xlabel" not in label_props and "ylabel" not in label_props:
            style_props = label_props.copy()

            current_xlabel = ax.get_xlabel()
            if current_xlabel:
                ax.set_xlabel(current_xlabel, **style_props)

            current_ylabel = ax.get_ylabel()
            if current_ylabel:
                ax.set_ylabel(current_ylabel, **style_props)

        _get_logger().debug(
            "标签样式已应用",
            profile_name=profile_name,
            has_xlabel="xlabel" in label_props,
            has_ylabel="ylabel" in label_props
        )

    except Exception as e:
        _get_logger().warning(
            "标签样式应用失败",
            profile_name=profile_name,
            error=str(e)
        )


def _apply_axes_styling(ax: plt.Axes, rc_params: Dict[str, Any], profile_name: str) -> None:
    """应用坐标轴和网格样式。

    从rc_params中提取坐标轴相关设置并应用到当前axes。

    Args:
        ax: matplotlib Axes对象
        rc_params: rcParams字典
        profile_name: 配置模板名称
    """
    try:
        # 应用网格设置
        if "axes.grid" in rc_params:
            grid_visible = rc_params["axes.grid"]
            ax.grid(grid_visible)

            # 应用网格样式
            if grid_visible:
                grid_props = {}
                if "grid.linestyle" in rc_params:
                    grid_props["linestyle"] = rc_params["grid.linestyle"]
                if "grid.alpha" in rc_params:
                    grid_props["alpha"] = rc_params["grid.alpha"]
                if "grid.color" in rc_params:
                    grid_props["color"] = rc_params["grid.color"]
                if "grid.linewidth" in rc_params:
                    grid_props["linewidth"] = rc_params["grid.linewidth"]

                if grid_props:
                    ax.grid(**grid_props)

        # 应用刻度设置
        if "xtick.minor.visible" in rc_params:
            ax.tick_params(axis='x', which='minor',
                         bottom=rc_params["xtick.minor.visible"])

        if "ytick.minor.visible" in rc_params:
            ax.tick_params(axis='y', which='minor',
                         left=rc_params["ytick.minor.visible"])

        # 应用边框设置
        if "axes.spines.top" in rc_params:
            ax.spines['top'].set_visible(rc_params["axes.spines.top"])
        if "axes.spines.right" in rc_params:
            ax.spines['right'].set_visible(rc_params["axes.spines.right"])
        if "axes.spines.bottom" in rc_params:
            ax.spines['bottom'].set_visible(rc_params["axes.spines.bottom"])
        if "axes.spines.left" in rc_params:
            ax.spines['left'].set_visible(rc_params["axes.spines.left"])

        _get_logger().debug(
            "坐标轴样式已应用",
            profile_name=profile_name
        )

    except Exception as e:
        _get_logger().warning(
            "坐标轴样式应用失败",
            profile_name=profile_name,
            error=str(e)
        )
