"""标准数据集夹具 - 测试专用

提供预定义的标准测试数据集夹具。

Examples
--------
>>> # 获取标准连续型数据集
>>> dataset = StandardDatasets.get_continuous_dataset()
>>>
>>> # 获取标准离散型数据集
>>> core_dataset = StandardDatasets.get_discrete_dataset()
>>>
>>> # 获取标准区间型数据集
>>> interval_dataset = StandardDatasets.get_interval_dataset()
"""

from __future__ import annotations

from typing import Any, TYPE_CHECKING

if TYPE_CHECKING:
    from logwp.models.datasets import WpContinuousDataset, WpDiscreteDataset, WpIntervalDataset


class StandardDatasets:
    """标准数据集夹具（Fixture）类。

    提供预定义的、带缓存的标准测试数据集，是进行一致性测试和功能验证的首选。

    使用此类中的 `get_*` 方法可以确保在不同测试用例中获取到完全相同的数据实例，
    从而提高测试的可重复性和性能。
    """

    # 缓存数据集实例
    _continuous_dataset: Any = None
    _discrete_dataset: Any = None
    _interval_dataset: Any = None
    _nmr_dataset: Any = None

    @classmethod
    def get_continuous_dataset(cls, force_recreate: bool = False) -> 'WpContinuousDataset':
        """获取标准连续型数据集。

        Args:
            force_recreate: 是否强制重新创建

        Returns:
            WpContinuousDataset: 标准连续型数据集
        """
        if cls._continuous_dataset is None or force_recreate:
            try:
                from logwp.testing.builders import DatasetBuilder

                cls._continuous_dataset = DatasetBuilder.quick_continuous_dataset(
                    name="standard_continuous_fixture",
                    well_name="FIXTURE-1",
                    depth_range=(2500.0, 2520.0),
                    interval=0.5,
                    curves={
                        "GR": lambda d: 55 + 15 * ((d - 2510) / 20),
                        "PHIT": lambda d: 0.16 + 0.06 * ((d - 2510) / 20),
                        "SW": lambda d: 0.40 + 0.20 * ((d - 2500) / 20),
                        "PERM": lambda d: 12 + 8 * ((d - 2510) / 20),
                        "RHOB": lambda d: 2.45 + 0.15 * ((d - 2500) / 20),
                        "NPHI": lambda d: 0.18 + 0.08 * ((d - 2510) / 20)
                    }
                )

            except ImportError:
                raise RuntimeError(
                    "StandardDatasets.get_continuous_dataset() requires full logwp environment."
                )

        return cls._continuous_dataset

    @classmethod
    def get_discrete_dataset(cls, force_recreate: bool = False) -> 'WpDiscreteDataset':
        """获取标准离散型数据集。

        Args:
            force_recreate: 是否强制重新创建

        Returns:
            WpDiscreteDataset: 标准离散型数据集
        """
        if cls._discrete_dataset is None or force_recreate:
            try:
                from logwp.testing.builders import DatasetBuilder

                cls._discrete_dataset = DatasetBuilder.quick_discrete_dataset(
                    name="standard_discrete_fixture",
                    well_name="FIXTURE-1",
                    depths=[2501.5, 2504.3, 2507.8, 2511.2, 2514.7, 2518.1],
                    curves={
                        "PERM_CORE": [15.2, 8.9, 22.1, 11.5, 18.7, 13.3],
                        "PHIT_CORE": [0.18, 0.14, 0.21, 0.16, 0.19, 0.17],
                        "GRAIN_DENS": [2.65, 2.68, 2.63, 2.66, 2.64, 2.67],
                        "FACIES": ["砂岩", "泥岩", "砂岩", "砂岩", "砂岩", "泥岩"],
                        "GRAIN_SIZE": ["中粒", "细粒", "粗粒", "中粒", "中粒", "细粒"],
                        "CEMENT_TYPE": ["石英", "粘土", "碳酸盐", "石英", "石英", "粘土"]
                    }
                )

            except ImportError:
                raise RuntimeError(
                    "StandardDatasets.get_discrete_dataset() requires full logwp environment."
                )

        return cls._discrete_dataset

    @classmethod
    def get_interval_dataset(cls, force_recreate: bool = False) -> 'WpIntervalDataset':
        """获取标准区间型数据集。

        Args:
            force_recreate: 是否强制重新创建

        Returns:
            WpIntervalDataset: 标准区间型数据集
        """
        if cls._interval_dataset is None or force_recreate:
            try:
                from logwp.testing.builders import DatasetBuilder

                intervals = [
                    (2500.0, 2506.0),  # 储层A
                    (2506.0, 2508.0),  # 盖层
                    (2508.0, 2515.0),  # 储层B
                    (2515.0, 2517.0),  # 盖层
                    (2517.0, 2525.0)   # 储层C
                ]

                cls._interval_dataset = DatasetBuilder.quick_interval_dataset(
                    name="standard_interval_fixture",
                    well_name="FIXTURE-1",
                    intervals=intervals,
                    curves={
                        "FORMATION": ["RES_A", "SEAL_1", "RES_B", "SEAL_2", "RES_C"],
                        "ZONE_TYPE": ["储层", "盖层", "储层", "盖层", "储层"],
                        "NET_PAY": [5.5, 0.0, 6.8, 0.0, 7.2],
                        "AVG_PORO": [0.17, 0.05, 0.19, 0.04, 0.21],
                        "AVG_PERM": [18.5, 0.1, 25.2, 0.1, 32.1],
                        "AVG_SW": [0.35, 0.95, 0.30, 0.95, 0.25],
                        "HCPV": [0.85, 0.0, 1.12, 0.0, 1.35],
                        "RECOVERY": [0.45, 0.0, 0.50, 0.0, 0.55]
                    }
                )

            except ImportError:
                raise RuntimeError(
                    "StandardDatasets.get_interval_dataset() requires full logwp environment."
                )

        return cls._interval_dataset

    @classmethod
    def get_nmr_dataset(cls, force_recreate: bool = False, t2_bins: int = 32) -> 'WpContinuousDataset':
        """获取标准NMR数据集。

        Args:
            force_recreate: 是否强制重新创建
            t2_bins: T2谱bin数量

        Returns:
            WpContinuousDataset: 标准NMR数据集
        """
        if cls._nmr_dataset is None or force_recreate:
            try:
                from logwp.testing.builders import DatasetBuilder

                cls._nmr_dataset = DatasetBuilder.synthetic_nmr_dataset(
                    name="standard_nmr_fixture",
                    well_name="FIXTURE-1",
                    depth_range=(2800.0, 2815.0),
                    interval=0.5,
                    t2_bins=t2_bins
                )

            except ImportError:
                raise RuntimeError(
                    "StandardDatasets.get_nmr_dataset() requires full logwp environment."
                )

        return cls._nmr_dataset

    @classmethod
    def get_all_datasets(cls, force_recreate: bool = False) -> dict[str, Any]:
        """获取所有标准数据集。

        Args:
            force_recreate: 是否强制重新创建

        Returns:
            dict: 包含所有标准数据集的字典
        """
        return {
            "continuous": cls.get_continuous_dataset(force_recreate),
            "discrete": cls.get_discrete_dataset(force_recreate),
            "interval": cls.get_interval_dataset(force_recreate),
            "nmr": cls.get_nmr_dataset(force_recreate)
        }

    @classmethod
    def clear_cache(cls) -> None:
        """清除缓存的数据集。"""
        cls._continuous_dataset = None
        cls._discrete_dataset = None
        cls._interval_dataset = None
        cls._nmr_dataset = None

    @classmethod
    def get_dataset_info(cls) -> dict[str, dict[str, Any]]:
        """获取数据集信息摘要。

        Returns:
            dict: 数据集信息摘要
        """
        return {
            "continuous": {
                "name": "standard_continuous_fixture",
                "well": "FIXTURE-1",
                "depth_range": (2500.0, 2520.0),
                "interval": 0.5,
                "curves": ["GR", "PHIT", "SW", "PERM", "RHOB", "NPHI"]
            },
            "discrete": {
                "name": "standard_discrete_fixture",
                "well": "FIXTURE-1",
                "depths": [2501.5, 2504.3, 2507.8, 2511.2, 2514.7, 2518.1],
                "curves": ["PERM_CORE", "PHIT_CORE", "GRAIN_DENS", "FACIES", "GRAIN_SIZE", "CEMENT_TYPE"]
            },
            "interval": {
                "name": "standard_interval_fixture",
                "well": "FIXTURE-1",
                "intervals": 5,
                "curves": ["FORMATION", "ZONE_TYPE", "NET_PAY", "AVG_PORO", "AVG_PERM", "AVG_SW", "HCPV", "RECOVERY"]
            },
            "nmr": {
                "name": "standard_nmr_fixture",
                "well": "FIXTURE-1",
                "depth_range": (2800.0, 2815.0),
                "interval": 0.5,
                "t2_bins": 32
            }
        }
