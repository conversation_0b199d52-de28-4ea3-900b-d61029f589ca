"""Internal computation logic for the LogScout step.

This module contains pure functions for performing all quantitative analyses,
such as calculating correlation matrices, Variance Inflation Factor (VIF),
and mutual information scores. These functions are designed to be stateless
and easily testable.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Literal

import numpy as np
import pandas as pd
from sklearn.feature_selection import mutual_info_classif, mutual_info_regression
from statsmodels.stats.outliers_influence import variance_inflation_factor

if TYPE_CHECKING:
    from pandas import DataFrame


def compute_correlations(
    df: DataFrame, features: list[str]
) -> tuple[DataFrame, DataFrame]:
    """Calculates both Pearson and <PERSON>pearman correlation matrices for the given features.

    Args:
        df: The input DataFrame containing the feature data.
        features: A list of column names to be included in the correlation analysis.

    Returns:
        A tuple containing two DataFrames: (pearson_correlation, spearman_correlation).
    """
    feature_df = df[features]
    pearson_corr = feature_df.corr(method="pearson")
    spearman_corr = feature_df.corr(method="spearman")
    return pearson_corr, spearman_corr


def compute_vif(df: DataFrame, features: list[str]) -> DataFrame:
    """Calculates the Variance Inflation Factor (VIF) for each feature.

    VIF measures how much the variance of an estimated regression coefficient is
    increased due to collinearity. A high VIF indicates that the feature is
    highly correlated with other features.

    Args:
        df: The input DataFrame containing the feature data.
        features: A list of column names for which to calculate VIF.

    Returns:
        A DataFrame with 'Feature' and 'VIF' columns, sorted by VIF descending.
        Returns an empty DataFrame if there are fewer than 2 features.
    """
    if len(features) < 2:
        return pd.DataFrame(columns=["Feature", "VIF"])

    feature_df = df[features].copy()
    # Add a constant for VIF calculation, which is standard practice.
    feature_df["_const"] = 1

    vif_data = []
    for i, feature in enumerate(features):
        try:
            vif_score = variance_inflation_factor(
                exog=feature_df.values, exog_idx=i
            )
            vif_data.append({"Feature": feature, "VIF": vif_score})
        except np.linalg.LinAlgError:
            # This can happen in cases of perfect multicollinearity.
            vif_data.append({"Feature": feature, "VIF": np.inf})

    vif_df = pd.DataFrame(vif_data)
    vif_df = vif_df.sort_values(by="VIF", ascending=False).reset_index(drop=True)

    return vif_df


def compute_mutual_information(
    df: DataFrame,
    features: list[str],
    target: str,
    task_type: Literal["regression", "classification"],
) -> DataFrame:
    """Calculates the mutual information between each feature and the target.

    Mutual information measures the dependency between two variables, capturing
    both linear and non-linear relationships.

    Args:
        df: The input DataFrame containing feature and target data.
        features: A list of input feature column names.
        target: The target column name.
        task_type: The type of task, either 'regression' or 'classification'.

    Returns:
        A DataFrame with 'Feature' and 'Mutual_Information' columns, sorted
        by the score in descending order.
    """
    x = df[features]
    y = df[target]

    if task_type == "regression":
        mi_scores = mutual_info_regression(x, y)
    elif task_type == "classification":
        mi_scores = mutual_info_classif(x, y)
    else:
        raise ValueError(f"Unsupported task_type: {task_type}")

    mi_df = pd.DataFrame(
        {"Feature": features, "Mutual_Information": mi_scores}
    ).sort_values(by="Mutual_Information", ascending=False, ignore_index=True)

    return mi_df
