# logwp.testing - 测试工具包

`logwp.testing` 是为 SCAPE 项目量身定制的测试支持库，旨在统一和简化测试数据的创建，尤其强化了对 `scape/core` 中科学计算算法的测试支持。

## 设计原则

- **测试专用**: 仅用于测试目的，不应在生产代码中使用
- **快速构建**: 提供从简单到具有物理意义的各类测试数据生成方法
- **职责明确**: 清晰划分不同工具的用途，避免混淆
- **可复用**: 通过标准“夹具”（Fixture）确保测试数据的一致性和可重复性

## 包结构

```text
logwp/testing/
├── __init__.py              # 统一导出主要API
├── builders/                # 构造器 (用于自定义构建)
│   ├── dataset_builder.py   # 数据集构建器
│   ├── project_builder.py   # 项目构建器
│   └── curve_builder.py     # 曲线构建器
├── factories/               # 工厂 (用于创建预定义对象)
│   ├── data_factory.py      # 测试项目工厂
│   ├── algorithm_data_factory.py # 算法专用数据工厂
│   ├── synthetic_curve_factory.py # 合成曲线工厂
│   └── metadata_factory.py  # 元数据工厂
├── fixtures/                # 夹具 (用于获取标准、可复用数据)
│   └── standard_datasets.py # 标准数据集夹具
└── utils/                   # 工具函数 (底层辅助)
    ├── quick_generators.py  # 快速生成器 (DataFrame, Metadata)
    └── validators.py        # 测试验证器
```


## 核心API：如何选择合适的工具？

为了高效编写测试，请根据您的需求选择最合适的工具：

| 您的需求是... | 最佳选择 | 示例 |
| :--- | :--- | :--- |
| 获取一个**标准的、可复用的**测试**数据集**？ | `StandardDatasets` (夹具) | `StandardDatasets.get_continuous_dataset()` |
| 获取一个**预定义的、完整的**测试**项目**？ | `TestDataFactory` (工厂) | `TestDataFactory.santos_demo_project()` |
| **自定义构建**一个特定的**数据集**？ | `DatasetBuilder` (构造器) | `DatasetBuilder.quick_continuous_dataset(...)` |
| **自定义构建**一个特定的**项目**？ | `ProjectBuilder` (构造器) | `ProjectBuilder("MyProject").with_dataset(...)` |
| 生成**具有物理意义**的合成**曲线数据**？ | `SyntheticCurveFactory` (工厂) | `SyntheticCurveFactory.nmr_t2_distribution(...)` |
| 为特定**算法**生成**输入+预期输出**？ | `FosterNMRTestDataFactory` (算法工厂) | `FosterNMRTestDataFactory.create_dataset_with_expected_perm()` |
| **验证**科学计算结果的**数值准确性**？ | `assert_curves_allclose` (验证器) | `assert_curves_allclose(actual, desired)` |
| **验证**曲线是否符合**物理约束**？ | `assert_physical_constraints` (验证器) | `assert_physical_constraints(perm, {"min": 0})` |

---

## 如何为 `scape/core` 编写高质量测试

为 `scape/core` 中的科学计算或机器学习算法编写测试时，我们推荐遵循经典的 **Arrange-Act-Assert (AAA)** 模式，并充分利用 `logwp/testing` 提供的工具。

**一个完整的测试流程如下：**

```python
import pytest
from logwp.testing import FosterNMRTestDataFactory, assert_curves_allclose, assert_physical_constraints
from scape.core.algorithms import foster_nmr # 假设的算法模块

def test_foster_nmr_calculation():
    # 1. ARRANGE (准备):
    #    - 使用算法专用工厂，一键生成包含输入曲线的数据集
    #    - 和一个根据公式精确计算出的、用于验证的预期结果数组。
    #    - 这种方式确保了测试的确定性和可重复性。
    input_dataset, expected_perm = FosterNMRTestDataFactory.create_dataset_with_expected_perm(
        n_points=100,
        phit_range=(0.05, 0.35),
        t2lm_range=(10.0, 500.0),
        foster_c=1.0,
        foster_m=4.0,
        foster_n=2.0
    )

    # 2. ACT (执行):
    #    - 调用 `scape/core` 中实际的算法进行计算。
    calculated_perm_bundle = foster_nmr.calculate(input_dataset)
    calculated_perm = calculated_perm_bundle.data["PERM"]

    # 3. ASSERT (断言):
    #    - 使用科学计算专用的验证器进行精确断言。

    #    - 断言1: 验证计算结果与理论值在容差范围内是否几乎相等。
    #      这是验证算法核心逻辑正确性的关键。
    assert_curves_allclose(
        actual=calculated_perm,
        desired=expected_perm,
        rtol=1e-6, # 使用相对容差以适应不同数量级的渗透率值
        err_msg="FOSTER-NMR渗透率计算结果与理论值不符"
    )

    #    - 断言2: 验证结果是否符合基本的物理约束（例如，渗透率 > 0）。
    #      这确保了算法的输出在物理上是合理的。
    assert_physical_constraints(
        curve_data=calculated_perm,
        curve_name="PERM",
        constraints={"min": 0}
    )
```

## 常见测试场景与工具选择

### 场景1: 获取标准测试数据集 (最常用)

当您需要一个标准化的数据集进行算法测试或功能验证时，请使用 `StandardDatasets`。它提供带缓存的实例，性能更佳。

```python
from logwp.testing import StandardDatasets

# 获取标准连续数据集
continuous_ds = StandardDatasets.get_continuous_dataset()

# 获取标准离散数据集
discrete_ds = StandardDatasets.get_discrete_dataset()

# 获取标准区间数据集
interval_ds = StandardDatasets.get_interval_dataset()

# 获取NMR数据集
nmr_ds = StandardDatasets.get_nmr_dataset()

# 获取所有标准数据集
all_datasets = StandardDatasets.get_all_datasets()
```

### 场景2: 获取预定义的完整测试项目

当您的测试需要一个包含多个数据集、头部信息和井名映射的完整 WpWellProject 时，请使用 TestDataFactory。

```python
from logwp.testing import TestDataFactory

# 获取一个包含多个数据集的、功能完备的演示项目
project = TestDataFactory.santos_demo_project()

# 获取一个结构最简单的项目
minimal_project = TestDataFactory.minimal_project()
```

### 场景3: 测试边界条件或简单逻辑

当标准数据无法满足您特定的测试场景时（例如，测试边界条件、特定数据分布），请使用 builders。

```python
from logwp.testing.builders import DatasetBuilder

# 创建一个包含特定曲线和数据范围的连续数据集
custom_dataset = DatasetBuilder.quick_continuous_dataset(
    name="edge_case_logs",
    curves={"GR": -999.25, "PHIT": None} # 测试空值和无效值
)
```

### 场景4: 创建标准元数据

当您需要为测试数据集创建标准的曲线元数据时，请使用 `MetadataFactory`。

```python
from logwp.testing import MetadataFactory

# 创建标准测井曲线元数据
logging_metadata = MetadataFactory.standard_logging_metadata()

# 创建NMR相关曲线元数据
nmr_metadata = MetadataFactory.nmr_metadata(t2_bins=64)

# 创建岩心分析元数据
core_metadata = MetadataFactory.core_analysis_metadata()
```

### 场景5: 生成具有物理意义的合成曲线

当您的测试需要模拟真实的物理过程或统计特征时（例如，测试科学计算算法），请使用 `SyntheticCurveFactory`。

```python
from logwp.testing import SyntheticCurveFactory

# 生成一个包含束缚水和自由流体双峰的T2谱分布
t2_dist = SyntheticCurveFactory.nmr_t2_distribution(
    depth_points=100,
    t2_bins=64,
    peaks=[(3, 1, 10), (200, 2, 50)] # (T2位置, 振幅, 宽度)
)

# 生成根据岩相分布的GR曲线
facies = ["sand"] * 50 + ["shale"] * 50
gr_curve = SyntheticCurveFactory.facies_based_curve(
    facies_log=facies,
    mapping={"sand": (30, 5), "shale": (90, 8)} # (均值, 标准差)
)
```

### 场景6: 使用快速生成器

当您需要快速生成DataFrame或元数据而不需要完整的数据集对象时，请使用快速生成器。

```python
from logwp.testing import quick_continuous_df, quick_discrete_df, quick_metadata

# 快速生成连续DataFrame
df = quick_continuous_df(
    well_name="W-1",
    start_depth=2500,
    end_depth=2510,
    interval=0.5,
    GR=50,
    PHIT=0.15
)

# 快速生成离散DataFrame
discrete_df = quick_discrete_df(
    well_name="W-2",
    depths=[2502.5, 2507.3, 2512.1],
    PERM=[12.5, 8.9, 15.2],
    FACIES=["砂岩", "泥岩", "砂岩"]
)

# 快速生成元数据
metadata = quick_metadata("WELL", "MD", "GR", "PHIT", "PERM")
```

## 完整测试流程示例

以下示例展示了如何结合使用不同的工具来编写一个完整的测试。

```python
import pytest
from logwp.testing import StandardDatasets, DatasetBuilder, TestDataFactory, validate_test_dataset

# 1. 针对算法的单元测试 (使用标准夹具)
def test_my_algorithm_with_standard_data():
    # ARRANGE: 获取一个标准、一致的数据集
    dataset = StandardDatasets.get_continuous_dataset()

    # ACT: 执行算法
    result = my_algorithm(dataset)

    # ASSERT: 验证结果
    assert result is not None
    assert result > 0.15

# 2. 针对边界条件的单元测试 (使用构造器)
def test_my_algorithm_with_null_data():
    # ARRANGE: 构造一个包含空值的数据集
    dataset_with_nulls = DatasetBuilder.quick_continuous_dataset(
        name="null_test",
        curves={"GR": 50, "PHIT": None}
    )

    # ACT & ASSERT: 验证算法能正确处理空值
    with pytest.raises(ValueError):
        my_algorithm(dataset_with_nulls)

# 3. 针对项目级功能的集成测试 (使用工厂)
def test_project_summary_generation():
    # ARRANGE: 获取一个完整的演示项目
    project = TestDataFactory.santos_demo_project()

    # ACT: 执行项目级功能
    summary = project.generate_summary()

    # ASSERT: 验证报告中包含了预期的内容
    assert "Santos_Demo_Project" in summary
    assert "Santos_logs" in summary
    assert "Santos_core" in summary

# 4. 确保我们创建的测试数据本身是有效的 (使用验证器)
def test_fixture_validity():
    dataset = StandardDatasets.get_continuous_dataset()
    assert validate_test_dataset(dataset)

```

## 注意事项

- **仅用于测试**: 此包不应在生产代码中被引用。所有方法都针对测试场景优化，可能不适合生产环境的性能要求。
- **数据非物理真实**: 生成的数据主要用于功能和逻辑验证，不保证其符合真实的物理规律。`SyntheticCurveFactory`生成的数据具有一定的物理意义，但仍主要用于测试目的。
- **环境依赖**: logwp.testing 依赖于完整的 logwp.models 环境。如果核心模型不可用，将抛出 RuntimeError。
- **统一导入**: 所有主要API都可以直接从 `logwp.testing` 包导入，无需从子模块导入。
- **缓存机制**: `StandardDatasets` 使用缓存机制提高性能，首次调用可能较慢，后续调用会很快。

## 参考文档

- 《SCAPE_STG_软件测试指南.md》- 测试数据生成规范和测试策略
- 《SCAPE_CCG_编码与通用规范.md》- 测试代码编写规范
- 《SCAPE_API_logwp_models.md》- logwp.models包API使用文档
