"""scape.core.baselines.timur.exceptions - Custom exceptions for the Timur/Coates component."""


class TimurError(Exception):
    """Base exception for all errors raised by the Timur/Coates baseline component."""
    pass


class TimurDataError(TimurError):
    """Raised for data-related errors, such as missing columns or invalid values."""
    pass


class TimurModelError(TimurError):
    """Raised for model or optimization related errors, such as optimization failure."""
    pass
