# 三元图 (Ternary Plot) 核心概念解析

本文档旨在系统性地解析三元图背后的两大核心逻辑：“标准绘图逻辑”与“直观读图逻辑”。理解这两者之间的区别与联系，对于正确创建和解读三元图至关重要。

---

## 1. 标准绘图逻辑 (The Standard Plotting Logic)

这是所有科学与工程领域公认的、用于绘制三元图的**唯一标准**，也是 Plotly 等专业绘图库的底层实现逻辑。

其核心原则如下：

*   **顶点 (Vertex) 代表 100%**: 三角形的三个顶点，分别代表其对应组分的 **100%** 纯态。
    *   例如，在我们的图中，顶部顶点代表 `VMACRO` = 100%。

*   **对边 (Opposite Side) 代表 0%**: 任意一个顶点所正对的那条边，是该组分含量为 **0%** 的线。
    *   例如，顶部 `VMACRO` 顶点所正对的底边（从 `VMICRO` 到 `VMESO`），代表了 `VMACRO` = 0%。

*   **网格线 (Grid Lines) 是等值线**: 图表内部的网格线是等值线。从一个组分的 0% 对边向其 100% 顶点移动，其含量值是**递增**的。

**结论**：在标准逻辑下，一个组分的含量**从其对边 (0%) 向其顶点 (100%) 增加**。


*图1：标准绘图逻辑下，宏孔隙度(A)从底边(0%)向顶点(100%)增加。*

---

## 2. 直观读图逻辑 (The Intuitive Reading Logic)

这是一种非常符合人类直觉的**读图习惯**，它通常从一个“主角”组分出发来解读图表。

*   **逻辑起点**: 从代表某个组分100%的顶点开始。
*   **解读方式**: 观察当从这个顶点向其对边移动时，该组分的含量是如何**递减**的。

这种读图方式非常有效，因为它能快速回答“当宏孔隙度不再占主导时，图上的点会如何分布？”这类问题。

---

## 3. 两者如何统一：一个巧妙的“视觉戏法”

在之前的沟通过程中，我们曾通过修改 Plotly 的刻度标签，实现了让图表的**视觉呈现**与“直观读图逻辑”相匹配。

*   **技术实现**:
    *   `tickvals`: 定义刻度线在数据坐标系中的**真实位置**（例如 `[0, 20, 40, ..., 100]`），这遵循标准绘图逻辑。
    *   `ticktext`: 定义在这些位置上**实际显示的文本**（例如 `[100, 80, 60, ..., 0]`），这遵循直观读图逻辑。

*   **效果**: 这种方法在不改变数据点真实位置的前提下，让坐标轴的刻度标签从顶点 (100) 向对边 (0) 递减，从而满足了直观的读图习惯。

---

## 4. 最终选择：为何回归标准

尽管“视觉戏法”在特定场景下很有效，但我们最终选择将脚本中的坐标轴恢复为标准显示方式（从 0 到 100）。主要原因如下：

1.  **科学严谨性与通用性**: 标准坐标系是全球通用的。任何熟悉三元图的科学家或工程师都能立刻、无歧义地理解图表，避免了因非标准刻度带来的潜在误解。

2.  **避免混淆**: 保持绘图逻辑与读图逻辑的表面一致性，是最清晰、最不容易出错的方式。

3.  **代码简洁与可维护性**: 遵循绘图库的默认行为，可以使代码更简洁，无需额外的“视觉技巧”，更易于长期维护。

**总结**: 为了确保图表的专业性、清晰度和通用性，我们采用标准的绘图和刻度逻辑。这要求读图者理解：**一个组分的含量是向其对应顶点递增的**。

---

## 5. 如何描述区域坐标 (How to Describe Region Coordinates)

在三元图中描述一个区域的顶点坐标时，必须遵循一个基本原则：**任何一个点的三个组分之和必须等于100%**。

下面我们通过一个具体的例子，来解析如何确定一个区域的顶点坐标。

**示例：描述图表顶部的一个三角形区域**

该区域由以下三个点定义：
1.  Macro顶点。
2.  在连接Macro和Meso的边线上，Macro为60%、Meso为40%的点。
3.  在连接Macro和Micro的边线上，Macro为60%、Micro为40%的点。

#### 坐标解析

我们以 `(Macro, Micro, Meso)` 的顺序来表示坐标。

*   **顶点1: Macro顶点**
    *   **描述**: 这是最简单的一个点，代表宏孔隙度为100%的纯态。
    *   **坐标**: 在这个点上，`Macro` = 100%，因此 `Micro` 和 `Meso` 必须都为 0%。
    *   **最终坐标**: **(100, 0, 0)**

*   **顶点2: Macro-Meso边线上的点**
    *   **描述**: "连接Macro和Meso的边线" 是 `Micro` 组分含量为 0% 的线。您指定了线上点的另外两个组分。
    *   **坐标**: `Macro` = 60%，`Meso` = 40%，因此 `Micro` = 0%。(检查: 60 + 40 + 0 = 100，有效)
    *   **最终坐标**: **(60, 0, 40)**

*   **顶点3: Macro-Micro边线上的点**
    *   **描述**: "连接Macro和Micro的边线" 是 `Meso` 组分含量为 0% 的线。
    *   **坐标**: `Macro` = 60%，`Micro` = 40%，因此 `Meso` = 0%。(检查: 60 + 40 + 0 = 100，有效)
    *   **最终坐标**: **(60, 40, 0)**

#### 总结与代码实现

您所描述的这个顶部三角形区域，其三个顶点的精确坐标分别是：
*   顶点1: `(100, 0, 0)`
*   顶点2: `(60, 0, 40)`
*   顶点3: `(60, 40, 0)`

在代码中，如果 `a`, `b`, `c` 分别对应 `Macro`, `Micro`, `Meso`，则可以在 `add_polygon_regions` 函数中这样定义该区域：

```python
'Top_Triangle': {
    # 为了形成闭合路径，需要按顺序提供三个顶点，
    # 并以第一个顶点作为第四个点来“闭合”它。
    'a': [100, 60, 60, 100],  # Macro 坐标
    'b': [0,   0,  40, 0],    # Micro 坐标
    'c': [0,   40, 0,  0],    # Meso 坐标
    'color': 'rgba(255, 0, 0, 0.2)',
}
```
