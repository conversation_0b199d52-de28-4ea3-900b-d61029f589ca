"""scape.core.swift_pso.prediction_facade - SWIFT-PSO预测步骤门面

实现SWIFT-PSO预测步骤的公共接口，使用训练好的模型进行渗透率预测。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO预测步骤门面
设计原则: Facade模式、模型消费、产物生成
性能特征: GPU/CPU自适应、批量处理、内存优化

References
----------
- 《SCAPE_MS_方法说明书》§4.4 - FOSTER-NMR预测应用
- 《logwp/extras/tracking/机器学习组件开发框架》§3 - Step开发规范
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, Optional

if TYPE_CHECKING:
    from logwp.extras.tracking import RunContext
    from logwp.models.datasets.bundle import WpDataFrameBundle

import numpy as np

from logwp.infra import get_logger
from logwp.models.constants import WpDataType

from .config import SwiftPsoPredictionConfig
from .constants import SwiftPsoPredictionArtifacts
from .artifact_handler import SwiftPsoArtifactHandler
from .internal import prediction_logic
from .internal.backend_utils import create_backend_service

logger = get_logger(__name__)


def run_swift_pso_prediction_step(
    config: SwiftPsoPredictionConfig,
    ctx: RunContext,
    model_assets: Dict[str, Any],
    prediction_bundle: Any,  # WpDataFrameBundle
    t2_time: np.ndarray,
    *,
    output_curve_names: Optional[tuple[str, str, str, str]] = None,
    backend: str = 'cpu'
) -> Dict[str, Any]:
    """执行SWIFT-PSO预测步骤。

    使用训练好的FOSTER-NMR模型参数对新数据进行渗透率和孔隙度组分预测。

    Args:
        config: SWIFT-PSO预测步骤的配置对象
        ctx: 当前运行的上下文，用于追踪和产物管理
        model_assets: 包含训练好的模型参数的字典，通常从训练步骤的产物中加载
        prediction_bundle: 包含待预测数据的Bundle
        t2_time: T2时间轴数组，必须与prediction_bundle中T2谱数据的维度完全匹配。
        output_curve_names (Optional[tuple[str, str, str, str]], optional):
            输出曲线名称四元组 (渗透率, Vmicro, Vmeso, Vmacro)。
            如果提供，预测结果将作为新曲线添加回 `prediction_bundle` 中，并且产物中的
            列名将使用这些名称。如果为 `None`（默认），则不向 `bundle` 添加曲线，
            产物使用默认列名 ("K_PRED", "VMICRO_PRED", "VMESO_PRED", "VMACRO_PRED")。
        backend (str, optional): 计算后端类型，'cpu' 或 'gpu'，默认为 'cpu'。

    Returns:
        Dict[str, Any]: 包含预测状态和结果的字典：
            - "status" (str): "completed"
            - "prediction_rows" (int): 预测结果行数
            - "predicted_bundle" (WpDataFrameBundle):
                WpDataFrameBundle 对象。如果 `output_curve_names` 被提供，
                此对象将包含新增的预测曲线；否则，它与输入的 `prediction_bundle` 相同。

    Raises:
        ValueError: 输入参数无效时抛出
        WpGpuError: 请求GPU但不可用时抛出

    Artifacts:
        - swift_pso_prediction.datasets.predicted_permeability: 预测结果表格数据（CSV）。其列名由 `output_curve_names` 参数决定。

    References:
        《SCAPE_MS_方法说明书》§4.4 - FOSTER-NMR预测应用定义
    """
    logger.info(
        "开始SWIFT-PSO预测步骤",
        operation="swift_pso_prediction_step",
        run_id=ctx.run_id,
        backend=backend,
        data_rows=len(prediction_bundle.data) if hasattr(prediction_bundle, 'data') else 0
    )

    # 1. 获取步骤目录
    step_dir = ctx.get_step_dir("swift_pso_prediction")

    # 2. 确定最终的输出曲线名称，用于产物DataFrame
    final_output_names = output_curve_names or ("K_PRED", "VMICRO_PRED", "VMESO_PRED", "VMACRO_PRED")

    # 3. 执行输入验证
    _validate_prediction_inputs(model_assets, prediction_bundle, t2_time, config)

    # 4. 初始化计算后端
    backend_service = create_backend_service(backend)

    # 5. 记录预测参数
    _log_prediction_parameters(ctx, config, t2_time, final_output_names)

    # 6. 调用内部预测逻辑
    logger.info("调用内部预测逻辑", operation="swift_pso_prediction_step")

    try:
        # 将Pydantic配置转换为字典格式
        config_dict = config.model_dump()

        prediction_results_df = prediction_logic.run_prediction(
            model_assets=model_assets,
            prediction_bundle=prediction_bundle,
            t2_time=t2_time,
            config=config_dict,
            backend_service=backend_service
        )

    except Exception as e:
        logger.error(
            "预测逻辑执行失败",
            operation="swift_pso_prediction_step",
            error=str(e)
        )
        raise

    # 7. 重命名列以匹配用户指定的输出曲线名称
    prediction_results_df = _rename_output_columns(prediction_results_df, final_output_names)

    # 8. 如果用户指定了输出曲线名，则将曲线添加回Bundle
    if output_curve_names is not None:
        _add_curves_to_bundle(
            bundle=prediction_bundle,
            prediction_df=prediction_results_df,
            output_curve_names=output_curve_names
        )

    # 9. 保存和注册产物
    handler = SwiftPsoArtifactHandler()
    prediction_rows = _save_and_register_prediction_artifacts(
        ctx, step_dir, handler, prediction_results_df
    )

    # 10. 记录预测指标
    _log_prediction_metrics(ctx, prediction_results_df)

    logger.info(
        "SWIFT-PSO预测步骤完成",
        operation="swift_pso_prediction_step",
        prediction_rows=prediction_rows
    )

    # 11. 返回轻量级结果
    return {
        "status": "completed",
        "prediction_rows": prediction_rows,
        "predicted_bundle": prediction_bundle
    }


def _validate_prediction_inputs(
    model_assets: Dict[str, Any],
    prediction_bundle: Any,
    t2_time: np.ndarray,
    config: SwiftPsoPredictionConfig
) -> None:
    """验证预测输入参数。

    Args:
        model_assets: 模型资产字典
        prediction_bundle: 预测数据bundle
        t2_time: T2时间轴数组
        config: 预测配置对象

    Raises:
        ValueError: 输入参数无效时抛出
    """
    # 使用内部验证函数
    config_dict = config.model_dump()
    prediction_logic.validate_prediction_inputs(
        model_assets, prediction_bundle, t2_time, config_dict
    )


def _log_prediction_parameters(
    ctx: RunContext,
    config: SwiftPsoPredictionConfig,
    t2_time: np.ndarray,
    output_curve_names: tuple[str, str, str, str]
) -> None:
    """记录预测参数到RunContext。

    Args:
        ctx: 运行上下文
        config: 预测配置对象
        t2_time: T2时间轴数组
        output_curve_names: 输出曲线名称
    """
    # 记录配置参数
    if config.t2_range_min is not None:
        ctx.log_parameter("t2_range_min", config.t2_range_min)
    if config.t2_range_max is not None:
        ctx.log_parameter("t2_range_max", config.t2_range_max)

    # 记录T2时间轴信息
    ctx.log_parameter("t2_time_length", len(t2_time))
    ctx.log_parameter("t2_time_min", float(t2_time.min()))
    ctx.log_parameter("t2_time_max", float(t2_time.max()))

    # 记录输出曲线名称
    ctx.log_parameter("output_curve_permeability", output_curve_names[0])
    ctx.log_parameter("output_curve_vmicro", output_curve_names[1])
    ctx.log_parameter("output_curve_vmeso", output_curve_names[2])
    ctx.log_parameter("output_curve_vmacro", output_curve_names[3])


def _rename_output_columns(
    prediction_df: Any,  # pd.DataFrame
    output_curve_names: tuple[str, str, str, str]
) -> Any:  # pd.DataFrame
    """重命名预测结果的列名以匹配用户指定的输出曲线名称。

    Args:
        prediction_df: 预测结果DataFrame
        output_curve_names: 用户指定的输出曲线名称

    Returns:
        pd.DataFrame: 重命名后的DataFrame
    """
    # 定义标准列名到用户指定名称的映射
    column_mapping = {
        'permeability_pred': output_curve_names[0],
        'vmicro_pred': output_curve_names[1],
        'vmeso_pred': output_curve_names[2],
        'vmacro_pred': output_curve_names[3]
    }

    # 只重命名存在的列
    existing_mapping = {
        old_name: new_name
        for old_name, new_name in column_mapping.items()
        if old_name in prediction_df.columns
    }

    if existing_mapping:
        prediction_df = prediction_df.rename(columns=existing_mapping)
        logger.debug(
            "重命名预测结果列",
            operation="swift_pso_prediction_step",
            column_mapping=existing_mapping
        )

    return prediction_df


def _add_curves_to_bundle(
    bundle: Any,  # WpDataFrameBundle
    prediction_df: Any,  # pd.DataFrame
    output_curve_names: tuple[str, str, str, str]
) -> None:
    """将预测结果曲线添加回WpDataFrameBundle中。

    Args:
        bundle: 要更新的WpDataFrameBundle对象
        prediction_df: 包含预测结果的DataFrame
        output_curve_names: 输出曲线名称四元组

    Note:
        此函数会直接修改传入的bundle对象。
        参照旧版 `pso_predictor.py` 的实现。
    """
    if len(output_curve_names) != 4:
        raise ValueError(
            "output_curve_names必须包含4个曲线名称: (渗透率, Vmicro, Vmeso, Vmacro)"
        )

    perm_name, vmicro_name, vmeso_name, vmacro_name = output_curve_names

    # 添加渗透率曲线
    if perm_name in prediction_df.columns:
        bundle.add_1d_curve(
            curve_name=perm_name,
            curve_data=prediction_df[perm_name].to_numpy(),
            unit="mD",
            data_type=WpDataType.FLOAT,
            description="Permeability calculated by FOSTER-NMR model",
            overwrite=True
        )

    # 添加孔隙度组分曲线
    if vmicro_name in prediction_df.columns:
        bundle.add_1d_curve(
            curve_name=vmicro_name,
            curve_data=prediction_df[vmicro_name].to_numpy(),
            unit="fraction",
            data_type=WpDataType.FLOAT,
            description="Micro-porosity component from T2 spectrum",
            overwrite=True
        )
    if vmeso_name in prediction_df.columns:
        bundle.add_1d_curve(
            curve_name=vmeso_name,
            curve_data=prediction_df[vmeso_name].to_numpy(),
            unit="fraction",
            data_type=WpDataType.FLOAT,
            description="Meso-porosity component from T2 spectrum",
            overwrite=True
        )
    if vmacro_name in prediction_df.columns:
        bundle.add_1d_curve(
            curve_name=vmacro_name,
            curve_data=prediction_df[vmacro_name].to_numpy(),
            unit="fraction",
            data_type=WpDataType.FLOAT,
            description="Macro-porosity component from T2 spectrum",
            overwrite=True
        )
    logger.info(
        "预测曲线已添加至Bundle",
        operation="swift_pso_prediction_step",
        added_curves=list(output_curve_names)
    )


def _save_and_register_prediction_artifacts(
    ctx: RunContext,
    step_dir: Any,  # Path
    handler: SwiftPsoArtifactHandler,
    prediction_df: Any  # pd.DataFrame
) -> int:
    """保存和注册预测产物。

    Args:
        ctx: 运行上下文
        step_dir: 步骤目录
        handler: 产物处理器
        prediction_df: 预测结果DataFrame

    Returns:
        int: 预测结果行数
    """
    # 保存预测结果
    prediction_path = step_dir / "predicted_permeability.csv"
    handler.save_dataframe(prediction_df, prediction_path)

    ctx.register_artifact(
        artifact_path=prediction_path.relative_to(ctx.run_dir),
        artifact_name=SwiftPsoPredictionArtifacts.PREDICTED_PERMEABILITY.value
    )

    return len(prediction_df)


def _log_prediction_metrics(ctx: RunContext, prediction_df: Any) -> None:
    """记录预测指标到RunContext。

    Args:
        ctx: 运行上下文
        prediction_df: 预测结果DataFrame
    """
    metrics = {}

    # 记录预测行数
    metrics["prediction_rows"] = len(prediction_df)

    # 记录渗透率统计（如果存在）
    permeability_columns = [col for col in prediction_df.columns if 'K' in col.upper() or 'PERM' in col.upper()]
    if permeability_columns:
        perm_col = permeability_columns[0]
        perm_values = prediction_df[perm_col].dropna()
        if not perm_values.empty:
            metrics["permeability_min"] = float(perm_values.min())
            metrics["permeability_max"] = float(perm_values.max())
            metrics["permeability_mean"] = float(perm_values.mean())

    # 批量记录指标
    if metrics:
        ctx.log_metrics(metrics, step_name="swift_pso_prediction")
