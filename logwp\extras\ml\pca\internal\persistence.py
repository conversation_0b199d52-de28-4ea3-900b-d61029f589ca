"""PCA模型持久化服务。

实现PCA模型的保存和加载功能，支持多种格式（JSON、Pickle、NPZ），
提供版本兼容性和完整性验证。

Architecture
------------
层次/依赖: PCA内部服务层，模型持久化
设计原则: 多格式支持、版本兼容、完整性保证
性能特征: 高效序列化、压缩存储、快速加载
"""

from __future__ import annotations

import json
import pickle
from pathlib import Path
from typing import TYPE_CHECKING, Any

import numpy as np
import structlog

from ..constants import (
    WpPcaModelFormat,
    MODEL_FORMAT_EXTENSIONS,
    SUPPORTED_EXTENSIONS,
)
from ..exceptions import (
    WpPcaPersistenceError,
    WpPcaErrorContext,
)

if TYPE_CHECKING:
    from .model import PCAParameters

logger = structlog.get_logger(__name__)

def save_model_to_file(
    pca_model: PCAParameters,
    filepath: str | Path,
    format: str = "json"
) -> None:
    """保存PCA模型到文件。

    Architecture
    ------------
    层次/依赖: PCA模型序列化器，多格式支持
    设计原则: 格式兼容、版本管理、完整性保证
    性能特征: 高效序列化、压缩存储、错误恢复

    Args:
        pca_model: PCA模型参数
        filepath: 保存文件路径
        format: 保存格式（json/pickle/npz）

    Raises:
        WpPcaPersistenceError: 模型保存异常
    """
    try:
        filepath = Path(filepath)

        logger.info(
            "开始保存PCA模型",
            filepath=str(filepath),
            format=format,
            n_components=pca_model["n_components"],
            n_features=pca_model["n_features"]
        )

        # 1. 格式验证
        if format not in [WpPcaModelFormat.JSON, WpPcaModelFormat.PICKLE, WpPcaModelFormat.NPZ]:
            raise WpPcaPersistenceError(f"不支持的保存格式: {format}")

        # 2. 确保目录存在
        filepath.parent.mkdir(parents=True, exist_ok=True)

        # 3. 根据格式调用相应的保存函数
        if format == WpPcaModelFormat.JSON:
            save_as_json(pca_model, filepath)
        elif format == WpPcaModelFormat.PICKLE:
            save_as_pickle(pca_model, filepath)
        elif format == WpPcaModelFormat.NPZ:
            save_as_npz(pca_model, filepath)

        logger.info(
            "PCA模型保存成功",
            filepath=str(filepath),
            format=format,
            file_size=filepath.stat().st_size if filepath.exists() else 0
        )

    except Exception as e:
        error_context = WpPcaErrorContext(
            operation="save_model",
            stage="persistence",
            additional_info={"filepath": str(filepath), "format": format}
        )

        if isinstance(e, WpPcaPersistenceError):
            e.context = error_context
            raise
        else:
            raise WpPcaPersistenceError(
                f"保存PCA模型时发生错误: {str(e)}",
                context=error_context
            ) from e


def load_model_from_file(
    filepath: str | Path,
    format: str = "auto"
) -> PCAParameters:
    """从文件加载PCA模型。

    Architecture
    ------------
    层次/依赖: PCA模型反序列化器，自动格式检测
    设计原则: 自动检测、版本兼容、完整性验证
    性能特征: 快速加载、内存优化、错误恢复

    Args:
        filepath: 模型文件路径
        format: 文件格式，auto表示自动检测

    Returns:
        加载的PCA模型参数

    Raises:
        WpPcaPersistenceError: 模型加载异常
    """
    try:
        filepath = Path(filepath)

        logger.info(
            "开始加载PCA模型",
            filepath=str(filepath),
            format=format
        )

        # 1. 检查文件是否存在
        if not filepath.exists():
            raise WpPcaPersistenceError(f"模型文件不存在: {filepath}")

        # 2. 自动检测格式
        if format == "auto":
            format = detect_file_format(filepath)
            logger.debug("自动检测文件格式", detected_format=format)

        # 3. 根据格式调用相应的加载函数
        if format == WpPcaModelFormat.JSON:
            pca_model = load_from_json(filepath)
        elif format == WpPcaModelFormat.PICKLE:
            pca_model = load_from_pickle(filepath)
        elif format == WpPcaModelFormat.NPZ:
            pca_model = load_from_npz(filepath)
        else:
            raise WpPcaPersistenceError(f"不支持的文件格式: {format}")

        # 4. 验证加载的模型
        from .model import validate_pca_parameters
        validate_pca_parameters(pca_model)

        logger.info(
            "PCA模型加载成功",
            filepath=str(filepath),
            format=format,
            n_components=pca_model["n_components"],
            n_features=pca_model["n_features"]
        )

        return pca_model

    except Exception as e:
        error_context = WpPcaErrorContext(
            operation="load_model",
            stage="persistence",
            additional_info={"filepath": str(filepath), "format": format}
        )

        if isinstance(e, WpPcaPersistenceError):
            e.context = error_context
            raise
        else:
            raise WpPcaPersistenceError(
                f"加载PCA模型时发生错误: {str(e)}",
                context=error_context
            ) from e


def detect_file_format(filepath: str | Path) -> str:
    """自动检测文件格式。

    Args:
        filepath: 文件路径

    Returns:
        检测到的文件格式

    Raises:
        WpPcaPersistenceError: 格式检测失败
    """
    filepath = Path(filepath)
    extension = filepath.suffix.lower()

    # 根据文件扩展名检测格式
    if extension == ".json":
        return WpPcaModelFormat.JSON
    elif extension in [".pkl", ".pickle"]:
        return WpPcaModelFormat.PICKLE
    elif extension == ".npz":
        return WpPcaModelFormat.NPZ
    elif extension in SUPPORTED_EXTENSIONS:
        # 如果扩展名在支持列表中但不在上面的映射中，尝试内容检测
        try:
            # 尝试读取文件开头来判断格式
            with open(filepath, 'rb') as f:
                header = f.read(10)

            # JSON文件通常以 { 或 [ 开头
            if header.startswith(b'{') or header.startswith(b'['):
                return WpPcaModelFormat.JSON
            # NPZ文件有特定的魔数
            elif header.startswith(b'PK'):  # ZIP格式魔数
                return WpPcaModelFormat.NPZ
            # 其他情况假设是pickle
            else:
                return WpPcaModelFormat.PICKLE

        except Exception:
            # 如果内容检测失败，根据扩展名猜测
            return WpPcaModelFormat.JSON  # 默认JSON格式
    else:
        raise WpPcaPersistenceError(
            f"无法识别的文件格式，扩展名: {extension}，支持的格式: {SUPPORTED_EXTENSIONS}"
        )


def save_as_json(pca_model: PCAParameters, filepath: Path) -> None:
    """保存为JSON格式。

    Args:
        pca_model: PCA模型参数
        filepath: 保存路径
    """
    try:
        # 准备JSON可序列化的数据
        json_data = {
            "version": "1.0",
            "format": "scape_pca_model",
            "model_data": {
                "components": pca_model["components"].tolist(),
                "mean": pca_model["mean"].tolist(),
                "scale": pca_model["scale"].tolist(),
                "explained_variance_ratio": pca_model["explained_variance_ratio"].tolist(),
                "n_components": pca_model["n_components"],
                "n_features": pca_model["n_features"],
                "feature_names": pca_model["feature_names"],
                "pca_curve_name": pca_model["pca_curve_name"]
            },
            # 注意：curve_metadata不能直接序列化，需要特殊处理
            "metadata_info": {
                "has_curve_metadata": True,
                "note": "曲线元数据需要单独处理，JSON格式不包含完整元数据"
            }
        }

        # 确保文件扩展名正确
        if filepath.suffix.lower() != ".json":
            filepath = filepath.with_suffix(".json")

        # 写入JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

    except Exception as e:
        raise WpPcaPersistenceError(f"JSON格式保存失败: {str(e)}") from e


def load_from_json(filepath: Path) -> PCAParameters:
    """从JSON格式加载。

    Args:
        filepath: 文件路径

    Returns:
        PCA模型参数
    """
    try:
        # 读取JSON文件
        with open(filepath, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

        # 验证文件格式
        if json_data.get("format") != "scape_pca_model":
            raise WpPcaPersistenceError("不是有效的SCAPE PCA模型JSON文件")

        model_data = json_data["model_data"]

        # 重构PCA模型参数
        # 注意：curve_metadata需要特殊处理，JSON格式无法完整保存
        pca_model: PCAParameters = {
            "components": np.array(model_data["components"]),
            "mean": np.array(model_data["mean"]),
            "scale": np.array(model_data["scale"]),
            "explained_variance_ratio": np.array(model_data["explained_variance_ratio"]),
            "n_components": model_data["n_components"],
            "n_features": model_data["n_features"],
            "feature_names": model_data["feature_names"],
            "pca_curve_name": model_data["pca_curve_name"],
            "curve_metadata": None  # JSON格式无法保存完整的curve_metadata
        }

        logger.warning(
            "从JSON加载的模型缺少曲线元数据",
            filepath=str(filepath),
            note="JSON格式不支持完整的曲线元数据，某些功能可能受限"
        )

        return pca_model

    except json.JSONDecodeError as e:
        raise WpPcaPersistenceError(f"JSON文件格式错误: {str(e)}") from e
    except KeyError as e:
        raise WpPcaPersistenceError(f"JSON文件缺少必需字段: {str(e)}") from e
    except Exception as e:
        raise WpPcaPersistenceError(f"JSON格式加载失败: {str(e)}") from e


def save_as_pickle(pca_model: PCAParameters, filepath: Path) -> None:
    """保存为Pickle格式。

    Args:
        pca_model: PCA模型参数
        filepath: 保存路径
    """
    try:
        # 确保文件扩展名正确
        if filepath.suffix.lower() not in [".pkl", ".pickle"]:
            filepath = filepath.with_suffix(".pkl")

        # 添加版本信息
        pickle_data = {
            "version": "1.0",
            "format": "scape_pca_model",
            "model": pca_model
        }

        # 写入Pickle文件
        with open(filepath, 'wb') as f:
            pickle.dump(pickle_data, f, protocol=pickle.HIGHEST_PROTOCOL)

    except Exception as e:
        raise WpPcaPersistenceError(f"Pickle格式保存失败: {str(e)}") from e


def load_from_pickle(filepath: Path) -> PCAParameters:
    """从Pickle格式加载。

    Args:
        filepath: 文件路径

    Returns:
        PCA模型参数
    """
    try:
        # 读取Pickle文件
        with open(filepath, 'rb') as f:
            pickle_data = pickle.load(f)

        # 验证文件格式
        if isinstance(pickle_data, dict) and pickle_data.get("format") == "scape_pca_model":
            return pickle_data["model"]
        else:
            # 兼容旧格式：直接保存的PCAParameters
            if isinstance(pickle_data, dict) and "components" in pickle_data:
                return pickle_data
            else:
                raise WpPcaPersistenceError("不是有效的SCAPE PCA模型Pickle文件")

    except pickle.PickleError as e:
        raise WpPcaPersistenceError(f"Pickle文件格式错误: {str(e)}") from e
    except Exception as e:
        raise WpPcaPersistenceError(f"Pickle格式加载失败: {str(e)}") from e


def save_as_npz(pca_model: PCAParameters, filepath: Path) -> None:
    """保存为NPZ格式。

    Args:
        pca_model: PCA模型参数
        filepath: 保存路径
    """
    try:
        # 确保文件扩展名正确
        if filepath.suffix.lower() != ".npz":
            filepath = filepath.with_suffix(".npz")

        # 准备NPZ数据（只保存numpy数组和基本数据）
        npz_data = {
            "components": pca_model["components"],
            "mean": pca_model["mean"],
            "scale": pca_model["scale"],
            "explained_variance_ratio": pca_model["explained_variance_ratio"],
            "n_components": np.array([pca_model["n_components"]]),
            "n_features": np.array([pca_model["n_features"]]),
            "pca_curve_name": np.array([pca_model["pca_curve_name"]], dtype=object),
            "feature_names": np.array(pca_model["feature_names"], dtype=object),
            "version": np.array(["1.0"], dtype=object),
            "format": np.array(["scape_pca_model"], dtype=object)
        }

        # 写入NPZ文件
        np.savez_compressed(filepath, **npz_data)

    except Exception as e:
        raise WpPcaPersistenceError(f"NPZ格式保存失败: {str(e)}") from e


def load_from_npz(filepath: Path) -> PCAParameters:
    """从NPZ格式加载。

    Args:
        filepath: 文件路径

    Returns:
        PCA模型参数
    """
    try:
        # 读取NPZ文件
        npz_data = np.load(filepath, allow_pickle=True)

        # 验证文件格式
        if "format" in npz_data and npz_data["format"].item() != "scape_pca_model":
            raise WpPcaPersistenceError("不是有效的SCAPE PCA模型NPZ文件")

        # 重构PCA模型参数
        pca_model: PCAParameters = {
            "components": npz_data["components"],
            "mean": npz_data["mean"],
            "scale": npz_data["scale"],
            "explained_variance_ratio": npz_data["explained_variance_ratio"],
            "n_components": int(npz_data["n_components"].item()),
            "n_features": int(npz_data["n_features"].item()),
            "pca_curve_name": str(npz_data["pca_curve_name"].item()),
            "feature_names": npz_data["feature_names"].tolist(),
            "curve_metadata": None  # NPZ格式无法保存完整的curve_metadata
        }

        logger.warning(
            "从NPZ加载的模型缺少曲线元数据",
            filepath=str(filepath),
            note="NPZ格式不支持完整的曲线元数据，某些功能可能受限"
        )

        return pca_model

    except Exception as e:
        if isinstance(e, WpPcaPersistenceError):
            raise
        else:
            raise WpPcaPersistenceError(f"NPZ格式加载失败: {str(e)}") from e
