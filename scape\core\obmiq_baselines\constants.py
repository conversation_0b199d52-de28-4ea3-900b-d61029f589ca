from __future__ import annotations

from enum import Enum


class ObmiqBaselinesTrainingArtifacts(str, Enum):
    """定义OBMIQ Baselines训练步骤的所有产物逻辑名称。"""

    MODEL_ASSETS = "obmiq_baselines.models.assets_sklearn"
    TRAINING_CONFIG = "obmiq_baselines.configs.training_config"
    NESTED_CV_REPORT = "obmiq_baselines.reports.nested_cv_performance"
    FINAL_MODEL_EVALUATION_DATA = "obmiq_baselines.data_snapshots.final_model_evaluation"
    EVAL_CROSSPLOT = "obmiq_baselines.plots.eval_crossplot"
    EVAL_RESIDUALS_PLOT = "obmiq_baselines.plots.eval_residuals_plot"
    EVAL_RESIDUALS_HIST = "obmiq_baselines.plots.eval_residuals_hist"
    FEATURE_IMPORTANCE_DATA = "obmiq_baselines.data_snapshots.feature_importance"
    FEATURE_IMPORTANCE_PLOT = "obmiq_baselines.plots.feature_importance"

    # LOWO-CV 泛化能力评估产物
    LOWO_CV_PREDICTIONS_DATA = "obmiq_baselines.data_snapshots.lowo_cv_predictions"
    LOWO_CV_CROSSPLOT = "obmiq_baselines.plots.lowo_cv_crossplot"


class ObmiqBaselinesPredictionArtifacts(str, Enum):
    """定义OBMIQ Baselines预测步骤的所有产物逻辑名称。"""

    PREDICTIONS = "obmiq_baselines.datasets.predictions"


class ObmiqBaselinesPlotProfiles(str, Enum):
    """定义将在全局注册表中注册的PlotProfile模板名称。"""

    BASE = "obmiq_baselines.base"
    CROSSPLOT = "obmiq_baselines.crossplot"
    RESIDUALS_PLOT = "obmiq_baselines.residuals_plot"
    RESIDUALS_HIST = "obmiq_baselines.residuals_hist"
    FEATURE_IMPORTANCE_PLOT = "obmiq_baselines.feature_importance"
    LOWO_CV_CROSSPLOT = "obmiq_baselines.lowo_cv_crossplot"
