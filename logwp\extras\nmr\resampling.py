from __future__ import annotations

import numpy as np

from logwp.infra import get_logger
from logwp.models.exceptions import WpValidationError, WpDataError
from .exceptions import WpNmrDataError

try:
    from scipy.interpolate import interp1d
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    interp1d = None  # To satisfy linters

logger = get_logger(__name__)


def resample_t2_spectrum(
    source_spectrum: np.ndarray,
    source_t2_axis: np.ndarray,
    target_t2_axis: np.ndarray,
    method: str = 'log_linear'
) -> np.ndarray:
    """
    将T2谱序列从源T2轴重采样到目标T2轴。

    此函数是处理来自不同仪器、不同采集设置的NMR数据的关键工具，
    确保所有数据在统一的坐标系下进行比较和建模。

    Args:
        source_spectrum: 原始T2谱数据，形状为 (n_samples, n_source_bins)。
                         也可以是单一样本的一维数组 (n_source_bins,)。
        source_t2_axis: 原始T2时间轴，形状为 (n_source_bins,)。
        target_t2_axis: 目标T2时间轴，形状为 (n_target_bins,)。
        method: 插值方法。
            - 'log_linear' (默认): 在对数空间进行线性插值。这是处理T2谱
              的首选方法，因为它尊重T2分布的对数特性。
            - 'linear': 标准线性插值。
            - 'nearest': 最近邻插值。

    Returns:
        np.ndarray: 重采样后的T2谱数据，形状为 (n_samples, n_target_bins) 或 (n_target_bins,)。

    Raises:
        WpValidationError: 如果插值方法不受支持或输入维度不匹配。
        WpNmrDataError: 如果输入数据包含NaN/Inf或T2轴为非正数。
        ImportError: 如果需要'scipy'库但未安装。
    """
    if not SCIPY_AVAILABLE:
        raise ImportError("此功能需要 'scipy' 库。请运行 'pip install scipy' 进行安装。")

    # 1. 验证输入
    supported_methods = ['log_linear', 'linear', 'nearest']
    if method not in supported_methods:
        raise WpValidationError(f"不支持的插值方法: '{method}'. 支持的方法是: {supported_methods}")

    if source_t2_axis.ndim != 1 or target_t2_axis.ndim != 1:
        raise WpNmrDataError("T2轴必须是一维数组。", data_shape=f"source: {source_t2_axis.shape}, target: {target_t2_axis.shape}")

    if source_spectrum.ndim not in [1, 2]:
        raise WpNmrDataError("source_spectrum必须是一维或二维数组。", data_shape=source_spectrum.shape)

    if (source_spectrum.ndim == 1 and source_spectrum.shape[0] != source_t2_axis.shape[0]) or \
       (source_spectrum.ndim == 2 and source_spectrum.shape[1] != source_t2_axis.shape[0]):
        raise WpNmrDataError(
            "source_spectrum中的分箱数与source_t2_axis的长度不匹配。",
            data_shape=source_spectrum.shape,
            axis_shape=source_t2_axis.shape
        )

    # 1a. 优化：如果源轴和目标轴相同，直接返回源谱的副本
    if source_t2_axis.shape == target_t2_axis.shape and np.allclose(source_t2_axis, target_t2_axis):
        logger.debug(
            "源T2轴与目标T2轴相同，跳过插值。",
            operation="resample_t2_spectrum"
        )
        return source_spectrum.copy()

    # 2. 为一致性处理，将一维输入重塑为二维
    is_1d_input = source_spectrum.ndim == 1
    if is_1d_input:
        source_spectrum = source_spectrum.reshape(1, -1)

    # 3. 根据插值方法准备坐标轴
    x_source = source_t2_axis
    x_target = target_t2_axis
    interp_kind = method

    if method == 'log_linear':
        if np.any(x_source <= 0) or np.any(x_target <= 0):
            raise WpNmrDataError("对于'log_linear'插值，T2轴的值必须为正数。")
        x_source = np.log10(source_t2_axis)
        x_target = np.log10(target_t2_axis)
        interp_kind = 'linear'  # 在对数空间中，我们执行线性插值

    # 4. 创建并应用插值器
    # 插值器一次性创建，并能高效地应用于所有样本。
    # fill_value=0.0 确保超出源范围的点被设置为零，这对于T2谱是合理的默认值。
    interpolator = interp1d(
        x_source,
        source_spectrum,
        kind=interp_kind,
        axis=1,  # 沿T2分箱维度进行插值
        bounds_error=False,
        fill_value=0.0
    )

    resampled_spectrum = interpolator(x_target)

    # 5. 如果原始输入是一维的，则将结果重塑回一维
    if is_1d_input:
        resampled_spectrum = resampled_spectrum.flatten()

    logger.debug("T2谱重采样成功。", source_shape=source_spectrum.shape, target_shape=resampled_spectrum.shape, method=method)

    return resampled_spectrum
