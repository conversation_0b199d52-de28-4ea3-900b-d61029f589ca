"""scape.core.swift_pso.internal.prediction_logic - SWIFT-PSO预测核心逻辑

包含SWIFT-PSO预测的纯计算函数，使用训练好的模型进行渗透率预测。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部预测逻辑
设计原则: 纯函数、无副作用、计算专注
性能特征: GPU/CPU优化、批量处理、内存管理

遵循CCG规范：
- SC-1: 算法正确性
- SC-2: 数值稳定性
- GP-1: 自动检测回退
- PF-1: 内存控制

References
----------
- 《SCAPE_MS_方法说明书》§4.4 - FOSTER-NMR预测应用数学定义
- 迁移自 scape/core/swift_pso_backup/pso_predictor.py
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, Optional, Tuple

if TYPE_CHECKING:
    from logwp.extras.backend import BackendService
    from logwp.models.datasets.bundle import WpDataFrameBundle

import numpy as np
import pandas as pd

from logwp.infra import get_logger
from scape.core.foster_nmr.calculate_foster_nmr import calculate_foster_nmr_permeability_for_prediction

logger = get_logger(__name__)


def run_prediction(
    model_assets: Dict[str, Any],
    prediction_bundle: Any,  # WpDataFrameBundle
    t2_time: np.ndarray,
    config: Dict[str, Any],
    backend_service: BackendService
) -> pd.DataFrame:
    """执行FOSTER-NMR渗透率预测。

    这是预测步骤的核心纯计算函数，使用训练好的模型参数对新数据进行预测。

    Args:
        model_assets: 包含训练好的模型参数的字典
        prediction_bundle: 包含待预测数据的WpDataFrameBundle
        t2_time: T2时间轴数组，必须与prediction_bundle中T2谱数据维度匹配
        config: 预测配置字典，包含t2_range_min、t2_range_max等参数
        backend_service: 计算后端服务实例

    Returns:
        pd.DataFrame: 包含预测结果的DataFrame，列包括：
            - 渗透率预测值 (mD)
            - Vmicro孔隙度组分
            - Vmeso孔隙度组分
            - Vmacro孔隙度组分

    Note:
        此函数是纯计算函数，不执行任何I/O操作。

    References:
        迁移并重构自 scape/core/swift_pso_backup/pso_predictor.py::apply_foster_nmr_permeability
    """
    logger.info(
        "开始FOSTER-NMR渗透率预测",
        operation="swift_pso_prediction",
        data_rows=len(prediction_bundle.data) if hasattr(prediction_bundle, 'data') else 0
    )

    # 提取模型参数
    if "parameters" not in model_assets:
        raise ValueError("model_assets 必须包含 'parameters' 键")

    model_parameters = model_assets["parameters"]

    # 调用核心FOSTER-NMR计算函数
    try:
        permeability, vmicro, vmeso, vmacro = calculate_foster_nmr_permeability_for_prediction(
            well_data=prediction_bundle.data,
            model_assets=model_assets,
            t2_time=t2_time,
            config={"curve_to_columns_map": prediction_bundle.curve_to_columns_map},
            backend_service=backend_service,
            t2_range_min=config.get("t2_range_min"),
            t2_range_max=config.get("t2_range_max"),
            return_porosity_components=True
        )

        # 构建预测结果DataFrame
        prediction_results_df = pd.DataFrame({
            'permeability_pred': permeability,
            'vmicro_pred': vmicro,
            'vmeso_pred': vmeso,
            'vmacro_pred': vmacro
        })

        # 获取井名和深度等标识符列
        identifier_df = prediction_bundle.get_identifier_dataframe()

        # 将标识符列与预测结果合并，确保产物包含完整的上下文信息
        final_df = pd.concat([identifier_df, prediction_results_df], axis=1)

        logger.info(
            "FOSTER-NMR渗透率预测完成",
            operation="swift_pso_prediction",
            result_rows=len(final_df),
            permeability_range=(float(permeability.min()), float(permeability.max()))
        )

        return final_df

    except Exception as e:
        logger.error(
            "FOSTER-NMR渗透率预测失败",
            operation="swift_pso_prediction",
            error=str(e)
        )
        raise


def validate_prediction_inputs(
    model_assets: Dict[str, Any],
    prediction_bundle: Any,
    t2_time: np.ndarray,
    config: Dict[str, Any]
) -> None:
    """验证预测输入参数的有效性。

    Args:
        model_assets: 模型资产字典
        prediction_bundle: 预测数据bundle
        t2_time: T2时间轴数组
        config: 预测配置字典

    Raises:
        ValueError: 输入参数无效时抛出

    Note:
        此函数执行预测前的输入验证，确保数据完整性和参数有效性
    """
    # 验证模型资产
    if not isinstance(model_assets, dict):
        raise ValueError("model_assets 必须是字典类型")

    if "parameters" not in model_assets:
        raise ValueError("model_assets 必须包含 'parameters' 键")

    # 验证T2时间轴
    if not isinstance(t2_time, np.ndarray):
        raise ValueError("t2_time 必须是numpy数组")

    if t2_time.ndim != 1:
        raise ValueError("t2_time 必须是一维数组")

    if len(t2_time) == 0:
        raise ValueError("t2_time 不能为空")

    # 验证T2范围参数
    t2_range_min = config.get("t2_range_min")
    t2_range_max = config.get("t2_range_max")

    if t2_range_min is not None and t2_range_max is not None:
        if t2_range_min >= t2_range_max:
            raise ValueError("t2_range_min 必须小于 t2_range_max")

    # 验证预测数据bundle
    if not hasattr(prediction_bundle, 'data'):
        raise ValueError("prediction_bundle 必须具有 'data' 属性")

    if len(prediction_bundle.data) == 0:
        raise ValueError("prediction_bundle 不能为空")

    logger.debug(
        "预测输入验证通过",
        operation="swift_pso_prediction",
        model_params_count=len(model_assets["parameters"]),
        t2_time_length=len(t2_time),
        data_rows=len(prediction_bundle.data)
    )
