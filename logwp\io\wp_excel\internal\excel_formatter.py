"""Excel表单格式化服务。

提供统一的Excel表单格式化功能，包括字体、颜色、边框、冻结窗格等。
支持不同类型表单的专门格式化需求。

Architecture
------------
层次/依赖: I/O层内部服务，被各个writer模块调用
设计原则: 统一格式化、可配置性、性能优化
性能特征: 批量格式化、样式复用、内存优化

Functions
---------
- apply_default_formatting: 应用默认格式化
- apply_table_borders: 应用表格边框
- apply_background_colors: 应用背景颜色
- auto_adjust_column_widths: 自动调整列宽
- freeze_panes: 冻结窗格

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》§5 - Excel格式化实现
- openpyxl文档 - Excel格式化API
"""

from __future__ import annotations

from typing import Sequence

import openpyxl
import structlog
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter

from ..config import ExcelFormattingConfig

logger = structlog.get_logger(__name__)

__all__ = [
    "apply_default_formatting",
    "apply_table_borders",
    "apply_background_colors",
    "auto_adjust_column_widths",
    "freeze_panes",
    "format_cell_range",
    "create_default_font",
    "create_header_fill",
    "create_structure_fill",
    "create_thin_border",
]


def apply_default_formatting(
    worksheet: openpyxl.Worksheet,
    config: ExcelFormattingConfig
) -> None:
    """应用默认格式化。

    为整个工作表设置默认字体和基本格式。

    Args:
        worksheet: Excel工作表对象
        config: 格式化配置
    """
    if not config.enable_formatting:
        return

    default_font = create_default_font(config)

    # 为所有有数据的单元格设置默认字体
    for row in worksheet.iter_rows():
        for cell in row:
            if cell.value is not None:
                cell.font = default_font

    logger.debug("默认格式化应用完成", worksheet_name=worksheet.title)


def apply_table_borders(
    worksheet: openpyxl.Worksheet,
    start_row: int,
    start_col: int,
    end_row: int,
    end_col: int,
    config: ExcelFormattingConfig
) -> None:
    """应用表格边框。

    为指定范围的单元格添加表格边框。

    Args:
        worksheet: Excel工作表对象
        start_row: 起始行（1-based）
        start_col: 起始列（1-based）
        end_row: 结束行（1-based）
        end_col: 结束列（1-based）
        config: 格式化配置
    """
    if not config.enable_borders:
        return

    thin_border = create_thin_border()

    for row in range(start_row, end_row + 1):
        for col in range(start_col, end_col + 1):
            cell = worksheet.cell(row=row, column=col)
            cell.border = thin_border

    logger.debug(
        "表格边框应用完成",
        worksheet_name=worksheet.title,
        range=f"{start_row}:{start_col}-{end_row}:{end_col}"
    )


def apply_background_colors(
    worksheet: openpyxl.Worksheet,
    ranges: list[tuple[int, int, int, int, str]],
    config: ExcelFormattingConfig
) -> None:
    """应用背景颜色。

    为指定范围的单元格设置背景颜色。

    Args:
        worksheet: Excel工作表对象
        ranges: 范围列表，每个元素为(start_row, start_col, end_row, end_col, color_type)
        config: 格式化配置
    """
    if not config.enable_formatting:
        return

    for start_row, start_col, end_row, end_col, color_type in ranges:
        if color_type == "header":
            fill = create_header_fill(config)
        elif color_type == "structure":
            fill = create_structure_fill(config)
        else:
            continue

        for row in range(start_row, end_row + 1):
            for col in range(start_col, end_col + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.fill = fill

    logger.debug(
        "背景颜色应用完成",
        worksheet_name=worksheet.title,
        range_count=len(ranges)
    )


def auto_adjust_column_widths(
    worksheet: openpyxl.Worksheet,
    column_widths: dict[int, float] | None = None,
    config: ExcelFormattingConfig | None = None
) -> None:
    """自动调整列宽。

    根据内容自动调整列宽，或使用指定的列宽设置。

    Args:
        worksheet: Excel工作表对象
        column_widths: 指定的列宽字典 {列号: 宽度}
        config: 格式化配置
    """
    if config and not config.enable_auto_width:
        return

    if column_widths:
        # 使用指定的列宽
        for col_num, width in column_widths.items():
            column_letter = get_column_letter(col_num)
            worksheet.column_dimensions[column_letter].width = width
    else:
        # 自动计算列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter

            for cell in column:
                try:
                    if cell.value is not None:
                        cell_length = len(str(cell.value))
                        if cell_length > max_length:
                            max_length = cell_length
                except:
                    pass

            # 设置列宽，限制最大宽度
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = max(adjusted_width, 8)

    logger.debug("列宽调整完成", worksheet_name=worksheet.title)


def freeze_panes(
    worksheet: openpyxl.Worksheet,
    freeze_row: int,
    freeze_col: int,
    config: ExcelFormattingConfig
) -> None:
    """冻结窗格。

    Args:
        worksheet: Excel工作表对象
        freeze_row: 冻结行数
        freeze_col: 冻结列数
        config: 格式化配置
    """
    if not config.enable_freeze_panes:
        return

    # 计算冻结位置
    freeze_cell = f"{get_column_letter(freeze_col + 1)}{freeze_row + 1}"
    worksheet.freeze_panes = freeze_cell

    logger.debug(
        "窗格冻结完成",
        worksheet_name=worksheet.title,
        freeze_cell=freeze_cell
    )


def format_cell_range(
    worksheet: openpyxl.Worksheet,
    start_row: int,
    start_col: int,
    end_row: int,
    end_col: int,
    font: Font | None = None,
    fill: PatternFill | None = None,
    border: Border | None = None,
    alignment: Alignment | None = None,
    number_format: str | None = None
) -> None:
    """格式化单元格范围。

    为指定范围的单元格应用格式化样式。

    Args:
        worksheet: Excel工作表对象
        start_row: 起始行（1-based）
        start_col: 起始列（1-based）
        end_row: 结束行（1-based）
        end_col: 结束列（1-based）
        font: 字体样式
        fill: 填充样式
        border: 边框样式
        alignment: 对齐样式
        number_format: 数字格式
    """
    for row in range(start_row, end_row + 1):
        for col in range(start_col, end_col + 1):
            cell = worksheet.cell(row=row, column=col)

            if font:
                cell.font = font
            if fill:
                cell.fill = fill
            if border:
                cell.border = border
            if alignment:
                cell.alignment = alignment
            if number_format:
                cell.number_format = number_format


def create_default_font(config: ExcelFormattingConfig) -> Font:
    """创建默认字体样式。

    Args:
        config: 格式化配置

    Returns:
        Font: 默认字体对象
    """
    return Font(
        name=config.default_font_name,
        size=config.default_font_size
    )


def create_header_fill(config: ExcelFormattingConfig) -> PatternFill:
    """创建标题背景填充样式。

    Args:
        config: 格式化配置

    Returns:
        PatternFill: 标题背景填充对象
    """
    return PatternFill(
        start_color=config.header_bg_color,
        end_color=config.header_bg_color,
        fill_type="solid"
    )


def create_structure_fill(config: ExcelFormattingConfig) -> PatternFill:
    """创建结构指示区背景填充样式。

    Args:
        config: 格式化配置

    Returns:
        PatternFill: 结构指示区背景填充对象
    """
    return PatternFill(
        start_color=config.structure_bg_color,
        end_color=config.structure_bg_color,
        fill_type="solid"
    )


def create_thin_border() -> Border:
    """创建细边框样式。

    Returns:
        Border: 细边框对象
    """
    return Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
