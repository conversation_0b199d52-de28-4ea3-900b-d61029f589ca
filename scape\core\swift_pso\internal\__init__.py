"""scape.core.swift_pso.internal - SWIFT-PSO内部实现逻辑

包含所有核心算法的纯计算函数，无副作用，只负责计算并返回结果。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO内部实现层
设计原则: 纯函数、无副作用、计算专注
性能特征: GPU/CPU优化、内存管理、数值稳定性

遵循CCG规范：
- SC-1: 算法正确性
- SC-2: 数值稳定性
- GP-1: 自动检测回退
- PF-1: 内存控制

Package Structure
-----------------
- pso_optimizer.py: PSO优化核心逻辑
- prediction_logic.py: 预测计算逻辑
- tsne_computer.py: t-SNE降维计算
- backend_utils.py: 计算后端工具

References
----------
- 《SCAPE_MS_方法说明书》§4 - SWIFT-PSO算法数学定义
- 《logwp/extras/tracking/机器学习组件开发框架》§2 - 内部实现规范
"""

# 内部模块，不导出任何公共API
__all__ = []
