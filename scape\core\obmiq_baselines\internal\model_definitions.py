from __future__ import annotations

from typing import Any, Dict

from scipy.stats import loguniform, randint, uniform
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from xgboost import XGBRegressor

from ..config import ObmiqBaselinesTrainingConfig

# 尝试导入cuml，如果失败则优雅地回退
try:
    from cuml.ensemble import RandomForestRegressor as cuMLRandomForestRegressor
    CUML_AVAILABLE = True
except ImportError:
    CUML_AVAILABLE = False



def get_candidate_models_and_param_spaces(
    config: ObmiqBaselinesTrainingConfig,
) -> Dict[str, Dict[str, Any]]:
    """
    定义所有候选模型及其超参数搜索空间。

    此函数集中管理了所有将在嵌套交叉验证中被评估的scikit-learn兼容
    模型。每个模型都关联了一个用于RandomizedSearchCV的参数分布字典。

    Args:
        config: 训练配置对象，用于获取随机种子和设备信息。

    Returns:
        一个字典，键是模型名称，值是包含 'estimator' 和 'param_space' 的字典。
    """
    models = {
        "XGBoost": {
            # 根据配置决定XGBoost的计算设备。
            # 当在CPU上运行时，必须设置n_jobs=1以避免嵌套并行。
            "estimator": XGBRegressor(
                random_state=config.random_seed,
                device=config.device,
                # Force sequential execution within the RandomizedSearchCV worker
                n_jobs=1 if config.device == "cpu" else None,
            ),
            "param_space": {
                # RFECV (selector) and Model (model) parameters are searched together
                "selector__min_features_to_select": randint(5, 15),
                "model__n_estimators": randint(100, 500),
                "model__max_depth": randint(3, 10),
                "model__learning_rate": loguniform(0.01, 0.3),
                "model__subsample": uniform(0.6, 0.4),  # [0.6, 1.0)
                "model__colsample_bytree": uniform(0.6, 0.4),
                "model__gamma": uniform(0, 0.5),
            },
        },
        "RandomForest": {
            # 根据设备和cuml可用性，条件性地选择RandomForest的实现
            "estimator": (
                cuMLRandomForestRegressor(
                    random_state=config.random_seed,
                    n_streams=1 # 推荐在scikit-learn的并行框架下设为1
                )
                if config.device == "cuda" and CUML_AVAILABLE
                else RandomForestRegressor(
                    random_state=config.random_seed,
                    # Critical Fix: Set n_jobs=1 to prevent nested parallelism.
                    n_jobs=1,
                )
            ),
            "param_space": {
                "selector__min_features_to_select": randint(5, 15),
                "model__n_estimators": randint(100, 500),
                "model__max_depth": randint(5, 20),
                "model__min_samples_split": randint(2, 10),
                "model__min_samples_leaf": randint(1, 10),
                "model__max_features": ["sqrt", "log2", 1.0],
            },
        },
        "SVR": {
            "estimator": SVR(),
            "param_space": {
                "selector__min_features_to_select": randint(5, 15),
                "model__kernel": ["rbf", "linear"],
                # C和gamma通常跨越多个数量级，使用对数均匀分布进行搜索更高效
                "model__C": loguniform(1e-1, 1e3),
                "model__gamma": loguniform(1e-4, 1e-1),
            },
        },
    }
    return models
