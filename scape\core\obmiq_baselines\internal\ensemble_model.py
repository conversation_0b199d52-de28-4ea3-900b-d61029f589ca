from __future__ import annotations

from pathlib import Path
from typing import Any

import joblib
import numpy as np
from sklearn.base import BaseEstimator


class WeightedEnsembleModel(BaseEstimator):
    """
    一个自定义的加权融合模型，用于组合两个scikit-learn兼容的模型。

    该类封装了两个基础模型及其对应的权重，通过加权平均它们的预测
    结果来生成最终预测。它还提供了标准的save/load方法，以便于
    作为产物进行持久化。
    """

    def __init__(self, model_1: Any, model_2: Any, weight_1: float, weight_2: float):
        """
        初始化加权融合模型。

        Args:
            model_1: 第一个训练好的模型（或Pipeline）。
            model_2: 第二个训练好的模型（或Pipeline）。
            weight_1: 第一个模型的权重。
            weight_2: 第二个模型的权重。
        """
        self.model_1 = model_1
        self.model_2 = model_2
        self.weight_1 = weight_1
        self.weight_2 = weight_2

    def predict(self, X: np.ndarray | pd.DataFrame) -> np.ndarray:
        """使用加权平均法进行预测。"""
        pred_1 = self.model_1.predict(X)
        pred_2 = self.model_2.predict(X)
        return (pred_1 * self.weight_1) + (pred_2 * self.weight_2)

    def save(self, filepath: str | Path):
        """将整个融合模型对象保存到单个文件。"""
        joblib.dump(self, filepath)

    @staticmethod
    def load(filepath: str | Path) -> "WeightedEnsembleModel":
        """从文件中加载融合模型。"""
        return joblib.load(filepath)
