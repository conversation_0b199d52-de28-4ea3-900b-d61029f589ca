"""logwp.extras.petroplot.nmr_ternary.constants - NMR三元图常量

定义NMR三元图组件使用的所有常量，包括产物逻辑名、绘图模板名，
以及预设的岩石物理分区和颜色方案。

Architecture
------------
层次/依赖: petroplot/nmr_ternary常量层，被组件内其他模块引用
设计原则: 集中管理、类型安全、易于维护
"""
from enum import Enum


class NmrTernaryPlotArtifacts(str, Enum):
    """定义三元图步骤的所有产物逻辑名称。"""
    # 使用前缀模式，确保在Workflow中多次调用时名称唯一。
    PLOT_PREFIX = "petroplot_nmr_ternary.plots.ternary_plot"
    DATA_SNAPSHOT = "petroplot_nmr_ternary.data_snapshots.main_plot_data"
    LOGIC_CONFIG = "petroplot_nmr_ternary.configs.logic_config"
    PLOT_PROFILE = "petroplot_nmr_ternary.configs.plot_profile"


class NmrTernaryPlotProfiles(str, Enum):
    """定义将在全局注册表中注册的PlotProfile模板名称。"""
    BASE = "petroplot.nmr_ternary.base"
    DEFAULT = "petroplot.nmr_ternary.default"


NMR_TERNARY_REGION_DEFINITIONS = {
    'Macro': [(100, 0, 0), (60, 0, 40), (60, 40, 0)],
    'Macro-Micro': [(60, 40, 0), (60, 25, 15), (25, 25, 50), (25, 50, 25), (50, 50, 0)],
    'Macro-Meso': [(60, 25, 15), (60, 0, 40), (25, 0, 75), (25, 25, 50)],
    'Meso': [(25, 25, 50), (25, 0, 75), (0, 0, 100), (0, 25, 75)],
    'Meso-Micro': [(25, 50, 25), (25, 25, 50), (0, 25, 75), (0, 50, 50)],
    'Micro': [(0, 100, 0), (25, 75, 0), (0, 75, 25)],
    'Micro-Meso': [(25, 75, 0), (25, 50, 25), (0, 50, 50), (0, 75, 25)],
    'Micro-Macro': [(50, 50, 0), (25, 50, 25), (25, 75, 0)]
}
"""预设的NMR三元图岩石物理分区定义。"""

NMR_TERNARY_REGION_COLOR_SCHEMES = {
    'Geolog': {
        'Macro': '#ffff00', 'Macro-Micro': '#cca344', 'Macro-Meso': '#ffaf0e',
        'Meso': '#ff6b15', 'Meso-Micro': '#e84400', 'Micro': '#56004f',
        'Micro-Meso': '#a00068', 'Micro-Macro': '#c6c600'
    },
    'CNLC': {
        'Macro': '#ff0000', 'Macro-Micro': '#c00000', 'Macro-Meso': '#ffff00',
        'Meso': '#00ff00', 'Meso-Micro': '#92d050', 'Micro': '#3365ff',
        'Micro-Meso': '#00b0f0', 'Micro-Macro': '#ff00ff'
    }
}
"""预设的NMR三元图岩石物理分区颜色方案。"""
