from __future__ import annotations

"""logwp.models.datasets.base - 数据集抽象基类

WpDepthIndexedDatabaseBase作为所有数据集的抽象基类，定义核心接口和共同行为。

Architecture
------------
层次/依赖: datasets层抽象基类，依赖types、constants、exceptions
设计原则: 抽象基类模式、模板方法、类型安全、WFS规范兼容
性能特征: 内存优化、延迟验证、类型检查、异常处理

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- CT-2: 使用枚举常量

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§3.2.1 - WpDepthIndexedDatabaseBase设计
- 《SCAPE_WFS_WP文件规范.md》A.3 - 数据集类型定义
"""
from abc import ABC, abstractmethod
from typing import Any, Optional, TYPE_CHECKING
from dataclasses import dataclass, field
from datetime import datetime
import numpy as np
import pandas as pd

from logwp.models.utils import CaseInsensitiveDict

if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpArrayBundle, WpDataFrameBundle

from logwp.models.constants import (
    WpDsType, WpDsIndexType, WpStatisticsKeys, WpDepthRole
)
from logwp.models.exceptions import (
    WpDataError,
    WpCurveMetadataError,
    ErrorContext
)
from logwp.models.types import (
    WpDataDict, WpWellName, WpIdentifier, WpDatasetName
)
from logwp.models.curve.metadata import CurveMetadata, CurveBasicAttributes
from logwp.models.base import WpProjectComponent

from logwp.infra import get_logger

logger = get_logger(__name__)


@dataclass
class WpIndexedDatasetBase(WpProjectComponent, ABC):
    """数据集索引抽象基类。

    定义所有索引数据集类型的核心接口，支持不同索引类型（深度、时间等）。

    Architecture
    ------------
    层次/依赖: datasets层索引抽象基类，被所有索引数据集继承
    设计原则: 抽象基类模式、索引类型抽象、类型安全
    性能特征: 轻量级抽象、最小接口

    Attributes:
        desc: 数据集描述信息，可选

    Examples:
        >>> # 抽象基类不能直接实例化
        >>> # dataset = WpIndexedDatasetBase()  # 会抛出TypeError
        >>>
        >>> # 通过具体子类使用
        >>> class MyIndexedDataset(WpIndexedDatasetBase):
        ...     def get_index_type(self):
        ...         return WpDsIndexType.DEPTH
        >>>
        >>> dataset = MyIndexedDataset(desc="测试数据集")
        >>> assert dataset.get_index_type() == WpDsIndexType.DEPTH

    References:
        《SCAPE_DDS_详细设计_logwp.md》§3.2.1 - 数据集索引抽象设计
    """

    desc: str = ""  # 数据集描述信息，可选

    @abstractmethod
    def get_index_type(self) -> WpDsIndexType:
        """获取数据集索引类型。

        Returns:
            WpDsIndexType: 数据集索引类型

        Note:
            子类必须实现此方法，返回对应的索引类型
        """
        pass


@dataclass
class WpDepthIndexedDatasetBase(WpIndexedDatasetBase, ABC):
    """深度索引数据集抽象基类。

    定义所有深度索引数据集类型的核心接口和共同行为。

    Architecture
    ------------
    层次/依赖: datasets层深度索引抽象基类，被所有深度索引数据集继承
    设计原则: 抽象基类模式、模板方法、类型安全
    性能特征: 内存优化、延迟验证、类型检查

    Attributes:
        name: 数据集名称
        df: 数据集DataFrame（直接暴露，用户可直接操作）
        curve_metadata: 曲线基本属性管理器（直接暴露，用户可直接操作）
        depth_sampling_rate: 静态深度采样间隔（WFS规范要求，可读写）
        created_at: 创建时间
        modified_at: 修改时间
        metadata: 元数据字典
        _validated: 验证状态缓存

    DataFrame索引规范（CDP-1核心设计原则）：
    ==========================================

    🚨 **最高优先级规范** - 严格遵循《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》CDP-1至CDP-4

    **强制要求**：
    - **默认整数索引**：df属性必须使用默认整数索引（pd.RangeIndex）
    - **严格禁止**：使用深度列、时间列或任何业务列作为DataFrame索引
    - **深度存储**：深度信息必须作为普通列存储，使用标准化列名
    - **验证方法**：`isinstance(df.index, pd.RangeIndex)`

    **适用范围**：
    - 所有数据集类型（连续型、离散型、区间型）
    - 所有处理阶段（IO层构造、业务层处理、算法层计算）
    - 所有转换操作（离散转连续、区间转连续等）

    **技术原因**：
    - **多场景支持**：支持分析、机器学习、可视化等多种使用场景
    - **性能优化**：避免索引对齐带来的性能损失和意外行为
    - **内存效率**：整数索引占用内存最小，访问速度最快
    - **操作简化**：避免复杂的索引操作，降低出错概率
    - **兼容性**：与pandas生态系统的最佳实践保持一致

    **用户自主索引策略**：
    - **灵活创建**：用户可根据需要创建自定义索引（深度索引、多级索引等）
    - **便捷方法**：提供create_depth_index()等便捷方法支持索引创建
    - **临时使用**：鼓励在特定操作中临时创建索引，操作完成后恢复
    - **不强制**：系统不强制任何特定的索引结构

    **违反规范的后果**：
    - **数据对齐错误**：可能导致数据错位和计算错误
    - **性能下降**：索引对齐操作消耗额外计算资源
    - **维护困难**：增加代码复杂度和调试难度
    - **兼容性问题**：与其他pandas操作产生冲突

    Design Notes:
        - df 和 curve_metadata 属性直接暴露，用户可直接操作
        - WpDepthIndexedDatabaseBase 本质上是曲线元数据和数据体的合体
        - 提供同步维护方法（如 remove_curve）确保元数据和数据体一致性
        - 如需单独操作 df 或 curve_metadata，用户需自行保证一致性
        - 建议优先使用同步维护方法以确保数据一致性

    Examples:
        >>> # 抽象基类不能直接实例化
        >>> # dataset = WpDepthIndexedDatabaseBase()  # 会抛出TypeError
        >>>
        >>> # 通过具体子类使用
        >>> class MyDataset(WpDepthIndexedDatabaseBase):
        ...     @property
        ...     def dataset_type(self):
        ...         return WpDsType.CONTINUOUS
        ...     def _validate_specific(self):
        ...         return True
        >>>
        >>> # ✅ 正确：使用默认整数索引的DataFrame
        >>> correct_df = pd.DataFrame({
        ...     "depth": [2500.0, 2501.0, 2502.0],
        ...     "GR": [45.2, 46.1, 44.8]
        ... })
        >>> assert isinstance(correct_df.index, pd.RangeIndex)  # 验证索引类型
        >>> dataset = MyDataset(name="test", df=correct_df)  # 创建成功
        >>>
        >>> # ❌ 错误：使用深度索引的DataFrame（会抛出异常）
        >>> wrong_df = pd.DataFrame({
        ...     "GR": [45.2, 46.1, 44.8]
        ... }, index=[2500.0, 2501.0, 2502.0])  # 深度作为索引
        >>> # dataset = MyDataset(name="test", df=wrong_df)  # 会抛出WpDataError
        >>>
        >>> # 直接操作数据体
        >>> print(dataset.df.shape)  # 直接访问DataFrame
        >>> dataset.df["NEW_CURVE"] = [1, 2, 3]  # 直接添加列
        >>>
        >>> # 直接操作曲线元数据
        >>> curve_attrs = dataset.curve_metadata.get_curve("GR")
        >>> dataset.curve_metadata.add_curve(new_curve_attrs)
        >>> curves = dataset.curve_metadata.list_curves(category=WpCurveCategory.LOGGING)
        >>>
        >>> # 使用同步维护方法（推荐）
        >>> dataset.remove_curve("GR")  # 同时删除元数据和数据体
        >>>
        >>> # 用户自主索引策略（临时使用）
        >>> temp_depth_indexed = dataset.df.set_index("depth")  # 临时创建深度索引
        >>> # ... 进行特定操作 ...
        >>> # 操作完成后恢复默认索引（如果需要）

    References:
        《SCAPE_DDS_详细设计_logwp.md》§3.2.1 - WpDepthIndexedDatabaseBase抽象基类设计
    """

    name: WpIdentifier = field(default_factory=lambda: WpIdentifier("unnamed_dataset"))  # 默认名称，避免dataclass顺序问题
    df: pd.DataFrame = field(default_factory=pd.DataFrame)  # 允许空DataFrame（支持io层渐进式构造）
    curve_metadata: CurveMetadata = field(default_factory=CurveMetadata)
    depth_sampling_rate: float = field(default=0.0)  # 静态采样间隔，WFS规范要求
    created_at: datetime = field(default_factory=datetime.now)
    modified_at: datetime = field(default_factory=datetime.now)
    metadata: dict[str, Any] = field(default_factory=dict)
    _validated: bool | None = field(default=None, init=False)

    def __post_init__(self) -> None:
        """初始化后处理，支持CIIA架构。"""
        # 如果name是字符串，转换为WpIdentifier（向后兼容）
        if isinstance(self.name, str):
            object.__setattr__(self, 'name', WpIdentifier(self.name))

        # 验证基本参数
        if not self.name:
            raise WpDataError(
                "数据集名称不能为空",
                context=ErrorContext(
                    operation="dataset_initialization",
                    additional_info={"name": str(self.name)}
                )
            )

        # 验证DataFrame索引规范（CDP-1核心设计原则）
        if not self.df.empty and not isinstance(self.df.index, pd.RangeIndex):
            raise WpDataError(
                "DataFrame必须使用默认整数索引（RangeIndex），严禁使用深度列或业务列作为索引",
                context=ErrorContext(
                    operation="dataset_initialization",
                    dataset_name=str(self.name),
                    additional_info={
                        "current_index_type": type(self.df.index).__name__,
                        "current_index": str(self.df.index),
                        "requirement": "pd.RangeIndex",
                        "reference": "《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》CDP-1"
                    }
                )
            )

        if self.df is None or self.df.empty:
            logger.warning(
                "创建了空数据集",
                operation="dataset_initialization",
                dataset_name=str(self.name),
                dataset_type=self.dataset_type.value,
                dataframe_shape=(0, 0) if self.df is None else self.df.shape
            )
        else:
            logger.debug(
                "DataFrame索引验证通过",
                operation="dataset_initialization",
                dataset_name=str(self.name),
                index_type=type(self.df.index).__name__,
                dataframe_shape=self.df.shape
            )

    @property
    @abstractmethod
    def dataset_type(self) -> WpDsType:
        """数据集类型（抽象属性）。

        Returns:
            WpDsType: 数据集类型枚举
        """
        ...

    @abstractmethod
    def clone_dataset(self) -> WpDepthIndexedDatasetBase:
        """克隆数据集，返回新的数据集实例。

        创建当前数据集的深拷贝，包括：
        - 数据体（DataFrame）的完整拷贝
        - 曲线元数据的完整拷贝
        - 所有属性的拷贝（除时间戳外）

        Returns:
            WpDepthIndexedDatabaseBase: 克隆后的新数据集实例

        Note:
            - 克隆后的数据集具有新的时间戳（created_at, modified_at）
            - 数据体和元数据完全独立，修改不会相互影响
            - 子类必须实现具体的克隆逻辑

        Examples:
            >>> original = dataset
            >>> cloned = dataset.clone_dataset()
            >>> assert cloned.name == original.name
            >>> assert cloned.df.equals(original.df)
            >>> assert cloned is not original  # 不同的对象
            >>> assert cloned.df is not original.df  # 不同的DataFrame
        """
        ...

    def get_index_type(self) -> WpDsIndexType:
        """获取数据集索引类型。

        Returns:
            WpDsIndexType: 深度索引类型

        Note:
            深度索引数据集固定返回DEPTH类型
        """
        return WpDsIndexType.DEPTH

    def is_continuous_dataset(self) -> bool:
        """判断是否为连续型数据集。

        Returns:
            bool: 是否为连续型数据集

        Examples:
            >>> continuous_ds = WpContinuousDataset(name="test", df=df)
            >>> assert continuous_ds.is_continuous_dataset()
            >>>
            >>> discrete_ds = WpDiscreteDataset(name="test", df=df)
            >>> assert not discrete_ds.is_continuous_dataset()
        """
        return self.dataset_type == WpDsType.CONTINUOUS

    def is_discrete_dataset(self) -> bool:
        """判断是否为离散型数据集。

        Returns:
            bool: 是否为离散型数据集

        Examples:
            >>> discrete_ds = WpDiscreteDataset(name="test", df=df)
            >>> assert discrete_ds.is_discrete_dataset()
            >>>
            >>> continuous_ds = WpContinuousDataset(name="test", df=df)
            >>> assert not continuous_ds.is_discrete_dataset()
        """
        return self.dataset_type == WpDsType.POINT

    def is_interval_dataset(self) -> bool:
        """判断是否为区间型数据集。

        Returns:
            bool: 是否为区间型数据集

        Examples:
            >>> interval_ds = WpIntervalDataset(name="test", df=df)
            >>> assert interval_ds.is_interval_dataset()
            >>>
            >>> continuous_ds = WpContinuousDataset(name="test", df=df)
            >>> assert not continuous_ds.is_interval_dataset()
        """
        return self.dataset_type == WpDsType.INTERVAL

    def get_dataset_type_name(self) -> str:
        """获取数据集类型名称字符串。

        Returns:
            str: 数据集类型名称

        Examples:
            >>> continuous_ds = WpContinuousDataset(name="test", df=df)
            >>> assert continuous_ds.get_dataset_type_name() == "Continuous"
            >>>
            >>> discrete_ds = WpDiscreteDataset(name="test", df=df)
            >>> assert discrete_ds.get_dataset_type_name() == "Point"
            >>>
            >>> interval_ds = WpIntervalDataset(name="test", df=df)
            >>> assert interval_ds.get_dataset_type_name() == "Interval"
        """
        return self.dataset_type.value

    @classmethod
    @abstractmethod
    def create_with_data(
        cls,
        name: WpDatasetName | WpIdentifier,
        df: pd.DataFrame,
        curve_metadata: CurveMetadata,
        *,
        depth_sampling_rate: float = 0
    ) -> WpDepthIndexedDatasetBase:
        """创建数据集并附加数据。

        工厂方法，创建新的数据集实例并同时附加DataFrame和曲线元数据。
        这是对create_empty + direct_attach_curves_data组合操作的便捷封装。

        Args:
            name: 数据集名称
            df: DataFrame数据体
            curve_metadata: 曲线元数据
            depth_sampling_rate: 深度采样间隔，默认为0（仅WpContinuousDataset使用）

        Returns:
            WpDepthIndexedDatabaseBase: 创建的数据集实例

        Note:
            - 子类必须实现具体的创建逻辑
            - 内部调用direct_attach_curves_data方法
            - 确保数据集类型和元数据的一致性

        Examples:
            >>> df = pd.DataFrame({'WELL': ['C-1'], 'MD': [2500.0], 'GR': [45.2]})
            >>> metadata = CurveMetadata()
            >>> # ... 配置metadata ...
            >>> dataset = WpContinuousDataset.create_with_data("test_logs", df, metadata)
            >>> assert dataset.name == "test_logs"
            >>> assert len(dataset.df) == 1

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.3 - 工厂方法模式
        """
        ...

    # ------------------------------------------------------------
    # 深度参考曲线便捷服务方法（抽象接口）
    # ------------------------------------------------------------

    @abstractmethod
    def get_depth_reference_count(self) -> int:
        """获取数据集深度参考曲线的条数。

        Returns:
            int: 深度参考曲线数量

        Note:
            - Continuous/Discrete数据集：返回1（单一深度索引）
            - Interval数据集：返回2（顶界和底界深度）

        Examples:
            >>> # 连续型数据集
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> assert continuous_dataset.get_depth_reference_count() == 1
            >>>
            >>> # 区间型数据集
            >>> interval_dataset = WpIntervalDataset(...)
            >>> assert interval_dataset.get_depth_reference_count() == 2
        """

    @abstractmethod
    def get_single_depth_reference_curve(self) -> CurveBasicAttributes:
        """获取单个深度曲线的CurveBasicAttributes。

        Returns:
            CurveBasicAttributes: 深度参考曲线的基本属性

        Raises:
            WpDataError: 当数据集类型不支持单一深度索引时抛出

        Note:
            - 适用于Continuous/Discrete数据集
            - Interval数据集调用此方法会抛出异常

        Examples:
            >>> # 连续型数据集
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> depth_curve = continuous_dataset.get_single_depth_reference_curve()
            >>> assert depth_curve.depth_role == WpDepthRole.SINGLE
        """

    @abstractmethod
    def get_interval_depth_reference_curves(self) -> tuple[CurveBasicAttributes, CurveBasicAttributes]:
        """获取区间深度参考曲线的CurveBasicAttributes。

        Returns:
            tuple[CurveBasicAttributes, CurveBasicAttributes]: (顶界深度曲线, 底界深度曲线)

        Raises:
            WpDataError: 当数据集类型不支持区间深度索引时抛出

        Note:
            - 仅适用于Interval数据集
            - Continuous/Discrete数据集调用此方法会抛出异常
            - 返回顺序：第一个为TOP角色，第二个为BOTTOM角色

        Examples:
            >>> # 区间型数据集
            >>> interval_dataset = WpIntervalDataset(...)
            >>> top_curve, bottom_curve = interval_dataset.get_interval_depth_reference_curves()
            >>> assert top_curve.depth_role == WpDepthRole.TOP
            >>> assert bottom_curve.depth_role == WpDepthRole.BOTTOM
        """

    @abstractmethod
    def get_wells_depth_ranges(self) -> dict[str, tuple[float, float]]:
        """获取每口井的深度范围。

        Returns:
            dict[str, tuple[float, float]]: {井名: (最小深度, 最大深度)}

        Note:
            - 返回数据集中每口井的深度范围
            - 空数据集返回空字典
            - 单井数据集返回包含一个井的字典
            - 多井数据集返回包含所有井的字典

        Examples:
            >>> # 多井数据集
            >>> dataset = WpContinuousDataset(...)
            >>> wells_ranges = dataset.get_wells_depth_ranges()
            >>> # {'C-1': (2500.0, 2600.0), 'C-2': (2550.0, 2650.0)}
            >>>
            >>> # 单井数据集
            >>> single_well_dataset = WpDiscreteDataset(...)
            >>> wells_ranges = single_well_dataset.get_wells_depth_ranges()
            >>> # {'Well-1': (2400.0, 2500.0)}
        """

    @abstractmethod
    def get_depth_range_for_well(self, well_name: str) -> tuple[float, float]:
        """获取指定井的深度范围。

        Args:
            well_name: 井名

        Returns:
            tuple[float, float]: (最小深度, 最大深度)

        Raises:
            WpDataError: 当指定井不存在时抛出

        Note:
            - 返回指定井的深度范围
            - 如果井名不存在，抛出WpDataError异常
            - 如果井存在但没有有效深度数据，返回(0.0, 0.0)

        Examples:
            >>> dataset = WpContinuousDataset(...)
            >>> min_depth, max_depth = dataset.get_depth_range_for_well("C-1")
            >>> assert min_depth <= max_depth
        """



    @abstractmethod
    def get_depth_reference_unit(self) -> str | None:
        """获取深度参考曲线的单位。

        Returns:
            str | None: 深度曲线的单位，如果没有单位则返回None

        Note:
            - Continuous/Discrete数据集：返回单一深度曲线的单位
            - Interval数据集：返回顶界深度曲线的单位（WFS规范保证顶界和底界单位一致）

        Examples:
            >>> # 连续型数据集
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> unit = continuous_dataset.get_depth_reference_unit()
            >>> assert unit == "m"  # 或 "ft" 等
            >>>
            >>> # 区间型数据集
            >>> interval_dataset = WpIntervalDataset(...)
            >>> unit = interval_dataset.get_depth_reference_unit()
            >>> assert unit == "m"  # 顶界和底界单位一致
        """

    def get_well_identifier_curve(self) -> CurveBasicAttributes:
        """获取井名标识曲线的CurveBasicAttributes。

        Returns:
            CurveBasicAttributes: 井名标识曲线的基本属性

        Raises:
            WpDataError: 当没有找到井名标识曲线时抛出

        Note:
            - 利用CurveMetadata.get_well_identifier_curves()方法
            - WFS规范保证返回列表的第一个元素是井名标识曲线
            - 所有数据集类型都有井名标识曲线，因此可以在基类中统一实现

        Examples:
            >>> # 任何数据集类型
            >>> dataset = WpContinuousDataset(...)  # 或其他数据集类型
            >>> well_curve = dataset.get_well_identifier_curve()
            >>> assert well_curve.is_well_identifier == True
            >>> assert well_curve.name in ["WELL", "WELL_NAME", "WELLNAME"]
        """
        well_curve_names = self.curve_metadata.get_well_identifier_curves()

        if not well_curve_names:
            raise WpDataError(
                "数据集缺少井名标识曲线",
                context=ErrorContext(
                    operation="get_well_identifier_curve",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "available_curves": list(self.curve_metadata.curves.keys()),
                        "requirement": "all_datasets_must_have_well_identifier"
                    }
                )
            )

        # WFS规范保证第一个是井名标识曲线
        well_curve_name = well_curve_names[0]
        return self.curve_metadata.get_curve(well_curve_name)

    def get_wells(self) -> list[str]:
        """获取数据集中的所有井名列表。

        Returns:
            list[str]: 井名列表，按出现顺序排序

        Raises:
            WpDataError: 当没有找到井名标识曲线时抛出

        Note:
            - 利用井名标识曲线从DataFrame中提取唯一井名
            - 返回的井名列表按在数据中的出现顺序排序
            - 如果数据集为空，返回空列表

        Examples:
            >>> dataset = WpContinuousDataset(...)
            >>> wells = dataset.get_wells()
            >>> print(f"数据集包含 {len(wells)} 口井: {wells}")
            >>> # ['Well-A', 'Well-B', 'Well-C']
        """
        try:
            # 获取井名标识曲线
            well_curve = self.get_well_identifier_curve()
            well_column = well_curve.dataframe_column_name

            # 检查DataFrame中是否存在井名列
            if well_column not in self.df.columns:
                raise WpDataError(
                    f"DataFrame中未找到井名列: {well_column}",
                    context=ErrorContext(
                        operation="get_wells",
                        dataset_name=str(self.name),
                        additional_info={
                            "well_column": well_column,
                            "available_columns": list(self.df.columns),
                            "dataframe_shape": self.df.shape
                        }
                    )
                )

            # 如果DataFrame为空，返回空列表
            if len(self.df) == 0:
                return []

            # 提取唯一井名，保持出现顺序
            wells = self.df[well_column].dropna().unique().tolist()

            # 转换为字符串列表（确保类型一致性）
            return [str(well) for well in wells]

        except WpDataError:
            # 重新抛出WpDataError
            raise
        except Exception as e:
            raise WpDataError(
                f"获取井名列表失败: {str(e)}",
                context=ErrorContext(
                    operation="get_wells",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "dataframe_shape": self.df.shape,
                        "error_type": type(e).__name__
                    }
                )
            ) from e

    def convert_depth_reference_unit(self, target_unit: str) -> None:
        """转换数据集深度参考曲线的单位。

        Args:
            target_unit: 目标深度单位（"m" 或 "ft"）

        Raises:
            WpValidationError: 当目标单位不支持时抛出
            WpDataError: 当数据集不支持深度转换时抛出

        Note:
            - 就地修改DataFrame中的深度列数据
            - 同步更新曲线元数据中的单位信息
            - 同步更新静态采样间隔depth_sampling_rate
            - 具体实现委托给service层

        Examples:
            >>> # 转换为英尺
            >>> dataset.convert_depth_reference_unit("ft")
            >>> assert dataset.get_depth_reference_unit() == "ft"

            >>> # 转换为米
            >>> dataset.convert_depth_reference_unit("m")
            >>> assert dataset.get_depth_reference_unit() == "m"

        References:
            《SCAPE_DDS_详细设计_logwp.md》§4.2 - 深度单位管理系统
        """
        from logwp.models.datasets.internal.depth_unit_conversion import convert_dataset_depth_unit
        convert_dataset_depth_unit(self, target_unit)

    @abstractmethod
    def calculate_depth_sampling_rate(self) -> float:
        """动态计算深度采样间隔。

        Returns:
            float: 计算得到的深度采样间隔

        Note:
            - Continuous数据集：计算并返回最常见的深度间隔（众数）
            - Discrete数据集：计算并返回最小深度间隔
            - Interval数据集：不适用，抛出异常
            - IO层会保证静态属性depth_sampling_rate与此方法的一致性

        Examples:
            >>> # 连续型数据集
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> calculated_rate = continuous_dataset.calculate_depth_sampling_rate()
            >>> assert calculated_rate == continuous_dataset.depth_sampling_rate  # IO层保证一致性
            >>>
            >>> # 离散型数据集
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> min_interval = discrete_dataset.calculate_depth_sampling_rate()
            >>> assert min_interval > 0  # 最小深度间隔
        """

    def check_uniform_depth_sampling(
        self,
        *,
        algorithm: str = "mad",
        tolerance: float = 1e-6
    ) -> tuple[bool, float | None]:
        """检查深度采样是否为等间隔。

        使用指定算法检查深度列的采样间隔是否一致。支持标准差和MAD两种算法，
        MAD算法对极端值更稳健，是默认推荐方法。

        Args:
            algorithm: 检查算法，支持"std_dev"（标准差）和"mad"（中位数绝对偏差），默认为"mad"
            tolerance: 容差阈值，用于判断是否为等间隔，默认1e-6

        Returns:
            tuple[bool, float | None]: (是否等间隔, 采样间隔)
                - 如果是等间隔，返回(True, 采样间隔值)
                - 如果不是等间隔，返回(False, None)

        Raises:
            WpDataError: 当数据集不支持单一深度参考或检查失败时抛出
            ValueError: 参数无效

        Note:
            - 仅适用于有单一深度参考曲线的数据集（Continuous/Discrete）
            - Interval数据集有两个深度参考曲线，不适用此方法
            - 标准差方法：计算深度差异的标准差，标准差为零表示等间隔
            - MAD方法：计算深度差异的中位数绝对偏差，MAD接近零表示等间隔
            - MAD方法对极端值更稳健，推荐使用

        Examples:
            >>> # 连续型数据集检查
            >>> continuous_dataset = WpContinuousDataset(...)
            >>> is_uniform, interval = continuous_dataset.check_uniform_depth_sampling()
            >>> if is_uniform:
            ...     print(f"等间隔采样，间隔为 {interval} {dataset.get_depth_reference_unit()}")
            ... else:
            ...     print("非等间隔采样")

            >>> # 使用标准差算法
            >>> is_uniform, interval = dataset.check_uniform_depth_sampling(
            ...     algorithm="std_dev", tolerance=1e-3
            ... )

            >>> # 离散型数据集检查
            >>> discrete_dataset = WpDiscreteDataset(...)
            >>> is_uniform, interval = discrete_dataset.check_uniform_depth_sampling()

        References:
            《SCAPE_DDS_详细设计_logwp.md》§6.3 - 深度采样间隔检查
        """
        # 检查是否支持单一深度参考
        if self.get_depth_reference_count() != 1:
            raise WpDataError(
                "深度采样间隔检查仅适用于有单一深度参考曲线的数据集",
                context=ErrorContext(
                    operation="check_uniform_depth_sampling",
                    dataset_name=str(self.name),
                    additional_info={
                        "dataset_type": self.dataset_type.value,
                        "depth_reference_count": self.get_depth_reference_count(),
                        "requirement": "single_depth_reference_only"
                    }
                )
            )

        # 获取深度参考曲线
        depth_curve = self.get_single_depth_reference_curve()
        depth_column_name = depth_curve.dataframe_column_name

        # 获取深度数据列
        if depth_column_name not in self.df.columns:
            raise WpDataError(
                f"深度列 '{depth_column_name}' 在DataFrame中不存在",
                context=ErrorContext(
                    operation="check_uniform_depth_sampling",
                    dataset_name=str(self.name),
                    additional_info={
                        "depth_column_name": depth_column_name,
                        "available_columns": list(self.df.columns)
                    }
                )
            )

        depth_column = self.df[depth_column_name]

        # 转发到服务层
        from logwp.models.datasets.internal import check_uniform_depth_sampling, DepthSamplingAlgorithm

        # 转换算法字符串为枚举
        try:
            if algorithm == "std_dev":
                algo_enum = DepthSamplingAlgorithm.STD_DEV
            elif algorithm == "mad":
                algo_enum = DepthSamplingAlgorithm.MAD
            else:
                raise ValueError(f"不支持的算法: {algorithm}，支持的算法: 'std_dev', 'mad'")
        except Exception as e:
            raise ValueError(f"算法参数无效: {str(e)}") from e

        return check_uniform_depth_sampling(
            depth_column=depth_column,
            depth_curve=depth_curve,
            algorithm=algo_enum,
            tolerance=tolerance
        )

    def to_dict(self) -> WpDataDict:
        """转换为字典格式。

        Returns:
            WpDataDict: 数据集信息字典，使用枚举常量作为键名

        Examples:
            >>> data_dict = dataset.to_dict()
            >>> assert data_dict["name"] == "OBMIQ_logs"
        """
        wells = self.get_wells()
        memory_usage = self.df.memory_usage(deep=True).sum()

        return {
            WpStatisticsKeys.NAME.value: str(self.name),
            WpStatisticsKeys.TYPE.value: self.dataset_type,
            WpStatisticsKeys.ROW_COUNT.value: len(self.df),
            WpStatisticsKeys.COLUMN_COUNT.value: len(self.df.columns),
            WpStatisticsKeys.WELLS.value: wells,
            WpStatisticsKeys.MEMORY_MB.value: round(memory_usage, 2),
            WpStatisticsKeys.CREATED_AT.value: self.created_at,
            WpStatisticsKeys.MODIFIED_AT.value: self.modified_at,
            WpStatisticsKeys.VALIDATION_STATUS.value: self.validate()
        }

    # ==================== 数据访问说明 ====================
    # 注意：df 和 curve_metadata 属性直接暴露，用户可直接操作
    #
    # 数据体操作：
    #   dataset.df.shape                    # 查看数据形状
    #   dataset.df["GR"]                    # 访问曲线数据
    #   dataset.df["NEW_CURVE"] = [1,2,3]   # 添加新列
    #
    # 曲线元数据操作：
    #   dataset.curve_metadata.get_curve("GR")
    #   dataset.curve_metadata.add_curve(curve_attrs)
    #   dataset.curve_metadata.list_curves(**filters)
    #
    # 同步维护方法（推荐）：
    #   dataset.remove_curve("GR")  # 同时删除元数据和数据体


    def __repr__(self) -> str:
        """字符串表示。

        Returns:
            str: 数据集的字符串表示，包含名称、类型和形状信息

        Examples:
            >>> dataset = WpContinuousDataset(name="test", df=df)
            >>> repr(dataset)
            'WpContinuousDataset(name='test', type=Continuous, shape=(100, 5))'
        """
        return (f"{self.__class__.__name__}("
                f"name='{self.name}', "
                f"type={self.dataset_type.value}, "
                f"shape={self.df.shape})")

    def __eq__(self, other: object) -> bool:
        """相等性比较。

        Args:
            other: 要比较的对象

        Returns:
            bool: 如果两个数据集相等则返回True

        Note:
            比较基于数据集名称、类型和DataFrame内容的完全相等性。

        Examples:
            >>> dataset1 = WpContinuousDataset(name="test", df=df)
            >>> dataset2 = WpContinuousDataset(name="test", df=df.copy())
            >>> assert dataset1 == dataset2
        """
        if not isinstance(other, WpDepthIndexedDatasetBase):
            return False

        return (self.name == other.name and
                self.dataset_type == other.dataset_type and
                self.df.equals(other.df))

    @classmethod
    def create_empty(cls, name: str) -> WpDepthIndexedDatasetBase:
        """创建空数据集（io层调用）。

        支持io层的渐进式构造需求，创建只有名称的空数据集实例。
        DataFrame和CurveMetadata都为空，后续通过set_curves_data方法设置。

        Args:
            name: 数据集名称

        Returns:
            WpDepthIndexedDatabaseBase: 空数据集实例

        Examples:
            >>> # io层使用示例
            >>> dataset = WpContinuousDataset.create_empty("OBMIQ_logs")
            >>> assert dataset.df.empty
            >>> assert len(dataset.curve_metadata.curves) == 0

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.2 - 创建空数据集
        """
        return cls(name=WpIdentifier(name))

    def direct_attach_curves_data(
        self,
        df: pd.DataFrame,
        curve_metadata: CurveMetadata
    ) -> None:
        """设置曲线数据和元数据（io层调用，两者必须同时设置）。

        注意：这是直接数据体和曲线元数据，调用者必须保证数据集类型和元数据的一致性。

        这是io层渐进式构造的关键方法，确保曲线元数据与DataFrame的原子性设置。
        验证DataFrame列名与曲线友好名称的一致性后同时设置，避免不一致状态。

        重要：DataFrame的列名必须使用CurveMetadata中的友好名称，而不是原始曲线名称。
        - 一维曲线：使用curve.dataframe_column_name
        - 二维组合曲线：使用curve.dataframe_element_names中的所有名称

        Args:
            df: 完整的DataFrame数据（列名必须是友好名称）
            curve_metadata: 完整的曲线元数据

        Raises:
            WpDataError: DataFrame列名与曲线元数据的友好名称不一致

        Examples:
            >>> # io层使用示例（正确的友好名称）
            >>> dataset = WpContinuousDataset.create_empty("OBMIQ_logs")
            >>>
            >>> # 创建曲线元数据
            >>> metadata = CurveMetadata()
            >>> md_attrs = CurveBasicAttributes.create_1d_curve(name="MD", unit="m")
            >>> gr_attrs = CurveBasicAttributes.create_1d_curve(name="GR", unit="API")
            >>> t2_attrs = CurveBasicAttributes.create_2d_composite_curve(name="T2_VALUE", element_count=2, unit="ms")
            >>> metadata.add_curve(md_attrs)
            >>> metadata.add_curve(gr_attrs)
            >>> metadata.add_curve(t2_attrs)
            >>>
            >>> # 使用友好名称创建DataFrame
            >>> df = pd.DataFrame({
            ...     "MD": [1, 2],           # 一维曲线友好名称
            ...     "GR": [45, 50],         # 一维曲线友好名称
            ...     "T2_VALUE_1": [10, 12], # 二维组合曲线元素友好名称
            ...     "T2_VALUE_2": [15, 18]  # 二维组合曲线元素友好名称
            ... })
            >>> dataset.attach_curves_data(df, metadata)  # 验证通过

        References:
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§3.4.4 - 原子性设置曲线数据
            《SCAPE_DDS_logwp_io层的渐进式生成流程.md》§4.3 - DataFrame友好名称原则
        """
        # 验证DataFrame索引规范（CDP-1核心设计原则）
        if not isinstance(df.index, pd.RangeIndex):
            raise WpDataError(
                "DataFrame必须使用默认整数索引（RangeIndex），严禁使用深度列或业务列作为索引",
                context=ErrorContext(
                    operation="direct_attach_curves_data",
                    dataset_name=str(self.name),
                    additional_info={
                        "current_index_type": type(df.index).__name__,
                        "current_index": str(df.index),
                        "requirement": "pd.RangeIndex",
                        "reference": "《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》CDP-1"
                    }
                )
            )

        # 验证曲线一致性
        from .internal import curve_operations
        curve_operations.validate_curves_consistency(df, curve_metadata)

        # 同时设置（原子性操作）
        object.__setattr__(self, 'df', df)
        object.__setattr__(self, 'curve_metadata', curve_metadata)
        object.__setattr__(self, 'modified_at', datetime.now())

        # 清除验证缓存
        object.__setattr__(self, '_validated', None)

        logger.debug(
            "DataFrame索引验证通过，数据附加成功",
            operation="direct_attach_curves_data",
            dataset_name=str(self.name),
            index_type=type(df.index).__name__,
            dataframe_shape=df.shape,
            curve_count=curve_metadata.get_curve_count()
        )



    def remove_curve(self, curve_name: str) -> None:
        """删除曲线。

        支持三种删除模式：
        1. 删除一维曲线：删除对应的DataFrame列和CurveBasicAttributes
        2. 删除整个二维组合曲线：删除所有相关DataFrame列和CurveBasicAttributes
        3. 删除二维组合曲线元素：删除对应DataFrame列，拆分二维组合曲线为一维曲线

        Args:
            curve_name: 曲线名称或二维组合曲线元素名称

        Raises:
            WpDataError: 当曲线不存在或删除失败时

        Examples:
            >>> # 删除一维曲线
            >>> dataset.remove_curve("GR")
            >>>
            >>> # 删除整个二维组合曲线
            >>> dataset.remove_curve("T2_VALUE")
            >>>
            >>> # 删除二维组合曲线元素
            >>> dataset.remove_curve("T2_VALUE[2]")
        """
        # 转发给service的就地修改函数
        from logwp.models.datasets.internal import curve_operations
        curve_operations.remove_curve_inplace(self.df, self.curve_metadata, curve_name)

        # 更新时间戳
        object.__setattr__(self, 'modified_at', datetime.now())

    def validate_numeric_data(self, curve_names: list[str] | None = None) -> dict[str, Any]:
        """验证数值型数据的质量。

        Args:
            curve_names: 要验证的曲线名称列表，None表示验证所有数值型曲线

        Returns:
            dict[str, Any]: 验证结果，包含NaN统计、异常值检测等

        Examples:
            >>> dataset = WpContinuousDataset(name="test", df=df)
            >>> validation_result = dataset.validate_numeric_data(["GR", "PHIT"])
            >>> print(validation_result["nan_counts"])  # {'GR': 5, 'PHIT': 2}
            >>> print(validation_result["data_types"])  # {'GR': 'float64', 'PHIT': 'float64'}
        """
        if curve_names is None:
            # 获取所有数值型曲线
            curve_names = self.curve_metadata.get_numeric_curves()

        # 获取对应的DataFrame列名
        try:
            column_names = self.curve_metadata.get_dataframe_columns_for_curves(curve_names)
        except WpCurveMetadataError:
            # 如果有曲线不存在，过滤掉不存在的曲线
            valid_curves = []
            for curve_name in curve_names:
                if self.curve_metadata.has_curve(curve_name):
                    valid_curves.append(curve_name)
            column_names = self.curve_metadata.get_dataframe_columns_for_curves(valid_curves)
            curve_names = valid_curves

        validation_result = {
            "total_curves": len(curve_names),
            "total_rows": len(self.df),
            "nan_counts": {},
            "data_types": {},
            "numeric_summary": {},
            "validation_passed": True,
            "issues": []
        }

        for curve_name, column_name in zip(curve_names, column_names):
            if column_name not in self.df.columns:
                validation_result["issues"].append(f"列 '{column_name}' 不存在于DataFrame中")
                validation_result["validation_passed"] = False
                continue

            series = self.df[column_name]

            # NaN统计
            nan_count = series.isna().sum()
            validation_result["nan_counts"][curve_name] = int(nan_count)

            # 数据类型
            validation_result["data_types"][curve_name] = str(series.dtype)

            # 数值型检查
            if not pd.api.types.is_numeric_dtype(series):
                validation_result["issues"].append(f"曲线 '{curve_name}' 不是数值型: {series.dtype}")
                validation_result["validation_passed"] = False
                continue

            # 数值统计（排除NaN）
            if nan_count < len(series):
                numeric_series = series.dropna()
                validation_result["numeric_summary"][curve_name] = {
                    "count": int(len(numeric_series)),
                    "mean": float(numeric_series.mean()),
                    "std": float(numeric_series.std()),
                    "min": float(numeric_series.min()),
                    "max": float(numeric_series.max()),
                    "has_infinite": bool(np.isinf(numeric_series).any()),
                    "completeness": float(len(numeric_series) / len(series))
                }

                # 检查无穷值
                if np.isinf(numeric_series).any():
                    validation_result["issues"].append(f"曲线 '{curve_name}' 包含无穷值")
                    validation_result["validation_passed"] = False

        return validation_result

    def get_data_completeness(self) -> dict[str, float]:
        """获取数据完整性统计。

        Returns:
            dict[str, float]: 每个曲线的数据完整性比例（0.0-1.0）

        Examples:
            >>> dataset = WpContinuousDataset(name="test", df=df)
            >>> completeness = dataset.get_data_completeness()
            >>> print(completeness)  # {'GR': 0.95, 'PHIT': 0.88, 'PERM': 0.92}
        """
        if self.df.empty:
            return {}

        completeness = {}
        total_rows = len(self.df)

        for column_name in self.df.columns:
            non_null_count = self.df[column_name].notna().sum()
            completeness[column_name] = float(non_null_count / total_rows)

        return completeness

    def get_curve_statistic(self, curve_name: str, statistic: str) -> float:
        """
        高效地获取单条一维曲线的特定统计值。

        这是一个轻量级的统计方法，作为 `generate_summary()` 的替代方案，
        用于在需要获取单个统计值时避免不必要的计算开销。

        Args:
            curve_name (str): 要计算的曲线的逻辑名称。
            statistic (str): 要计算的统计量。支持的值包括:
                'mean', 'median', 'std', 'min', 'max',
                'p25', 'p50', 'p75', 'p90'。

        Returns:
            float: 计算出的统计值。

        Raises:
            WpCurveMetadataError: 如果指定的曲线不存在。
            WpDataError: 如果曲线不是一维数值型，或者没有有效数据。
            ValueError: 如果指定的统计量不受支持。

        Examples:
            >>> # 获取 PHIT 曲线的中位数
            >>> median_phit = dataset.get_curve_statistic("PHIT_NMR", "median")
            >>>
            >>> # 获取 T2_P50 曲线的75分位数
            >>> p75_t2p50 = dataset.get_curve_statistic("T2_P50", "p75")
        """
        logger.debug(
            "开始获取曲线统计值",
            operation="get_curve_statistic",
            dataset_name=str(self.name),
            curve_name=curve_name,
            statistic=statistic
        )

        # 1. 获取曲线元数据和列名
        curve_attrs = self.curve_metadata.get_curve(curve_name)
        if not curve_attrs:
            raise WpCurveMetadataError(f"曲线 '{curve_name}' 在元数据中不存在。")

        if not curve_attrs.is_1d_curve():
            raise WpDataError(f"统计计算仅支持一维曲线，但 '{curve_name}' 是二维曲线。")

        column_name = curve_attrs.dataframe_column_name
        if column_name not in self.df.columns:
            raise WpDataError(f"DataFrame中缺少曲线 '{curve_name}' 对应的列 '{column_name}'。")

        # 3. 提取数据并验证
        series = self.df[column_name]

        # 4. 调用核心服务函数进行计算
        # 局部导入避免循环依赖
        from logwp.models.datasets.internal.statistic_calculator import calculate_series_statistic
        return calculate_series_statistic(
            series=series,
            statistic=statistic,
            curve_name=curve_name,
            dataset_name=str(self.name)
        )

    def check_na(
        self,
        curve_names: Optional[list[str]] = None,
        *,
        check_how: str = "any"
    ) -> pd.Series:
        """
        检查指定曲线中是否存在NaN（空值）。

        此方法返回一个布尔型的Series，其索引与数据集的DataFrame一致，
        标记出满足NaN条件的行。

        Args:
            curve_names (Optional[list[str]], optional): 要检查的曲线名称列表。
                如果为None或空列表，则检查所有数据曲线（不包括井名、深度等系统曲线）。
                默认为None。
            check_how (str, optional): 检查NaN的策略。默认为 "any"。
                - "any": 如果某行在指定曲线列表中任意一条曲线上包含NaN，则该行标记为True。
                - "all": 只有当某行在指定曲线列表中的所有曲线上都为NaN时，该行才标记为True。

        Returns:
            pd.Series: 一个布尔型的Series，True表示该行满足NaN检查条件。

        Raises:
            ValueError: 如果 `check_how` 参数不是 "any" 或 "all"。
            WpCurveMetadataError: 如果 `curve_names` 中包含不存在的曲线。

        Examples:
            >>> # 检查GR或PHIT中任意一个是否存在NaN
            >>> nan_mask_any = dataset.check_na(curve_names=['GR', 'PHIT'], check_how='any')
            >>> rows_with_any_nan = dataset.df[nan_mask_any]

            >>> # 检查GR和PHIT是否都为NaN
            >>> nan_mask_all = dataset.check_na(curve_names=['GR', 'PHIT'], check_how='all')
            >>> rows_with_all_nan = dataset.df[nan_mask_all]

            >>> # 检查所有数据曲线中是否存在NaN
            >>> nan_mask_all_curves = dataset.check_na()
        """
        # 1. 验证参数
        if check_how not in ("any", "all"):
            raise ValueError(f"参数 'check_how' 必须是 'any' 或 'all'，但收到了 '{check_how}'。")

        if self.df.empty:
            return pd.Series(dtype=bool)

        # 2. 确定要检查的曲线
        curves_to_check = curve_names or self.curve_metadata.get_data_curves()

        if not curves_to_check:
            return pd.Series([False] * len(self.df), index=self.df.index)

        # 3. 获取DataFrame列名并执行检查
        from logwp.models.curve import CurveExpansionMode
        df_columns = self.curve_metadata.expand_curve_names(curves_to_check, CurveExpansionMode.DATAFRAME)

        existing_columns = [col for col in df_columns if col in self.df.columns]
        if not existing_columns:
            return pd.Series([False] * len(self.df), index=self.df.index)

        if check_how == "any":
            return self.df[existing_columns].isna().any(axis=1)
        else:  # check_how == "all"
            return self.df[existing_columns].isna().all(axis=1)

    def generate_summary(self) -> dict[str, Any]:
        """生成数据集概况。

        生成包含基本信息、元数据信息、DataFrame信息、按井统计和曲线统计的完整概况。

        Returns:
            dict[str, Any]: 数据集概况数据
                - basic_info: 基本信息
                - metadata_info: 元数据信息
                - dataframe_info: DataFrame信息
                - well_statistics: 按井统计
                - curve_statistics: 曲线统计

        Examples:
            >>> dataset = WpContinuousDataset(name="test", df=test_df)
            >>> summary = dataset.generate_summary()
            >>> print(f"数据集类型: {summary['basic_info']['type']}")
            >>> print(f"总行数: {summary['basic_info']['total_rows']}")
            >>> print(f"曲线数: {summary['basic_info']['curve_count']}")

        References:
            《SCAPE_DDS_logwp_generate_summary.md》§4.5 - 数据集概况服务设计
        """
        from logwp.models.datasets.internal.dataset_summary import generate_dataset_summary
        return generate_dataset_summary(self)

    def extract_curve_array_bundle(
        self,
        curve_names: list[str],
        *,
        include_system_columns: bool = False,
        validate_existence: bool = True,
        query_condition: Optional[str] = None,
        rename_map: Optional[CaseInsensitiveDict[str, str]] = None
    ) -> 'WpArrayBundle':
        """轻量级曲线NumPy数组数据包提取。

        提供算法友好的数据提取接口，返回包含numpy数组和便捷元数据访问的Bundle对象，
        避免创建新数据集对象的开销。专为各种算法和分析工具设计。

        Args:
            curve_names: 要提取的曲线名称列表
                例如: ["PHIT", "GR", "T2_VALUE"]
            include_system_columns: 是否包含系统列（井名+深度），默认False
            validate_existence: 是否验证曲线存在性，默认True
            query_condition: 可选的查询条件，用于数据过滤
            rename_map: 可选的曲线重命名映射表, 格式为 {'原始曲线名': '新曲线名'}。
        Returns:
            WpArrayBundle: 包含numpy数组数据和便捷元数据访问的Bundle对象
            - data属性：曲线名到numpy数组的映射（大小写不敏感）
            - 一维曲线返回形状为(n_depths,)的数组
            - 二维组合曲线返回形状为(n_depths, n_bins)的数组
            - 自动计算井名、深度曲线的便捷映射

        Raises:
            WpDataError: 数据集操作失败
            WpCurveMetadataError: 曲线不存在或名称无效

        Examples:
            >>> # 基本使用：提取一维曲线
            >>> bundle = dataset.extract_curve_array_bundle([
            ...     "PHIT", "GR", "RHOB"
            ... ])
            >>> assert bundle.data["PHIT"].shape == (n_depths,)  # 一维数组
            >>> assert bundle.data["GR"].ndim == 1

            >>> # 提取二维组合曲线
            >>> bundle = dataset.extract_curve_array_bundle([
            ...     "T2_VALUE", "RES_ARRAY"
            ... ])
            >>> assert bundle.data["T2_VALUE"].shape == (n_depths, n_bins)  # 二维数组

            >>> # 包含系统列
            >>> bundle = dataset.extract_curve_array_bundle([
            ...     "PHIT"
            ... ], include_system_columns=True)
            >>> assert "well_name" in bundle.data
            >>> assert "depth" in bundle.data

            >>> # 便捷属性访问
            >>> well_names = bundle.get_well_names()  # np.ndarray
            >>> depths = bundle.get_depths()  # np.ndarray (单深度情况)

            >>> # 机器学习辅助
            >>> X, y = bundle.to_sklearn_format(target_curve="PERM")

            >>> # 使用查询条件过滤
            >>> bundle = dataset.extract_curve_array_bundle([
            ...     "PHIT"
            ... ], query_condition="PHIT > 0.1")

        Performance Benefits:
            - 相比extract_curves避免了数据集对象创建开销
            - 直接返回numpy数组，适合数值计算
            - 重用经过优化的曲线提取核心逻辑
            - 支持大规模数据的高效处理

        Design Notes:
            与extract_curve_dataframe_bundle形成互补：
            - extract_curve_dataframe_bundle: 返回DataFrame，适合数据预处理
            - extract_curve_array_bundle: 返回numpy数组，适合算法计算

            保持业务无关性：不包含任何特定算法的逻辑（如T2轴验证等），
            这些业务逻辑应该在算法层实现。

        References:
            《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
            《SCAPE_CCG_编码与通用规范.md》§FAP-2 - 四层架构分离
        """
        from logwp.models.datasets.internal.curve_extraction import extract_curve_array_bundle

        return extract_curve_array_bundle(
            dataset=self,
            curve_names=curve_names,
            include_system_columns=include_system_columns,
            validate_existence=validate_existence,
            query_condition=query_condition,
            rename_map=rename_map
        )


    def extract_curve_dataframe_bundle(
        self,
        curve_names: list[str] | None = None,
        *,
        include_system_columns: bool = False,
        validate_existence: bool = True,
        query_condition: Optional[str] = None,
        rename_map: Optional[CaseInsensitiveDict[str, str]] = None
    ) -> 'WpDataFrameBundle':
        """轻量级曲线DataFrame数据包提取。

        提供中间层的数据提取接口，返回包含DataFrame和便捷元数据访问的Bundle对象，
        适用于需要进一步处理DataFrame的场景。

        Args:
            curve_names: 要提取的曲线名称列表（原始曲线名称）。
                如果为 `None`，则提取所有数据曲线（即排除井名、深度等系统曲线）。
                例如: `["PHIT", "GR", "T2_VALUE"]`
            include_system_columns: 是否包含系统列（井名+深度），默认False
            validate_existence: 是否验证曲线存在性，默认True
            query_condition: 可选的查询条件，用于数据过滤
            rename_map: 可选的曲线重命名映射表, 格式为 {'旧曲线名': '新曲线名'}。

        Returns:
            WpDataFrameBundle: 包含DataFrame数据和便捷元数据访问的Bundle对象
            - data属性：提取的数据DataFrame
            - curve_to_columns_map属性：曲线名到DataFrame列名的映射
            - 自动计算井名、深度曲线的便捷映射

        Raises:
            WpDataError: 数据集操作失败
            WpCurveMetadataError: 曲线不存在或名称无效

        Examples:
            >>> # 基本使用：提取DataFrame bundle
            >>> bundle = dataset.extract_curve_dataframe_bundle([
            ...     "PHIT", "GR", "T2_VALUE"
            ... ])
            >>> print(bundle.curve_to_columns_map)  # {"PHIT": ["PHIT"], "T2_VALUE": ["T2_VALUE_1", "T2_VALUE_2", ...]}
            >>> assert isinstance(bundle.data, pd.DataFrame)

            >>> # 包含系统列
            >>> bundle = dataset.extract_curve_dataframe_bundle([
            ...     "PHIT"
            ... ], include_system_columns=True)
            >>> # bundle.data将包含井名列、深度列和PHIT列

            >>> # 便捷属性访问
            >>> well_names_series = bundle.get_well_names()  # pd.Series
            >>> depths_series = bundle.get_depths()  # pd.Series (单深度情况)

            >>> # 使用查询条件
            >>> bundle = dataset.extract_curve_dataframe_bundle([
            ...     "PHIT", "GR"
            ... ], query_condition="PHIT > 0.1")

            >>> # 使用重命名映射
            >>> rename_map = {'Porosity': 'PHIT', 'GammaRay': 'GR'}
            >>> bundle = dataset.extract_curve_dataframe_bundle(
            ...     curve_names=["Porosity", "GammaRay"], # 使用新名称
            ...     rename_map=rename_map
            ... )
            >>> assert "Porosity" in bundle.curve_to_columns_map

        Performance Benefits:
            - 返回DataFrame格式，便于进一步的pandas操作
            - 提供曲线到列名的映射，支持复杂的数据处理
            - 重用经过优化的曲线提取核心逻辑

        Design Notes:
            与extract_curve_array_bundle形成互补：
            - extract_curve_dataframe_bundle: 返回DataFrame，适合数据预处理
            - extract_curve_array_bundle: 返回numpy数组，适合算法计算

        References:
            《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
        """
        from logwp.models.datasets.internal.curve_extraction import extract_curve_dataframe_bundle

        return extract_curve_dataframe_bundle(
            dataset=self,
            curve_names=curve_names,
            include_system_columns=include_system_columns,
            validate_existence=validate_existence,
            query_condition=query_condition,
            rename_map=rename_map
        )
