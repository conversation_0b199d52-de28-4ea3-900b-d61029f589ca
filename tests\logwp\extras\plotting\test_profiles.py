"""测试核心数据模型 (profiles.py)。

测试PlotProfile和SaveConfig的功能，包括：
- 数据模型创建和验证
- JSON序列化和反序列化
- 配置合并逻辑
- 错误处理
"""

import json
import pytest
from pathlib import Path

from logwp.extras.plotting import PlotProfile, SaveConfig
from logwp.extras.plotting.exceptions import ProfileIOError, ProfileMergeError


class TestSaveConfig:
    """测试SaveConfig数据类。"""

    def test_default_creation(self):
        """测试默认参数创建。"""
        config = SaveConfig()

        assert config.format == "png"
        assert config.dpi == 300
        assert config.width is None
        assert config.height is None
        assert config.bbox_inches == "tight"
        assert config.transparent is False
        assert config.save_kwargs == {}

    def test_custom_creation(self, sample_save_config):
        """测试自定义参数创建。"""
        config = sample_save_config

        assert config.format == "png"
        assert config.dpi == 150
        assert config.width == 8.0
        assert config.height == 6.0
        assert config.transparent is True

    def test_validation_invalid_dpi(self):
        """测试DPI验证。"""
        with pytest.raises(ValueError, match="DPI必须大于0"):
            SaveConfig(dpi=0)

        with pytest.raises(ValueError, match="DPI必须大于0"):
            SaveConfig(dpi=-100)

    def test_validation_invalid_dimensions(self):
        """测试尺寸验证。"""
        with pytest.raises(ValueError, match="宽度必须大于0"):
            SaveConfig(width=0)

        with pytest.raises(ValueError, match="高度必须大于0"):
            SaveConfig(height=-5.0)

    def test_multi_format_support(self):
        """测试多格式支持。"""
        config = SaveConfig(format=["png", "svg", "pdf"])
        assert config.format == ["png", "svg", "pdf"]


class TestPlotProfile:
    """测试PlotProfile数据类。"""

    def test_default_creation(self):
        """测试默认参数创建。"""
        profile = PlotProfile(name="test.profile")

        assert profile.name == "test.profile"
        assert profile.save_config is None
        assert profile.rc_params == {}
        assert profile.figure_props == {}
        assert profile.title_props == {}
        assert profile.label_props == {}
        assert profile.artist_props == {}

    def test_full_creation(self, sample_plot_profile):
        """测试完整参数创建。"""
        profile = sample_plot_profile

        assert profile.name == "test.sample"
        assert profile.rc_params["font.size"] == 12
        assert profile.figure_props["figsize"] == (10, 8)
        assert profile.title_props["label"] == "Sample Plot"
        assert profile.label_props["xlabel"] == "X Axis"
        assert profile.artist_props["color"] == "blue"

    def test_json_serialization(self, sample_plot_profile, temp_dir):
        """测试JSON序列化。"""
        profile = sample_plot_profile
        json_path = temp_dir / "test_profile.json"

        # 保存到JSON
        profile.to_json(json_path)

        # 验证文件存在
        assert json_path.exists()

        # 验证JSON内容
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        assert data["name"] == "test.sample"
        assert data["rc_params"]["font.size"] == 12
        assert data["figure_props"]["figsize"] == [10, 8]
        assert data["title_props"]["label"] == "Sample Plot"

    def test_json_deserialization(self, temp_dir, sample_json_config):
        """测试JSON反序列化。"""
        json_path = temp_dir / "test_load.json"

        # 写入测试JSON
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(sample_json_config, f, indent=2)

        # 从JSON加载
        profile = PlotProfile.from_json(json_path)

        assert profile.name == "json_test.profile"
        assert profile.save_config.format == "svg"
        assert profile.save_config.dpi == 300
        assert profile.rc_params["font.size"] == 14
        assert profile.figure_props["figsize"] == [12, 8]
        assert profile.title_props["label"] == "JSON Test Plot"

    def test_json_io_error_save(self, sample_plot_profile):
        """测试JSON保存错误处理。"""
        profile = sample_plot_profile

        # 在Windows上使用一个真正无效的路径
        import platform
        if platform.system() == "Windows":
            # 使用保留设备名作为无效路径
            invalid_path = Path("CON:/invalid/test.json")
        else:
            invalid_path = Path("/invalid/readonly/path/test.json")

        with pytest.raises(ProfileIOError) as exc_info:
            profile.to_json(invalid_path)

        assert exc_info.value.file_path == str(invalid_path)
        assert exc_info.value.operation == "save"

    def test_json_io_error_load(self, temp_dir):
        """测试JSON加载错误处理。"""
        # 测试文件不存在
        nonexistent_path = temp_dir / "nonexistent.json"

        with pytest.raises(ProfileIOError) as exc_info:
            PlotProfile.from_json(nonexistent_path)

        assert exc_info.value.file_path == str(nonexistent_path)
        assert exc_info.value.operation == "load"

        # 测试无效JSON
        invalid_json_path = temp_dir / "invalid.json"
        with open(invalid_json_path, 'w') as f:
            f.write("invalid json content")

        with pytest.raises(ProfileIOError):
            PlotProfile.from_json(invalid_json_path)

    def test_merge_with_base_simple(self):
        """测试简单配置合并。"""
        base = PlotProfile(
            name="base",
            rc_params={"font.size": 10, "font.family": "Arial"},
            figure_props={"dpi": 150}
        )

        target = PlotProfile(
            name="target",
            rc_params={"font.size": 12},  # 覆盖base的font.size
            title_props={"label": "Target Title"}  # 新增属性
        )

        merged = target.merge_with_base(base)

        # 验证合并结果
        assert merged.name == "target"  # 保持目标名称
        assert merged.rc_params["font.size"] == 12  # 目标覆盖基础
        assert merged.rc_params["font.family"] == "Arial"  # 继承基础
        assert merged.figure_props["dpi"] == 150  # 继承基础
        assert merged.title_props["label"] == "Target Title"  # 目标新增

    def test_merge_with_base_deep(self):
        """测试深度合并。"""
        base = PlotProfile(
            name="base",
            artist_props={
                "line_style": {"color": "blue", "linewidth": 1},
                "marker_style": {"marker": "o", "size": 50}
            }
        )

        target = PlotProfile(
            name="target",
            artist_props={
                "line_style": {"linewidth": 2},  # 部分覆盖
                "scatter_style": {"alpha": 0.7}  # 新增
            }
        )

        merged = target.merge_with_base(base)

        # 验证深度合并
        assert merged.artist_props["line_style"]["color"] == "blue"  # 继承
        assert merged.artist_props["line_style"]["linewidth"] == 2  # 覆盖
        assert merged.artist_props["marker_style"]["marker"] == "o"  # 继承
        assert merged.artist_props["scatter_style"]["alpha"] == 0.7  # 新增

    def test_merge_save_config_priority(self):
        """测试SaveConfig合并优先级。"""
        base_save = SaveConfig(format="png", dpi=150)
        target_save = SaveConfig(format="svg", dpi=300)

        base = PlotProfile(name="base", save_config=base_save)
        target = PlotProfile(name="target", save_config=target_save)

        merged = target.merge_with_base(base)

        # 目标的save_config优先
        assert merged.save_config.format == "svg"
        assert merged.save_config.dpi == 300

        # 测试目标没有save_config的情况
        target_no_save = PlotProfile(name="target_no_save")
        merged_no_save = target_no_save.merge_with_base(base)

        # 应该使用基础的save_config
        assert merged_no_save.save_config.format == "png"
        assert merged_no_save.save_config.dpi == 150

    def test_deep_merge_dict_helper(self, sample_plot_profile):
        """测试深度合并字典辅助方法。"""
        profile = sample_plot_profile

        base_dict = {
            "level1": {
                "level2a": {"key1": "base_value1", "key2": "base_value2"},
                "level2b": "base_simple"
            },
            "simple_key": "base_simple_value"
        }

        override_dict = {
            "level1": {
                "level2a": {"key1": "override_value1", "key3": "new_value3"},
                "level2c": "new_level2c"
            },
            "new_simple_key": "new_simple_value"
        }

        result = profile._deep_merge_dict(base_dict, override_dict)

        # 验证深度合并结果
        assert result["level1"]["level2a"]["key1"] == "override_value1"  # 覆盖
        assert result["level1"]["level2a"]["key2"] == "base_value2"      # 保留
        assert result["level1"]["level2a"]["key3"] == "new_value3"       # 新增
        assert result["level1"]["level2b"] == "base_simple"              # 保留
        assert result["level1"]["level2c"] == "new_level2c"              # 新增
        assert result["simple_key"] == "base_simple_value"               # 保留
        assert result["new_simple_key"] == "new_simple_value"            # 新增
