"""scape.core.obmiq.internal.interpretability - OBMIQ模型可解释性计算

本模块使用PyTorch官方的可解释性库Captum，为OBMIQ模型提供深入的归因分析。

Architecture
------------
层次/依赖: scape/core/obmiq/internal层，被training_facade调用
设计原则: 封装复杂的可解释性算法，提供统一的分析接口

Functions:
    compute_captum_attributions: 执行完整的Captum归因计算，返回原始数值。

References:
    - Captum Library: https://captum.ai/
"""
from __future__ import annotations

from typing import Any, Dict

import numpy as np
import pandas as pd
import torch
from logwp.infra import get_logger

from .model_builder import OBMIQPyTorchModel

logger = get_logger()


def compute_captum_attributions(
    model_assets: Dict[str, Any],
    final_eval_df: pd.DataFrame,
) -> Dict[str, Any] | None:
    """使用Captum库执行完整的模型可解释性计算。

    该函数是一个纯计算单元，不执行任何I/O或绘图操作。它返回一个
    包含所有原始归因数值的字典，供facade层进一步处理。
    """
    try:
        from captum.attr import IntegratedGradients, Saliency

        # 1. 准备模型和数据
        metadata = model_assets["metadata"]
        best_hps = model_assets["model_hyperparameters"]
        data_shapes = model_assets["data_shapes"]
        scaler = model_assets["preprocessors"]["tabular_scaler"]
        # 使用物理列名进行数据提取，使用逻辑名作为结果字典的键
        sequence_cols, tabular_cols, target_features = (
            metadata["sequence_cols"],
            metadata["tabular_cols"],
            metadata["target_features"],  # 逻辑名
        )

        model = OBMIQPyTorchModel(best_hps, data_shapes).cpu()
        model.load_state_dict(model_assets["model_state_dict"])
        model.eval()

        # 创建一个模型包装器以适配Captum的输入格式
        def model_wrapper(seq_input, tab_input):
            return model({"sequence_input": seq_input, "tabular_input": tab_input})

        # 准备用于归因分析的完整评估数据集张量
        eval_seq = torch.from_numpy(
            final_eval_df[sequence_cols].values.astype(np.float32)
        ).unsqueeze(1)
        eval_tab_scaled = scaler.transform(final_eval_df[tabular_cols])
        eval_tab = torch.from_numpy(eval_tab_scaled.astype(np.float32))

        tabular_attributions_dict = {}
        sequence_attributions_dict = {}

        # 2. 为每个目标输出分别进行分析
        for target_idx, target_name in enumerate(target_features):
            logger.info(f"Running Captum analysis for target: {target_name}...")

            # a. 表格特征归因 (使用Integrated Gradients)
            ig = IntegratedGradients(model_wrapper)
            baseline_seq = torch.zeros_like(eval_seq)
            baseline_tab = torch.zeros_like(eval_tab)
            attributions_ig = ig.attribute(
                inputs=(eval_seq, eval_tab),
                baselines=(baseline_seq, baseline_tab),
                target=target_idx,
                internal_batch_size=16,
            )
            tabular_attributions_dict[target_name] = attributions_ig[1].cpu().detach().numpy()

            # b. 序列特征归因 (使用Saliency)
            saliency = Saliency(model_wrapper)
            attributions_saliency = saliency.attribute(
                inputs=(eval_seq, eval_tab), target=target_idx
            )
            sequence_attributions_dict[target_name] = (
                attributions_saliency[0].abs().cpu().detach().numpy()
            )

        return {
            "tabular_attributions": tabular_attributions_dict,
            "sequence_attributions": sequence_attributions_dict,
            "scaled_tabular_features_df": pd.DataFrame(eval_tab_scaled, columns=tabular_cols),
            "original_sequences_arr": eval_seq.cpu().detach().numpy(),
        }

    except ImportError:
        logger.warning(
            "`captum`库未安装，跳过可解释性分析。请运行 'pip install captum'。"
        )
        return None
    except Exception as e:
        logger.error(f"Captum分析失败: {e}", exc_info=True)
        return None
