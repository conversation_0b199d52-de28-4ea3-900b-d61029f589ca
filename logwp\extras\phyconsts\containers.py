"""
Defines the core data container class for physical constants.
"""
from dataclasses import dataclass

from logwp.extras.units import Quantity


@dataclass(frozen=True)
class Constant:
    """
    Represents a physical constant, combining a value with a unit and metadata.

    This is a frozen dataclass, meaning its instances are immutable.

    Attributes:
        name (str): The full name of the constant (e.g., 'standard_gravity').
        value (Quantity): The value of the constant as a Quantity object,
                          which includes the numerical value and its unit.
        symbol (str): The standard symbol for the constant (e.g., 'g').
        reference (str): The source or reference for the constant's value
                         (e.g., 'CODATA 2018').
    """
    name: str
    value: Quantity
    symbol: str
    reference: str

