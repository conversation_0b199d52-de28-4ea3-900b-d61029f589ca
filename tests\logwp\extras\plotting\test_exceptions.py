"""测试异常处理体系 (exceptions.py)。

测试绘图配置服务的异常类，包括：
- 异常层次结构
- 结构化异常信息
- 异常链保持
- 错误上下文
"""

import pytest

from logwp.extras.plotting.exceptions import (
    WpPlottingError,
    ProfileNotFoundError,
    ProfileRegistrationError,
    ProfileMergeError,
    ProfileIOError,
    StyleApplicationError
)
from logwp.infra.exceptions import ErrorContext


class TestWpPlottingError:
    """测试基础异常类。"""

    def test_basic_creation(self):
        """测试基础异常创建。"""
        error = WpPlottingError("Test plotting error")

        assert str(error) == "Test plotting error"
        assert isinstance(error, Exception)

    def test_inheritance_chain(self):
        """测试异常继承链。"""
        # 所有plotting异常都应该继承自WpPlottingError
        assert issubclass(ProfileNotFoundError, WpPlottingError)
        assert issubclass(ProfileRegistrationError, WpPlottingError)
        assert issubclass(ProfileMergeError, WpPlottingError)
        assert issubclass(ProfileIOError, WpPlottingError)
        assert issubclass(StyleApplicationError, WpPlottingError)

    def test_exception_catching(self):
        """测试异常捕获。"""
        # 应该能够用基类捕获所有子类异常
        with pytest.raises(WpPlottingError):
            raise ProfileNotFoundError("Test error")

        with pytest.raises(WpPlottingError):
            raise ProfileRegistrationError("Test error")


class TestProfileNotFoundError:
    """测试配置未找到异常。"""

    def test_basic_creation(self):
        """测试基础创建。"""
        error = ProfileNotFoundError("Profile not found")

        assert str(error) == "Profile not found"
        assert error.profile_name is None
        assert error.available_profiles == []

    def test_detailed_creation(self):
        """测试详细信息创建。"""
        available = ["base", "test.profile1", "test.profile2"]

        error = ProfileNotFoundError(
            "Profile 'missing.profile' not found",
            profile_name="missing.profile",
            available_profiles=available
        )

        assert error.profile_name == "missing.profile"
        assert error.available_profiles == available
        assert "missing.profile" in str(error)

    def test_with_context(self):
        """测试带上下文的异常。"""
        context = ErrorContext(
            operation="get_profile",
            dataset_name="test_dataset"
        )

        error = ProfileNotFoundError(
            "Profile not found",
            profile_name="test.profile",
            context=context
        )

        assert error.context == context
        assert error.profile_name == "test.profile"


class TestProfileRegistrationError:
    """测试配置注册异常。"""

    def test_basic_creation(self):
        """测试基础创建。"""
        error = ProfileRegistrationError("Registration failed")

        assert str(error) == "Registration failed"
        assert error.profile_name is None
        assert error.operation is None

    def test_detailed_creation(self):
        """测试详细信息创建。"""
        error = ProfileRegistrationError(
            "Profile 'test.profile' already exists",
            profile_name="test.profile",
            operation="register"
        )

        assert error.profile_name == "test.profile"
        assert error.operation == "register"
        assert "test.profile" in str(error)

    def test_base_registration_error(self):
        """测试基础模板注册错误。"""
        error = ProfileRegistrationError(
            "Invalid base profile name",
            profile_name="invalid.name",
            operation="register_base"
        )

        assert error.profile_name == "invalid.name"
        assert error.operation == "register_base"


class TestProfileMergeError:
    """测试配置合并异常。"""

    def test_basic_creation(self):
        """测试基础创建。"""
        error = ProfileMergeError("Merge failed")

        assert str(error) == "Merge failed"
        assert error.base_profile_name is None
        assert error.target_profile_name is None
        assert error.merge_stage is None

    def test_detailed_creation(self):
        """测试详细信息创建。"""
        error = ProfileMergeError(
            "Cannot merge incompatible types",
            base_profile_name="base",
            target_profile_name="target.profile",
            merge_stage="rc_params_merge"
        )

        assert error.base_profile_name == "base"
        assert error.target_profile_name == "target.profile"
        assert error.merge_stage == "rc_params_merge"
        # 注意：异常消息只包含传入的message，不会自动包含属性值
        assert "Cannot merge incompatible types" in str(error)

    def test_deep_merge_error(self):
        """测试深度合并错误。"""
        error = ProfileMergeError(
            "Deep merge conflict in artist_props",
            base_profile_name="module.base",
            target_profile_name="module.specific",
            merge_stage="artist_props_deep_merge"
        )

        assert error.merge_stage == "artist_props_deep_merge"


class TestProfileIOError:
    """测试配置I/O异常。"""

    def test_basic_creation(self):
        """测试基础创建。"""
        error = ProfileIOError("I/O operation failed")

        assert str(error) == "I/O operation failed"
        assert error.file_path is None
        assert error.operation is None

    def test_detailed_creation(self):
        """测试详细信息创建。"""
        error = ProfileIOError(
            "Failed to parse JSON configuration file",
            file_path="/config/plot_profiles/custom.json",
            operation="load"
        )

        assert error.file_path == "/config/plot_profiles/custom.json"
        assert error.operation == "load"
        # 注意：异常消息只包含传入的message，不会自动包含属性值
        assert "Failed to parse JSON configuration file" in str(error)

    def test_save_error(self):
        """测试保存错误。"""
        error = ProfileIOError(
            "Permission denied",
            file_path="/readonly/config.json",
            operation="save"
        )

        assert error.operation == "save"
        assert error.file_path == "/readonly/config.json"

    def test_scan_error(self):
        """测试目录扫描错误。"""
        error = ProfileIOError(
            "Directory not accessible",
            file_path="/config/plot_profiles/",
            operation="scan"
        )

        assert error.operation == "scan"


class TestStyleApplicationError:
    """测试样式应用异常。"""

    def test_basic_creation(self):
        """测试基础创建。"""
        error = StyleApplicationError("Style application failed")

        assert str(error) == "Style application failed"
        assert error.profile_name is None
        assert error.matplotlib_object is None
        assert error.failed_property is None

    def test_detailed_creation(self):
        """测试详细信息创建。"""
        error = StyleApplicationError(
            "Font 'CustomFont' not available",
            profile_name="custom.profile",
            matplotlib_object="Axes",
            failed_property="font.family"
        )

        assert error.profile_name == "custom.profile"
        assert error.matplotlib_object == "Axes"
        assert error.failed_property == "font.family"
        assert "CustomFont" in str(error)

    def test_figure_property_error(self):
        """测试Figure属性错误。"""
        error = StyleApplicationError(
            "Invalid figure size",
            profile_name="test.profile",
            matplotlib_object="Figure",
            failed_property="figsize"
        )

        assert error.matplotlib_object == "Figure"
        assert error.failed_property == "figsize"

    def test_rcparams_error(self):
        """测试rcParams错误。"""
        error = StyleApplicationError(
            "Invalid rcParams value",
            profile_name="test.profile",
            matplotlib_object="rcParams",
            failed_property="axes.grid"
        )

        assert error.matplotlib_object == "rcParams"
        assert error.failed_property == "axes.grid"


class TestExceptionChaining:
    """测试异常链保持。"""

    def test_exception_chain_preservation(self):
        """测试异常链保持。"""
        original_error = ValueError("Original error")

        try:
            raise original_error
        except ValueError as e:
            plotting_error = ProfileIOError(
                "Failed to load profile",
                file_path="test.json",
                operation="load"
            )

            # 手动设置异常链
            plotting_error.__cause__ = e

            with pytest.raises(ProfileIOError) as exc_info:
                raise plotting_error

            # 验证异常链
            assert exc_info.value.__cause__ is original_error

    def test_nested_exception_handling(self):
        """测试嵌套异常处理。"""
        def inner_function():
            raise ValueError("Inner error")

        def middle_function():
            try:
                inner_function()
            except ValueError as e:
                raise ProfileMergeError(
                    "Merge failed due to inner error",
                    base_profile_name="base",
                    target_profile_name="target"
                ) from e

        def outer_function():
            try:
                middle_function()
            except ProfileMergeError as e:
                raise ProfileIOError(
                    "I/O failed due to merge error",
                    operation="save"
                ) from e

        with pytest.raises(ProfileIOError) as exc_info:
            outer_function()

        # 验证异常链
        io_error = exc_info.value
        assert isinstance(io_error.__cause__, ProfileMergeError)
        assert isinstance(io_error.__cause__.__cause__, ValueError)


class TestErrorContextIntegration:
    """测试错误上下文集成。"""

    def test_error_context_usage(self):
        """测试错误上下文使用。"""
        context = ErrorContext(
            operation="apply_profile",
            dataset_name="test_dataset"
        )

        error = StyleApplicationError(
            "Failed to apply style",
            profile_name="test.profile",
            context=context
        )

        assert error.context == context
        assert error.context.operation == "apply_profile"
        assert error.context.dataset_name == "test_dataset"

    def test_multiple_context_types(self):
        """测试多种上下文类型。"""
        # 测试不同异常类型的上下文使用
        contexts = [
            ErrorContext(operation="register"),
            ErrorContext(operation="merge"),
            ErrorContext(operation="save"),
            ErrorContext(operation="load"),
            ErrorContext(operation="apply")
        ]

        exceptions = [
            ProfileRegistrationError("Registration error", context=contexts[0]),
            ProfileMergeError("Merge error", context=contexts[1]),
            ProfileIOError("Save error", context=contexts[2]),
            ProfileIOError("Load error", context=contexts[3]),
            StyleApplicationError("Apply error", context=contexts[4])
        ]

        for i, exc in enumerate(exceptions):
            assert exc.context == contexts[i]
            assert exc.context.operation in ["register", "merge", "save", "load", "apply"]
