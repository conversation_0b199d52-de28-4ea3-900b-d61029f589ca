from __future__ import annotations

"""logwp.io.wp_excel - WP Excel文件格式处理器

实现WFS v1.0规范的WP Excel文件读写功能，支持完整的测井数据模型构造。

Architecture
------------
层次/依赖: I/O层格式特定处理器，依赖models、constants、exceptions
设计原则: 格式解耦、渐进式构造、WFS规范兼容
性能特征: 流式处理、内存优化、异步I/O支持

Core Features
-------------
- **WFS v1.0规范**: 完整支持三种数据集类型、扩展属性、二维组合曲线
- **渐进式构造**: 按照CF-1流程逐步构建数据模型
- **大小写不敏感**: 支持CIIA架构的标识符处理
- **错误诊断**: 结构化异常和详细错误上下文

Package Structure
-----------------
- wp_excel_reader: 主要读取器类
- internal/: 内部解析服务模块
  - excel_parser: Excel文件基础解析
  - head_info_parser: 井头信息解析
  - well_map_parser: 井名映射解析
  - dataset_parser: 数据集解析
  - curve_parser: 曲线元数据解析
  - data_converter: 数据转换服务
  - validator: WFS规范验证

Examples
--------
>>> from logwp.io.wp_excel import WpExcelReader
>>>
>>> # 创建读取器
>>> reader = WpExcelReader()
>>>
>>> # 读取WP文件
>>> project = reader.read("data/sample.wp.xlsx")
>>> print(f"项目: {project.name}")

References
----------
- 《SCAPE_WFS_WP文件规范.md》- WFS v1.0规范
- 《SCAPE_DDS_logwp_io层的渐进式生成流程.md》- 构造流程设计
"""

__all__ = [
    # 主要读写器
    "WpExcelReader",
    "WpExcelWriter",

    # 配置类
    "ExcelFormattingConfig",

    # 内部服务模块（通常不直接使用）
    "internal",
]

# 直接导入主要组件
from .wp_excel_reader import WpExcelReader
from .wp_excel_writer import WpExcelWriter
from .config import ExcelFormattingConfig


def __getattr__(name: str) -> object:
    """延迟导入内部模块。

    Architecture
    ------------
    层次/依赖: WP Excel处理器门面
    设计原则: 延迟导入、内部模块隔离
    性能特征: 按需加载、模块化设计
    """
    # 内部服务模块
    if name == "internal":
        from . import internal
        return internal

    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
