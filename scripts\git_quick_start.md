# Git 核心思想简介

* **Git 是一个快照系统**: 每次您提交 (commit)，Git 都会为您项目的**所有文件**拍一张快照（就像一个存档点），而不是记录文件的差异。
* **三个区域**: 理解这三个区域是使用 Git 的关键。
    1.  **工作区 (Working Directory)**: 您在电脑上能看到的、正在编辑的项目文件夹。
    2.  **暂存区 (Staging Area)**: 一个临时的“购物车”。您把希望下次提交的修改（快照内容）通过 `git add` 命令放进这里。
    3.  **本地仓库 (Local Repository)**: 您电脑上一个安全的、包含所有历史快照的数据库（`.git` 文件夹）。`git commit` 命令会将暂存区的内容生成一个永久快照并存入这里。
* **您的单向备份工作流**:
    `工作区` -> `暂存区` -> `本地仓库` -> `远程仓库 (GitHub)`

# 场景零：新环境初始化与特殊配置
**目的：** 我在一台新电脑上（或者家里/办公室）开始工作，需要配置好 Git 的基本信息和网络代理，确保能顺利连接 GitHub。

**核心思路：** 利用 Git 的**全局配置 (`--global`)**，让配置跟随**电脑环境**，而不是跟随移动硬盘里的**项目**。

## 1. 设置身份标识 (所有电脑都必须执行)
这是您的身份签名，会记录在每一次提交中，是 Git 的最基本配置。
```bash
git config --global user.name "POLO"
git config --global user.email "<EMAIL>"
```

## 2. 设置办公室网络代理 (仅在办公室电脑执行)
如果办公室需要代理才能访问 GitHub，请执行以下命令。请将代理地址和端口替换为您自己的。
```bash
git config --global http.proxy [http://127.0.0.1:7890](http://127.0.0.1:7890)
git config --global https.proxy [http://127.0.0.1:7890](http://127.0.0.1:7890)
```

## 3. 清理家庭网络代理 (仅在家里电脑执行)
为确保家里电脑直连网络，请检查并移除可能存在的代理配置。
```bash
git config --global --unset http.proxy
git config --global --unset https.proxy
```

> **重要提示**：**不要**在您移动硬盘的项目目录中使用 `git config --local` 来设置代理或身份，因为这会导致配置被同步，从而在另一台机器上出错或产生不一致的提交者信息。

# 场景一：项目启动与首次备份到GitHub
**目的：** 我有一个本地的 Python 项目（无论新旧），现在想把它完整地备份到私有的 GitHub 仓库。

**步骤流程：**
```bash
# 1. 在本地项目根目录，初始化Git仓库 (如果项目已有.git文件夹，请跳过此步)
git init

# 2. 创建 .gitignore 文件，告诉Git忽略不必要的文件 (极其重要！)
#    (例如虚拟环境venv/，缓存__pycache__/等)

# 3. 在 GitHub 上创建一个空的、私有的仓库，然后复制其URL

# 4. 将本地仓库与远程仓库关联
git remote add origin <你的GitHub仓库URL>

# 5. 将所有文件（除忽略的）加入“购物车”准备提交
git add .

# 6. 为项目拍下第一个“快照”（提交）
git commit -m "Initial project backup"

# 7. (推荐) 将主分支统一为 'main'，以匹配GitHub的默认设置
git branch -M main

# 8. 首次推送到GitHub，完成第一次完整备份
#    -u 参数会自动关联本地和远程分支，仅需使用一次
git push -u origin main
```

# 场景二：日常编码与增量备份
**目的：** 我今天写了一些代码，完成了一个小功能或修复了一个bug，想把这些新的进展保存并备份。

**步骤流程：**
```bash
# 1. 查看我改动了哪些文件，检查工作状态 (可选，但好习惯)
git status

# 2. 将所有修改和新文件加入暂存区（准备提交）
git add .

# 3. 提交这些修改到本地仓库，并写清楚这次干了什么
git commit -m "例如：增加了用户登录API"

# 4. 将本地的新提交推送到GitHub，完成备份
git push
```

# 场景三：“糟糕，代码被我/AI改乱了！” (一键还原与高级撤销)
**目的：** 我在上次提交之后做了一些修改，但结果不满意，或者想对已经提交的内容进行更精细的“反悔”操作。

| 具体情况 | 解决方案 |
| :--- | :--- |
| **情况A：想放弃上次提交之后的所有修改，让整个项目目录恢复到上次提交的干净状态。** | `git reset --hard HEAD` <br> > **[危险]** 此命令会**永久丢弃**所有未提交的本地修改和暂存区内容。执行前请确认。|
| **情况B：** 我只想放弃对**某一个文件**的修改。| `git restore <文件名>` <br> > (经典命令: `git checkout -- <文件名>`) |
| **情况C：** 我不小心 `git add` 了一个文件，但暂时不想提交它。| `git restore --staged <文件名>` <br> > (经典命令: `git reset HEAD <文件名>`) |
| **情况D：** 刚 `commit` 完，但发现提交信息写错了。| `git commit --amend` |
| **情况E：** 刚 `commit` 完，但发现漏掉了一个文件。| `git add <漏掉的文件名>` <br> `git commit --amend` |
| **情况F：已 `commit`，但想撤销这次提交，并保留代码修改，以便重新整理。** | `git reset --soft HEAD~1`<br> > 这是 `reset` 的安全用法。它仅将 `HEAD` 指针回退一步，提交的所有代码更改都会保留在暂存区，您可以重新整理和提交。 |
| **情况G：已 `push`，想安全地“反转”某一次历史提交，生成一次新的“拨乱反正”的提交。** | `git revert <commit_id>`<br> > 这是最专业的“后悔”方式。它不会删除历史，而是创建一个内容相反的新提交来抵消错误。非常适合已推送到远程的场景。 |

# 场景四：回顾过去与恢复旧版文件
**目的：** 我想看看之前的提交记录，或者某个文件以前的版本是怎样的，并可能需要恢复它。

| 具体需求 | 解决方案 |
| :--- | :--- |
| **我想快速浏览提交历史** | `git log --oneline --graph` |
| **我想看看某个文件的修改历史** | `git log -p <文件名>` |
| **我想对比当前修改和上次提交有什么不同** | `git diff HEAD` |
| **我想把某个文件恢复到历史上的某个版本**| `git checkout <commit_id> -- <文件名>` <br> > (现代命令: `git restore --source=<commit_id> -- <文件名>`) |

# 场景五：尝试新想法或开发复杂功能 (使用分支)
**目的：** 我要开发一个可能耗时较长或有风险的新功能，不想影响到当前稳定的主分支。

**步骤流程：**
```bash
# 1. 基于当前主分支，创建一个名为 'new-feature' 的新分支并切换过去
#    (现代命令: git switch -c new-feature)
git checkout -b new-feature

# 2. 在 'new-feature' 分支上自由地进行开发...
git add .
git commit -m "完成新功能的A部分"

# 3. 新功能开发测试完毕后，准备合并回主分支
#    (现代命令: git switch main)
git checkout main

# 4. 将新功能分支的全部修改合并到主分支
git merge new-feature

# 5. 推送合并后的主分支到GitHub
git push

# 6. 删除已经完成使命的功能分支
git branch -d new-feature
```

# 场景六：版本发布 (Release) 与新版本开发
**目的：** 我的 1.0 版本已经开发完成，代码稳定。我该如何正式“发布”它，并在 Git 中留下标记，然后开始 2.0 版本的新功能开发？

**核心思路：** 使用 Git 的 **标签 (Tag)** 功能来标记发布的节点，它就像一个永久不会移动的“书签”。开发工作继续在主分支上进行。

## 1. 确认主分支稳定
确保所有功能都已合并到 `main` 分支，并且代码处于可发布的稳定状态。
```bash
git checkout main
# 对于单人开发，也可以用 git pull 确保本地与远程备份一致
```

## 2. 创建发行标签 (本地)
我们使用“附注标签” (annotated tag)，因为它包含更丰富的信息（如作者、日期、说明），更适合正式发布。
```bash
# -a 表示创建一个附注标签，v1.0.0是标签名 (推荐使用v前缀和语义化版本)
# -m 后面是这次发布的说明
git tag -a v1.0.0 -m "Release version 1.0.0"

# (可选) 查看所有本地标签
git tag
```

## 3. 将标签推送到 GitHub
默认情况下，`git push` **不会**推送标签。您需要显式地推送它。
```bash
# 推送您刚刚创建的 v1.0.0 标签
git push origin v1.0.0

# (备选方案) 如果您有多个标签需要一次性推送
# git push --tags
```

## 4. (推荐) 在 GitHub 上创建正式的 Release
Git 标签只是一个指针，而 GitHub Release 是基于标签的、更丰富的发布页面。
* 打开您在 GitHub 上的项目页面。
* 点击右侧的 “**Releases**” 链接。
* 点击 “**Draft a new release**”。
* 在 “**Choose a tag**” 下拉框中，选择您刚刚推送的 `v1.0.0` 标签。
* 填写发布标题（如 `Version 1.0.0`）和详细的更新日志。
* (可选) 您还可以在这里上传编译好的程序包、文档等二进制文件。
* 点击 “**Publish release**”。

## 5. 开始 2.0 版本的开发
您**不需要做任何特殊操作**，直接在 `main` 分支上继续开发即可。
```bash
# 继续您的日常开发流程
git add .
git commit -m "Feat: Add new feature for version 2.0"
git push
```
所有新的提交都会在 `v1.0.0` 标签之后，自然而然地成为 2.0 开发过程的一部分。`v1.0.0` 标签则永远指向那个特定的历史时刻。

# 场景七：仓库维护与管理
**目的：** 随着项目发展，我需要管理远程备份地址，或者清理不小心提交到历史记录中的敏感文件。

| 具体需求 | 解决方案 |
| :--- | :--- |
| **我想修改远程仓库的地址**<br>(例如GitHub更改了用户名) | `git remote set-url origin <新的GitHub仓库URL>` |
| **我想同时备份到GitHub和Gitee** | 1. `git remote add gitee <你的Gitee仓库URL>`<br>2. `git push gitee main` |
| **[高级] 我不小心把密码或大文件提交并推送了，想从所有历史记录中彻底删除它。** | `git filter-branch --force --index-filter "git rm --cached --ignore-unmatch path/to/your/file" --prune-empty --tag-name-filter cat -- --all`<br> > **[极度危险]** 这是重写历史的终极武器，操作前**必须备份整个项目**。操作后需要强制推送 (`git push --force`) 来覆盖远程仓库。 |

# 场景八：项目交付与归档
**目的：** 我需要将当前版本的项目代码，打包成一个不包含 `.git` 历史记录的纯净 `.zip` 文件发给别人。

| 具体需求 | 解决方案 |
| :--- | :--- |
| **打包当前分支的最新代码** | `git archive -o latest.zip HEAD` |

# 场景九：命令的等效用法与现代实践
**目的：** 了解同一操作的不同命令实现，写出更简洁、更现代、意图更清晰的指令。

## 1. `git add` 的细微差别
当您在项目的**根目录**下执行时，`git add .` 和 `git add -A` 的效果几乎一模一样。

| 命令 | 作用范围 | 作用对象 | 核心区别 |
| :--- | :--- | :--- | :--- |
| `git add .` | 从**当前目录**开始，递归向下 | 新增的文件、修改的文件 | 只关注当前及子目录。 |
| `git add -A` | 整个**项目仓库** | 新增、修改、**删除**的文件 | **无视您在哪个目录执行**，永远对整个项目生效。 |
| `git add -u` | 整个**项目仓库** | 修改、**删除**的文件 | 不包含**新增的**未跟踪文件。 |

## 2. 现代命令 vs 经典命令 (强烈推荐使用现代命令)
旧版的 `git checkout` 命令功能过于混杂，新版 Git 将其拆分为 `switch` 和 `restore`，意图更清晰，更安全。

| 目的/意图 | 现代命令 (推荐) | 经典命令 |
| :--- | :--- | :--- |
| **切换到一个已存在的分支** | `git switch main` | `git checkout main` |
| **创建并切换到新分支** | `git switch -c new-feature` | `git checkout -b new-feature` |
| **放弃对工作区某个文件的修改** | `git restore user.py` | `git checkout -- user.py`|
| **将文件从暂存区撤出** | `git restore --staged user.py` | `git reset HEAD user.py`|

## 3. 便捷的组合命令
| 目的/意图 | 便捷命令 | 等效的步骤 | 备注 |
| :--- | :--- | :--- | :--- |
| **将已跟踪文件的修改<br>直接暂存并提交** | `git commit -am "msg"` | 1. `git add -u`<br>2. `git commit -m "msg"`| 非常高效，但**不会暂存新创建的文件**。|
| **强制删除一个分支** | `git branch -D my-feature` | `git branch -d --force my-feature`| `-D` 是 `-d --force` 的组合。|

## 4. 理解 `git pull` 的本质
`git pull` 实际上是两个命令的组合，了解其原理有助于在协作中更好地控制版本。

| 组合命令 | 等效的步骤 | 解释 |
| :--- | :--- | :--- |
| `git pull origin main`| 1. `git fetch origin main` <br> 2. `git merge FETCH_HEAD`| 1. **Fetch**: 先从远程下载最新历史记录，但**不合并**。 <br> 2. **Merge**: 然后，将下载的内容合并到当前分支。|

# Git 忽略特定文件夹并清理已跟踪文件的标准操作流程

本方法旨在解决一个常见问题：当你希望 Git 开始忽略某些之前已经被跟踪过的文件或文件夹时，如何正确地更新 `.gitignore` 并清理 Git 的跟踪记录。

---

### **第一步：编辑 `.gitignore` 文件**

首先，明确你想要忽略的文件模式，并将其添加到项目根目录的 `.gitignore` 文件中。

1.  **定位文件**：确保你在项目根目录操作 `.gitignore` 文件。如果不存在，请创建它。

2.  **添加规则**：根据需求添加规则。例如，要忽略 `scape_case/jupyter/` 目录下任意深度的 `output*` 文件夹：

    ```gitignore
    # .gitignore

    # 忽略 scape_case/jupyter/ 目录下任何深度的 output* 文件夹
    scape_case/jupyter/**/output*/
    ```

    * **语法解释**：
        * `**`：匹配任意多层的中间目录。
        * `*`：匹配任意字符（此处用于匹配 `output01`, `output02` 等）。
        * 末尾的 `/`：确保此规则只匹配文件夹。

---

### **第二步：从 Git 跟踪记录中移除已存在的文件**

修改 `.gitignore` 不会影响已经被 Git 跟踪（`tracked`）的文件。你必须手动将它们从 Git 的暂存区（Staging Area / Index）中移除。此操作不会删除你的本地物理文件。

根据你的操作系统和命令行工具，选择以下一种方法执行。

#### **方案A (推荐): 使用 Git Bash 或 macOS/Linux 终端**

这个环境下的工具链更强大，命令更简洁。

```bash
# 该命令会自动找出所有应被忽略但已被跟踪的文件，并从暂存区移除它们
git ls-files -ci --exclude-standard -z 'scape_case/jupyter/**/output*/*' | xargs -0 git rm --cached
```

#### **方案B (备选): 使用 Windows 命令提示符 (CMD)**

如果你必须使用原生 CMD，请使用以下 `FOR` 循环命令。

```cmd
# 注意：此命令是为直接在 CMD 窗口中运行而设计的（使用 %G）
FOR /F "tokens=*" %G IN ('git ls-files -ci --exclude-standard "scape_case/jupyter/**/output*/*"') DO git rm --cached "%G"
```
* **提示**: 如果你想把此命令写入 `.bat` 批处理脚本，需要将 `%G` 改为 `%%G`。

---

### **第三步：提交你的全部更改**

现在，你需要提交两项更改：1) `.gitignore` 文件本身的新增内容；2) 文件跟踪状态的变更（将旧文件从跟踪列表中移除）。

1.  **检查状态**：运行 `git status`，你会看到 `.gitignore` 文件被修改，以及一大批之前被跟踪的文件现在处于 `deleted` 状态（实际上是从暂存区删除）。

2.  **提交更改**：

    ```bash
    # 添加 .gitignore 文件的修改和所有已移除跟踪的文件
    git add .gitignore

    # 你也可以直接用 git add . 来一次性暂存所有更改
    # git add .

    # 创建一个清晰的提交信息
    git commit -m "build: Update .gitignore to ignore output folders and untrack existing files"
    ```

---

### **第四步：验证结果**

提交后，你的远程仓库和本地 Git 记录中将不再包含这些 `output` 文件。未来，这些被忽略的文件夹中任何内容的修改或新增，都不会再出现在 `git status` 的结果中。
