"""曲线统计分析服务。

提供曲线数据统计分析的服务实现，按数据类型分类进行统计。

Architecture
------------
层次/依赖: models/curve/internal服务层，无状态服务函数
设计原则: Service Layer模式、无状态设计、向量化计算
性能特征: 高效统计计算、内存优化、错误容错

Examples:
    >>> from logwp.models.curve.internal.curve_statistics import generate_curve_statistics
    >>> stats = generate_curve_statistics(df, curve_metadata)
    >>> print(f"数值型曲线: {len(stats['numeric_curves'])}")

References:
    《SCAPE_DDS_logwp_generate_summary.md》§4.6 - 曲线统计服务设计
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any

import numpy as np
import pandas as pd

if TYPE_CHECKING:
    from logwp.models.curve.metadata import CurveMetadata, CurveBasicAttributes

from logwp.models.constants import WpDataType, WpCurveClass, WpCurveCategory
from logwp.infra import get_logger
from logwp.models.internal.summary_constants import SummaryKeys, LogMessages

logger = get_logger(__name__)


def generate_curve_statistics(
    df: pd.DataFrame,
    curve_metadata: CurveMetadata
) -> dict[str, Any]:
    """生成曲线统计分析（无状态服务函数）。

    直接对DataFrame的每列进行统计分析，根据数据类型自动判断统计方法。
    适用于单井数据或需要整体统计的场景。

    Args:
        df: DataFrame数据
        curve_metadata: 曲线元数据（用于获取曲线分类信息）

    Returns:
        dict: 曲线统计分析结果
            - numeric_curves: 数值型曲线统计
            - categorical_curves: 类别型曲线统计
            - identifier_curves: 标识型曲线统计
            - summary: 统计汇总信息

    Examples:
        >>> stats = generate_curve_statistics(df, metadata)
        >>> numeric_stats = stats['numeric_curves']
        >>> for column_name, stat in numeric_stats.items():
        ...     print(f"{column_name}: 均值={stat['mean']:.2f}")
    """
    logger.debug(LogMessages.MSG_CURVE_STATS_START,
                total_curves=len(curve_metadata),
                dataframe_shape=df.shape,
                dataframe_columns=len(df.columns))

    try:
        # 直接对DataFrame的每列进行统计分析
        stats = _analyze_dataframe_columns(df, curve_metadata)

        # 添加统计汇总
        stats[SummaryKeys.SUMMARY] = {
            SummaryKeys.NUMERIC_COUNT: len(stats[SummaryKeys.NUMERIC_CURVES]),
            SummaryKeys.CATEGORICAL_COUNT: len(stats[SummaryKeys.CATEGORICAL_CURVES]),
            SummaryKeys.IDENTIFIER_COUNT: len(stats[SummaryKeys.IDENTIFIER_CURVES]),
            SummaryKeys.TOTAL_ANALYZED: len(stats[SummaryKeys.NUMERIC_CURVES]) + len(stats[SummaryKeys.CATEGORICAL_CURVES]) + len(stats[SummaryKeys.IDENTIFIER_CURVES])
        }

        logger.debug(LogMessages.MSG_CURVE_STATS_COMPLETE,
                    numeric_count=stats[SummaryKeys.SUMMARY][SummaryKeys.NUMERIC_COUNT],
                    categorical_count=stats[SummaryKeys.SUMMARY][SummaryKeys.CATEGORICAL_COUNT],
                    identifier_count=stats[SummaryKeys.SUMMARY][SummaryKeys.IDENTIFIER_COUNT])

        return stats

    except Exception as e:
        logger.error(LogMessages.MSG_CURVE_STATS_FAILED, error=str(e))
        return {SummaryKeys.ERROR: f"{LogMessages.MSG_CURVE_STATS_FAILED}: {str(e)}"}


def generate_multi_well_curve_statistics(
    df: pd.DataFrame,
    curve_metadata: 'CurveMetadata',
    wells: list[str]
) -> dict[str, Any]:
    """生成多井曲线统计分析（先按井统计，再整体统计）。

    Args:
        df: DataFrame数据
        curve_metadata: 曲线元数据
        wells: 井名列表

    Returns:
        dict: 多井曲线统计分析结果
            - numeric_curves: 数值型曲线统计（整体）
            - categorical_curves: 类别型曲线统计（整体）
            - identifier_curves: 标识型曲线统计（整体）
            - summary: 统计汇总信息（整体）
            - by_well_statistics: 按井的曲线统计
    """
    logger.debug("开始多井曲线统计分析",
                total_wells=len(wells),
                dataframe_shape=df.shape)

    try:
        # 获取井名曲线
        well_curve_names = curve_metadata.get_well_identifier_curves()
        if not well_curve_names:
            logger.warning("多井数据集中未找到井名曲线，回退到整体统计")
            return generate_curve_statistics(df, curve_metadata)

        # 获取第一个井名曲线的属性对象
        well_curve = curve_metadata.get_curve(well_curve_names[0])
        if well_curve is None:
            logger.warning(f"无法获取井名曲线属性 {well_curve_names[0]}，回退到整体统计")
            return generate_curve_statistics(df, curve_metadata)

        well_column = well_curve.dataframe_column_name
        if well_column not in df.columns:
            logger.warning(f"DataFrame中未找到井名列 {well_column}，回退到整体统计")
            return generate_curve_statistics(df, curve_metadata)

        # 1. 按井统计
        by_well_stats = {}
        for well_name in wells:
            well_data = df[df[well_column] == well_name]
            if len(well_data) > 0:
                logger.debug(f"统计井 {well_name}",
                           well_rows=len(well_data))
                well_stats = generate_curve_statistics(well_data, curve_metadata)
                by_well_stats[str(well_name)] = well_stats
            else:
                logger.warning(f"井 {well_name} 无数据")
                by_well_stats[str(well_name)] = {SummaryKeys.ERROR: "无数据"}

        # 2. 整体统计
        overall_stats = generate_curve_statistics(df, curve_metadata)

        # 3. 合并结果
        result = overall_stats.copy()
        result["by_well_statistics"] = by_well_stats

        logger.debug("多井曲线统计分析完成",
                    wells_analyzed=len(by_well_stats),
                    overall_numeric_count=overall_stats.get(SummaryKeys.SUMMARY, {}).get(SummaryKeys.NUMERIC_COUNT, 0))

        return result

    except Exception as e:
        logger.error("多井曲线统计分析失败", error=str(e))
        return {SummaryKeys.ERROR: f"多井曲线统计分析失败: {str(e)}"}


def _analyze_dataframe_columns(
    df: pd.DataFrame,
    curve_metadata: CurveMetadata
) -> dict[str, dict[str, Any]]:
    """直接分析DataFrame的每列数据。

    Args:
        df: DataFrame数据
        curve_metadata: 曲线元数据（用于获取曲线分类信息）

    Returns:
        dict: 按类型分类的统计结果
    """
    numeric_stats = {}
    categorical_stats = {}
    identifier_stats = {}

    for column_name in df.columns:
        try:
            series = df[column_name]

            # 获取对应的曲线元数据信息（如果存在）
            curve_info = curve_metadata.get_curve_by_dataframe_name(column_name)

            # 根据数据类型和曲线信息决定统计方法
            if _is_numeric_column(series, curve_info):
                numeric_stats[column_name] = _calculate_numeric_statistics(series)
            elif _is_categorical_column(series, curve_info):
                categorical_stats[column_name] = _calculate_categorical_statistics(series)
            elif _is_identifier_column(series, curve_info):
                identifier_stats[column_name] = _calculate_identifier_statistics(series)
            else:
                # 默认按数据类型处理
                if pd.api.types.is_numeric_dtype(series):
                    numeric_stats[column_name] = _calculate_numeric_statistics(series)
                else:
                    categorical_stats[column_name] = _calculate_categorical_statistics(series)

        except Exception as e:
            logger.warning(f"列 {column_name} 统计分析失败", error=str(e))
            # 将错误信息放入合适的分类中
            categorical_stats[column_name] = {SummaryKeys.ERROR: f"统计分析失败: {str(e)}"}

    return {
        SummaryKeys.NUMERIC_CURVES: numeric_stats,
        SummaryKeys.CATEGORICAL_CURVES: categorical_stats,
        SummaryKeys.IDENTIFIER_CURVES: identifier_stats
    }



def _is_numeric_column(series: pd.Series, curve_info: CurveBasicAttributes | None) -> bool:
    """判断列是否应该进行数值型统计。

    Args:
        series: 数据序列
        curve_info: 曲线信息（可选）

    Returns:
        bool: 是否为数值型列
    """
    # 如果有曲线信息，优先使用曲线信息判断
    if curve_info:
        return (curve_info.data_type in [WpDataType.FLOAT, WpDataType.INT] and
                curve_info.curve_class != WpCurveClass.CATEGORICAL and
                curve_info.category != WpCurveCategory.IDENTIFIER)

    # 否则根据数据类型判断
    return pd.api.types.is_numeric_dtype(series)


def _is_categorical_column(series: pd.Series, curve_info: CurveBasicAttributes | None) -> bool:
    """判断列是否应该进行类别型统计。

    Args:
        series: 数据序列
        curve_info: 曲线信息（可选）

    Returns:
        bool: 是否为类别型列
    """
    # 如果有曲线信息，优先使用曲线信息判断
    if curve_info:
        return (curve_info.curve_class == WpCurveClass.CATEGORICAL and
                curve_info.category != WpCurveCategory.IDENTIFIER)

    # 否则根据数据类型判断（字符串类型默认为类别型）
    return pd.api.types.is_string_dtype(series) or pd.api.types.is_object_dtype(series)


def _is_identifier_column(series: pd.Series, curve_info: CurveBasicAttributes | None) -> bool:
    """判断列是否应该进行标识型统计。

    Args:
        series: 数据序列
        curve_info: 曲线信息（可选）

    Returns:
        bool: 是否为标识型列
    """
    # 如果有曲线信息，优先使用曲线信息判断
    if curve_info:
        return curve_info.category == WpCurveCategory.IDENTIFIER
    else:
        return False

    # 否则根据列名模式判断（井名、深度等常见标识符）
    # column_name_lower = series.name.lower() if series.name else ""
    # identifier_patterns = ["well", "depth", "md", "tvd", "id", "name"]
    # return any(pattern in column_name_lower for pattern in identifier_patterns)





def _calculate_numeric_statistics(series: pd.Series) -> dict[str, Any]:
    """计算数值型曲线统计指标。

    Args:
        series: 数值型数据序列

    Returns:
        dict: 统计指标
    """
    # 移除非数值数据
    clean_series = series.dropna()

    if len(clean_series) == 0:
        return {
            SummaryKeys.COUNT: 0,
            SummaryKeys.MISSING: len(series),
            SummaryKeys.ERROR: LogMessages.MSG_NO_VALID_NUMERIC_DATA
        }

    try:
        stats = {
            SummaryKeys.COUNT: len(series),
            SummaryKeys.MISSING: int(series.isna().sum()),
            SummaryKeys.VALID_COUNT: len(clean_series),
            SummaryKeys.MIN: float(clean_series.min()),
            SummaryKeys.MAX: float(clean_series.max()),
            SummaryKeys.MEAN: float(clean_series.mean()),
            SummaryKeys.MEDIAN: float(clean_series.median()),
            SummaryKeys.STD: float(clean_series.std()),
            SummaryKeys.Q25: float(clean_series.quantile(0.25)),
            SummaryKeys.Q75: float(clean_series.quantile(0.75))
        }

        # 计算偏度和峰度（需要足够的数据点）
        if len(clean_series) >= 3:
            stats[SummaryKeys.SKEWNESS] = float(clean_series.skew())
        if len(clean_series) >= 4:
            stats[SummaryKeys.KURTOSIS] = float(clean_series.kurtosis())

        # 计算异常值（使用IQR方法）
        if len(clean_series) >= 4:
            iqr = stats[SummaryKeys.Q75] - stats[SummaryKeys.Q25]
            lower_bound = stats[SummaryKeys.Q25] - 1.5 * iqr
            upper_bound = stats[SummaryKeys.Q75] + 1.5 * iqr
            outliers = clean_series[(clean_series < lower_bound) | (clean_series > upper_bound)]
            stats[SummaryKeys.OUTLIER_COUNT] = len(outliers)
            stats[SummaryKeys.OUTLIER_PERCENTAGE] = float(len(outliers) / len(clean_series) * 100)

        return stats

    except Exception as e:
        return {SummaryKeys.ERROR: f"{LogMessages.ERROR_NUMERIC_CALC_FAILED}: {str(e)}"}


def _calculate_categorical_statistics(series: pd.Series) -> dict[str, Any]:
    """计算类别型曲线统计指标。

    Args:
        series: 类别型数据序列

    Returns:
        dict: 统计指标
    """
    try:
        value_counts = series.value_counts()

        stats = {
            SummaryKeys.COUNT: len(series),
            SummaryKeys.MISSING: int(series.isna().sum()),
            SummaryKeys.UNIQUE_VALUES: int(series.nunique()),
            SummaryKeys.MODE: series.mode().iloc[0] if len(series.mode()) > 0 else None,
            SummaryKeys.VALUE_DISTRIBUTION: value_counts.to_dict(),
            SummaryKeys.TOP_5_VALUES: value_counts.head(5).to_dict()
        }

        # 计算类别分布百分比
        if len(series) > 0:
            value_percentages = (value_counts / len(series) * 100).round(2)
            stats[SummaryKeys.VALUE_PERCENTAGES] = value_percentages.to_dict()

        return stats

    except Exception as e:
        return {SummaryKeys.ERROR: f"{LogMessages.ERROR_CATEGORICAL_CALC_FAILED}: {str(e)}"}


def _calculate_identifier_statistics(series: pd.Series) -> dict[str, Any]:
    """计算标识型曲线统计指标。

    注意：对于井名等标识符，重复值是正常现象（同一口井的所有行都有相同的井名）。

    Args:
        series: 标识型数据序列

    Returns:
        dict: 统计指标
    """
    try:
        stats = {
            SummaryKeys.COUNT: len(series),
            SummaryKeys.MISSING: int(series.isna().sum()),
            SummaryKeys.UNIQUE_VALUES: int(series.nunique()),
            SummaryKeys.COMPLETENESS: float((len(series) - series.isna().sum()) / len(series) * 100) if len(series) > 0 else 0.0
        }

        # 对于标识符，提供值分布信息
        if series.nunique() > 0:
            value_counts = series.value_counts()
            stats[SummaryKeys.MOST_COMMON] = value_counts.index[0]
            stats[SummaryKeys.MOST_COMMON_COUNT] = int(value_counts.iloc[0])

            # 提供值分布信息（而不是标记为"重复"）
            stats[SummaryKeys.VALUE_DISTRIBUTION] = value_counts.to_dict()

            # 对于井名标识符，重复是正常的；对于深度标识符，可能需要特别关注
            # 这里我们提供信息但不做价值判断
            if series.nunique() == 1:
                stats["identifier_type"] = "single_value"  # 单一值（如单井数据）
            elif series.nunique() == len(series):
                stats["identifier_type"] = "all_unique"   # 全部唯一（如连续深度）
            else:
                stats["identifier_type"] = "mixed"        # 混合情况（如多井数据或区间深度）

        return stats

    except Exception as e:
        return {SummaryKeys.ERROR: f"{LogMessages.ERROR_IDENTIFIER_CALC_FAILED}: {str(e)}"}
