"""logwp.extras.nmr.nmr_utils - NMR测井数据分析外部API

提供NMR测井数据分析的外部API接口，封装内部实现逻辑。

Architecture
------------
层次/依赖: extras层外部API，依赖internal实现
设计原则: 简洁API、参数验证、错误处理
性能特征: 透明代理、统一接口、结构化日志

主要功能：
- T2谱孔隙度划分：根据T2截止值划分微孔、中孔、大孔
- 数据验证：物理约束验证和数值稳定性检查
- 范围限制：支持用户自定义T2时间轴计算范围

References
----------
- 《SCAPE_MS_方法说明书》第3.1节 - NMR T2谱孔隙度划分
- 《SCAPE_WFS_WP文件规范》第5.4.3节 - T2_AXIS属性规范
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Optional, Tuple, Any

import structlog

from .internal.t2_porosity_compute import calculate_t2_porosity_components_impl, calculate_t2_porosity_components_vectorized_impl
from ..backend.internal.factory import create_backend_service_by_name, create_backend_service_from_module
from ..backend import BackendService
from .constants import LOG_OPERATION_COMPUTE, LOG_STAGE_START, LOG_STAGE_COMPLETE
from .exceptions import WpNmrError

if TYPE_CHECKING:
    from logwp.extras.backend import BackendService

# 获取logger
logger = structlog.get_logger("logwp.extras.nmr")


def calculate_t2_porosity_components(
    t2_spectrum: Any,
    t2_time: Any,
    t2cutoff_short: float,
    t2cutoff_long: float,
    *,
    backend_service: str | "BackendService" | Any = 'cpu',
    t2_range_min: Optional[float] = None,
    t2_range_max: Optional[float] = None,
) -> Tuple[float, float, float]:
    """计算单点T2谱孔隙度组分。

    根据T2截止值将T2谱划分为微孔、中孔、大孔三个组分，支持用户自定义T2时间轴范围。
    实现《SCAPE_MS_方法说明书》第3.1节的NMR T2谱孔隙度划分算法。

    Mathematical Foundation
    ----------------------
    在指定的T2时间轴范围内对孔隙度分量进行求和：

    V_micro = Σ T2_spectrum[i] for T2_time[i] in [T2_range_min, T2_short)
    V_meso  = Σ T2_spectrum[i] for T2_time[i] in [T2_short, T2_long)
    V_macro = Σ T2_spectrum[i] for T2_time[i] in [T2_long, T2_range_max]

    其中：
    - T2_range_min, T2_range_max: 用户指定的T2时间轴计算范围
    - T2_short: 短T2截止值，区分微孔和中孔
    - T2_long: 长T2截止值，区分中孔和大孔

    Args:
        t2_spectrum: T2谱孔隙度分布，一维数组，单位v/v
        t2_time: T2时间轴数组，单位ms，必须与t2_spectrum长度一致且单调递增
        t2cutoff_short: 短T2截止值，单位ms，区分微孔和中孔
        t2cutoff_long: 长T2截止值，单位ms，区分中孔和大孔
        backend (str | BackendService | Any, optional): 指定计算后端。
            可以是字符串 'cpu' 或 'gpu'，一个已经创建的 BackendService 实例，
            或一个 numpy/cupy 模块。默认为 'cpu'。
        t2_range_min: T2时间轴计算范围最小值，单位ms，默认为None（使用全部范围）
        t2_range_max: T2时间轴计算范围最大值，单位ms，默认为None（使用全部范围）

    Returns:
        Tuple[float, float, float]: (V_micro, V_meso, V_macro)
        - V_micro: 微孔孔隙度，单位v/v
        - V_meso: 中孔孔隙度，单位v/v
        - V_macro: 大孔孔隙度，单位v/v

    Raises:
        WpNmrError: NMR数据分析异常
        - T2谱数据维度错误
        - T2轴与T2谱数据长度不匹配
        - T2截止值顺序错误
        - 物理约束验证失败

    Examples:
        >>> import numpy as np
        >>>
        >>> # 创建T2时间轴（对数分布）
        >>> t2_time = np.logspace(-1, 4, 64)  # 0.1ms到10000ms，64个点
        >>>
        >>> # 模拟T2谱数据
        >>> t2_spectrum = np.random.random(64) * 0.3  # 孔隙度范围0-0.3
        >>>
        >>> # 计算孔隙度组分
        >>> v_micro, v_meso, v_macro = calculate_t2_porosity_components(
        ...     t2_spectrum=t2_spectrum,
        ...     t2_time=t2_time,
        ...     t2cutoff_short=3.0,    # 3ms
        ...     t2cutoff_long=33.0     # 33ms
        ... )
        >>>
        >>> # 在指定范围内计算
        >>> v_micro, v_meso, v_macro = calculate_t2_porosity_components(
        ...     t2_spectrum=t2_spectrum,
        ...     t2_time=t2_time,
        ...     t2cutoff_short=3.0,
        ...     t2cutoff_long=33.0,
        ...     t2_range_min=1.0,      # 只在1-1000ms范围内计算
        ...     t2_range_max=1000.0
        ... )

    References:
        《SCAPE_MS_方法说明书》第3.1节 - NMR T2谱孔隙度划分
        《SCAPE_WFS_WP文件规范》第5.4.3节 - T2_AXIS属性规范
    """
    logger.info(
        "开始T2谱孔隙度组分计算",
        operation=LOG_OPERATION_COMPUTE,
        stage=LOG_STAGE_START,
        t2cutoff_short=t2cutoff_short,
        t2cutoff_long=t2cutoff_long,
        t2_range=(t2_range_min, t2_range_max)
    )

    # 根据backend参数类型获取服务实例 (保持兼容性)
    service: BackendService
    if isinstance(backend_service, BackendService):
        service = backend_service
    elif isinstance(backend_service, str):
        service = create_backend_service_by_name(backend_service)
    elif hasattr(backend_service, '__name__'):
        service = create_backend_service_from_module(backend_service)
    else:
        raise TypeError(f"不支持的backend类型: {type(backend_service)}. 请提供 'cpu', 'gpu', a numpy/cupy module, 或 BackendService 实例。")

    try:
        # 调用内部实现
        result = calculate_t2_porosity_components_impl( # type: ignore
            t2_spectrum=t2_spectrum,
            t2_time=t2_time,
            t2cutoff_short=t2cutoff_short,
            t2cutoff_long=t2cutoff_long,
            service=service,
            t2_range_min=t2_range_min,
            t2_range_max=t2_range_max
        )

        # 返回值已经是标量，无需转换
        logger.info(
            "T2谱孔隙度组分计算完成",
            operation=LOG_OPERATION_COMPUTE,
            stage=LOG_STAGE_COMPLETE,
            v_micro=result[0],
            v_meso=result[1],
            v_macro=result[2],
            total_porosity=sum(result)
        )

        return result

    except Exception as e:
        logger.error(
            "T2谱孔隙度组分计算失败",
            operation=LOG_OPERATION_COMPUTE,
            stage="error",
            error=str(e),
            error_type=type(e).__name__
        )
        raise


def calculate_t2_porosity_components_vectorized(
    t2_spectrum_matrix: Any,
    t2_time: Any,
    t2cutoff_short: float,
    t2cutoff_long: float,
    *,
    t2_range_min: Optional[float] = None,
    t2_range_max: Optional[float] = None,
    backend_service: "BackendService",
    t2_spectrum_unit: str = "v/v"
) -> Tuple[Any, Any, Any]:
    """计算多点T2谱孔隙度组分（向量化版本）。

    批量处理多个深度点的T2谱数据，相比逐点调用calculate_t2_porosity_components
    具有显著的性能优势，特别适用于SCAPE Stage-II等大规模数据处理场景。

    Mathematical Foundation
    ----------------------
    对每个深度点i，在指定的T2时间轴范围内对孔隙度分量进行求和：

    V_micro[i] = Σ T2_spectrum[i, j] for T2_time[j] in [T2_range_min, T2_short)
    V_meso[i]  = Σ T2_spectrum[i, j] for T2_time[j] in [T2_short, T2_long)
    V_macro[i] = Σ T2_spectrum[i, j] for T2_time[j] in [T2_long, T2_range_max]

    向量化实现使用numpy广播和掩码操作，避免Python循环，显著提升性能。

    Performance Benefits
    -------------------
    - **内存效率**: 一次性分配结果数组，减少内存碎片
    - **计算效率**: 利用numpy向量化操作，避免Python循环开销
    - **缓存友好**: 连续内存访问模式，提高CPU缓存命中率
    - **并行优化**: numpy底层可利用SIMD指令和多线程

    Args:
        t2_spectrum_matrix: T2谱孔隙度分布矩阵，形状(n_depths, n_bins)
                           每行代表一个深度点的T2谱分布
        t2_time: T2时间轴数组，单位ms，必须与t2_spectrum_matrix的列数一致且单调递增
        t2cutoff_short: 短T2截止值，单位ms，区分微孔和中孔
        t2cutoff_long: 长T2截止值，单位ms，区分中孔和大孔
        t2_range_min: T2时间轴计算范围最小值，单位ms，默认为None（使用全部范围）
        t2_range_max: T2时间轴计算范围最大值，单位ms，默认为None（使用全部范围）
        service (BackendService): 用于计算的后端服务实例。
        t2_spectrum_unit (str, optional): T2谱数据的孔隙度单位。默认为"v/v"（体积分数）。
                                        支持"v/v"（0-1范围）和"percent"（0-100范围）。
                                        返回的孔隙度组分将与此单位保持一致。

    Returns:
        Tuple[Any, Any, Any]: (V_micro, V_meso, V_macro)
        - V_micro: 微孔孔隙度数组，形状(n_depths,)，单位与t2_spectrum_unit一致
        - V_meso: 中孔孔隙度数组，形状(n_depths,)，单位与t2_spectrum_unit一致
        - V_macro: 大孔孔隙度数组，形状(n_depths,)，单位与t2_spectrum_unit一致
        数组类型取决于所选的后端。

    Raises:
        WpNmrDataError: 输入数据格式错误
        WpNmrComputationError: 计算过程失败
        WpNmrPhysicsError: 物理约束验证失败

    Examples:
        >>> import numpy as np
        >>>
        >>> # 创建T2时间轴（对数分布）
        >>> t2_time = np.logspace(-1, 4, 64)  # 0.1ms到10000ms，64个点
        >>>
        >>> # 模拟多个深度点的T2谱数据
        >>> n_depths = 1000
        >>> n_bins = 64
        >>> t2_spectrum_matrix = np.random.random((n_depths, n_bins)) * 0.3
        >>>
        >>> # 向量化计算孔隙度组分
        >>> v_micro, v_meso, v_macro = calculate_t2_porosity_components_vectorized(
        ...     t2_spectrum_matrix=t2_spectrum_matrix,
        ...     t2_time=t2_time,
        ...     t2cutoff_short=3.0,    # 3ms
        ...     t2cutoff_long=33.0     # 33ms
        ... )
        >>>
        >>> # 结果验证
        >>> assert v_micro.shape == (n_depths,)
        >>> assert v_meso.shape == (n_depths,)
        >>> assert v_macro.shape == (n_depths,)
        >>> assert np.all(v_micro >= 0) and np.all(v_micro <= 1)

    References:
        《SCAPE_MS_方法说明书》第3.1节 - NMR T2谱孔隙度划分算法
        《SCAPE_DDS_Scape_Stage-II.md》第2.5.3节 - 性能优化需求
    """
    # 根据backend参数类型获取服务实例 (保持兼容性)
    service: BackendService
    if isinstance(backend_service, BackendService):
        service = backend_service
    elif isinstance(backend_service, str):
        service = create_backend_service_by_name(backend_service)
    elif hasattr(backend_service, '__name__'):
        service = create_backend_service_from_module(backend_service)
    else:
        raise TypeError(f"不支持的backend类型: {type(backend_service)}. 请提供 'cpu', 'gpu', a numpy/cupy module, 或 BackendService 实例。")

    try:
        # 调用内部向量化实现
        result = calculate_t2_porosity_components_vectorized_impl(
            t2_spectrum_matrix=t2_spectrum_matrix,
            t2_time=t2_time,
            t2cutoff_short=t2cutoff_short,
            t2cutoff_long=t2cutoff_long,
            service=service,
            t2_range_min=t2_range_min,
            t2_range_max=t2_range_max,
            t2_spectrum_unit=t2_spectrum_unit
        )

        # 使用service.as_scalar确保日志记录的健壮性
        logger.info(
            "向量化T2谱孔隙度组分计算完成",
            operation=LOG_OPERATION_COMPUTE,
            stage=LOG_STAGE_COMPLETE,
            n_depths=t2_spectrum_matrix.shape[0],
            n_bins=t2_spectrum_matrix.shape[1],
            v_micro_range=(backend_service.as_scalar(backend_service.min(result[0])), backend_service.as_scalar(backend_service.max(result[0]))),
            v_meso_range=(backend_service.as_scalar(backend_service.min(result[1])), backend_service.as_scalar(backend_service.max(result[1]))),
            v_macro_range=(backend_service.as_scalar(backend_service.min(result[2])), backend_service.as_scalar(backend_service.max(result[2])))
        )

        return result

    except Exception as e:
        logger.error(
            "向量化T2谱孔隙度组分计算失败",
            operation=LOG_OPERATION_COMPUTE,
            stage="error",
            error=str(e),
            error_type=type(e).__name__,
            input_shape=t2_spectrum_matrix.shape if hasattr(t2_spectrum_matrix, 'shape') else 'unknown'
        )
        raise
