"""logwp.extras.petroplot.nmr_ternary.plot_profiles - NMR三元图绘图模板

本模块定义并注册了NMR三元图组件所需的默认PlotProfile模板。
遵循 `logwp.extras.plotting` 的两级继承体系，提供一个基础模板和一个
默认的具体图表模板。

- `petroplot.nmr_ternary.base`: 模块级基础模板，定义通用样式。
- `petroplot.nmr_ternary.default`: 默认的具体图表模板，继承基础模板并
  添加了更详细的样式配置。

Architecture
------------
层次/依赖: petroplot/nmr_ternary绘图模板层，被facade层使用
设计原则: 样式与逻辑分离、可复用、可定制
"""

from logwp.extras.plotting import PlotProfile, SaveConfig, registry
from .constants import NmrTernaryPlotProfiles


def register_nmr_ternary_profiles():
    """
    将本模块中定义的PlotProfile注册到全局注册表中。
    这是一个良好的实践，可以避免在模块导入时产生副作用。
    该函数应在应用程序启动时或使用该组件前被调用。
    """
    # 1. 定义并注册模块级基础模板 (Module-level Base Profile)
    # 这个模板定义了所有NMR三元图共享的通用样式，如字体、图表尺寸和坐标轴/网格线的基础样式。
    # 它的配置来源于对 `snippets` 中原始脚本的分析，提取了其中的共性。
    base_profile = PlotProfile(
        name=NmrTernaryPlotProfiles.BASE.value,
        # rc_params 用于设置通用的matplotlib-like参数，这里主要控制字体
        rc_params={
            "font.family": "Arial",
            "font.size": 12,
            "legend.fontsize": 11,
        },
        # figure_props 控制Plotly图表的整体画布属性
        figure_props={
            "width": 800,
            "height": 700,
        },
        # artist_props 是一个灵活的字典，用于存放特定绘图元素的样式。
        # 这里的键名 (如 "ternary_axis") 是我们自定义的，将在 internal/plotter.py 中被解析使用。
        artist_props={
            "ternary_axis": {
                "linewidth": 2,
                "ticks": "outside",
            },
            "ternary_grid": {
                "width": 0.5,
                "dash": "dot",
            },
        },
        # save_config 定义了默认的图表保存选项
        save_config=SaveConfig(
            format=["html", "png", "svg"],
            dpi=300,
            bbox_inches="tight"
        )
    )

    # 2. 定义并注册默认的具体图表模板 (Default Specific Profile)
    # 这个模板继承自基础模板，并为具体图表元素（如数据点、颜色条）提供了更详细的样式。
    default_profile = PlotProfile(
        name=NmrTernaryPlotProfiles.DEFAULT.value,
        # title_props 控制图表主标题的样式
        title_props={
            "fontsize": 18,
            "fontweight": "bold",
            "pad": 15,
            "x": 0.5,  # 标题居中
        },
        # artist_props 在基础模板之上进行扩展
        artist_props={
            # 主数据点的样式
            "data_marker": {
                "size": 8,
                "line": {"width": 0.5, "color": "white"},
            },
            # 颜色条的样式
            "colorbar": {
                "thickness": 15,
                "len": 0.7,
                "tickfont": {"size": 11},
                "title_side": "right",
            },
            # 背景分区的样式
            "background_region": {
                "line": {"width": 0},  # 默认不显示分区的边界线
            },
        }
    )

    if not registry.is_registered(base_profile.name):
        registry.register_base(base_profile)
    if not registry.is_registered(default_profile.name):
        registry.register(default_profile)


# 在模块首次导入时自动注册，方便使用
register_nmr_ternary_profiles()
