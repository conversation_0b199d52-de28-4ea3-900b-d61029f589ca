"""logwp.extras.petroplot.common.layout_manager - 通用布局管理器

本模块定义了 `LayoutManager`，一个无状态的辅助类，负责应用通用的、
与图例无关的Plotly布局属性。

职责:
- 管理图表标题、全局字体、基础边距和画布背景色。
- 不处理图例 (`legend`)、颜色轴 (`coloraxis`) 或任何特定于图表的坐标轴。
- 不处理图表尺寸 (`width`, `height`)，此职责由作为“总指挥”的绘图器承担。

设计原则:
- 组合优于继承：被具体的绘图器实例化并调用。
- 单一职责原则：只处理通用布局，分离了图例管理的复杂性。
"""

from typing import Optional, Dict, Any, List

from logwp.extras.plotting import PlotProfile


class LayoutManager:
    """
    一个无状态的辅助类，用于构建通用的Plotly布局配置。
    """

    def __init__(
        self,
        config: Any,
        plot_profile: PlotProfile,
        subtitle: Optional[str] = None,
    ):
        self.config = config
        self.plot_profile = plot_profile
        self.subtitle = subtitle

    def build_base_layout(self) -> Dict[str, Any]:
        """构建基础布局更新字典，不包含图例或特定坐标轴的配置。"""
        title_text = ""
        if self.config.show_title:
            title_text = self.config.title
            if self.subtitle:
                title_text += f"<br><sub>{self.subtitle}</sub>"

        margin_props = self.config.margin.copy() if self.config.margin else {}

        layout_updates = {
            'title': {
                'text': title_text,
                'x': self.plot_profile.title_props.get("x", 0.5),
                'font': {'size': self.plot_profile.title_props.get("fontsize", 18), 'family': self.plot_profile.rc_params.get("font.family", "Arial")}
            },
            'margin': margin_props,
            'font': {'family': self.plot_profile.rc_params.get("font.family", "Arial"), 'size': self.plot_profile.rc_params.get("font.size", 12)},
            'paper_bgcolor': self.plot_profile.rc_params.get("figure.facecolor", "white"),
        }
        return layout_updates
