#!/usr/bin/env python3
"""连续型数据集深度重采样服务模块。

提供WpContinuousDataset深度重采样的核心逻辑。

Architecture
------------
层次/依赖: datasets/service层，连续数据重采样业务逻辑
设计原则: Service Layer模式、向量化插值、类型安全
性能特征: 高效插值处理、内存优化、批量处理

Examples:
    >>> # 基本重采样
    >>> df, metadata, depth_range = resample_continuous_dataset(
    ...     continuous_dataset, new_sampling_interval=0.25
    ... )

    >>> # 扩展深度范围重采样
    >>> df, metadata, depth_range = resample_continuous_dataset(
    ...     continuous_dataset,
    ...     new_sampling_interval=0.5,
    ...     depth_range=(2490.0, 2530.0),
    ...     interpolation_method="linear",
    ...     out_of_range_fill_value=-999
    ... )
"""

from __future__ import annotations

import copy
from datetime import datetime
from typing import TYPE_CHECKING, Any

import numpy as np
import pandas as pd

from logwp.models.constants import WpCurveCategory, WpDepthRole, WpStandardColumn
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata
from logwp.models.exceptions import WpDataError, WpValidationError
from logwp.infra.exceptions import ErrorContext
from logwp.models.utils.interpolation_utils import apply_interpolation_distance_limit
from logwp.infra import get_logger

# 导入scipy用于插值
try:
    from scipy.interpolate import interp1d
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

if TYPE_CHECKING:
    from logwp.models.datasets.continuous import WpContinuousDataset

logger = get_logger(__name__)


def resample_continuous_dataset(
    continuous_dataset: "WpContinuousDataset",
    new_sampling_interval: float,
    *,
    depth_range: tuple[float, float] | None = None,
    interpolation_method: str = "nearest",
    max_interpolation_distance: float | None = None,
    out_of_range_fill_value: Any = np.nan
) -> tuple[pd.DataFrame, CurveMetadata, tuple[float, float]]:
    """对连续型数据集进行深度重采样。

    实现连续数据的深度重采样，支持单井和多井数据，以及多种插值方法和
    自定义深度范围。

    Args:
        continuous_dataset: 源连续型数据集
        new_sampling_interval: 新的深度采样间隔（必须 > 0）
        depth_range: 输出深度范围，None表示使用原数据集范围
        interpolation_method: 插值方法，默认"nearest"
        max_interpolation_distance: 最大插值距离，None表示使用默认值 new_sampling_interval / 2
                                     超出此距离的点将被填充为NaN。
        out_of_range_fill_value: 超出原数据集范围的填充值
    Returns:
        tuple[pd.DataFrame, CurveMetadata, tuple[float, float]]:
            (重采样后的DataFrame, 新的曲线元数据, 实际深度范围)

    Raises:
        WpValidationError: 当参数验证失败时抛出
        WpDataError: 当数据集状态异常时抛出

    Note:
        - 重采样后的深度曲线名称保持为原名称
        - 保留原有的井名曲线
        - 使用pandas插值方法进行数据重采样
        - 智能处理多井数据：
          - 如果 `depth_range` 未指定，则按每口井各自的深度范围进行重采样。
          - 如果 `depth_range` 已指定，则将所有井的数据重采样到该统一深度范围。
        - 深度范围会按采样间隔对齐
        - 插值方法优先级：强制方法 > 数据类型约束 > 用户指定方法

    Examples:
        >>> # 基本重采样
        >>> df, metadata, depth_range = resample_continuous_dataset(
        ...     continuous_dataset, new_sampling_interval=0.5
        ... )
        >>> assert len(df) >= len(continuous_dataset.df)  # 更密集的采样

        >>> # 扩展深度范围重采样
        >>> df, metadata, depth_range = resample_continuous_dataset(
        ...     continuous_dataset,
        ...     new_sampling_interval=1.0,
        ...     depth_range=(2490.0, 2530.0),  # 扩展范围
        ...     interpolation_method="linear",
        ...     out_of_range_fill_value=-999
        ... )
    """
    logger.info(
        "开始连续型数据集深度重采样",
        operation="resample_continuous_dataset",
        dataset_name=str(continuous_dataset.name),
        original_sampling_interval=continuous_dataset.depth_sampling_rate,
        new_sampling_interval=new_sampling_interval,
        custom_depth_range=depth_range is not None,
        interpolation_method=interpolation_method,
        out_of_range_fill_value=str(out_of_range_fill_value)
    )

    # 1.a. 确定实际的最大插值距离
    actual_max_dist = (
        max_interpolation_distance
        if max_interpolation_distance is not None
        else new_sampling_interval / 2
    )
    logger.debug(
        f"使用的最大插值距离: {actual_max_dist}", operation="resample_continuous_dataset"
    )

    # 1. 参数验证
    _validate_resample_parameters(continuous_dataset, new_sampling_interval, depth_range, interpolation_method)

    # 2. 获取源数据集信息
    source_df = continuous_dataset.df
    source_metadata = continuous_dataset.curve_metadata

    # 3. 获取深度和井名曲线信息
    depth_curves = source_metadata.get_depth_reference_curves()
    if not depth_curves:
        raise WpDataError(
            "连续型数据集缺少深度参考曲线",
            context=ErrorContext(
                operation="resample_continuous_dataset",
                dataset_name=str(continuous_dataset.name)
            )
        )

    depth_curve_name = depth_curves[0]
    well_curves = source_metadata.get_well_identifier_curves()

    # 4. 按井分别处理或使用指定深度范围
    if depth_range is not None:
        # 用户指定了深度范围，使用传统逻辑
        target_depth_range = depth_range
        new_depth_sequence = _generate_aligned_depth_sequence(target_depth_range, new_sampling_interval)
        actual_depth_range = (new_depth_sequence[0], new_depth_sequence[-1])

        new_df = _create_resampled_dataframe_with_fixed_range(
            source_df, new_depth_sequence, depth_curve_name, well_curves,
            source_metadata, interpolation_method, target_depth_range, out_of_range_fill_value,
            max_interpolation_distance=actual_max_dist
        )
    else:
        # 按井分别处理，避免跨井的无意义深度范围
        new_df, actual_depth_range = _create_resampled_dataframe_per_well(
            source_df, depth_curve_name, well_curves, source_metadata,
            new_sampling_interval, interpolation_method, out_of_range_fill_value,
            max_interpolation_distance=actual_max_dist
        )

    # 7. 创建新的曲线元数据
    new_metadata = _create_resampled_metadata(
        source_metadata, new_sampling_interval
    )

    logger.info(
        "连续型数据集深度重采样完成",
        operation="resample_continuous_dataset",
        dataset_name=str(continuous_dataset.name),
        original_rows=len(source_df),
        resampled_rows=len(new_df),
        actual_depth_range=actual_depth_range,
        depth_points="per_well_calculated"
    )

    return new_df, new_metadata, actual_depth_range


def _validate_resample_parameters(
    continuous_dataset: "WpContinuousDataset",
    new_sampling_interval: float,
    depth_range: tuple[float, float] | None,
    interpolation_method: str
) -> None:
    """验证重采样参数的有效性。

    Args:
        continuous_dataset: 连续型数据集
        new_sampling_interval: 新的深度采样间隔
        depth_range: 深度范围
        interpolation_method: 插值方法

    Raises:
        WpValidationError: 当参数无效时抛出
        WpDataError: 当数据集状态异常时抛出
    """
    # 验证采样间隔
    if new_sampling_interval <= 0:
        raise WpValidationError(
            "深度采样间隔必须为正数",
            context=ErrorContext(
                operation="validate_resample_parameters",
                dataset_name=str(continuous_dataset.name),
                additional_info={
                    "new_sampling_interval": new_sampling_interval,
                    "requirement": "new_sampling_interval > 0"
                }
            )
        )

    # 验证数据集状态
    if continuous_dataset.df.empty:
        raise WpDataError(
            "连续型数据集为空，无法进行重采样",
            context=ErrorContext(
                operation="validate_resample_parameters",
                dataset_name=str(continuous_dataset.name),
                additional_info={"reason": "empty_dataset"}
            )
        )

    # 验证数据点数量（至少需要2个点进行插值）
    if len(continuous_dataset.df) < 2:
        raise WpDataError(
            "连续型数据集数据点不足，至少需要2个数据点进行插值",
            context=ErrorContext(
                operation="validate_resample_parameters",
                dataset_name=str(continuous_dataset.name),
                additional_info={
                    "current_points": len(continuous_dataset.df),
                    "minimum_required": 2
                }
            )
        )

    # 验证深度范围
    if depth_range is not None:
        if len(depth_range) != 2 or depth_range[0] >= depth_range[1]:
            raise WpValidationError(
                "深度范围格式无效",
                context=ErrorContext(
                    operation="validate_resample_parameters",
                    dataset_name=str(continuous_dataset.name),
                    additional_info={
                        "depth_range": depth_range,
                        "requirement": "tuple[float, float] with start < end"
                    }
                )
            )

        # 验证深度范围是否大于等于原数据集范围（按井验证）
        wells_ranges = continuous_dataset.get_wells_depth_ranges()
        for well, well_range in wells_ranges.items():
            if (depth_range[0] > well_range[0] or depth_range[1] < well_range[1]):
                raise WpValidationError(
                    f"指定的深度范围必须大于等于所有井的深度范围，井{well}超出范围",
                    context=ErrorContext(
                        operation="validate_resample_parameters",
                        dataset_name=str(continuous_dataset.name),
                        additional_info={
                            "specified_range": depth_range,
                            "well_range": well_range,
                            "well_name": well,
                            "requirement": "specified_range must contain or equal all wells' ranges"
                    }
                )
            )

    # 验证插值方法（基本验证，具体方法由pandas验证）
    if not isinstance(interpolation_method, str) or not interpolation_method.strip():
        raise WpValidationError(
            "插值方法必须为非空字符串",
            context=ErrorContext(
                operation="validate_resample_parameters",
                dataset_name=str(continuous_dataset.name),
                additional_info={
                    "interpolation_method": interpolation_method,
                    "requirement": "non-empty string"
                }
            )
        )


def _generate_aligned_depth_sequence(
    depth_range: tuple[float, float],
    sampling_interval: float
) -> np.ndarray:
    """生成对齐的深度序列。

    Args:
        depth_range: 深度范围
        sampling_interval: 采样间隔

    Returns:
        np.ndarray: 对齐的深度序列
    """
    depth_start, depth_end = depth_range

    # 深度边界对齐
    aligned_start = np.floor(depth_start / sampling_interval) * sampling_interval
    aligned_end = np.ceil(depth_end / sampling_interval) * sampling_interval

    # 生成深度序列
    depth_sequence = np.arange(aligned_start, aligned_end + sampling_interval, sampling_interval)

    logger.debug(
        "深度序列生成完成",
        operation="generate_aligned_depth_sequence",
        original_range=depth_range,
        aligned_range=(aligned_start, aligned_end),
        sampling_interval=sampling_interval,
        depth_points=len(depth_sequence)
    )

    return depth_sequence


def _create_resampled_dataframe(
    source_df: pd.DataFrame,
    new_depth_sequence: np.ndarray,
    depth_column: str,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any,
    max_interpolation_distance: float
) -> pd.DataFrame:
    """创建重采样后的DataFrame。

    🚨 **重要修复**：现在支持多井数据的正确重采样处理。

    修复说明：
    - 检测输入数据是否包含多口井
    - 单井数据：使用原有的高效重采样逻辑
    - 多井数据：按井分组，对每口井单独重采样，然后合并结果
    - 确保所有井的数据都被正确保留，包括部分曲线为空的情况

    Args:
        source_df: 源连续型数据集的DataFrame
        new_depth_sequence: 新的深度序列
        depth_column: 深度列名
        well_curves: 井名曲线列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法
        original_depth_range: 原数据集深度范围
        out_of_range_fill_value: 超出原数据集范围的填充值
        max_interpolation_distance: 最大插值距离

    Returns:
        pd.DataFrame: 重采样后的DataFrame
    """
    # 🚨 关键修复：检测并正确处理多井数据
    #
    # 修复逻辑：
    # 1. 检查是否包含多口井的数据
    # 2. 单井数据：使用原有高效逻辑
    # 3. 多井数据：按井分组重采样，确保所有井数据都被保留

    # 检测井的数量
    unique_wells = []
    if well_curves and well_curves[0] in source_df.columns:
        unique_wells = source_df[well_curves[0]].dropna().unique().tolist()

    if len(unique_wells) <= 1:
        # 🟢 单井数据：使用原有的高效重采样逻辑
        logger.debug(
            "单井数据重采样",
            operation="create_resampled_dataframe",
            wells_count=len(unique_wells),
            well_name=unique_wells[0] if unique_wells else "unknown"
        )
        return _create_single_well_resampled_dataframe(
            source_df, new_depth_sequence, depth_column, well_curves,
            curve_metadata, default_interpolation_method, original_depth_range, out_of_range_fill_value,
            max_interpolation_distance
        )
    else:
        # 🔧 多井数据：按井分组重采样，然后合并结果
        logger.debug(
            "多井数据重采样",
            operation="create_resampled_dataframe",
            wells_count=len(unique_wells),
            wells=unique_wells
        )
        return _create_multi_well_resampled_dataframe(
            source_df, new_depth_sequence, depth_column, well_curves,
            curve_metadata, default_interpolation_method, original_depth_range, out_of_range_fill_value,
            max_interpolation_distance
        )


def _create_single_well_resampled_dataframe(
    source_df: pd.DataFrame,
    new_depth_sequence: np.ndarray,
    depth_column: str,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any,
    max_interpolation_distance: float
) -> pd.DataFrame:
    """为单井数据创建重采样后的DataFrame（原有逻辑）。

    这是原有的高效重采样逻辑，适用于单井数据。

    Args:
        source_df: 源连续型数据集的DataFrame
        new_depth_sequence: 新的深度序列
        depth_column: 深度列名
        well_curves: 井名曲线列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法
        original_depth_range: 原数据集深度范围
        out_of_range_fill_value: 超出原数据集范围的填充值
        max_interpolation_distance: 最大插值距离

    Returns:
        pd.DataFrame: 重采样后的DataFrame
    """
    # 创建基础DataFrame（使用默认整数索引，符合CDP-1规范）
    new_df = pd.DataFrame({depth_column: new_depth_sequence})

    # 获取井名（单井数据，取第一个井名即可）
    well_name = None
    if well_curves and well_curves[0] in source_df.columns:
        well_name_series = source_df[well_curves[0]].dropna()
        if not well_name_series.empty:
            well_name = well_name_series.iloc[0]

    # 添加井名列
    if well_name is not None and well_curves:
        new_df[well_curves[0]] = well_name

    # 获取需要重采样的数据曲线（排除深度和井名曲线）
    exclude_columns = {depth_column}
    if well_curves:
        exclude_columns.update(well_curves)

    data_columns = [col for col in source_df.columns if col not in exclude_columns]

    # 执行插值重采样
    interpolated_df = _interpolate_continuous_curves(
        source_df, new_depth_sequence, depth_column, data_columns,
        curve_metadata, default_interpolation_method
    )

    # [核心改进] 应用最大插值距离限制
    valid_source_depths = source_df[depth_column].dropna().to_numpy()
    for col in interpolated_df.columns:
        interpolated_df[col] = apply_interpolation_distance_limit(
            interpolated_df[col].to_numpy(), new_depth_sequence,
            valid_source_depths, max_interpolation_distance
        )

    # 合并插值结果（使用pd.concat避免DataFrame碎片化）
    # 筛选出实际存在的数据列
    available_columns = [col for col in data_columns if col in interpolated_df.columns]

    if available_columns:
        # 使用pd.concat一次性合并所有列，避免逐列赋值导致的碎片化
        interpolated_subset = interpolated_df[available_columns].copy()
        # 重置索引确保与new_df对齐
        interpolated_subset.index = new_df.index
        new_df = pd.concat([new_df, interpolated_subset], axis=1)

        # 记录调试信息
        for column in available_columns:
            logger.debug(
                "数据列重采样完成",
                operation="create_single_well_resampled_dataframe",
                column_name=column,
                has_nan_values=new_df[column].isna().any(),
                sample_values=new_df[column].head().tolist()
            )

    # 处理超出范围的数据点
    _fill_out_of_range_values(new_df, new_depth_sequence, original_depth_range,
                             data_columns, out_of_range_fill_value)

    logger.debug(
        "单井重采样DataFrame创建完成",
        operation="create_single_well_resampled_dataframe",
        depth_points=len(new_depth_sequence),
        data_columns=len(data_columns),
        well_name=well_name,
        columns=list(new_df.columns)
    )

    return new_df


def _create_multi_well_resampled_dataframe(
    source_df: pd.DataFrame,
    new_depth_sequence: np.ndarray,
    depth_column: str,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any,
    max_interpolation_distance: float
) -> pd.DataFrame:
    """为多井数据创建重采样后的DataFrame（新增逻辑）。

    🚨 **关键修复**：正确处理多井数据的重采样。

    修复策略：
    1. 按井名分组，对每口井单独重采样
    2. 为每口井生成完整的深度序列
    3. 合并所有井的重采样结果
    4. 保留部分曲线为空的数据（NaN值）

    Args:
        source_df: 源连续型数据集的DataFrame
        new_depth_sequence: 新的深度序列
        depth_column: 深度列名
        well_curves: 井名曲线列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法
        original_depth_range: 原数据集深度范围
        out_of_range_fill_value: 超出原数据集范围的填充值
        max_interpolation_distance: 最大插值距离

    Returns:
        pd.DataFrame: 重采样后的DataFrame
    """
    if not well_curves or well_curves[0] not in source_df.columns:
        raise ValueError("多井数据重采样需要井名曲线")

    well_column = well_curves[0]
    unique_wells = source_df[well_column].dropna().unique()

    # 获取需要重采样的数据曲线（排除深度和井名曲线）
    exclude_columns = {depth_column}
    if well_curves:
        exclude_columns.update(well_curves)
    data_columns = [col for col in source_df.columns if col not in exclude_columns]

    # 存储每口井的重采样结果
    well_dataframes = []

    for well_name in unique_wells:
        logger.debug(
            "开始处理单井重采样",
            operation="create_multi_well_resampled_dataframe",
            well_name=well_name,
            data_columns=data_columns
        )

        # 提取当前井的数据
        well_data = source_df[source_df[well_column] == well_name].copy()

        if well_data.empty:
            logger.warning(
                "井数据为空，跳过",
                operation="create_multi_well_resampled_dataframe",
                well_name=well_name
            )
            continue

        # 为当前井创建重采样DataFrame
        well_resampled_df = pd.DataFrame({depth_column: new_depth_sequence})
        well_resampled_df[well_column] = well_name

        # 先批量处理缺失的曲线，避免逐列赋值
        missing_columns = [col for col in data_columns if col not in well_data.columns]
        if missing_columns:
            # 使用pd.concat批量添加缺失列，避免逐列赋值导致的碎片化
            missing_df = pd.DataFrame(
                {col: np.nan for col in missing_columns},
                index=well_resampled_df.index
            )
            well_resampled_df = pd.concat([well_resampled_df, missing_df], axis=1)

        # 对当前井的每个存在的数据曲线进行插值
        existing_columns = [col for col in data_columns if col in well_data.columns]
        for column in existing_columns:

            # 获取当前井当前曲线的有效数据
            valid_mask = well_data[column].notna() & well_data[depth_column].notna()
            valid_data = well_data[valid_mask]

            if valid_data.empty:
                # 当前井的当前曲线没有有效数据，填充NaN
                well_resampled_df[column] = np.nan
                logger.debug(
                    "井曲线无有效数据，填充NaN",
                    operation="create_multi_well_resampled_dataframe",
                    well_name=well_name,
                    column=column
                )
                continue

            # 执行插值
            try:
                # 获取曲线的推荐插值方法
                curve_attrs = curve_metadata.get_curve(column)
                if curve_attrs is not None:
                    interpolation_method = curve_attrs.get_recommended_interpolation_method(
                        default_interpolation_method
                    )
                else:
                    interpolation_method = default_interpolation_method

                # 执行插值
                interpolated_values = _interpolate_single_curve(
                    valid_data[depth_column].values,
                    valid_data[column].values,
                    new_depth_sequence,
                    interpolation_method
                )

                # [核心改进] 应用最大插值距离限制
                valid_source_depths = valid_data[depth_column].to_numpy()
                interpolated_values = apply_interpolation_distance_limit(
                    interpolated_values, new_depth_sequence,
                    valid_source_depths, max_interpolation_distance
                )

                logger.debug(
                    "井曲线插值完成",
                    operation="create_multi_well_resampled_dataframe",
                    well_name=well_name,
                    column=column,
                    valid_points=len(valid_data),
                    interpolated_points=len(interpolated_values),
                    has_nan_values=pd.Series(interpolated_values).isna().any()
                )

            except Exception as e:
                logger.warning(
                    "井曲线插值失败，填充NaN",
                    operation="create_multi_well_resampled_dataframe",
                    well_name=well_name,
                    column=column,
                    error=str(e)
                )
                well_resampled_df[column] = np.nan

        # 处理超出范围的数据点
        well_depth_range = (
            well_data[depth_column].min(),
            well_data[depth_column].max()
        )
        _fill_out_of_range_values(
            well_resampled_df, new_depth_sequence, well_depth_range,
            data_columns, out_of_range_fill_value
        )

        well_dataframes.append(well_resampled_df)

        logger.debug(
            "单井重采样完成",
            operation="create_multi_well_resampled_dataframe",
            well_name=well_name,
            resampled_rows=len(well_resampled_df)
        )

    # 合并所有井的重采样结果
    if not well_dataframes:
        # 没有有效的井数据，返回空DataFrame
        logger.warning(
            "没有有效的井数据",
            operation="create_multi_well_resampled_dataframe"
        )
        return pd.DataFrame()

    # 使用concat合并所有井的数据
    result_df = pd.concat(well_dataframes, ignore_index=True)

    logger.debug(
        "多井重采样DataFrame创建完成",
        operation="create_multi_well_resampled_dataframe",
        wells_count=len(unique_wells),
        total_rows=len(result_df),
        columns=list(result_df.columns)
    )

    return result_df


def _interpolate_single_curve(
    source_depths: np.ndarray,
    source_values: np.ndarray,
    target_depths: np.ndarray,
    interpolation_method: str
) -> np.ndarray:
    """对单个曲线进行插值。

    Args:
        source_depths: 源深度数组
        source_values: 源数值数组
        target_depths: 目标深度数组
        interpolation_method: 插值方法

    Returns:
        np.ndarray: 插值后的数值数组
    """
    if not SCIPY_AVAILABLE:
        # 如果scipy不可用，回退到numpy的基本插值
        if interpolation_method == "nearest":
            # 最近邻插值的简单实现
            indices = np.searchsorted(source_depths, target_depths)
            indices = np.clip(indices, 0, len(source_values) - 1)
            return source_values[indices]
        else:
            # 线性插值
            return np.interp(target_depths, source_depths, source_values)

    if interpolation_method == "nearest":
        if source_values.dtype == 'object':
            # 字符串类型最近邻插值
            unique_values = pd.Series(source_values).unique()
            value_map = {val: i for i, val in enumerate(unique_values)}
            reverse_map = {i: val for val, i in value_map.items()}
            numeric_values = np.array([value_map[val] for val in source_values])

            f = interp1d(
                source_depths, numeric_values,
                kind='nearest', bounds_error=False, fill_value='extrapolate'
            )
            numeric_result = f(target_depths)

            return np.array([
                reverse_map.get(int(val), np.nan) if not np.isnan(val) else np.nan
                for val in numeric_result
            ])
        else:
            # 数值型最近邻插值
            f = interp1d(
                source_depths, source_values,
                kind='nearest', bounds_error=False, fill_value='extrapolate'
            )
            return f(target_depths)

    elif interpolation_method == "linear":
        # 线性插值（仅适用于数值型）
        return np.interp(target_depths, source_depths, source_values)

    else:
        # 其他方法使用scipy.interpolate
        try:
            f = interp1d(
                source_depths, source_values,
                kind=interpolation_method, bounds_error=False, fill_value='extrapolate'
            )
            return f(target_depths)
        except ValueError:
            # 如果插值方法不支持，回退到线性插值
            return np.interp(target_depths, source_depths, source_values)


def _interpolate_continuous_curves(
    source_df: pd.DataFrame,
    new_depth_sequence: np.ndarray,
    depth_column: str,
    data_columns: list[str],
    curve_metadata: CurveMetadata,
    default_interpolation_method: str
) -> pd.DataFrame:
    """对连续型曲线数据进行插值处理。

    Args:
        source_df: 源DataFrame
        new_depth_sequence: 目标深度序列
        depth_column: 深度列名
        data_columns: 数据列名列表
        curve_metadata: 曲线元数据
        default_interpolation_method: 默认插值方法

    Returns:
        pd.DataFrame: 插值后的DataFrame（使用默认整数索引）
    """
    # 获取源数据并排序
    temp_df = source_df.copy()
    temp_df = temp_df.sort_values(by=depth_column).reset_index(drop=True)

    # 使用字典收集插值结果，避免逐列赋值导致的DataFrame碎片化
    interpolated_data = {}

    # 对每个数据曲线进行插值
    for column in data_columns:
        curve_attrs = curve_metadata.get_curve(column)
        if curve_attrs is not None:
            # 使用CurveBasicAttributes的智能插值方法选择
            interpolation_method = curve_attrs.get_recommended_interpolation_method(
                default_interpolation_method
            )
        else:
            # 如果找不到曲线属性，使用默认方法
            interpolation_method = default_interpolation_method

        try:
            # 检查数据类型
            column_data = temp_df[column].dropna()
            if column_data.empty:
                # 如果列为空，填充NaN
                interpolated_values = np.full(len(new_depth_sequence), np.nan)
            elif column_data.dtype == 'object' or interpolation_method == "nearest":
                # 字符串类型或最近邻插值
                from scipy.interpolate import interp1d

                # 为字符串数据创建数值映射
                if column_data.dtype == 'object':
                    unique_values = column_data.unique()
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    reverse_map = {i: val for val, i in value_map.items()}

                    # 转换为数值
                    numeric_values = temp_df[column].map(value_map).values

                    # 最近邻插值
                    f = interp1d(
                        temp_df[depth_column].values,
                        numeric_values,
                        kind='nearest',
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    numeric_result = f(new_depth_sequence)

                    # 转换回字符串
                    interpolated_values = np.array([
                        reverse_map.get(int(val), np.nan) if not np.isnan(val) else np.nan
                        for val in numeric_result
                    ])
                else:
                    # 数值型最近邻插值
                    f = interp1d(
                        temp_df[depth_column].values,
                        temp_df[column].values,
                        kind='nearest',
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    interpolated_values = f(new_depth_sequence)
            elif interpolation_method == "linear":
                # 线性插值（仅适用于数值型）
                interpolated_values = np.interp(
                    new_depth_sequence,
                    temp_df[depth_column].values,
                    temp_df[column].values
                )
            else:
                # 其他方法使用scipy.interpolate
                from scipy.interpolate import interp1d
                try:
                    f = interp1d(
                        temp_df[depth_column].values,
                        temp_df[column].values,
                        kind=interpolation_method,
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    interpolated_values = f(new_depth_sequence)
                except ValueError:
                    # 如果插值方法不支持，回退到线性插值
                    logger.warning(
                        f"插值方法 {interpolation_method} 不支持，回退到线性插值",
                        operation="interpolate_continuous_curves",
                        curve_name=column
                    )
                    interpolated_values = np.interp(
                        new_depth_sequence,
                        temp_df[depth_column].values,
                        temp_df[column].values
                    )

            # 将插值结果存储到字典中
            interpolated_data[column] = interpolated_values

            # 检查NaN值（处理字符串类型）
            try:
                has_nan_values = np.isnan(interpolated_values).any() if interpolated_values.dtype.kind in 'fc' else False
            except (TypeError, ValueError):
                has_nan_values = pd.isna(interpolated_values).any()

            logger.debug(
                "曲线重采样插值完成",
                operation="interpolate_continuous_curves",
                curve_name=column,
                interpolation_method=interpolation_method,
                original_points=len(temp_df),
                interpolated_points=len(interpolated_values),
                has_nan_values=has_nan_values,
                sample_values=interpolated_values[:5].tolist() if len(interpolated_values) > 0 else []
            )

        except Exception as e:
            # 插值失败时的备选方案
            logger.warning(
                "曲线插值失败，使用备选方案",
                operation="interpolate_continuous_curves",
                curve_name=column,
                failed_method=interpolation_method,
                error=str(e)
            )

            # 检查数据类型，选择合适的备选方案
            column_data = temp_df[column].dropna()
            if column_data.dtype == 'object':
                # 字符串类型使用最近邻插值
                try:
                    from scipy.interpolate import interp1d
                    unique_values = column_data.unique()
                    value_map = {val: i for i, val in enumerate(unique_values)}
                    reverse_map = {i: val for val, i in value_map.items()}

                    numeric_values = temp_df[column].map(value_map).values
                    f = interp1d(
                        temp_df[depth_column].values,
                        numeric_values,
                        kind='nearest',
                        bounds_error=False,
                        fill_value='extrapolate'
                    )
                    numeric_result = f(new_depth_sequence)
                    interpolated_values = np.array([
                        reverse_map.get(int(val), np.nan) if not np.isnan(val) else np.nan
                        for val in numeric_result
                    ])
                except Exception:
                    # 最后的备选：填充最常见的值
                    most_common = column_data.mode()
                    fill_value = most_common.iloc[0] if len(most_common) > 0 else np.nan
                    interpolated_values = np.full(len(new_depth_sequence), fill_value)
            else:
                # 数值类型使用线性插值
                try:
                    interpolated_values = np.interp(
                        new_depth_sequence,
                        temp_df[depth_column].values,
                        temp_df[column].values
                    )
                except Exception:
                    # 最后的备选：填充NaN
                    interpolated_values = np.full(len(new_depth_sequence), np.nan)

            # 将备选插值结果存储到字典中
            interpolated_data[column] = interpolated_values

    # 一次性创建结果DataFrame，避免逐列赋值导致的碎片化
    result_df = pd.DataFrame(interpolated_data)
    return result_df


def _fill_out_of_range_values(
    df: pd.DataFrame,
    depth_sequence: np.ndarray,
    original_depth_range: tuple[float, float],
    data_columns: list[str],
    out_of_range_fill_value: Any
) -> None:
    """填充超出原数据集范围的数据点。

    Args:
        df: 目标DataFrame（就地修改）
        depth_sequence: 深度序列
        original_depth_range: 原数据集深度范围
        data_columns: 数据列名列表
        out_of_range_fill_value: 超出范围的填充值
    """
    # 确定超出范围的索引
    out_of_range_mask = (
        (depth_sequence < original_depth_range[0]) |
        (depth_sequence > original_depth_range[1])
    )

    # 如果有超出范围的点，进行填充
    if out_of_range_mask.any():
        # 筛选出实际存在的数据列
        available_data_columns = [col for col in data_columns if col in df.columns]
        if available_data_columns:
            # 使用向量化操作一次性填充所有列，避免逐列赋值
            df.loc[out_of_range_mask, available_data_columns] = out_of_range_fill_value

        out_of_range_count = out_of_range_mask.sum()
        logger.debug(
            "超出范围数据点填充完成",
            operation="fill_out_of_range_values",
            out_of_range_count=out_of_range_count,
            total_points=len(depth_sequence),
            fill_value=str(out_of_range_fill_value)
        )


def _create_resampled_metadata(
    source_metadata: CurveMetadata,
    new_sampling_interval: float
) -> CurveMetadata:
    """创建重采样后的曲线元数据。

    Args:
        source_metadata: 源曲线元数据
        new_sampling_interval: 新的采样间隔

    Returns:
        CurveMetadata: 新的曲线元数据
    """
    # 深拷贝源元数据
    new_metadata = copy.deepcopy(source_metadata)

    # 更新时间戳
    new_metadata.modified_at = datetime.now()

    logger.debug(
        "重采样曲线元数据创建完成",
        operation="create_resampled_metadata",
        total_curves=new_metadata.get_curve_count(),
        new_sampling_interval=new_sampling_interval
    )

    return new_metadata


def _create_resampled_dataframe_with_fixed_range(
    source_df: pd.DataFrame,
    new_depth_sequence: np.ndarray,
    depth_column: str,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    interpolation_method: str,
    original_depth_range: tuple[float, float],
    out_of_range_fill_value: Any,
    max_interpolation_distance: float
) -> pd.DataFrame:
    """使用固定深度范围创建重采样DataFrame（用户指定深度范围时）。"""
    return _create_resampled_dataframe(
        source_df, new_depth_sequence, depth_column, well_curves,
        curve_metadata, interpolation_method, original_depth_range, out_of_range_fill_value,
        max_interpolation_distance
    )


def _create_resampled_dataframe_per_well(
    source_df: pd.DataFrame,
    depth_column: str,
    well_curves: list[str],
    curve_metadata: CurveMetadata,
    new_sampling_interval: float,
    interpolation_method: str,
    out_of_range_fill_value: Any,
    max_interpolation_distance: float
) -> tuple[pd.DataFrame, tuple[float, float]]:
    """按井分别创建重采样DataFrame，避免跨井的无意义深度范围。

    Returns:
        tuple[pd.DataFrame, tuple[float, float]]: (重采样后的DataFrame, 实际深度范围)
    """
    # 检测井的数量
    unique_wells = []
    if well_curves and well_curves[0] in source_df.columns:
        unique_wells = source_df[well_curves[0]].dropna().unique().tolist()

    if len(unique_wells) <= 1:
        # 单井数据：使用井自己的深度范围
        well_data = source_df
        if unique_wells:
            well_data = source_df[source_df[well_curves[0]] == unique_wells[0]]

        well_depths = well_data[depth_column].dropna()
        if well_depths.empty:
            return pd.DataFrame(), (0.0, 0.0)

        well_depth_range = (float(well_depths.min()), float(well_depths.max()))
        well_depth_sequence = _generate_aligned_depth_sequence(well_depth_range, new_sampling_interval)

        new_df = _create_resampled_dataframe(
            source_df, well_depth_sequence, depth_column, well_curves,
            curve_metadata, interpolation_method, well_depth_range, out_of_range_fill_value,
            max_interpolation_distance
        )

        actual_depth_range = (well_depth_sequence[0], well_depth_sequence[-1])
        return new_df, actual_depth_range

    else:
        # 多井数据：按井分别处理，然后合并
        well_dataframes = []
        all_depth_ranges = []

        for well in unique_wells:
            well_data = source_df[source_df[well_curves[0]] == well]
            well_depths = well_data[depth_column].dropna()

            if well_depths.empty:
                continue

            well_depth_range = (float(well_depths.min()), float(well_depths.max()))
            well_depth_sequence = _generate_aligned_depth_sequence(well_depth_range, new_sampling_interval)

            well_df = _create_resampled_dataframe(
                well_data, well_depth_sequence, depth_column, well_curves,
                curve_metadata, interpolation_method, well_depth_range, out_of_range_fill_value,
                max_interpolation_distance
            )

            if not well_df.empty:
                well_dataframes.append(well_df)
                all_depth_ranges.extend([well_depth_sequence[0], well_depth_sequence[-1]])

        if not well_dataframes:
            return pd.DataFrame(), (0.0, 0.0)

        # 合并所有井的数据
        combined_df = pd.concat(well_dataframes, ignore_index=True)

        # 计算实际深度范围
        if all_depth_ranges:
            actual_depth_range = (min(all_depth_ranges), max(all_depth_ranges))
        else:
            actual_depth_range = (0.0, 0.0)

        return combined_df, actual_depth_range
