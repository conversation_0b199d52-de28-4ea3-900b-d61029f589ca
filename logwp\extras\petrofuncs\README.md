# Petrophysical Functions (`logwp.extras.petrofuncs`)

一个提供用于测井解释的经典岩石物理模型函数的包。

这个包的核心设计理念是**单位安全**。所有函数都与 `logwp.extras.units` 包无缝集成，通过装饰器强制进行量纲检查，从根本上防止因单位混淆导致的计算错误。

## 核心特性

- **单位感知**: 所有输入参数都应为 `units.Quantity` 对象，函数会自动处理不同单位之间的换算。
- **量纲验证**: 在函数执行前，`@validate_dimensions` 装饰器会确保每个输入的物理量纲都是正确的。
- **物理意义**: 函数的返回值经过处理（例如使用 `np.clip`），以确保其符合物理现实（如孔隙度和饱和度在 0-1 之间）。

## 快速入门

使用包中的函数非常直观：

```python
from logwp.extras import units
from logwp.extras import petrofuncs

# 1. 准备带有单位的输入参数
rho_b_log = units.Q_(2.35, 'g/cm³')
rho_matrix_sandstone = units.Q_(2.65, 'g/cm³')
rho_fluid_water = units.Q_(1.05, 'g/cm³')

# 2. 调用模型函数
phi = petrofuncs.density_phi(
    rho_b=rho_b_log,
    rho_matrix=rho_matrix_sandstone,
    rho_fluid=rho_fluid_water
)

print(f"计算出的孔隙度 (phi) 为: {phi:.2%}")
# > 计算出的孔隙度 (phi) 为: 18.75%
```

## 可用模型函数

### 孔隙度模型 (`porosity.py`)

#### `density_phi(rho_b, rho_matrix, rho_fluid)`
使用密度测井值计算孔隙度。
- `rho_b`: 地层体积密度 (`density`)
- `rho_matrix`: 岩石骨架密度 (`density`)
- `rho_fluid`: 孔隙流体密度 (`density`)

#### `wyllie_phi(dt, dt_matrix, dt_fluid)`
使用威利时间平均公式计算总孔隙度。
- `dt`: 地层声波时差 (`slowness`)
- `dt_matrix`: 岩石骨架声波时差 (`slowness`)
- `dt_fluid`: 孔隙流体声波时差 (`slowness`)

### 饱和度模型 (`saturation.py`)

#### `archie_sw(Rt, Rw, phi, a=1.0, m=2.0, n=2.0)`
使用阿尔奇公式计算含水饱和度。
- `Rt`: 地层真实电阻率 (`resistivity`)
- `Rw`: 地层水电阻率 (`resistivity`)
- `phi`: 有效孔隙度 (无量纲小数)
- `a`, `m`, `n`: 阿尔奇公式参数 (无量纲)

---

## 设计哲学：为何强制使用单位？

在传统的计算流程中，工程师必须手动确保所有输入（例如，`dt_matrix`）都转换到了与测井值 (`dt`) 相同的单位（例如 `us/ft`）。这个过程非常容易出错。

`petrofuncs` 通过要求所有输入都是 `Quantity` 对象来解决这个问题。您可以放心地传入一个单位为 `us/m` 的骨架时差和一个单位为 `us/ft` 的测井值，底层的 `units` 包会自动、准确地完成所有单位换算。

```python
# 即使单位不同，计算依然正确
dt_log = units.Q_(95, 'us/ft')
dt_matrix_metric = units.Q_(182, 'us/m') # 相当于 55.5 us/ft
dt_fluid = units.Q_(189, 'us/ft')

phi = petrofuncs.wyllie_phi(dt=dt_log, dt_matrix=dt_matrix_metric, dt_fluid=dt_fluid)
print(f"混合单位输入计算的孔隙度 (phi) 为: {phi:.2%}")
# > 混合单位输入计算的孔隙度 (phi) 为: 29.56%
```
