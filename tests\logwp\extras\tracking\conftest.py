"""pytest配置和fixtures for tracking测试。

提供tracking包测试所需的通用fixtures和配置。
"""

from __future__ import annotations

import json
import tempfile
from pathlib import Path
from typing import Any, Dict, Generator

import pytest

from logwp.extras.tracking import BaseRunConfig, TrackingConfig


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """提供临时目录fixture。"""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture
def sample_config() -> Dict[str, Any]:
    """提供示例配置数据。"""
    return {
        "project_name": "SCAPE",
        "run_name": "test_tracking_run",
        "algorithm": "SWIFT-PSO",
        "learning_rate": 0.01,
        "bootstrap_iterations": 20,
        "model_type": "FOSTER-NMR"
    }


@pytest.fixture
def sample_metadata() -> Dict[str, Any]:
    """提供示例元数据。"""
    return {
        "git_commit": "abc123def456",
        "user": "test_user",
        "environment": "test"
    }


@pytest.fixture
def base_run_config() -> BaseRunConfig:
    """提供BaseRunConfig实例。"""
    return BaseRunConfig(
        project_name="SCAPE",
        run_name="test_run",
        description="测试运行配置",
        tags=["test", "tracking"]
    )


@pytest.fixture
def tracking_config() -> TrackingConfig:
    """提供TrackingConfig实例。"""
    return TrackingConfig(
        manifest_filename="test_manifest.json",
        config_snapshot_filename="test_config.yaml",
        max_artifact_size_mb=100
    )


@pytest.fixture
def sample_artifact_file(temp_dir: Path) -> Path:
    """创建示例产物文件。"""
    artifact_file = temp_dir / "sample_artifact.json"
    sample_data = {
        "model_type": "test_model",
        "accuracy": 0.95,
        "parameters": {"param1": 1.0, "param2": 2.0}
    }
    
    with open(artifact_file, "w") as f:
        json.dump(sample_data, f, indent=2)
    
    return artifact_file


@pytest.fixture
def sample_metrics() -> Dict[str, float]:
    """提供示例指标数据。"""
    return {
        "rmse": 0.85,
        "r2": 0.92,
        "mae": 0.12,
        "spearman_rho": 0.88
    }


@pytest.fixture
def sample_parameters() -> Dict[str, Any]:
    """提供示例参数数据。"""
    return {
        "learning_rate": 0.01,
        "batch_size": 32,
        "epochs": 100,
        "model_type": "XGBoost",
        "use_gpu": True
    }


@pytest.fixture
def registry_path(temp_dir: Path) -> Path:
    """提供模型注册表文件路径。"""
    return temp_dir / "test_registry.json"


@pytest.fixture
def run_dir(temp_dir: Path) -> Path:
    """提供运行目录路径。"""
    return temp_dir / "test_run"


@pytest.fixture
def completed_manifest_data() -> Dict[str, Any]:
    """提供完整的清单数据示例。"""
    return {
        "run_id": "test-run-123",
        "start_time_utc": "2025-07-17T10:00:00.000Z",
        "end_time_utc": "2025-07-17T10:30:00.000Z",
        "duration_seconds": 1800.0,
        "status": "COMPLETED",
        "lineage": {
            "inputs": {"data_version": "v1.0"},
            "code_version": {"git_commit": "abc123"}
        },
        "config_snapshot_path": "config_snapshot.yaml",
        "parameters": {
            "learning_rate": 0.01,
            "model_type": "XGBoost"
        },
        "metrics": {
            "summary": {"rmse": 0.85, "r2": 0.92},
            "training": {"loss": 0.123},
            "validation": {"spearman_rho": 0.88}
        },
        "artifacts": {
            "model.pkl": {
                "path": "models/model.pkl",
                "type": "model",
                "metadata": {
                    "size_bytes": 1024,
                    "sha256": "abc123def456"
                }
            }
        }
    }


@pytest.fixture
def sample_model_versions() -> list[Dict[str, Any]]:
    """提供示例模型版本数据。"""
    return [
        {
            "version": "1.0.0",
            "name": "test-model",
            "stage": "Staging",
            "description": "Initial version",
            "registered_at_utc": "2025-07-17T10:00:00.000Z",
            "source_run": {
                "run_id": "run-001",
                "artifact_path": "models/model_v1.pkl"
            },
            "metrics": {"rmse": 0.85, "r2": 0.92},
            "tags": ["baseline"]
        },
        {
            "version": "2.0.0",
            "name": "test-model",
            "stage": "Production",
            "description": "Improved version",
            "registered_at_utc": "2025-07-17T11:00:00.000Z",
            "source_run": {
                "run_id": "run-002",
                "artifact_path": "models/model_v2.pkl"
            },
            "metrics": {"rmse": 0.78, "r2": 0.95},
            "tags": ["improved", "validated"]
        }
    ]
