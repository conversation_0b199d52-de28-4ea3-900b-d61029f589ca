"""scape.core.obmiq.constants - OBMIQ步骤常量

定义OBMIQ训练和预测步骤的所有产物和绘图配置模板的逻辑名称。

Architecture
------------
层次/依赖: scape/core层，OBMIQ组件的常量模块
设计原则: 枚举化、点分层级命名、避免硬编码

遵循CCG规范：
- CS-2: 使用Wp前缀命名 (此处为Obmiq前缀)

References
----------
- 《机器学习组件开发框架》§3.2 - 产物与常量规范
- 《SCAPE_方法说明书_V5_OBMIQ建模.md》§五 - 标准产物列表
"""

from enum import Enum


class ObmiqTrainingArtifacts(str, Enum):
    """训练步骤产物。"""
    # 模型与报告
    TRAINING_CONFIG = "obmiq_training.configs.training_config_snapshot"
    MODEL_ASSETS = "obmiq_training.models.assets_pytorch"
    ONNX_MODEL = "obmiq_training.models.onnx_model"
    TUNING_REPORT = "obmiq_training.reports.hyperparameter_tuning"
    CV_PERFORMANCE_REPORT = "obmiq_training.reports.cv_performance"
    LOWO_CV_PERFORMANCE_SUMMARY = "obmiq_training.reports.lowo_cv_performance_summary"

    # LOWO-CV 泛化能力评估图表
    LOWO_CV_PREDICTIONS_DATA = "obmiq_training.data_snapshots.lowo_cv_predictions"
    LOWO_CV_CROSSPLOT_DT2_P50 = "obmiq_training.plots.lowo_cv_crossplot_dt2_p50"
    LOWO_CV_CROSSPLOT_DPHIT_NMR = "obmiq_training.plots.lowo_cv_crossplot_dphit_nmr"

    # 日志
    TENSORBOARD_LOGS = "obmiq_training.logs.tensorboard"

    # 训练历史
    FINAL_TRAINING_HISTORY_PLOT = "obmiq_training.plots.final_training_history"
    FINAL_TRAINING_HISTORY_DATA = "obmiq_training.data_snapshots.final_training_history"

    # 最终模型在训练集上的评估图表
    FINAL_MODEL_EVALUATION_DATA = "obmiq_training.data_snapshots.final_model_evaluation"
    EVAL_CROSSPLOT_DT2_P50 = "obmiq_training.plots.eval_crossplot_dt2_p50"
    EVAL_CROSSPLOT_DPHIT_NMR = "obmiq_training.plots.eval_crossplot_dphit_nmr"
    EVAL_RESIDUALS_PLOT_DT2_P50 = "obmiq_training.plots.eval_residuals_plot_dt2_p50"
    EVAL_RESIDUALS_PLOT_DPHIT_NMR = "obmiq_training.plots.eval_residuals_plot_dphit_nmr"
    EVAL_RESIDUALS_HIST_DT2_P50 = "obmiq_training.plots.eval_residuals_hist_dt2_p50"
    EVAL_RESIDUALS_HIST_DPHIT_NMR = "obmiq_training.plots.eval_residuals_hist_dphit_nmr"

    # 模型可解释性产物
    # 数据快照 (由Captum计算)
    CAPTUM_TABULAR_ATTRIBUTIONS_DATA = "obmiq_training.data_snapshots.captum_tabular_attributions"
    CAPTUM_SEQUENCE_ATTRIBUTIONS_DIR = "obmiq_training.data_snapshots.captum_sequence_attributions_dir"
    # 最终图表 (根据快照绘制)
    CAPTUM_IG_SUMMARY_PLOT = "obmiq_training.plots.captum_ig_summary" # 名称前缀
    CAPTUM_SALIENCY_EXAMPLES_DIR = "obmiq_training.plots.captum_saliency_examples_dir" # 目录


class ObmiqPredictionArtifacts(str, Enum):
    """预测步骤产物。"""
    # 核心数据产物 (同时作为所有绘图的数据快照)
    PREDICTIONS = "obmiq_prediction.datasets.predictions"

    # 评估图表
    CROSSPLOT_DT2_P50 = "obmiq_prediction.plots.crossplot_dt2_p50"
    CROSSPLOT_DPHIT_NMR = "obmiq_prediction.plots.crossplot_dphit_nmr"
    RESIDUALS_PLOT_DT2_P50 = "obmiq_prediction.plots.residuals_plot_dt2_p50"
    RESIDUALS_PLOT_DPHIT_NMR = "obmiq_prediction.plots.residuals_plot_dphit_nmr"
    RESIDUALS_HIST_DT2_P50 = "obmiq_prediction.plots.residuals_hist_dt2_p50"
    RESIDUALS_HIST_DPHIT_NMR = "obmiq_prediction.plots.residuals_hist_dphit_nmr"


class ObmiqPlotProfiles(str, Enum):
    """OBMIQ绘图配置模板名称常量。"""
    BASE = "obmiq.base"
    TRAINING_HISTORY = "obmiq.training_history"
    CROSSPLOT = "obmiq.crossplot"
    RESIDUALS_PLOT = "obmiq.residuals_plot"
    RESIDUALS_HIST = "obmiq.residuals_hist"
    SHAP_SUMMARY = "obmiq.shap_summary"
    GRAD_CAM = "obmiq.grad_cam"
    CAPTUM_IG_SUMMARY = "obmiq.captum_ig_summary"
