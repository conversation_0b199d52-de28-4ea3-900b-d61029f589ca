#!/usr/bin/env python3
"""数据集深度单位转换服务模块。

提供数据集级别的深度单位转换功能，支持原地转换深度曲线和采样间隔。

Architecture
------------
层次/依赖: datasets/service层，业务逻辑实现
设计原则: Service Layer模式、原地转换、元数据同步
性能特征: 向量化转换、内存优化、GPU支持

Examples:
    >>> # 单个数据集转换
    >>> convert_dataset_depth_unit(dataset, "ft")

    >>> # 批量数据集转换
    >>> convert_datasets_depth_unit(datasets, "m")

References:
    《SCAPE_DDS_详细设计_logwp.md》§4.2 - 深度单位管理系统
"""

from typing import TYPE_CHECKING

from logwp.models.constants import WpDepthUnit
from logwp.models.exceptions import WpValidationError, WpDataError
from logwp.infra.exceptions import ErrorContext
from logwp.models.utils.unit_conversion import convert_depth_array, convert_depth_value, is_valid_depth_unit
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase

logger = get_logger(__name__)


def convert_dataset_depth_unit(dataset: "WpDepthIndexedDatasetBase", target_unit: str) -> None:
    """原地转换数据集的深度单位。

    Args:
        dataset: 要转换的数据集
        target_unit: 目标深度单位（"m" 或 "ft"）

    Raises:
        WpValidationError: 当目标单位不支持时抛出
        WpDataError: 当数据集不支持深度转换时抛出

    Note:
        - 原地修改DataFrame中的深度列
        - 同步更新曲线元数据中的单位
        - 同步更新静态采样间隔
        - 不产生新的DataFrame对象

    Examples:
        >>> # 转换连续型数据集
        >>> convert_dataset_depth_unit(continuous_dataset, "ft")
        >>> assert continuous_dataset.get_depth_reference_unit() == "ft"

        >>> # 转换区间型数据集
        >>> convert_dataset_depth_unit(interval_dataset, "m")
        >>> top_curve, bottom_curve = interval_dataset.get_interval_depth_reference_curves()
        >>> assert top_curve.unit == "m"
        >>> assert bottom_curve.unit == "m"
    """
    # 验证目标单位
    if not is_valid_depth_unit(target_unit):
        raise WpValidationError(
            f"不支持的目标深度单位: {target_unit}",
            context=ErrorContext(
                operation="convert_dataset_depth_unit",
                dataset_name=str(dataset.name),
                additional_info={
                    "target_unit": target_unit,
                    "supported_units": list(WpDepthUnit.get_all_units())
                }
            )
        )

    # 获取当前深度单位
    current_unit = dataset.get_depth_reference_unit()
    if current_unit is None:
        raise WpDataError(
            "数据集没有深度参考曲线，无法进行单位转换",
            context=ErrorContext(
                operation="convert_dataset_depth_unit",
                dataset_name=str(dataset.name),
                additional_info={
                    "target_unit": target_unit,
                    "reason": "no_depth_reference_curve"
                }
            )
        )

    # 如果单位相同，无需转换
    if current_unit == target_unit:
        logger.info(
            "深度单位已经是目标单位，跳过转换",
            dataset_name=str(dataset.name),
            current_unit=current_unit,
            target_unit=target_unit,
            operation="convert_dataset_depth_unit"
        )
        return

    logger.info(
        "开始转换数据集深度单位",
        dataset_name=str(dataset.name),
        current_unit=current_unit,
        target_unit=target_unit,
        operation="convert_dataset_depth_unit"
    )

    try:
        # 根据数据集类型进行转换
        depth_count = dataset.get_depth_reference_count()

        if depth_count == 1:
            # 连续型或离散型数据集
            _convert_single_depth_dataset(dataset, current_unit, target_unit)
        elif depth_count == 2:
            # 区间型数据集
            _convert_interval_depth_dataset(dataset, current_unit, target_unit)
        else:
            raise WpDataError(
                f"不支持的深度曲线数量: {depth_count}",
                context=ErrorContext(
                    operation="convert_dataset_depth_unit",
                    dataset_name=str(dataset.name),
                    additional_info={
                        "depth_count": depth_count,
                        "current_unit": current_unit,
                        "target_unit": target_unit
                    }
                )
            )

        # 转换静态采样间隔
        if dataset.depth_sampling_rate != 0.0:
            converted_rate = convert_depth_value(
                dataset.depth_sampling_rate,
                current_unit,
                target_unit
            )
            dataset.depth_sampling_rate = converted_rate

            logger.debug(
                "转换静态采样间隔",
                dataset_name=str(dataset.name),
                original_rate=dataset.depth_sampling_rate,
                converted_rate=converted_rate,
                operation="convert_dataset_depth_unit"
            )

        logger.info(
            "数据集深度单位转换完成",
            dataset_name=str(dataset.name),
            current_unit=current_unit,
            target_unit=target_unit,
            operation="convert_dataset_depth_unit"
        )

    except Exception as e:
        logger.error(
            "数据集深度单位转换失败",
            dataset_name=str(dataset.name),
            current_unit=current_unit,
            target_unit=target_unit,
            error=str(e),
            operation="convert_dataset_depth_unit"
        )

        if isinstance(e, (WpValidationError, WpDataError)):
            raise
        else:
            raise WpDataError(
                "深度单位转换过程中发生错误",
                context=ErrorContext(
                    operation="convert_dataset_depth_unit",
                    dataset_name=str(dataset.name),
                    additional_info={
                        "current_unit": current_unit,
                        "target_unit": target_unit,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    }
                )
            ) from e


def _convert_single_depth_dataset(
    dataset: "WpDepthIndexedDatasetBase",
    current_unit: str,
    target_unit: str
) -> None:
    """转换单深度曲线数据集（连续型或离散型）。"""
    # 获取深度曲线
    depth_curve = dataset.get_single_depth_reference_curve()
    depth_column = depth_curve.dataframe_column_name

    # 检查深度列是否存在
    if depth_column not in dataset.df.columns:
        raise WpDataError(
            f"深度列 {depth_column} 不存在于DataFrame中",
            context=ErrorContext(
                operation="_convert_single_depth_dataset",
                dataset_name=str(dataset.name),
                column_name=depth_column,
                additional_info={
                    "available_columns": list(dataset.df.columns)
                }
            )
        )

    # 转换深度列数据
    original_values = dataset.df[depth_column]
    converted_values = convert_depth_array(original_values, current_unit, target_unit)
    dataset.df[depth_column] = converted_values

    # 更新曲线元数据中的单位
    dataset.curve_metadata.update_curve(depth_curve.name, unit=target_unit)

    logger.debug(
        "转换单深度曲线完成",
        dataset_name=str(dataset.name),
        depth_column=depth_column,
        current_unit=current_unit,
        target_unit=target_unit,
        operation="_convert_single_depth_dataset"
    )


def _convert_interval_depth_dataset(
    dataset: "WpDepthIndexedDatasetBase",
    current_unit: str,
    target_unit: str
) -> None:
    """转换区间深度曲线数据集。"""
    # 获取顶界和底界深度曲线
    top_curve, bottom_curve = dataset.get_interval_depth_reference_curves()
    top_column = top_curve.dataframe_column_name
    bottom_column = bottom_curve.dataframe_column_name

    # 检查深度列是否存在
    missing_columns = []
    if top_column not in dataset.df.columns:
        missing_columns.append(top_column)
    if bottom_column not in dataset.df.columns:
        missing_columns.append(bottom_column)

    if missing_columns:
        raise WpDataError(
            f"深度列不存在于DataFrame中: {missing_columns}",
            context=ErrorContext(
                operation="_convert_interval_depth_dataset",
                dataset_name=str(dataset.name),
                additional_info={
                    "missing_columns": missing_columns,
                    "available_columns": list(dataset.df.columns)
                }
            )
        )

    # 转换顶界深度列
    top_values = dataset.df[top_column]
    converted_top_values = convert_depth_array(top_values, current_unit, target_unit)
    dataset.df[top_column] = converted_top_values

    # 转换底界深度列
    bottom_values = dataset.df[bottom_column]
    converted_bottom_values = convert_depth_array(bottom_values, current_unit, target_unit)
    dataset.df[bottom_column] = converted_bottom_values

    # 更新曲线元数据中的单位
    dataset.curve_metadata.update_curve(top_curve.name, unit=target_unit)
    dataset.curve_metadata.update_curve(bottom_curve.name, unit=target_unit)

    logger.debug(
        "转换区间深度曲线完成",
        dataset_name=str(dataset.name),
        top_column=top_column,
        bottom_column=bottom_column,
        current_unit=current_unit,
        target_unit=target_unit,
        operation="_convert_interval_depth_dataset"
    )
