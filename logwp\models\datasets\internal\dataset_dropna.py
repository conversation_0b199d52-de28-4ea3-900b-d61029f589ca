"""数据集dropna操作服务。

提供对指定数据集的特定曲线执行dropna操作的核心逻辑，并根据
清理后的数据特征智能地确定新数据集的类型。
"""

from __future__ import annotations
from typing import TYPE_CHECKING
import copy

from logwp.models.exceptions import (
    WpDatasetNotFoundError, WpCurveMetadataError, WpDataError, WpValidationError
)
from logwp.infra.exceptions import ErrorContext
from logwp.infra import get_logger
from logwp.models.datasets.continuous import WpContinuousDataset
from logwp.models.datasets.discrete import WpDiscreteDataset
from logwp.models.datasets.interval import WpIntervalDataset
from logwp.models.curve import CurveExpansionMode

if TYPE_CHECKING:
    from logwp.models.well_project import WpWellProject
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase

logger = get_logger(__name__)


def dropna_dataset(
    project: WpWellProject,
    source_dataset_name: str,
    curve_names: list[str],
    new_dataset_name: str,
    *,
    dropna_how: str = "any"
) -> WpDepthIndexedDatasetBase:
    """
    对指定数据集的特定曲线执行dropna操作，并生成一个新的数据集。
    这是 WpWellProject.dropna_dataset 的服务层实现。
    """
    # 1. Validate parameters
    normalized_how = dropna_how.strip().lower()
    if normalized_how not in ("any", "all"):
        raise WpValidationError(
            f"dropna_how参数必须是'any'或'all'，当前值: {dropna_how}",
            context=ErrorContext(
                operation="dropna_dataset",
                dataset_name=source_dataset_name,
                additional_info={
                    "parameter": "dropna_how",
                    "invalid_value": dropna_how,
                    "allowed_values": ["any", "all"]
                }
            )
        )

    source_ds = project.get_dataset(source_dataset_name)
    if not source_ds:
        raise WpDatasetNotFoundError(f"源数据集 '{source_dataset_name}' 不存在。")

    curves_for_dropna = curve_names
    if not curves_for_dropna:
        # 如果曲线列表为空，则对所有数据曲线执行dropna操作
        # "数据曲线" 指排除了井名、深度等系统曲线的曲线
        curves_for_dropna = source_ds.curve_metadata.get_data_curves()
        logger.info(
            "曲线列表为空，将对所有数据曲线执行dropna操作。",
            operation="dropna_dataset",
            all_data_curves_count=len(curves_for_dropna)
        )

    logger.info(
        "开始数据集dropna操作",
        operation="dropna_dataset",
        source_dataset=source_dataset_name,
        new_dataset=new_dataset_name,
        curve_count=len(curves_for_dropna),
        dropna_how=normalized_how
    )

    # 2. Get the columns to check for NaN
    try:
        df_columns_to_check = source_ds.curve_metadata.expand_curve_names(
            curves_for_dropna,
            CurveExpansionMode.DATAFRAME
        )
    except WpCurveMetadataError as e:
        raise WpCurveMetadataError(f"指定的曲线名称无效: {e}", context=e.context) from e

    # 3. Perform dropna
    original_df = source_ds.df
    cleaned_df = original_df.dropna(subset=df_columns_to_check, how=normalized_how).copy()

    # Reset index to conform to CDP-1
    cleaned_df.reset_index(drop=True, inplace=True)

    logger.info(
        "dropna操作完成",
        operation="dropna_dataset",
        original_rows=len(original_df),
        cleaned_rows=len(cleaned_df),
        removed_rows=len(original_df) - len(cleaned_df)
    )

    # 4. Determine new dataset type and create it
    source_type = type(source_ds)
    new_metadata = copy.deepcopy(source_ds.curve_metadata)

    if source_type in (WpDiscreteDataset, WpIntervalDataset):
        # Type remains the same
        new_dataset = source_type.create_with_data(
            name=new_dataset_name,
            df=cleaned_df,
            curve_metadata=new_metadata
        )
        logger.info(
            f"源数据集类型为 {source_type.__name__}, 新数据集类型保持不变。",
            operation="dropna_dataset"
        )
    elif source_type is WpContinuousDataset:
        # 优化：直接调用服务函数检查深度采样，避免创建临时数据集
        from logwp.models.datasets.internal.depth_sampling_check import (
            check_uniform_depth_sampling, DepthSamplingAlgorithm
        )

        try:
            # 1. 从源数据集中获取深度曲线的元数据
            # source_ds 是 WpContinuousDataset, 所以该方法可用
            depth_curve_attrs = source_ds.get_single_depth_reference_curve()
            depth_column_name = depth_curve_attrs.dataframe_column_name

            # 2. 从清理后的DataFrame中获取深度数据列
            if depth_column_name not in cleaned_df.columns:
                # 这种情况理论上不应发生，因为深度列不应被dropna
                raise WpDataError(
                    f"清理后的DataFrame中缺少深度列 '{depth_column_name}'",
                    context=ErrorContext(
                        operation="dropna_dataset",
                        dataset_name=new_dataset_name,
                        additional_info={
                            "reason": "Depth column was likely dropped, which should not happen.",
                            "expected_column": depth_column_name,
                            "available_columns": list(cleaned_df.columns)
                        }
                    )
                )

            depth_column = cleaned_df[depth_column_name]

            # 如果清理后数据点过少，直接判定为离散
            if len(depth_column) < 2:
                is_uniform = False
                new_rate = None
                logger.info("dropna后数据点少于2个，新数据集类型将为 WpDiscreteDataset。", operation="dropna_dataset")
            else:
                # 3. 直接调用服务函数进行检查
                is_uniform, new_rate = check_uniform_depth_sampling(
                    depth_column=depth_column,
                    depth_curve=depth_curve_attrs,
                    algorithm=DepthSamplingAlgorithm.MAD  # 使用默认稳健算法
                )
        except WpDataError as e:
            logger.warning("检查深度采样均匀性时出错，将降级为离散数据集。错误: %s", e, operation="dropna_dataset")
            is_uniform = False
            new_rate = None

        if is_uniform:
            new_dataset = WpContinuousDataset.create_with_data(
                name=new_dataset_name,
                df=cleaned_df,
                curve_metadata=new_metadata,
                depth_sampling_rate=new_rate or source_ds.depth_sampling_rate
            )
            logger.info("深度采样仍为等间隔，新数据集类型为 WpContinuousDataset。", operation="dropna_dataset")
        else:
            new_dataset = WpDiscreteDataset.create_with_data(name=new_dataset_name, df=cleaned_df, curve_metadata=new_metadata)
            logger.info("深度采样变为非等间隔，新数据集类型为 WpDiscreteDataset。", operation="dropna_dataset")
    else:
        raise WpDataError(f"不支持的数据集类型: {source_type.__name__}")

    return new_dataset
