"""
COMP类型JSON处理器。

实现FAP原则的JSON结构处理，专注于COMP类型的JSON验证和提取。

Architecture
------------
层次/依赖: attr层 → constants层（格式无关常量）
设计原则: FAP格式无关、JSON专用、类型安全
性能特征: 高效JSON处理、结构验证、错误诊断

COMP数据一般形式：
    直接保存完整JSON结构，具备保存任意复杂属性的能力

Classes:
    StandardCompProcessor: JSON结构处理器（格式无关）

Examples:
    >>> # JSON结构验证
    >>> processor = StandardCompProcessor()
    >>> json_data = {"Axis_Type": "log10", "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"}}
    >>> validation = processor.validate_json_structure(json_data)
    >>> assert validation.is_valid

References:
    - 《SCAPE_SAD_软件架构设计.md》FAP-1到FAP-10 - 格式无关原则
    - 《SCAPE_CCG_编码与通用规范.md》CT-2 - 常量枚举使用
"""

from __future__ import annotations

from typing import Any, NamedTuple
from dataclasses import dataclass
import json

from logwp.models.exceptions import WpCompValidationError
from logwp.infra.exceptions import ErrorContext


class JsonValidationResult(NamedTuple):
    """JSON结构验证结果。

    Architecture
    ------------
    层次/依赖: 验证结果封装，轻量级数据结构
    设计原则: 结构化结果、错误聚合、格式无关
    性能特征: 轻量级NamedTuple，创建开销极小
    """
    is_valid: bool
    errors: list[str]
    warnings: list[str]
    validated_data: dict[str, Any] | None


@dataclass(frozen=True)
class StandardCompProcessor:
    """标准JSON处理器（格式无关）。

    专注于COMP类型的JSON结构验证和提取，支持任意复杂的JSON对象。

    Architecture
    ------------
    层次/依赖: attr层实现，专注JSON处理
    设计原则: FAP格式无关、JSON专用、类型安全
    性能特征: 高效JSON验证、结构检查、错误诊断

    COMP数据一般形式：
    ```python
    # 直接保存完整JSON结构，具备保存任意复杂属性的能力
    {
        "Axis_Type": "log10",
        "T2_Start": {"v": 0.1, "u": "ms", "t": "FLOAT"},
        "T2_End": {"v": 10000, "u": "ms", "t": "FLOAT"},
        "N": {"v": 64, "t": "INT"},
        "Values": {"v": [0.1, 0.12, ..., 10000], "u": "ms", "t": "FLOAT"}
    }
    ```

    Examples:
        >>> processor = StandardCompProcessor()
        >>>
        >>> # 验证JSON结构
        >>> json_data = {"Axis_Type": "log10", "T2_Start": {...}}
        >>> validation = processor.validate_json_structure(json_data)
        >>> assert validation.is_valid

    References:
        《SCAPE_SAD_软件架构设计.md》FAP-1 - 格式无关原则
    """

    strict_mode: bool = True
    """严格模式，启用时进行完整验证。"""

    def validate_json_structure(self, data: dict[str, Any] | None) -> JsonValidationResult:
        """验证JSON结构的有效性。

        Args:
            data: 待验证的JSON数据

        Returns:
            JsonValidationResult: 验证结果

        Examples:
            >>> processor = StandardCompProcessor()
            >>> json_data = {"Axis_Type": "log10", "T2_Start": {"v": 0.1}}
            >>> result = processor.validate_json_structure(json_data)
            >>> assert result.is_valid
        """
        errors = []
        warnings = []

        if data is None:
            errors.append("JSON数据为空")
            return JsonValidationResult(
                is_valid=False,
                errors=errors,
                warnings=warnings,
                validated_data=None
            )

        if not isinstance(data, dict):
            errors.append("JSON数据必须是字典类型")
            return JsonValidationResult(
                is_valid=False,
                errors=errors,
                warnings=warnings,
                validated_data=None
            )

        # 基础JSON结构验证通过
        return JsonValidationResult(
            is_valid=True,
            errors=errors,
            warnings=warnings,
            validated_data=data
        )

    def extract_json_value(self, data: dict[str, Any], path: str) -> Any:
        """从JSON数据中提取指定路径的值。

        Args:
            data: JSON数据
            path: 字段路径，支持点号分隔的嵌套路径

        Returns:
            Any: 提取的值

        Raises:
            WpCompValidationError: 路径不存在或提取失败

        Examples:
            >>> processor = StandardCompProcessor()
            >>> data = {"T2_Start": {"v": 0.1, "u": "ms"}}
            >>> value = processor.extract_json_value(data, "T2_Start.v")
            >>> assert value == 0.1
        """
        try:
            current = data
            for key in path.split('.'):
                if not isinstance(current, dict) or key not in current:
                    raise WpCompValidationError(
                        f"JSON路径 '{path}' 不存在",
                        context=ErrorContext(
                            operation="extract_json_value",
                            additional_info={
                                "path": path,
                                "current_key": key,
                                "available_keys": list(current.keys()) if isinstance(current, dict) else None
                            }
                        )
                    )
                current = current[key]
            return current
        except Exception as e:
            if isinstance(e, WpCompValidationError):
                raise
            raise WpCompValidationError(
                f"JSON值提取失败: {e}",
                context=ErrorContext(
                    operation="extract_json_value",
                    stage="data_extraction",
                    details={
                        "path": path,
                        "data_structure": data,
                        "error": str(e)
                    }
                )
            ) from e

    def is_valid_json_string(self, json_str: str) -> bool:
        """检查字符串是否为有效的JSON格式。

        Args:
            json_str: 待检查的JSON字符串

        Returns:
            bool: 是否为有效JSON

        Examples:
            >>> processor = StandardCompProcessor()
            >>> assert processor.is_valid_json_string('{"key": "value"}')
            >>> assert not processor.is_valid_json_string('invalid json')
        """
        try:
            json.loads(json_str)
            return True
        except (json.JSONDecodeError, TypeError):
            return False

    def parse_json_string(self, json_str: str) -> dict[str, Any]:
        """解析JSON字符串为字典。

        Args:
            json_str: JSON字符串

        Returns:
            dict[str, Any]: 解析后的字典

        Raises:
            WpCompValidationError: JSON解析失败

        Examples:
            >>> processor = StandardCompProcessor()
            >>> data = processor.parse_json_string('{"Axis_Type": "log10"}')
            >>> assert data["Axis_Type"] == "log10"
        """
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            raise WpCompValidationError(
                f"JSON解析失败: {e}",
                context=ErrorContext(
                    operation="parse_json_string",
                    stage="json_parsing",
                    details={
                        "json_string": json_str,
                        "error": str(e)
                    }
                )
            ) from e


# 默认JSON处理器实例（单例模式）
default_json_processor = StandardCompProcessor()


def get_default_json_processor() -> StandardCompProcessor:
    """获取默认JSON处理器实例。

    Returns:
        StandardCompProcessor: 默认JSON处理器实例

    Examples:
        >>> processor = get_default_json_processor()
        >>> validation = processor.validate_json_structure({"key": "value"})
        >>> assert validation.is_valid
    """
    return default_json_processor
