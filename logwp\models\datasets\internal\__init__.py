"""数据集内部服务模块。

专供datasets模块内部使用，不对业务层开放。
提供无状态的服务函数，处理复杂的数据集操作。
"""

from .curve_operations import remove_curve_inplace, validate_curves_consistency
from .interval_to_continuous import convert_interval_to_continuous
from .discrete_to_continuous import convert_discrete_to_continuous
from .continuous_resample import resample_continuous_dataset
from .depth_sampling_check import check_uniform_depth_sampling, DepthSamplingAlgorithm
from .dataset_summary import generate_dataset_summary
from .statistic_calculator import calculate_series_statistic
from .ml_converter import convert_bundle_to_structured_ml_inputs

__all__ = [
    "remove_curve_inplace",
    "validate_curves_consistency",
    "convert_interval_to_continuous",
    "convert_discrete_to_continuous",
    "resample_continuous_dataset",
    "check_uniform_depth_sampling",
    "DepthSamplingAlgorithm",
    "generate_dataset_summary",
    "calculate_series_statistic",
    "convert_bundle_to_structured_ml_inputs",
]
