{"cells": [{"cell_type": "code", "execution_count": 4, "id": "a9e0a2e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "1e86095f", "metadata": {}, "source": ["# 基准模型对比案例 (Benchmark Comparison Case)"]}, {"cell_type": "markdown", "id": "4266395e", "metadata": {}, "source": ["## 1. 初始化与导入"]}, {"cell_type": "code", "execution_count": 5, "id": "3e9b170c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "# 导入 baselines 组件\n", "from scape.core.baselines import (\n", "    run_sdr_training_step, run_sdr_prediction_step,\n", "    run_timur_training_step, run_timur_prediction_step,\n", "    run_dnn_training_step, run_dnn_prediction_step\n", ")\n", "from scape.core.baselines.sdr import SdrTrainingConfig, SdrPredictionConfig, SdrArtifacts\n", "from scape.core.baselines.timur import TimurTrainingConfig, TimurPredictionConfig, TimurArtifacts\n", "from scape.core.baselines.hybrid_dnn import DnnTrainingConfig, DnnPredictionConfig, DnnArtifacts\n", "\n", "# 导入 validation 组件\n", "from scape.core.validation import (\n", "    run_plt_analysis_step,\n", "    PltAnalysisConfig,\n", "    PltAnalysisArtifacts,\n", "    PltPlotTypes,\n", "    PltAnalysisPlotProfiles\n", ")\n", "\n", "import scape.core.validation.plot_profiles"]}, {"cell_type": "markdown", "id": "f519543e", "metadata": {}, "source": ["## 2. 实验设置与数据加载"]}, {"cell_type": "code", "execution_count": 6, "id": "e446002f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:18:51.557903Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 790.4, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\baselines_run_20250729_141851 run_id=20250729-061851-4c594b3c\n", "实验运行已初始化，所有产物将保存至: F:\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\25_baselines\\case32\\output01\\baselines_run_20250729_141851\n", "2025-07-29T06:18:51.602466Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 790.41, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T06:18:51.633463Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 790.59, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx file_size_mb=0.16 sheet_count=1\n", "2025-07-29T06:18:51.655910Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 790.61, 'cpu_percent': 0.0} project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T06:18:51.669250Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 790.62, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T06:18:51.689829Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 790.8, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T06:18:51.735817Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 790.93, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=12 well_curves=1\n", "2025-07-29T06:18:52.139066Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.69, 'cpu_percent': 0.0} shape=(377, 75) sheet_name=swift_pso_train_cleaned\n", "2025-07-29T06:18:52.168403Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.69, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned')\n", "2025-07-29T06:18:52.181604Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.69, 'cpu_percent': 0.0} curve_count=12 dataset_name=swift_pso_train_cleaned dataset_type=Point df_shape=(377, 75) processing_time=0.497\n", "2025-07-29T06:18:52.203738Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.69, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:18:52.218202Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.69, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:18:52.234329Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.69, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=0.632 project_name=WpIdentifier('scape_swift_pso_train_cleaned')\n", "2025-07-29T06:18:52.273688Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=10 input_curves=['K_LABEL', 'DT2_P50', 'WELL_NO', 'DPHIT_NMR', 'MD', 'T2_P50', 'PHI_T2_DIST', 'PHIT_NMR', 'T2LM', 'BFV_NMR'] operation=extract_metadata output_curve_count=10 output_curves=['K_LABEL', 'DT2_P50', 'WELL_NO', 'DPHIT_NMR', 'MD', 'T2_P50', 'PHI_T2_DIST', 'PHIT_NMR', 'T2LM', 'BFV_NMR']\n", "2025-07-29T06:18:52.294574Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T06:18:52.312614Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T06:18:52.332519Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T06:18:52.353097Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0}\n", "✅ 成功读取训练数据: ./scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T06:18:52.368115Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T06:18:52.390402Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx file_size_mb=1.9 sheet_count=1\n", "2025-07-29T06:18:52.408330Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T06:18:52.420616Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.89, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T06:18:52.441761Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.9, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T06:18:52.474682Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 791.91, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T06:18:56.517834Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 803.47, 'cpu_percent': 0.0} shape=(4689, 72) sheet_name=swift_pso_apply_cleaned\n", "2025-07-29T06:18:56.539645Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 803.47, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned')\n", "2025-07-29T06:18:56.569946Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 803.47, 'cpu_percent': 0.0} curve_count=9 dataset_name=swift_pso_apply_cleaned dataset_type=Continuous df_shape=(4689, 72) processing_time=4.133\n", "2025-07-29T06:18:56.599420Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 803.47, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:18:56.632301Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 803.47, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:18:56.650682Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 803.47, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=4.283 project_name=WpIdentifier('scape_swift_pso_apply_cleaned')\n", "2025-07-29T06:18:56.682572Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.49, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['WELL_NO', 'DT2_P50', 'DPHIT_NMR', 'MD', 'T2_P50', 'PHI_T2_DIST', 'PHIT_NMR', 'T2LM', 'BFV_NMR'] operation=extract_metadata output_curve_count=9 output_curves=['WELL_NO', 'DT2_P50', 'DPHIT_NMR', 'MD', 'T2_P50', 'PHI_T2_DIST', 'PHIT_NMR', 'T2LM', 'BFV_NMR']\n", "2025-07-29T06:18:56.709718Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.49, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T06:18:56.725640Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.49, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T06:18:56.739825Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.49, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T06:18:56.754429Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.49, 'cpu_percent': 0.0}\n", "✅ 成功读取应用数据: ./scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T06:18:56.778210Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.49, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx\n", "2025-07-29T06:18:56.795125Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.5, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T06:18:56.821095Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.5, 'cpu_percent': 0.0} project_name=scape_plt_val\n", "2025-07-29T06:18:56.835344Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.5, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_plt_val\n", "2025-07-29T06:18:56.840193Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T06:18:56.855881Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:18:56.888787Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:18:56.900644Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T06:18:56.932631Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T06:18:56.956599Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} shape=(14, 4) sheet_name=PLT\n", "2025-07-29T06:18:56.980434Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T06:18:56.988297Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(14, 4) processing_time=0.088\n", "2025-07-29T06:18:57.022103Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:18:57.027137Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:18:57.042883Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx processing_time=0.272 project_name=WpIdentifier('scape_plt_val')\n", "2025-07-29T06:18:57.074637Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} extracted_curve_count=1 operation=extract_curve_dataframe_bundle\n", "2025-07-29T06:18:57.103309Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['MD_Bottom', 'WELL_NO', 'QOZI', 'MD_Top'] operation=extract_metadata output_curve_count=4 output_curves=['MD_Bottom', 'WELL_NO', 'QOZI', 'MD_Top']\n", "✅ 成功读取PLT验证数据: ./scape_plt_val.wp.xlsx\n", "2025-07-29T06:18:57.134643Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.5, 'cpu_percent': 0.0} file_path=scape_core_k_val_t_1.wp.xlsx\n", "2025-07-29T06:18:57.161019Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} file_path=scape_core_k_val_t_1.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T06:18:57.164760Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} project_name=scape_core_k_val_t_1\n", "2025-07-29T06:18:57.180406Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_core_k_val_t_1\n", "2025-07-29T06:18:57.196122Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T06:18:57.227362Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:18:57.243341Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T06:18:57.275209Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val_T_1 dataset_type=Point operation=dataset_initialization\n", "2025-07-29T06:18:57.291032Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.51, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T06:18:57.322665Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} shape=(27, 9) sheet_name=K_Val_T_1\n", "2025-07-29T06:18:57.344190Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val_T_1')\n", "2025-07-29T06:18:57.359209Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val_T_1 dataset_type=Point df_shape=(27, 9) processing_time=0.1\n", "2025-07-29T06:18:57.387317Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T06:18:57.409211Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T06:18:57.413173Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val_t_1.wp.xlsx processing_time=0.279 project_name=WpIdentifier('scape_core_k_val_t_1')\n", "2025-07-29T06:18:57.444711Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} extracted_curve_count=7 operation=extract_curve_dataframe_bundle\n", "2025-07-29T06:18:57.474158Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['PERM_LT_001_FLAG', 'K_LABEL', 'WELL_NO', 'POR', 'MD', 'SAMPLE_TYPE', 'PZI', 'K_LABEL_TYPE', 'PERM'] operation=extract_metadata output_curve_count=9 output_curves=['PERM_LT_001_FLAG', 'K_LABEL', 'WELL_NO', 'POR', 'MD', 'SAMPLE_TYPE', 'PZI', 'K_LABEL_TYPE', 'PERM']\n", "✅ 成功读取MDT验证数据: ./scape_core_k_val_t_1.wp.xlsx\n", "  - T2轴信息: T2AxisLog10(t2_start=0.1, t2_end=10000, n=64, unit='ms')\n"]}], "source": ["# --- 实验设置 ---\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"baselines_run\")\n", "run_context = RunContext(output_dir / run_dir_name, overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")\n", "\n", "# --- 加载数据 ---\n", "reader = WpExcelReader()\n", "\n", "# 训练数据\n", "train_data_path = \"./scape_swift_pso_train_cleaned.wp.xlsx\"\n", "train_project = reader.read(train_data_path)\n", "train_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'BFV_NMR', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',\n", "        'PHI_T2_DIST', 'K_LABEL'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print(f\"✅ 成功读取训练数据: {train_data_path}\")\n", "\n", "# 应用数据\n", "apply_data_path = \"./scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "apply_project = reader.read(apply_data_path)\n", "apply_bundle = apply_project.get_dataset(\"swift_pso_apply_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'BFV_NMR', 'DT2_P50', 'T2_P50', 'DPHIT_NMR', 'PHI_T2_DIST'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print(f\"✅ 成功读取应用数据: {apply_data_path}\")\n", "\n", "# PLT验证数据\n", "plt_val_path = \"./scape_plt_val.wp.xlsx\"\n", "plt_val_project = reader.read(plt_val_path)\n", "plt_bundle = plt_val_project.get_dataset(\"PLT\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取PLT验证数据: {plt_val_path}\")\n", "\n", "mdt_val_path = \"./scape_core_k_val_t_1.wp.xlsx\"\n", "mdt_val_project = reader.read(mdt_val_path)\n", "mdt_val_bundle = mdt_val_project.get_dataset(\"K_Val_T_1\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取MDT验证数据: {mdt_val_path}\")\n", "\n", "# --- 准备数据依赖参数 ---\n", "t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "t2_time_array = t2_axis_info.calculate_values()\n", "print(f\"  - T2轴信息: {t2_axis_info}\")"]}, {"cell_type": "markdown", "id": "e5b87190", "metadata": {}, "source": ["## 3. 基准模型一：SDR 模型"]}, {"cell_type": "code", "execution_count": 7, "id": "76495147", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 1/4] 开始执行 SDR 基准模型...\n", "2025-07-29T06:18:57.594086Z [info     ] 开始SDR基准模型训练步骤                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.54, 'cpu_percent': 0.0} operation=sdr_training_step\n", "2025-07-29T06:18:57.618690Z [warning  ] 移除了包含非正数值的行，这些值在对数计算中无效        [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.56, 'cpu_percent': 0.0} operation=sdr_training_step remaining_rows=226 removed_rows=151\n", "2025-07-29T06:18:57.643222Z [info     ] 调用内部优化器寻找最佳SDR参数...            [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.56, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:57.674790Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_training.models.sdr_assets artifact_path=sdr_baseline_training\\sdr_model_assets.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:57.698174Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_training.configs.training_config_snapshot artifact_path=sdr_baseline_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:57.717344Z [info     ] SDR基准模型训练步骤完成                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.95, 'cpu_percent': 0.0} final_loss=1.126361682611099\n", "  - SDR 训练完成，最终损失: 1.1264\n", "2025-07-29T06:18:57.738337Z [info     ] 开始SDR基准模型预测步骤                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.95, 'cpu_percent': 0.0} operation=sdr_prediction_step\n", "2025-07-29T06:18:57.753769Z [info     ] 调用内部计算逻辑进行渗透率预测...             [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.95, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:57.767130Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 808.96, 'cpu_percent': 0.0} curve_name=K_SDR_PRED curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T06:18:57.814182Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_prediction.datasets.sdr_predictions artifact_path=sdr_baseline_prediction\\sdr_predictions.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.0, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:57.840604Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=sdr_baseline_prediction.configs.prediction_config_snapshot artifact_path=sdr_baseline_prediction\\prediction_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.0, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:57.863617Z [info     ] SDR基准模型预测步骤完成                  [scape.core.baselines.sdr.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.0, 'cpu_percent': 0.0} output_curve=K_SDR_PRED predicted_samples=4689\n", "  - SDR 预测完成，已将 'K_SDR_PRED' 添加到应用集。\n"]}], "source": ["print(\"🚀 [Step 1/4] 开始执行 SDR 基准模型...\")\n", "\n", "# --- 训练 ---\n", "sdr_train_result = run_sdr_training_step(\n", "    config=SdrTrainingConfig(),\n", "    ctx=run_context,\n", "    train_bundle=train_bundle,\n", "    k_label_curve='K_LABEL',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    t2lm_curve='T2LM'\n", ")\n", "print(f\"  - SDR 训练完成，最终损失: {sdr_train_result['final_loss']:.4f}\")\n", "\n", "# --- 预测 ---\n", "from scape.core.baselines.sdr.artifact_handler import SdrArtifactHandler\n", "handler = SdrArtifactHandler()\n", "model_params_path = run_context.get_artifact_path(SdrArtifacts.MODEL_ASSETS.value)\n", "sdr_model_assets = handler.load_model_assets(model_params_path)\n", "sdr_pred_result = run_sdr_prediction_step(\n", "    config=SdrPredictionConfig(),\n", "    ctx=run_context,\n", "    model_assets=sdr_model_assets,\n", "    prediction_bundle=apply_bundle, # 直接修改应用集Bundle\n", "    output_curve_name='K_SDR_PRED',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    t2lm_curve='T2LM'\n", ")\n", "print(f\"  - SDR 预测完成，已将 'K_SDR_PRED' 添加到应用集。\")"]}, {"cell_type": "markdown", "id": "b1c2d3e4", "metadata": {}, "source": ["## 4. 基准模型二：Timur/Coates 模型"]}, {"cell_type": "code", "execution_count": 8, "id": "f5g6h7i8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 2/4] 开始执行 Timur/Coates 基准模型...\n", "2025-07-29T06:18:57.982414Z [info     ] 开始Timur/Coates基准模型训练步骤         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} operation=timur_training_step\n", "2025-07-29T06:18:58.004988Z [warning  ] 移除了包含无效物理值的行（如非正渗透率/孔隙度，或PHIT<=BFV） [scape.core.baselines.timur.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} operation=timur_training_step remaining_rows=226 removed_rows=151\n", "2025-07-29T06:18:58.027539Z [info     ] 调用内部优化器寻找最佳Timur/Coates参数...   [scape.core.baselines.timur.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:58.059772Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_training.models.timur_assets artifact_path=timur_baseline_training\\timur_model_assets.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:58.090237Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_training.configs.training_config_snapshot artifact_path=timur_baseline_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:58.112086Z [info     ] Timur/Coates基准模型训练步骤完成         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} final_loss=1.0229781773871895\n", "  - Timur 训练完成，最终损失: 1.0230\n", "2025-07-29T06:18:58.129187Z [info     ] 开始Timur/Coates基准模型预测步骤         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} operation=timur_prediction_step\n", "2025-07-29T06:18:58.143701Z [info     ] 调用内部计算逻辑进行渗透率预测...             [scape.core.baselines.timur.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:58.160090Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} curve_name=K_TIMUR_PRED curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T06:18:58.202176Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_prediction.datasets.timur_predictions artifact_path=timur_baseline_prediction\\timur_predictions.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:58.228740Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=timur_baseline_prediction.configs.prediction_config_snapshot artifact_path=timur_baseline_prediction\\prediction_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:58.249467Z [info     ] Timur/Coates基准模型预测步骤完成         [scape.core.baselines.timur.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.01, 'cpu_percent': 0.0} output_curve=K_TIMUR_PRED predicted_samples=4689\n", "  - Timur 预测完成，已将 'K_TIMUR_PRED' 添加到应用集。\n"]}], "source": ["print(\"🚀 [Step 2/4] 开始执行 Timur/Coates 基准模型...\")\n", "\n", "# --- 训练 ---\n", "timur_train_result = run_timur_training_step(\n", "    config=TimurTrainingConfig(bfv_min=0.02),\n", "    ctx=run_context,\n", "    train_bundle=train_bundle,\n", "    k_label_curve='K_LABEL',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    bfv_nmr_curve='BFV_NMR'\n", ")\n", "print(f\"  - Timur 训练完成，最终损失: {timur_train_result['final_loss']:.4f}\")\n", "\n", "# --- 预测 ---\n", "from scape.core.baselines.timur.artifact_handler import TimurArtifactHandler\n", "handler = TimurArtifactHandler()\n", "model_params_path = run_context.get_artifact_path(TimurArtifacts.MODEL_ASSETS.value)\n", "timur_model_assets = handler.load_model_assets(model_params_path)\n", "timur_pred_result = run_timur_prediction_step(\n", "    config=TimurPredictionConfig(bfv_min=0.02),\n", "    ctx=run_context,\n", "    model_assets=timur_model_assets,\n", "    prediction_bundle=apply_bundle, # 继续修改同一个应用集Bundle\n", "    output_curve_name='K_TIMUR_PRED',\n", "    phit_nmr_curve='PHIT_NMR',\n", "    bfv_nmr_curve='BFV_NMR'\n", ")\n", "print(f\"  - Timur 预测完成，已将 'K_TIMUR_PRED' 添加到应用集。\")"]}, {"cell_type": "markdown", "id": "n3o4p5q6", "metadata": {}, "source": ["## 5. 基准模型三：Hybrid DNN 模型"]}, {"cell_type": "code", "execution_count": 9, "id": "r7s8t9u0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 3/4] 开始执行 Hybrid DNN 基准模型...\n", "2025-07-29T06:18:58.380653Z [info     ] ===== Hybrid DNN Training Step Started ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.02, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:58.397044Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.configs.training_config artifact_path=dnn_hybrid_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.02, 'cpu_percent': 0.0} description=本次DNN训练运行的完整配置参数。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:18:58.423860Z [info     ] 训练配置已作为产物保存。                   [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.02, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:58.450617Z [info     ] 曲线名已成功解析为DataFrame列名。          [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.02, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:58.463473Z [info     ] --- Stage 1: Hyperparameter Tuning using LOWO-CV --- [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.02, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:58.532329Z [info     ] Tuning procedure started on device: cuda [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.2, 'cpu_percent': 0.0}\n", "2025-07-29T06:18:58.557224Z [info     ] --- Starting CV Fold 1/2 ---   [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 809.21, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-29 14:18:58,596] A new study created in memory with name: no-name-db0c13db-ab10-485b-9bcc-88530b5a726c\n", "[I 2025-07-29 14:19:04,858] Trial 0 finished with value: 7.5242509841918945 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.5615186977431705, 'learning_rate': 0.0012566933043015288, 'weight_decay': 6.655979762116761e-05}. Best is trial 0 with value: 7.5242509841918945.\n", "[I 2025-07-29 14:19:08,006] Trial 1 finished with value: 24.34385871887207 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.37012458925433445, 'learning_rate': 0.00018080331632337168, 'weight_decay': 1.8739192298034377e-05}. Best is trial 0 with value: 7.5242509841918945.\n", "[I 2025-07-29 14:19:11,306] Trial 2 finished with value: 13.113524436950684 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.5860342091005788, 'learning_rate': 0.00044206470914760125, 'weight_decay': 0.0008151953945968769}. Best is trial 0 with value: 7.5242509841918945.\n", "[I 2025-07-29 14:19:14,544] Trial 3 finished with value: 27.828821182250977 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.2647156246307768, 'learning_rate': 0.00012771475774363627, 'weight_decay': 0.0006360398250043469}. Best is trial 0 with value: 7.5242509841918945.\n", "[I 2025-07-29 14:19:18,053] Trial 4 finished with value: 36.03582000732422 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 8, 'dropout_rate': 0.5007749759386866, 'learning_rate': 0.0001295826679901393, 'weight_decay': 0.00016722931851106536}. Best is trial 0 with value: 7.5242509841918945.\n", "[I 2025-07-29 14:19:18,160] Trial 5 pruned. \n", "[I 2025-07-29 14:19:18,213] Trial 6 pruned. \n", "[I 2025-07-29 14:19:18,253] Trial 7 pruned. \n", "[I 2025-07-29 14:19:19,720] Trial 8 finished with value: 7.071704864501953 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.4394190084465984, 'learning_rate': 0.0024401138390901882, 'weight_decay': 0.0009086865559998955}. Best is trial 8 with value: 7.071704864501953.\n", "[I 2025-07-29 14:19:22,835] Trial 9 finished with value: 7.601413249969482 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.23269242680443855, 'learning_rate': 0.0010503476723002494, 'weight_decay': 0.0006235601283816626}. Best is trial 8 with value: 7.071704864501953.\n", "[I 2025-07-29 14:19:24,639] Trial 10 finished with value: 7.328981399536133 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.4571365044782863, 'learning_rate': 0.008925324566535744, 'weight_decay': 0.0002528382174013928}. Best is trial 8 with value: 7.071704864501953.\n", "[I 2025-07-29 14:19:25,603] Trial 11 finished with value: 7.759141445159912 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.4561312078563775, 'learning_rate': 0.008754294511821945, 'weight_decay': 0.0002672580211293613}. Best is trial 8 with value: 7.071704864501953.\n", "[I 2025-07-29 14:19:26,696] Trial 12 finished with value: 7.056056022644043 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.40726613565322367, 'learning_rate': 0.007014065863944467, 'weight_decay': 0.00034376882635928004}. Best is trial 12 with value: 7.056056022644043.\n", "[I 2025-07-29 14:19:26,758] Trial 13 pruned. \n", "[I 2025-07-29 14:19:28,990] Trial 14 finished with value: 7.221541881561279 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4278254501993691, 'learning_rate': 0.002827051897871514, 'weight_decay': 0.00042152503501659294}. Best is trial 12 with value: 7.056056022644043.\n", "[I 2025-07-29 14:19:29,071] Trial 15 pruned. \n", "[I 2025-07-29 14:19:29,145] Trial 16 pruned. \n", "[I 2025-07-29 14:19:29,217] Trial 17 pruned. \n", "[I 2025-07-29 14:19:29,293] Trial 18 pruned. \n", "[I 2025-07-29 14:19:29,364] Trial 19 pruned. \n", "[I 2025-07-29 14:19:29,435] Trial 20 pruned. \n", "[I 2025-07-29 14:19:29,497] Trial 21 pruned. \n", "[I 2025-07-29 14:19:29,564] Trial 22 pruned. \n", "[I 2025-07-29 14:19:31,845] Trial 23 finished with value: 7.511813640594482 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.4781361176730562, 'learning_rate': 0.007355781049845136, 'weight_decay': 0.0003962734276921483}. Best is trial 12 with value: 7.056056022644043.\n", "[I 2025-07-29 14:19:31,906] Trial 24 pruned. \n", "[I 2025-07-29 14:19:32,004] Trial 25 pruned. \n", "[I 2025-07-29 14:19:32,085] Trial 26 pruned. \n", "[I 2025-07-29 14:19:32,165] Trial 27 pruned. \n", "[I 2025-07-29 14:19:32,245] Trial 28 pruned. \n", "[I 2025-07-29 14:19:32,306] Trial 29 pruned. \n", "[I 2025-07-29 14:19:32,387] Trial 30 pruned. \n", "[I 2025-07-29 14:19:34,269] Trial 31 finished with value: 7.274866580963135 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.45400693525084845, 'learning_rate': 0.009262262804889744, 'weight_decay': 0.00022015614193484923}. Best is trial 12 with value: 7.056056022644043.\n", "[I 2025-07-29 14:19:34,340] Trial 32 pruned. \n", "[I 2025-07-29 14:19:34,393] Trial 33 pruned. \n", "[I 2025-07-29 14:19:34,441] Trial 34 pruned. \n", "[I 2025-07-29 14:19:34,499] Trial 35 pruned. \n", "[I 2025-07-29 14:19:34,553] Trial 36 pruned. \n", "[I 2025-07-29 14:19:34,635] Trial 37 pruned. \n", "[I 2025-07-29 14:19:35,646] Trial 38 finished with value: 7.925634860992432 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.40353082242776034, 'learning_rate': 0.009866878468875922, 'weight_decay': 0.0005462929722128532}. Best is trial 12 with value: 7.056056022644043.\n", "[I 2025-07-29 14:19:35,740] Trial 39 pruned. \n", "[I 2025-07-29 14:19:35,806] Trial 40 pruned. \n", "[I 2025-07-29 14:19:35,873] Trial 41 pruned. \n", "[I 2025-07-29 14:19:35,951] Trial 42 pruned. \n", "[I 2025-07-29 14:19:36,020] Trial 43 pruned. \n", "[I 2025-07-29 14:19:36,086] Trial 44 pruned. \n", "[I 2025-07-29 14:19:38,234] Trial 45 finished with value: 7.771409511566162 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.5119673601314952, 'learning_rate': 0.0076993487098958755, 'weight_decay': 0.00016836251123954115}. Best is trial 12 with value: 7.056056022644043.\n", "[I 2025-07-29 14:19:38,314] Trial 46 pruned. \n", "[I 2025-07-29 14:19:38,390] Trial 47 pruned. \n", "[I 2025-07-29 14:19:40,275] Trial 48 finished with value: 6.805108547210693 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.21728034636230623, 'learning_rate': 0.004403389633553295, 'weight_decay': 4.133885637299337e-05}. Best is trial 48 with value: 6.805108547210693.\n", "[I 2025-07-29 14:19:40,375] Trial 49 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:19:40.380978Z [info     ] Fold 1 best trial: value=6.8051, params={'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.21728034636230623, 'learning_rate': 0.004403389633553295, 'weight_decay': 4.133885637299337e-05} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1288.32, 'cpu_percent': 0.0}\n", "2025-07-29T06:19:40.396323Z [info     ] --- Performing blind test for Fold 1 on well 'C-1' --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1288.32, 'cpu_percent': 0.0}\n", "2025-07-29T06:19:41.871485Z [info     ] --- Starting CV Fold 2/2 ---   [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1288.92, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-29 14:19:41,889] A new study created in memory with name: no-name-d16448fa-c856-4d4e-8173-995c0937767b\n", "[I 2025-07-29 14:19:43,169] Trial 0 finished with value: 13.820260047912598 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.5061431758681622, 'learning_rate': 0.00791162532548065, 'weight_decay': 1.4628812266968653e-05}. Best is trial 0 with value: 13.820260047912598.\n", "[I 2025-07-29 14:19:45,930] Trial 1 finished with value: 13.028156280517578 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.4713029666240483, 'learning_rate': 0.0023444957493288024, 'weight_decay': 0.00022211196958934223}. Best is trial 1 with value: 13.028156280517578.\n", "[I 2025-07-29 14:19:48,290] Trial 2 finished with value: 12.910731315612793 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 8, 'dropout_rate': 0.44846311447195525, 'learning_rate': 0.002255833743845875, 'weight_decay': 0.0005286036501001605}. Best is trial 2 with value: 12.910731315612793.\n", "[I 2025-07-29 14:19:49,329] Trial 3 finished with value: 11.920734405517578 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3167535581921051, 'learning_rate': 0.004825898180136395, 'weight_decay': 0.0001530114984268355}. Best is trial 3 with value: 11.920734405517578.\n", "[I 2025-07-29 14:19:52,053] Trial 4 finished with value: 24.093080520629883 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.5833467249896169, 'learning_rate': 0.0001981051323563338, 'weight_decay': 0.00036523402653130567}. Best is trial 3 with value: 11.920734405517578.\n", "[I 2025-07-29 14:19:52,211] Trial 5 pruned. \n", "[I 2025-07-29 14:19:52,613] Trial 6 pruned. \n", "[I 2025-07-29 14:19:52,642] Trial 7 pruned. \n", "[I 2025-07-29 14:19:52,772] Trial 8 pruned. \n", "[I 2025-07-29 14:19:54,085] Trial 9 finished with value: 13.576415061950684 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.56040742984096, 'learning_rate': 0.008559517514080546, 'weight_decay': 2.9862377483717088e-05}. Best is trial 3 with value: 11.920734405517578.\n", "[I 2025-07-29 14:19:54,213] Trial 10 pruned. \n", "[I 2025-07-29 14:19:54,359] Trial 11 pruned. \n", "[I 2025-07-29 14:19:54,427] Trial 12 pruned. \n", "[I 2025-07-29 14:19:54,486] Trial 13 pruned. \n", "[I 2025-07-29 14:19:54,559] Trial 14 pruned. \n", "[I 2025-07-29 14:19:54,613] Trial 15 pruned. \n", "[I 2025-07-29 14:19:54,653] Trial 16 pruned. \n", "[I 2025-07-29 14:19:54,814] Trial 17 pruned. \n", "[I 2025-07-29 14:19:54,966] Trial 18 pruned. \n", "[I 2025-07-29 14:19:55,014] Trial 19 pruned. \n", "[I 2025-07-29 14:19:56,107] Trial 20 finished with value: 12.521543502807617 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.37519162868600997, 'learning_rate': 0.005132593818477306, 'weight_decay': 0.0006004583610207154}. Best is trial 3 with value: 11.920734405517578.\n", "[I 2025-07-29 14:19:56,174] Trial 21 pruned. \n", "[I 2025-07-29 14:19:56,588] Trial 22 pruned. \n", "[I 2025-07-29 14:19:56,767] Trial 23 pruned. \n", "[I 2025-07-29 14:19:57,650] Trial 24 finished with value: 12.665085792541504 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.30453900764712066, 'learning_rate': 0.0064085893044412865, 'weight_decay': 0.0008755726772253659}. Best is trial 3 with value: 11.920734405517578.\n", "[I 2025-07-29 14:19:57,708] Trial 25 pruned. \n", "[I 2025-07-29 14:19:57,961] Trial 26 pruned. \n", "[I 2025-07-29 14:19:58,015] Trial 27 pruned. \n", "[I 2025-07-29 14:19:58,121] Trial 28 pruned. \n", "[I 2025-07-29 14:19:59,261] Trial 29 finished with value: 13.102882385253906 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.2517638127196826, 'learning_rate': 0.007142503893514528, 'weight_decay': 8.289627802864297e-05}. Best is trial 3 with value: 11.920734405517578.\n", "[I 2025-07-29 14:19:59,775] Trial 30 pruned. \n", "[I 2025-07-29 14:19:59,829] Trial 31 pruned. \n", "[I 2025-07-29 14:19:59,892] Trial 32 pruned. \n", "[I 2025-07-29 14:20:00,032] Trial 33 pruned. \n", "[I 2025-07-29 14:20:00,088] Trial 34 pruned. \n", "[I 2025-07-29 14:20:00,189] Trial 35 pruned. \n", "[I 2025-07-29 14:20:00,269] Trial 36 pruned. \n", "[I 2025-07-29 14:20:00,322] Trial 37 pruned. \n", "[I 2025-07-29 14:20:00,605] Trial 38 pruned. \n", "[I 2025-07-29 14:20:00,656] Trial 39 pruned. \n", "[I 2025-07-29 14:20:00,709] Trial 40 pruned. \n", "[I 2025-07-29 14:20:00,770] Trial 41 pruned. \n", "[I 2025-07-29 14:20:00,881] Trial 42 pruned. \n", "[I 2025-07-29 14:20:01,029] Trial 43 pruned. \n", "[I 2025-07-29 14:20:01,173] Trial 44 pruned. \n", "[I 2025-07-29 14:20:01,216] Trial 45 pruned. \n", "[I 2025-07-29 14:20:01,367] Trial 46 pruned. \n", "[I 2025-07-29 14:20:01,469] Trial 47 pruned. \n", "[I 2025-07-29 14:20:01,568] Trial 48 pruned. \n", "[I 2025-07-29 14:20:01,656] Trial 49 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:20:01.656590Z [info     ] Fold 2 best trial: value=11.9207, params={'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3167535581921051, 'learning_rate': 0.004825898180136395, 'weight_decay': 0.0001530114984268355} [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1289.84, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:01.669876Z [info     ] --- Performing blind test for Fold 2 on well 'C-2' --- [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1289.84, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:02.722682Z [info     ] Aggregating results from all CV folds... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1295.37, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:02.737104Z [info     ] Best hyperparameters found: {'cnn_filters': 8.0, 'cnn_kernel_size': 5.0, 'dropout_rate': 0.21728034636230623, 'learning_rate': 0.004403389633553295, 'mlp_units': 64.0, 'weight_decay': 4.133885637299337e-05} [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1295.74, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:02.763775Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.reports.cv_performance artifact_path=dnn_hybrid_training\\cv_performance_report.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1295.74, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:02.808589Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.reports.hyperparameter_tuning artifact_path=dnn_hybrid_training\\hyperparameter_tuning_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1295.74, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:02.830229Z [info     ] --- Stage 2: Final Model Training --- [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1295.74, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:02.892500Z [info     ] Final model training started on device: cuda [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1295.84, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:03.337360Z [info     ] Epoch 1/150, Train Loss: 35.2139, Val Loss: 39.5710 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1384.6, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:03.430542Z [info     ] Epoch 2/150, Train Loss: 28.6835, Val Loss: 34.8291 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:03.523897Z [info     ] Epoch 3/150, Train Loss: 20.8285, Val Loss: 26.5875 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:03.630668Z [info     ] Epoch 4/150, Train Loss: 14.7497, Val Loss: 18.7977 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:03.733006Z [info     ] Epoch 5/150, Train Loss: 11.9222, Val Loss: 14.8337 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:03.844174Z [info     ] Epoch 6/150, Train Loss: 11.4682, Val Loss: 13.3472 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:03.958243Z [info     ] Epoch 7/150, Train Loss: 10.2241, Val Loss: 12.6306 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.070792Z [info     ] Epoch 8/150, Train Loss: 9.4751, Val Loss: 12.1489 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.177632Z [info     ] Epoch 9/150, Train Loss: 9.3397, Val Loss: 11.5862 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.297706Z [info     ] Epoch 10/150, Train Loss: 10.0509, Val Loss: 11.4180 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.394194Z [info     ] Epoch 11/150, Train Loss: 9.2179, Val Loss: 12.0735 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.486799Z [info     ] Epoch 12/150, Train Loss: 8.8314, Val Loss: 13.4755 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.577754Z [info     ] Epoch 13/150, Train Loss: 9.0063, Val Loss: 13.1164 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.696532Z [info     ] Epoch 14/150, Train Loss: 8.3828, Val Loss: 12.0476 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.790808Z [info     ] Epoch 15/150, Train Loss: 9.0479, Val Loss: 11.9252 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.893476Z [info     ] Epoch 16/150, Train Loss: 9.6795, Val Loss: 11.5266 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:04.991310Z [info     ] Epoch 17/150, Train Loss: 8.6454, Val Loss: 11.9216 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.088281Z [info     ] Epoch 18/150, Train Loss: 8.7328, Val Loss: 11.0544 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.164476Z [info     ] Epoch 19/150, Train Loss: 8.4900, Val Loss: 11.1990 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.261864Z [info     ] Epoch 20/150, Train Loss: 8.8540, Val Loss: 11.0706 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.351342Z [info     ] Epoch 21/150, Train Loss: 8.1689, Val Loss: 11.1915 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.444746Z [info     ] Epoch 22/150, Train Loss: 8.7534, Val Loss: 11.1665 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.551478Z [info     ] Epoch 23/150, Train Loss: 8.0287, Val Loss: 11.2454 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.644839Z [info     ] Epoch 24/150, Train Loss: 8.8306, Val Loss: 11.4791 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.751395Z [info     ] Epoch 25/150, Train Loss: 9.3183, Val Loss: 11.9761 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.871621Z [info     ] Epoch 26/150, Train Loss: 8.7442, Val Loss: 11.7938 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:05.990392Z [info     ] Epoch 27/150, Train Loss: 8.5445, Val Loss: 11.4201 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.111625Z [info     ] Epoch 28/150, Train Loss: 8.7083, Val Loss: 11.7841 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.218356Z [info     ] Epoch 29/150, Train Loss: 7.8928, Val Loss: 11.0229 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.342993Z [info     ] Epoch 30/150, Train Loss: 8.4819, Val Loss: 11.0150 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.418342Z [info     ] Epoch 31/150, Train Loss: 8.6676, Val Loss: 11.6248 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.524001Z [info     ] Epoch 32/150, Train Loss: 8.6362, Val Loss: 12.2633 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.618418Z [info     ] Epoch 33/150, Train Loss: 7.8564, Val Loss: 12.1804 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.725167Z [info     ] Epoch 34/150, Train Loss: 8.3167, Val Loss: 11.8137 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.816598Z [info     ] Epoch 35/150, Train Loss: 8.1037, Val Loss: 11.2816 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:06.925240Z [info     ] Epoch 36/150, Train Loss: 7.6213, Val Loss: 11.3722 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.029572Z [info     ] Epoch 37/150, Train Loss: 8.0817, Val Loss: 11.3663 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.146276Z [info     ] Epoch 38/150, Train Loss: 7.3968, Val Loss: 11.8926 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.265603Z [info     ] Epoch 39/150, Train Loss: 7.9241, Val Loss: 11.2455 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.377924Z [info     ] Epoch 40/150, Train Loss: 8.4797, Val Loss: 11.2158 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.454379Z [info     ] Epoch 41/150, Train Loss: 7.9115, Val Loss: 11.5205 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.549748Z [info     ] Epoch 42/150, Train Loss: 7.7952, Val Loss: 12.4446 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.661335Z [info     ] Epoch 43/150, Train Loss: 7.3845, Val Loss: 11.2165 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.764547Z [info     ] Epoch 44/150, Train Loss: 8.0462, Val Loss: 11.1421 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.872361Z [info     ] Epoch 45/150, Train Loss: 7.0977, Val Loss: 10.3305 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:07.992379Z [info     ] Epoch 46/150, Train Loss: 7.5521, Val Loss: 10.5837 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.087215Z [info     ] Epoch 47/150, Train Loss: 7.9009, Val Loss: 12.1171 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.192313Z [info     ] Epoch 48/150, Train Loss: 7.5840, Val Loss: 12.6061 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.299023Z [info     ] Epoch 49/150, Train Loss: 7.4607, Val Loss: 11.8926 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.392574Z [info     ] Epoch 50/150, Train Loss: 8.0541, Val Loss: 11.9577 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.512588Z [info     ] Epoch 51/150, Train Loss: 7.6786, Val Loss: 12.7283 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.619204Z [info     ] Epoch 52/150, Train Loss: 7.5095, Val Loss: 13.2044 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.712607Z [info     ] Epoch 53/150, Train Loss: 7.4790, Val Loss: 12.6466 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.805859Z [info     ] Epoch 54/150, Train Loss: 8.8708, Val Loss: 12.2825 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:08.931255Z [info     ] Epoch 55/150, Train Loss: 7.1066, Val Loss: 13.9959 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.028775Z [info     ] Epoch 56/150, Train Loss: 7.8124, Val Loss: 13.4180 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.112617Z [info     ] Epoch 57/150, Train Loss: 7.6429, Val Loss: 12.4938 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.214798Z [info     ] Epoch 58/150, Train Loss: 7.9417, Val Loss: 13.3702 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.312839Z [info     ] Epoch 59/150, Train Loss: 7.5449, Val Loss: 15.3564 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.404978Z [info     ] Epoch 60/150, Train Loss: 7.7745, Val Loss: 14.1075 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.498443Z [info     ] Epoch 61/150, Train Loss: 7.7012, Val Loss: 12.8864 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.592702Z [info     ] Epoch 62/150, Train Loss: 7.9853, Val Loss: 13.0014 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.711878Z [info     ] Epoch 63/150, Train Loss: 7.6206, Val Loss: 12.7743 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.814553Z [info     ] Epoch 64/150, Train Loss: 7.3781, Val Loss: 13.1352 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.913069Z [info     ] Epoch 65/150, Train Loss: 7.2564, Val Loss: 13.6063 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.938889Z [info     ] Early stopping triggered at epoch 65. [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1385.88, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:09.991348Z [info     ] Loaded best model state with validation loss: 10.3305 [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1386.5, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:10.006239Z [info     ] Performing final evaluation on the full training dataset... [root] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1386.5, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:10.066055Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.models.assets_pytorch artifact_path=dnn_hybrid_training\\model_assets_pytorch.joblib context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1386.98, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:10.099322Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_training.data_snapshots.final_training_history artifact_path=dnn_hybrid_training\\final_training_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1386.99, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:10.126889Z [info     ] ===== Hybrid DNN Training Step Finished ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1386.99, 'cpu_percent': 0.0}\n", "  - DNN 训练完成，最佳超参数: {'cnn_filters': 8.0, 'cnn_kernel_size': 5.0, 'dropout_rate': 0.21728034636230623, 'learning_rate': 0.004403389633553295, 'mlp_units': 64.0, 'weight_decay': 4.133885637299337e-05}\n", "2025-07-29T06:20:10.187715Z [info     ] ===== Hybrid DNN Prediction Step Started ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1387.03, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:10.206483Z [info     ] Curve names successfully resolved to DataFrame column names (Prediction). [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1387.03, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:16.070451Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1401.49, 'cpu_percent': 0.0} curve_name=K_DNN_PRED curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T06:20:16.102118Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=dnn_hybrid_prediction.datasets.predictions artifact_path=dnn_hybrid_prediction\\dnn_hybrid_predictions.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.82, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:16.129860Z [info     ] ===== Hybrid DNN Prediction Step Finished ===== [scape.core.baselines.hybrid_dnn.facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.82, 'cpu_percent': 0.0} output_curve=K_DNN_PRED predicted_samples=4689\n", "  - DNN 预测完成，已将 'K_DNN_PRED' 添加到应用集。\n"]}], "source": ["print(\"🚀 [Step 3/4] 开始执行 Hybrid DNN 基准模型...\")\n", "\n", "# --- 训练 ---\n", "well_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "dnn_train_config = DnnTrainingConfig(\n", "    n_trials=50,\n", "    max_epochs_per_trial = 100,\n", "    final_train_epochs = 150,\n", "    patience=20,\n", "    batch_size=48, #这个参数影响很大，越小性能越好\n", "    random_seed=2025)\n", "dnn_train_result = run_dnn_training_step(\n", "    config=dnn_train_config,\n", "    ctx=run_context,\n", "    train_bundle=train_bundle,\n", "    sequence_feature='T2_VALUE',\n", "    normalization_feature='PHIT_NMR',\n", "    tabular_features=['PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR'],\n", "    target_feature='K_LABEL',\n", "    grouping_feature=well_name,\n", "    t2_time_axis=t2_time_array,\n", ")\n", "print(f\"  - DNN 训练完成，最佳超参数: {dnn_train_result['best_hyperparameters']}\")\n", "\n", "# --- 预测 ---\n", "from scape.core.baselines.hybrid_dnn.artifact_handler import DnnArtifactHandler\n", "handler = DnnArtifactHandler()\n", "model_params_path = run_context.get_artifact_path(DnnArtifacts.MODEL_ASSETS.value)\n", "dnn_model_assets = handler.load_model_assets(model_params_path)\n", "dnn_pred_result = run_dnn_prediction_step(\n", "    config=DnnPredictionConfig(),\n", "    ctx=run_context,\n", "    model_assets=dnn_model_assets,\n", "    prediction_bundle=apply_bundle, # 继续修改同一个应用集Bundle\n", "    output_curve_name='K_DNN_PRED'\n", ")\n", "print(f\"  - DNN 预测完成，已将 'K_DNN_PRED' 添加到应用集。\")"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["## 6. 统一验证：PLT 盲井检验\n", "\n", "现在，`apply_bundle` 中已经包含了三个不同基准模型预测的渗透率曲线。我们将循环调用 `run_plt_analysis_step`，对每一条预测曲线进行独立的盲井检验，并通过 `prefix` 参数来区分不同模型的产物。"]}, {"cell_type": "code", "execution_count": 10, "id": "w5x6y7z8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 4/4] 开始执行统一的 PLT 盲井检验...\n", "--- 正在验证模型: SDR (曲线: K_SDR_PRED) ---\n", "2025-07-29T06:20:16.355526Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.64, 'cpu_percent': 0.0} step_name=sdr_plt_analysis\n", "2025-07-29T06:20:16.381103Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:16.394360Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.66, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:20:16.422019Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:16.453680Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:16.475342Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:16.515320Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:16.542031Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:16.568746Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:16.595534Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:16.634366Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.9, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:16.674966Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.9, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:16.688390Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.9, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:16.714904Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1395.9, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:16.768941Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1396.29, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:16.795533Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1396.34, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:17.035805Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1399.95, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\sdr_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T06:20:17.062402Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1399.95, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:17.104127Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1399.95, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:17.130045Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1399.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:17.155478Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1399.95, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:17.195476Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1399.95, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:17.208819Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1399.95, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:17.436019Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.75, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\sdr_plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T06:20:17.462625Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.75, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:17.501426Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.75, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:17.515329Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:17.542036Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:17.581887Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.75, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:17.595325Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1402.75, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:17.822762Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.61, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\sdr_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T06:20:17.862751Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=sdr_plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.61, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:17.875795Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.61, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:20:17.915919Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.62, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:17.941490Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.62, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:17.969288Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.62, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:17.995837Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.62, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:18.022052Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.63, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:18.048995Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.64, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:18.075330Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.64, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:18.115638Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.64, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:18.142587Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.64, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:18.155933Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:18.182540Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:18.222745Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.76, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:18.235930Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1405.78, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:18.475235Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1408.89, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\sdr_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T06:20:18.502977Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1408.89, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:18.529362Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1408.89, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:18.556004Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1408.89, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:18.583164Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1408.89, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:18.636794Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1409.03, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:18.649198Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1409.06, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:18.889742Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1412.23, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\sdr_plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T06:20:18.929882Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1412.23, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:18.956331Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1412.23, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:18.982942Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1412.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:19.011143Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1412.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:19.049194Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1412.38, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:19.062927Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\baselines_run_20250729_141851\\sdr_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1412.42, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:19.275536Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1415.87, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\sdr_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T06:20:19.303287Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=sdr_plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1415.87, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:19.329779Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1415.87, 'cpu_percent': 0.0}\n", "  - SDR 模型验证完成。\n", "\n", "--- 正在验证模型: TIMUR (曲线: K_TIMUR_PRED) ---\n", "2025-07-29T06:20:19.355514Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1415.87, 'cpu_percent': 0.0} step_name=timur_plt_analysis\n", "2025-07-29T06:20:19.368857Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1415.89, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:19.369763Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1415.89, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:20:19.396426Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.05, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:19.435959Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.06, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:19.475937Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.06, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:19.502776Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:19.529663Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:19.556384Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:19.595503Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:19.638589Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=timur_plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.11, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:19.676563Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.11, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:19.703338Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:19.742238Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:19.782562Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.38, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:19.795982Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\baselines_run_20250729_141851\\timur_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1416.4, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:20.027277Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1419.41, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\timur_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T06:20:20.059530Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=timur_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1419.41, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:20.091498Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1419.41, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:20.114664Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1419.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:20.149566Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1419.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:20.181405Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1419.59, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:20.214870Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\baselines_run_20250729_141851\\timur_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1419.62, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:20.435215Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1422.97, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\timur_plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T06:20:20.467140Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1422.97, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:20.499105Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1422.97, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:20.516148Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1422.97, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:20.549332Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1422.97, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:20.581086Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1423.16, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:20.599730Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\baselines_run_20250729_141851\\timur_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1423.19, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:20.795854Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\timur_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T06:20:20.827701Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=timur_plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:20.867947Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:20:20.917411Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:20.961648Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:21.000825Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:21.030465Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:21.057487Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.53, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:21.092765Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.54, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:21.120496Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.55, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:21.158769Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=timur_plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.55, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:21.174786Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.55, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:21.190623Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=timur_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.55, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:21.222323Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.55, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:21.270292Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.68, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:21.286186Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\baselines_run_20250729_141851\\timur_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1426.71, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:21.501884Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1429.86, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\timur_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T06:20:21.536486Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=timur_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1429.86, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:21.565721Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1429.86, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:21.588759Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=timur_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1429.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:21.627624Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1429.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:21.663056Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.04, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:21.678962Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\baselines_run_20250729_141851\\timur_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.08, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:21.928739Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1433.43, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\timur_plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T06:20:21.960115Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1433.43, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:21.991883Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1433.43, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:22.007608Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=timur_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1433.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:22.039581Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1433.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:22.071557Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1433.73, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:22.093958Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\baselines_run_20250729_141851\\timur_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1433.77, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:22.308813Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.11, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\timur_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T06:20:22.340440Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=timur_plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.11, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:22.356365Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.11, 'cpu_percent': 0.0}\n", "  - TIMUR 模型验证完成。\n", "\n", "--- 正在验证模型: DNN (曲线: K_DNN_PRED) ---\n", "2025-07-29T06:20:22.372327Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.11, 'cpu_percent': 0.0} step_name=dnn_plt_analysis\n", "2025-07-29T06:20:22.388141Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.11, 'cpu_percent': 0.0}\n", "2025-07-29T06:20:22.404036Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.11, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T06:20:22.430045Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.24, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:22.467375Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.25, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:22.503338Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.25, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:22.551345Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.25, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:22.583176Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.25, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:22.598983Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:22.639097Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.27, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:22.688842Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.29, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:22.730803Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.29, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:22.752988Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:22.768781Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:22.816565Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.41, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:22.832345Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1437.43, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:23.071437Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1440.58, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\dnn_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T06:20:23.103522Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1440.58, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:23.132828Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1440.58, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:23.180758Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1440.58, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:23.210272Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1440.58, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:23.258208Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1440.71, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:23.273935Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1440.74, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:23.497477Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.17, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\dnn_plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T06:20:23.534796Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.18, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:23.560793Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.18, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:23.592870Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:23.637353Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:23.672650Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.47, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:23.695152Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1444.5, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:23.926740Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.86, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\dnn_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T06:20:23.958262Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=dnn_plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.86, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:23.974396Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.86, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T06:20:24.006232Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:24.038344Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.86, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:24.063983Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.87, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:24.103992Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.88, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:24.120014Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.9, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:24.145038Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.9, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:24.175669Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.91, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:20:24.207974Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.91, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:24.242192Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.91, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:24.258046Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.91, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:24.273749Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1447.91, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:24.322015Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1448.05, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T06:20:24.338393Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1448.08, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:24.577593Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1451.25, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\dnn_plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T06:20:24.609285Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1451.25, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:24.645929Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1451.25, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:24.661756Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1451.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:24.677689Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1451.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:24.725555Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1451.43, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T06:20:24.741680Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1451.44, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:24.985765Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1455.06, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\dnn_plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T06:20:25.017685Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1455.07, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:25.065500Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1455.07, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:25.086023Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1455.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:25.113120Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1455.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:25.150509Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1455.35, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T06:20:25.165423Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\baselines_run_20250729_141851\\dnn_plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1455.38, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T06:20:25.366880Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1458.84, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\dnn_plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T06:20:25.401448Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=dnn_plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1458.84, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:20:25.413799Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1458.84, 'cpu_percent': 0.0}\n", "  - DNN 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"sdr\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.42857142857142866,\n", "      \"gini_capture\": 0.3953631010250631,\n", "      \"gini_lorenz\": 0.404532955505697\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.3571428571428572,\n", "      \"gini_capture\": 0.21257750409937892,\n", "      \"gini_lorenz\": 0.18354163046179917\n", "    }\n", "  },\n", "  \"timur\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.25,\n", "      \"gini_capture\": 0.3513401288404263,\n", "      \"gini_lorenz\": 0.10871281695926971\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.642857142857143,\n", "      \"gini_capture\": -0.28910402953673175,\n", "      \"gini_lorenz\": 0.3905745306813474\n", "    }\n", "  },\n", "  \"dnn\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.6785714285714287,\n", "      \"gini_capture\": 0.6364089205739811,\n", "      \"gini_lorenz\": 0.26368097456489237\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.8571428571428573,\n", "      \"gini_capture\": 0.0897681461984996,\n", "      \"gini_lorenz\": 0.48933457363252986\n", "    }\n", "  }\n", "}\n"]}], "source": ["print(\"🚀 [Step 4/4] 开始执行统一的 PLT 盲井检验...\")\n", "\n", "models_to_validate = {\n", "    \"sdr\": \"K_SDR_PRED\",\n", "    \"timur\": \"K_TIMUR_PRED\",\n", "    \"dnn\": \"K_DNN_PRED\"\n", "}\n", "\n", "validation_results = {}\n", "\n", "for model_prefix, perm_curve_name in models_to_validate.items():\n", "    print(f\"--- 正在验证模型: {model_prefix.upper()} (曲线: {perm_curve_name}) ---\")\n", "\n", "    # 1. 创建PLT分析配置\n", "    plt_config = PltAnalysisConfig()\n", "\n", "    # 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "    contribution_profile = plot_registry.get(PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT).with_updates(\n", "        title_props={\"label\": f\"Flow Contribution Crossplot ({model_prefix.upper()})\"}\n", "    )\n", "    capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE).with_updates(\n", "        title_props={\"label\": f\"Permeability Capture Curve ({model_prefix.upper()})\"}\n", "    )\n", "    lorenz_profile = plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE).with_updates(\n", "        title_props={\"label\": f\"Lorenz Curve Analysis ({model_prefix.upper()})\"}\n", "    )\n", "    plt_plot_profiles = {\n", "        PltPlotTypes.CONTRIBUTION_CROSSPLOT: contribution_profile,\n", "        PltPlotTypes.CAPTURE_CURVE: capture_profile,\n", "        PltPlotTypes.LORENZ_CURVE: lorenz_profile\n", "    }\n", "\n", "    # 3. 执行PLT分析步骤\n", "    plt_result = run_plt_analysis_step(\n", "        config=plt_config,\n", "        ctx=run_context,\n", "        prediction_bundle=apply_bundle, # 使用已包含所有预测结果的应用集\n", "        plt_bundle=plt_bundle,\n", "        permeability_curve=perm_curve_name, # 指定当前要验证的曲线\n", "        flow_rate_curve=\"QOZI\",\n", "        plot_profiles=plt_plot_profiles,\n", "        prefix=model_prefix # 使用模型名为前缀，区分产物\n", "    )\n", "    validation_results[model_prefix] = plt_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(validation_results, indent=2))"]}, {"cell_type": "markdown", "id": "cf9915b8", "metadata": {}, "source": ["## 7. T-1井MDT检验"]}, {"cell_type": "code", "execution_count": 12, "id": "41f99fde", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 5/5] 开始执行统一的 岩心渗透率 检验...\n", "--- 正在验证模型: SDR (曲线: K_SDR_PRED) ---\n", "2025-07-29T06:21:38.537860Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1459.66, 'cpu_percent': 0.0} step_name=sdr_perm_corr_analysis\n", "2025-07-29T06:21:38.559445Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1459.66, 'cpu_percent': 0.0}\n", "2025-07-29T06:21:38.575753Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1459.66, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T06:21:38.606574Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1460.25, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:38.637143Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1460.26, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:21:38.671142Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1460.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:38.697721Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1460.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:38.740384Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1460.61, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:21:38.760671Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\baselines_run_20250729_141851\\sdr_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1460.65, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:21:40.242759Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.05, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\baselines_run_20250729_141851\\\\sdr_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T06:21:40.280974Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=sdr_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.05, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:40.317005Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.05, 'cpu_percent': 0.0}\n", "  - SDR 模型验证完成。\n", "\n", "--- 正在验证模型: TIMUR (曲线: K_TIMUR_PRED) ---\n", "2025-07-29T06:21:40.326744Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.05, 'cpu_percent': 0.0} step_name=timur_perm_corr_analysis\n", "2025-07-29T06:21:40.344397Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.11, 'cpu_percent': 0.0}\n", "2025-07-29T06:21:40.348405Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.11, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T06:21:40.377728Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.71, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:40.402578Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.72, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:21:40.418723Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.72, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:40.451036Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1470.74, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:40.494361Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1471.09, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:21:40.513449Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\baselines_run_20250729_141851\\timur_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1471.15, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:21:41.904862Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1476.79, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\baselines_run_20250729_141851\\\\timur_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T06:21:41.940657Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=timur_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1476.79, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:41.967436Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1476.79, 'cpu_percent': 0.0}\n", "  - TIMUR 模型验证完成。\n", "\n", "--- 正在验证模型: DNN (曲线: K_DNN_PRED) ---\n", "2025-07-29T06:21:41.992180Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1476.79, 'cpu_percent': 0.0} step_name=dnn_perm_corr_analysis\n", "2025-07-29T06:21:42.007425Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1476.8, 'cpu_percent': 0.0}\n", "2025-07-29T06:21:42.019409Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1476.8, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T06:21:42.045130Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1477.44, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:42.071873Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1477.44, 'cpu_percent': 0.0} path=output01\\baselines_run_20250729_141851\\dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:21:42.091075Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1477.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:42.117940Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1477.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:42.152861Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1477.54, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T06:21:42.176875Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\baselines_run_20250729_141851\\dnn_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1477.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T06:21:43.827078Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\baselines_run_20250729_141851\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\baselines_run_20250729_141851\\\\dnn_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T06:21:43.876523Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=dnn_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-061851-4c594b3c\n", "2025-07-29T06:21:43.907487Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0}\n", "  - DNN 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"sdr\": {\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.66213256010534,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 41.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 75.0% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"timur\": {\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.7398728201986795,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 33.3% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 66.7% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"dnn\": {\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.66213256010534,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 16.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 58.3% <= 90%\\u3002\"\n", "    }\n", "  }\n", "}\n"]}], "source": ["from scape.core.validation.config import PermCorrelationConfig\n", "from scape.core.validation.constants import PermCorrelationPlotProfiles\n", "from scape.core.validation.perm_corr_facade import run_perm_correlation_step\n", "\n", "\n", "print(\"🚀 [Step 5/5] 开始执行统一的 岩心渗透率 检验...\")\n", "\n", "models_to_validate = {\n", "    \"sdr\": \"K_SDR_PRED\",\n", "    \"timur\": \"K_TIMUR_PRED\",\n", "    \"dnn\": \"K_DNN_PRED\"\n", "}\n", "\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "\n", "perm_corr_results = {}\n", "\n", "for model_prefix, perm_curve_name in models_to_validate.items():\n", "    print(f\"--- 正在验证模型: {model_prefix.upper()} (曲线: {perm_curve_name}) ---\")\n", "\n", "\n", "    perm_corr_config = PermCorrelationConfig()\n", "\n", "    perm_corr_profile = original_perm_corr_profile.with_updates(\n", "        title_props={\"label\": f\"Core Perm vs. Predicted Perm ({model_prefix.upper()})\"},\n", "        save_config={\"format\": [\"png\", \"svg\"]}\n", "    )\n", "\n", "    perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle, # 使用带预测结果的应用集\n", "    right_bundle=mdt_val_bundle,\n", "    left_curve=perm_curve_name, # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=model_prefix\n", "    )\n", "\n", "    perm_corr_results[model_prefix] = perm_corr_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(perm_corr_results, indent=2))"]}, {"cell_type": "markdown", "id": "i9j0k1l2", "metadata": {}, "source": ["## 8. 总结与产物保存\n", "\n", "将包含了所有基准模型预测结果的应用集数据保存为一个新的 `.wp.xlsx` 文件，并最终化本次实验运行。"]}, {"cell_type": "code", "execution_count": 13, "id": "n2o3p4q5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T06:21:49.593904Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} copy_data=False new_dataset_name=apply_with_predictions operation=add_dataframe_bundle project_name=WpIdentifier('baselines_apply_result')\n", "2025-07-29T06:21:49.622595Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T06:21:49.645598Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=apply_with_predictions dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T06:21:49.661714Z [info     ] 成功添加 'apply_with_predictions' (WpContinuousDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('baselines_apply_result')\n", "2025-07-29T06:21:49.688225Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} dataset_count=1 file_path=output01\\baselines_run_20250729_141851\\baselines_apply_result.wp.xlsx project_name=WpIdentifier('baselines_apply_result') save_head_info=True save_well_map=True\n", "2025-07-29T06:21:49.713595Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1430.37, 'cpu_percent': 0.0} curve_count=12 dataset_name=WpIdentifier('apply_with_predictions') dataset_type=Continuous df_shape=(4689, 75)\n", "2025-07-29T06:21:51.393423Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1507.17, 'cpu_percent': 0.0} dataset_name=WpIdentifier('apply_with_predictions') processing_time=1.68\n", "2025-07-29T06:21:51.414980Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1504.54, 'cpu_percent': 0.0} dataset_count=1 head_info=False total_sheets=1 well_map=False\n", "2025-07-29T06:21:51.445142Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1504.54, 'cpu_percent': 0.0}\n", "2025-07-29T06:21:51.457610Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1504.54, 'cpu_percent': 0.0} workbook_sheets=1\n", "2025-07-29T06:22:01.581644Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1545.45, 'cpu_percent': 0.0}\n", "2025-07-29T06:22:01.594792Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1545.45, 'cpu_percent': 0.0}\n", "2025-07-29T06:22:07.535529Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1547.89, 'cpu_percent': 0.0} file_path=output01\\baselines_run_20250729_141851\\baselines_apply_result.wp.xlsx processing_time=17.847 project_name=WpIdentifier('baselines_apply_result')\n", "✅ 包含所有预测结果的应用集已保存至: output01\\baselines_run_20250729_141851\\baselines_apply_result.wp.xlsx\n", "2025-07-29T06:22:07.569487Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 1547.89, 'cpu_percent': 0.0} duration_seconds=196.008 manifest_path=output01\\baselines_run_20250729_141851\\manifest.json operation=finalize run_id=20250729-061851-4c594b3c status=RUNNING\n", "✅ 运行已最终化，所有记录已保存。\n"]}], "source": ["# --- 保存最终结果 ---\n", "final_project = WpWellProject(name=\"baselines_apply_result\")\n", "final_project.add_dataframe_bundle(\"apply_with_predictions\", apply_bundle)\n", "\n", "writer = WpExcelWriter()\n", "final_result_path = run_context.run_dir / \"baselines_apply_result.wp.xlsx\"\n", "writer.write(final_project, final_result_path, apply_formatting=True)\n", "print(f\"✅ 包含所有预测结果的应用集已保存至: {final_result_path}\")\n", "\n", "# --- 最终化运行 ---\n", "run_context.finalize()\n", "print(\"✅ 运行已最终化，所有记录已保存。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}