"""数据概况生成配置。

提供数据概况生成的配置管理，包括格式、模板、性能等设置。

Architecture
------------
层次/依赖: models/internal配置层
设计原则: 配置集中管理、类型安全、可扩展
使用范围: 数据概况生成服务

Examples:
    >>> config = SummaryConfig()
    >>> print(config.default_format)
    >>> config.enable_cache = False

References:
    《SCAPE_DDS_logwp_generate_summary.md》- 配置管理设计
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Literal


@dataclass
class SummaryConfig:
    """数据概况生成配置。
    
    集中管理数据概况生成的各种配置选项。
    """
    
    # 默认设置
    default_format: Literal["json", "markdown"] = "markdown"
    default_template: str = "default"
    
    # 模板设置
    template_dir: Path | None = None
    enable_template_cache: bool = True
    
    # 性能设置
    enable_cache: bool = True
    max_cache_size: int = 100
    
    # 输出设置
    datetime_format: str = "%Y-%m-%d %H:%M:%S"
    float_precision: int = 2
    
    # 错误处理
    strict_mode: bool = False  # 严格模式下遇到错误立即停止
    fallback_to_builtin: bool = True  # 模板失败时回退到内置模板
    
    # 国际化
    default_locale: str = "zh_CN"
    
    @classmethod
    def create_default(cls) -> SummaryConfig:
        """创建默认配置。
        
        Returns:
            SummaryConfig: 默认配置实例
        """
        return cls()
    
    @classmethod
    def create_compact(cls) -> SummaryConfig:
        """创建紧凑模式配置。
        
        Returns:
            SummaryConfig: 紧凑模式配置
        """
        return cls(
            default_template="compact",
            enable_template_cache=False,
            float_precision=1
        )
    
    @classmethod
    def create_english(cls) -> SummaryConfig:
        """创建英文模式配置。
        
        Returns:
            SummaryConfig: 英文模式配置
        """
        return cls(
            default_template="english",
            default_locale="en_US"
        )
    
    def get_available_formats(self) -> list[str]:
        """获取可用的输出格式。
        
        Returns:
            list[str]: 可用格式列表
        """
        return ["json", "markdown"]
    
    def get_available_templates(self) -> list[str]:
        """获取可用的模板。
        
        Returns:
            list[str]: 可用模板列表
        """
        # 这里可以动态扫描模板目录
        return ["default", "english", "compact"]
    
    def validate(self) -> bool:
        """验证配置的有效性。
        
        Returns:
            bool: 配置是否有效
            
        Raises:
            ValueError: 配置无效
        """
        if self.default_format not in self.get_available_formats():
            raise ValueError(f"无效的默认格式: {self.default_format}")
        
        if self.default_template not in self.get_available_templates():
            raise ValueError(f"无效的默认模板: {self.default_template}")
        
        if self.float_precision < 0 or self.float_precision > 10:
            raise ValueError(f"无效的浮点精度: {self.float_precision}")
        
        return True


# 全局默认配置实例
_default_config = SummaryConfig.create_default()


def get_default_config() -> SummaryConfig:
    """获取全局默认配置。
    
    Returns:
        SummaryConfig: 默认配置实例
    """
    return _default_config


def set_default_config(config: SummaryConfig) -> None:
    """设置全局默认配置。
    
    Args:
        config: 新的默认配置
        
    Raises:
        ValueError: 配置无效
    """
    global _default_config
    config.validate()
    _default_config = config
