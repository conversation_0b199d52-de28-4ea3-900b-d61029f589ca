"""logwp.extras.plotting.registry - 配置注册表和继承体系

实现PlotProfileRegistry类，支持两级基础模板继承体系和层叠合并逻辑。

Architecture
------------
层次/依赖: logwp.extras.plotting包核心层
设计原则: 注册表模式、层叠继承、单例管理
性能特征: 配置缓存、延迟合并、深度拷贝

遵循CCG规范：
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持完整性
- LG-2: 结构化日志信息
- CT-1: 分层枚举常量

两级继承体系：
- 全局根模板 (base): 项目级通用样式
- 模块级基础模板 (module.base): 模块内通用样式
- 具体图表模板 (module.chart): 特定图表样式

References
----------
- 《绘图系统全新重构设计文档》§附录 - 两级基础模板继承体系设计
- 《SCAPE_CCG_编码与通用规范》§TS - 现代化类型系统
"""

from __future__ import annotations

import json
from copy import deepcopy
from enum import Enum
from pathlib import Path
from typing import Dict, Optional, Union

from .exceptions import ProfileNotFoundError, ProfileRegistrationError, ProfileIOError
from .profiles import PlotProfile

# 延迟导入logger避免循环依赖
def _get_logger():
    try:
        from logwp.infra import get_logger
        return get_logger(__name__)
    except ImportError:
        import logging
        return logging.getLogger(__name__)

def _normalize_profile_name(name: Union[str, Enum]) -> str:
    """将枚举成员或字符串统一转换为字符串名称。"""
    if isinstance(name, Enum):
        return str(name.value)
    if isinstance(name, str):
        return name
    raise TypeError(
        f"配置模板名称必须是 str 或 Enum 类型，但收到了 {type(name).__name__}"
    )

class PlotProfileRegistry:
    """绘图配置注册表。

    中心化管理所有绘图配置模板，支持两级基础模板继承体系。
    实现层叠合并逻辑，提供配置的注册、获取、持久化功能。

    继承体系：
        全局根模板 (base)
        └── 模块级基础模板 (module.base)
            └── 具体图表模板 (module.chart)

    Examples:
        >>> registry = PlotProfileRegistry()
        >>>
        >>> # 注册全局基础模板
        >>> base_profile = PlotProfile(name="base", rc_params={"font.size": 10})
        >>> registry.register_base(base_profile)
        >>>
        >>> # 注册模块级基础模板
        >>> module_base = PlotProfile(name="plt_analyzer.base", figure_props={"figsize": (8, 8)})
        >>> registry.register_base(module_base)
        >>>
        >>> # 注册具体图表模板
        >>> chart_profile = PlotProfile(name="plt_analyzer.capture_curve", title_props={"label": "Capture Curve"})
        >>> registry.register(chart_profile)
        >>>
        >>> # 获取合并后的配置（自动三级继承）
        >>> merged = registry.get("plt_analyzer.capture_curve")
    """

    def __init__(self) -> None:
        """初始化注册表。"""
        self._profiles: Dict[str, PlotProfile] = {}
        self._base_profiles: Dict[str, PlotProfile] = {}

        _get_logger().info("绘图配置注册表已初始化", operation="registry_init")

    def register(self, profile: PlotProfile, overwrite: bool = False) -> None:
        """注册具体的图表配置模板。

        Args:
            profile: 要注册的配置模板
            overwrite: 是否允许覆盖已存在的同名模板

        Raises:
            ProfileRegistrationError: 注册失败
        """
        profile_name = _normalize_profile_name(profile.name)

        if profile_name in self._profiles and not overwrite:
            raise ProfileRegistrationError(
                f"配置模板 '{profile_name}' 已存在，使用 overwrite=True 强制覆盖",
                profile_name=profile_name,
                operation="register"
            )

        # 确保profile内部的name也是字符串
        profile.name = profile_name
        self._profiles[profile_name] = profile

        _get_logger().info(
            "配置模板已注册",
            operation="register_profile",
            profile_name=profile_name,
            overwrite=overwrite
        )

    def register_base(self, profile: PlotProfile) -> None:
        """注册基础模板（全局或模块级）。

        Args:
            profile: 基础配置模板，名称应为 "base" 或 "module.base"

        Raises:
            ProfileRegistrationError: 注册失败
        """
        profile_name = _normalize_profile_name(profile.name)

        if not (profile_name == "base" or profile_name.endswith(".base")):
            raise ProfileRegistrationError(
                f"基础模板名称必须为 'base' 或以 '.base' 结尾，当前: '{profile_name}'",
                profile_name=profile_name,
                operation="register_base"
            )

        # 确保profile内部的name也是字符串
        profile.name = profile_name
        self._base_profiles[profile_name] = profile

        _get_logger().info(
            "基础模板已注册",
            operation="register_base_profile",
            profile_name=profile_name
        )

    def get(self, name: Union[str, Enum], clone: bool = True) -> PlotProfile:
        """获取配置模板，自动执行三级层叠合并。

        合并顺序：
        1. 全局根模板 (base)
        2. 模块级基础模板 (module.base)
        3. 具体图表模板 (module.chart)

        Args:
            name: 配置模板名称
            clone: 是否返回深拷贝，避免意外修改

        Returns:
            PlotProfile: 合并后的配置模板

        Raises:
            ProfileNotFoundError: 找不到指定的配置模板
        """
        profile_name = _normalize_profile_name(name)

        if profile_name not in self._profiles:
            available = list(self._profiles.keys())
            raise ProfileNotFoundError(
                f"配置模板 '{profile_name}' 不存在",
                profile_name=profile_name,
                available_profiles=available
            )

        # 获取目标配置
        target_profile = self._profiles[profile_name]

        # 开始三级层叠合并
        result = self._perform_cascade_merge(target_profile)

        _get_logger().debug(
            "配置模板已获取",
            operation="get_profile",
            profile_name=profile_name,
            clone=clone
        )

        return deepcopy(result) if clone else result

    def get_base(self, name: Union[str, Enum], clone: bool = True) -> PlotProfile:
        """获取基础模板，不执行层叠合并。

        直接获取基础模板的原始配置，不进行任何继承合并。
        适用于需要访问基础模板本身的场景。

        Args:
            name: 基础模板名称，应为 "base" 或 "module.base" 格式
            clone: 是否返回深拷贝，避免意外修改

        Returns:
            PlotProfile: 基础模板配置

        Raises:
            ProfileNotFoundError: 找不到指定的基础模板

        Examples:
            >>> # 获取全局基础模板
            >>> base_profile = registry.get_base("base")
            >>>
            >>> # 获取模块级基础模板
            >>> module_base = registry.get_base("swift_pso.base")
        """
        profile_name = _normalize_profile_name(name)

        if profile_name not in self._base_profiles:
            available = list(self._base_profiles.keys())
            raise ProfileNotFoundError(
                f"基础模板 '{profile_name}' 不存在",
                profile_name=profile_name,
                available_profiles=available
            )

        base_profile = self._base_profiles[profile_name]

        _get_logger().debug(
            "基础模板已获取",
            operation="get_base_profile",
            profile_name=profile_name,
            clone=clone
        )

        return deepcopy(base_profile) if clone else base_profile

    def list_profiles(self) -> list[str]:
        """列出所有已注册的配置模板名称。

        Returns:
            list[str]: 配置模板名称列表
        """
        return list(self._profiles.keys())

    def list_base_profiles(self) -> list[str]:
        """列出所有已注册的基础模板名称。

        Returns:
            list[str]: 基础模板名称列表
        """
        return list(self._base_profiles.keys())

    def is_registered(self, name: Union[str, Enum]) -> bool:
        """检查指定名称的配置模板是否已注册。

        查找范围包括普通模板和基础模板。

        Args:
            name: 要检查的配置模板名称

        Returns:
            bool: 如果已注册则返回True，否则返回False
        """
        profile_name = _normalize_profile_name(name)
        registered = profile_name in self._profiles or profile_name in self._base_profiles
        _get_logger().debug("检查配置模板注册状态", profile_name=profile_name, is_registered=registered)
        return registered

    def load_from_dir(self, dir_path: Union[str, Path]) -> None:
        """从目录扫描并加载JSON配置文件。

        扫描指定目录下的所有.json文件，尝试加载为PlotProfile。
        基础模板（名称包含.base）会自动注册为基础模板。

        Args:
            dir_path: 配置文件目录路径

        Raises:
            ProfileIOError: 目录扫描或文件加载失败
        """
        try:
            dir_path = Path(dir_path)
            if not dir_path.exists():
                _get_logger().warning("配置目录不存在", path=str(dir_path))
                return

            json_files = list(dir_path.glob("*.json"))
            loaded_count = 0

            for json_file in json_files:
                try:
                    profile = PlotProfile.from_json(json_file)

                    # 根据名称判断是否为基础模板
                    if profile.name == "base" or profile.name.endswith(".base"):
                        self.register_base(profile)
                    else:
                        self.register(profile, overwrite=True)

                    loaded_count += 1

                except Exception as e:
                    _get_logger().warning(
                        "跳过无效的配置文件",
                        file=str(json_file),
                        error=str(e)
                    )

            _get_logger().info(
                "配置目录加载完成",
                operation="load_from_dir",
                path=str(dir_path),
                loaded_count=loaded_count,
                total_files=len(json_files)
            )

        except Exception as e:
            raise ProfileIOError(
                f"配置目录扫描失败: {e}",
                file_path=str(dir_path),
                operation="scan"
            ) from e

    def save_to_dir(self, dir_path: Union[str, Path]) -> None:
        """将所有注册的配置保存到指定目录。

        每个配置模板保存为单独的JSON文件，文件名为模板名称。

        Args:
            dir_path: 保存目录路径

        Raises:
            ProfileIOError: 目录创建或文件保存失败
        """
        try:
            dir_path = Path(dir_path)
            dir_path.mkdir(parents=True, exist_ok=True)

            saved_count = 0

            # 保存基础模板
            for profile in self._base_profiles.values():
                file_path = dir_path / f"{profile.name}.json"
                profile.to_json(file_path)
                saved_count += 1

            # 保存具体模板
            for profile in self._profiles.values():
                file_path = dir_path / f"{profile.name}.json"
                profile.to_json(file_path)
                saved_count += 1

            _get_logger().info(
                "配置目录保存完成",
                operation="save_to_dir",
                path=str(dir_path),
                saved_count=saved_count
            )

        except Exception as e:
            raise ProfileIOError(
                f"配置目录保存失败: {e}",
                file_path=str(dir_path),
                operation="save_dir"
            ) from e

    def _perform_cascade_merge(self, target_profile: PlotProfile) -> PlotProfile:
        """执行三级层叠合并。

        Args:
            target_profile: 目标配置模板

        Returns:
            PlotProfile: 合并后的配置
        """
        result = target_profile

        # 第一层：与模块级基础模板合并
        module_base_name = self._extract_module_base_name(target_profile.name)
        if module_base_name and module_base_name in self._base_profiles:
            module_base = self._base_profiles[module_base_name]
            result = result.merge_with_base(module_base)

            _get_logger().debug(
                "已合并模块级基础模板",
                target=target_profile.name,
                module_base=module_base_name
            )

        # 第二层：与全局根模板合并
        if "base" in self._base_profiles:
            global_base = self._base_profiles["base"]
            result = result.merge_with_base(global_base)

            _get_logger().debug(
                "已合并全局根模板",
                target=target_profile.name,
                global_base="base"
            )

        return result

    def _extract_module_base_name(self, profile_name: str) -> Optional[str]:
        """从配置名称提取模块级基础模板名称。

        Args:
            profile_name: 配置模板名称，如 "plt_analyzer.capture_curve"

        Returns:
            Optional[str]: 模块级基础模板名称，如 "plt_analyzer.base"
        """
        if "." not in profile_name:
            return None

        module_prefix = profile_name.split(".")[0]
        return f"{module_prefix}.base"


# 创建模块级单例
registry = PlotProfileRegistry()

# 注册默认的全局根模板
from .profiles import SaveConfig

_default_global_base = PlotProfile(
    name="base",
    save_config=SaveConfig(
        format=["png"],
        dpi=150,
        transparent=False,
        bbox_inches="tight"
    ),
    rc_params={
        "font.family": "Arial",
        "font.size": 10,
        "figure.facecolor": "white",
        "axes.grid": False,
        "grid.alpha": 0.3,
    },
    figure_props={
        "dpi": 150,
        "layout": "constrained",
    }
)

registry.register_base(_default_global_base)

_get_logger().info("默认全局基础模板已注册", profile_name="base")
