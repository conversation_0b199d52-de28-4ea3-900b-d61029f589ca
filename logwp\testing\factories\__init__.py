"""logwp.testing.factories - 测试数据工厂

提供预定义的测试数据工厂类。

Examples
--------
>>> from logwp.testing.factories import TestDataFactory, MetadataFactory
>>> from logwp.testing.factories import SyntheticCurveFactory, FosterNMRTestDataFactory
>>>
>>> # 使用数据工厂创建预定义的测试项目
>>> project = TestDataFactory.santos_demo_project()
>>>
>>> # 使用元数据工厂创建标准元数据
>>> logging_metadata = MetadataFactory.standard_logging_metadata()
>>> nmr_metadata = MetadataFactory.nmr_metadata()
>>>
>>> # 使用合成曲线工厂生成物理意义的数据
>>> t2_dist = SyntheticCurveFactory.nmr_t2_distribution(depth_points=100, t2_bins=64)
>>>
>>> # 使用算法工厂生成测试数据
>>> input_ds, expected_perm = FosterNMRTestDataFactory.create_dataset_with_expected_perm()
"""

from .data_factory import TestDataFactory
from .metadata_factory import MetadataFactory
from .algorithm_data_factory import FosterNMRTestDataFactory
from .synthetic_curve_factory import SyntheticCurveFactory

__all__ = [
    "TestDataFactory",
    "MetadataFactory",
    "FosterNMRTestDataFactory",
    "SyntheticCurveFactory",
]
