"""logwp.extras.tracking.exceptions - 实验追踪系统专属异常

定义实验追踪系统的异常层次结构，遵循SCAPE项目异常处理规范。

Architecture
------------
层次/依赖: logwp.extras.tracking包异常层
设计原则: 结构化异常信息、异常链保持、分层异常树
性能特征: 轻量级异常对象、延迟格式化

遵循CCG规范：
- EH-1: Exception Groups支持（Python 3.11+）
- EH-2: 结构化异常上下文信息
- EH-3: 分层异常树设计
- EH-4: 异常链保持完整性

References
----------
- 《SCAPE_CCG_编码与通用规范》§EH - 现代化异常处理
- 《logwp_extras_tracking设计.md》§3.4 - 异常处理设计
"""

from __future__ import annotations

from typing import Optional

from logwp.infra.exceptions import WpError, ErrorContext


class TrackingError(WpError):
    """实验追踪系统的基础异常。

    所有tracking包相关的异常都应该继承自这个基类，
    形成清晰的异常层次结构。

    Examples:
        >>> try:
        ...     ctx.log_artifact("nonexistent.file")
        ... except TrackingError as e:
        ...     logger.error("实验追踪错误", error=str(e))
    """
    pass


class RunExistsError(TrackingError):
    """当试图覆盖一个已存在的运行但未设置overwrite=True时抛出。

    常见场景：
    1. 运行目录已存在且包含manifest.json
    2. 用户未明确指定overwrite=True
    3. 防止意外覆盖重要实验结果

    Attributes:
        run_dir (str): 冲突的运行目录路径
        existing_run_id (str): 已存在运行的ID

    Examples:
        >>> raise RunExistsError(
        ...     "Run directory already exists",
        ...     run_dir="output/run-001",
        ...     existing_run_id="20250720-103000-a1b2c3d4"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        run_dir: Optional[str] = None,
        existing_run_id: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.run_dir = run_dir
        self.existing_run_id = existing_run_id


class RunNotFoundError(TrackingError):
    """当加载一个不存在的运行时抛出。

    常见场景：
    1. 指定的运行目录不存在
    2. 运行目录存在但缺少manifest.json
    3. manifest.json格式损坏无法解析

    Attributes:
        run_dir (str): 请求的运行目录路径
        missing_component (str): 缺失的组件（directory/manifest/config）

    Examples:
        >>> raise RunNotFoundError(
        ...     "Run manifest not found",
        ...     run_dir="output/run-999",
        ...     missing_component="manifest"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        run_dir: Optional[str] = None,
        missing_component: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.run_dir = run_dir
        self.missing_component = missing_component


class ArtifactNotFoundError(TrackingError):
    """当在清单中找不到指定的产物时抛出。

    常见场景：
    1. 请求不存在的artifact名称
    2. artifact已注册但文件被外部删除
    3. artifact路径配置错误

    Attributes:
        artifact_name (str): 请求的产物名称
        run_id (str): 相关的运行ID
        registered_artifacts (list[str]): 当前已注册的产物列表

    Examples:
        >>> raise ArtifactNotFoundError(
        ...     "Artifact 'model.pkl' not found in run",
        ...     artifact_name="model.pkl",
        ...     run_id="run-001",
        ...     registered_artifacts=["config.yaml", "results.json"]
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        artifact_name: Optional[str] = None,
        run_id: Optional[str] = None,
        registered_artifacts: Optional[list[str]] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.artifact_name = artifact_name
        self.run_id = run_id
        self.registered_artifacts = registered_artifacts or []


class ModelNotRegisteredError(TrackingError):
    """当在注册表中找不到指定的模型或版本时抛出。

    常见场景：
    1. 请求不存在的模型名称
    2. 模型存在但版本不存在
    3. 注册表文件损坏或为空

    Attributes:
        model_name (str): 请求的模型名称
        version (str): 请求的模型版本
        available_models (list[str]): 当前可用的模型列表

    Examples:
        >>> raise ModelNotRegisteredError(
        ...     "Model 'unknown-model' not found in registry",
        ...     model_name="unknown-model",
        ...     available_models=["foster-nmr", "obmiq-permeability"]
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        model_name: Optional[str] = None,
        version: Optional[str] = None,
        available_models: Optional[list[str]] = None,  # 可用模型名称列表
        available_versions: Optional[list[str]] = None, # 可用模型版本列表
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.model_name = model_name
        self.version = version
        self.available_models = available_models or []
        self.available_versions = available_versions or []


class RegistryError(TrackingError):
    """与模型注册表文件读写相关的通用错误。

    常见场景：
    1. 注册表文件权限不足
    2. JSON格式错误或损坏
    3. 文件系统错误
    4. 并发访问冲突

    Attributes:
        registry_path (str): 注册表文件路径
        operation (str): 失败的操作类型（read/write/update）

    Examples:
        >>> raise RegistryError(
        ...     "Failed to write model registry",
        ...     registry_path="/path/to/registry.json",
        ...     operation="write"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        registry_path: Optional[str] = None,
        operation: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.registry_path = registry_path
        self.operation = operation


class ManifestError(TrackingError):
    """运行清单文件操作相关的错误。

    常见场景：
    1. manifest.json格式错误
    2. 必需字段缺失
    3. 数据类型不匹配
    4. 文件写入失败

    Attributes:
        manifest_path (str): 清单文件路径
        operation (str): 失败的操作类型（read/write/validate）
        invalid_field (str): 无效的字段名称

    Examples:
        >>> raise ManifestError(
        ...     "Invalid manifest format",
        ...     manifest_path="output/run-001/manifest.json",
        ...     operation="validate",
        ...     invalid_field="start_time_utc"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        manifest_path: Optional[str] = None,
        operation: Optional[str] = None,
        invalid_field: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.manifest_path = manifest_path
        self.operation = operation
        self.invalid_field = invalid_field


class ArtifactIOError(TrackingError):
    """产物文件I/O操作失败时抛出。

    常见场景：
    1. 源文件不存在或无法读取
    2. 目标目录权限不足
    3. 磁盘空间不足
    4. 文件复制/移动失败

    Attributes:
        source_path (str): 源文件路径
        target_path (str): 目标文件路径
        operation (str): 失败的I/O操作（copy/move/delete）

    Examples:
        >>> raise ArtifactIOError(
        ...     "Failed to copy artifact file",
        ...     source_path="/tmp/model.pkl",
        ...     target_path="output/run-001/models/model.pkl",
        ...     operation="copy"
        ... )
    """

    def __init__(
        self,
        message: str,
        *,
        source_path: Optional[str] = None,
        target_path: Optional[str] = None,
        operation: Optional[str] = None,
        context: Optional[ErrorContext] = None
    ) -> None:
        super().__init__(message, context=context)
        self.source_path = source_path
        self.target_path = target_path
        self.operation = operation
