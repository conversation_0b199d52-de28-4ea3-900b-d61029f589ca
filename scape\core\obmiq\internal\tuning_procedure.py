"""scape.core.obmiq.internal.tuning_procedure - OBMIQ超参数寻优规程

本模块负责执行完整的嵌套交叉验证和超参数寻优流程。

Architecture
------------
层次/依赖: scape/core/obmiq/internal层，被training_facade调用
设计原则: 封装复杂性、流程自动化、结果可复现

Classes:
    _Objective: Optuna的目标函数类，封装了单次试验的训练-验证循环。

Functions:
    run_hyperparameter_tuning_cv: 执行完整的LOWO-CV + Optuna嵌套寻优。

References:
    - 《scape_core_obmiq_pytorch版开发计划.md》§2.5, §5
"""
from __future__ import annotations
import copy
import random
from typing import Any, Dict, List, Tuple

import numpy as np
import optuna
import pandas as pd
from sklearn.metrics import r2_score, mean_squared_error
import torch
from logwp.infra import get_logger
from sklearn.model_selection import LeaveOneGroupOut, train_test_split
from torch.utils.data import DataLoader

from ..config import ObmiqTrainingConfig
from .data_handler import OBMIQDataset, create_dataloaders_for_fold
from .model_builder import OBMIQPyTorchModel, AdaptiveLossModule

logger = get_logger()


def _set_seed(seed: int):
    """设置所有相关的随机种子以确保可复现性。"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
        # 新增：确保cuDNN的行为是确定性的，以保证在GPU上的可复现性。
        # benchmark=False, deterministic=True 是实现可复现性的标准配置。
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False


def _train_and_evaluate_single_model(
    model: OBMIQPyTorchModel,
    criterion: torch.nn.Module,
    optimizer: torch.optim.Optimizer,
    train_loader: DataLoader,
    val_loader: DataLoader,
    config: ObmiqTrainingConfig,
    device: str,
    trial: optuna.Trial | None = None,
) -> float:
    """
    一个可重用的函数，封装了单次模型训练、验证和早停的核心逻辑。

    该函数执行一个完整的训练循环，并在每个epoch后进行验证。它实现了
    早停机制，并在训练结束后将性能最佳的模型权重加载回传入的model对象中。

    Args:
        model: 待训练的PyTorch模型。
        criterion: 损失函数。
        optimizer: 优化器。
        train_loader: 训练数据加载器。
        val_loader: 验证数据加载器。
        config: 训练配置。
        device: 计算设备 ('cpu' or 'cuda')。
        trial: (可选) Optuna的Trial对象，用于剪枝。

    Returns:
        在验证集上达到的最佳（最低）损失值。
    """
    best_val_loss = float("inf")
    patience_counter = 0
    best_model_state = None

    for epoch in range(config.max_epochs_per_trial):
        model.train()
        for batch in train_loader:
            inputs = {k: v.to(device) for k, v in batch.items() if k != "target"}
            targets = batch["target"].to(device)
            optimizer.zero_grad()
            predictions = model(inputs)
            loss = criterion(predictions, targets)
            loss.backward()
            optimizer.step()

        model.eval()
        total_val_loss = 0
        with torch.no_grad():
            for batch in val_loader:
                inputs = {k: v.to(device) for k, v in batch.items() if k != "target"}
                targets = batch["target"].to(device)
                predictions = model(inputs)
                total_val_loss += criterion(predictions, targets).item()
        avg_val_loss = total_val_loss / len(val_loader)

        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            best_model_state = copy.deepcopy(model.state_dict())
            patience_counter = 0
        else:
            patience_counter += 1
        if patience_counter >= config.patience:
            logger.debug(f"Early stopped at epoch {epoch+1}")
            break
        if trial:
            trial.report(avg_val_loss, epoch)
            if trial.should_prune():
                raise optuna.TrialPruned()

    if best_model_state:
        model.load_state_dict(best_model_state)
    return best_val_loss

def _create_inference_loader(
    df: pd.DataFrame,
    config: ObmiqTrainingConfig,
    feature_selectors: Dict[str, Any],
    preprocessors: Dict[str, Any],
) -> DataLoader:
    """使用已拟合的预处理器为推理(预测)创建DataLoader。

    此函数与 `create_dataloaders_for_fold` 的关键区别在于，它不重新拟合
    预处理器，而是使用外部传入的已拟合好的预处理器来转换数据。
    """
    seq_cols = feature_selectors["sequence_cols"]
    norm_col = feature_selectors["normalization_col"]
    tab_cols = feature_selectors["tabular_cols"]
    target_cols = feature_selectors["target_cols"]

    df_processed = df.copy()
    df_processed[tab_cols] = preprocessors["tabular_scaler"].transform(df[tab_cols])

    sequence_values = df_processed[seq_cols].values
    norm_values = df_processed[norm_col].values[:, np.newaxis]
    norm_values[norm_values == 0] = 1e-6
    normalized_sequence = sequence_values / norm_values

    tabular_values = df_processed[tab_cols].values
    target_values = df_processed[target_cols].values
    dataset = OBMIQDataset(normalized_sequence, tabular_values, target_values)
    return DataLoader(dataset, batch_size=config.batch_size, shuffle=False, num_workers=0)


class _Objective:
    """Optuna的目标函数类，封装了单次试验的训练-验证循环。"""

    def __init__(
        self,
        config: ObmiqTrainingConfig,
        train_loader: torch.utils.data.DataLoader,
        val_loader: torch.utils.data.DataLoader,
        data_shapes: Dict[str, int],
        device: str,
    ):
        self.config = config
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.data_shapes = data_shapes
        self.device = device

    def __call__(self, trial: optuna.Trial) -> float:
        """执行一次完整的训练-验证试验。"""
        # 1. 从配置中读取超参数搜索空间并进行建议
        # 离散类别参数
        hp = {
            "cnn_filters": trial.suggest_categorical(
                "cnn_filters",
                self.config.hp_cnn_filters,
            ),
            "cnn_kernel_size": trial.suggest_categorical(
                "cnn_kernel_size",
                self.config.hp_cnn_kernel_size,
            ),
            # 2024-07-01: 调整为从配置中为两个MLP层分别建议超参数
            "mlp_units_1": trial.suggest_categorical(
                "mlp_units_1",
                self.config.hp_mlp_units_1,
            ),
            "mlp_units_2": trial.suggest_categorical(
                "mlp_units_2",
                self.config.hp_mlp_units_2,
            ),
        }
        # 连续浮点数参数
        dropout_min, dropout_max = self.config.hp_dropout_rate
        lr_min, lr_max = self.config.hp_learning_rate
        wd_min, wd_max = self.config.hp_weight_decay

        hp.update(
            {
                "dropout_rate": trial.suggest_float("dropout_rate", dropout_min, dropout_max),
                "learning_rate": trial.suggest_float(
                    "learning_rate", lr_min, lr_max, log=True
                ),
                "weight_decay": trial.suggest_float(
                    "weight_decay", wd_min, wd_max, log=True
                ),
            }
        )

        # 2. 构建模型、损失函数和优化器
        model = OBMIQPyTorchModel(hp, self.data_shapes).to(self.device)
        criterion = AdaptiveLossModule().to(self.device)
        optimizer = torch.optim.AdamW(
            list(model.parameters()) + list(criterion.parameters()),
            lr=hp["learning_rate"],
            weight_decay=hp["weight_decay"],
        )

        # 3. 调用共享的训练函数，并将trial对象传递进去以支持剪枝
        best_val_loss = _train_and_evaluate_single_model(
            model=model,
            criterion=criterion,
            optimizer=optimizer,
            train_loader=self.train_loader,
            val_loader=self.val_loader,
            config=self.config,
            device=self.device,
            trial=trial,
        )

        return best_val_loss


def _aggregate_cv_results(
    fold_results: List[Dict[str, Any]]
) -> Tuple[Dict[str, Any], pd.DataFrame]:
    """聚合交叉验证结果，确定最佳超参数，并生成报告。"""
    if not fold_results:
        raise ValueError("Cannot aggregate empty fold results.")

    report_df = pd.DataFrame(fold_results)

    # 将字典形式的超参数展开为列
    params_df = report_df["best_params"].apply(pd.Series)
    report_df = pd.concat([report_df.drop("best_params", axis=1), params_df], axis=1)

    # 找到在所有折中平均验证损失最低的超参数组合
    # 首先，将超参数列转换为一个元组，使其可哈希，以便分组
    # 通过排序确保列的顺序是确定的，使逻辑更健壮
    param_cols = sorted(list(params_df.columns))
    report_df["params_tuple"] = report_df[param_cols].apply(tuple, axis=1)

    # 按超参数组合分组，计算平均验证损失
    avg_loss_by_params = report_df.groupby("params_tuple")["best_val_loss"].mean()
    best_params_tuple = avg_loss_by_params.idxmin()

    # 将元组转换回字典
    best_overall_params = dict(zip(param_cols, best_params_tuple))

    report_df = report_df.drop("params_tuple", axis=1)
    return best_overall_params, report_df


def _perform_blind_test_for_fold(
    fold_train_df: pd.DataFrame,
    held_out_df: pd.DataFrame,
    best_params: Dict[str, Any],
    data_shapes: Dict[str, int],
    config: ObmiqTrainingConfig,
    feature_selectors: Dict[str, Any],
    device: str,
    fold_idx: int,
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    在单次CV折中，为泛化能力评估执行盲测。

    该函数使用为当前折找到的最佳超参数，在完整的折训练数据上训练一个
    临时模型，然后用它对从未见过的留出井进行预测，并返回预测结果和性能指标。

    Args:
        fold_train_df: 当前折的训练数据 (除留出井外的所有井)。
        held_out_df: 当前折的留出井数据。
        best_params: Optuna为当前折找到的最佳超参数。
        data_shapes: 数据维度信息。
        config: 训练配置。
        feature_selectors: 特征列名。
        device: 计算设备。
        fold_idx: 当前折的索引。

    Returns:
        一个元组 (result_df, fold_metric)，其中 result_df 包含预测结果，
        fold_metric 包含性能指标。如果无法生成预测，则返回空的DataFrame和字典。
    """
    # 1. 在当前折的全部训练数据上，使用找到的最佳超参数重新训练一个模型
    # 1.1 再次划分临时的训练/验证集用于早停
    temp_train_df, temp_val_df = train_test_split(
        fold_train_df, test_size=config.val_split_ratio, random_state=config.random_seed
    )
    # 1.2 创建DataLoader并拟合预处理器
    temp_train_loader, temp_val_loader, fitted_preprocessors = create_dataloaders_for_fold(
        temp_train_df, temp_val_df, config, feature_selectors
    )
    # 1.3 构建模型并训练
    fold_model = OBMIQPyTorchModel(best_params, data_shapes).to(device)
    fold_criterion = AdaptiveLossModule().to(device)
    fold_optimizer = torch.optim.AdamW(
        list(fold_model.parameters()) + list(fold_criterion.parameters()),
        lr=best_params["learning_rate"],
        weight_decay=best_params["weight_decay"],
    )

    # 调用共享的训练函数。
    _train_and_evaluate_single_model(
        model=fold_model,
        criterion=fold_criterion,
        optimizer=fold_optimizer,
        train_loader=temp_train_loader,
        val_loader=temp_val_loader,
        config=config,
        device=device,
        trial=None,
    )

    # 2. 使用训练好的模型对留出井进行预测
    fold_model.eval()
    inference_loader = _create_inference_loader(
        held_out_df, config, feature_selectors, fitted_preprocessors
    )

    fold_predictions = []
    with torch.no_grad():
        for batch in inference_loader:
            inputs = {k: v.to(device) for k, v in batch.items() if k != "target"}
            predictions = fold_model(inputs)
            fold_predictions.append(predictions.cpu().numpy())

    if not fold_predictions:
        return pd.DataFrame(), {}

    predictions_np = np.concatenate(fold_predictions, axis=0)

    # 3. 收集盲测结果并计算指标
    target_cols = feature_selectors["target_cols"]
    pred_cols = [f"{col}_pred" for col in target_cols]
    result_df = held_out_df.copy()
    result_df[pred_cols] = predictions_np

    y_true = result_df[target_cols].values
    y_pred = result_df[pred_cols].values
    grouping_col = feature_selectors["grouping_col"]
    held_out_well_name = held_out_df[grouping_col].iloc[0]
    fold_metric = {"fold": fold_idx + 1, "well_name": held_out_well_name}
    for i, col_name in enumerate(target_cols):
        r2 = r2_score(y_true[:, i], y_pred[:, i])
        rmse = np.sqrt(mean_squared_error(y_true[:, i], y_pred[:, i]))
        fold_metric[f"r2_{col_name}"] = r2
        fold_metric[f"rmse_{col_name}"] = rmse

    return result_df, fold_metric


def run_hyperparameter_tuning_cv(
    train_df: pd.DataFrame,
    config: ObmiqTrainingConfig,
    feature_selectors: Dict[str, Any],
) -> Tuple[Dict[str, Any], pd.DataFrame, Dict[str, pd.DataFrame]]:
    """
    执行完整的LOWO-CV + Optuna嵌套超参数寻优。

    Args:
        train_df: 包含所有训练井的完整DataFrame。
        config: 训练配置。
        feature_selectors: 包含特征列名的字典。

    Returns:
        一个元组 (best_overall_params, cv_report_df, cv_evaluation_results)，其中
        best_overall_params 是在所有折中表现最佳的超参数字典，
        cv_report_df 是包含每一折详细结果的DataFrame,
        cv_evaluation_results 包含所有井的盲测结果和性能指标。
    """
    _set_seed(config.random_seed)
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Tuning procedure started on device: {device}")

    grouping_col = feature_selectors["grouping_col"]
    groups = train_df[grouping_col]
    logo = LeaveOneGroupOut()
    fold_results = []
    all_blind_test_results = []
    per_fold_metrics = []

    for fold_idx, (train_idx, test_idx) in enumerate(logo.split(train_df, groups=groups)):
        fold_train_df = train_df.iloc[train_idx].copy()
        # held_out_df 是当前折从未见过的数据，用于最终的盲测
        held_out_df = train_df.iloc[test_idx].copy()
        held_out_well_name = held_out_df[grouping_col].iloc[0]
        logger.info(
            f"--- Starting CV Fold {fold_idx + 1}/{logo.get_n_splits(groups=groups)} ---"
        )

        # 1. 在当前折的训练数据内部，再次划分为子训练集和验证集
        sub_train_df, sub_val_df = train_test_split(
            fold_train_df,
            test_size=config.val_split_ratio,
            random_state=config.random_seed,
        )

        # 2. 为当前折创建DataLoader，这是防止数据泄露的关键步骤
        train_loader, val_loader, _ = create_dataloaders_for_fold(
            sub_train_df, sub_val_df, config, feature_selectors
        )

        # 3. 运行Optuna研究
        # 获取数据维度信息
        sample_batch = next(iter(train_loader))
        data_shapes = {
            "num_tabular_features": sample_batch["tabular_input"].shape[1],
            "num_targets": sample_batch["target"].shape[1],
        }

        study = optuna.create_study(
            direction="minimize", pruner=optuna.pruners.MedianPruner()
        )
        objective = _Objective(config, train_loader, val_loader, data_shapes, device)
        study.optimize(objective, n_trials=config.n_trials, n_jobs=1)

        # 4. 记录该折的最佳结果
        logger.info(
            f"Fold {fold_idx + 1} best trial: value={study.best_value:.4f}, "
            f"params={study.best_params}"
        )
        fold_results.append(
            {
                "fold": fold_idx + 1,
                "best_val_loss": study.best_value,
                "best_params": study.best_params,
            }
        )

        # --- 新增：为泛化能力评估执行盲测 ---
        logger.info(
            f"--- Performing blind test for Fold {fold_idx + 1} on well '{held_out_well_name}' ---"
        )
        result_df, fold_metric = _perform_blind_test_for_fold(
            fold_train_df=fold_train_df,
            held_out_df=held_out_df,
            best_params=study.best_params,
            data_shapes=data_shapes,
            config=config,
            feature_selectors=feature_selectors,
            device=device,
            fold_idx=fold_idx,
        )
        if not result_df.empty:
            all_blind_test_results.append(result_df)
        if fold_metric:
            per_fold_metrics.append(fold_metric)

    # 5. 聚合所有折的结果
    logger.info("Aggregating results from all CV folds...")
    best_overall_params, cv_report_df = _aggregate_cv_results(fold_results)

    # 6. 准备最终的泛化能力评估产物
    cv_evaluation_results = {}
    if all_blind_test_results:
        # 合并所有盲测结果为一个DataFrame
        all_predictions_df = pd.concat(all_blind_test_results, ignore_index=True)
        cv_evaluation_results["all_predictions_df"] = all_predictions_df

    if per_fold_metrics:
        # 合并所有折的性能指标为一个DataFrame
        per_fold_metrics_df = pd.DataFrame(per_fold_metrics)
        cv_evaluation_results["per_fold_metrics_df"] = per_fold_metrics_df

    return best_overall_params, cv_report_df, cv_evaluation_results
