"""Integration tests for the SWIFT-PSO visualization facade.

Verifies that the t-SNE visualization step correctly integrates with the
RunContext, artifact handling, and the PlotProfile system.
"""

import pandas as pd
import pytest

from logwp.extras.plotting import registry as plot_registry
from logwp.extras.tracking import RunContext
from scape.core.swift_pso import (
    run_tsne_visualization_step,
    TsneVisualConfig,
    TsnePlotProfiles,
    TsneVisualArtifacts,
    SwiftPsoArtifactHandler,
)


@pytest.fixture
def tsne_source_data() -> pd.DataFrame:
    """Provides a sample DataFrame for t-SNE source data."""
    return pd.DataFrame({
        "bootstrap_run": [1, 1, 1, 2, 2, 2],
        "iteration": [0, 1, 2, 0, 1, 2],
        "param1": [0.1, 0.2, 0.3, 0.7, 0.8, 0.9],
        "param2": [1.1, 1.2, 1.3, 1.7, 1.8, 1.9],
    })


class TestVisualizationFacade:
    """Tests for the run_tsne_visualization_step function."""

    def test_run_tsne_step_with_default_profile(self, tmp_path, tsne_source_data):
        """
        Tests the t-SNE step using a default PlotProfile object from the registry.
        This is the primary test case for the new API contract.
        """
        config = TsneVisualConfig(perplexity=2)  # Low perplexity for small dataset

        # Key Change: Get the PlotProfile object from the registry first.
        profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY)

        with RunContext(tmp_path) as ctx:
            result = run_tsne_visualization_step(
                config=config,
                ctx=ctx,
                tsne_source_data=tsne_source_data,
                plot_profile=profile,  # Pass the object, not its name.
            )

            # Assertions
            assert result["status"] == "completed"
            assert result["tsne_points"] > 0

            # Verify that all artifacts were registered
            manifest = ctx.get_manifest()
            assert TsneVisualArtifacts.TSNE_PLOT_DATA in manifest.artifacts
            assert TsneVisualArtifacts.TSNE_PLOT_PROFILE in manifest.artifacts
            assert TsneVisualArtifacts.TSNE_PLOT in manifest.artifacts

            # Verify the plot profile config was saved correctly
            saved_profile_path = ctx.get_artifact_path(TsneVisualArtifacts.TSNE_PLOT_PROFILE)
            handler = SwiftPsoArtifactHandler()
            saved_profile = handler.load_plot_profile(saved_profile_path)
            assert saved_profile.name == TsnePlotProfiles.CONVERGENCE_TRAJECTORY

    def test_run_tsne_step_with_modified_profile(self, tmp_path, tsne_source_data):
        """
        Tests the "Get -> Modify -> Pass" pattern, ensuring user customizations
        are correctly handled and saved.
        """
        config = TsneVisualConfig(perplexity=2)
        custom_title = "My Custom t-SNE Title for This Run"

        # 1. Get the default profile
        profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY)

        # 2. Modify it at runtime
        profile.title_props["label"] = custom_title
        profile.artist_props["end_point"]["color"] = "magenta"

        with RunContext(tmp_path) as ctx:
            run_tsne_visualization_step(
                config=config,
                ctx=ctx,
                tsne_source_data=tsne_source_data,
                plot_profile=profile,  # 3. Pass the modified object
            )

            # Verify that the MODIFIED profile was saved
            saved_profile_path = ctx.get_artifact_path(TsneVisualArtifacts.TSNE_PLOT_PROFILE)
            handler = SwiftPsoArtifactHandler()
            saved_profile = handler.load_plot_profile(saved_profile_path)

            assert saved_profile.title_props["label"] == custom_title
            assert saved_profile.artist_props["end_point"]["color"] == "magenta"
