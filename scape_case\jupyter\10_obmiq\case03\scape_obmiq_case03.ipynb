{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# OBMIQ 端到端工作流 (PyTorch版)\n", "使用更多的输入曲线"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-24T15:12:26.647015Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 126.65, 'cpu_percent': 0.0}\n", "2025-07-24T15:12:28.658123Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 712.37, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-24T15:12:28.673851Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 712.38, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-24T15:12:28.686867Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 712.38, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-24T15:12:29.338354Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.82, 'cpu_percent': 0.0} operation=register_base_profile profile_name=obmiq.base\n", "2025-07-24T15:12:29.363695Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.83, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.training_history\n", "2025-07-24T15:12:29.375654Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.85, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.crossplot\n", "2025-07-24T15:12:29.387608Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.87, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_plot\n", "2025-07-24T15:12:29.405698Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.89, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.residuals_hist\n", "2025-07-24T15:12:29.415754Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.92, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.shap_summary\n", "2025-07-24T15:12:29.438620Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.94, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.captum_ig_summary\n", "2025-07-24T15:12:29.480290Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 767.95, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=obmiq.grad_cam\n", "环境设置与导入完成。\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "from pathlib import Path\n", "\n", "# 导入 SCAPE 和 LOGWP 的核心组件\n", "from logwp.io import WpExcelReader\n", "from logwp.extras.tracking import RunContext\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "\n", "# 导入新版OBMIQ组件\n", "import scape.core.obmiq.plot_profiles # 导入以注册绘图模板\n", "from scape.core.obmiq import (\n", "    run_obmiq_training_step,\n", "    run_obmiq_prediction_step,\n", "    ObmiqTrainingConfig,\n", "    ObmiqPredictionConfig,\n", "    ObmiqTrainingArtifacts,\n", "    ObmiqArtifactHandler\n", ")\n", "from logwp.models.constants import WpDepthRole\n", "\n", "# 设置\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', 50)\n", "\n", "print(\"环境设置与导入完成。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据\n", "\n", "在此步骤中，我们从 `.wp.xlsx` 文件中加载训练和预测所需的数据集。\n", "\n", "**请注意**:\n", "- `train_bundle` 应包含所有用于训练的井（如 C-1, C-2）。\n", "- `prediction_bundle` 可以是训练井的一部分（用于验证），也可以是全新的盲井（如 T-1）。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:12:33.140866Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.09, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx\n", "2025-07-24T15:12:33.153471Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.44, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all.wp.xlsx file_size_mb=1.37 sheet_count=1\n", "2025-07-24T15:12:33.163715Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.46, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all\n", "2025-07-24T15:12:33.170881Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.48, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all\n", "2025-07-24T15:12:33.182535Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.77, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:12:33.198802Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 768.82, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=22 well_curves=1\n", "2025-07-24T15:12:34.586148Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.05, 'cpu_percent': 0.0} shape=(2651, 85) sheet_name=nmr_obmiq\n", "2025-07-24T15:12:34.606043Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.08, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq')\n", "2025-07-24T15:12:34.619061Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.08, 'cpu_percent': 0.0} curve_count=22 dataset_name=nmr_obmiq dataset_type=Continuous df_shape=(2651, 85) processing_time=1.438\n", "2025-07-24T15:12:34.636738Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.08, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-24T15:12:34.647905Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.08, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-24T15:12:34.658135Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.08, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all.wp.xlsx processing_time=1.517 project_name=WpIdentifier('santos_obmiq_cum_all')\n", "2025-07-24T15:12:34.669469Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 780.08, 'cpu_percent': 0.0} extracted_curve_count=20 operation=extract_curve_dataframe_bundle\n", "2025-07-24T15:12:34.693578Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 784.6, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=22 input_curves=['RD_LOG10', 'PHIE_NMR', 'SWB_NMR', 'CN', 'DT', 'DPHIT_NMR', 'T2LM', 'BVI_NMR', 'T2_P50', 'MD', 'DRES', 'WELL_NO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'PHIT_NMR', 'RS_LOG10', 'T2LM_LONG', 'DT2_P50', 'DEN', 'T2_P20', 'SDR_PROXY', 'BFV_NMR'] operation=extract_metadata output_curve_count=22 output_curves=['RD_LOG10', 'PHIE_NMR', 'SWB_NMR', 'CN', 'DT', 'DPHIT_NMR', 'T2LM', 'BVI_NMR', 'T2_P50', 'MD', 'DRES', 'WELL_NO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'PHIT_NMR', 'RS_LOG10', 'T2LM_LONG', 'DT2_P50', 'DEN', 'T2_P20', 'SDR_PROXY', 'BFV_NMR']\n", "2025-07-24T15:12:34.707949Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 784.61, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx\n", "2025-07-24T15:12:34.719552Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 784.62, 'cpu_percent': 0.0} file_path=santos_obmiq_cum_all_apply.wp.xlsx file_size_mb=2.27 sheet_count=1\n", "2025-07-24T15:12:34.752702Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 784.62, 'cpu_percent': 0.0} project_name=santos_obmiq_cum_all_apply\n", "2025-07-24T15:12:34.769392Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 784.62, 'cpu_percent': 0.0} default_depth_unit=m project_name=santos_obmiq_cum_all_apply\n", "2025-07-24T15:12:34.792396Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 784.64, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=nmr_obmiq_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-24T15:12:34.810178Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 784.64, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=22 well_curves=1\n", "2025-07-24T15:12:36.980199Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.52, 'cpu_percent': 0.0} shape=(4503, 85) sheet_name=nmr_obmiq_apply\n", "2025-07-24T15:12:37.000456Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.52, 'cpu_percent': 0.0} dataset_name=WpIdentifier('nmr_obmiq_apply')\n", "2025-07-24T15:12:37.010574Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.52, 'cpu_percent': 0.0} curve_count=22 dataset_name=nmr_obmiq_apply dataset_type=Continuous df_shape=(4503, 85) processing_time=2.22\n", "2025-07-24T15:12:37.064019Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.52, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-24T15:12:37.089752Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.52, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-24T15:12:37.097971Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.52, 'cpu_percent': 0.0} dataset_count=1 file_path=santos_obmiq_cum_all_apply.wp.xlsx processing_time=2.39 project_name=WpIdentifier('santos_obmiq_cum_all_apply')\n", "2025-07-24T15:12:37.109660Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 791.52, 'cpu_percent': 0.0} extracted_curve_count=20 operation=extract_curve_dataframe_bundle\n", "2025-07-24T15:12:37.128648Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.54, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=22 input_curves=['RD_LOG10', 'PHIE_NMR', 'SWB_NMR', 'CN', 'DT', 'DPHIT_NMR', 'T2LM', 'BVI_NMR', 'T2_P50', 'MD', 'DRES', 'WELL_NO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'PHIT_NMR', 'RS_LOG10', 'T2LM_LONG', 'DT2_P50', 'DEN', 'T2_P20', 'SDR_PROXY', 'BFV_NMR'] operation=extract_metadata output_curve_count=22 output_curves=['RD_LOG10', 'PHIE_NMR', 'SWB_NMR', 'CN', 'DT', 'DPHIT_NMR', 'T2LM', 'BVI_NMR', 'T2_P50', 'MD', 'DRES', 'WELL_NO', 'SWI_NMR', 'PHI_T2_DIST_CUM', 'PHIT_NMR', 'RS_LOG10', 'T2LM_LONG', 'DT2_P50', 'DEN', 'T2_P20', 'SDR_PROXY', 'BFV_NMR']\n", "数据加载成功。\n", "训练数据束: nmr_obmiq, 形状: (2651, 85), 井名: WELL_NO\n", "预测数据束: nmr_obmiq_apply, 形状: (4503, 85)\n", "T2时间轴长度: 64\n"]}], "source": ["# --- 请在此处填写您的数据路径 ---\n", "\n", "\n", "TRAIN_WP_FILE_PATH = \"santos_obmiq_cum_all.wp.xlsx\"\n", "TRAIN_DATASET_NAME = \"nmr_obmiq\"\n", "\n", "PRED_WP_FILE_PATH = \"santos_obmiq_cum_all_apply.wp.xlsx\"\n", "PREDICTION_DATASET_NAME = \"nmr_obmiq_apply\"\n", "# ---------------------------------\n", "\n", "# 加载整个工区文件\n", "try:\n", "    # 获取训练和预测数据束\n", "    reader = WpExcelReader()\n", "    project = reader.read(TRAIN_WP_FILE_PATH)\n", "    train_ds = project.get_dataset(TRAIN_DATASET_NAME)\n", "    train_bundle = train_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "    well_no_name = train_bundle.curve_metadata.get_well_identifier_curves()[0]\n", "    depth_name = train_bundle.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)[0]\n", "\n", "    pred_reader = WpExcelReader()\n", "    pred_project = pred_reader.read(PRED_WP_FILE_PATH)\n", "    prediction_ds = pred_project.get_dataset(PREDICTION_DATASET_NAME)\n", "    prediction_bundle = prediction_ds.extract_curve_dataframe_bundle(include_system_columns=True)\n", "\n", "    # 从 head_info 获取 T2 时间轴\n", "    # 假设所有井共享一个T2轴定义\n", "\n", "    t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "    t2_time_array = t2_axis_info.calculate_values()\n", "\n", "    print(\"数据加载成功。\")\n", "    print(f\"训练数据束: {train_ds.name}, 形状: {train_ds.df.shape}, 井名: {well_no_name}\")\n", "    print(f\"预测数据束: {prediction_ds.name}, 形状: {prediction_ds.df.shape}\")\n", "    print(f\"T2时间轴长度: {len(t2_time_array)}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 数据加载失败: {e}\")\n", "    project = None\n", "    pred_project = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 运行 OBMIQ 工作流\n", "\n", "我们使用 `RunContext` 来包裹整个实验流程，以确保所有参数、指标和产物都被追踪。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:12:40.877309Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.62, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\obmiq_run_pytorch_20250724_231240 run_id=20250724-151240-68730aa4\n", "--- 开始 OBMIQ 训练步骤 (PyTorch) ---\n", "2025-07-24T15:12:40.889813Z [info     ] ===== OBMIQ Training Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.62, 'cpu_percent': 0.0}\n", "2025-07-24T15:12:40.896272Z [info     ] 曲线名已成功解析为DataFrame列名。          [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.62, 'cpu_percent': 0.0}\n", "2025-07-24T15:12:40.901588Z [info     ] --- Stage 0: Saving Configuration Snapshot --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.62, 'cpu_percent': 0.0}\n", "2025-07-24T15:12:40.908305Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.configs.training_config_snapshot artifact_path=obmiq_training_pytorch\\training_config.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.63, 'cpu_percent': 0.0} description=Snapshot of the training configuration used for this run. operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:12:40.919598Z [info     ] --- Stage 2: Hyperparameter Tuning using LOWO-CV --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.63, 'cpu_percent': 0.0}\n", "2025-07-24T15:12:40.931748Z [info     ] Tuning procedure started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 798.84, 'cpu_percent': 0.0}\n", "2025-07-24T15:12:40.941174Z [info     ] --- Starting CV Fold 1/2 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 799.9, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-24 23:12:40,955] A new study created in memory with name: no-name-3abd3251-6fcb-43f3-8583-3225abcae198\n", "[I 2025-07-24 23:12:52,023] Trial 0 finished with value: -8.627813148498536 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.3724428432835518, 'learning_rate': 0.002606321399190842, 'weight_decay': 0.0004993043899697651}. Best is trial 0 with value: -8.627813148498536.\n", "[I 2025-07-24 23:13:02,560] Trial 1 finished with value: -2.140821123123169 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.3260652268594895, 'learning_rate': 0.0004016789081992797, 'weight_decay': 0.00017631841687404593}. Best is trial 0 with value: -8.627813148498536.\n", "[I 2025-07-24 23:13:12,630] Trial 2 finished with value: -3.2238239288330077 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.4434968973413469, 'learning_rate': 0.000646418856862345, 'weight_decay': 1.9579183371157624e-05}. Best is trial 0 with value: -8.627813148498536.\n", "[I 2025-07-24 23:13:22,270] Trial 3 finished with value: -4.392805290222168 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 32, 'dropout_rate': 0.4534470504156912, 'learning_rate': 0.0009231911735834407, 'weight_decay': 0.0001481517607487696}. Best is trial 0 with value: -8.627813148498536.\n", "[I 2025-07-24 23:13:32,118] Trial 4 finished with value: -0.5426127076148987 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.351838283461938, 'learning_rate': 0.00011048583054619533, 'weight_decay': 1.579064615707535e-05}. Best is trial 0 with value: -8.627813148498536.\n", "[I 2025-07-24 23:13:42,171] Trial 5 finished with value: -9.115671157836914 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3635969196537959, 'learning_rate': 0.004203484907155095, 'weight_decay': 2.77363259473653e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:13:54,818] Trial 6 finished with value: -8.877821922302246 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.4117723544003471, 'learning_rate': 0.005700207618744259, 'weight_decay': 1.463210090750952e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:14:06,290] Trial 7 finished with value: -8.76617202758789 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.3912696183071479, 'learning_rate': 0.0058621810741763675, 'weight_decay': 1.2346711135304858e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:14:06,398] Trial 8 pruned. \n", "[I 2025-07-24 23:14:06,507] Trial 9 pruned. \n", "[I 2025-07-24 23:14:16,974] Trial 10 finished with value: -8.998118209838868 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.22494117302420097, 'learning_rate': 0.008918052603543813, 'weight_decay': 3.903424134252395e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:14:25,546] Trial 11 finished with value: -8.841674423217773 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.21753062251493374, 'learning_rate': 0.00990827649513791, 'weight_decay': 4.285911610511913e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:14:25,654] Trial 12 pruned. \n", "[I 2025-07-24 23:14:35,317] Trial 13 finished with value: -8.960812759399413 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.28639145426794116, 'learning_rate': 0.00993555101486472, 'weight_decay': 3.743718431291581e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:14:35,389] Trial 14 pruned. \n", "[I 2025-07-24 23:14:46,117] Trial 15 finished with value: -8.963406181335449 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.5295670730108923, 'learning_rate': 0.004831385273436633, 'weight_decay': 2.983375105756906e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:14:46,184] Trial 16 pruned. \n", "[I 2025-07-24 23:14:46,254] Trial 17 pruned. \n", "[I 2025-07-24 23:14:46,330] Trial 18 pruned. \n", "[I 2025-07-24 23:14:56,837] Trial 19 finished with value: -9.066265678405761 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3156221193698335, 'learning_rate': 0.007604068221437092, 'weight_decay': 1.0241549108892988e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:14:56,952] Trial 20 pruned. \n", "[I 2025-07-24 23:15:05,028] Trial 21 finished with value: -8.976982116699219 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2454770466953407, 'learning_rate': 0.007178350552857432, 'weight_decay': 2.2479098892988284e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:15:05,101] Trial 22 pruned. \n", "[I 2025-07-24 23:15:15,772] Trial 23 finished with value: -8.949156188964844 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3554339524757823, 'learning_rate': 0.007443851633135373, 'weight_decay': 1.9239237801562313e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:15:24,108] Trial 24 finished with value: -8.7528564453125 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.4056203500679681, 'learning_rate': 0.009485533877150315, 'weight_decay': 3.362810459851885e-05}. Best is trial 5 with value: -9.115671157836914.\n", "[I 2025-07-24 23:15:36,892] Trial 25 finished with value: -9.160315895080567 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.22932732437631326, 'learning_rate': 0.00610763243734262, 'weight_decay': 1.0060886495594988e-05}. Best is trial 25 with value: -9.160315895080567.\n", "[I 2025-07-24 23:15:37,019] Trial 26 pruned. \n", "[I 2025-07-24 23:15:37,134] Trial 27 pruned. \n", "[I 2025-07-24 23:15:37,255] Trial 28 pruned. \n", "[I 2025-07-24 23:15:37,389] Trial 29 pruned. \n", "[I 2025-07-24 23:15:37,528] Trial 30 pruned. \n", "[I 2025-07-24 23:15:51,589] Trial 31 finished with value: -9.237900543212891 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.21878301642868134, 'learning_rate': 0.007203576866402574, 'weight_decay': 2.3542904101386786e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:15:51,709] Trial 32 pruned. \n", "[I 2025-07-24 23:15:51,834] Trial 33 pruned. \n", "[I 2025-07-24 23:15:51,967] Trial 34 pruned. \n", "[I 2025-07-24 23:15:52,073] Trial 35 pruned. \n", "[I 2025-07-24 23:16:01,844] Trial 36 finished with value: -8.965690422058106 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.20751740314642006, 'learning_rate': 0.0070708834708985285, 'weight_decay': 5.43065459122258e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:01,939] Trial 37 pruned. \n", "[I 2025-07-24 23:16:02,024] Trial 38 pruned. \n", "[I 2025-07-24 23:16:02,106] Trial 39 pruned. \n", "[I 2025-07-24 23:16:02,183] Trial 40 pruned. \n", "[I 2025-07-24 23:16:08,687] Trial 41 finished with value: -8.883511161804199 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.22917812028802478, 'learning_rate': 0.008359414723491424, 'weight_decay': 4.918891895165036e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:16,441] Trial 42 finished with value: -9.036776924133301 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.20224410053256883, 'learning_rate': 0.007958204023329078, 'weight_decay': 1.7925291752173526e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:16,542] Trial 43 pruned. \n", "[I 2025-07-24 23:16:26,460] Trial 44 finished with value: -8.924024963378907 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2009438278161747, 'learning_rate': 0.007979988727076446, 'weight_decay': 1.9301418654437188e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:26,561] Trial 45 pruned. \n", "[I 2025-07-24 23:16:26,661] Trial 46 pruned. \n", "[I 2025-07-24 23:16:26,772] Trial 47 pruned. \n", "[I 2025-07-24 23:16:26,878] Trial 48 pruned. \n", "[I 2025-07-24 23:16:36,060] Trial 49 finished with value: -9.03191261291504 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.26305466900119684, 'learning_rate': 0.008536278899780217, 'weight_decay': 0.0001477514505675508}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:36,127] Trial 50 pruned. \n", "[I 2025-07-24 23:16:44,219] Trial 51 finished with value: -8.99909324645996 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.26420549312767794, 'learning_rate': 0.008293927967057625, 'weight_decay': 0.00019643944690723818}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:50,802] Trial 52 finished with value: -8.921178245544434 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.22103023848630537, 'learning_rate': 0.009904186298612599, 'weight_decay': 0.00014397001367608096}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:51,728] Trial 53 pruned. \n", "[I 2025-07-24 23:16:51,838] Trial 54 pruned. \n", "[I 2025-07-24 23:16:59,489] Trial 55 finished with value: -8.8752046585083 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.2527513246919177, 'learning_rate': 0.008563633594806244, 'weight_decay': 1.7298477868120237e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:16:59,617] Trial 56 pruned. \n", "[I 2025-07-24 23:16:59,764] Trial 57 pruned. \n", "[I 2025-07-24 23:16:59,888] Trial 58 pruned. \n", "[I 2025-07-24 23:17:00,017] Trial 59 pruned. \n", "[I 2025-07-24 23:17:07,833] Trial 60 finished with value: -8.980345344543457 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.24113118950744522, 'learning_rate': 0.009044762429128767, 'weight_decay': 1.1388594810101272e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:17:18,352] Trial 61 finished with value: -9.01656608581543 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.26675113380911397, 'learning_rate': 0.008211812537204799, 'weight_decay': 0.0002109265107675694}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:17:18,457] Trial 62 pruned. \n", "[I 2025-07-24 23:17:18,561] Trial 63 pruned. \n", "[I 2025-07-24 23:17:18,667] Trial 64 pruned. \n", "[I 2025-07-24 23:17:30,133] Trial 65 finished with value: -9.00900058746338 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23752541051322593, 'learning_rate': 0.009603289837112542, 'weight_decay': 0.0004113975253163296}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:17:30,244] Trial 66 pruned. \n", "[I 2025-07-24 23:17:30,356] Trial 67 pruned. \n", "[I 2025-07-24 23:17:39,293] Trial 68 finished with value: -8.858785820007324 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.38107330169025233, 'learning_rate': 0.008036006096655283, 'weight_decay': 2.2510240343826485e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:17:39,427] Trial 69 pruned. \n", "[I 2025-07-24 23:17:39,539] Trial 70 pruned. \n", "[I 2025-07-24 23:17:48,567] Trial 71 finished with value: -8.966943550109864 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23792808568919285, 'learning_rate': 0.009186444472634305, 'weight_decay': 0.0004641603394668443}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:17:48,671] Trial 72 pruned. \n", "[I 2025-07-24 23:17:48,868] Trial 73 pruned. \n", "[I 2025-07-24 23:17:58,579] Trial 74 finished with value: -8.913988494873047 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.24985094341082506, 'learning_rate': 0.009568673478618322, 'weight_decay': 0.00010354450172387517}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:17:58,686] Trial 75 pruned. \n", "[I 2025-07-24 23:18:07,997] Trial 76 finished with value: -8.849471282958984 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.423086608124095, 'learning_rate': 0.008434136909037536, 'weight_decay': 1.9548049313821167e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:18:08,095] Trial 77 pruned. \n", "[I 2025-07-24 23:18:08,202] Trial 78 pruned. \n", "[I 2025-07-24 23:18:08,306] Trial 79 pruned. \n", "[I 2025-07-24 23:18:12,241] Trial 80 pruned. \n", "[I 2025-07-24 23:18:22,925] Trial 81 finished with value: -9.046245574951172 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.25397579086758815, 'learning_rate': 0.008701413164942322, 'weight_decay': 0.00024163718428234527}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:18:30,069] Trial 82 finished with value: -8.777432441711426 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.20149955132481884, 'learning_rate': 0.008589920728024183, 'weight_decay': 0.00026044191344770466}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:18:30,177] Trial 83 pruned. \n", "[I 2025-07-24 23:18:30,287] Trial 84 pruned. \n", "[I 2025-07-24 23:18:30,422] Trial 85 pruned. \n", "[I 2025-07-24 23:18:39,535] Trial 86 finished with value: -8.865321731567382 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.341326510543006, 'learning_rate': 0.009877794576117694, 'weight_decay': 0.00031183152514847965}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:18:51,388] Trial 87 finished with value: -8.972157859802246 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.2299239980326942, 'learning_rate': 0.008617909958205508, 'weight_decay': 3.408997710996649e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:18:51,511] Trial 88 pruned. \n", "[I 2025-07-24 23:18:51,640] Trial 89 pruned. \n", "[I 2025-07-24 23:18:51,761] Trial 90 pruned. \n", "[I 2025-07-24 23:18:51,960] Trial 91 pruned. \n", "[I 2025-07-24 23:19:02,569] Trial 92 finished with value: -9.04404811859131 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.24680766856665357, 'learning_rate': 0.008909351617228884, 'weight_decay': 0.0001985975333302896}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:19:02,657] Trial 93 pruned. \n", "[I 2025-07-24 23:19:14,302] Trial 94 finished with value: -9.04198169708252 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.22124979216344293, 'learning_rate': 0.009059193089464878, 'weight_decay': 0.00016941519434117988}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:19:14,416] Trial 95 pruned. \n", "[I 2025-07-24 23:19:25,588] Trial 96 finished with value: -8.900251960754394 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.20811462880637355, 'learning_rate': 0.008890010501951694, 'weight_decay': 0.00018791688063741043}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:19:25,871] Trial 97 pruned. \n", "[I 2025-07-24 23:19:26,169] Trial 98 pruned. \n", "[I 2025-07-24 23:19:26,406] Trial 99 pruned. \n", "[I 2025-07-24 23:19:26,567] Trial 100 pruned. \n", "[I 2025-07-24 23:19:39,567] Trial 101 finished with value: -9.021342658996582 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23351344767814575, 'learning_rate': 0.00899748789903145, 'weight_decay': 0.0002861129687175478}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:19:39,663] Trial 102 pruned. \n", "[I 2025-07-24 23:19:39,769] Trial 103 pruned. \n", "[I 2025-07-24 23:19:39,863] Trial 104 pruned. \n", "[I 2025-07-24 23:19:41,370] Trial 105 pruned. \n", "[I 2025-07-24 23:19:41,476] Trial 106 pruned. \n", "[I 2025-07-24 23:19:50,839] Trial 107 finished with value: -8.871830272674561 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.33020799509629356, 'learning_rate': 0.009218699950760433, 'weight_decay': 2.109528771542186e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:19:50,984] Trial 108 pruned. \n", "[I 2025-07-24 23:19:51,053] Trial 109 pruned. \n", "[I 2025-07-24 23:19:51,143] Trial 110 pruned. \n", "[I 2025-07-24 23:19:59,076] Trial 111 finished with value: -8.955827522277833 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2714345816314479, 'learning_rate': 0.009546953485676853, 'weight_decay': 0.0004244900966280122}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:19:59,271] Trial 112 pruned. \n", "[I 2025-07-24 23:19:59,373] Trial 113 pruned. \n", "[I 2025-07-24 23:20:08,404] Trial 114 finished with value: -8.971215438842773 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.22142452479367203, 'learning_rate': 0.00989741871309502, 'weight_decay': 0.0002788671470370173}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:20:08,509] Trial 115 pruned. \n", "[I 2025-07-24 23:20:21,463] Trial 116 finished with value: -9.084485816955567 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2444701116347794, 'learning_rate': 0.008972423904659391, 'weight_decay': 1.8581119778058864e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:20:21,590] Trial 117 pruned. \n", "[I 2025-07-24 23:20:21,699] Trial 118 pruned. \n", "[I 2025-07-24 23:20:23,584] Trial 119 pruned. \n", "[I 2025-07-24 23:20:23,774] Trial 120 pruned. \n", "[I 2025-07-24 23:20:36,641] Trial 121 finished with value: -9.080545997619629 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2166537355932241, 'learning_rate': 0.009918947652436777, 'weight_decay': 1.164149370987817e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:20:36,765] Trial 122 pruned. \n", "[I 2025-07-24 23:20:36,846] Trial 123 pruned. \n", "[I 2025-07-24 23:20:45,956] Trial 124 finished with value: -9.161352157592773 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23164218062667222, 'learning_rate': 0.009965264725063183, 'weight_decay': 1.7899258959819086e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:20:46,069] Trial 125 pruned. \n", "[I 2025-07-24 23:20:58,262] Trial 126 finished with value: -9.10559959411621 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.20379455213509307, 'learning_rate': 0.009050599965663533, 'weight_decay': 1.910821115121195e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:21:05,905] Trial 127 finished with value: -9.008522033691406 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.2004207894641569, 'learning_rate': 0.009883770900109619, 'weight_decay': 1.8264144776593586e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:21:06,009] Trial 128 pruned. \n", "[I 2025-07-24 23:21:06,121] Trial 129 pruned. \n", "[I 2025-07-24 23:21:06,231] Trial 130 pruned. \n", "[I 2025-07-24 23:21:06,327] Trial 131 pruned. \n", "[I 2025-07-24 23:21:13,641] Trial 132 finished with value: -8.964723205566406 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2310148606417381, 'learning_rate': 0.009991833717259974, 'weight_decay': 2.3239857667959205e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:21:13,749] Trial 133 pruned. \n", "[I 2025-07-24 23:21:15,355] Trial 134 pruned. \n", "[I 2025-07-24 23:21:15,438] Trial 135 pruned. \n", "[I 2025-07-24 23:21:15,517] Trial 136 pruned. \n", "[I 2025-07-24 23:21:15,588] Trial 137 pruned. \n", "[I 2025-07-24 23:21:15,675] Trial 138 pruned. \n", "[I 2025-07-24 23:21:15,749] Trial 139 pruned. \n", "[I 2025-07-24 23:21:15,824] Trial 140 pruned. \n", "[I 2025-07-24 23:21:15,955] Trial 141 pruned. \n", "[I 2025-07-24 23:21:16,032] Trial 142 pruned. \n", "[I 2025-07-24 23:21:16,107] Trial 143 pruned. \n", "[I 2025-07-24 23:21:16,178] Trial 144 pruned. \n", "[I 2025-07-24 23:21:16,257] Trial 145 pruned. \n", "[I 2025-07-24 23:21:25,380] Trial 146 finished with value: -8.95830478668213 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.4132654265611099, 'learning_rate': 0.009965861514560987, 'weight_decay': 3.258556197536984e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:21:25,475] Trial 147 pruned. \n", "[I 2025-07-24 23:21:25,569] Trial 148 pruned. \n", "[I 2025-07-24 23:21:25,663] Trial 149 pruned. \n", "[I 2025-07-24 23:21:25,752] Trial 150 pruned. \n", "[I 2025-07-24 23:21:26,919] Trial 151 pruned. \n", "[I 2025-07-24 23:21:27,002] Trial 152 pruned. \n", "[I 2025-07-24 23:21:35,457] Trial 153 finished with value: -8.99471836090088 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23850322645915142, 'learning_rate': 0.00997542153034048, 'weight_decay': 0.0006583350681904897}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:21:42,890] Trial 154 finished with value: -9.003654289245606 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.22391264262452462, 'learning_rate': 0.009155477081676983, 'weight_decay': 0.00029448903498797627}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:21:42,974] Trial 155 pruned. \n", "[I 2025-07-24 23:21:43,063] Trial 156 pruned. \n", "[I 2025-07-24 23:21:50,902] Trial 157 finished with value: -9.04435977935791 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.20014804128146862, 'learning_rate': 0.009985128506395423, 'weight_decay': 1.160175827902394e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:21:51,013] Trial 158 pruned. \n", "[I 2025-07-24 23:21:54,052] Trial 159 pruned. \n", "[I 2025-07-24 23:21:54,174] Trial 160 pruned. \n", "[I 2025-07-24 23:22:01,293] Trial 161 finished with value: -8.987434768676758 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.23211251118791706, 'learning_rate': 0.009358522309728853, 'weight_decay': 0.00014984363524452856}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:22:01,513] Trial 162 pruned. \n", "[I 2025-07-24 23:22:10,396] Trial 163 finished with value: -8.988580131530762 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.24398700657565084, 'learning_rate': 0.009966346558472733, 'weight_decay': 0.000481126102666371}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:22:10,475] Trial 164 pruned. \n", "[I 2025-07-24 23:22:10,547] Trial 165 pruned. \n", "[I 2025-07-24 23:22:10,612] Trial 166 pruned. \n", "[I 2025-07-24 23:22:10,870] Trial 167 pruned. \n", "[I 2025-07-24 23:22:20,197] Trial 168 finished with value: -9.071334266662598 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.22131960607712947, 'learning_rate': 0.009191441992986885, 'weight_decay': 1.857295235264824e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:22:20,272] Trial 169 pruned. \n", "[I 2025-07-24 23:22:20,348] Trial 170 pruned. \n", "[I 2025-07-24 23:22:21,466] Trial 171 pruned. \n", "[I 2025-07-24 23:22:21,574] Trial 172 pruned. \n", "[I 2025-07-24 23:22:21,689] Trial 173 pruned. \n", "[I 2025-07-24 23:22:21,796] Trial 174 pruned. \n", "[I 2025-07-24 23:22:21,911] Trial 175 pruned. \n", "[I 2025-07-24 23:22:22,026] Trial 176 pruned. \n", "[I 2025-07-24 23:22:22,190] Trial 177 pruned. \n", "[I 2025-07-24 23:22:30,596] Trial 178 finished with value: -8.987861442565919 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.23148667414968105, 'learning_rate': 0.009914310991847837, 'weight_decay': 1.4766823686062467e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:22:31,616] Trial 179 pruned. \n", "[I 2025-07-24 23:22:31,690] Trial 180 pruned. \n", "[I 2025-07-24 23:22:37,440] Trial 181 finished with value: -8.941152000427246 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.20127895788361372, 'learning_rate': 0.009901596314398985, 'weight_decay': 1.91302927109622e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:22:37,550] Trial 182 pruned. \n", "[I 2025-07-24 23:22:37,672] Trial 183 pruned. \n", "[I 2025-07-24 23:22:37,789] Trial 184 pruned. \n", "[I 2025-07-24 23:22:37,907] Trial 185 pruned. \n", "[I 2025-07-24 23:22:38,030] Trial 186 pruned. \n", "[I 2025-07-24 23:22:44,273] Trial 187 finished with value: -8.852815437316895 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 5, 'mlp_units': 64, 'dropout_rate': 0.23322440182851512, 'learning_rate': 0.00939575837908336, 'weight_decay': 0.0001796400595377163}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:22:53,617] Trial 188 finished with value: -8.93793601989746 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.32529874744515574, 'learning_rate': 0.009869357833002754, 'weight_decay': 1.3420563870490138e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:22:53,699] Trial 189 pruned. \n", "[I 2025-07-24 23:22:53,773] Trial 190 pruned. \n", "[I 2025-07-24 23:22:54,037] Trial 191 pruned. \n", "[I 2025-07-24 23:23:04,384] Trial 192 finished with value: -9.037790298461914 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2275126777612708, 'learning_rate': 0.009990243607497049, 'weight_decay': 0.0003129008534865927}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:23:12,251] Trial 193 finished with value: -8.898108100891113 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2430758516044435, 'learning_rate': 0.009869976911716294, 'weight_decay': 0.0002471525132755284}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:23:18,134] Trial 194 finished with value: -8.994433784484864 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2006146566038915, 'learning_rate': 0.009981059429009453, 'weight_decay': 7.739292554585303e-05}. Best is trial 31 with value: -9.237900543212891.\n", "[I 2025-07-24 23:23:18,206] Trial 195 pruned. \n", "[I 2025-07-24 23:23:18,273] Trial 196 pruned. \n", "[I 2025-07-24 23:23:18,346] Trial 197 pruned. \n", "[I 2025-07-24 23:23:18,415] Trial 198 pruned. \n", "[I 2025-07-24 23:23:18,487] Trial 199 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:23:18.488696Z [info     ] Fold 1 best trial: value=-9.2379, params={'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.21878301642868134, 'learning_rate': 0.007203576866402574, 'weight_decay': 2.3542904101386786e-05} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1380.24, 'cpu_percent': 0.0}\n", "2025-07-24T15:23:18.498419Z [info     ] --- Performing blind test for Fold 1 on well 'C-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1380.24, 'cpu_percent': 0.0}\n", "2025-07-24T15:23:30.078582Z [info     ] --- Starting CV Fold 2/2 ---   [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1384.69, 'cpu_percent': 0.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-07-24 23:23:30,098] A new study created in memory with name: no-name-6ec81f66-b9e6-4b12-a67c-57ac6baa7958\n", "[I 2025-07-24 23:23:39,610] Trial 0 finished with value: -1.449606567621231 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 32, 'dropout_rate': 0.4170594692525506, 'learning_rate': 0.0003990849309080024, 'weight_decay': 0.00024096373538379568}. Best is trial 0 with value: -1.449606567621231.\n", "[I 2025-07-24 23:23:49,127] Trial 1 finished with value: -3.18879097700119 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.554984691516256, 'learning_rate': 0.0009380723410542089, 'weight_decay': 0.00022688073542951226}. Best is trial 1 with value: -3.18879097700119.\n", "[I 2025-07-24 23:23:57,933] Trial 2 finished with value: -1.3597222566604614 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 8, 'dropout_rate': 0.47069570166349706, 'learning_rate': 0.00038811461743620064, 'weight_decay': 0.0007892993511912245}. Best is trial 1 with value: -3.18879097700119.\n", "[I 2025-07-24 23:24:06,317] Trial 3 finished with value: -8.181946396827698 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 16, 'dropout_rate': 0.27909861113047996, 'learning_rate': 0.004418261259610735, 'weight_decay': 1.6370299751169125e-05}. Best is trial 3 with value: -8.181946396827698.\n", "[I 2025-07-24 23:24:14,315] Trial 4 finished with value: -8.129247903823853 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.41348214789551485, 'learning_rate': 0.0034974626502740163, 'weight_decay': 0.0007541747283768775}. Best is trial 3 with value: -8.181946396827698.\n", "[I 2025-07-24 23:24:21,357] Trial 5 finished with value: -8.196228981018066 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.3801829970313423, 'learning_rate': 0.007599429056231878, 'weight_decay': 0.00025832974418659675}. Best is trial 5 with value: -8.196228981018066.\n", "[I 2025-07-24 23:24:21,417] Trial 6 pruned. \n", "[I 2025-07-24 23:24:30,606] Trial 7 finished with value: -8.065766096115112 and parameters: {'cnn_filters': 16, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.4848030279384496, 'learning_rate': 0.005461429823016179, 'weight_decay': 2.8107059109650467e-05}. Best is trial 5 with value: -8.196228981018066.\n", "[I 2025-07-24 23:24:30,689] Trial 8 pruned. \n", "[I 2025-07-24 23:24:30,772] Trial 9 pruned. \n", "[I 2025-07-24 23:24:38,155] Trial 10 finished with value: -8.476002812385559 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.2441994268875319, 'learning_rate': 0.009710115176313507, 'weight_decay': 5.249212510903249e-05}. Best is trial 10 with value: -8.476002812385559.\n", "[I 2025-07-24 23:24:42,316] Trial 11 finished with value: -8.250857949256897 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.2142174645382706, 'learning_rate': 0.00966310274549173, 'weight_decay': 5.100452108407934e-05}. Best is trial 10 with value: -8.476002812385559.\n", "[I 2025-07-24 23:24:42,396] Trial 12 pruned. \n", "[I 2025-07-24 23:24:42,480] Trial 13 pruned. \n", "[I 2025-07-24 23:24:49,173] Trial 14 finished with value: -8.462141871452332 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2721421817529803, 'learning_rate': 0.009809580549609044, 'weight_decay': 5.4983711582138027e-05}. Best is trial 10 with value: -8.476002812385559.\n", "[I 2025-07-24 23:24:49,262] Trial 15 pruned. \n", "[I 2025-07-24 23:24:49,348] Trial 16 pruned. \n", "[I 2025-07-24 23:24:59,239] Trial 17 finished with value: -8.575924396514893 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.33888772232600795, 'learning_rate': 0.009267860796658174, 'weight_decay': 8.679223821566611e-05}. Best is trial 17 with value: -8.575924396514893.\n", "[I 2025-07-24 23:24:59,378] Trial 18 pruned. \n", "[I 2025-07-24 23:24:59,454] Trial 19 pruned. \n", "[I 2025-07-24 23:25:07,770] Trial 20 finished with value: -8.0780770778656 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 16, 'dropout_rate': 0.24462530877937907, 'learning_rate': 0.005912528211867331, 'weight_decay': 3.086858000240383e-05}. Best is trial 17 with value: -8.575924396514893.\n", "[I 2025-07-24 23:25:11,805] Trial 21 finished with value: -7.648088097572327 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.30674144092502964, 'learning_rate': 0.009443718301660619, 'weight_decay': 6.957342563023624e-05}. Best is trial 17 with value: -8.575924396514893.\n", "[I 2025-07-24 23:25:11,879] Trial 22 pruned. \n", "[I 2025-07-24 23:25:19,644] Trial 23 finished with value: -8.323698282241821 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.37814584921056416, 'learning_rate': 0.00999138948531343, 'weight_decay': 0.00010159959386959368}. Best is trial 17 with value: -8.575924396514893.\n", "[I 2025-07-24 23:25:19,728] Trial 24 pruned. \n", "[I 2025-07-24 23:25:19,809] Trial 25 pruned. \n", "[I 2025-07-24 23:25:19,891] Trial 26 pruned. \n", "[I 2025-07-24 23:25:19,967] Trial 27 pruned. \n", "[I 2025-07-24 23:25:30,247] Trial 28 finished with value: -8.645747661590576 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2510198913320727, 'learning_rate': 0.007455362710243651, 'weight_decay': 9.543078818704664e-05}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:25:30,330] Trial 29 pruned. \n", "[I 2025-07-24 23:25:30,422] Trial 30 pruned. \n", "[I 2025-07-24 23:25:30,570] Trial 31 pruned. \n", "[I 2025-07-24 23:25:30,652] Trial 32 pruned. \n", "[I 2025-07-24 23:25:37,311] Trial 33 finished with value: -8.179975032806396 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3237802776306002, 'learning_rate': 0.00885029869450078, 'weight_decay': 3.6929821936485255e-05}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:25:37,412] Trial 34 pruned. \n", "[I 2025-07-24 23:25:37,500] Trial 35 pruned. \n", "[I 2025-07-24 23:25:43,798] Trial 36 pruned. \n", "[I 2025-07-24 23:25:43,886] Trial 37 pruned. \n", "[I 2025-07-24 23:25:43,985] Trial 38 pruned. \n", "[I 2025-07-24 23:25:44,074] Trial 39 pruned. \n", "[I 2025-07-24 23:25:44,151] Trial 40 pruned. \n", "[I 2025-07-24 23:25:54,008] Trial 41 finished with value: -8.416398286819458 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.38141261684407324, 'learning_rate': 0.008336312092956046, 'weight_decay': 0.0001231735060125427}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:25:54,098] Trial 42 pruned. \n", "[I 2025-07-24 23:25:54,188] Trial 43 pruned. \n", "[I 2025-07-24 23:25:54,287] Trial 44 pruned. \n", "[I 2025-07-24 23:25:54,379] Trial 45 pruned. \n", "[I 2025-07-24 23:25:54,469] Trial 46 pruned. \n", "[I 2025-07-24 23:25:54,565] Trial 47 pruned. \n", "[I 2025-07-24 23:25:54,659] Trial 48 pruned. \n", "[I 2025-07-24 23:25:54,756] Trial 49 pruned. \n", "[I 2025-07-24 23:25:56,108] Trial 50 pruned. \n", "[I 2025-07-24 23:25:56,188] Trial 51 pruned. \n", "[I 2025-07-24 23:26:05,025] Trial 52 finished with value: -8.27991497516632 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.42915182499104754, 'learning_rate': 0.009480955078012143, 'weight_decay': 0.0001253822898948205}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:26:14,796] Trial 53 finished with value: -8.421293258666992 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.46080264832533835, 'learning_rate': 0.009183364083485534, 'weight_decay': 6.210324179829888e-05}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:26:14,883] Trial 54 pruned. \n", "[I 2025-07-24 23:26:14,968] Trial 55 pruned. \n", "[I 2025-07-24 23:26:15,058] Trial 56 pruned. \n", "[I 2025-07-24 23:26:15,146] Trial 57 pruned. \n", "[I 2025-07-24 23:26:24,071] Trial 58 finished with value: -8.15252411365509 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 5, 'mlp_units': 8, 'dropout_rate': 0.25147313401327803, 'learning_rate': 0.009978143833602755, 'weight_decay': 7.898687833461964e-05}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:26:24,154] Trial 59 pruned. \n", "[I 2025-07-24 23:26:24,253] Trial 60 pruned. \n", "[I 2025-07-24 23:26:30,373] Trial 61 finished with value: -8.069968461990356 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.3621446923685324, 'learning_rate': 0.00990693723726551, 'weight_decay': 8.755556201395669e-05}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:26:30,451] Trial 62 pruned. \n", "[I 2025-07-24 23:26:30,530] Trial 63 pruned. \n", "[I 2025-07-24 23:26:30,611] Trial 64 pruned. \n", "[I 2025-07-24 23:26:30,688] Trial 65 pruned. \n", "[I 2025-07-24 23:26:30,778] Trial 66 pruned. \n", "[I 2025-07-24 23:26:30,914] Trial 67 pruned. \n", "[I 2025-07-24 23:26:30,995] Trial 68 pruned. \n", "[I 2025-07-24 23:26:31,070] Trial 69 pruned. \n", "[I 2025-07-24 23:26:31,147] Trial 70 pruned. \n", "[I 2025-07-24 23:26:36,072] Trial 71 finished with value: -7.690303087234497 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.4280045212050055, 'learning_rate': 0.009917040607408245, 'weight_decay': 0.00013474321728957446}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:26:36,214] Trial 72 pruned. \n", "[I 2025-07-24 23:26:36,295] Trial 73 pruned. \n", "[I 2025-07-24 23:26:36,377] Trial 74 pruned. \n", "[I 2025-07-24 23:26:36,454] Trial 75 pruned. \n", "[I 2025-07-24 23:26:36,534] Trial 76 pruned. \n", "[I 2025-07-24 23:26:36,619] Trial 77 pruned. \n", "[I 2025-07-24 23:26:36,968] Trial 78 pruned. \n", "[I 2025-07-24 23:26:42,115] Trial 79 finished with value: -8.185406565666199 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.21380589691960422, 'learning_rate': 0.00996850136519336, 'weight_decay': 0.00021455886881917307}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:26:42,210] Trial 80 pruned. \n", "[I 2025-07-24 23:26:42,288] Trial 81 pruned. \n", "[I 2025-07-24 23:26:42,433] Trial 82 pruned. \n", "[I 2025-07-24 23:26:50,182] Trial 83 finished with value: -8.286673307418823 and parameters: {'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 16, 'dropout_rate': 0.2559099251593674, 'learning_rate': 0.009994051684588228, 'weight_decay': 0.00010747985524368294}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:26:50,254] Trial 84 pruned. \n", "[I 2025-07-24 23:26:50,334] Trial 85 pruned. \n", "[I 2025-07-24 23:26:50,415] Trial 86 pruned. \n", "[I 2025-07-24 23:26:50,494] Trial 87 pruned. \n", "[I 2025-07-24 23:26:50,616] Trial 88 pruned. \n", "[I 2025-07-24 23:26:50,696] Trial 89 pruned. \n", "[I 2025-07-24 23:26:50,777] Trial 90 pruned. \n", "[I 2025-07-24 23:26:50,906] Trial 91 pruned. \n", "[I 2025-07-24 23:26:50,986] Trial 92 pruned. \n", "[I 2025-07-24 23:26:51,060] Trial 93 pruned. \n", "[I 2025-07-24 23:26:51,146] Trial 94 pruned. \n", "[I 2025-07-24 23:26:51,215] Trial 95 pruned. \n", "[I 2025-07-24 23:26:51,305] Trial 96 pruned. \n", "[I 2025-07-24 23:26:51,390] Trial 97 pruned. \n", "[I 2025-07-24 23:26:51,469] Trial 98 pruned. \n", "[I 2025-07-24 23:26:51,543] Trial 99 pruned. \n", "[I 2025-07-24 23:26:51,624] Trial 100 pruned. \n", "[I 2025-07-24 23:26:51,710] Trial 101 pruned. \n", "[I 2025-07-24 23:26:58,235] Trial 102 pruned. \n", "[I 2025-07-24 23:26:58,326] Trial 103 pruned. \n", "[I 2025-07-24 23:26:58,415] Trial 104 pruned. \n", "[I 2025-07-24 23:26:58,512] Trial 105 pruned. \n", "[I 2025-07-24 23:26:58,657] Trial 106 pruned. \n", "[I 2025-07-24 23:26:58,737] Trial 107 pruned. \n", "[I 2025-07-24 23:26:58,835] Trial 108 pruned. \n", "[I 2025-07-24 23:26:58,994] Trial 109 pruned. \n", "[I 2025-07-24 23:26:59,099] Trial 110 pruned. \n", "[I 2025-07-24 23:26:59,453] Trial 111 pruned. \n", "[I 2025-07-24 23:26:59,538] Trial 112 pruned. \n", "[I 2025-07-24 23:27:05,945] Trial 113 finished with value: -7.9634846448898315 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.2061675314351859, 'learning_rate': 0.009902441182473997, 'weight_decay': 0.0002721116395720584}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:27:13,637] Trial 114 finished with value: -8.218267679214478 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.2664044340061301, 'learning_rate': 0.009904251116137446, 'weight_decay': 0.00010138272063283444}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:27:13,733] Trial 115 pruned. \n", "[I 2025-07-24 23:27:13,901] Trial 116 pruned. \n", "[I 2025-07-24 23:27:13,983] Trial 117 pruned. \n", "[I 2025-07-24 23:27:14,087] Trial 118 pruned. \n", "[I 2025-07-24 23:27:14,171] Trial 119 pruned. \n", "[I 2025-07-24 23:27:14,254] Trial 120 pruned. \n", "[I 2025-07-24 23:27:22,146] Trial 121 finished with value: -8.363475561141968 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.23525036949417205, 'learning_rate': 0.009731228914827146, 'weight_decay': 0.00023391734471995128}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:27:22,203] Trial 122 pruned. \n", "[I 2025-07-24 23:27:29,288] Trial 123 finished with value: -8.560272693634033 and parameters: {'cnn_filters': 8, 'cnn_kernel_size': 7, 'mlp_units': 32, 'dropout_rate': 0.23665010894095473, 'learning_rate': 0.009893006483070437, 'weight_decay': 0.0002605353434643277}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:27:30,026] Trial 124 pruned. \n", "[I 2025-07-24 23:27:30,113] Trial 125 pruned. \n", "[I 2025-07-24 23:27:30,213] Trial 126 pruned. \n", "[I 2025-07-24 23:27:30,287] Trial 127 pruned. \n", "[I 2025-07-24 23:27:30,371] Trial 128 pruned. \n", "[I 2025-07-24 23:27:30,474] Trial 129 pruned. \n", "[I 2025-07-24 23:27:30,554] Trial 130 pruned. \n", "[I 2025-07-24 23:27:30,654] Trial 131 pruned. \n", "[I 2025-07-24 23:27:30,744] Trial 132 pruned. \n", "[I 2025-07-24 23:27:30,837] Trial 133 pruned. \n", "[I 2025-07-24 23:27:30,996] Trial 134 pruned. \n", "[I 2025-07-24 23:27:31,112] Trial 135 pruned. \n", "[I 2025-07-24 23:27:31,838] Trial 136 pruned. \n", "[I 2025-07-24 23:27:31,926] Trial 137 pruned. \n", "[I 2025-07-24 23:27:32,015] Trial 138 pruned. \n", "[I 2025-07-24 23:27:38,853] Trial 139 finished with value: -8.361749649047852 and parameters: {'cnn_filters': 32, 'cnn_kernel_size': 3, 'mlp_units': 64, 'dropout_rate': 0.2598480541282847, 'learning_rate': 0.009943595779071019, 'weight_decay': 0.00011525964524427201}. Best is trial 28 with value: -8.645747661590576.\n", "[I 2025-07-24 23:27:39,773] Trial 140 pruned. \n", "[I 2025-07-24 23:27:39,854] Trial 141 pruned. \n", "[I 2025-07-24 23:27:39,938] Trial 142 pruned. \n", "[I 2025-07-24 23:27:40,015] Trial 143 pruned. \n", "[I 2025-07-24 23:27:40,096] Trial 144 pruned. \n", "[I 2025-07-24 23:27:40,175] Trial 145 pruned. \n", "[I 2025-07-24 23:27:40,313] Trial 146 pruned. \n", "[I 2025-07-24 23:27:40,387] Trial 147 pruned. \n", "[I 2025-07-24 23:27:40,472] Trial 148 pruned. \n", "[I 2025-07-24 23:27:40,546] Trial 149 pruned. \n", "[I 2025-07-24 23:27:41,446] Trial 150 pruned. \n", "[I 2025-07-24 23:27:42,103] Trial 151 pruned. \n", "[I 2025-07-24 23:27:42,187] Trial 152 pruned. \n", "[I 2025-07-24 23:27:43,155] Trial 153 pruned. \n", "[I 2025-07-24 23:27:43,249] Trial 154 pruned. \n", "[I 2025-07-24 23:27:43,354] Trial 155 pruned. \n", "[I 2025-07-24 23:27:43,437] Trial 156 pruned. \n", "[I 2025-07-24 23:27:43,533] Trial 157 pruned. \n", "[I 2025-07-24 23:27:43,626] Trial 158 pruned. \n", "[I 2025-07-24 23:27:43,722] Trial 159 pruned. \n", "[I 2025-07-24 23:27:43,818] Trial 160 pruned. \n", "[I 2025-07-24 23:27:43,906] Trial 161 pruned. \n", "[I 2025-07-24 23:27:44,496] Trial 162 pruned. \n", "[I 2025-07-24 23:27:44,588] Trial 163 pruned. \n", "[I 2025-07-24 23:27:44,676] Trial 164 pruned. \n", "[I 2025-07-24 23:27:44,784] Trial 165 pruned. \n", "[I 2025-07-24 23:27:44,876] Trial 166 pruned. \n", "[I 2025-07-24 23:27:44,973] Trial 167 pruned. \n", "[I 2025-07-24 23:27:45,075] Trial 168 pruned. \n", "[I 2025-07-24 23:27:45,181] Trial 169 pruned. \n", "[I 2025-07-24 23:27:45,266] Trial 170 pruned. \n", "[I 2025-07-24 23:27:46,037] Trial 171 pruned. \n", "[I 2025-07-24 23:27:46,121] Trial 172 pruned. \n", "[I 2025-07-24 23:27:46,209] Trial 173 pruned. \n", "[I 2025-07-24 23:27:46,307] Trial 174 pruned. \n", "[I 2025-07-24 23:27:46,387] Trial 175 pruned. \n", "[I 2025-07-24 23:27:46,467] Trial 176 pruned. \n", "[I 2025-07-24 23:27:46,545] Trial 177 pruned. \n", "[I 2025-07-24 23:27:46,626] Trial 178 pruned. \n", "[I 2025-07-24 23:27:46,707] Trial 179 pruned. \n", "[I 2025-07-24 23:27:50,181] Trial 180 pruned. \n", "[I 2025-07-24 23:27:50,265] Trial 181 pruned. \n", "[I 2025-07-24 23:27:50,346] Trial 182 pruned. \n", "[I 2025-07-24 23:27:50,990] Trial 183 pruned. \n", "[I 2025-07-24 23:27:51,099] Trial 184 pruned. \n", "[I 2025-07-24 23:27:51,204] Trial 185 pruned. \n", "[I 2025-07-24 23:27:51,282] Trial 186 pruned. \n", "[I 2025-07-24 23:27:51,383] Trial 187 pruned. \n", "[I 2025-07-24 23:27:51,473] Trial 188 pruned. \n", "[I 2025-07-24 23:27:51,579] Trial 189 pruned. \n", "[I 2025-07-24 23:27:51,667] Trial 190 pruned. \n", "[I 2025-07-24 23:27:51,758] Trial 191 pruned. \n", "[I 2025-07-24 23:27:51,854] Trial 192 pruned. \n", "[I 2025-07-24 23:27:51,946] Trial 193 pruned. \n", "[I 2025-07-24 23:27:52,045] Trial 194 pruned. \n", "[I 2025-07-24 23:27:52,135] Trial 195 pruned. \n", "[I 2025-07-24 23:27:52,249] Trial 196 pruned. \n", "[I 2025-07-24 23:27:52,352] Trial 197 pruned. \n", "[I 2025-07-24 23:27:52,449] Trial 198 pruned. \n", "[I 2025-07-24 23:27:52,538] Trial 199 pruned. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:27:52.538062Z [info     ] Fold 2 best trial: value=-8.6457, params={'cnn_filters': 64, 'cnn_kernel_size': 7, 'mlp_units': 64, 'dropout_rate': 0.2510198913320727, 'learning_rate': 0.007455362710243651, 'weight_decay': 9.543078818704664e-05} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1410.59, 'cpu_percent': 0.0}\n", "2025-07-24T15:27:52.550457Z [info     ] --- Performing blind test for Fold 2 on well 'T-1' --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1410.59, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.454370Z [info     ] Aggregating results from all CV folds... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.6, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.474957Z [info     ] 成功从交叉验证流程中收集到泛化能力评估数据。         [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.41, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.486016Z [info     ] Best hyperparameters found: {'cnn_filters': 8.0, 'cnn_kernel_size': 7.0, 'dropout_rate': 0.21878301642868134, 'learning_rate': 0.007203576866402574, 'mlp_units': 64.0, 'weight_decay': 2.3542904101386786e-05} [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.41, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.497562Z [info     ] --- Stage 2 Artifacts: Saving CV and Tuning Reports --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.41, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.578542Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.cv_performance artifact_path=obmiq_training_pytorch\\cv_performance_report.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.45, 'cpu_percent': 0.0} description=LOWO-CV中每一折的最佳验证损失和对应的超参数。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:02.595598Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.hyperparameter_tuning artifact_path=obmiq_training_pytorch\\hyperparameter_tuning_report.json context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.45, 'cpu_percent': 0.0} description=在所有CV折中聚合得到的全局最佳超参数组合。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:02.612685Z [info     ] --- Stage 2 Artifacts: Generating Generalization Evaluation Artifacts --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.45, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.629981Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.reports.lowo_cv_performance_summary artifact_path=obmiq_training_pytorch\\lowo_cv_performance_summary.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.45, 'cpu_percent': 0.0} description=LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:02.645034Z [info     ] 成功生成并保存了泛化能力性能评估表: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\lowo_cv_performance_summary.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1415.45, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.821114Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.lowo_cv_predictions artifact_path=obmiq_training_pytorch\\lowo_cv_predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1417.81, 'cpu_percent': 0.0} description=LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:02.840242Z [info     ] 成功保存了泛化能力评估的数据快照: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\lowo_cv_predictions.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1417.81, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:02.904224Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1419.99, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:28:02.918787Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1420.05, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:03.328807Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1443.64, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dt2_p50.svg']\n", "2025-07-24T15:28:03.399607Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1441.9, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dt2 P50 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:03.457071Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1443.64, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:28:03.469002Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=lowo_cv_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1443.64, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:03.876969Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1466.29, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\lowo_cv_crossplot_dphit_nmr.svg']\n", "2025-07-24T15:28:03.893634Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.lowo_cv_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\lowo_cv_crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.55, 'cpu_percent': 0.0} description=LOWO-CV Generalization Plot: Obmiq Training.Plots.Lowo Cv Crossplot Dphit Nmr operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:03.910122Z [info     ] 成功生成并保存了泛化能力交叉图。               [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.55, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:03.952022Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.55, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\tensorboard_logs' operation=register_artifact\n", "2025-07-24T15:28:03.966027Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.logs.tensorboard artifact_path=obmiq_training_pytorch\\tensorboard_logs context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.55, 'cpu_percent': 0.0} description=用于TensorBoard可视化的日志文件目录。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:03.982088Z [info     ] --- Stage 3: Final Model Training --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.55, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:03.993084Z [info     ] Final model training started on device: cuda [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1464.57, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:04.460274Z [info     ] Epoch 1/300, Train Loss: -0.0507, Val Loss: -0.3306 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1555.36, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:04.756190Z [info     ] Epoch 2/300, Train Loss: -0.5553, Val Loss: -0.7936 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.26, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.042755Z [info     ] Epoch 3/300, Train Loss: -1.0171, Val Loss: -1.2511 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.153570Z [info     ] Epoch 4/300, Train Loss: -1.4227, Val Loss: -1.6657 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.276118Z [info     ] Epoch 5/300, Train Loss: -1.8538, Val Loss: -2.0683 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.398613Z [info     ] Epoch 6/300, Train Loss: -2.2125, Val Loss: -2.3911 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.509671Z [info     ] Epoch 7/300, Train Loss: -2.5842, Val Loss: -2.7638 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.617003Z [info     ] Epoch 8/300, Train Loss: -2.9333, Val Loss: -2.9989 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.730585Z [info     ] Epoch 9/300, Train Loss: -3.2224, Val Loss: -3.4184 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:05.886896Z [info     ] Epoch 10/300, Train Loss: -3.5720, Val Loss: -3.5748 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.004226Z [info     ] Epoch 11/300, Train Loss: -3.8494, Val Loss: -3.9060 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.110590Z [info     ] Epoch 12/300, Train Loss: -4.1084, Val Loss: -4.2061 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.229723Z [info     ] Epoch 13/300, Train Loss: -4.1981, Val Loss: -4.4186 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.345742Z [info     ] Epoch 14/300, Train Loss: -4.4098, Val Loss: -4.5441 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.476118Z [info     ] Epoch 15/300, Train Loss: -4.7182, Val Loss: -4.9082 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.628812Z [info     ] Epoch 16/300, Train Loss: -5.0569, Val Loss: -5.1804 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.765045Z [info     ] Epoch 17/300, Train Loss: -5.3295, Val Loss: -5.3193 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:06.902493Z [info     ] Epoch 18/300, Train Loss: -5.5795, Val Loss: -5.6493 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:07.031355Z [info     ] Epoch 19/300, Train Loss: -5.7982, Val Loss: -5.9186 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:07.159874Z [info     ] Epoch 20/300, Train Loss: -6.0787, Val Loss: -5.9655 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:07.287343Z [info     ] Epoch 21/300, Train Loss: -6.2082, Val Loss: -6.1922 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:07.431365Z [info     ] Epoch 22/300, Train Loss: -6.4220, Val Loss: -6.5317 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:07.597994Z [info     ] Epoch 23/300, Train Loss: -6.6028, Val Loss: -6.6925 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:07.732883Z [info     ] Epoch 24/300, Train Loss: -6.8143, Val Loss: -6.9570 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:07.867314Z [info     ] Epoch 25/300, Train Loss: -7.1088, Val Loss: -7.1227 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:08.001289Z [info     ] Epoch 26/300, Train Loss: -7.2292, Val Loss: -7.3158 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:08.131315Z [info     ] Epoch 27/300, Train Loss: -7.2617, Val Loss: -7.3895 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:08.300802Z [info     ] Epoch 28/300, Train Loss: -7.5260, Val Loss: -7.4215 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:08.437237Z [info     ] Epoch 29/300, Train Loss: -7.5932, Val Loss: -7.7637 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:08.570495Z [info     ] Epoch 30/300, Train Loss: -7.7612, Val Loss: -7.8744 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:08.701109Z [info     ] Epoch 31/300, Train Loss: -7.9166, Val Loss: -7.7023 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:08.865102Z [info     ] Epoch 32/300, Train Loss: -7.6887, Val Loss: -7.8854 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:09.109794Z [info     ] Epoch 33/300, Train Loss: -7.8446, Val Loss: -7.8380 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:09.348427Z [info     ] Epoch 34/300, Train Loss: -7.9705, Val Loss: -7.3098 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:09.643327Z [info     ] Epoch 35/300, Train Loss: -7.9976, Val Loss: -8.0877 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:09.802987Z [info     ] Epoch 36/300, Train Loss: -8.1709, Val Loss: -8.0730 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:09.931337Z [info     ] Epoch 37/300, Train Loss: -8.2593, Val Loss: -8.3026 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.053542Z [info     ] Epoch 38/300, Train Loss: -8.3121, Val Loss: -8.2151 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.181304Z [info     ] Epoch 39/300, Train Loss: -8.3525, Val Loss: -8.1403 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.320639Z [info     ] Epoch 40/300, Train Loss: -8.4078, Val Loss: -7.9942 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.436876Z [info     ] Epoch 41/300, Train Loss: -8.1182, Val Loss: -8.2545 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.603517Z [info     ] Epoch 42/300, Train Loss: -8.3012, Val Loss: -8.3599 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.720215Z [info     ] Epoch 43/300, Train Loss: -8.3808, Val Loss: -8.4421 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.842764Z [info     ] Epoch 44/300, Train Loss: -8.4825, Val Loss: -8.3174 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:10.959390Z [info     ] Epoch 45/300, Train Loss: -8.5088, Val Loss: -8.3516 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:11.076235Z [info     ] Epoch 46/300, Train Loss: -8.3270, Val Loss: -7.9623 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:11.192756Z [info     ] Epoch 47/300, Train Loss: -8.2901, Val Loss: -8.4188 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:11.348273Z [info     ] Epoch 48/300, Train Loss: -8.3804, Val Loss: -8.4418 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:11.464647Z [info     ] Epoch 49/300, Train Loss: -8.5231, Val Loss: -8.4793 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:11.715438Z [info     ] Epoch 50/300, Train Loss: -8.5066, Val Loss: -8.4802 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:11.965243Z [info     ] Epoch 51/300, Train Loss: -8.5573, Val Loss: -8.5249 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:12.203746Z [info     ] Epoch 52/300, Train Loss: -8.5530, Val Loss: -8.1910 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:12.442888Z [info     ] Epoch 53/300, Train Loss: -8.3220, Val Loss: -8.5008 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:12.698824Z [info     ] Epoch 54/300, Train Loss: -8.4562, Val Loss: -8.4587 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:12.934007Z [info     ] Epoch 55/300, Train Loss: -8.6064, Val Loss: -8.4429 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:13.167010Z [info     ] Epoch 56/300, Train Loss: -8.4795, Val Loss: -8.5492 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:13.392896Z [info     ] Epoch 57/300, Train Loss: -8.4583, Val Loss: -8.5681 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:13.620752Z [info     ] Epoch 58/300, Train Loss: -8.6518, Val Loss: -8.4449 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:13.853865Z [info     ] Epoch 59/300, Train Loss: -8.5234, Val Loss: -8.3479 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:14.120653Z [info     ] Epoch 60/300, Train Loss: -8.4383, Val Loss: -8.2472 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:14.359362Z [info     ] Epoch 61/300, Train Loss: -8.3890, Val Loss: -8.3798 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.39, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:14.531244Z [info     ] Epoch 62/300, Train Loss: -8.5909, Val Loss: -8.6013 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.4, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:14.650738Z [info     ] Epoch 63/300, Train Loss: -8.6050, Val Loss: -8.5415 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:14.764593Z [info     ] Epoch 64/300, Train Loss: -8.4800, Val Loss: -8.4331 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:14.876256Z [info     ] Epoch 65/300, Train Loss: -8.6529, Val Loss: -8.6311 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.036817Z [info     ] Epoch 66/300, Train Loss: -8.7213, Val Loss: -8.4035 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.150459Z [info     ] Epoch 67/300, Train Loss: -8.5649, Val Loss: -8.5034 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.275689Z [info     ] Epoch 68/300, Train Loss: -8.6259, Val Loss: -8.6583 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.392358Z [info     ] Epoch 69/300, Train Loss: -8.6597, Val Loss: -8.4838 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.509029Z [info     ] Epoch 70/300, Train Loss: -8.5871, Val Loss: -8.6592 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.626002Z [info     ] Epoch 71/300, Train Loss: -8.6191, Val Loss: -8.6270 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.781567Z [info     ] Epoch 72/300, Train Loss: -8.4906, Val Loss: -8.4261 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:15.892664Z [info     ] Epoch 73/300, Train Loss: -8.5666, Val Loss: -8.4664 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:16.009377Z [info     ] Epoch 74/300, Train Loss: -8.5640, Val Loss: -8.6924 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:16.126022Z [info     ] Epoch 75/300, Train Loss: -8.5976, Val Loss: -8.6883 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:16.248261Z [info     ] Epoch 76/300, Train Loss: -8.6962, Val Loss: -8.5092 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:16.454056Z [info     ] Epoch 77/300, Train Loss: -8.6650, Val Loss: -8.5597 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:16.721084Z [info     ] Epoch 78/300, Train Loss: -8.5432, Val Loss: -8.5899 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:16.954080Z [info     ] Epoch 79/300, Train Loss: -8.6818, Val Loss: -8.5407 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:17.177023Z [info     ] Epoch 80/300, Train Loss: -8.6730, Val Loss: -8.6602 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:17.403952Z [info     ] Epoch 81/300, Train Loss: -8.6440, Val Loss: -8.6775 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:17.633797Z [info     ] Epoch 82/300, Train Loss: -8.5757, Val Loss: -8.2897 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:17.859552Z [info     ] Epoch 83/300, Train Loss: -8.4947, Val Loss: -8.0975 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:18.140085Z [info     ] Epoch 84/300, Train Loss: -8.5077, Val Loss: -8.5664 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:18.326979Z [info     ] Epoch 85/300, Train Loss: -8.6800, Val Loss: -8.6613 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:18.450406Z [info     ] Epoch 86/300, Train Loss: -8.7023, Val Loss: -8.7265 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:18.571125Z [info     ] Epoch 87/300, Train Loss: -8.7489, Val Loss: -8.3891 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:18.686741Z [info     ] Epoch 88/300, Train Loss: -8.7156, Val Loss: -8.6409 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:18.806158Z [info     ] Epoch 89/300, Train Loss: -8.7817, Val Loss: -8.6630 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:18.964596Z [info     ] Epoch 90/300, Train Loss: -8.6511, Val Loss: -8.6721 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.077826Z [info     ] Epoch 91/300, Train Loss: -8.6858, Val Loss: -8.7560 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.194807Z [info     ] Epoch 92/300, Train Loss: -8.7033, Val Loss: -8.5599 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.310060Z [info     ] Epoch 93/300, Train Loss: -8.6700, Val Loss: -8.6503 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.425747Z [info     ] Epoch 94/300, Train Loss: -8.6977, Val Loss: -8.5510 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.542378Z [info     ] Epoch 95/300, Train Loss: -8.7177, Val Loss: -8.2691 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.692361Z [info     ] Epoch 96/300, Train Loss: -8.6236, Val Loss: -8.5359 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.813597Z [info     ] Epoch 97/300, Train Loss: -8.6990, Val Loss: -8.4645 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:19.915676Z [info     ] Epoch 98/300, Train Loss: -8.7272, Val Loss: -8.7170 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:20.046923Z [info     ] Epoch 99/300, Train Loss: -8.8010, Val Loss: -8.6933 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:20.248286Z [info     ] Epoch 100/300, Train Loss: -8.6245, Val Loss: -8.5438 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:20.384916Z [info     ] Epoch 101/300, Train Loss: -8.7735, Val Loss: -8.6163 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:20.551769Z [info     ] Epoch 102/300, Train Loss: -8.7976, Val Loss: -8.6618 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:20.662751Z [info     ] Epoch 103/300, Train Loss: -8.7300, Val Loss: -8.7040 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:20.781325Z [info     ] Epoch 104/300, Train Loss: -8.7376, Val Loss: -8.7646 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:20.932021Z [info     ] Epoch 105/300, Train Loss: -8.8276, Val Loss: -8.6982 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:21.164066Z [info     ] Epoch 106/300, Train Loss: -8.7737, Val Loss: -8.7297 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:21.454916Z [info     ] Epoch 107/300, Train Loss: -8.6427, Val Loss: -8.4625 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:21.628383Z [info     ] Epoch 108/300, Train Loss: -8.7345, Val Loss: -8.6792 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:21.757554Z [info     ] Epoch 109/300, Train Loss: -8.6770, Val Loss: -8.6395 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:21.898297Z [info     ] Epoch 110/300, Train Loss: -8.6918, Val Loss: -8.7257 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.031711Z [info     ] Epoch 111/300, Train Loss: -8.7368, Val Loss: -8.3330 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.161428Z [info     ] Epoch 112/300, Train Loss: -8.6110, Val Loss: -8.6267 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.276025Z [info     ] Epoch 113/300, Train Loss: -8.8471, Val Loss: -8.7130 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.431574Z [info     ] Epoch 114/300, Train Loss: -8.7306, Val Loss: -8.0310 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.553403Z [info     ] Epoch 115/300, Train Loss: -8.7570, Val Loss: -8.6184 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.687215Z [info     ] Epoch 116/300, Train Loss: -8.8072, Val Loss: -8.6867 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.814844Z [info     ] Epoch 117/300, Train Loss: -8.7487, Val Loss: -8.7460 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:22.947865Z [info     ] Epoch 118/300, Train Loss: -8.7105, Val Loss: -8.5880 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.070034Z [info     ] Epoch 119/300, Train Loss: -8.8663, Val Loss: -8.5052 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.236723Z [info     ] Epoch 120/300, Train Loss: -8.7659, Val Loss: -7.8947 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.359260Z [info     ] Epoch 121/300, Train Loss: -8.8003, Val Loss: -8.4802 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.487071Z [info     ] Epoch 122/300, Train Loss: -8.6763, Val Loss: -8.5593 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.625649Z [info     ] Epoch 123/300, Train Loss: -8.9203, Val Loss: -8.5744 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.749306Z [info     ] Epoch 124/300, Train Loss: -8.8862, Val Loss: -8.4869 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.759285Z [info     ] Early stopping triggered at epoch 124. [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.42, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.826549Z [info     ] Loaded best model state with validation loss: -8.7646 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.77, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.831757Z [info     ] Performing final evaluation on the full training dataset... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1557.77, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.897818Z [info     ] Final model training completed. [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1559.74, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.904081Z [info     ] --- Stage 3 Artifacts: Saving Final Model Assets --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1559.66, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:23.919270Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.assets_pytorch artifact_path=obmiq_training_pytorch\\model_assets_pytorch.pkl context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1559.72, 'cpu_percent': 0.0} description=包含模型权重、超参数和预处理器的PyTorch模型资产包。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:23.941072Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1559.72, 'cpu_percent': 0.0} description=最终模型训练过程中的损失变化历史。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:23.983250Z [info     ] Plotting final training history... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1559.72, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:24.011195Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1559.72, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.training_history\n", "2025-07-24T15:28:24.022193Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=final_training_history base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1559.72, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:24.348415Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1584.44, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\final_training_history.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\final_training_history.svg']\n", "2025-07-24T15:28:24.359351Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.final_training_history artifact_path=obmiq_training_pytorch\\final_training_history.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1584.44, 'cpu_percent': 0.0} description=最终模型训练的损失曲线图。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:24.417942Z [info     ] Saving final model evaluation data and generating plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1584.44, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:24.570430Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.final_model_evaluation artifact_path=obmiq_training_pytorch\\final_model_evaluation.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1587.44, 'cpu_percent': 0.0} description=最终模型在全部训练数据上的预测和残差结果。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:24.584905Z [info     ] Generating evaluation plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1587.44, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:24.624168Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1589.22, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:28:24.637274Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1589.22, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:25.098917Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1611.84, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_crossplot_dt2_p50.svg']\n", "2025-07-24T15:28:25.115408Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1610.06, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dt2 P50 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:25.159237Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1611.84, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:28:25.175120Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1611.84, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:25.650316Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1634.44, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_crossplot_dphit_nmr.svg']\n", "2025-07-24T15:28:25.664982Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_crossplot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1632.66, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Crossplot Dphit Nmr operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:25.720386Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1635.31, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:28:25.758491Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1635.31, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:26.304486Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.01, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dt2_p50.svg']\n", "2025-07-24T15:28:26.316094Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1658.23, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dt2 P50 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:26.363980Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.01, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:28:26.371344Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1660.01, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:26.894424Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1684.83, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_plot_dphit_nmr.svg']\n", "2025-07-24T15:28:26.910463Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_plot_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_plot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1683.05, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Plot Dphit Nmr operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:26.961089Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1685.88, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:28:27.066190Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1686.39, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:27.443013Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1711.09, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dt2_p50.svg']\n", "2025-07-24T15:28:27.469345Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dt2_p50 artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1709.31, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dt2 P50 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:27.571758Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1711.09, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:28:27.632854Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=eval_residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1711.09, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:28.009385Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1735.92, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\eval_residuals_hist_dphit_nmr.svg']\n", "2025-07-24T15:28:28.032054Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.eval_residuals_hist_dphit_nmr artifact_path=obmiq_training_pytorch\\eval_residuals_hist_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1734.14, 'cpu_percent': 0.0} description=Final model evaluation plot: Obmiq Training.Plots.Eval Residuals Hist Dphit Nmr operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:28.081059Z [info     ] --- Stage 3: Model Interpretability Analysis (Captum) --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1734.14, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:28.143062Z [info     ] Running Captum analysis for target: DT2_P50... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1737.29, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:30.759108Z [info     ] Running Captum analysis for target: DPHIT_NMR... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1778.43, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:33.123481Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1781.53, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-24T15:28:33.138894Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1781.53, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png operation=register_artifact\n", "2025-07-24T15:28:33.193578Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1781.54, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DT2_P50 的贡献度。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:33.245859Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DT2_P50 artifact_path=obmiq_training_pytorch\\captum_ig_summary_DT2_P50_data.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1783.02, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DT2_P50) operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:33.277062Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1784.04, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-24T15:28:33.293964Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1784.04, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:33.744774Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.23, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_ig_summary_DT2_P50.svg']\n", "2025-07-24T15:28:33.762599Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.23, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-24T15:28:33.800530Z [warning  ] Failed to get file size        [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.23, 'cpu_percent': 0.0} error=File not found: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png operation=register_artifact\n", "2025-07-24T15:28:33.818278Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_ig_summary_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.23, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因分析图，展示表格特征对 DPHIT_NMR 的贡献度。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:33.926190Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_tabular_attributions_DPHIT_NMR artifact_path=obmiq_training_pytorch\\captum_ig_summary_DPHIT_NMR_data.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1809.99, 'cpu_percent': 0.0} description=Captum Integrated Gradients归因数据 (表格特征, 目标 DPHIT_NMR) operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:28:33.963556Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1810.55, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.captum_ig_summary\n", "2025-07-24T15:28:33.988709Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=captum_ig_summary_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1810.56, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:34.399367Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.27, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_ig_summary_DPHIT_NMR.svg']\n", "2025-07-24T15:28:34.429833Z [warning  ] 井名列 'WELL_NO' 不是字符串类型，将进行区分大小写的精确匹配。这可能导致因大小写或空格问题而找不到井。 [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.27, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.454818Z [info     ] 找到样本: 井='c-1', 目标深度=6311.73, 实际深度=6311.80 (索引=9) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1835.32, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.490053Z [info     ] 找到样本: 井='c-1', 目标深度=6313.38, 实际深度=6313.32 (索引=19) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.0, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.508295Z [info     ] 找到样本: 井='c-1', 目标深度=6318.8, 实际深度=6318.81 (索引=50) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.0, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.520481Z [info     ] 找到样本: 井='c-1', 目标深度=6334.55, 实际深度=6334.51 (索引=129) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.0, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.530280Z [info     ] 找到样本: 井='c-1', 目标深度=6409.94, 实际深度=6409.94 (索引=624) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.0, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.540847Z [info     ] 找到样本: 井='c-1', 目标深度=6426.71, 实际深度=6426.71 (索引=734) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.0, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.552583Z [info     ] 找到样本: 井='c-1', 目标深度=6440.34, 实际深度=6440.27 (索引=823) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.0, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.588663Z [info     ] 找到样本: 井='t-1', 目标深度=6426.51, 实际深度=6426.56 (索引=1069) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1837.02, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.599309Z [info     ] 找到样本: 井='t-1', 目标深度=6471.0, 实际深度=6471.06 (索引=1327) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1837.02, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.610791Z [info     ] 找到样本: 井='t-1', 目标深度=6552.36, 实际深度=6552.29 (索引=1835) [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1837.02, 'cpu_percent': 0.0}\n", "2025-07-24T15:28:34.634526Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.06, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:34.692863Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1836.71, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:37.942153Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1889.03, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DT2_P50.svg']\n", "2025-07-24T15:28:37.966461Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1889.07, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:38.192967Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6311.80_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1865.39, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:41.126118Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1917.1, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6311.80_target_DPHIT_NMR.svg']\n", "2025-07-24T15:28:41.150964Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1917.16, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:41.262885Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1917.98, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:44.360200Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1971.78, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DT2_P50.svg']\n", "2025-07-24T15:28:44.384973Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1972.16, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:44.453047Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6313.32_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 1973.37, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:47.620237Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2027.04, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6313.32_target_DPHIT_NMR.svg']\n", "2025-07-24T15:28:47.642221Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2027.07, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:47.686523Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2027.97, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:50.736603Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2081.73, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DT2_P50.svg']\n", "2025-07-24T15:28:50.771199Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2081.75, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:50.862981Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6318.81_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2082.48, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:54.026922Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2037.74, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6318.81_target_DPHIT_NMR.svg']\n", "2025-07-24T15:28:54.053925Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2038.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:54.137978Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2038.0, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:28:57.170332Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2088.2, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DT2_P50.svg']\n", "2025-07-24T15:28:57.199787Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2088.23, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:28:57.264470Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6334.51_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2088.72, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:00.259844Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2141.61, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6334.51_target_DPHIT_NMR.svg']\n", "2025-07-24T15:29:00.283955Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2141.7, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:00.376495Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2142.44, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:03.416166Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2196.0, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DT2_P50.svg']\n", "2025-07-24T15:29:03.445298Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2196.07, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:03.539820Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6409.94_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2196.86, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:06.581696Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2250.68, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6409.94_target_DPHIT_NMR.svg']\n", "2025-07-24T15:29:06.627570Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2250.98, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:06.701429Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2251.85, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:10.158910Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2183.0, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DT2_P50.svg']\n", "2025-07-24T15:29:10.185095Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2183.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:10.275912Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6426.71_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2183.29, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:13.502885Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2233.25, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6426.71_target_DPHIT_NMR.svg']\n", "2025-07-24T15:29:13.530666Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2233.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:13.579313Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2233.35, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:16.603938Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2286.24, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DT2_P50.svg']\n", "2025-07-24T15:29:16.632706Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2286.33, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:16.681316Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_C-1_depth_6440.27_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2287.01, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:19.569460Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2340.35, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_C-1_depth_6440.27_target_DPHIT_NMR.svg']\n", "2025-07-24T15:29:19.593728Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2340.38, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:19.714021Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2341.29, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:22.581510Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2394.95, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DT2_P50.svg']\n", "2025-07-24T15:29:22.622676Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2395.27, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:22.729428Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6426.56_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2395.97, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:25.851235Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2449.75, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6426.56_target_DPHIT_NMR.svg']\n", "2025-07-24T15:29:25.874874Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2449.81, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:26.126440Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2305.6, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:29.320792Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2355.28, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DT2_P50.svg']\n", "2025-07-24T15:29:29.349146Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2355.29, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:29.407531Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6471.06_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2355.39, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:32.571938Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2405.49, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6471.06_target_DPHIT_NMR.svg']\n", "2025-07-24T15:29:32.612063Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2405.51, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:32.660349Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DT2_P50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2405.89, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:35.590837Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2459.09, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DT2_P50.svg']\n", "2025-07-24T15:29:35.627339Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2459.16, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.grad_cam\n", "2025-07-24T15:29:35.674880Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=saliency_well_T-1_depth_6552.29_target_DPHIT_NMR base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2459.92, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:38.416153Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2513.15, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir\\\\saliency_well_T-1_depth_6552.29_target_DPHIT_NMR.svg']\n", "2025-07-24T15:29:38.445165Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2513.15, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_sequence_attributions_dir' operation=register_artifact\n", "2025-07-24T15:29:38.466065Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.data_snapshots.captum_sequence_attributions_dir artifact_path=obmiq_training_pytorch\\captum_sequence_attributions_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2513.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:38.493126Z [warning  ] Failed to calculate file hash  [logwp.extras.tracking.context] artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2513.16, 'cpu_percent': 0.0} error=Failed to calculate file hash: [Errno 13] Permission denied: 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_training_pytorch\\\\captum_saliency_examples_dir' operation=register_artifact\n", "2025-07-24T15:29:38.554262Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.plots.captum_saliency_examples_dir artifact_path=obmiq_training_pytorch\\captum_saliency_examples_dir context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2513.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:38.594323Z [info     ] --- Stage 5: Exporting Model to ONNX --- [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2513.16, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.654304Z [info     ] Model successfully exported to ONNX format at: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_training_pytorch\\obmiq_model.onnx [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.71, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.666996Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_training.models.onnx_model artifact_path=obmiq_training_pytorch\\obmiq_model.onnx context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.71, 'cpu_percent': 0.0} description=可用于跨平台部署的ONNX格式模型。 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:38.708450Z [info     ] ===== OBMIQ Training Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.72, 'cpu_percent': 0.0}\n", "训练步骤完成。最佳超参数: {'cnn_filters': 8.0, 'cnn_kernel_size': 7.0, 'dropout_rate': 0.21878301642868134, 'learning_rate': 0.007203576866402574, 'mlp_units': 64.0, 'weight_decay': 2.3542904101386786e-05}\n", "\n", "--- 开始 OBMIQ 预测步骤 (PyTorch) ---\n", "2025-07-24T15:29:38.723278Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Started ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.81, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.735398Z [info     ] Validating prediction inputs... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.81, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.741741Z [info     ] Loading model assets and reconstructing model... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.82, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.756771Z [info     ] Preprocessing prediction data... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.83, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.783871Z [info     ] 成功将模型所需的逻辑曲线名解析为预测数据的物理列名。     [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2521.84, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.808318Z [info     ] Performing inference on device: cuda... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2524.11, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:38.825175Z [info     ] Formatting predictions and saving artifacts... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2524.27, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:39.046936Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.datasets.predictions artifact_path=obmiq_prediction_pytorch\\predictions.csv context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2531.23, 'cpu_percent': 0.0} description=包含原始输入和模型预测结果的数据集 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:39.061845Z [info     ] Ground truth found in prediction data. Generating evaluation plots... [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2531.23, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:39.314713Z [info     ] Updated prediction snapshot with residual columns at: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch\\predictions.csv [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2534.0, 'cpu_percent': 0.0}\n", "2025-07-24T15:29:39.359100Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2538.93, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:29:39.387397Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2539.04, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:39.842652Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2561.68, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\crossplot_dt2_p50.svg']\n", "2025-07-24T15:29:39.857961Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\crossplot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2558.65, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dt2 P50 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:39.975460Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2562.43, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.crossplot\n", "2025-07-24T15:29:39.991098Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=crossplot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2562.52, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:40.412198Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2585.14, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\crossplot_dphit_nmr.svg']\n", "2025-07-24T15:29:40.463824Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.crossplot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\crossplot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2582.11, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Crossplot Dphit Nmr operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:40.550617Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2585.95, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:29:40.553930Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2586.04, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:41.124211Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2610.75, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_plot_dt2_p50.svg']\n", "2025-07-24T15:29:41.140349Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_plot_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2607.73, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dt2 P50 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:41.232512Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2611.36, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_plot\n", "2025-07-24T15:29:41.257587Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_plot_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2611.45, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:41.836859Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2636.15, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_plot_dphit_nmr.svg']\n", "2025-07-24T15:29:41.869402Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_plot_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_plot_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2633.13, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Plot Dphit Nmr operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:41.963776Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2636.87, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:29:42.028331Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dt2_p50 base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2636.98, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:42.407517Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2661.68, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_hist_dt2_p50.svg']\n", "2025-07-24T15:29:42.417173Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dt2_p50 artifact_path=obmiq_prediction_pytorch\\residuals_hist_dt2_p50.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2658.66, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dt2 P50 operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:42.519097Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2662.81, 'cpu_percent': 0.0} operation=apply_profile profile_name=obmiq.residuals_hist\n", "2025-07-24T15:29:42.597486Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=residuals_hist_dphit_nmr base_path=output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2662.86, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-24T15:29:42.963368Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2687.59, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.png', 'output01\\\\obmiq_run_pytorch_20250724_231240\\\\obmiq_prediction_pytorch\\\\residuals_hist_dphit_nmr.svg']\n", "2025-07-24T15:29:42.987942Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=obmiq_prediction.plots.residuals_hist_dphit_nmr artifact_path=obmiq_prediction_pytorch\\residuals_hist_dphit_nmr.png context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2684.57, 'cpu_percent': 0.0} description=Prediction evaluation plot: Obmiq Prediction.Plots.Residuals Hist Dphit Nmr operation=register_artifact run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:43.013458Z [info     ] ===== OBMIQ Prediction Step (PyTorch) Finished ===== [root] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2684.57, 'cpu_percent': 0.0}\n", "预测步骤完成。\n", "\n", "预测结果已保存至: output01\\obmiq_run_pytorch_20250724_231240\\obmiq_prediction_pytorch\\predictions.csv\n", "\n", "预测结果预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>WELL_NO</th>\n", "      <th>MD</th>\n", "      <th>DT2_P50</th>\n", "      <th>DT2_P50_PRED</th>\n", "      <th>DPHIT_NMR</th>\n", "      <th>DPHIT_NMR_PRED</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C-1</td>\n", "      <td>6310.4268</td>\n", "      <td>0.104076</td>\n", "      <td>0.016155</td>\n", "      <td>0.065477</td>\n", "      <td>0.049063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C-1</td>\n", "      <td>6310.5792</td>\n", "      <td>-0.148789</td>\n", "      <td>-0.072933</td>\n", "      <td>0.061988</td>\n", "      <td>0.040121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C-1</td>\n", "      <td>6310.7316</td>\n", "      <td>-0.211292</td>\n", "      <td>-0.144519</td>\n", "      <td>0.052548</td>\n", "      <td>0.021190</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C-1</td>\n", "      <td>6310.8840</td>\n", "      <td>-0.245636</td>\n", "      <td>-0.093340</td>\n", "      <td>0.038409</td>\n", "      <td>0.003836</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C-1</td>\n", "      <td>6311.0364</td>\n", "      <td>0.112414</td>\n", "      <td>0.086301</td>\n", "      <td>0.051449</td>\n", "      <td>0.014523</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  WELL_NO         MD   DT2_P50  DT2_P50_PRED  DPHIT_NMR  DPHIT_NMR_PRED\n", "0     C-1  6310.4268  0.104076      0.016155   0.065477        0.049063\n", "1     C-1  6310.5792 -0.148789     -0.072933   0.061988        0.040121\n", "2     C-1  6310.7316 -0.211292     -0.144519   0.052548        0.021190\n", "3     C-1  6310.8840 -0.245636     -0.093340   0.038409        0.003836\n", "4     C-1  6311.0364  0.112414      0.086301   0.051449        0.014523"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2025-07-24T15:29:43.085982Z [info     ] Run marked as successful       [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2684.32, 'cpu_percent': 0.0} operation=mark_success run_id=20250724-151240-68730aa4\n", "2025-07-24T15:29:43.109147Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'NVIDIA GeForce RTX 4060 Ti', 'memory_used_mb': 1126.5, 'memory_total_mb': 8187.5} context_performance={'memory_rss_mb': 2684.32, 'cpu_percent': 0.0} duration_seconds=1022.225 manifest_path=output01\\obmiq_run_pytorch_20250724_231240\\manifest.json operation=finalize run_id=20250724-151240-68730aa4 status=COMPLETED\n"]}], "source": ["if project:\n", "    # 定义输出目录\n", "    output_dir = Path(\"./output01\") # 使用新的输出目录\n", "    output_dir.mkdir(exist_ok=True)\n", "\n", "    run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"obmiq_run_pytorch\")\n", "    # 使用 RunContext 包裹整个工作流\n", "    with RunContext(run_dir=output_dir / run_dir_name, overwrite=True) as ctx:\n", "\n", "        # --- 1. 训练步骤 ---\n", "        print(\"--- 开始 OBMIQ 训练步骤 (PyTorch) ---\")\n", "\n", "        # a. 定义训练配置\n", "        training_config = ObmiqTrainingConfig(\n", "            n_trials=200,\n", "            max_epochs_per_trial=150,\n", "            final_train_epochs=300,\n", "            patience=20,\n", "            batch_size=64\n", "        )\n", "\n", "        # b. 定义facade函数的关键字参数\n", "        training_kwargs = {\n", "            \"sequence_feature\": \"PHI_T2_DIST_CUM\",\n", "            \"normalization_feature\": \"PHIT_NMR\",\n", "            \"tabular_features\": ['DEN', 'CN', 'DT', 'RD_LOG10', 'RS_LOG10', 'DRES',\n", "                                 'T2LM', 'T2LM_LONG', 'T2_P50', 'T2_P20', 'PHIT_NMR',\n", "                                 'PHIE_NMR', 'BFV_NMR', 'BVI_NMR','SWB_NMR', 'SWI_NMR', 'SDR_PROXY',],\n", "            \"target_features\": [\"DT2_P50\", \"DPHIT_NMR\"],\n", "            \"grouping_feature\": well_no_name, # 使用新的参数名\n", "            \"t2_time_axis\": t2_time_array,\n", "        }\n", "\n", "        saliency_samples = [(\"c-1\",6311.73),\n", "                            (\"c-1\",6313.38),\n", "                            (\"c-1\",6318.8),\n", "                            (\"c-1\",6334.55),\n", "                            (\"c-1\",6409.94),\n", "                            (\"c-1\",6426.71),\n", "                            (\"c-1\",6440.34),\n", "                            (\"t-1\",6426.51),\n", "                            (\"t-1\",6471.0),\n", "                            (\"t-1\",6552.36)]\n", "\n", "        # c. 执行训练步骤\n", "        # 新版facade直接接收WpDataFrameBundle，无需手动准备X, y\n", "        training_results = run_obmiq_training_step(\n", "            config=training_config,\n", "            ctx=ctx,\n", "            train_bundle=train_bundle,\n", "            depth_feature=depth_name,\n", "            saliency_samples=saliency_samples,\n", "            **training_kwargs\n", "        )\n", "\n", "        print(f\"训练步骤完成。最佳超参数: {training_results.get('best_hyperparameters')}\")\n", "\n", "        # --- 2. 预测步骤 ---\n", "        print(\"\\n--- 开始 OBMIQ 预测步骤 (PyTorch) ---\")\n", "\n", "        # a. 从上下文中获取模型资产的路径\n", "        model_assets_path = ctx.get_artifact_path(ObmiqTrainingArtifacts.MODEL_ASSETS.value)\n", "\n", "        # b. 使用产物处理器加载模型资产\n", "        handler = ObmiqArtifactHandler()\n", "        model_assets = handler.load_model_assets(model_assets_path)\n", "\n", "        # c. 定义预测配置\n", "        prediction_config = ObmiqPredictionConfig()\n", "\n", "        # d. 定义facade函数的关键字参数\n", "        prediction_kwargs = {\n", "            \"source_t2_time_axis\": t2_time_array, # 假设预测数据与训练数据T2轴相同\n", "            \"output_curve_names\": (\"DT2_P50_PRED\", \"DPHIT_NMR_PRED\")\n", "        }\n", "\n", "        # e. 执行预测步骤\n", "        prediction_results = run_obmiq_prediction_step(\n", "            config=prediction_config,\n", "            ctx=ctx,\n", "            model_assets=model_assets,\n", "            prediction_bundle=prediction_bundle,\n", "            **prediction_kwargs\n", "        )\n", "\n", "        print(\"预测步骤完成。\")\n", "\n", "        # --- 3. 结果检查 ---\n", "        # 新版facade返回的是预测结果文件的路径\n", "        prediction_path = Path(prediction_results[\"output_path\"])\n", "        if prediction_path.exists():\n", "            print(f\"\\n预测结果已保存至: {prediction_path}\")\n", "            # 从CSV加载结果进行预览\n", "            predicted_df = pd.read_csv(prediction_path)\n", "            print(\"\\n预测结果预览:\")\n", "            display(predicted_df[[\"WELL_NO\", \"MD\", \"DT2_P50\", \"DT2_P50_PRED\", \"DPHIT_NMR\", \"DPHIT_NMR_PRED\"]].head())\n", "        else:\n", "            print(f\"❌ 预测结果文件未找到: {prediction_path}\")\n", "\n", "else:\n", "    print(\"因数据加载失败，跳过工作流执行。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}