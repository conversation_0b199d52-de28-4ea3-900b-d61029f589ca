# VS Code 项目级配置推荐 (`.vscode/settings.json`)

本文档旨在提供一份推荐的 VS Code 工作区（项目级）配置清单。将这些配置保存在项目根目录的 `.vscode/settings.json` 文件中，可以确保您（以及团队成员）在开发本项目时拥有一致、高效的编码体验。

### 如何使用

1. 在您的项目根目录下，创建一个名为 `.vscode` 的文件夹（如果尚不存在）。

2. 在该文件夹内，创建一个名为 `settings.json` 的文件。

3. 将下文推荐的配置项复制到 `settings.json` 文件中。

### 推荐配置项详解

#### 1. 默认终端为 Command Prompt

**目的**：确保在 Windows 系统下，VS Code 打开的集成终端默认为 `Command Prompt` (cmd.exe)，这与 Conda 等工具的兼容性最好。

```json
"terminal.integrated.defaultProfile.windows": "Command Prompt"
```

#### 2. 文本自动换行

**目的**：避免水平滚动条，让长代码行或文本行自动换行以适应编辑器宽度，提升代码可读性。

```json
"editor.wordWrap": "on"
```

#### 3. 大纲视图 (Outline) 优化

**目的**：让大纲视图更聚焦于代码结构（如类、函数、方法），隐藏琐碎的细节（如变量），使其更加清爽。

* **隐藏变量**：

  ```json
  "outline.showVariables": false
  ```

#### 4. 保存时自动清理

**目的**：在保存文件时，自动删除行尾多余的空格，并确保文件末尾有一个空行，保持代码整洁。

```json
"files.trimTrailingWhitespace": true,
"files.insertFinalNewline": true
```

#### 5. 隐藏不必要的文件

**目的**：让左侧的文件浏览器视图更干净，隐藏那些自动生成、无需手动编辑的文件和文件夹。

```json
"files.exclude": {
    "**/__pycache__": true,
    "**/.pytest_cache": true
}
```

#### 6. 显示代码长度标尺

**目的**：在编辑器中显示一条垂直线，用来提醒代码行的长度，有助于遵循 PEP 8 等编码规范。

```json
"editor.rulers": [79, 99]
```

#### 7. 括号对着色

**目的**：让配对的括号显示不同的颜色，在代码嵌套层次很深时，可以清晰地看出括号的配对关系。

```json
"editor.bracketPairColorization.enabled": true
```

#### 8. 增强 Git 集成

**目的**：在编辑器中直接提供更丰富的 Git 信息，并保持远程分支信息最新。

```json
"git.codeLens": true,
"git.autofetch": true
```

#### 9. 优化搜索结果

**目的**：在全局搜索时，自动忽略 `.gitignore` 文件中指定的文件，让搜索结果更精准。

```json
"search.useIgnoreFiles": true
```

#### 10. 启用代码缩略图 (Minimap)

**目的**：在编辑器右侧显示代码的微型概览图，便于在长文件中快速导航。

```json
"editor.minimap.enabled": true
```

### 完整 `settings.json` 示例

您可以直接复制下面的完整配置到您的 `.vscode/settings.json` 文件中。

```json
{
    "terminal.integrated.defaultProfile.windows": "Command Prompt",
    "editor.wordWrap": "on",
    "editor.rulers": [
        79,
        99
    ],
    "editor.minimap.enabled": true,
    "editor.bracketPairColorization.enabled": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/.pytest_cache": true
    },
    "outline.showVariables": false,
    "git.codeLens": true,
    "git.autofetch": true,
    "search.useIgnoreFiles": true,
    "python.defaultInterpreterPath": "C:\\path\\to\\your\\anaconda3\\envs\\scape_env\\python.exe",
    "python.terminal.activateEnvironment": true
}
```

将这份配置与您之前的环境配置指南结合使用，就能为您的项目打造一个非常强大和一致的开发环境。
