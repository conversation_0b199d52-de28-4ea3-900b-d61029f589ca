"""Configuration models for the validation package steps.

This module defines the Pydantic models used to configure the behavior
of the different validation steps, such as PLT analysis and permeability
correlation analysis.

Following the component development framework, these models should only contain
model/algorithm parameters that directly affect the numerical results.
"""

from __future__ import annotations

from typing import List, Optional

from pydantic import BaseModel, Field


class PltAnalysisConfig(BaseModel):
    """Configuration for the PLT Blind Well Analysis step.

    Currently, this step has a fixed calculation logic and does not require
    any external configuration parameters. This model is provided as a
    placeholder for future extensions (e.g., adding calculation thresholds).
    """
    pass


class PermCorrelationConfig(BaseModel):
    """Configuration for the Permeability Correlation Analysis step."""

    relaxed_wells: Optional[List[str]] = Field(
        default=None,
        description=(
            "A list of well names for which a relaxed validation standard "
            "should be applied. If None, the standard criteria are used for all wells."
        ),
    )
