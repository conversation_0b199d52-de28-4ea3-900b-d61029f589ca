"""scape.core.obmiq - OBMIQ多步骤包

符合logwp.extras.tracking机器学习组件开发框架规范的OBMIQ建模与预测实现。

Architecture
------------
层次/依赖: scape/core层，OBMIQ算法实现
设计原则: 多步骤包架构、关注点分离、可追踪性
性能特征: 支持GPU加速、纯函数计算核心

Package Structure
-----------------
- training_facade.py: 训练步骤门面
- prediction_facade.py: 预测步骤门面
- config.py: Pydantic配置模型
- constants.py: 产物常量定义
- artifact_handler.py: 无状态产物处理器
- exceptions.py: 专属异常类
- internal/: 内部实现逻辑

Public API
----------
Step Functions (主要接口):
    - run_obmiq_training_step: 执行OBMIQ训练步骤
    - run_obmiq_prediction_step: 执行OBMIQ预测步骤

Configuration Models:
    - ObmiqTrainingConfig: 训练步骤配置模型
    - ObmiqPredictionConfig: 预测步骤配置模型

Artifact Constants:
    - ObmiqTrainingArtifacts: 训练步骤产物常量
    - ObmiqPredictionArtifacts: 预测步骤产物常量

Utility Classes:
    - ObmiqArtifactHandler: 产物处理器

References
----------
- 《SCAPE_MS_方法说明书》§5 - OBMIQ阶段定义
- 《logwp/extras/tracking/机器学习组件开发框架》- 架构规范
"""

# =============================================================================
# 导入所有公共API
# =============================================================================

# Step Functions - 主要接口
from .prediction_facade import run_obmiq_prediction_step
from .training_facade import run_obmiq_training_step

# Configuration Models
from .config import ObmiqPredictionConfig, ObmiqTrainingConfig

# Artifact Constants
from .constants import ObmiqPredictionArtifacts, ObmiqTrainingArtifacts

# Utility Classes
from .artifact_handler import ObmiqArtifactHandler

# =============================================================================
# 公共API导出列表
# =============================================================================

__all__ = [
    # Step Functions
    "run_obmiq_training_step",
    "run_obmiq_prediction_step",
    # Configuration Models
    "ObmiqTrainingConfig",
    "ObmiqPredictionConfig",
    # Artifact Constants
    "ObmiqTrainingArtifacts",
    "ObmiqPredictionArtifacts",
    # Utility Classes
    "ObmiqArtifactHandler",
]
