from __future__ import annotations

from typing import TYPE_CHECKING, cast
import re

import pandas as pd

from logwp.models.constants import (
    WpCurveCategory,
    WpDataType,
    WpStandardColumn,
)
from logwp.models.curve.metadata import CurveBasicAttributes, CurveMetadata
from logwp.infra import get_logger

if TYPE_CHECKING:
    from pandas.core.dtypes.dtypes import Dtype

logger = get_logger(__name__)

"""
从DataFrame推断曲线元数据的内部服务。

此模块包含从pandas DataFrame的结构（如列名、数据类型）
自动推断和创建CurveMetadata的核心逻辑。

主要功能：
- 识别标准列（井名、深度）
- 反向解析二维组合曲线
- 生成完整的CurveMetadata对象
"""

# 用于查找二维组合曲线模式的正则表达式，如 'NAME_1', 'NAME_2' 等。
# 它会捕获基础名称和索引。
COMPOSITE_CURVE_PATTERN = re.compile(r"^(.*?)_(\d+)$")


def _infer_data_type(dtype: "Dtype") -> WpDataType:
    """从pandas的dtype推断WpDataType。"""
    if pd.api.types.is_integer_dtype(dtype):
        return WpDataType.INT
    if pd.api.types.is_float_dtype(dtype) or pd.api.types.is_numeric_dtype(dtype):
        return WpDataType.FLOAT
    if pd.api.types.is_bool_dtype(dtype):
        return WpDataType.BOOL
    # 默认为字符串类型
    return WpDataType.STR


def _find_standard_columns(
    columns: list[str],
) -> tuple[str | None, str | None, str | None, str | None, list[str]]:
    """从列名列表中查找标准列（井名、深度），并返回它们以及剩余的列。

    此函数以不区分大小写的方式进行匹配，并优先识别双深度（顶/底深）组合。

    Args:
        columns: 原始的DataFrame列名列表。

    Returns:
        一个元组，包含:
        - (str | None): 识别出的井名列，未找到则为None。
        - (str | None): 识别出的单深度列，未找到或为双深度模式则为None。
        - (str | None): 识别出的顶深列，未找到则为None。
        - (str | None): 识别出的底深列，未找到则为None。
        - (list[str]): 移除了已识别标准列后的剩余列名列表。
    """
    remaining_columns = list(columns)
    well_name_col: str | None = None
    depth_col: str | None = None
    depth_top_col: str | None = None
    depth_bottom_col: str | None = None

    # 为不区分大小写的匹配准备小写别名集合
    well_aliases_lower = {alias.lower() for alias in WpStandardColumn.well_name_aliases()}
    depth_top_aliases_lower = {alias.lower() for alias in WpStandardColumn.depth_top_aliases()}
    depth_bottom_aliases_lower = {alias.lower() for alias in WpStandardColumn.depth_bottom_aliases()}
    depth_aliases_lower = {alias.lower() for alias in WpStandardColumn.depth_aliases()}

    # 1. 查找井名列
    for col in remaining_columns[:]:  # 遍历副本以安全地修改原列表
        if col.lower() in well_aliases_lower:
            if not well_name_col:  # 只取第一个匹配项
                well_name_col = col
                remaining_columns.remove(col)
                break

    # 2. 优先查找双深度列
    for col in remaining_columns[:]:
        if col.lower() in depth_top_aliases_lower and not depth_top_col:
            depth_top_col = col
            remaining_columns.remove(col)
            break
    for col in remaining_columns[:]:
        if col.lower() in depth_bottom_aliases_lower and not depth_bottom_col:
            depth_bottom_col = col
            remaining_columns.remove(col)
            break

    # 3. 如果未找到完整的双深度组合，则查找单深度列
    if not (depth_top_col and depth_bottom_col):
        for col in remaining_columns[:]:
            if col.lower() in depth_aliases_lower and not depth_col:
                depth_col = col
                remaining_columns.remove(col)
                break

    return well_name_col, depth_col, depth_top_col, depth_bottom_col, remaining_columns


def _group_2d_composite_curves(
    columns: list[str],
) -> tuple[dict[str, list[str]], list[str]]:
    """根据列名模式（如 'NAME_1', 'NAME_2'）对二维组合曲线进行分组。

    Args:
        columns: 待分组的列名列表。

    Returns:
        一个元组，包含:
        - (dict[str, list[str]]): 分组后的二维曲线字典。
          键是曲线基础名（如 'T2_VALUE'），
          值是按索引排序的原始列名列表（如 ['T2_VALUE_1', 'T2_VALUE_2']）。
        - (list[str]): 未被分组的剩余列名列表。
    """
    groups: dict[str, list[tuple[int, str]]] = {}
    ungrouped_columns = []

    for col in columns:
        match = COMPOSITE_CURVE_PATTERN.match(col)
        if match:
            base_name, index_str = match.groups()
            index = int(index_str)
            if base_name not in groups:
                groups[base_name] = []
            groups[base_name].append((index, col))
        else:
            ungrouped_columns.append(col)

    # 筛选出真正的组合曲线（元素数量 > 1），并排序
    final_groups: dict[str, list[str]] = {}
    for base_name, indexed_cols in groups.items():
        if len(indexed_cols) > 1:
            # 按索引排序以确保列顺序正确
            indexed_cols.sort(key=lambda x: x[0])
            final_groups[base_name] = [col_name for _, col_name in indexed_cols]
        else:
            # 只有一个元素的，不视为组合曲线，放回未分组列表
            ungrouped_columns.append(indexed_cols[0][1])

    return final_groups, ungrouped_columns


def from_dataframe_internal(df: pd.DataFrame) -> tuple[CurveMetadata, bool]:
    """
    从pandas DataFrame推断并创建曲线元数据。
    这是该功能的核心内部实现。
    """
    metadata = CurveMetadata()
    all_columns = list(df.columns)

    # 1. 识别标准列
    (
        well_col,
        single_depth_col,
        top_depth_col,
        bottom_depth_col,
        data_columns,
    ) = _find_standard_columns(all_columns)

    is_interval = bool(top_depth_col and bottom_depth_col)

    # 2. 从剩余列中对二维组合曲线进行分组
    composite_groups, remaining_1d_columns = _group_2d_composite_curves(data_columns)

    # 3. 按顺序创建并添加元数据：井名、深度、其他

    # 井名标识曲线
    if well_col:
        well_attrs = CurveBasicAttributes.create_well_identifier_curve(name=well_col)
        metadata.add_curve(well_attrs)

    # 深度曲线
    if is_interval:
        # 只有当is_interval为True时，顶深和底深才都存在，因此cast是安全的
        top_attrs, bottom_attrs = CurveBasicAttributes.create_interval_depth_reference_curves(
            top_name=cast(str, top_depth_col), bottom_name=cast(str, bottom_depth_col)
        )
        metadata.add_curve(top_attrs)
        metadata.add_curve(bottom_attrs)
    elif single_depth_col:
        depth_attrs = CurveBasicAttributes.create_depth_reference_curve(name=single_depth_col)
        metadata.add_curve(depth_attrs)

    # 其他数据曲线
    # 二维组合曲线
    for base_name, element_cols in composite_groups.items():
        # 从第一个元素列推断数据类型
        data_type = _infer_data_type(df.dtypes[element_cols[0]])

        comp_attrs = CurveBasicAttributes.create_2d_composite_curve(
            name=base_name,
            element_count=len(element_cols),
            data_type=data_type,
            category=WpCurveCategory.COMPUTED,
            description=f"从 {len(element_cols)} 个列推断出的二维组合曲线。",
        )
        metadata.add_curve(comp_attrs)

    # 一维数据曲线
    for col in remaining_1d_columns:
        data_type = _infer_data_type(df.dtypes[col])
        attrs = CurveBasicAttributes.create_1d_curve(
            name=col,
            data_type=data_type,
            category=WpCurveCategory.COMPUTED,
            description=f"从列 '{col}' 推断出的一维曲线。",
        )
        metadata.add_curve(attrs)

    return metadata, is_interval
