"""scape.core.baselines.sdr.facade - SDR基准模型门面

实现SDR基准模型训练和预测步骤的公共接口。

"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict

import numpy as np
import pandas as pd
from logwp.infra import get_logger
from logwp.models.curve import CurveExpansionMode

from . import computer
from .artifact_handler import SdrArtifactHandler
from .config import SdrModelParameters, SdrPredictionConfig, SdrTrainingConfig
from .constants import SdrArtifacts
from .exceptions import SdrDataError, SdrModelError

if TYPE_CHECKING:
    from logwp.extras.tracking import RunContext
    from logwp.models.datasets.bundle import WpDataFrameBundle

logger = get_logger(__name__)


def _resolve_sdr_selectors(
    bundle: "WpDataFrameBundle",
    k_label_curve: str | None = None,
    phit_nmr_curve: str | None = None,
    t2lm_curve: str | None = None,
) -> Dict[str, str]:
    """将SDR模型所需的逻辑曲线名解析为DataFrame的物理列名。

    Args:
        bundle: 包含数据和元数据的WpDataFrameBundle。
        **kwargs: 所有以曲线名形式传入的特征选择器。

    Returns:
        一个字典，其中包含了解析后的DataFrame列名。
    """
    resolved = {}
    if k_label_curve:
        resolved["k_label_col"] = bundle.curve_metadata.expand_curve_names([k_label_curve], mode=CurveExpansionMode.DATAFRAME)[0]
    if phit_nmr_curve:
        resolved["phit_col"] = bundle.curve_metadata.expand_curve_names([phit_nmr_curve], mode=CurveExpansionMode.DATAFRAME)[0]
    if t2lm_curve:
        resolved["t2lm_col"] = bundle.curve_metadata.expand_curve_names([t2lm_curve], mode=CurveExpansionMode.DATAFRAME)[0]
    return resolved

def run_sdr_training_step(
    config: SdrTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    *,
    k_label_curve: str,
    phit_nmr_curve: str,
    t2lm_curve: str,
) -> Dict[str, Any]:
    """执行SDR基准模型训练和参数优化。

    Args:
        config: SDR训练步骤的配置对象。
        ctx: 当前运行的上下文，用于追踪和产物管理。
        train_bundle: 用于训练的数据Bundle。
        k_label_curve: 渗透率真值标签的曲线名。
        phit_nmr_curve: 总孔隙度曲线名。
        t2lm_curve: T2对数平均值曲线名。

    Returns:
        一个包含训练状态的字典。

    Raises:
        SdrDataError: 如果输入数据缺少必需的曲线。
        SdrModelError: 如果优化过程失败。
    """
    logger.info("开始SDR基准模型训练步骤", operation="sdr_training_step")
    step_dir = ctx.get_step_dir("sdr_baseline_training")
    handler = SdrArtifactHandler()

    # 1. 解析曲线名
    resolved_cols = _resolve_sdr_selectors(
        bundle=train_bundle,
        k_label_curve=k_label_curve,
        phit_nmr_curve=phit_nmr_curve,
        t2lm_curve=t2lm_curve,
    )
    k_label_col, phit_col, t2lm_col = (
        resolved_cols["k_label_col"], resolved_cols["phit_col"], resolved_cols["t2lm_col"]
    )

    # 2. 输入验证 (使用解析后的列名)
    required_cols = [k_label_col, phit_col, t2lm_col]
    missing_cols = [c for c in required_cols if c not in train_bundle.data.columns]
    if missing_cols:
        raise SdrDataError(f"训练数据中缺少必需的列: {missing_cols}")

    # 3. 准备数据
    train_df = train_bundle.data[required_cols].dropna()

    # 增加数据有效性检查，确保用于对数计算的列为正数
    original_rows = len(train_df)
    train_df = train_df[
        (train_df[k_label_col] > 0) &
        (train_df[phit_col] > 0) &
        (train_df[t2lm_col] > 0)
    ]
    filtered_rows = len(train_df)
    if original_rows > filtered_rows:
        logger.warning(
            "移除了包含非正数值的行，这些值在对数计算中无效",
            operation="sdr_training_step",
            removed_rows=original_rows - filtered_rows,
            remaining_rows=filtered_rows
        )

    if train_df.empty:
        raise SdrDataError("在移除缺失值和非正值后，没有可用的训练数据。")

    data_for_optimizer = {"k_label": train_df[k_label_col].to_numpy(), "phit_nmr": train_df[phit_col].to_numpy(), "t2lm": train_df[t2lm_col].to_numpy()}

    # 3. 调用内部优化器
    logger.info("调用内部优化器寻找最佳SDR参数...")
    opt_results = computer.find_sdr_parameters(data_for_optimizer)

    if not opt_results["success"]:
        raise SdrModelError(f"SDR参数优化失败: {opt_results['optimizer_status']}")

    # 4. 构建模型资产包
    log10_ksdr_a = opt_results["optimized_parameters"]["log10_KSDR_A"]
    model_assets = {
        "model_type": "sdr_baseline",
        "optimized_parameters": {
            "KSDR_A": 10**log10_ksdr_a,
            "PHIT_EXP": opt_results["optimized_parameters"]["PHIT_EXP"],
            "T2LM_EXP": opt_results["optimized_parameters"]["T2LM_EXP"],
            "RHO_NMR": opt_results["optimized_parameters"]["RHO_NMR"],
        },
        "metadata": {
            "training_loss": opt_results["final_loss"],
            "optimizer_status": opt_results["optimizer_status"],
            "training_samples": len(train_df),
        },
    }

    # 5. 保存和注册产物
    # 5.1 保存模型资产
    assets_path = step_dir / "sdr_model_assets.json"
    handler.save_model_assets(model_assets, assets_path)
    ctx.register_artifact(
        assets_path.relative_to(ctx.run_dir),
        SdrArtifacts.MODEL_ASSETS.value,
    )

    # 5.2 保存配置快照
    config_path = step_dir / "training_config.json"
    handler.save_model_assets(config.model_dump(), config_path)
    ctx.register_artifact(
        config_path.relative_to(ctx.run_dir),
        SdrArtifacts.TRAINING_CONFIG_SNAPSHOT.value,
    )

    # 6. 记录指标
    ctx.log_metrics(
        {
            "final_loss": opt_results["final_loss"],
            "training_samples": len(train_df),
        },
        step_name="sdr_training"
    )

    logger.info("SDR基准模型训练步骤完成", final_loss=opt_results["final_loss"])
    return {"status": "completed", "final_loss": opt_results["final_loss"]}


def run_sdr_prediction_step(
    config: SdrPredictionConfig,
    ctx: RunContext,
    model_assets: Dict[str, Any],
    prediction_bundle: WpDataFrameBundle,
    *,
    output_curve_name: str,
    phit_nmr_curve: str,
    t2lm_curve: str,
) -> Dict[str, Any]:
    """使用优化好的SDR参数进行渗透率预测。

    Args:
        config: SDR预测步骤的配置对象。
        ctx: 当前运行的上下文。
        model_assets: 从训练步骤加载的模型资产字典。
        prediction_bundle: 包含待预测数据的Bundle。
        output_curve_name: 输出的渗透率曲线名称。
        phit_nmr_curve: 总孔隙度曲线名。
        t2lm_curve: T2对数平均值曲线名。

    Returns:
        一个包含预测状态的字典。

    Raises:
        SdrDataError: 如果输入数据或模型资产无效。
    """
    logger.info("开始SDR基准模型预测步骤", operation="sdr_prediction_step")
    step_dir = ctx.get_step_dir("sdr_baseline_prediction")
    handler = SdrArtifactHandler()

    # 1. 解析曲线名
    resolved_cols = _resolve_sdr_selectors(
        bundle=prediction_bundle,
        phit_nmr_curve=phit_nmr_curve,
        t2lm_curve=t2lm_curve,
    )
    phit_col, t2lm_col = resolved_cols["phit_col"], resolved_cols["t2lm_col"]

    # 2. 输入验证
    required_cols = [phit_col, t2lm_col]
    missing_cols = [c for c in required_cols if c not in prediction_bundle.data.columns]
    if missing_cols:
        raise SdrDataError(f"预测数据中缺少必需的列: {missing_cols}")

    # 使用Pydantic模型进行自动验证和解析，更稳健
    try:
        params = SdrModelParameters.model_validate(model_assets["optimized_parameters"])
    except Exception as e:
        raise SdrDataError(f"模型资产的参数结构无效: {e}")

    # 3. 准备数据
    phit_data = prediction_bundle.data[phit_col].to_numpy()
    t2lm_data = prediction_bundle.data[t2lm_col].to_numpy()

    # 3. 调用内部计算逻辑
    logger.info("调用内部计算逻辑进行渗透率预测...")
    k_pred = computer._calculate_sdr_permeability(
        phit=phit_data,
        t2lm=t2lm_data,
        ksdr_a=params.KSDR_A,
        phit_exp=params.PHIT_EXP,
        rho_nmr=params.RHO_NMR,
        t2lm_exp=params.T2LM_EXP,
    )

    # 4. 准备输出DataFrame
    # 使用 WpDataFrameBundle 的标准方法获取标识符列，更稳健
    identifier_df = prediction_bundle.get_identifier_dataframe()

    # 创建一个只包含预测结果的临时DataFrame
    results_df = pd.DataFrame({output_curve_name: k_pred})

    # 将标识符和预测结果合并，确保产物包含完整的上下文信息
    prediction_df = pd.concat([identifier_df, results_df], axis=1)

    # 5. 将预测曲线添加回Bundle
    prediction_bundle.add_1d_curve(
        curve_name=output_curve_name,
        curve_data=k_pred,
        unit="mD",
        description="Permeability from SDR baseline model",
        overwrite=True,
    )

    # 6. 保存和注册产物
    # 6.1 保存预测结果数据集
    prediction_path = step_dir / "sdr_predictions.csv"
    handler.save_dataframe(prediction_df, prediction_path)
    ctx.register_artifact(
        prediction_path.relative_to(ctx.run_dir),
        SdrArtifacts.PREDICTED_DATA.value,
    )

    # 6.2 保存配置快照
    config_path = step_dir / "prediction_config.json"
    handler.save_model_assets(config.model_dump(), config_path)
    ctx.register_artifact(
        config_path.relative_to(ctx.run_dir),
        SdrArtifacts.PREDICTION_CONFIG_SNAPSHOT.value,
    )

    # 7. 记录指标
    valid_preds = prediction_df[output_curve_name].dropna()
    if not valid_preds.empty:
        ctx.log_metrics(
            {
                "predicted_samples": len(prediction_df),
                "predicted_permeability_mean": valid_preds.mean(),
                "predicted_permeability_min": valid_preds.min(),
                "predicted_permeability_max": valid_preds.max(),
            },
            step_name="sdr_prediction",
        )

    logger.info(
        "SDR基准模型预测步骤完成",
        predicted_samples=len(prediction_df),
        output_curve=output_curve_name,
    )
    return {"status": "completed", "predicted_samples": len(prediction_df)}
