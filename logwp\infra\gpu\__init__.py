from __future__ import annotations

"""logwp.infra.gpu - GPU计算工具包

提供GPU加速计算的统一接口，支持自动CPU/GPU切换和性能优化。

Architecture
------------
层次/依赖: utils层GPU工具，依赖constants、exceptions
设计原则: 透明切换、自动回退、统一接口
性能特征: GPU加速、内存优化、异步计算

Core Features
-------------
- **统一计算引擎**: CPU/GPU透明切换的计算接口
- **自动回退机制**: GPU不可用时自动切换到CPU
- **内存管理**: 智能GPU内存分配和释放
- **性能监控**: GPU使用率和内存监控
- **设备管理**: 多GPU设备选择和管理

Package Structure
-----------------
- compute_engine: 统一计算引擎
- device_manager: GPU设备管理
- memory_manager: GPU内存管理
- performance_monitor: GPU性能监控

Examples
--------
>>> from logwp.infra.gpu import ComputeEngine, is_gpu_available
>>>
>>> # 检查GPU可用性
>>> if is_gpu_available():
...     print("GPU可用")
>>>
>>> # 创建计算引擎
>>> engine = ComputeEngine()
>>> result = engine.compute(data, algorithm="foster_nmr")

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§6 - GPU计算架构
- 《SCAPE_CCG_编码与通用规范.md》§7 - GPU编程规范
"""

__all__ = [
    # 统一计算引擎
    "ComputeEngine",
    "ComputeBackend",
    "ComputeResult",
    "BackendSelector",

    # GPU工具函数
    "is_gpu_available",
    "get_gpu_info",
    "to_gpu",
    "to_cpu",
    "detect_gpu_libraries",
    "check_gpu_capability",

    # 内存管理
    "get_gpu_memory_info",
    "optimize_gpu_memory",
    "clear_gpu_cache",

    # 设备管理（部分实现）
    "auto_select_device",

    # 性能监控
    "gpu_monitor",
]


def __getattr__(name: str) -> object:
    """延迟导入GPU工具函数。

    Architecture
    ------------
    层次/依赖: GPU工具门面
    设计原则: 延迟导入、条件加载、错误处理
    性能特征: 按需加载、GPU库延迟导入
    """
    # 统一计算引擎
    if name in ("ComputeEngine", "ComputeBackend", "ComputeResult", "BackendSelector"):
        from .compute_engine import ComputeEngine, ComputeBackend, ComputeResult, BackendSelector
        return locals()[name]

    # GPU基础工具
    elif name in ("is_gpu_available", "get_gpu_info", "to_gpu", "to_cpu",
                  "detect_gpu_libraries", "check_gpu_capability"):
        from .gpu_utils import (
            is_gpu_available, get_gpu_info, to_gpu, to_cpu,
            detect_gpu_libraries, check_gpu_capability
        )
        return locals()[name]

    # 内存管理（实际在 gpu_utils 中）
    elif name in ("get_gpu_memory_info", "optimize_gpu_memory", "clear_gpu_cache"):
        from .gpu_utils import get_gpu_memory_info, optimize_gpu_memory, clear_gpu_cache
        return locals()[name]

    # 设备管理（实际在 gpu_utils 中）
    elif name in ("set_gpu_device", "get_current_device", "list_gpu_devices", "auto_select_device"):
        from .gpu_utils import auto_select_device
        # 其他设备管理功能暂未实现，返回占位符
        if name == "auto_select_device":
            return auto_select_device
        else:
            raise AttributeError(f"设备管理功能 '{name}' 暂未实现")

    # 性能监控（从上级 performance 模块导入）
    elif name == "gpu_monitor":
        from ..performance import gpu_monitor
        return gpu_monitor

    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
