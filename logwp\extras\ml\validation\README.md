# Model Validation & Evaluation (`logwp.extras.ml.validation`)

一个提供专为测井机器学习设计的模型验证与评估工具的包。

在处理具有空间自相关性的测井数据时，标准的验证方法（如`KFold`）往往会给出过于乐观的评估结果。本包的核心目标是提供更科学、更可靠的验证策略，以真实地评估模型在未知井或未知深度段的泛化能力。

## 核心特性

- **领域专用交叉验证器**: 提供 `LeaveOneWellOut` 和 `SpatialKFold`，专门解决测井数据中的“信息泄露”问题。
- **与 `scikit-learn` 兼容**: 所有交叉验证器都遵循 `scikit-learn` API，可无缝集成到 `cross_val_score` 和 `GridSearchCV` 等工具中。
- **全面的评估报告**: 提供数值报告、可视化图表和自定义工程指标，形成一套完整的评估体系。

## 可用工具

### 1. 交叉验证器 (`splitters.py`)

#### `LeaveOneWellOut`

**留一井交叉验证**。在每一次迭代中，选择一口井作为测试集，其余所有井作为训练集。

**适用场景**: **多井建模**。这是评估模型在**未知井**上泛化能力的黄金标准。

```python
from sklearn.model_selection import cross_val_score
from sklearn.ensemble import RandomForestRegressor
from logwp.extras.ml.validation import LeaveOneWellOut

# 假设 X 是特征矩阵, y 是目标值, wells 是包含井名的Series
# X, y, wells = ...

model = RandomForestRegressor()
lowo_cv = LeaveOneWellOut()

# 使用 cross_val_score，必须传入 groups 参数
scores = cross_val_score(model, X, y, cv=lowo_cv, groups=wells)

print(f"留一井交叉验证 R² 得分: {scores}")
print(f"平均 R²: {scores.mean():.4f}")
```

#### `SpatialKFold`

**空间K折交叉验证**。它在每个测试集折叠的两侧强制留出一个“间隔”（gap），确保训练集和测试集在深度上是物理分离的。

**适用场景**: **单井建模**。用于评估模型在当前井的**未知深度段**上的表现，有效避免了因数据自相关导致的高估。

```python
from logwp.extras.ml.validation import SpatialKFold

# 假设 X_well_A 是A井的特征矩阵, y_well_A 是A井的目标值
# 数据必须按深度排序
# X_well_A, y_well_A = ...

# 创建一个带有10个样本点间隔的SpatialKFold
spatial_cv = SpatialKFold(n_splits=5, gap=10)

scores = cross_val_score(model, X_well_A, y_well_A, cv=spatial_cv)

print(f"空间K折交叉验证 R² 得分: {scores}")
print(f"平均 R²: {scores.mean():.4f}")
```

### 2. 评估报告 (`reporting.py` & `plotting.py`)

#### `regression_report(y_true, y_pred, groups=None)`

生成一个包含整体和按组（如按井）分解的数值指标的字典。

```python
from logwp.extras.ml.validation import regression_report
import json

# y_true, y_pred, wells = ...
report = regression_report(y_true, y_pred, groups=wells)
print(json.dumps(report, indent=2))
```

#### `plot_regression_crossplot(y_true, y_pred, groups=None, ...)`

绘制一个高质量的预测值 vs. 真实值交会图，支持按组着色。

```python
import matplotlib.pyplot as plt
from logwp.extras.ml.validation import plot_regression_crossplot

# y_true, y_pred, wells = ...
ax = plot_regression_crossplot(y_true, y_pred, groups=wells, title="模型预测效果")
plt.show()
```

### 3. 自定义指标 (`metrics.py`)

#### `accuracy_within_tolerance(y_true, y_pred, tolerance)`

计算在给定绝对容差范围内的预测准确率百分比。

**适用场景**: 当我们更关心预测值是否落在“可接受的误差范围”内时，这个指标比RMSE更有解释性。例如，评估孔隙度预测误差在±0.02 (2 p.u.)以内的样本比例。

```python
from logwp.extras.ml.validation import accuracy_within_tolerance

# 假设 y_true 和 y_pred 是孔隙度预测值
# y_true, y_pred = ...

accuracy = accuracy_within_tolerance(y_true, y_pred, tolerance=0.02)
print(f"在 ±0.02 容差范围内的准确率为: {accuracy:.2f}%")
```
