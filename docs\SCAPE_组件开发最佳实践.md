# SCAPE 机器学习组件开发最佳实践

**版本**: 1.0
**状态**: 已采纳
**参考实现**: `scape/core/obmiq` (PyTorch版)

---

## 0. 引言

### 0.1. 文档目标

本篇文档是**SCAPE项目机器学习组件（步骤包）的权威开发指南**。它旨在为开发者（包括人类与AI编程助手）提供一套清晰、可执行的最佳实践，确保所有组件都具备高度的健壮性、可维护性和一致性。

本文档并非要取代以下三个基础框架文档，而是作为它们的**上层应用指南**：
*   `logwp/extras/tracking/README.md`: 定义了如何使组件**可追踪**。
*   `logwp/extras/plotting/README.md`: 定义了如何为组件提供**绘图服务**。
*   `logwp/models/README.md`: 定义了项目统一的**核心数据模型**。

本指南将以 `scape/core/obmiq` 组件作为标准参考实现，详细阐述如何综合运用上述三大基础服务，构建一个高质量的多步骤机器学习组件。

### 0.2. 核心理念：分层解耦

组件开发的核心理念是**分层与解耦**。我们将一个组件的开发明确划分为两个层面，每个层面使用不同的“语言”和“数据类型”，并通过`门面(Facade)`层进行优雅的转换。

 <!-- 你可以替换成真实的图床链接 -->

```mermaid
graph TD
    subgraph "外部世界 (Workflow & User)"
        A[WpDataFrameBundle]
        B[RunContext]
        C[PlotProfile]
    end

    subgraph "组件门面 (Facade Layer)"
        style F fill:#f9f,stroke:#333,stroke-width:2px
        F(Facade 函数<br>run_*_step)
    end

    subgraph "组件内部 (Internal Layer)"
        G[内部算法<br>e.g., PyTorch, Scikit-learn]
        H[内部数据类型<br>pd.DataFrame, np.ndarray, torch.Tensor]
    end

    A --> F
    B --> F
    C --> F
    F -- "翻译与转换" --> G
    G -- "使用" --> H
```

*   **外部API层 (API & Orchestration Layer)**:
    *   **职责**: 与工作流（Workflow）和其他组件交互，处理用户输入，管理产物。
    *   **数据类型**: 使用项目统一的**外部数据类型**，如 `WpDataFrameBundle`, `RunContext`, `PlotProfile`。
    *   **实现位置**: 主要在 `facade.py` 文件中。

*   **内部算法层 (Internal Algorithm Layer)**:
    *   **职责**: 实现核心的、纯粹的科学计算和机器学习算法。
    *   **数据类型**: 使用标准的、与技术栈绑定的**内部算法数据类型**，如 `pandas.DataFrame`, `numpy.ndarray`, `torch.Tensor`。
    *   **实现位置**: 严格封装在 `internal/` 目录中。

**门面 (Facade) 的核心角色是“翻译官”**，负责将外部世界丰富但复杂的 `logwp` 对象模型，转换为内部算法层所需的、简洁标准的 `DataFrame` 或 `ndarray`。

### 0.3. 指南，而非教条 (A Guide, Not a Dogma)

本篇文档旨在为组件开发提供一个坚实的起点和一套经过验证的最佳实践。它定义了一套“默认的成功路径”，遵循它能够极大地提升代码质量和团队协作效率。

然而，本指南并非一成不变的教条。在实际的开发过程中，您可能会遇到本文档未曾预见的新问题或更优的解决方案。我们鼓励开发者在深刻理解本框架核心理念（分层、解耦、可追踪）的基础上，根据具体场景的需要，对这些实践进行合理的调整和创新。

**本指南提供了“应该怎么做”的建议，但更重要的是理解“为什么这么做”。** 当您需要偏离本指南时，请确保您的设计同样能够满足我们对代码健壮性、可维护性和可复现性的核心要求。


---

## 1. 组件的结构与设计模式

所有组件必须遵循标准的多步骤包结构。

```
scape/core/my_component/
├── __init__.py               # 导出公共API
├── training_facade.py        # 训练步骤的入口
├── prediction_facade.py      # 预测步骤的入口
├── config.py                 # Pydantic配置模型
├── constants.py              # 产物和绘图模板的常量
├── artifact_handler.py       # 产物的序列化/反序列化
├── plotting.py               # 从数据快照复现图表
├── plot_profiles.py          # 注册绘图模板
└── internal/                 # 内部算法实现
    ├── model_builder.py
    ├── tuning_procedure.py
    └── ...
```

---

## 2. 门面 (Facade) 开发最佳实践

`facade.py` 是组件的“门面”，是其唯一公共API。编写高质量的门面函数是组件成功的关键。

### 2.1. API契约：定义清晰的函数签名

门面函数的签名就是它与外部世界签订的“契约”。

*   **必须** 接收 `config` 和 `ctx: RunContext` 参数。
*   **必须** 为所有数据输入参数使用 `logwp.models` 中定义的类型，最常用的是 `WpDataFrameBundle`。
*   **必须** 使用关键字参数（`*`）来明确区分数据选择器。

**示例 (`training_facade.py`)**:
```python
def run_obmiq_training_step(
    config: ObmiqTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle, # ✅ 使用logwp核心数据模型
    *,
    sequence_feature: str,           # ✅ 数据选择器参数
    tabular_features: List[str],
    # ...
) -> Dict[str, Any]:
```

### 2.2. 核心职责：扮演好“翻译官”

门面函数**最重要**的职责，是在流程开始时，将外部的“逻辑曲线名”一次性地解析为内部算法所需的“物理列名”。

*   **必须** 在函数入口处，调用一个私有的辅助函数（如 `_resolve_feature_selectors`）来完成所有名称的解析和转换。
*   **必须** 利用 `WpDataFrameBundle.curve_metadata` 提供的 `expand_curve_names` 方法来处理名称映射，特别是对于二维组合曲线的一对多关系。
*   后续所有对内部规程的调用，都**必须**传递解析后的“物理列名”字典。

**示例 (`training_facade.py`)**:
```python
def run_obmiq_training_step(...):
    # ...
    # 步骤 0: 将用户提供的曲线名解析为DataFrame列名
    resolved_selectors = _resolve_feature_selectors(
        bundle=train_bundle,
        sequence_feature=sequence_feature,
        # ...
    )
    # ...
    # 后续调用都使用 resolved_selectors
    best_hps, _ = tuning_procedure.run_hyperparameter_tuning_cv(
        train_df=train_bundle.data, config=config, feature_selectors=resolved_selectors
    )
    # ...

def _resolve_feature_selectors(...):
    # ✅ 利用bundle元数据进行翻译
    sequence_cols = bundle.curve_metadata.expand_curve_names(
        [sequence_feature], mode=CurveExpansionMode.DATAFRAME
    )
    # ...
    return {"sequence_cols": sequence_cols, ...}
```

### 2.3. 健壮性：执行输入验证

*   **必须** 在名称解析后，立即调用一个私有的验证函数（如 `_validate_training_inputs`）来检查参数的有效性。
*   验证逻辑**必须**基于解析后的“物理列名”，检查它们是否存在于 `DataFrame` 中。

**示例 (`training_facade.py`)**:
```python
def _validate_training_inputs(
    bundle: WpDataFrameBundle,
    resolved_selectors: Dict[str, Any],
    # ...
):
    # ...
    all_resolved_cols = set(resolved_selectors["sequence_cols"]) | ...
    missing_cols = all_resolved_cols - set(bundle.data.columns)
    if missing_cols:
        raise ValueError(f"输入数据中缺少以下必需的列: {missing_cols}")
```

### 2.4. 清晰性：保持主流程简洁

门面函数的主流程应该像一份高级“菜谱”，清晰地列出主要步骤，而将每个步骤的具体实现封装在私有函数中。

**示例 (`training_facade.py`)**:
```python
def run_obmiq_training_step(...):
    logger.info("===== OBMIQ Training Step Started =====")
    step_dir = ctx.get_step_dir(...)

    # 步骤 0: 解析与验证
    resolved_selectors = _resolve_feature_selectors(...)
    _validate_training_inputs(...)

    # 步骤 1: 超参数寻优
    best_hps, cv_report_df = _run_tuning_procedure(...)

    # 步骤 2: 最终模型训练
    model_assets, history_df, final_eval_df = _run_final_training(...)

    # 步骤 3: 保存核心产物
    _save_core_artifacts(...)

    # 步骤 4: 可解释性分析
    _run_and_save_interpretability_artifacts(...)

    # 步骤 5: 导出ONNX
    _export_model_to_onnx(...)

    logger.info("===== OBMIQ Training Step Finished =====")
    return {"status": "completed", ...}
```

---

## 3. 内部逻辑 (`internal/`) 开发最佳实践

`internal/` 目录是组件的“引擎室”，外部世界不应关心其内部构造。

*   **纯粹性**: 内部函数应尽可能设计为纯函数，接收标准的 `DataFrame`, `ndarray` 等作为输入，返回计算结果。它们不应直接与 `RunContext` 或 `WpDataFrameBundle` 交互。
*   **封装性**: 将复杂的、多阶段的内部逻辑封装成“规程 (Procedure)”，例如 `tuning_procedure.py`。
*   **无副作用**: 内部函数（尤其是在交叉验证循环中被调用的）**严禁**原地修改传入的 `DataFrame`。**必须**在操作前创建副本 (`df.copy()`)，以防止数据泄露。

**示例 (`internal/data_handler.py`)**:
```python
def create_dataloaders_for_fold(...):
    # ...
    # ✅ 正确做法：创建副本以避免原地修改
    train_df_processed = train_df.copy()
    val_df_processed = val_df.copy()

    train_df_processed[tab_cols] = tabular_scaler.fit_transform(train_df[tab_cols])
    val_df_processed[tab_cols] = tabular_scaler.transform(val_df[tab_cols])
    # ...
```

---

## 4. 可复现绘图 (`plotting.py` & `plot_profiles.py`)

绘图是组件产出的重要部分，必须遵循“可复现”原则。

### 4.1. `plotting.py`: 实现“快照优先”原则

*   **单一职责**: `plotting.py` 中的函数**只负责从数据快照（如`.csv`文件）中读取数据并绘图**。它们**严禁**进行任何形式的计算。
*   **命名约定**: 所有函数必须以 `replot_` 开头，例如 `replot_crossplot`。
*   **标准签名**: 函数必须接收 `snapshot_path: Path`, `profile: PlotProfile`, `output_path: Path` 作为核心参数。
*   **样式解耦**: 函数内部**严禁**硬编码任何样式参数（如 `color`, `linewidth`）。所有样式都必须从传入的 `profile.artist_props` 字典中获取。

**示例 (`plotting.py`)**:
```python
def replot_grad_cam(snapshot_path: Path, profile: PlotProfile, output_path: Path):
    # 1. 只从快照读取数据
    data = pd.read_csv(snapshot_path)
    t2_axis = data["t2_axis"].values
    # ...

    # 2. 应用配置
    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    # 3. 从profile获取样式
    ax.plot(t2_axis, image, **profile.artist_props.get("line", {}))
    ax.imshow(..., **profile.artist_props.get("imshow", {}))

    # 4. 保存图像
    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
```

### 4.2. `plot_profiles.py`: 注册默认样式

*   **职责**: 为本组件的所有图表定义并注册默认的 `PlotProfile` 模板。
*   **实现**: 创建多个 `_create_*_profile()` 函数，并在模块的顶层调用 `registry.register()` 将它们注册到全局绘图服务中。
*   **命名**: 模板名称应在 `constants.py` 中以枚举形式定义，并遵循 `component_name.chart_type` 的格式。

**示例 (`plot_profiles.py`)**:
```python
from .constants import ObmiqPlotProfiles

def _create_grad_cam_profile() -> PlotProfile:
    return PlotProfile(
        name=ObmiqPlotProfiles.GRAD_CAM.value, # ✅ 使用常量
        artist_props={
            "line": {"color": "black", "linewidth": 2, "label": "T2 Spectrum Shape"},
            "imshow": {"cmap": "viridis", "aspect": "auto", "alpha": 0.5},
        },
        # ...
    )

# 在模块加载时，立即执行注册
registry.register(_create_grad_cam_profile())
```

---

## 5. 产物处理 (`artifact_handler.py`)

*   **无状态**: `ArtifactHandler` 类必须是无状态的。所有方法都应是静态方法，或在一个无状态的实例上调用。
*   **职责单一**: 只负责特定格式产物的序列化/反序列化和读/写，不包含任何业务逻辑。
*   **门面调用**: 由 `facade` 函数在需要保存或加载产物时调用。

**示例 (`training_facade.py` 中使用)**:
```python
def run_obmiq_training_step(...):
    # ...
    # 步骤 3: 保存核心产物
    handler = ObmiqArtifactHandler()
    assets_path = step_dir / "model_assets_pytorch.pkl"
    handler.save_model_assets(model_assets, assets_path) # ✅ 调用handler保存
    ctx.register_artifact( # ✅ 调用ctx注册
        assets_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.MODEL_ASSETS.value
    )
    # ...
```

---

## 6. 总结：一个完整的工作流

1.  **Workflow** 调用 `training_facade.run_obmiq_training_step`，传入 `config`、`ctx` 和一个 `WpDataFrameBundle`。
2.  **Facade** 接收输入，调用 `_resolve_feature_selectors` 将“逻辑名”翻译为“物理列名”，然后调用 `_validate_training_inputs` 进行验证。
3.  **Facade** 调用 `internal.tuning_procedure`，传入 `DataFrame` 和解析后的“物理列名”。
4.  **Tuning Procedure** 在CV循环中，为每一折调用 `internal.data_handler` 创建 `DataLoader`。
5.  **Data Handler** 创建 `DataFrame` 的副本，拟合预处理器，并生成 `torch.Tensor`。
6.  **Tuning Procedure** 使用 `Optuna` 和 `PyTorch` 训练循环找到最佳超参数。
7.  **Facade** 接收最佳超参数，调用 `internal.final_training_procedure` 训练最终模型，返回 `model_assets` 字典。
8.  **Facade** 调用 `ObmiqArtifactHandler` 将 `model_assets` 保存到磁盘，并调用 `ctx.register_artifact` 注册。
9.  **Facade** 调用 `internal.interpretability` 计算归因，返回数据。
10. **Facade** 将归因数据保存为**数据快照**。
11. **Facade** 调用 `plotting.replot_grad_cam`，传入快照路径和从 `plot_registry` 获取的 `PlotProfile`，生成最终图表。
12. **Workflow** 结束，`RunContext` 将所有记录写入 `manifest.json`，完成一次完全可追踪的运行。

---

## 7. API层的演进与功能下沉

### 7.1. 稳定性优先原则

项目的核心API层，特别是 `logwp.models`、`logwp.extras.tracking` 和 `logwp.extras.plotting`，其稳定性是保证整个项目健壮性的基石。在日常的组件开发中，我们应将这些API视为稳定的“服务提供者”，并基于其现有功能进行构建，**一般不应轻易修改API**。

### 7.2. 自底向上的功能提炼

然而，当多个组件的开发实践都指向同一个问题，即“我们反复在不同的组件中编写相似的、可复用的辅助函数”时，这就触发了一个重要的架构演进信号。

这意味着我们发现了一个**公共的、可复用的功能模式**，此时应考虑将这个功能**“下沉”**到更基础的API层。

*   **主要目标**: `logwp.models` 包是功能下沉的主要候选者。例如，如果在多个组件中都需要一种特定的数据筛选或转换逻辑，那么将其作为 `WpDataFrameBundle` 的一个新方法，将极大地提升代码的复用性和开发效率。

*   **决策流程**: 任何对核心API的扩充都必须经过审慎的讨论。开发者在识别出潜在的下沉功能后，应与项目架构师或核心团队进行沟通，在获得同意后，方可对API进行扩展。

这种“自底向上”的反馈机制，确保了我们的核心API能够持续吸收来自具体实践的精华，不断演进，保持其先进性和实用性。

---

## 8. 架构与流程可视化
为了更直观地理解本框架的设计，本节提供组件的对象结构图和一次典型运行的关键流程图。

### 8.1. 对象结构与关系图
此图展示了框架中核心概念（Workflow, RunContext, Step等）之间的静态关系和职责划分。它强调了“关注点分离”和“清晰接口”的设计原则。

```mermaid
graph TD
    subgraph "上层编排 (Workflow Layer)"
        A[Workflow 驱动脚本]
    end

    subgraph "追踪核心 (Tracking Core)"
        B(RunContext)
    end

    subgraph "组件/步骤 (Step Component)"
        style C fill:#f9f,stroke:#333,stroke-width:2px
        C(MyStep 包)
        subgraph C
            D[facade.py<br>run_my_step()]
            E[config.py<br>MyStepConfig]
            F[artifact_handler.py<br>MyStepArtifactHandler]
            G[internal/*]
        end
    end

    subgraph "物理产物 (Physical Output)"
        H((Artifact<br>模型/数据文件))
    end

    A -- "1. 创建" --> B
    A -- "2. 创建" --> E
    A -- "3. 调用" --> D

    D -- "4. 接收" --> B
    D -- "4. 接收" --> E
    D -- "5. 调用" --> G
    D -- "6. 使用" --> F

    B -- "7. 管理" --> H
    F -- "8. 读/写" --> H

    linkStyle 0,1,2,3,4,5,6,7,8 stroke-width:2px,fill:none,stroke:gray;
```
**图解说明**:

- **Workflow驱动脚本**: 位于最上层，负责整个流程的编排。它创建 `RunContext` 和 `Step` 所需的配置 `MyStepConfig`。
- **调用入口**: `Workflow` 脚本通过调用 `facade` 模块中的 `run_my_step()` 函数来启动一个步骤。
- **参数传递**: `run_my_step()` 函数接收 `RunContext` 和 `MyStepConfig` 作为核心输入，这是它与外界交互的“契约”。
- **内部实现**: `run_my_step()` 内部调用 `internal` 目录下的核心算法逻辑，并将产物的读写操作委托给 `MyStepArtifactHandler`。
- **产物交互**: `Artifact Handler` 负责具体的I/O操作，将模型或数据（`Artifact`）写入文件系统。
- **追踪记录**: `run_my_step()` 通过 `RunContext` 提供的接口（如 `register_artifact`）来记录产物元数据，`RunContext` 最终将所有信息汇总到清单文件中。

### 8.2. 关键工作流程图

此序列图展示了一次包含两个步骤（一个训练，一个预测）的典型工作流中，各个组件之间随时间发生的动态交互。

```mermaid
sequenceDiagram
    participant U as User/Workflow
    participant R as RunContext
    participant S1 as Step 1 (Training)
    participant H1 as ArtifactHandler 1
    participant S2 as Step 2 (Prediction)

    U->>R: 创建 RunContext(run_dir, config)
    activate R

    U->>S1: run_training_step(config1, ctx)
    activate S1
    S1->>R: get_step_dir("training")
    R-->>S1: 返回 "training" 目录路径
    S1->>H1: save_model(model_data, path)
    activate H1
    H1-->>S1: 模型保存成功
    deactivate H1
    S1->>R: register_artifact(name="final_model", ...)
    R-->>S1: 注册成功
    S1-->>U: 返回训练结果
    deactivate S1

    U->>S2: run_prediction_step(config2, ctx)
    activate S2
    S2->>R: get_artifact_path("final_model")
    R-->>S2: 返回模型文件绝对路径
    S2->>H1: load_model(path)
    activate H1
    H1-->>S2: 返回加载的模型对象
    deactivate H1
    S2-->>U: 返回预测结果
    deactivate S2

    deactivate R
    Note right of U: `with` 块结束, RunContext 自动<br>将所有记录写入 manifest.json
```
**图解说明**:

- **初始化**: 用户（或 `Workflow` 脚本）首先初始化 `RunContext`。
- **Step 1 (生产者)**:
  - `Step 1` 从 `RunContext` 获取其专属的输出目录。
  - 它使用自己的 `ArtifactHandler` 将训练好的模型保存到该目录。
  - 然后，它调用 `RunContext` 的 `register_artifact` 方法，将刚才保存的文件的元数据（路径、哈希等）注册到运行清单中。
- **Step 2 (消费者)**:
  - `Step 2` 需要使用 `Step 1` 产出的模型。它通过向 `RunContext` 查询逻辑名称 `"final_model"` 来获取该产物的物理路径。
  - 然后，它使用 `Step 1` 提供的 `ArtifactHandler` 来加载模型文件。
- **完成**: 当所有步骤执行完毕，`RunContext` 的上下文管理器退出时，它会自动将所有记录的参数、指标和产物信息持久化到 `manifest.json` 文件中，完成一次可复现的运行记录。

---

## 9. PyTorch最佳实践

当使用 PyTorch 作为组件的后端时，其灵活性和命令式的编程风格为我们带来了强大的控制力，但同时也要求我们遵循一套特定的最佳实践来保证代码的健壮性和可维护性。本章总结了在 SCAPE 框架下使用 PyTorch 的核心要点。

### 9.1. 配置与超参数寻优：流程与逻辑的分离 (Optuna)

在设计机器学习组件时，如何管理超参数是一个核心问题。我们采取了一种**混合模式**，旨在兼顾**用户易用性**（通过配置文件快速调整）和**开发者灵活性**（在代码中实现复杂逻辑）。

**设计决策**:
我们将超参数的**搜索范围**定义在 `ObmiqTrainingConfig` 中，并提供合理的默认值。同时，在 `internal/tuning_procedure.py` 的 `objective` 函数中，我们从配置对象中读取这些范围，并将其传递给 `Optuna` 的 `trial.suggest_*` 方法。

**这种设计的好处**:

1.  **单一事实来源 (Single Source of Truth)**: 所有超参数的默认搜索范围都集中在 `config.py` 中，清晰明了，易于管理。
2.  **用户友好**: 对于大多数实验，用户只需修改顶层的配置文件，即可调整搜索空间，无需深入到算法的内部实现代码。
3.  **代码解耦**: `tuning_procedure.py` 的职责变得更纯粹，它只负责执行寻优“流程”，而不再硬编码搜索范围的“默认值”。
4.  **保留灵活性**: 对于需要实现复杂条件化超参数（例如，当`num_layers=2`时，才定义`layer2_units`的搜索范围）的高级场景，开发者仍然可以修改 `objective` 函数的内部逻辑来实现。

**最佳实践**:

*   **对于用户**: 通过修改传递给 `run_obmiq_training_step` 的 `ObmiqTrainingConfig` 实例来调整超参数搜索范围。
*   **对于开发者**: 将 `config.py` 作为定义超参数默认范围的唯一地方。在 `objective` 函数中，直接从 `config` 对象读取这些范围。

**示例 (`config.py`)**:
```python
class ObmiqTrainingConfig(BaseModel):
    # ... 其他流程控制参数 ...

    # --- Hyperparameter Search Space ---
    # 用户可以通过修改这些值来覆盖默认搜索范围。
    hp_cnn_filters: conlist(int, min_length=1) = Field(
        default_factory=lambda: [16, 32, 64], description="CNN卷积核数量的搜索范围"
    )
    hp_learning_rate: conlist(float, min_length=2, max_length=2) = Field(
        default_factory=lambda: [1e-4, 1e-2],
        description="学习率的浮点数搜索范围 [min, max]",
    )
    # ...
```

**示例 (`internal/tuning_procedure.py`)**:
```python
class _Objective:
    def __init__(self, config: ObmiqTrainingConfig, ...):
        self.config = config
        # ...

    def __call__(self, trial: optuna.Trial) -> float:
        # 1. 从配置对象中读取搜索范围
        hp = {
            'cnn_filters': trial.suggest_categorical(
                'cnn_filters',
                self.config.hp_cnn_filters # ✅ 直接从config读取
            ),
            'learning_rate': trial.suggest_float(
                'learning_rate',
                self.config.hp_learning_rate[0], # ✅ 从config读取min
                self.config.hp_learning_rate[1], # ✅ 从config读取max
                log=True
            )
        }

        # 2. 使用这些超参数构建模型并进行训练
        model = OBMIQPyTorchModel(hp, ...)
        # ... 训练循环 ...

        return validation_score
```

### 9.2. 模型健壮性：在构造函数中进行类型转换

在数据流转过程中（例如，从 `Optuna` 的 `DataFrame` 报告中读取最佳参数），数值类型可能会发生非预期的变化（如 `int` 变为 `float` 或 `numpy.int64`）。PyTorch 的底层实现通常期望纯粹的 Python `int` 或 `float`。

**最佳实践**: 在模型的 `__init__` 方法中，对从超参数字典中获取的值进行**显式的类型转换**。这是一种防御性编程，确保模型总能接收到正确的类型，增加了组件的健壮性。

**示例 (`internal/model_builder.py`)**:
```python
class OBMIQPyTorchModel(nn.Module):
    def __init__(self, hp: Dict[str, Any], data_shapes: Dict[str, int]):
        super().__init__()

        # ✅ 在使用前进行显式类型转换
        cnn_filters = int(hp["cnn_filters"])
        cnn_kernel_size = int(hp["cnn_kernel_size"])
        dropout_rate = float(hp["dropout_rate"])

        self.cnn_branch = nn.Sequential(
            nn.Conv1d(
                in_channels=1,
                out_channels=cnn_filters, # 使用转换后的类型
                kernel_size=cnn_kernel_size,
            ),
            # ...
        )
```

### 9.3. 适配生态：使用包装器处理字典输入

我们的模型 `forward` 方法接收一个字典作为输入 (`def forward(self, x: Dict[str, torch.Tensor])`)，这种设计清晰且可扩展。然而，很多 PyTorch 生态工具（如用于导出 ONNX 的 `torch.onnx.export` 或用于可视化的 `torch.utils.tensorboard.SummaryWriter.add_graph`）期望模型的 `forward` 方法接收多个独立的张量作为位置参数 (`*args`)。

**最佳实践**: 使用一个临时的**包装器模型 (Wrapper Model)** 来适配这些工具。这个包装器的 `forward` 方法接收 `*args`，在内部将它们重新组装成字典，再传递给原始模型。

**示例 (`internal/final_training_procedure.py`)**:
```python
class _GraphTracerWrapper(torch.nn.Module):
    """一个包装器，用于适配TensorBoard的add_graph对字典输入模型的追踪。"""
    def __init__(self, model: OBMIQPyTorchModel, input_keys: list[str]):
        super().__init__()
        self.model = model
        self.input_keys = input_keys

    def forward(self, *inputs) -> torch.Tensor:
        # ✅ 将位置参数重新打包成字典
        input_dict = {key: val for key, val in zip(self.input_keys, inputs)}
        return self.model(input_dict)

# 在需要的地方使用包装器
wrapper = _GraphTracerWrapper(model, list(sample_inputs.keys()))
tb_logger.writer.add_graph(wrapper, list(sample_inputs.values()))
```

### 9.4. 设备管理：明确的 `.to(device)` 调用

`RuntimeError: Input type (...) and weight type (...) should be the same` 是 PyTorch 中最常见的错误之一，它源于模型参数和输入数据位于不同的设备上（如一个在 CPU，一个在 GPU）。

**最佳实践**:
1.  在流程开始时确定设备 `device = "cuda" if torch.cuda.is_available() else "cpu"`。
2.  在执行任何计算之前，**明确地**将模型 (`model.to(device)`) 和所有输入张量 (`inputs.to(device)`) 移动到同一个设备。
3.  在所有计算（如训练、评估）完成后，**在保存模型状态之前**，将模型移回CPU (`model.cpu().state_dict()`)。这确保了保存的模型资产不依赖于特定的硬件，具有更好的可移植性。

### 9.5. 手动训练循环：控制与责任

与 Keras 的高级 `.fit()` 方法不同，PyTorch 鼓励用户编写手动的训练循环。这提供了无与伦比的灵活性和控制力，但也意味着开发者需要自己负责实现训练、验证、早停、指标计算等所有逻辑。在我们的框架中，这些逻辑被清晰地封装在 `internal` 规程中，如 `tuning_procedure.py` 和 `final_training_procedure.py`。
