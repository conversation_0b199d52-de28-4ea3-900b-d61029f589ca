"""scape.core.swift_pso.internal.tsne_computer - t-SNE降维计算核心逻辑

包含t-SNE降维计算的纯计算函数，无副作用，只负责计算并返回结果。

Architecture
------------
层次/依赖: scape/core层，t-SNE内部计算逻辑
设计原则: 纯函数、无副作用、计算专注
性能特征: scikit-learn优化、内存管理、数值稳定性

遵循CCG规范：
- SC-1: 算法正确性
- SC-2: 数值稳定性
- SC-3: 实验可复现
- PF-1: 内存控制

References
----------
- 《SCAPE_MS_方法说明书》§4.4.5 - t-SNE可视化数学定义
- 迁移自 scape/core/swift_pso_backup/internal/tsne_service.py
"""

from __future__ import annotations

from typing import Any, Dict, Tuple

import pandas as pd
import numpy as np
from sklearn.manifold import TSNE
from sklearn.cluster import KMeans, DBSCAN
from sklearn.metrics import silhouette_score
from sklearn.preprocessing import StandardScaler
from scipy.spatial.distance import pdist, squareform

from logwp.infra import get_logger

logger = get_logger(__name__)


def compute_tsne(
    source_data: pd.DataFrame,
    config: Dict[str, Any]
) -> Tuple[pd.DataFrame, float]:
    """执行t-SNE降维和聚类计算。

    这是t-SNE可视化步骤的核心纯计算函数，对SWIFT-PSO参数演化轨迹
    进行降维和聚类分析。

    Args:
        source_data: t-SNE源数据DataFrame，包含参数演化轨迹
        config: t-SNE配置字典，包含perplexity、n_iter等参数

    Returns:
        Tuple[pd.DataFrame, float]:
            - result_df (pd.DataFrame): 包含t-SNE降维和聚类结果的DataFrame。
            - silhouette_avg (float): 聚类的平均轮廓系数。

    Note:
        此函数是纯计算函数，不执行任何I/O操作。绘图逻辑已分离到plotting模块。

    References:
        迁移并重构自 scape/core/swift_pso_backup/internal/tsne_service.py::run_tsne_analysis_and_plot
    """
    logger.info(
        "开始t-SNE降维计算",
        operation="tsne_computation",
        data_rows=len(source_data),
        perplexity=config.get("perplexity", 30)
    )

    # 验证输入数据
    if source_data.empty:
        raise ValueError("t-SNE源数据不能为空")

    # 提取参数列（排除元数据列）
    metadata_columns = ['bootstrap_run', 'lowo_fold', 'iteration']
    param_columns = [col for col in source_data.columns if col not in metadata_columns]

    if not param_columns:
        raise ValueError("源数据中没有找到参数列")

    # 提取参数数据进行标准化
    param_data = source_data[param_columns].values

    # 检查数据有效性
    if np.any(np.isnan(param_data)):
        logger.warning("源数据包含NaN值，将进行处理", operation="tsne_computation")
        # 移除包含NaN的行
        valid_mask = ~np.isnan(param_data).any(axis=1)
        param_data = param_data[valid_mask]
        source_data_clean = source_data[valid_mask].copy()

        if len(param_data) == 0:
            raise ValueError("移除NaN值后没有有效数据")
    else:
        source_data_clean = source_data.copy()

    # 数据标准化
    logger.debug("开始数据标准化", operation="tsne_computation")
    scaler = StandardScaler()
    param_data_scaled = scaler.fit_transform(param_data)

    # 配置t-SNE参数
    tsne_params = {
        'n_components': 2,
        'perplexity': config.get('perplexity', 30),
        'learning_rate': config.get('learning_rate', 200.0),
        'max_iter': config.get('n_iter', 1000),  # 新版scikit-learn使用max_iter而不是n_iter
        'init': config.get('init', 'pca'),
        'random_state': config.get('random_state', 42),
        'verbose': 0  # 禁用详细输出
    }

    # 验证perplexity参数
    if tsne_params['perplexity'] >= len(param_data_scaled):
        logger.warning(
            "perplexity值过大，自动调整",
            operation="tsne_computation",
            original_perplexity=tsne_params['perplexity'],
            data_size=len(param_data_scaled)
        )
        tsne_params['perplexity'] = min(30, len(param_data_scaled) - 1)

    # 执行t-SNE降维
    logger.info(
        "执行t-SNE降维",
        operation="tsne_computation",
        **tsne_params
    )

    try:
        tsne = TSNE(**tsne_params)
        tsne_coords = tsne.fit_transform(param_data_scaled)

        # --- 新增：在原始高维空间上执行K-means聚类 ---
        logger.info(
            "在原始高维空间上执行K-means聚类",
            operation="tsne_computation",
            n_clusters=config.get('n_clusters', 4)
        )
        kmeans = KMeans(
            n_clusters=config.get('n_clusters', 4),
            init='k-means++',
            n_init='auto',
            random_state=config.get('random_state', 42)
        )
        cluster_labels = kmeans.fit_predict(param_data_scaled)

        # --- 新增：计算聚类质量（轮廓系数） ---
        # 轮廓系数要求：聚类数量在2到n_samples-1之间，且每个聚类至少有1个样本
        if len(param_data_scaled) >= 3 and len(set(cluster_labels)) >= 2:
            silhouette_avg = silhouette_score(param_data_scaled, cluster_labels)
            logger.info(f"聚类轮廓系数 (Silhouette Score): {silhouette_avg:.4f}")
        else:
            silhouette_avg = None
            logger.warning(
                f"样本数量不足或聚类数量不合适，跳过轮廓系数计算",
                n_samples=len(param_data_scaled),
                n_clusters=len(set(cluster_labels))
            )

        # 构建结果DataFrame
        result_df = source_data_clean.copy()
        result_df['tsne_x'] = tsne_coords[:, 0]
        result_df['tsne_y'] = tsne_coords[:, 1]
        result_df['cluster_label'] = cluster_labels

        logger.info(
            "t-SNE降维和聚类计算完成",
            operation="tsne_computation",
            result_rows=len(result_df),
            tsne_x_range=(float(tsne_coords[:, 0].min()), float(tsne_coords[:, 0].max())),
            tsne_y_range=(float(tsne_coords[:, 1].min()), float(tsne_coords[:, 1].max()))
        )

        return result_df, silhouette_avg

    except Exception as e:
        logger.error(
            "t-SNE降维计算失败",
            operation="tsne_computation",
            error=str(e),
            **tsne_params
        )
        raise


def compute_cluster_analysis(
    source_data: pd.DataFrame,
    config: Dict[str, Any]
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    对最终收敛的参数点进行t-SNE降维和聚类分析。

    此函数专门用于分析每个优化运行的最终结果，包括：
    1. 从完整的演化轨迹中筛选出最终收敛点。
    2. 对这些点进行t-SNE降维和K-means聚类。
    3. 计算定量的聚类指标，如轮廓系数、簇心、簇内方差和簇间距离。

    Args:
        source_data (pd.DataFrame): 完整的参数演化轨迹数据。
        config (Dict[str, Any]): t-SNE和聚类的配置字典。

    Returns:
        Tuple[pd.DataFrame, Dict[str, Any]]:
            - result_df (pd.DataFrame): 包含最终收敛点、t-SNE坐标和聚类标签的DataFrame。
            - report (Dict[str, Any]): 包含所有量化分析结果的报告字典。
    """
    logger.info(
        "开始最终收敛点的聚类分析",
        operation="cluster_analysis_computation",
        total_points=len(source_data)
    )

    # 1. 筛选出每个 (bootstrap_run, lowo_fold) 组合的最终收敛点
    group_cols = ['bootstrap_run', 'lowo_fold']
    final_points_idx = source_data.groupby(group_cols)['iteration'].idxmax()
    final_points_df = source_data.loc[final_points_idx].copy()
    logger.info(f"筛选出 {len(final_points_df)} 个最终收敛点进行分析。")

    if final_points_df.empty:
        raise ValueError("未能从源数据中筛选出最终收敛点。")

    # 2. 提取参数列并进行标准化
    metadata_columns = ['bootstrap_run', 'lowo_fold', 'iteration']
    param_columns = [col for col in final_points_df.columns if col not in metadata_columns]
    param_data = final_points_df[param_columns].values

    scaler = StandardScaler()
    param_data_scaled = scaler.fit_transform(param_data)

    # 3. 执行t-SNE降维
    tsne_params = {
        'n_components': 2,
        'perplexity': config.get('perplexity', 30),
        'learning_rate': config.get('learning_rate', 200.0),
        'max_iter': config.get('n_iter', 1000),
        'init': config.get('init', 'pca'),
        'random_state': config.get('random_state', 42),
    }
    if tsne_params['perplexity'] >= len(param_data_scaled):
        tsne_params['perplexity'] = max(1, len(param_data_scaled) - 1)

    tsne = TSNE(**tsne_params)
    tsne_coords = tsne.fit_transform(param_data_scaled)

    # 4. 执行聚类 (K-means 或 DBSCAN)
    cluster_method = config.get('cluster_method', 'kmeans')
    logger.info(f"执行聚类分析，方法: {cluster_method}", operation="cluster_analysis_computation")

    # 初始化，确保在任何分支下都已定义
    centroids_scaled = np.array([]).reshape(0, param_data_scaled.shape[1])
    actual_cluster_ids = []

    if cluster_method == 'kmeans':
        n_clusters = config.get('n_clusters', 4)
        cluster_algo = KMeans(
            n_clusters=n_clusters,
            init='k-means++',
            n_init='auto',
            random_state=config.get('random_state', 42)
        )
        cluster_labels = cluster_algo.fit_predict(param_data_scaled)
        centroids_scaled = cluster_algo.cluster_centers_
        actual_cluster_ids = list(range(n_clusters))

    elif cluster_method == 'dbscan':
        eps = config.get('dbscan_eps', 0.5)
        min_samples = config.get('dbscan_min_samples', 5)
        cluster_algo = DBSCAN(eps=eps, min_samples=min_samples)
        cluster_labels = cluster_algo.fit_predict(param_data_scaled)
        # 获取实际找到的簇标签（排除噪声点-1）
        actual_cluster_ids = sorted([label for label in np.unique(cluster_labels) if label != -1])

        # 仅在找到簇时计算簇心
        if actual_cluster_ids:
            centroids_scaled = np.array([
                param_data_scaled[cluster_labels == i].mean(axis=0)
                for i in actual_cluster_ids
            ])
    else:
        raise ValueError(f"不支持的聚类方法: {cluster_method}")

    # 将聚类标签添加到DataFrame中，供后续所有统计计算使用
    final_points_df['cluster_label'] = cluster_labels
    n_clusters_found = len(actual_cluster_ids)

    # 5. 计算量化指标
    report = {'n_clusters_found': n_clusters_found}

    # 仅在找到有效聚类时，才计算后续的量化指标
    if n_clusters_found == 0:
        logger.warning("未找到任何有效聚类，跳过簇心和相关指标计算。", operation="cluster_analysis_computation")
        report['silhouette_score'] = None
        report['centroids'] = {}
        report['intra_cluster_variance'] = {}
        report['inter_cluster_distances'] = {}
        report['cluster_statistics'] = {}
    else:
        # 5.1 轮廓系数
        if n_clusters_found >= 2 and len(param_data_scaled) >= 3:
            # 轮廓系数不能在有噪声点(-1)时计算，先过滤
            valid_mask = cluster_labels != -1
            report['silhouette_score'] = silhouette_score(param_data_scaled[valid_mask], cluster_labels[valid_mask])
        else:
            report['silhouette_score'] = None

        # 5.2 簇心 (转换回原始尺度)
        centroids_original = scaler.inverse_transform(centroids_scaled)
        report['centroids'] = {
            f"cluster_{cluster_id}": dict(zip(param_columns, centroids_original[idx]))
            for idx, cluster_id in enumerate(actual_cluster_ids)
        }

        # 5.3 簇内方差和簇间距离
        intra_cluster_variance = {}
        for idx, cluster_id in enumerate(actual_cluster_ids):
            cluster_points = final_points_df[final_points_df['cluster_label'] == cluster_id][param_columns].values
            centroid = centroids_original[idx]
            variance = np.mean(np.sum((cluster_points - centroid)**2, axis=1))
            intra_cluster_variance[f"cluster_{cluster_id}"] = variance
        report['intra_cluster_variance'] = intra_cluster_variance

        inter_cluster_distances_df = pd.DataFrame(
            squareform(pdist(centroids_original)),
            columns=[f"cluster_{i}" for i in actual_cluster_ids],
            index=[f"cluster_{i}" for i in actual_cluster_ids]
        )
        report['inter_cluster_distances'] = inter_cluster_distances_df.to_dict()

        # 5.4 新增：详细的簇内统计
        cluster_statistics = {}
        for cluster_id in actual_cluster_ids:
            cluster_mask = (final_points_df['cluster_label'] == cluster_id)
            cluster_points_df = final_points_df.loc[cluster_mask, param_columns]

            if not cluster_points_df.empty:
                # 使用 .agg 高效计算多项统计指标
                stats_df = cluster_points_df.agg(['mean', 'std', 'min', 'max']).transpose()
                cluster_statistics[f"cluster_{cluster_id}"] = {
                    "n_points": len(cluster_points_df),
                    "parameter_summary": stats_df.to_dict('index')
                }
        report['cluster_statistics'] = cluster_statistics

    # 6. 构建最终结果
    result_df = final_points_df.copy()
    result_df['tsne_x'] = tsne_coords[:, 0]
    result_df['tsne_y'] = tsne_coords[:, 1]

    logger.info("聚类分析计算完成。")
    return result_df, report

def validate_tsne_config(config: Dict[str, Any]) -> None:
    """验证t-SNE配置参数的有效性。

    Args:
        config: t-SNE配置字典

    Raises:
        ValueError: 配置参数无效时抛出

    Note:
        此函数验证t-SNE算法参数的合理性，确保计算能够正常执行
    """
    # 验证perplexity
    perplexity = config.get('perplexity', 30)
    if not isinstance(perplexity, (int, float)) or perplexity <= 0:
        raise ValueError("perplexity 必须是正数")

    if perplexity > 100:
        logger.warning(
            "perplexity值较大，可能影响计算性能",
            perplexity=perplexity
        )

    # 验证learning_rate
    learning_rate = config.get('learning_rate', 200.0)
    if not isinstance(learning_rate, (int, float)) or learning_rate <= 0:
        raise ValueError("learning_rate 必须是正数")

    # 验证n_iter
    n_iter = config.get('n_iter', 1000)
    if not isinstance(n_iter, int) or n_iter <= 0:
        raise ValueError("n_iter 必须是正整数")

    if n_iter < 250:
        logger.warning(
            "n_iter值较小，可能影响收敛效果",
            n_iter=n_iter
        )

    # 验证init
    init = config.get('init', 'pca')
    if init not in ['pca', 'random']:
        raise ValueError("init 必须是 'pca' 或 'random'")

    # 验证random_state
    random_state = config.get('random_state')
    if random_state is not None and not isinstance(random_state, int):
        raise ValueError("random_state 必须是整数或None")

    logger.debug(
        "t-SNE配置验证通过",
        operation="tsne_computation",
        **config
    )
