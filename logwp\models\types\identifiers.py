from __future__ import annotations

"""logwp.models.types.identifiers - CIIA类型安全标识符

实现CIIA架构的类型安全标识符，提供大小写不敏感比较和原始格式保持。

Architecture
------------
层次/依赖: types层值对象，CIIA架构第二层（类型安全标识符）
设计原则: 值对象模式、不可变性、类型安全、语义明确
性能特征: 哈希优化、比较优化、内存友好

遵循CIIA规范：
- CIIA-1: 显示索引分离 - 保存原始和规范化两种格式
- CIIA-5: 类型安全 - 提供编译时类型检查
- CIIA-6: 格式无关 - 完全独立于具体数据格式

References
----------
- 《SCAPE_SAD_软件架构设计.md》§4.6 - CIIA架构设计
- .augment-guidelines CIIA规则 - CIIA-1, CIIA-5, CIIA-6
"""

from dataclasses import dataclass
from typing import Any

from logwp.models.utils.string_normalizer import WpStringNormalizer
from logwp.models.constants import WpNormalizationMode

__all__ = [
    "WpIdentifier",
    "WpWellIdentifier",
    "WpDatasetIdentifier",
    "WpCurveIdentifier",
    "WpAttributeIdentifier",
]


@dataclass(frozen=True)
class WpIdentifier:
    """测井数据标识符值对象（格式无关）。

    封装测井数据中的各种标识符，提供大小写不敏感比较和原始显示格式保持。
    实现CIIA架构的核心标识符抽象。

    Architecture
    ------------
    层次/依赖: types层值对象，CIIA架构第二层
    设计原则: 值对象模式、不可变性、类型安全
    性能特征: 哈希优化、比较优化、内存友好

    遵循CIIA规范：
    - CIIA-1: 显示索引分离 - original用于显示，normalized用于比较
    - CIIA-5: 类型安全 - 提供编译时类型检查和语义明确
    - CIIA-6: 格式无关 - 完全独立于具体数据格式

    Attributes:
        original: 原始字符串，保持原始大小写用于显示
        normalized: 规范化字符串，用于比较和索引

    Examples:
        >>> # 基本使用
        >>> well_id = WpIdentifier("C-1")
        >>> assert str(well_id) == "C-1"  # 显示原始格式
        >>>
        >>> # 大小写不敏感比较
        >>> id1 = WpIdentifier("OBMIQ_logs")
        >>> id2 = WpIdentifier("obmiq_LOGS")
        >>> assert id1 == id2  # True
        >>>
        >>> # 与字符串比较
        >>> assert id1 == "obmiq_logs"  # True
        >>>
        >>> # 哈希一致性
        >>> assert hash(id1) == hash(id2)

    References:
        《SCAPE_SAD_软件架构设计.md》§4.6.2 - 分层架构设计
    """

    original: str
    normalized: str

    def __init__(self, value: str, mode: WpNormalizationMode = WpNormalizationMode.UNICODE_CASEFOLD):
        """初始化标识符。

        Args:
            value: 原始标识符字符串
            mode: 规范化模式

        Raises:
            TypeError: 输入不是字符串时抛出
            ValueError: 输入为空字符串时抛出

        Examples:
            >>> identifier = WpIdentifier("OBMIQ_logs")
            >>> assert identifier.original == "OBMIQ_logs"
            >>> assert identifier.normalized == "obmiq_logs"
        """
        if not isinstance(value, str):
            raise TypeError(f"标识符必须是字符串，得到: {type(value).__name__}")

        if not value.strip():
            raise ValueError("标识符不能为空或仅包含空格")

        # 使用object.__setattr__绕过frozen限制
        object.__setattr__(self, 'original', value)
        object.__setattr__(self, 'normalized', WpStringNormalizer.normalize(value, mode))

    def __eq__(self, other: Any) -> bool:
        """大小写不敏感相等比较（CIIA-1规范）。

        Args:
            other: 比较对象，可以是WpIdentifier或字符串

        Returns:
            bool: 是否相等（大小写不敏感）

        Examples:
            >>> id1 = WpIdentifier("OBMIQ_logs")
            >>> id2 = WpIdentifier("obmiq_LOGS")
            >>> assert id1 == id2
            >>> assert id1 == "obmiq_logs"
            >>> assert id1 != "K_Label"
        """
        if isinstance(other, WpIdentifier):
            return self.normalized == other.normalized
        elif isinstance(other, str):
            return self.normalized == WpStringNormalizer.normalize(other)
        return False

    def __hash__(self) -> int:
        """基于规范化字符串的哈希（CIIA-1规范）。

        确保大小写不敏感的标识符具有相同的哈希值。

        Returns:
            int: 哈希值

        Examples:
            >>> id1 = WpIdentifier("OBMIQ_logs")
            >>> id2 = WpIdentifier("obmiq_LOGS")
            >>> assert hash(id1) == hash(id2)
        """
        return hash(self.normalized)

    def __str__(self) -> str:
        """显示时使用原始大小写（CIIA-1规范）。

        Returns:
            str: 原始格式的字符串

        Examples:
            >>> identifier = WpIdentifier("OBMIQ_logs")
            >>> assert str(identifier) == "OBMIQ_logs"
        """
        return self.original

    def __repr__(self) -> str:
        """调试表示。

        Returns:
            str: 调试字符串
        """
        return f"{self.__class__.__name__}('{self.original}')"

    def compare_with(self, other: str | WpIdentifier) -> bool:
        """显式的大小写不敏感比较方法。

        Args:
            other: 比较对象

        Returns:
            bool: 是否相等

        Examples:
            >>> identifier = WpIdentifier("OBMIQ_logs")
            >>> assert identifier.compare_with("obmiq_LOGS")
        """
        return self == other

    def get_normalized(self) -> str:
        """获取规范化字符串。

        Returns:
            str: 规范化后的字符串

        Examples:
            >>> identifier = WpIdentifier("OBMIQ_logs")
            >>> assert identifier.get_normalized() == "obmiq_logs"
        """
        return self.normalized

    def get_original(self) -> str:
        """获取原始字符串。

        Returns:
            str: 原始字符串

        Examples:
            >>> identifier = WpIdentifier("OBMIQ_logs")
            >>> assert identifier.get_original() == "OBMIQ_logs"
        """
        return self.original


class WpWellIdentifier(WpIdentifier):
    """井名标识符（CIIA特化类型）。

    专门用于井名的标识符类型，提供更强的语义和类型安全。

    Examples:
        >>> well_id = WpWellIdentifier("C-1")
        >>> assert isinstance(well_id, WpIdentifier)
        >>> assert str(well_id) == "C-1"
    """
    pass


class WpDatasetIdentifier(WpIdentifier):
    """数据集名称标识符（CIIA特化类型）。

    专门用于数据集名称的标识符类型，提供更强的语义和类型安全。

    Examples:
        >>> dataset_id = WpDatasetIdentifier("OBMIQ_logs")
        >>> assert isinstance(dataset_id, WpIdentifier)
        >>> assert str(dataset_id) == "OBMIQ_logs"
    """
    pass


class WpCurveIdentifier(WpIdentifier):
    """曲线名称标识符（CIIA特化类型）。

    专门用于曲线名称的标识符类型，提供更强的语义和类型安全。

    Examples:
        >>> curve_id = WpCurveIdentifier("PHIT_NMR")
        >>> assert isinstance(curve_id, WpIdentifier)
        >>> assert str(curve_id) == "PHIT_NMR"
    """
    pass


class WpAttributeIdentifier(WpIdentifier):
    """属性名称标识符（CIIA特化类型）。

    专门用于属性名称的标识符类型，提供更强的语义和类型安全。

    Examples:
        >>> attr_id = WpAttributeIdentifier("T2_AXIS")
        >>> assert isinstance(attr_id, WpIdentifier)
        >>> assert str(attr_id) == "T2_AXIS"
    """
    pass
