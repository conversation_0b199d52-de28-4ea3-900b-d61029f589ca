**巴西桑托斯深水碳酸盐岩渗透率计算(SCAPE)**方法说明书 Ver 4.0

# 0 背景

巴西桑托斯盆地是迄今全球深水领域石油资源最为富集地区，其盐下白垩系湖相碳酸盐岩储层埋深超过5000m，并遭受复杂的火山作用、成岩作用、构造作用叠加改造，因此储集层类型多样且非均质性极强，勘探开发面临高成本高风险。

巴西桑托斯深水碳酸盐岩渗透率计算方法命名为SCAPE Framework（Santos Carbonate Adaptive Permeability Estimator），SCAPE Framework包括两个阶段，即Stage-1和Stage-2：

1.  **Stage-1：**
    * **方法命名：** OBMIQ (Oil-Based Mud Invasion Quantifier)。
    * **核心目标：** 通过分析测井数据，精准预测油基泥浆侵入导致的`DT2_P50`和`DPHIT_NMR`变化，为Stage-2的渗透率计算提供关键的侵入校正因子。
    * **技术方案：** 考虑到传统机器学习模型难以有效处理`PHI_T2_DIST_CUM`（T2谱累积曲线）这类具有内在序列结构的高维数据，新版OBMIQ采用了一套先进的**混合输入深度学习模型 (Hybrid Input Model)**。该模型包含两个并行的处理分支，如同一个“专家会诊团队”：
        * **一维卷积神经网络 (1D-CNN) 分支：** 专门负责处理`PHI_T2_DIST_CUM`序列，通过卷积操作自动学习和提取曲线的形状、趋势等高级模式特征。
        * **多层感知机 (MLP) 分支：** 负责处理其他所有一维的常规测井曲线（如`GR`, `DEN`等），学习它们之间的非线性关系。
    * **工作机制：** 最终，两个分支提取的特征被融合在一起，共同做出对`DT2_P50`和`DPHIT_NMR`的精准预测。这一架构旨在最大化地利用不同类型数据的独特信息，从而提升模型的预测精度和泛化能力。

2.  **Stage-2：**
    * 完成渗透率建模评估和计算，包含两部分：
        * `FOSTER-NMR`核磁共振测井渗透率计算方程；
        * `SWIFT-PSO`参数优化流程。

# 1 数据

## 1.1 数据文件格式

详见`SCAPE_WFS_WP文件规范.md`。

## 1.2 数据收集概况

共收集到了4口井（井号分别为C-1、C-2、C-3和T-1）用于研究，主要用于建模的数据包括常规测井、随钻NMR、电缆NMR、岩心渗透率、MDT渗透率和生产测井中的产出剖面（PLT），下文用WL指代电缆测井、用LWD指代随钻测井。

研究目的层的数据概况见下表（统计的是样点数）：

| 井号  | 常规测井(含LWD和WL) | 随钻NMR | 电缆NMR | 岩心渗透率 | MDT渗透率 | PLT |
| --- | ------------- | ----- | ----- | ----- | ------ | --- |
| C-1 | 910           | 910   | 910   | 56    | 30     | 9层  |
| C-2 | 1748          |       | 1748  | 150   | 92     |     |
| C-3 | 10454         |       | 10454 |       | 48     |     |
| T-1 | 1543          | 1543  | 1543  | 46    | 31     |     |

wp文件名为santos_data.wp.xlsx，其中包含以下数据集：
- Logs数据集：常规测井、随钻NMR和电缆NMR数据，Continuous类型。
- K_Label数据集：岩心渗透率和MDT渗透率，Point类型。
- PLT数据集：C-1井的PLT解释结果，Interval类型，用于盲井验证。
- K_Val数据集：T-1井的岩心渗透率（CT分析结果），Point类型，用于盲井验证。

## 1.3 曲线名称和含义

收集到的数据中的测井曲线包括：

| 曲线名               | 单位    | 测井类型           | 曲线维数 | 含义                       |     |
| ----------------- | ----- | -------------- | ---- | ------------------------ | --- |
| WELL_NO           |       |                | 1    | 井名                       |     |
| MD                | m     |                | 1    | 深度索引                     |     |
| GR                | gAPI  | WL常规测井         | 1    | 自然伽马                     |     |
| BS                | in    | WL常规测井         | 1    | 钻头尺寸                     |     |
| CAL               | in    | WL常规测井         | 1    | 井径                       |     |
| DEN               | g/c3  | WL常规测井         | 1    | 密度                       |     |
| CN                | v/v   | WL常规测井         | 1    | 中子                       |     |
| DT                | us/ft | WL常规测井         | 1    | 声波时差                     |     |
| RD                | ohm.m | WL常规测井         | 1    | 深电阻率                     |     |
| RS                | ohm.m | WL常规测井         | 1    | 浅电阻率                     |     |
| RD_LOG10          |       | WL常规测井         | 1    | 深电阻率对数                   |     |
| RS_LOG10          |       | WL常规测井         | 1    | 浅电阻率参数                   |     |
| DRES              |       | WL常规测井         | 1    | 深浅电阻率对数差值                |     |
| PE                | b/e   | WL常规测井         | 1    | PE曲线                     |     |
| RD_LWD            | ohm.m | LWD常规测井        | 1    | 深电阻率（随钻）                 |     |
| RS_LWD            | ohm.m | LWD常规测井        | 1    | 浅电阻率（随钻）                 |     |
| DRES_LWD          | ohm.m | LWD常规测井        | 1    | 深浅电阻率对数差值（随钻）            |     |
| PHIT_NMR          | v/v   | WL NMR         | 1    | NMR总孔隙度                  |     |
| T2LM              | ms    | WL NMR         | 1    | T2谱对数平均值                 |     |
| T2LM_LONG         | ms    | WL NMR         | 1    | T2谱可动流体部分对数平均值           |     |
| T2CUTOFF          | ms    | WL NMR         | 1    | T2截止值                    |     |
| CBW_NMR           | v/v   | WL NMR         | 1    | NMR粘土束缚水孔隙度              |     |
| BFV_NMR           | v/v   | WL NMR         | 1    | NMR总束缚流体孔隙度              |     |
| BVI_NMR           | v/v   | WL NMR         | 1    | NMR毛管束缚水孔隙度              |     |
| PHIE_NMR          | v/v   | WL NMR         | 1    | NMR有效孔隙度                 |     |
| SWB_NMR           | v/v   | WL NMR         | 1    | NMR总束缚水饱和度               |     |
| SWI_NMR           | v/v   | WL NMR         | 1    | NMR毛管束缚水饱和度              |     |
| LT2STDDEV_FFI     |       | WL NMR         | 1    | T2谱可动流体部分对数标准差           |     |
| LSKEW_FFI         |       | WL NMR         | 1    | T2谱可动流体部分对数偏度            |     |
| LKURT_FFI         |       | WL NMR         | 1    | T2谱可动流体部分对数峰度            |     |
| T2_P20            | ms    | WL NMR         | 1    | 累积T2谱曲线20%分位             |     |
| T2_P50            | ms    | WL NMR         | 1    | 累积T2谱曲线50%分位             |     |
| PHI_T2_DIST       | v/v   | WL NMR         | 2    | T2谱Bin对应的孔隙度数组           |     |
| PHI_T2_DIST_CUM   | v/v   | WL NMR         | 2    | 累积T2谱曲线Bin对应的累计孔隙度数组     |     |
| T2_TIME           | ms    | WL NMR         | 2    | T2谱Bin时间数组               |     |
| SDR_PROXY         |       | WL NMR         | 1    | 类SDR公式项：PHIT_NMR*T2LM    |     |
| PHIT_NMR_LWD      | v/v   | LWD NMR        | 1    | NMR总孔隙度（随钻）              |     |
| T2LM_LWD          | ms    | LWD NMR        | 1    | T2谱对数平均值（随钻）             |     |
| T2LM_LONG_LWD     | ms    | LWD NMR        | 1    | T2谱可动流体部分对数平均值（随钻）       |     |
| T2CUTOFF_LWD      | ms    | LWD NMR        | 1    | T2截止值（随钻）                |     |
| CBW_NMR_LWD       | v/v   | LWD NMR        | 1    | NMR粘土束缚水孔隙度（随钻）          |     |
| BFV_NMR_LWD       | v/v   | LWD NMR        | 1    | NMR总束缚流体孔隙度（随钻）          |     |
| BVI_NMR_LWD       | v/v   | LWD NMR        | 1    | NMR毛管束缚水孔隙度（随钻）          |     |
| PHIE_NMR_LWD      | v/v   | LWD NMR        | 1    | NMR有效孔隙度（随钻）             |     |
| SWB_NMR_LWD       | v/v   | LWD NMR        | 1    | NMR总束缚水饱和度（随钻）           |     |
| SWI_NMR_LWD       | v/v   | LWD NMR        | 1    | NMR毛管束缚水饱和度（随钻）          |     |
| LT2STDDEV_FFI_LWD |       | LWD NMR        | 1    | T2谱可动流体部分对数标准差（随钻）       |     |
| LSKEW_FFI_LWD     |       | LWD NMR        | 1    | T2谱可动流体部分对数偏度（随钻）        |     |
| LKURT_FFI_LWD     |       | LWD NMR        | 1    | T2谱可动流体部分对数峰度（随钻）        |     |
| T2_P20_LWD        | ms    | LWD NMR        | 1    | 累积T2谱曲线20%分位（随钻）         |     |
| T2_P50_LWD        | ms    | LWD NMR        | 1    | 累积T2谱曲线50%分位（随钻）         |     |
| PHI_T2_DIST_LWD   | v/v   | LWD NMR        | 2    | T2谱Bin对应的孔隙度数组（随钻）       |     |
| T2_TIME_LWD       | ms    | LWD NMR        | 2    | T2谱Bin时间数组（随钻）           |     |
| DT2LM             | ms    | WL NMR与LWD NMR | 1    | 电缆与随钻T2谱对数平均值之差          |     |
| DT2LM_LONG        | ms    | WL NMR与LWD NMR | 1    | 电缆与随钻T2谱可动流体部分对数平均值之差    |     |
| DT2_P50           | ms    | WL NMR与LWD NMR | 1    | 电缆与随钻累积T2谱曲线50%分位之差      |     |
| DT2_P20           | ms    | WL NMR与LWD NMR | 1    | 电缆与随钻累积T2谱曲线20%分位之差      |     |
| DPHIT_NMR         | v/v   | WL NMR与LWD NMR | 1    | 电缆与随钻NMR总孔隙度之差           |     |
| DPHIE_NMR         | v/v   | WL NMR与LWD NMR | 1    | 电缆与随钻NMR有效孔隙度之差          |     |
| K_LABEL_TYPE      |       | 岩心/MDT渗透率      | 1    | 渗透率来源，CORE为岩心，MDT为电缆地层测试 |     |
| K_LABEL           | mD    | 岩心/MDT渗透率      | 1    | 渗透率                      |     |
| PZI               |       |                | 1    | 产层指示，1为产层，0为非产层          |     |
| QOZI              | b/d   | PLT            | 1    | PLT层段油产量                 |     |

## 1.4 名称/符号规则

对于下文中符号形式需要注意有两点：

- 同一物理意义的量，在测井曲线名称中全部采用大写，在公式中根据其惯用表达可以大写或小写，比如核磁共振测井渗透率的测井曲线名称用K_NMR表示，在公式中用表示$k_{NMR}$。
- 公式中的同一参数有两种表示形式：LaTex符号或字符串名称，两者是等效的，字符串名称根据惯用法可以是全部大写或是首字母大小形式。

# Stage-1

## 2. OBMIQ建模：基于混合输入深度学习模型

### 2.0 背景与方法革新

OBMIQ（Oil-Based Mud Invasion Quantifier）阶段的核心任务不变：通过分析测井数据，精准地量化油基泥浆侵入对储层造成的影响，具体体现为预测`DT2_P50`（T2谱中位值的变化,为电缆测井和随钻测井测量值的对数差）和`DPHIT_NMR`（总孔隙度的变化，为电缆测井和随钻测井测量值的差）这两个关键指标。

传统的机器学习方法（如随机森林、XGBoost）在处理此类问题时，通常需要将所有不同类型的测井曲线“拍平”成一个二维表格。这种做法虽然简单，但存在一个根本性的缺陷：它无法区分和尊重不同数据的内在结构。特别是对于像`PHI_T2_DIST_CUM`（T2谱累积曲线）这样的二维序列数据，其最有价值的“形状”信息在“拍平”过程中被严重破坏了。

为了克服这一限制，我们引入了一套全新的、基于深度学习的**混合输入模型 (Hybrid Input Model)**。该方法的核心思想是为不同结构的数据配备不同的“专家”处理模块，让模型能够更智能、更深刻地理解数据，从而做出更精准的预测。

### 2.1 核心思想：为何采用混合输入模型？

您可以将传统的机器学习模型想象成一个“通才”医生，他试图用同一套诊断方法（如听诊、问询）来分析所有病症。而混合输入模型则像一个**专家会诊团队**。

在这个团队里，我们有两位专家：

1.  **序列分析专家 (1D-CNN):** 这位专家专门负责“阅读”像心电图、脑电波这样的**序列图谱**。对于我们的问题，他专门分析`PHI_T2_DIST_CUM`曲线。他不会只看曲线上的某一个孤立的点，而是通过一个“滑动的识别窗口”来审视整条曲线的**形态、趋势和局部模式**。

2.  **常规指标专家 (MLP):** 这位专家擅长分析离散的、表格化的常规指标，比如病人的年龄、体重、血压等。对应到我们的问题，他负责分析`GR`、`DEN`、`DT`等所有一维的测井曲线。

**会诊流程：**
两位专家各自独立完成分析，并给出自己的专业意见。然后，他们在一个“会诊室”（融合层）里汇合，将各自的发现结合起来，进行最终的综合诊断，从而得出最可靠的结论。

通过这种方式，我们确保了`PHI_T2_DIST_CUM`曲线宝贵的形状信息得到了最充分的利用，而不是被当作一堆孤立的数字对待。

### 2.2 为何选择1D-CNN：正确的“阅读”工具

卷积神经网络（CNN）是深度学习中用于自动提取特征的强大工具。但根据数据形态的不同，它有不同的版本。

* **2D-CNN (二维CNN):** 这是最广为人知的CNN，主要用于**图像识别**。它的“识别窗口”（卷积核）是二维的，可以在图像的高度和宽度两个方向上滑动，以识别物体的边缘、纹理和形状。
* **3D-CNN (三维CNN):** 用于处理三维数据，如**医学CT扫描**或视频中的动作识别。
* **1D-CNN (一维CNN):** 这是专门为处理**序列数据**而设计的。它的“识别窗口”是一维的，只能沿着序列的方向（如时间或深度）滑动。

对于`PHI_T2_DIST_CUM`这样一条代表孔隙度随T2时间累积的曲线，它本质上是一个**一维序列**。我们最关心的是它沿着T2时间轴的**局部形状模式**，例如：
* 曲线在哪个区间**斜率突然变大**（对应原始谱的一个峰值）？
* 曲线是否出现了**平坦的“台阶”**（对应谱中的一个空白区）？

**1D-CNN**正是捕捉这些一维局部模式的完美工具。而2D-CNN则会错误地试图在T2时间和另一个不存在的维度上寻找“二维纹理”，这完全不符合我们数据的物理本质。因此，选择1D-CNN是唯一正确且符合物理直觉的方案。

### 2.3 数据准备：为专家团队提供“定制化”的资料

为了让两位“专家”能最高效地工作，我们需要为它们准备两种不同格式的数据。

#### a. 序列输入 (为1D-CNN专家准备)

1.  **核心数据：** `PHI_T2_DIST_CUM` 曲线（假设为64个点）。
2.  **关键预处理 - 逐样本归一化 (Per-Sample Normalization):**
    * **目的：** 分离曲线的“形状”与“大小”信息。`PHI_T2_DIST_CUM`曲线本身同时包含了孔径分布的**形状**和总孔隙度**大小**（其最大值等于`PHIT_NMR`）两种信息。我们将它们解耦，让模型可以独立学习。
    * **方法：** 对于每一个深度点的`PHI_T2_DIST_CUM`曲线，都用该深度点的总孔隙度`PHIT_NMR`去除它。
        `Normalized_CUM[i] = PHI_T2_DIST_CUM[i] / PHIT_NMR[i]`
    * **结果：** 得到一个纯粹的、数值在[0, 1]之间的“形状因子”曲线。这个处理后的序列将作为1D-CNN的输入，其最终形状是一个三维张量：`(样本数, 64, 1)`。

#### b. 表格输入 (为MLP专家准备)

1.  **核心数据：** 所有其他一维测井曲线。
2.  **特征工程与筛选：**
    * **创建交互特征：** 创建如`SDR_Proxy = PHIT_NMR * T2LM_LONG`这样的高级特征，注入物理先验知识。
    * **手动筛选：** 使用`LogScout`工具包进行分析，基于VIF和相关性矩阵，手动剔除高度冗余的特征（如在多个孔隙度曲线中只保留`PHIT_NMR`），以增强模型稳定性和可解释性。
3.  **预处理：**
    * **缺失值填充：** 用户保证输入数据中无缺失值。
    * **特征标准化 (Feature Scaling):** 使用`StandardScaler`（Z-score标准化）对所有最终入选的表格特征进行缩放，使其均值为0，标准差为1。
    * **结果：** 得到一个标准的二维矩阵，形状为：`(样本数, 一维特征数)`。

**注意：** 准化的统计量（中位数、均值、标准差）**必须**在交叉验证的每一折中，仅使用训练集计算，然后应用到对应的验证集上，以防数据泄露。

#### c. 序列重采样策略 (Sequence Resampling Strategy)
为确保模型在训练和应用中的一致性与泛化能力，我们采用一套严格的序列处理策略。

1.  **“标准T2轴”的确立与保存:**
    * **前提：** 本项目所有用于建模的井（C-1, C-2等）均共享一个**一致的T2时间轴**。
    * **操作：** 这个来自训练数据的、统一的T2轴将被定义为**“标准T2轴”**。它作为模型的关键元数据，定义了CNN分支所期望的输入维度和坐标系，必须与最终的模型权重、预处理器等产物一同被妥善保存。

2.  **训练阶段无需重采样:**
    * 由于所有训练和交叉验证数据的T2轴均与“标准T2轴”一致，因此在**整个模型训练和超参数寻优阶段，无需进行任何序列重采样**。模型可以直接处理这些维度一致的序列数据，这保证了训练过程的高效性。

3.  **应用阶段的预处理：执行重采样:**
    * **时机：** 序列重采样是**模型应用（或推断）阶段**的一个关键预处理步骤。
    * **过程：** 当模型需要应用到一口新井时，必须同时提供该井的T2谱（累积）曲线数据及其对应的**“源T2轴”**。应用流程将执行以下操作：
        * 使用**对数空间线性插值**的方法，将新井的T2谱曲线从其“源T2轴”精准地重采样到模型保存的那个“标准T2轴”上。
        * 经过重采样后，新井的序列数据便具有了模型期望的标准长度（如64个点），可以被送入CNN分支进行预测。

4.  **记录映射关系用于解释性分析:**
    * 在应用阶段的重采样过程中，需要记录并保存从新井的“源T2轴”到“标准T2轴”的映射关系。这一步骤对于后续的模型解释性至关重要，它能确保我们通过Grad-CAM等方法计算出的“模型注意力”热力图，可以被准确地反投影回新井原始的、具有物理意义的T2时间轴上，从而进行有效的地质解释。

### 2.4 模型架构：专家团队的协同工作流程

我们采用一个“多分支输入，单一输出”的神经网络架构。该架构由四个逻辑部分组成：

1.  **分支A - 序列处理模块 (1D-CNN):**
    * 该模块接收经过归一化的`PHI_T2_DIST_CUM`序列数据。
    * 其内部由一到多个一维卷积层（`Conv1D`）构成。这些卷积层像探照灯一样，在64个点的序列上滑动，自动学习和识别出有意义的局部形状特征（例如，斜率的突然变化代表了某一孔径区间的富集）。
    * 随后，一个最大池化层（`MaxPooling1D`）会对提取出的特征进行筛选，只保留最显著的模式。
    * 最后，一个**全局平均池化层 (`GlobalAveragePooling1D`)** 会将所有提取出的模式进行平均，形成一个高度浓缩且参数量更少的一维特征向量，这有助于防止过拟合，并作为该专家的‘分析报告’。

2.  **分支B - 表格处理模块 (MLP):**
    * 该模块接收经过标准化的所有一维测井曲线数据。
    *   其内部被设计为一个包含两个隐藏层的深度网络，以学习更复杂的特征交互。每个隐藏层都采用了`Linear -> BatchNorm1d -> ReLU -> Dropout`的稳健结构，以增强模型的表达能力、稳定训练过程并有效防止过拟合。
    * 该模块的输出也是一个浓缩的一维特征向量，是这位专家的“分析报告”。

3.  **融合模块 (Fusion):**
    * 这是两位专家的“会诊室”。它通过一个拼接层（`Concatenate`）将来自CNN分支和MLP分支的两个特征向量“拼接”在一起。
    * 这样就形成了一个更长、信息更全面的融合特征向量，它同时包含了对T2谱形状的深刻理解和对其他常规测井指标的综合分析。

4.  **回归输出模块 (Regression Head):**
    * 该模块负责对融合后的信息进行最终的整合与权衡。它接收来自融合模块的拼接向量，并按以下顺序进行处理：首先通过一个**批标准化层 (`Batch Normalization`)** 来稳定数据分布，加速训练；然后通过一个**Dropout层**来随机丢弃部分神经元，以增强模型的泛化能力；最后再连接一到多个全连接层（`Dense`）。最后一层是一个不带激活函数的、包含两个神经元的输出层，这两个神经元分别输出对`DT2_P50`和`DPHIT_NMR`的最终预测值。

整个模型将使用高效的`Adam`优化器进行训练。

**关于损失函数的重要补充：自适应损失加权** 考虑到两个预测目标`DT2_P50`和`DPHIT_NMR`的数值范围和方差可能存在巨大差异，直接使用标准的均方误差（MSE）可能会导致损失被误差较大的目标所主导。为解决此问题，模型将采用**自适应损失加权**机制。该机制通过引入两个可学习的权重参数，让模型在训练过程中自动学习如何平衡两个任务的损失，确保两个目标都能得到充分的优化，从而提升多任务学习的整体性能。

### 2.5 训练与验证策略

#### a. 留一井交叉验证 (LOWO-CV) 框架
为了获得对模型泛化能力的无偏估计，我们依然采用**LOWO-CV作为最外层的验证框架**。在每一折中，一口井作为测试集，其余井作为训练集。

#### b. 折内训练与验证
在每一个LOWO折的内部，我们执行以下操作：

1.  **数据划分：** 将当前折的训练集再划分为**训练子集 (sub-train)** 和 **验证子集 (validation)**，例如按80/20的比例。
2.  **模型训练：**
    * 在**训练子集**上训练混合输入模型。
    * 使用**验证子集**来监控训练过程。
    * 引入**早停机制 (Early Stopping)**：这是防止深度学习模型过拟合的关键手段。我们会监控模型在验证集上的损失（loss），如果验证损失连续多个轮次（Epochs）不再下降，就自动停止训练，并**通过设置`restore_best_weights=True`，确保最终得到的是那个验证损失最低点的模型权重**。
3.  **超参数寻优 (Hyperparameter Tuning):**
    * 使用`KerasTuner`等自动化工具，在**每一折**LOWO交叉验证的训练集内部，进行独立的超参数搜索，并由该折的验证集评估性能。最终，通过聚合所有折的结果，来确定全局最优的超参数组合。

### 2.6 最终模型交付

1.  **性能评估：** LOWO-CV流程结束后，我们将得到模型在不同测试井上的性能指标（如RMSE），计算其均值和标准差，作为模型最终泛化能力的评估。
2.  **最终模型训练：**
    * 使用在交叉验证中找到的**最佳超参数组合**。
    * 在**全部建模数据**（C-1和C-2井等）上重新训练一个最终的模型。同样，可以从全部数据中分出一个小的验证集（如10%）用于早停。

通过这一套全新的、基于深度学习的流程，`OBMIQ`模型将能更充分地挖掘`PHI_T2_DIST_CUM`曲线中的结构化信息，有望在预测精度和泛化能力上实现质的飞跃。

### 2.7 编程实现的关键细节与配置指南

本节为`OBMIQ`混合输入模型的编程实现提供一份完整的技术规范，旨在作为AI编程助手的核心开发文档。整个流程被设计为高度自动化和可配置的，以确保实验的灵活性、可复现性和标准化。

---

#### **一、 推荐技术栈**

为充分利用GPU加速并采用现代深度学习实践，推荐使用以下核心库：

* **深度学习框架:** `TensorFlow` (版本 2.10+) with `Keras`。Keras的函数式API（Functional API）是构建多分支输入模型的理想选择。
* **超参数寻优:** `KerasTuner`。它与Keras无缝集成，提供了高效的自动化超参数搜索算法（如Hyperband）。
* **数据处理与分析:** `Pandas`, `NumPy`, `Scikit-learn`。
* **核心依赖库:** `Statsmodels` (用于VIF计算), `Seaborn` & `Matplotlib` (用于可视化)。

---

#### **二、 整体工作流程**

建模过程遵循一个严谨的两阶段工作流：

* **阶段一：交叉验证与超参数寻优 (Cross-Validation & Hyperparameter Tuning)**
    * **目标：** 通过稳健的留一井交叉验证（LOWO-CV）框架，客观地评估模型架构的泛化能力，并自动寻找一组最优的超参数组合。
    * **过程：** 以井为单位进行循环验证。在每一折中，使用`KerasTuner`等工具在训练集上进行超参数搜索，并由独立的验证集来评估每组参数的性能。最终，根据所有折的平均验证性能，确定全局最佳的超参数。

* **阶段二：最终模型训练与交付 (Final Model Training & Deployment)**
    * **目标：** 使用在阶段一中找到的最佳超参数，在全部可用的建模数据上训练一个最终的、用于生产部署的模型。
    * **过程：** 将所有建模井的数据合并，进行一次性的训练。同样会利用早停机制来防止过拟合，并保存性能最佳的模型及其相关的训练产物。
    * **最终评估：** 在模型训练完成后，使用该模型对**全部建模数据**进行一次预测，并基于预测结果和真实值，生成一套完整的性能评估图件（如交会图、残差图等），作为最终产物的一部分。

---

#### **三、 配置参数详解**

所有参数应通过单一方式进行管理。

##### **a. 输入/输出配置 (Input/Output Configuration)**

| 参数名 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| `data_source.filepath` | `string` | 指向`.wp.xlsx`数据文件的路径。 | `'./data/santos_data.wp.xlsx'` |
| `data_source.dataset_name` | `string` | 数据集在Excel文件中的表单名称。 | `'OBMIQ_logs'` |
| `feature_mapping.sequence_feature` | `string` | 作为1D-CNN输入的二维序列特征名称。 | `'PHI_T2_DIST_CUM'` |
| `feature_mapping.sequence_length` | `int` | 二维序列的固定长度（维度）。 | `64` |
| `feature_mapping.normalization_feature` | `string` | 用于对序列进行逐样本归一化的特征。 | `'PHIT_NMR'` |
| `feature_mapping.tabular_features` | `list[string]` | 所有最终入选的一维表格特征列表。 | `['GR', 'DEN', 'DT', 'SDR_Proxy']` |
| `feature_mapping.target_features` | `list[string]` | 两个目标特征的名称列表。 | `['DT2_P50', 'DPHIT_NMR']` |
| `feature_mapping.group_column` | `string` | 用于LOWO-CV分组依据的列名。 | `'WELL_NO'` |
| `output_path.main_directory` | `string` | 所有产物的根输出目录。 | `'./OBMIQ_run_01/'` |

##### **b. 用户可调超参数 (User-Tunable Hyperparameters)**

这些参数定义了超参数搜索的空间，用户可以根据需求进行调整。

| 参数名 | 类型 | 描述 | 示例 |
| :--- | :--- | :--- | :--- |
| `tuning.hyperband_iterations` | `int` | `KerasTuner`中Hyperband算法的迭代次数。 | `2` |
| `tuning.max_epochs_per_trial` | `int` | 每个超参数组合尝试训练的最大轮次。 | `50` |
| `tuning.cv_method` | `string` | 交叉验证方法，可设为`'lowo'`。 | `'lowo'` |
| `tuning.val_split_ratio` | `float` | 在每个CV折内部，用于划分训练/验证集的比例。 | `0.2` |
| `model_params.cnn_filters` | `list[int]` | CNN卷积核数量的搜索范围。 | `[16, 32, 64]` |
| `model_params.cnn_kernel_size` | `list[int]` | CNN卷积核尺寸的搜索范围。 | `[3, 5]` |
| `model_params.mlp_units_1` | `list[int]` | MLP第一个隐藏层神经元数量的搜索范围。 | `[16, 32, 64]` |
| `model_params.mlp_units_2` | `list[int]` | MLP第二个隐藏层神经元数量的搜索范围。 | `[8, 16, 32]` |
| `model_params.dropout_rate` | `list[float]` | Dropout比率的搜索范围。 | `[0.1, 0.25, 0.5]` |
| `training_params.learning_rate` | `list[float]` | Adam优化器学习率的搜索范围。 | `[1e-3, 5e-4, 1e-4]` |
| `training_params.batch_size` | `list[int]` | 批处理大小的搜索范围。 | `[32, 64]` |

##### **c. 内部固定参数 (Internal Fixed Parameters)**

这些参数在建模流程中固定使用，通常不需用户修改。

| 参数名 | 类型 | 描述 | 固定值 |
| :--- | :--- | :--- | :--- |
| `internal.loss_function` | `string` | 损失函数。 | `'mean_squared_error'` |
| `internal.metrics` | `list[string]` | 评估指标列表。 | `['root_mean_squared_error']` |
| `internal.early_stopping.monitor` | `string` | 早停机制监控的指标。 | `'val_loss'` |
| `internal.early_stopping.patience` | `int` | 早停机制的耐心轮次数。 | `10` |
| `internal.reproducibility.seed` | `int` | 全局随机种子，确保结果可复现。 | `2025` |

---

#### **四、 核心实现细节**

* **模型架构实现 (Model Architecture Implementation):**
    * **构建函数:** 必须创建一个模型构建函数，例如 `build_model(hp, ...)`，该函数接收一个`KerasTuner`的`HyperParameters`对象`hp`以及相关配置作为输入，并返回一个编译好的Keras模型。这是与`KerasTuner`集成的关键。
    * **输入层:** 使用Keras函数式API，定义**三个**独立的`Input`层：
        * `sequence_input = Input(shape=(sequence_length, 1), name='sequence_input')`：用于接收T2谱累积曲线序列。
        * `tabular_input = Input(shape=(num_tabular_features,), name='tabular_input')`：用于接收一维表格化测井曲线。
        * `y_true_input = Input(shape=(num_targets,), name='y_true_input')`：这是一个特殊的技术性输入，用于将真实的目标值传入模型内部，以便计算自适应损失。
    * **CNN分支:**
        * 在该分支上，至少连接一个`Conv1D`层,将层数作为超参数进行搜索( `model_params.num_cnn_layers`,搜索空间`[1, 2]` )。其`filters`和`kernel_size`等超参数应通过`hp`对象从配置中定义的搜索空间中获取。
        * `Conv1D`层后接一个`GlobalAveragePooling1D`层。与`Flatten`层相比，`GlobalAveragePooling1D`能更有效地将序列信息浓缩为一个特征向量，同时大幅减少模型参数量，是防止在小样本上过拟合的有效手段。
    * **MLP分支:**
        * MLP分支被固定为两层结构，以提供足够的深度来学习复杂的特征交互。
        * 每一层的神经元数量（`mlp_units_1`, `mlp_units_2`）作为独立的超参数进行搜索。
    * **融合层:**
        * 使用`Concatenate()`层将CNN分支和MLP分支的输出向量拼接在一起，形成一个融合特征向量。
    * **回归头:**
        * 在融合向量后，采用`BatchNormalization -> Dropout -> Dense`的稳健结构。`BatchNormalization`层用于稳定梯度，加速训练；`Dropout`层（其`rate`由`hp`提供）用于防止过拟合。
        * 最终的预测输出层是`Dense(2, name='output_pred')`，不带激活函数。
    * **自适应损失加权:**
        * 为了处理多任务学习中的目标不平衡问题，模型将包含一个自定义的`AdaptiveLossWrapper`层。该层接收`y_true_input`（真实值）和`output_pred`（预测值）作为输入，计算出自适应加权的损失值。这个损失值将作为模型的第二个输出`loss_output`。
    * **最终模型定义与编译:**
        * 最终模型的输入是`[sequence_input, tabular_input, y_true_input]`，输出是`[output_pred, loss_output]`。
        * 通过`model.add_loss(loss_output)`方法，将自定义的损失作为模型需要优化的总损失。
        * 在`model.compile()`时，由于总损失已经通过`add_loss`定义，我们可以为原始输出设置虚拟损失`loss=[None, None]`。优化器的学习率`learning_rate`应由`hp`对象提供。

* **早停机制 (Early Stopping):**
    * 在**阶段一**的每次超参数尝试和**阶段二**的最终训练中，都必须使用`EarlyStopping`回调。
    * 关键配置为`restore_best_weights=True`。这能确保在训练停止后，模型自动恢复到其在验证集上性能最佳的那个轮次的状态，而不是停在最后一个轮次的状态。

* **超参数寻优 (Hyperparameter Tuning):**
    * **工具:** `KerasTuner`，推荐使用`Hyperband`算法。
    * **实现逻辑:** 在LOWO-CV的循环内部，针对每一折的训练/验证数据，实例化并运行一个`KerasTuner.Hyperband(build_model, ...)`对象。
    * **结果聚合:** 在所有CV折完成后，聚合每一折中最佳模型的验证分数（如`val_rmse`）。最终，选择在所有折中**平均验证分数**最好的那一组超参数，作为全局最佳配置。

* **性能评估指标 (Performance Metrics):**
    * **主要指标：** `Root Mean Squared Error (RMSE)`。
    * **辅助指标：** `Mean Absolute Error (MAE)` 和 `R-squared (R²)`。
    * **报告形式：** 在交叉验证结束后，应生成一个DataFrame，其中每一行代表一折CV，列包含该折的`RMSE`, `MAE`, `R²`等指标，最后一行是所有折的平均值和标准差。

* **最终模型训练 (Final Model Training):**
    * **模型重建：** 首先，利用`KerasTuner`的`get_best_hyperparameters()`方法获取在阶段一中找到的最佳超参数对象。然后，基于该对象的`.values`属性，调用模型构建函数（`build_model`）来创建一个全新的、结构最优且未经训练的模型实例。
    * 将**所有**建模井的数据合并。从这个完整数据集中，按`val_split_ratio`（例如10%）随机分出一小部分作为**仅用于早停的验证集**。
    * 在剩余的90%数据上进行训练，并由10%的验证集监控，启用`EarlyStopping`回调。

---

#### **五、 标准产物列表 (Standard Artifacts List)**

整个`OBMIQ`工作流程结束后，应在`output_path.main_directory`下生成以下标准化的产物。这份清单整合了所有必需的报告、模型文件以及用于分析和评估的关键图件。

---

* **1. 配置文件快照 :**
    * **描述：** 完整保存本次运行所使用的全部配置，用于复现和追溯。

* **2. 预处理对象 (`preprocessors.pkl`):**
    * **描述：** 一个pickle文件，其中包含在**全部数据**上`fit`好的`StandardScaler`对象和缺失值填充的中位数。这对于在未来新井上应用模型至关重要。

* **3. 超参数寻优产物 (Hyperparameter Tuning Artifacts):**
    * **`hyperparameter_tuning_report.csv`**:
        * **描述：** 一个CSV文件，详细记录`KerasTuner`的搜索过程。每一行代表一个超参数组合尝试，列包含该组合的具体参数值及其在所有CV折上的平均验证分数。
    * **`tuning_score_vs_trial.png`**:
        * **描述：** “验证分数 vs. 试验次数图”。用于监控超参数搜索过程的收敛性。
    * **`tuning_parallel_coordinates.png`**:
        * **描述：** “平行坐标图”。用于直观地发现“优胜”超参数组合的模式。

* **4. 交叉验证产物 (Cross-Validation Artifacts):**
    * **`cv_performance_report.csv`**:
        * **描述：** 一个CSV文件，总结了模型在每一折LOWO-CV上的详细性能。每一行代表一折交叉验证，列应包含该折的性能指标（RMSE, MAE, R²）以及在该折上找到的最佳超参数组合。报告的最后一行应提供所有折性能指标的**平均值**和**标准差**。

* **5. 最终模型产物 (Final Model Artifacts):**
    * **`final_model.keras`**:
        * **描述：** 使用`.keras`格式保存的、训练好的最终模型。该格式同时包含了模型架构、权重和优化器状态。
    * **`final_model_training_history.png`**:
        * **描述：** “训练与验证的损失/指标曲线图”。绘制了最终模型在训练过程中的训练损失（`loss`）和验证损失（`val_loss`）随轮次（Epoch）变化的曲线，用于诊断过拟合。
    * **`final_model_evaluation_plots/`** (子目录):
        * **`crossplot_DT2_P50.png`**: `DT2_P50`的“预测值 vs. 真实值交会图”。
        * **`crossplot_DPHIT_NMR.png`**: `DPHIT_NMR`的“预测值 vs. 真实值交会图”。
        * **`residuals_plot_DT2_P50.png`**: `DT2_P50`的“残差图”。
        * **`residuals_plot_DPHIT_NMR.png`**: `DPHIT_NMR`的“残差图”。
        * **`residuals_hist_DT2_P50.png`**: `DT2_P50`的“残差分布直方图”。
        * **`residuals_hist_DPHIT_NMR.png`**: `DPHIT_NMR`的“残差分布直方图”。
* **6. 模型解释性产物 (Model Interpretability Artifacts)**:
	* **`shap_summary_plot.png`**:
	    - **描述：** 针对最终模型**MLP分支**的SHAP（SHapley Additive exPlanations）分析摘要图。该图展示了各个一维表格特征对模型输出的平均贡献度和影响方向，用于理解模型对常规测井曲线的依赖关系。

	- **`grad_cam_examples/`** (子目录):
	    - **描述：** 针对最终模型**CNN分支**的1D-Grad-CAM（梯度加权类激活映射）分析示例图件。该目录将包含几张图，每张图展示一个典型的T2谱累积曲线样本，并用热力图高亮显示出模型在做预测时“注意力最集中”的曲线段，用于理解模型是如何“阅读”T2谱形状的。

### 2.8 编程实现伪代码

本节提供了一套结构化的Python伪代码，旨在作为实现新版`OBMIQ`混合输入模型工作流的编程指南。所有实现都应围绕之前定义的配置文件进行，以实现最大的灵活性和可复现性。

---

#### **1. 主控流程 (`main.py`)**

主控脚本负责解析配置、加载数据，并依次调用两个核心阶段的函数。

```python
# main.py

import pandas as pd
from data_loader import load_and_preprocess_data
from stage_1_tuning import run_hyperparameter_tuning
from stage_2_training import train_final_model
from utils import load_config, save_pickle, set_global_seed

def run_OBMIQ_workflow(config_path: str):
    """
    执行完整的OBMIQ建模工作流。
    """
    # 1. 加载配置和设置随机种子
    config = load_config(config_path)
    set_global_seed(config['internal']['reproducibility']['seed'])

    # 2. 加载并预处理数据
    # data_dict 的结构: {'sequence': X_seq, 'tabular': X_tab, 'target': y, 'groups': groups}
    data_dict, preprocessors_initial = load_and_preprocess_data(
        config['data_source'],
        config['feature_mapping']
    )

    # --- 阶段一：交叉验证与超参数寻优 ---
    print("--- 开始阶段一：交叉验证与超参数寻优 ---")
    best_hyperparameters = run_hyperparameter_tuning(
        data_dict=data_dict,
        tuning_config=config['tuning'],
        model_config=config['model_params'],
        training_config=config['training_params'],
        internal_config=config['internal'],
        output_path=config['output_path']['main_directory']
    )
    print(f"最佳超参数已找到: {best_hyperparameters.values}")

    # --- 阶段二：最终模型训练与交付 ---
    print("\n--- 开始阶段二：最终模型训练与交付 ---")
    final_model, final_preprocessors = train_final_model(
        data_dict=data_dict,
        best_hyperparameters=best_hyperparameters,
        model_config=config['model_params'],
        training_config=config['training_params'],
        internal_config=config['internal'],
        output_path=config['output_path']['main_directory']
    )
    print(f"最终模型已训练并保存在: {config['output_path']['main_directory']}")

    # 保存用于部署的预处理器
    save_pickle(final_preprocessors, f"{config['output_path']['main_directory']}/preprocessors.pkl")

    print("\nOBMIQ工作流执行完毕。")

if __name__ == '__main__':
    run_OBMIQ_workflow('config.yml')
```
#### **2. 模型构建函数 (`model_builder.py`)**

这个函数是与KerasTuner集成的核心。它已根据最终审核报告进行了更新，包含了`GlobalAveragePooling1D`、`BatchNormalization`等先进结构，并实现了**自适应损失加权**机制。

```python
# model_builder.py

import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, Conv1D, GlobalAveragePooling1D, BatchNormalization,
    Dense, Concatenate, Dropout
)

# --- 自适应损失加权实现 ---
# 这是一个自定义的损失函数层，用于处理多任务学习中的目标不平衡问题。
# 它将作为模型的一部分，自动学习两个任务的最佳权重。
class AdaptiveLossWrapper(tf.keras.layers.Layer):
    def __init__(self, num_tasks=2, **kwargs):
        super().__init__(**kwargs)
        self.num_tasks = num_tasks
        # 初始化两个可学习的对数方差参数，用于加权
        # 初始化为0意味着初始权重相等
        self.log_vars = [
            self.add_weight(
                name=f'log_var_{i}',
                shape=(),
                initializer=tf.keras.initializers.Constant(0.0),
                trainable=True
            ) for i in range(num_tasks)
        ]

    def call(self, inputs):
        y_true, y_pred = inputs
        loss = 0
        for i in range(self.num_tasks):
            precision = tf.exp(-self.log_vars[i])
            diff = (y_true[:, i] - y_pred[:, i]) ** 2
            loss += precision * diff + self.log_vars[i]
        return tf.reduce_mean(loss)

def build_model(hp, model_config: dict, training_config: dict, sequence_length: int, num_tabular_features: int, num_targets: int) -> Model:
    """
    根据给定的超参数构建并编译混合输入Keras模型。
    此函数已被更新以包含更先进的架构组件和自适应损失。
    """
    # --- 从配置中读取搜索范围，并使用hp对象定义超参数搜索空间 ---
    cnn_filters = hp.Choice('cnn_filters', values=model_config['cnn_filters'])
    cnn_kernel_size = hp.Choice('cnn_kernel_size', values=model_config['cnn_kernel_size'])
    mlp_units = hp.Choice('mlp_units', values=model_config['mlp_units'])
    dropout_rate = hp.Choice('dropout_rate', values=model_config['dropout_rate'])
    learning_rate = hp.Choice('learning_rate', values=training_config['learning_rate'])

    # --- 构建模型架构 ---
    # 分支A: 1D-CNN序列处理分支
    sequence_input = Input(shape=(sequence_length, 1), name='sequence_input')
    x = Conv1D(filters=cnn_filters, kernel_size=cnn_kernel_size, activation='relu')(sequence_input)
    # **更新点**: 使用GlobalAveragePooling1D替代Flatten，以减少参数并增强鲁棒性
    x = GlobalAveragePooling1D()(x)
    cnn_output = Dense(16, activation='relu')(x)

    # 分支B: MLP表格数据处理分支
    tabular_input = Input(shape=(num_tabular_features,), name='tabular_input')
    y = Dense(mlp_units, activation='relu')(tabular_input)
    mlp_output = y

    # 融合层
    combined = Concatenate()([cnn_output, mlp_output])

    # 回归头
    # **更新点**: 采用 BatchNormalization -> Dropout -> Dense 的稳健结构
    z = BatchNormalization()(combined)
    z = Dropout(dropout_rate)(z)
    z = Dense(16, activation='relu')(z)
    output_pred = Dense(num_targets, name='output_pred')(z)

    # --- 自适应损失加权的应用 ---
    y_true_input = Input(shape=(num_targets,), name='y_true_input')
    loss_output = AdaptiveLossWrapper(name='loss_output')([y_true_input, output_pred])

    # 创建并编译模型
    # 模型的输入现在包含真实值，以便计算自定义损失
    model = Model(
        inputs=[sequence_input, tabular_input, y_true_input],
        outputs=[output_pred, loss_output]
    )

    # 我们只关心自定义损失的输出，所以将其作为总损失
    model.add_loss(loss_output)

    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
    # 编译时，我们仍然需要一个虚拟的损失，但它不会被使用
    model.compile(optimizer=optimizer, loss=[None, None])

    return model
```

#### **3. 阶段一：超参数寻优 (`stage_1_tuning.py`)**

此模块的伪代码已更新，以反映如何处理新的模型输入结构（包含`y_true_input`），并确保回调函数参数的可配置性。

```python
# stage_1_tuning.py

import keras_tuner as kt
import numpy as np
import pandas as pd
from sklearn.model_selection import LeaveOneGroupOut, train_test_split
from tensorflow.keras.callbacks import EarlyStopping
from model_builder import build_model

def aggregate_and_report_cv_results(fold_results: list, output_path: str) -> kt.HyperParameters:
    """
    聚合交叉验证结果，确定最佳超参数，并保存详细报告。
    """
    # 1. 将所有折叠的结果转换为DataFrame以便分析
    results_df = pd.DataFrame(fold_results)

    # 2. 将字典形式的超参数展开为列，以便分组
    params_df = results_df['best_params'].apply(pd.Series)
    full_results_df = pd.concat([results_df.drop('best_params', axis=1), params_df], axis=1)

    # 3. 按超参数组合进行分组，计算每个组合的平均RMSE和标准差
    param_names = list(params_df.columns)
    agg_results = full_results_df.groupby(param_names)['test_rmse'].agg(['mean', 'std']).reset_index()
    agg_results = agg_results.sort_values(by='mean').rename(columns={'mean': 'mean_rmse', 'std': 'std_rmse'})

    # 4. 确定最佳超参数组合（平均RMSE最低）
    best_params_dict = agg_results.iloc[0][param_names].to_dict()

    # 5. 创建一个KerasTuner的HyperParameters对象来承载最佳参数
    best_hps = kt.HyperParameters()
    for key, value in best_params_dict.items():
        best_hps.Fixed(key, value)

    # 6. 保存完整的交叉验证性能报告
    report_path = f"{output_path}/cv_performance_report.csv"
    full_results_df.to_csv(report_path, index=False)
    print(f"交叉验证性能报告已保存至: {report_path}")

    return best_hps

def run_hyperparameter_tuning(data_dict: dict, tuning_config: dict, model_config: dict, training_config: dict, internal_config: dict, output_path: str) -> kt.HyperParameters:
    """
    执行LOWO-CV和超参数寻优。
    """
    X_seq, X_tab, y, groups = data_dict['sequence'], data_dict['tabular'], data_dict['target'], data_dict['groups']

    logo = LeaveOneGroupOut()
    fold_results = []

    for fold, (train_idx, test_idx) in enumerate(logo.split(X_seq, y, groups)):
        print(f"\n--- 开始CV折叠 {fold + 1}/{logo.get_n_splits(groups=groups)} ---")
        X_seq_train, X_tab_train, y_train = X_seq[train_idx], X_tab[train_idx], y[train_idx]
        X_seq_test, X_tab_test, y_test = X_seq[test_idx], X_tab[test_idx], y[test_idx]

        X_seq_subtrain, X_seq_val, X_tab_subtrain, X_tab_val, y_subtrain, y_val = train_test_split(
            X_seq_train, X_tab_train, y_train, test_size=tuning_config['val_split_ratio'],
            random_state=internal_config['reproducibility']['seed']
        )

        build_fn = lambda hp: build_model(
            hp=hp, model_config=model_config, training_config=training_config,
            sequence_length=X_seq.shape[1], num_tabular_features=X_tab.shape[1], num_targets=y.shape[1]
        )

        tuner = kt.Hyperband(
            build_fn,
            objective='val_loss',
            max_epochs=tuning_config['max_epochs_per_trial'],
            hyperband_iterations=tuning_config['hyperband_iterations'],
            directory=f"{output_path}/tuner_fold_{fold}",
            project_name='OBMIQ_tuning'
        )

        early_stopping = EarlyStopping(
            monitor=internal_config['early_stopping']['monitor'],
            patience=internal_config['early_stopping']['patience'],
            restore_best_weights=True
        )

        train_inputs = {'sequence_input': X_seq_subtrain, 'tabular_input': X_tab_subtrain, 'y_true_input': y_subtrain}
        val_inputs = {'sequence_input': X_seq_val, 'tabular_input': X_tab_val, 'y_true_input': y_val}

        tuner.search(
            x=train_inputs,
            y={'output_pred': y_subtrain, 'loss_output': np.zeros(len(y_subtrain))}, # 虚拟目标
            epochs=tuning_config['max_epochs_per_trial'],
            validation_data=(
                val_inputs,
                {'output_pred': y_val, 'loss_output': np.zeros(len(y_val))}
            ),
            callbacks=[early_stopping]
        )

        best_hps_fold = tuner.get_best_hyperparameters(num_trials=1)[0]
        model_fold = tuner.get_best_models(num_models=1)[0]

        # 评估时，我们只关心预测输出的RMSE
        # model.predict的输入需要与训练时一致
        test_inputs = {'sequence_input': X_seq_test, 'tabular_input': X_tab_test, 'y_true_input': y_test}
        y_pred_test = model_fold.predict(test_inputs)[0] # predict返回[output_pred, loss_output]
        test_rmse = np.sqrt(np.mean((y_test - y_pred_test)**2))

        fold_results.append({
            'fold': fold,
            'best_params': best_hps_fold.values,
            'test_rmse': test_rmse
        })

    # **更新点**: 调用新的聚合函数来处理结果
    final_best_hps = aggregate_and_report_cv_results(fold_results, output_path)

    return final_best_hps
```

#### **4. 阶段二：最终模型训练 (`stage_2_training.py`)**

此模块负责使用在阶段一找到的最佳超参数，在全部可用的建模数据上训练、评估并保存最终的生产模型。

```python
# stage_2_training.py

import tensorflow as tf
import keras_tuner as kt
from sklearn.model_selection import train_test_split
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
from model_builder import build_model
from utils import plot_training_history, perform_final_evaluation # 假设的辅助函数

def train_final_model(
    data_dict: dict,
    best_hyperparameters: kt.HyperParameters,
    model_config: dict,
    training_config: dict,
    internal_config: dict,
    output_path: str
):
    """
    使用最佳超参数在全部数据上训练并保存最终模型。

    Args:
        data_dict: 包含所有数据的字典。
        best_hyperparameters: KerasTuner返回的最佳HyperParameters对象。
        model_config: 包含模型架构超参数搜索空间的字典。
        training_config: 包含训练超参数搜索空间的字典。
        internal_config: 包含内部固定参数的字典。
        output_path: 产物的根输出目录。

    Returns:
        一个元组，包含训练好的最终模型和预处理器对象。
    """
    X_seq = data_dict['sequence']
    X_tab = data_dict['tabular']
    y = data_dict['target']

    # 1. 从全部数据中划分一个小的验证集，仅用于早停
    # 注意：这里的 random_state 应使用配置文件中的全局随机种子
    X_seq_train, X_seq_val, X_tab_train, X_tab_val, y_train, y_val = train_test_split(
        X_seq, X_tab, y, test_size=0.1, # 验证集比例可配置
        random_state=internal_config['reproducibility']['seed']
    )

    # 2. 使用最佳超参数重建模型
    # 直接复用build_model函数。当KerasTuner的HyperParameters对象
    # 作为hp参数传入时，其Choice/Int等方法会自动返回已确定的最佳值。
    final_model = build_model(
        hp=best_hyperparameters,
        model_config=model_config,
        training_config=training_config,
        sequence_length=X_seq.shape[1],
        num_tabular_features=X_tab.shape[1],
        num_targets=y.shape[1]
    )
    print("--- 最终模型架构 ---")
    final_model.summary()

    # 3. 定义回调函数
    # 回调的参数（如patience）应从配置文件中读取
    early_stopping = EarlyStopping(
        monitor=internal_config['early_stopping']['monitor'],
        patience=internal_config['early_stopping']['patience'],
        restore_best_weights=True
    )

    checkpoint_filepath = f"{output_path}/final_model.keras"
    model_checkpoint = ModelCheckpoint(
        filepath=checkpoint_filepath,
        monitor=internal_config['early_stopping']['monitor'],
        save_best_only=True
    )

    # 4. 训练最终模型
    # 训练的epochs也应可配置，可以为最终训练设置一个较大的值
    history = final_model.fit(
        x={'sequence_input': X_seq_train, 'tabular_input': X_tab_train},
        y=y_train,
        epochs=training_config.get('final_train_epochs', 150), # 示例：从配置读取或设默认值
        batch_size=best_hyperparameters.get('batch_size'),
        validation_data=(
            {'sequence_input': X_seq_val, 'tabular_input': X_tab_val},
            y_val
        ),
        callbacks=[early_stopping, model_checkpoint],
        verbose=2
    )

    # 5. 加载性能最佳的模型（由ModelCheckpoint自动保存）
    print(f"从 {checkpoint_filepath} 加载性能最佳的最终模型...")
    best_model = tf.keras.models.load_model(checkpoint_filepath)

    # 6. 生成并保存产物
    print("正在生成最终模型的产物...")
    # a. 保存训练历史图表
    plot_training_history(history, f"{output_path}/final_model_training_history.png")

    # b. 在全部数据上进行评估并生成图表
    perform_final_evaluation(
        model=best_model,
        full_data_dict=data_dict,
        output_dir=f"{output_path}/final_model_evaluation_plots"
    )

    # c. (此处还应返回在全部数据上fit好的预处理器)
    # final_preprocessors = fit_preprocessors_on_all_data(data_dict)

    return best_model, None # 临时返回None作为预处理器占位符
```

## 3 OBMIQ基准模型：基于经典机器学习的稳健性评估

### 3.1 目标与定位

`scape.core.obmiq_baselines`组件的核心任务与PyTorch版完全相同——预测`DT2_P50`和`DPHIT_NMR`，但其实现路径遵循一套经典的、基于Scikit-learn的机器学习流程。

**核心特点**:
*   **经典技术栈**: 完全基于`Scikit-learn`、`XGBoost`等成熟库，利用`Pipeline`机制确保数据处理流程的稳健性与可复现性。
*   **稳健的评估框架**: 采用**嵌套交叉验证 (Nested Cross-Validation)** 框架，无偏地评估和选择最佳模型组合，从根本上避免了“过拟合的评估”。
*   **自动化模型选择与融合**: 流程会自动从多个候选模型（如XGBoost, RandomForest, SVR）中，选择表现最佳的两个进行**加权融合**，以提升模型的稳定性和泛化能力。

### 3.2 建模流程详解

该基准模型的建模流程严格遵循《SCAPE_方法说明书_OBMIQ建模方法2.md》中定义的规程，核心是嵌套交叉验证和最终模型训练两个阶段。

#### a. 嵌套交叉验证 (Nested Cross-Validation)

此阶段的**唯一目的**是无偏地评估不同候选模型的泛化能力，并确定最优的模型融合策略。

*   **外层循环: 留一井交叉验证 (LOWO-CV)**
    *   **目的**: 提供对模型最终泛化能力的无偏估计。
    *   **执行**: 以井为单位进行循环。在每一折中，一口井作为**测试集**，其余井作为**训练集**。

*   **内层循环: K-Fold 交叉验证**
    *   **目的**: 在外层循环提供的训练集上，执行稳健的特征选择和超参数寻优。
    *   **执行**: 将训练集划分为K个互不相交的子集，进行K-Fold交叉验证。

*   **内层优化流程 (在每一折内独立执行)**:
    1.  **特征选择 (RFECV)**: 采用递归特征消除交叉验证，内部使用一个轻量级的`RandomForestRegressor`来评估特征重要性，并自动选择最优的特征子集。
    2.  **超参数寻优 (RandomizedSearchCV)**: 在上一步选出的特征子集上，为每个候选模型（XGBoost, RandomForest, SVR）执行随机搜索，以找到最佳的超参数组合。

*   **外层评估**:
    *   使用在内层找到的最优特征和超参数，在外层训练集上训练一个临时模型。
    *   用该模型对外层预留的测试井进行预测，并计算其RMSE。

#### b. 模型比较与融合策略选择

*   **性能聚合**: 在所有LOWO折完成后，计算每个候选模型在所有测试井上的平均RMSE。
*   **加权融合**: 选取平均RMSE最低的两个模型（例如，XGBoost和RandomForest），根据其性能（1/RMSE）计算融合权重。表现越好的模型，权重越高。
    $$ w_1 = \frac{1/\text{RMSE}_1}{1/\text{RMSE}_1 + 1/\text{RMSE}_2}, \quad w_2 = 1 - w_1 $$

#### c. 最终模型训练与交付

此阶段的目标是利用**所有可用数据**，通过已选定的最优流程（即“加权融合XGBoost和RandomForest”）来训练一个用于实际部署的、性能最强的最终模型。

1.  **为每个模型组件进行最终优化**:
    *   对于选出的两个最佳模型（如XGBoost），在**全部数据集**上，重新、独立地执行特征选择（RFECV）和超参数寻优（RandomizedSearchCV）。
    *   **防泄漏关键**: 在此步骤中，所有内部的交叉验证都必须使用`GroupKFold`，并按井号进行分组，以确保在全量数据上优化时，井间信息不会泄露。

2.  **最终模型训练**:
    *   使用上一步为每个组件找到的全局最优特征和超参数，在**全部数据集**上分别训练出最终的模型组件。

3.  **模型交付**:
    *   将两个训练好的最终模型组件，连同在嵌套CV阶段确定的融合权重，一同封装到一个`WeightedEnsembleModel`对象中。
    *   该融合模型即为`obmiq_baselines`组件的最终交付成果，它在调用`.predict()`时会自动完成对内部两个子模型预测结果的加权平均。
    *   由于存在两个预测目标，上述流程将为`DT2_P50`和`DPHIT_NMR`独立执行，最终产出两个独立的融合模型。

---

# Stage-2

Stage-2阶段完成渗透率建模评估和计算。建模数据分为三部分：
1. 训练数据：
	-  数据来自三口井C-1、C-2、C-3井，数据已经经过合并，即以Point标签数据（岩心和MDT渗透率）的深度为基准，将对应深度的测井曲线值取出来，合并成一个测井数据集，数据集名称叫“swift_pso_logs"。
	- 测井曲线包括：DPHIT_NMR、PHIT_NMR、T2LM、DT2_P50、T2_P50、PHI_T2_DIST_CUM、K_LABEL_TYPE、K_LABEL。其中DT2_P50和DPHIT_NMR是利用Stage-1的OBMIQ模型预测（对于没有LWD NMR的井段）；K_LABEL为渗透率真值标签；K_LABEL_TYPE为渗透率类型，CORE为岩心渗透率、MDT为MDT渗透率；PHI_T2_DIST_CUM为二维曲线，是累积T2谱曲线。
2. PLT验证盲井：
	- 即C-1井的PLT解释成果，Interval类型，数据集名称"plt"，产量曲线名为qozi。
	- 注意：C-1井的数据在训练阶段已经使用过，但是PLT的测量范围是整个井段，其中的岩心和MDT渗透率数据的覆盖度只有PLT覆盖度的9%，也就是说大部分PLT的地层信息在训练阶段是没有看到的，不构成信息泄露。
3. T-1井渗透率检验盲井：
	- 包含岩心渗透率和MDT渗透率，数据集名称”K_Val“，注意岩心渗透率是采用CT方法计算得到的。
	- 曲线：K_LABEL为渗透率真值标签；K_LABEL_TYPE为渗透率类型，CORE为岩心渗透率、MDT为MDT渗透率。
上述数据已经存入wp文件中，文件名swift_pso_input.wp.xlsx。


## 4 FOSTER-NMR核磁共振测井渗透率计算方程

核磁共振测井渗透率计算方程命名为FOSTER-NMR（Fusion Of SDR–Timur with Extended Recalibrations for NMR permeability）.

FOSTER-NMR is a physically-constrained permeability model that fuses SDR and Timur pathways, then applies sequential OBM invasion correction and MDT-based bias adjustment, providing a unified framework for NMR-derived permeability in complex carbonate reservoirs.

基于FOSTER-NMR得到的核磁共振渗透率测井渗透率用K_NMR（公式中用表示）表示，计算过程共有5个步骤：

- NMR T2孔隙度划分；
- SDR碳酸盐岩渗透率计算和Timur/Coates大孔隙渗透率计算；
- 融合SDR渗透率和Timur/Coates大孔隙渗透率；
- 核磁共振油基泥浆侵入校正；
- MDT渗透率修正。

### 4.1 NMR T2谱孔隙度划分

- T2cutoff_short和T2cutoff_long: 分别为长短T2截止值，ms；
- Vmacro、Vmeso、Vmicro: 由T2cutoff_short和T2cutoff_long在T2谱划分得到的分段孔隙度，v/v。

### 4.2 SDR碳酸盐岩渗透率公式(SDR Carbonate Equation)

SDR公式为（公式3-1）：

$$

k_{SDR} = KSDR\_A \cdot PHIT^{PHIT_{exp}} \cdot (RHO_{NMR} \cdot T2LM)^{T2LM_{exp}}

$$

式中：

- K_SDR：渗透率，单位为mD
- KSDR_A：为经验性的SDR渗透率方程系数(premultiplier)；
- PHIT：核磁共振测井总孔隙度，相当于输入曲线PHIT_NMR，v/vv；
- PHIT_EXP：孔隙度指数，无量纲；
- T2LM_EXP：T2对数平均值指数，无量纲，通过优化确定；
- RHO_NMR：核磁共振表面弛豫率，µm/s。

### 4.3 Timur/Coates大孔隙渗透率公式

Timur/Coates大孔隙渗透率公式如下（公式3-2）：

$$

k_{macro} = KMACRO\_A \cdot PHIT^{PHIT\_exp} \cdot \left(\frac{V_{macro}}{PHIT - V_{macro}}\right)^{KMACRO\_B}

$$

式中：

- K_MACRO：渗透率，单位为mD；
- KMACRO_A：经验性系数(premultiplier)；
- PHIT：核磁共振测井总孔隙度，相当于输入曲线PHIT_NMR，v/vv；
- PHIT_EXP：与SDR碳酸盐岩渗透率公式中的PHIT_EXP两者是一同指数；
- KMACRO_B：大孔孔隙度比率指数，无量纲，通过优化确定；
- Vmacro：大孔孔隙度，v/v。

### 4.4 融合SDR渗透率和Timur/Coates大孔隙渗透率

- 当 Vmacro>Vmacro_min时，采用K_MACRO公式计算，否则采用K_SDR计算，Vmacro_min为Vmacro的阀值，单位为v/v。
- 为了避免在 Vmacro ≈ Vmacro_min的边界上出现非物理跳变，SWIFT-PSO采用Sigmoid 权重融合方式对两种渗透率计算结果进行平滑过渡。

定义 Sigmoid 权重函数如下（公式3-3）：

$$

W_{macro} = \frac{1}{1 + e^{-s(V_{macro} - V_{macro\_min})}}

$$

式中：

- W_macro：为大孔渗透率K_MACRO的融合权重；
- s：平滑系数，控制融合区域的过渡带宽，默认取30，此参数暂时不列入优化参数集合中。

融合后视渗透率K_APP（$k_{APP}$）计算公式为（公式3-4）：

$$

k_{APP} = W_{macro} \cdot k_{macro} + (1 - W_{macro}) \cdot k_{SDR}

$$

K_APP的单位为mD，该公式可保证：

- 当Vmacro<<Vmacro_min时，W_macro→0，模型主要使用 K_SDR；
- 当Vmacro>>Vmacro_min时 ，W_macro→1，模型主要使用 K_MACRO；
- 中间区域将以可导、连续的方式融合两者。

### 4.5 核磁共振油基泥浆侵入校正

核磁共振油基泥浆侵入校正因子OBMCF_K（$OBMCF_k$）定义为（公式3-5）：

$$

OBMCF_k = e^{\left[ \beta_1 \frac{DT2\_P50}{T2\_P50^{ref}} + \beta_2 \frac{DPHIT\_NMR}{PHIT\_NMR^{ref}} \right]}

$$

式中：

- OBMCF_K：为校正因子，无量纲；
- T2_P50^ref（$T2\_P50^{ref}$）：T2_P50的中位数，单位是ms，仅用训练 + 验证井计算中位数，部署到盲井时保持固定；
- PHIT_NMR^ref（$PHIT\_NMR^{ref}$）：PHIT_NMR的中位数，单位是v/v，仅用训练 + 验证井计算中位数，部署到盲井时保持固定；
- beta_1（$\beta_1$）、beta_2（$\beta_2$）：指数斜率系数，无量纲，>0。。

上式物理含义是：侵入增强→渗透率升高。

- 当 DT2_P50 = 0 且 DPHIT_NMR = 0 时，括号内为 0，故 OBMCF_K = 1；
- 任一增量为正，函数保证系数随侵入程度单调上升，即侵入程度越高，渗透率校正越高。

经核磁共振油基泥浆侵入校正后的渗透率K_OBMC($k_{OBMC}$)为（公式3-6）：

$$
k_{OBMC}=k_{APP}\cdot OBMCF_k
$$
K_OBMC的单位为mD。
### 4.6 MDT渗透率修正

定义MDT渗透率校正系数为delta_MDT（$\delta_{MDT}$）,无量纲，仅作用于MDT样本，CORE样本固定为0，
最终渗透率$k_{NMR}$计算公式如下（公式3-7）：

$\displaystyle k_{NMR,i}=\begin{cases}\;k_{OBMC,i}(1+\delta_{MDT}) & \text{ if } i\in\text{MDT}\\[4pt]\;k_{OBMC,i} & \text{ if } i\in\text{CORE}\end{cases}$

### 4.7 FOSTER-NMR 正向模型函数伪代码

这份伪代码旨在作为一个清晰的编程蓝图，将上述的五个计算步骤，封装成一个可调用的函数。它使用了Numpy库的风格，以处理批量数据（例如，一口井中所有深度点的数据）。

```python
import numpy as np

def calculate_porosity_components(T2_TIME, T2_VALUE, T2cutoff_short, T2cutoff_long):
    """
    根据给定的T2截止值，从T2谱计算Vmicro, Vmeso, Vmacro孔隙度组分。

    Args:
        T2_TIME (np.array): T2谱的时间轴 (ms)。
        T2_VALUE (np.array): T2谱的孔隙度分布 (v/v)。
        T2cutoff_short (float): 短T2截止值 (ms)。
        T2cutoff_long (float): 长T2截止值 (ms)。

    Returns:
        tuple: (Vmicro, Vmeso, Vmacro) 孔隙度组分 (v/v)。
    """
    # 确保截止值顺序正确
    if T2cutoff_short >= T2cutoff_long:
        raise ValueError("T2cutoff_short must be less than T2cutoff_long.")

    # 根据条件筛选计算孔隙度
    # 注意: T2_VALUE 可能是 (n_depths, n_bins) 的二维数组
    # axis=1 表示沿着T2 bins的方向求和
    Vmicro = np.sum(T2_VALUE[:, T2_TIME < T2cutoff_short], axis=1)
    Vmeso = np.sum(T2_VALUE[:, (T2_TIME >= T2cutoff_short) & (T2_TIME < T2cutoff_long)], axis=1)
    Vmacro = np.sum(T2_VALUE[:, T2_TIME >= T2cutoff_long], axis=1)

    return Vmicro, Vmeso, Vmacro

def calculate_foster_nmr(parameters, well_data):
    """
    根据FOSTER-NMR模型，计算最终的核磁共振渗透率 K_NMR。

    该函数严格遵循文档第3节定义的5个计算步骤。

    Args:
        parameters (dict): 包含全部10个待优化模型参数的字典。
            - 'log10_KSDR_A', 'PHIT_EXP', 'T2LM_EXP', 'RHO_NMR', 'log10_KMACRO_A',
            - 'KMACRO_B', 'Vmacro_min', 'log10_T2cutoff_short', 'log10_T2cutoff_long',
            - 'beta_1', 'beta_2', 'delta_MDT'
            - 还应包含参考值 'T2_P50_ref', 'PHIT_NMR_ref'

        well_data (dict or pd.DataFrame): 包含一口井所有深度点的测井曲线。
            - 'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',
            - 'T2_TIME', 'T2_VALUE' (T2谱)
            - 'K_LABEL_TYPE' (用于MDT修正)

    Returns:
        np.array: 在所有深度点上计算出的最终渗透率 K_NMR (mD)。
    """
    # --- 0. 解包参数和数据 ---
    # 将对数域的参数转换回线性域
    KSDR_A = 10**parameters['log10_KSDR_A']
    KMACRO_A = 10**parameters['log10_KMACRO_A']
    T2cutoff_short = 10**parameters['log10_T2cutoff_short']
    T2cutoff_long = 10**parameters['log10_T2cutoff_long']

    PHIT_EXP = parameters['PHIT_EXP']
    T2LM_EXP = parameters['T2LM_EXP']
    RHO_NMR = parameters['RHO_NMR']
    KMACRO_B = parameters['KMACRO_B']
    Vmacro_min = parameters['Vmacro_min']
    beta_1 = parameters['beta_1']
    beta_2 = parameters['beta_2']
    delta_MDT = parameters['delta_MDT']

    # 获取参考值和固定参数
    T2_P50_ref = parameters['T2_P50_ref']
    PHIT_NMR_ref = parameters['PHIT_NMR_ref']
    s = 30.0      # Sigmoid平滑系数，固定值

    # 从well_data中提取测井曲线
    PHIT = well_data['PHIT_NMR']
    T2LM = well_data['T2LM']

    # --- 1. NMR T2谱孔隙度划分 (Section 3.1) ---
    # 根据优化出的T2截止值实时计算孔隙度组分
    _Vmicro, _Vmeso, Vmacro = calculate_porosity_components(
        well_data['T2_TIME'],
        well_data['T2_VALUE'],
        T2cutoff_short,
        T2cutoff_long
    )

    # --- 2. SDR碳酸盐岩渗透率 与 Timur/Coates大孔隙渗透率 (Section 3.2 & 3.3) ---
    # 公式1: SDR Carbonate Equation
    k_sdr = KSDR_A * (PHIT ** PHIT_EXP) * ((RHO_NMR * T2LM) ** T2LM_EXP)

    # 公式2: Timur/Coates Macro-porosity Equation
    # 为避免分母为零或负数，增加数值稳定性处理
    movable_fluid_fraction = PHIT - Vmacro
    # 将小于一个极小正数的值替换掉，防止除零错误
    movable_fluid_fraction[movable_fluid_fraction <= 1e-6] = 1e-6
    ratio_term = Vmacro / movable_fluid_fraction
    k_macro = KMACRO_A * (PHIT ** PHIT_EXP) * (ratio_term ** KMACRO_B)

    # --- 3. 融合SDR和Timur/Coates渗透率 (Section 3.4) ---
    # 公式3: Sigmoid 权重函数
    w_macro = 1 / (1 + np.exp(-s * (Vmacro - Vmacro_min)))

    # 公式4: 融合后的视渗透率 K_APP
    k_app = w_macro * k_macro + (1 - w_macro) * k_sdr

    # --- 4. 核磁共振油基泥浆侵入校正 (Section 3.5) ---
    DT2_P50 = well_data['DT2_P50']
    T2_P50 = well_data['T2_P50']
    DPHIT_NMR = well_data['DPHIT_NMR']

    # 公式5: 油基泥浆侵入校正因子 OBMCF_K
    # 增加数值稳定性，防止 T2_P50 或 PHIT_NMR_ref 为零
    safe_T2_P50_ref = T2_P50_ref if T2_P50_ref > 1e-6 else 1e-6
    safe_PHIT_NMR_ref = PHIT_NMR_ref if PHIT_NMR_ref > 1e-6 else 1e-6

    exponent = (beta_1 * (DT2_P50 / safe_T2_P50_ref)) + \
               (beta_2 * (DPHIT_NMR / safe_PHIT_NMR_ref))
    obmcf_k = np.exp(exponent)

    # 公式6: 校正后的渗透率 K_OBMC
    k_obmc = k_app * obmcf_k

    # --- 5. MDT渗透率修正 (Section 3.6) ---
    # 公式7: 根据样本类型应用MDT修正
    sample_types = well_data['K_LABEL_TYPE']

    # 使用 np.where 实现条件化修正，对整个数组高效操作
    # 当样本类型为'MDT'时，应用 (1 + delta_MDT) 的乘性修正，否则修正为1（即不变）
    mdt_correction_factor = np.where(sample_types == 'MDT', 1 + delta_MDT, 1.0)

    k_nmr = k_obmc * mdt_correction_factor

    return k_nmr

```


## 5 SWIFT-PSO核磁共振渗透率计算模型参数优化流程

SWIFT-PSO是针对该盆地阿拉姆区块储层渗透率计算的核心优化流程。面对训练数据中非产层样本占比较高、而关键目标是精准预测高产层渗透率的挑战，SWIFT-PSO流程的核心不仅在于参数优化，更在于如何智能地处理和区分不同类型的样本。

为此，SWIFT-PSO在优化过程中采用了一套基于先验知识的复合损失函数。该函数利用已知的产层指示（PZI）曲线，实现了**“加权误差”**与**“非对称惩罚”**的双重机制。这一设计旨在引导优化算法重点关注高权重的产层样本，同时通过惩罚项防止模型对低权重的非产层样本做出过高的渗透率预测，从而在复杂的数据条件下获得更可靠、更符合地质规律的模型参数。

该损失函数被应用于“Bootstrap+LOWO+Fine-Tuning+盲井泛化估计”的核心工作流中。具体来讲，流程的第一阶段通过Bootstrap（自助采样）与LOWO（留一井交叉验证）的双重循环扰动，在每一折中执行PSO参数搜索，以稳健地探索参数空间。该阶段结束后，进入Fine-Tuning阶段，在全量数据上进行热启动（Warm-Start）和精细微调，最终得到用于部署的10个模型参数。

SWIFT-PSO的全称是**S**ample-**W**eighted **I**terative **F**ine-**T**uning with **P**article **S**warm **O**ptimization，名称含义如下：

* **Sample-Weighted**→基于PZI的样本加权与非对称惩罚
* **Iterative Fine-Tuning**→窄窗口 Warm-Start 微调
* LOWO隐含在流程中（跨井验证）
* PSO→全程作为全局搜索与局部微调核心算法

用“SWIFT-PSO”既突出了快速收敛（swift）的特点，又完整涵盖了流程关键步骤的技术要点，方便在后续文档、代码与汇报中统一引用。

### 5.1 待优化参数概况

利用SWIFT-PSO求取核磁共振渗透率计算模型中的12个关键参数如下:

1. KSDR_A
2. PHIT_EXP
3. T2LM_EXP
4. RHO_NMR
5. KMACRO_A
6. KMACRO_B
7. Vmacro_min
8. T2cutoff_short
9. T2cutoff_long
10. beta_1
11. beta_2
12. delta_MDT

其中`PHIT_EXP`是SDR和Timur/Coates共享参数，这些参数形成待优化参数向量为Theta（$\theta$）:

$\theta$= \[log10_KSDR_A, PHIT_EXP, T2LM_EXP, RHO_NMR, log10_KMACRO_A, KMACRO_B, Vmacro_min, log10_T2cutoff_short, log10_T2cutoff_long, beta_1, beta_2, delta_MDT]

注意：

- 对于物理上呈乘幂／跨数量级变化的系数（ KSDR_A、KMACRO_A、T2cutoff 等）取log10后的数值作为粒子坐标，整个粒子群仅在这个对数域里做线性加减、速度更新和边界裁剪。待需要代入公式时，再把搜索得到的对数值还原回线性量级；
- 对于这种对数搜索的参数形式上表示为log10(KSDR_A)这样的形式，也可以表示前缀形式log10_KSDR_A，下文中将使用前缀形式；
- “先把每个参数在自己的物理合法区间里均匀撒豆子，再让PSO去追优”：所有 10 个待优化参数在第一次迭代前必须覆盖各自允许区间的全尺度信息，因此统一采用均匀分布随机采样 (U) 进行粒子初始化；对跨数量级变化的参数，先在 log10 域_上均匀采样，再指数还原到线性域，以保证搜索空间在参数物理意义上是“对称”的，而不会因取值跨度过大导致早期失衡。

### 5.2 参数边界约束机制

采用**硬-软边界协同**策略：
- **硬边界**（下表中"Hard LB/UB"）：在Bootstrap+LOWO阶段PSO搜索时采用Hard Reflection策略，在Fine-Tuning阶段采用Boundary-Jitter策略，保证任何时刻参数处于物理可行域；
- **软边界**（下表中“Soft LB/UB”列）：位于硬边界 **10 % 内侧**，由Loss函数中的物理惩罚项执行，仅当粒子越过软界时才产生惩罚梯度；若进一步撞到硬界，则先反射后再计算罚分；$$ \Omega_{phys}=λ_2\sum_{p\in p_2}\Bigl[\max(0,\,p-p_{soft\_UB})^2+\max(0,\,p_{soft\_LB}-p)^2\Bigr]+…  $$
-  **用途区分**：软惩罚提供“**回中心**”的连续信号；硬边界则为“**绝不越线**”的数值保险。**两层边界并存**并非冗余：硬界保安全，软界给梯度。“内软-外硬”模式可兼得 **物理可信** 与 **优化可导**。

#### 5.2.1 **Hard Reflection**策略

该策略在 **Bootstrap + LOWO 阶段**的PSO（粒子群优化）搜索中作为主要的边界处理机制。其核心思想是将任何试图穿越硬边界的粒子以边界为镜面进行对称反弹，并同时反转其在该维度上的速度，从而强制其留在可行域内并彻底改变其飞行方向，以鼓励在搜索空间内进行更充分的探索。

具体步骤如下：
1.  **越界检测 (Detection)**：
    在每一代粒子位置更新后，检测粒子在维度 *p* 上的新位置 $x_p$ 是否超出了其预设的硬边界范围 $[HardLB_p, HardUB_p]$。

2.  **位置镜像反射 (Position Mirror Reflection)**：
    根据粒子所撞击的边界，将其位置沿该边界对称地反射回搜索空间的内部。
    * 如果粒子试图穿越**下边界** ($x_p < HardLB_p$)，则其新位置通过以下公式计算：
        $$x_p \leftarrow 2 \cdot HardLB_p - x_p$$
        *（该公式的几何意义是：粒子穿过边界的深度 `HardLB_p - x_p`，将被同样深度地反射回边界内侧。）*
    * 如果粒子试图穿越**上边界** ($x_p > HardUB_p$)，则其新位置通过以下公式计算：
        $$x_p \leftarrow 2 \cdot HardUB_p - x_p$$
3.  **速度反转 (Velocity Reversal)**：
    在更新粒子位置的同时，将该粒子在维度 *p* 上的速度 $v_p$ 进行完全反向处理。这是该策略的关键组成部分，旨在惩罚并阻止粒子继续朝向同一边界移动。
    $$v_p \leftarrow -v_p$$

这种“位置反射 + 速度反转”的组合，是一种非常严格的边界约束机制。它不仅能有效将粒子约束在可行域内，还能通过剧烈改变其动量来显著增加搜索的扰动，有助于算法在优化的早期和中期阶段跳出局部最优陷阱，因此非常适用于需要进行全局探索的 **Bootstrap + LOWO** 阶段。这与用于局部精细调整的 **Boundary-Jitter** 策略形成了鲜明的对比。

#### 5.2.2 **Boundary-Jitter**策略

在**Fine-Tuning 阶段（窄窗口 PSO）**，粒子若越过其窄搜索窗 $[HardLB_{narrow,p}, HardUB_{narrow,p}]$，则采用**Boundary-Jitter**策略，具体步骤如下：

1.  **越界检测 (Detection)**：
    判断粒子在维度 *p* 上的新位置 $x_p$ 是否超出了其窄窗硬边界。

2.  **边界钳位 (Clamping)**：
    首先，将粒子的位置强制拉回并置于它所撞击的边界上。
    * 若 $x_p < HardLB_{narrow,p}$，则将位置更新为 $x_p \leftarrow HardLB_{narrow,p}$。
    * 若 $x_p > HardUB_{narrow,p}$，则将位置更新为 $x_p \leftarrow HardUB_{narrow,p}$。

3.  **定向扰动 (Directed Jitter)**：
    接下来，从边界位置向搜索空间的**内部**施加一个小的随机扰动，以将粒子“推”回有效区域内。
    * 如果粒子是撞击了**下边界**($HardLB_{narrow,p}$)，则为其增加一个**正向的**随机扰动值：
        $$x_p \leftarrow x_p + |\mathcal{N}(0, 0.01\sigma_p^2)|$$
        这里使用高斯分布随机数的**绝对值**`abs()`，确保扰动方向是朝向搜索空间内部的。
    * 如果粒子是撞击了**上边界**($HardUB_{narrow,p}$)，则为其减去一个**正向的**随机扰动值：
        $$x_p \leftarrow x_p - |\mathcal{N}(0, 0.01\sigma_p^2)|$$
        同样，通过减去一个绝对值，确保扰动方向是朝向搜索空间内部的。

4.  **最终校验 (Final Check)**：
    为了防止一个极大的随机扰动（虽然概率很低）使粒子越过整个窄窗并从另一侧出界，可以增加一步最终的边界校验。
    $$x_p \leftarrow \max(HardLB_{narrow,p}, \min(x_p, HardUB_{narrow,p}))$$

5.  **速度处理 (Velocity Handling)**：
    在此过程中粒子的速度 $v_p$ **不进行反转**，保持不变，留待下一轮由标准的PSO公式进行更新。

#### 5.2.3 **参数硬边界和软边界**

待优化参数硬边界和软边界见下表：

| 序号  | 参数                   | log10 | Hard LB/UB  | Soft LB/UB    | 单位        | 备注  |
| --- | -------------------- | ----- | ----------- | ------------- | --------- | --- |
| 1   | Log10_KSDR_A         | Y     | -2 / 2      | -1.6 / 1.6    |           |     |
| 2   | PHIT_EXP             |       | 1 / 5       | 1.4 / 4.6     |           |     |
| 3   | T2LM_EXP             |       | 1 / 5       | 1.4 / 4.6     |           |     |
| 4   | RHO_NMR              |       | 0.1 / 50    | 5.09 / 45.01  | µm/s      |     |
| 5   | Log10_KMACRO_A       | Y     | -2 / 2      | -1.6 / 1.6    |           |     |
| 6   | KMACRO_B             |       | 1 / 5       | 1.4 / 4.6     |           |     |
| 7   | Vmacro_min           |       | 0.02 / 0.15 | 0.033 / 0.137 | v/v       |     |
| 8   | Log10_T2cutoff_short | Y     | 1.5 / 2.5   | 1.6 / 2.4     | log10(ms) |     |
| 9   | Log10_T2cutoff_long  | Y     | 3 / 3.5     | 3.05 / 3.45   | log10(ms) |     |
| 10  | beta_1               |       | 0.02 / 0.5  | 0.068 / 0.452 |           |     |
| 11  | beta_2               |       | 0.5 / 3.0   | 0.75 / 2.75   |           |     |
| 12  | delta_MDT            |       | -0.5 / 1    | -0.35 / 0.85  |           |     |

#### 5.2.4 边界处理策略伪代码

代码包含了`Hard Reflection`和`Boundary-Jitter`两种策略，分别对应于优化流程中的不同阶段。

##### 策略一：Hard Reflection (用于Bootstrap+LOWO阶段)

```python
import numpy as np

def handle_boundary_hard_reflection(position, velocity, hard_boundaries):
    """
    对超出硬边界的粒子应用“硬反射”策略。

    Args:
        position (np.array): 粒子的当前位置向量。
        velocity (np.array): 粒子的当前速度向量。
        hard_boundaries (np.array): 形如 [[lb_1, ub_1], [lb_2, ub_2], ...] 的硬边界。

    Returns:
        tuple: (更新后的 position, 更新后的 velocity)。
    """
    new_position = np.copy(position)
    new_velocity = np.copy(velocity)

    for i in range(len(new_position)):
        lb, ub = hard_boundaries[i]

        if new_position[i] < lb:
            # 将位置沿下边界反射回界内
            new_position[i] = 2 * lb - new_position[i]
            # 将该维度的速度反向
            new_velocity[i] *= -1

        elif new_position[i] > ub:
            # 将位置沿上边界反射回界内
            new_position[i] = 2 * ub - new_position[i]
            # 将该维度的速度反向
            new_velocity[i] *= -1

    return new_position, new_velocity
```

##### 策略二：Boundary-Jitter (用于Fine-Tuning阶段)

```python
import numpy as np

def handle_boundary_jitter(position, narrow_hard_boundaries, parameter_sigmas):
    """
    对超出窄窗硬边界的粒子应用“边界抖动”策略。

    Args:
        position (np.array): 粒子的当前位置向量。
        narrow_hard_boundaries (np.array): Fine-Tuning阶段的窄窗硬边界。
        parameter_sigmas (np.array): 各参数在Bootstrap阶段统计得到的标准差。

    Returns:
        np.array: 更新后的 position。
    """
    new_position = np.copy(position)

    for i in range(len(new_position)):
        lb, ub = narrow_hard_boundaries[i]

        if new_position[i] < lb or new_position[i] > ub:
            # 钳位：首先将粒子拉回边界
            clamped_pos = np.clip(new_position[i], lb, ub)

            # 扰动：从边界向界内施加一个小的随机扰动
            # 扰动的标准差为参数本身标准差的10%
            jitter_std = 0.1 * parameter_sigmas[i]
            jitter = np.abs(np.random.normal(0, jitter_std))

            if new_position[i] < lb:
                # 从下边界向内（正方向）抖动
                new_position[i] = clamped_pos + jitter
            else: # new_position[i] > ub
                # 从上边界向内（负方向）抖动
                new_position[i] = clamped_pos - jitter

            # 最终校验：确保抖动后的位置仍在界内
            new_position[i] = np.clip(new_position[i], lb, ub)

    return new_position
```


### 5.3 Loss函数

为了在优化过程中实现“优先拟合高渗产层，同时确保非产层渗透率预测值不过高”的双重目标，我们设计了一套包含“加权误差”与“非对称惩罚”的复合损失函数。该函数的核心思想是，利用已知的产层指示曲线`PZI`（Productive Zone Indicator，产层为1，非产层为0）作为先验知识，对不同类型的样本施加不同的优化影响。

#### 5.3.1 Bootstrap+LOWO阶段Loss函数

在Bootstrap+LOWO阶段，需要进行全局探索，因此损失函数包含所有约束项，其完整形式如下（公式4-1）：

$$
Loss(\theta) = \underbrace{MSE_{\omega}(\theta)}_{\text{加权对数误差}} + \underbrace{\lambda_{penalty} \Omega_{nonprod}(\theta)}_{\text{非产层惩罚项}} + \underbrace{\lambda_1||\theta_{sensitive}||_2^2}_{\text{L2正则}} + \underbrace{\lambda_2\Omega_{phys}}_{\text{物理惩罚}} + \underbrace{\lambda_{MDT}(\delta_{MDT})^2}_{\text{MDT独立正则}}
$$

Loss函数由5个部分组成，下面将逐一说明。
##### 5.3.1.1 加权对数误差

第一项为加权对数误差$MSE_{\omega}(\theta)$，是损失函数的主体。它通过对数变换来弱化高渗透率样本的主导作用，并通过样本权重来聚焦于产层的拟合。公式如下（公式4-2）：

$$
MSE_{\omega}(\theta) = \frac{1}{\sum_{i=1}^{N} w_i} \sum_{i=1}^{N} w_i \left( \log_{10}(k_{NMR,i}(\theta)) - \log_{10}(k_{Label,i}) \right)^2
$$

式中：

* $N$：训练样本点个数。
* $k_{NMR,i}(\theta)$：基于$\theta$参数，利用3.7节公式计算出的模型预测渗透率，mD。
* $k_{Label,i}$：训练样本的渗透率真值（岩心或MDT渗透率），mD。
* $w_i$：为第 $i$ 个样本的权重，由其产层指示曲线`PZI`的值决定，旨在引导优化器更多地关注产层样本。具体规则如下：
    * 若 $PZI_i = 1$ （产层），则权重 $w_i = w_{prod}$，推荐值为 **1.0**。
    * 若 $PZI_i = 0$ （非产层），则权重 $w_i = w_{nonprod}$，推荐值为 **0.1**。

##### 5.3.1.2 非产层惩罚项

第二项是为解决“降低非产层权重可能导致其预测值偏高”问题而专门设计的非对称惩罚项。它的作用是为非产层的预测值设置一个“软上限”，一旦预测值超过该上限，则施加一个迅速增大的惩罚。公式如下（公式4-3）：

$$
\lambda_{penalty} \Omega_{nonprod}(\theta) = \lambda_{penalty} \sum_{i=1}^{N} (1 - PZI_i) \cdot \max(0, k_{NMR,i}(\theta) - K_{penalty\_thresh})^2
$$

式中：

* $(1 - PZI_i)$：作为惩罚开关。当样本为产层时（$PZI_i=1$），此项为0，惩罚不生效；当样本为非产层时（$PZI_i=0$），此项为1，惩罚机制启动。
* $K_{penalty\_thresh}$：惩罚阈值，代表了非产层预测渗透率可容忍的上限，推荐值为 **1.0 mD**。
* $\max(0, ...)^2$：确保只有当预测值 $k_{NMR,i}$ 超过上限时才计算惩罚，且惩罚力度随超出量的平方而增加。
* $\lambda_{penalty}$：惩罚项的权重系数，用于调节惩罚的强度，推荐初始值为 **0.5**。


##### 5.3.1.3 L2正则

第二项是L2正则，主要作用是防止指数/敏感度系数被噪声推得过大（大于物理可接受范围），保证Loss公式具有可解释性且在外推时不发散，公式如下（公式4-4）：
$$
\lambda_1 ||\theta_{sensitive}||_2^2
$$

式中：

- $\lambda_1$为权重，根据经验取0.02，轻量级即可，取值过大将削弱模型表达力，可放宽为0.01–0.05；
- $\theta_{sensitive}$ 为对模型输出影响大且容易过拟合的参数，= (PHIT_EXP, beta_1, beta_2, RHO_NMR)，T2cutoff 等偏移量参数不计入 $\theta_{sensitive}$。

**更新**: `T2LM_EXP` 和 `KMACRO_B` 也被视为敏感参数，加入到L2正则项中。因此，$\theta_{sensitive}$ = (PHIT_EXP, T2LM_EXP, KMACRO_B, beta_1, beta_2, RHO_NMR)。

##### 5.3.1.4 物理惩罚

第三项是物理惩罚，作用是强制某些参数约束在边界内，公式如下（公式4-5）：

$$
\lambda_2\Omega_{phys} = \lambda_2 \left(  \sum_{p} [\max(0, p - p_{soft\_UB})^2 + \max(0, p_{soft\_LB} - p)^2]  \right)
$$

式中：

- 双边软约束参数集合p包含如下参数：PHIT_EXP, beta_1, beta_2, RHO_NMR, log10_KSDR_A, log10_KMACRO_A, log10_T2cutoff_short, log10_T2cutoff_long, delta_MDT, Vmacro_min；参数软边界$p_{soft\_LB}$和$p_{soft\_UB}$见 4.2节表格中“Soft LB/UB”列；
- 式中，权重系数 $\lambda_2$ 在公式中为简化表达，其实际取值是一个根据参数越界严重程度动态调整的条件值，具体规则如下：
	-  **轻微越界**: 当有任意参数 $p$ 越过其软边界（$p_{soft\_UB}$ 或 $p_{soft\_LB}$），但越界幅度未超过其软、硬边界间距的50%时，置 $\lambda_2 = 5$；
	- **严重越界**: 当有任意参数 $p$ 越过其软边界的幅度超过了其软、硬边界间距的50%时，设置 $\lambda_2 = 10$；
	- 这种动态权重机制旨在对微小的、探索性的越界行为施加温和惩罚，同时对显著偏离物理合理区间的行为施加强烈惩罚。

##### 5.3.1.5 $\delta_{MDT}$独立L2正则项

岩心（CORE）样本对$\delta_{MDT}$的梯度贡献为零，导致该参数完全由MDT样本独立决定，这意味着，**只有MDT数据点在约束和优化$\delta_{MDT}$参数，而数量可能更多、质量通常更高的岩心数据对此参数的确定没有任何贡献**。如果MDT样本数量稀少或噪声较大，$\delta_{MDT}$的优化结果可能会非常不稳定或产生偏差。

解决方法是加入一个独立的L2正则项$\lambda_{MDT}(\delta_{MDT})^2$，当MDT数据点稀少或噪声大时，该惩罚项会阻止$\delta_{MDT}$取极端值，将其“拉向”0（即无偏差校正）。只有当MDT数据提供足够强的信号时，$\delta_{MDT}$才能克服惩罚项偏离0。

但是需要仔细调整$\lambda_{MDT}$的值，设置过高会过度压制$\delta_{MDT}$，导致无法校正真实的系统偏差；设置过低则效果不明显。合适的$\lambda_{MDT}$取值依赖于多种因素，特别是MDT数据的数量、质量以及它在总损失函数（Loss Function）中的相对影响。它的值需要在“信任MDT数据”和“防止过拟合”之间取得平衡，希望$\lambda_{MDT}$惩罚项的量级与$MSE_{\omega}(\theta)$项（加权对数误差项）相比，既不能小到可以忽略，也不能大到完全主导了Loss函数。通常，可以将$\lambda_{MDT}$的取值范围设置在 **0.01 到 1.0** 之间（对数尺度），目前暂时根据经验取**0.2**，相当于对$\lambda_{MDT}$的惩罚力度不强，模型将根据MDT数据来拟合$\lambda_{MDT}$。

#### 5.3.2 Fine-Tuning阶段Loss函数

在Fine-Tuning阶段，由于参数的搜索范围被限定在一个很窄的“经验窗口”内，不再需要额外的物理软边界惩罚。因此，损失函数移除物理惩罚项，形式更为简洁（公式4-6）：

$$
Loss_{FT}(\theta) = MSE_{\omega}(\theta) + \lambda_{penalty} \Omega_{nonprod}(\theta) + \lambda_1||\theta_{sensitive}||_2^2 + \lambda_{MDT}(\delta_{MDT})^2
$$

该函数保留了加权误差、非产层惩罚以及两个核心的L2正则项，以在全量数据上进行精细调优。

#### 5.3.3 Loss函数伪代码

下面的伪代码将演示了损失函数（包含Bootstrap+LOWO和Fine-Tuning两种模式）：

```python
import numpy as np

# 假设已存在函数 calculate_foster_nmr(parameters, well_data)
# from previous_step import calculate_foster_nmr

def compute_loss(parameters, train_data, mode='bootstrap'):
    """
    计算给定参数在训练数据上的损失函数值。
    该函数实现了“权重+惩罚”双重机制。

    Args:
        parameters (dict): 包含当前模型参数的字典。
        train_data (dict or pd.DataFrame): 训练集数据，必须包含 'K_LABEL' 和 'PZI' 列。
        mode (str): 计算模式，'bootstrap' 或 'finetune'。

    Returns:
        float: 计算出的总损失值。
    """
    # --- 0. 准备工作与超参数定义 ---
    lambda_1 = 0.02
    lambda_MDT = 0.2
    lambda_penalty = 0.5
    K_penalty_thresh = 1.0  # mD

    # 提取真值数据
    k_label = train_data['K_LABEL']
    pzi_truth = train_data['PZI']  # 产层指示, 1为产层, 0为非产层

    # 获取模型预测值
    k_pred = calculate_foster_nmr(parameters, train_data)

    # 为对数计算增加稳定性，防止log(0)
    epsilon = 1e-9
    k_pred_safe = np.maximum(k_pred, epsilon)
    k_label_safe = np.maximum(k_label, epsilon)

    # --- 1. 加权对数均方误差 (MSE_omega) ---
    # 根据 PZI 定义样本权重
    weights = np.where(pzi_truth == 1, 1.0, 0.1)  # 产层权重1.0，非产层权重0.1

    log_errors = np.log10(k_pred_safe) - np.log10(k_label_safe)
    weighted_squared_errors = weights * (log_errors ** 2)

    sum_of_weights = np.sum(weights)
    mse_w_term = np.sum(weighted_squared_errors) / sum_of_weights if sum_of_weights > 0 else 0.0

    # --- 2. 非产层惩罚项 (Omega_nonprod) ---
    # 惩罚开关 (1 - PZI)，当PZI=0(非产层)时，开关为1
    penalty_switch = 1 - pzi_truth
    # 计算预测值超出惩罚阈值的部分
    over_prediction = np.maximum(0, k_pred - K_penalty_thresh)
    # 计算惩罚项
    nonprod_penalty_term = lambda_penalty * np.sum(penalty_switch * (over_prediction ** 2))

    # --- 3. L2正则项 ---
    sensitive_params_keys = ['PHIT_EXP', 'T2LM_EXP', 'KMACRO_B', 'beta_1', 'beta_2', 'RHO_NMR']
    theta_sensitive = np.array([parameters[key] for key in sensitive_params_keys])
    l2_term = lambda_1 * np.sum(theta_sensitive**2)

    # --- 4. 物理边界软约束惩罚项 (仅在Bootstrap+LOWO阶段) ---
    physical_penalty_term = 0.0
    if mode == 'bootstrap':
        # 此处省略边界定义和动态lambda_2的计算逻辑，与原文4.3.1.3节一致
        # ...
        pass # 假设已完成计算

    # --- 5. delta_MDT 的独立L2正则项 ---
    delta_mdt_val = parameters['delta_MDT']
    delta_mdt_term = lambda_MDT * (delta_mdt_val**2)

    # --- 6. 加总得到最终Loss ---
    total_loss = mse_w_term + nonprod_penalty_term + l2_term + physical_penalty_term + delta_mdt_term

    return total_loss
```

### 5.4 PSO参数优化流程

基本要求如下：
-  Bootstrap+LOWO和Fine-Tuning粒子群参数初始化策略：避免正态初始化，要采用均匀分布随机采样 (U) 进行粒子初始化；对跨数量级变化的参数，先在 log10 域_上均匀采样，再指数还原到线性域；
- 采用不同的早停规则：
	- 在Bootstrap+LOWO阶段，以 10 代滑动平均 Loss 为平滑指标，连续 5 个窗口内同时满足 **RI ≤ 1.5 % 且 ΔLoss ≤ max(0.05×Loss, 5e-4)**；
	- 在Fine-Tuning阶段，以 5 代滑动平均 Loss 为平滑指标，连续 4 个窗口内同时满足 **RI ≤ 0.8 % 且 ΔLoss ≤ max(0.03×Loss, 2e-4)**；
	- 这样将形成‘外松内紧’的分层策略：先在大空间内粗筛最优区，再在窄窗口内精修，从而提高收敛效率并降低过拟合风险；
- 流程中所有算法设置相同的随机种子为2025，若在分布式环境中，需确保每个工作器同种子。

具体流程说明如下：

#### 5.4.1 Bootstrap+LOWO阶段

##### 5.4.1.1 两层循环骨架

- 外层循环：自助抽样（Bootstrap）重复$B$次，$B$取 20 次。注意注意这里使用的是一种调整了样本量的Bootstrap方法：对于每口训练井，从其总共 $N_j$ 个数据点中，**有放回地**随机抽取 $n_j = \lceil0.8\,N_j\rceil$ 个点来构成该井的训练子集。这种方法既保留了Bootstrap通过随机重采样引入扰动的优点，又通过使用略小于原样本量的样本（80%），有助于在小样本条件下增强模型的鲁棒性。下文提到的Booststrap均指的是这样抽样方法。
- 内层循环：设井数，那么每次自助抽样里，再按Leave-One-Well-Out(LOWO)轮流把$W_N$口井中的一口留作验证，其余$W_N-1$口井做训练。

这样累计就会得到 $B × W_N$组“训练-验证”组合。

##### 5.4.1.2 外层循环Bootstrap，执行_B_次，（_b_ = 1…_B_）

每一轮中外层循环执行LOWO $W_N$折交叉验证内循环，内循环针对井集合中的每一口井进行内循环$W_N$次（不包括盲井），执行5.4.1.3步操作。

##### 5.4.1.3 内层循环LOWO，执行W_N次，（f=1…）

符号定义：
- $b = 1 \dots B$：**Bootstrap** 迭代编号；
- $f = 1 \dots F$：**LOWO** 折号，$F=W_N$；
- $t = 0\dots T$：粒子群迭代引索；
- $\hat{\theta}_{b,f}^{(t)} \in \mathbb{R}^{10}$：第 b-Bootstrap、第 f-LOWO、第 t 代的 **10 维最优参数向量** 。

操作步骤如下：

1. 选择当前井作为验证井Val_Well，本折称为f折；
2. 除了验证井Val_Well之外的其余井作为训练井集合Train_Wells；
3. 按如下规则进行抽样：
	- **井级抽样**（保持井间分布）：在 LOWO 场景下，留下的 $W_N-1$ 口训练井分别处理，**绝不把不同井的数据先混合再抽样**；
	- **井内数据点的Bootstrap抽样**：设第 $j$ 口井原始深度点数为 $N_j$，生成样本数 $n_j = \lceil0.8\,N_j\rceil$，在其深度索引集合上 **有放回** 随机抽取 $n_j$ 个索引，抽完后按深度升序重排，得到序列 $\mathcal{D}^{\ast}_j$；
	- 合并训练集：把所有井的新序列 $\{\mathcal{D}^{\ast}_j\}_{j=1}^{W_N-1}$ 拼接，即为本次 Bootstrap-LOWO 的训练集，称为Train_Boostrap_Wells；
	- 验证集保持不变：LOWO 留出的那口井全井深度数据不做任何采样，作为验证集。
4. 在Train_Boostrap_Wells上运行PSO:
    - 采用固定样本权重，w_core=0.8, w_MDT=0.2；
    - PSO 搜索10个参数, Loss(θ)函数形式如4.3.1节公式8所示；
    - 在Train_Boostrap_Wells上计算适应度(fitness)，即适应度(fitness)= Loss(θ) on Train_Boostrap_Wells，Loss(θ)作为粒子当前好坏的唯一评价，驱动粒子更新，搜索结束后得到折内最优参数集θ_hat_b,f（$\hat{\theta}_{b,f}$），θ_hat_b,f需要保存供后续统计使用；
    - PSO参数设定如下：
        - 粒子数P1：根据经验取100，≈10×参数维度；
        - 最大迭代T1：取300，触发Early-Stopping可提前终止；
        - 惯性权重ω(t)：采用线性衰减，ω_max=0.9→ω_min=0.4，保证探索-收敛平衡，公式见后述：
        - 学习因子c1, c2：分别为1.8、1.8，保持对称；
        - 粒子越界处理策略：Hard Reflection。
    - PSO早停判定相关公式定义：
	    - 定义窗口参数： **窗口长度** $W=10$ 代，**连续窗口数** $K=5$（对应最近 $W\times K=50$ 代）；$j$为窗口整数索引，取值$j = 1, 2, 3, \dots$，下文$t$仅取$t=jW$，检查逻辑每完成 W 代执行一次
	    - 定义第t代粒子在长度为W的滑动窗口中的平均损失（仅在 $t=jW$ 时定义）：$L_t=\overline{Loss}_{t}^{W} = \frac{1}{W}\sum_{k=t-W+1}^{t} Loss_k$
	    - 定义平均损失的动态绝对下降量：$\Delta L_{t} = \Delta Loss_{LOWO,t} = \max(0,L_{t-W}-L_{t})$
	    - 定义平均损失的相对改善率$RI_{t}$为窗口首尾平均损失的相对差，这里的‘窗口首尾两点’ 均指 长度 W 滑动窗口的末端均值：$RI_{t} = \frac{\Delta L_{t}}{L_{t-W}}$
	    - 定义阀值：$\text{DynThresh}_{LOWO}=0.05\times L_{t}$，$\text{AbsFloor}=5\times10^{-4}$
	- PSO早停判定：
		- 若在 **迭代数 $\ge W\times K$  代** 时，若最近 **$K$ 个窗口**的$RI_{t}$ 全部 $\le1.5\%$且各窗口的 $\Delta L_{t}$ 均满足$\Delta L_{t}\;\le\;\max\!\bigl(\text{DynThresh}_{\text{LOWO}},\;\text{AbsFloor}\bigr)$，则提前终止，否则继续迭代，直至达到最大外层迭代数 T1。
		- 说明：**动态阈值** $\text{DynThresh}_{\text{LOWO}}$ 随损失量级缩放，高于 $\text{AbsFloor}$ 时采用比例阈值、低于 $\text{AbsFloor}$ 时退化为固定阈值，避免在噪声平台长时间停滞或永不满足条件；“连续 $K$ 个窗口”采用 **滑动** 检查：每完成 $W$ 代更新一次判断，并覆盖最近 $50$ 代。
	    - 若提前终止代并非全程最低 Loss，则回滚到历史最优参数 θ；
	- 需要额外保存 **每 10 代一次**的$\hat{\theta}_{b,f}^{(t)}$，以便后续用来做可视化收敛轨迹。
5. 将θ_hat_b,f用于验证集Val_Well计算该折RMSE_Val_b,f（$RMSE\_Val_{b,f}$），注意：RMSE_Val_b,f不反哺适应度，不参与粒子下一轮更新。

上述PSO搜索中的惯性权重ω(t)公式如下（公式4-6）：
$$
\omega(t) = \omega_{max} - (\omega_{max} - \omega_{min}) \frac{t}{T}
$$
式中t为粒子群当前迭代编号，0-T。

RMSE_Val_b,f计算公式如下（公式4-7）：
$$
RMSE\_Val_{b,f} = \sqrt{\frac{\sum_{i=1}^{N} w_i (\log_{10} K_{NMR,i}(\hat{\theta}_{b,f}) - \log_{10} K_{Label,i})^2}{\sum_{i=1}^{N} w_i}}
$$
式中N为验证集的样本点个数。

完成W_N折之后，可求出本轮自助抽样的平均验证误差μ_RMSE_b（公式4-8）：
$$
\mu\_RMSE_b = \frac{1}{W_N} \sum_{f=1}^{W_N} RMSE\_Val_{b,f}
$$
#### 5.4.2 Fine-Tuning阶段
##### 5.4.2.1 统计分析与Warm-Start参数选择

在完成Bootstrap+LOWO阶段的全部 $B \times W_N$ 次训练-验证循环后，获得了以下两组关键结果：
* $B$ 个平均验证误差值：$\{ {\mu\_RMSE_b} \}_{b=1,\dots,B}$
* $B \times W_N$ 组对应的最优参数向量：$\Theta^{\ast}=\{\hat\theta_{b,f}\}_{b=1,\dots,B}^{f=1,\dots,W_N}$

接下来，基于这些结果执行统计分析，并为下一阶段选择一个稳健的热启动（Warm-Start）起点。

**操作步骤如下：**

1.  **泛化误差统计**：对 $B$ 轮Bootstrap得到的平均验证误差序列 $\{ {\mu\_RMSE_b} \}$ 计算其均值和标准差。这可以评估出模型整体的期望泛化误差水平及其稳定性。

2.  **参数置信区间计算**：采用百分位法（Percentile Bootstrap），对所有 $B \times W_N$ 组参数构成的集合 $\Theta^{\ast}$ 进行分析。对10个参数中的每一个，分别计算其2.5%与97.5%分位数，从而获得每个模型参数的95%置信区间（95% CI）。该区间反映了由数据随机性（采样和井划分）带来的参数不确定性范围。

3.  **稳健的Warm-Start参数选定**：为避免因选择单次最佳结果（“樱桃挑选”）而引入的偶然性，采用一种更稳健的策略来确定Fine-Tuning阶段的热启动参数点 $\hat{\theta}^{\ast}$。

    a. **确定最佳Bootstrap轮次**：首先，在 $B$ 个平均验证误差中找到最小值，确定其对应的“最佳”Bootstrap轮次编号 $b^*$。这一轮次的模型被认为在整体上具有最优的泛化表现。
    $$
    b^* = \arg\min_b(\mu\_RMSE_b)
    $$
    b. **计算平均参数作为起点**：接下来，取上一步确定的最佳轮次 $b^*$ 中，所有 $W_N$ 折交叉验证得到的最优参数向量 $\{ \hat{\theta}_{b^*,f} \}_{f=1,\dots,W_N}$，对它们的10个分量进行**逐项平均 (component-wise average)**。将得到的平均向量作为最终的热启动点 $\hat{\theta}^{\ast}$。
    $$
    \hat{\theta}^{\ast} = \frac{1}{W_N} \sum_{f=1}^{W_N} \hat{\theta}_{b^*,f}
    $$
    该方法综合了模型在该轮最佳训练中应对不同验证井时的表现，使得 $\hat{\theta}^{\ast}$ 作为一个起点，更为平衡且具有代表性。

    c. **中位数法 (备选方案)**：如果担心少数几折的优化结果是严重偏离的异常值（outliers），可采用中位数法代替平均值法。即计算最佳轮次 $b^*$ 中所有参数向量的**逐项中位数**作为 $\hat{\theta}^{\ast}$，这种方法对异常值的鲁棒性更强。

##### 5.4.2.2 Fine-Tuning：全量窄窗PSO

将$W_N$口井的数据全部作为训练集 `Train_All_Wells`，此阶段无独立验证集。采用的损失函数为4.3.2节定义的$Loss_{FT}(\theta)$ (公式12)。全量数据训练的关键步骤如下：

1.  **确定搜索起点与范围**
    以5.4.2.1节中确定的Warm-Start参数向量 $\hat{\theta}^{\ast}$ 作为粒子群的初始化中心。在由该起点定义的窄窗口内再次运行PSO进行精细调优。窄窗口的边界定义为 $\hat{\theta}^{\ast} \pm k \cdot \sigma$，其中：
    * $\sigma_p = \operatorname{StdDev}\bigl(\{\hat\theta_{b,f}^{(p)}\}\bigr)$，是第 $p$ 个参数在整个Bootstrap+LOWO阶段得到的样本标准差，代表其经验波动幅度。
    * $k$ 是控制窗口宽度的系数，典型取值为0.3。

2.  **PSO关键参数设定**
    此阶段的PSO参数设定旨在实现局部精细搜索：
    * **样本权重**：固定样本权重，与评估阶段一致，$w_{core}=0.8, w_{MDT}=0.2$。
    * **粒子数 $P2$**：设为60，约为评估阶段的一半，以降低计算量并聚焦于局部搜索。
    * **粒子初始化**：所有粒子在步骤1定义的窄窗口搜索区间内，围绕Warm-Start起点 $\hat{\theta}^{\ast}$ 集中分布进行初始化。
    * **最大迭代次数 $T2$**：设为100。
    * **惯性权重 $\omega(t)$**：仍采用线性衰减，但范围收紧以促进收敛，$\omega_{max}=0.8 \to \omega_{min}=0.3$。
    * **学习因子 $c1, c2$**：设为1.6、1.6，采用更保守的值以稳定收敛过程。
    * **粒子越界处理策略**：采用**Boundary-Jitter**策略。

3.  **PSO早停判定相关公式定义**
    此阶段的早停判定采用更严格的“内紧”策略，以在窄窗口内实现精细收敛。
    * **窗口参数**：窗口长度 $W_{FT}=5$ 代，连续窗口数 $K_{FT}=4$。
    * **滑动窗口平均损失**：$L_t=\overline{Loss}_{t}^{W_{FT}} = \frac{1}{W_{FT}}\sum_{k=t-W_{FT}+1}^{t} Loss_k$。
    * **平均损失的动态绝对下降量**：$\Delta L_{t} = \Delta Loss_{FT,t} = \max(0,L_{t-W_{FT}}-L_{t})$。
    * **平均损失的相对改善率**：$RI_{t} = \frac{\Delta L_{t}}{L_{t-W_{FT}}}$。
    * **阈值**：$\text{DynThresh}_{FT}=0.03\times L_{t}$，$\text{AbsFloor}_{FT}=2\times10^{-4}$。

4.  **PSO早停判定与最终参数的确定**
    * **触发条件**：
        若在迭代数 $\ge W_{FT}\times K_{FT}$ 代时，最近连续 $K_{FT}$ 个窗口的相对改善率 $RI_{t}$ 全部 $\le0.8\%$ **且** 各窗口的绝对下降量 $\Delta L_{t}$ 均满足 $\Delta L_{t}\;\le\;\max(\text{DynThresh}_{\text{FT}},\;\text{AbsFloor}_{FT})$，则提前终止优化流程。否则，继续迭代直至达到最大迭代数 $T2$。
    * **最终结果的确定**：
        在整个Fine-Tuning的迭代过程中，算法会持续追踪并内部保存一个**历史最优参数向量**，即在所有已完成的迭代中，使损失函数 $Loss_{FT}(\theta)$ 达到最小值的那个参数向量。当满足早停条件或达到最大迭代次数而终止时，**流程将直接返回这个被记录的“历史最优参数向量”作为最终的优化结果 $\theta_{final}$**。该机制确保输出的始终是整个搜索过程中的最佳解。

5.  **过程记录**
    每10代保存一次当前最优参数，以便输出$\Delta Loss$收敛曲线图，并为最终报告提供Early-Stopping点的记录。

6.  **最终输出**
    该阶段结束后，得到最终用于部署的参数向量 $\theta_{final}$。

#### 5.4.3 早停判断逻辑伪代码

该伪代码将复杂的、分阶段的早停逻辑封装成一个独立的函数。函数可通过`mode`参数切换“外松内紧”两种不同的判断标准。

```python
import numpy as np

def check_early_stopping(loss_history, current_iteration, mode='lowo'):
    """
    根据历史损失和当前迭代次数，判断是否满足早停条件。

    Args:
        loss_history (list or np.array): 从开始到现在的损失值历史记录。
        current_iteration (int): 当前的迭代次数。
        mode (str): 判断模式，'lowo' (外松) 或 'finetune' (内紧)。

    Returns:
        bool: 如果满足早停条件，则返回 True，否则返回 False。
    """
    # --- 1. 根据模式选择判断参数 ---
    if mode == 'lowo':
        # Bootstrap+LOWO阶段的“外松”策略
        window_length = 10         # W
        consecutive_windows = 5    # K
        ri_threshold = 0.015       # 1.5%
        dyn_thresh_factor = 0.05
        abs_floor = 5e-4
    elif mode == 'finetune':
        # Fine-Tuning阶段的“内紧”策略
        window_length = 5          # W_FT
        consecutive_windows = 4    # K_FT
        ri_threshold = 0.008       # 0.8%
        dyn_thresh_factor = 0.03
        abs_floor = 2e-4
    else:
        raise ValueError("mode 必须是 'lowo' 或 'finetune'")

    # --- 2. 检查是否达到可判断的最小迭代次数 ---
    min_iterations = window_length * consecutive_windows
    if current_iteration < min_iterations:
        return False

    # --- 3. 检查最近K个窗口是否“连续”满足停止条件 ---
    for k in range(consecutive_windows):
        # 定义当前窗口和上一个窗口的索引范围
        # k=0 是最近的窗口, k=1是次近的, ...
        end_idx_current = len(loss_history) - (k * window_length)
        start_idx_current = end_idx_current - window_length

        end_idx_prev = start_idx_current
        start_idx_prev = end_idx_prev - window_length

        if start_idx_prev < 0:
            # 历史记录不足以形成更早的窗口
            return False

        # 计算窗口平均损失
        loss_current_window = np.mean(loss_history[start_idx_current:end_idx_current])
        loss_prev_window = np.mean(loss_history[start_idx_prev:end_idx_prev])

        # 计算绝对下降量 (delta_L) 和相对改善率 (RI)
        absolute_drop = max(0, loss_prev_window - loss_current_window)
        relative_improvement = absolute_drop / loss_prev_window if loss_prev_window > 1e-9 else 0

        # 计算动态停止阈值
        dynamic_threshold = dyn_thresh_factor * loss_current_window
        stop_threshold = max(dynamic_threshold, abs_floor)

        # 判断当前窗口是否满足双重条件
        is_stagnant = (relative_improvement <= ri_threshold) and \
                      (absolute_drop <= stop_threshold)

        if not is_stagnant:
            # 只要有一个窗口不满足，则立即返回False
            return False

    # 如果所有K个窗口都满足了停滞条件，则返回True
    return True
```

#### 5.4.4 主控流程框架伪代码

该伪代码勾勒了`SWIFT-PSO`的顶层控制逻辑，分为两个主要阶段：**Bootstrap+LOWO** 和 **Fine-Tuning**。它清晰地展示了数据如何被划分、嵌套循环如何执行、以及各个独立的模块（如PSO、Loss计算、边界处理等）如何被协同调用。

```python
import numpy as np

# 假设已存在以下函数:
# pso_optimizer(train_data, config): 一个执行PSO优化的函数
# calculate_rmse(k_pred, k_label): 计算均方根对数误差的函数
# ...以及之前定义的其他辅助函数

def run_bootstrap_lowo_phase(all_wells_data, config):
    """
    执行SWIFT-PSO的第一阶段：Bootstrap + LOWO 双重循环。

    Args:
        all_wells_data (dict): 包含所有井数据的字典 {well_id: data}。
        config (dict): 包含流程所需配置的字典 (如 B, 优化参数等)。

    Returns:
        tuple: (all_theta_hats, all_mu_rmse_per_b, all_thetas_per_b_star)
               - all_theta_hats: 所有 B*W_N 次运行得到的最优参数集列表。
               - all_mu_rmse_per_b: 每轮Bootstrap的平均验证误差列表。
               - all_thetas_per_b_star: 最佳轮次B*对应的W_N组参数。
    """
    B = config['bootstrap_iterations']
    well_ids = list(all_wells_data.keys())
    W_N = len(well_ids)

    all_theta_hats = []
    all_mu_rmse_per_b = []

    # 外层循环: Bootstrap
    for b in range(B):
        print(f"--- Bootstrap-Runde {b+1}/{B} ---")
        current_bootstrap_val_errors = []
        current_bootstrap_thetas = []

        # 内层循环: Leave-One-Well-Out (LOWO)
        for f in range(W_N):
            # 1. 划分训练集和验证集
            val_well_id = well_ids[f]
            train_well_ids = [wid for wid in well_ids if wid != val_well_id]

            validation_set = all_wells_data[val_well_id]

            # 2. 对训练集进行井内Bootstrap抽样
            train_bootstrap_set = []
            for train_id in train_well_ids:
                well_data = all_wells_data[train_id]
                n_j = int(np.ceil(0.8 * len(well_data)))
                # 有放回地抽样
                bootstrap_indices = np.random.choice(well_data.index, size=n_j, replace=True)
                train_bootstrap_set.append(well_data.loc[bootstrap_indices])

            # 合并成最终的训练数据
            training_set = pd.concat(train_bootstrap_set)

            # 3. 运行PSO优化器
            # config_lowo 应包含 'lowo' 模式的早停、边界处理等参数
            theta_hat_b_f = pso_optimizer(training_set, config['pso_config_lowo'])

            # 4. 在验证集上评估并存储结果
            k_pred_val = calculate_foster_nmr(theta_hat_b_f, validation_set)
            rmse_val = calculate_rmse(k_pred_val, validation_set['K_LABEL'])

            all_theta_hats.append(theta_hat_b_f)
            current_bootstrap_thetas.append(theta_hat_b_f)
            current_bootstrap_val_errors.append(rmse_val)

        # 5. 计算本轮Bootstrap的平均验证误差
        mu_rmse_b = np.mean(current_bootstrap_val_errors)
        all_mu_rmse_per_b.append({'mu_rmse': mu_rmse_b, 'thetas': current_bootstrap_thetas})

    # 找到最佳轮次 (b_star) 及其对应的参数
    best_run = min(all_mu_rmse_per_b, key=lambda x: x['mu_rmse'])
    all_thetas_per_b_star = best_run['thetas']

    return all_theta_hats, all_mu_rmse_per_b, all_thetas_per_b_star


def run_finetuning_phase(all_wells_data, all_theta_hats, all_thetas_per_b_star, config):
    """
    执行SWIFT-PSO的第二阶段：Warm-Start 与 Fine-Tuning。

    Args:
        all_wells_data (dict): 包含所有井数据的字典。
        all_theta_hats (list): 第一阶段产出的所有参数集。
        all_thetas_per_b_star (list): 第一阶段最佳轮次产出的W_N个参数集。
        config (dict): 流程配置。

    Returns:
        dict: 最终优化得到的参数向量 a_final。
    """
    # 1. 计算Warm-Start参数 (theta_hat_star)
    # 采用最佳轮次(b_star)中W_N折参数的逐项平均值
    theta_hat_star = np.mean(all_thetas_per_b_star, axis=0)

    # 2. 定义窄窗搜索边界
    # 计算所有参数在第一阶段的整体标准差
    parameter_sigmas = np.std(all_theta_hats, axis=0)
    k = config['narrow_window_factor'] # k=0.3
    narrow_boundaries = np.array([(p - k * sigma, p + k * sigma) for p, sigma in zip(theta_hat_star, parameter_sigmas)])

    # 3. 准备全量训练数据
    full_training_set = pd.concat(all_wells_data.values())

    # 4. 运行Fine-Tuning PSO
    # config_ft 应包含 'finetune' 模式的早停、边界处理、窄窗边界、初始粒子中心等
    config['pso_config_finetune']['narrow_boundaries'] = narrow_boundaries
    config['pso_config_finetune']['initial_center'] = theta_hat_star
    config['pso_config_finetune']['parameter_sigmas'] = parameter_sigmas # 用于Jitter策略

    theta_final = pso_optimizer(full_training_set, config['pso_config_finetune'])

    return theta_final

def run_swift_pso(all_wells_data, config):
    """
    SWIFT-PSO主控函数。
    """
    # --- 阶段一 ---
    all_theta_hats, all_mu_rmse_per_b, thetas_b_star = run_bootstrap_lowo_phase(all_wells_data, config)

    # --- 阶段二 ---
    theta_final = run_finetuning_phase(all_wells_data, all_theta_hats, thetas_b_star, config)

    print("SWIFT-PSO 优化完成。")
    return theta_final
```

#### 5.4.5 Bootstrap+LOWO阶段θ演化轨迹t-SNE可视化

##### **目标**

此步骤旨在通过t-SNE（t-distributed Stochastic Neighbor Embedding）降维技术，将`Bootstrap+LOWO`阶段高维度的参数优化轨迹进行可视化。其核心目标是直观地观察和分析由外层采样不确定性（Bootstrap）和留一井差异（LOWO）共同导致的最优参数向量 $\hat{\theta}_{b,f}$ 的收敛分布特征。通过该可视化，可以清晰地识别全局收敛方向、参数簇结构以及潜在的离群行为。

##### **数据准备与预处理**

###### **数据采集**

在`Bootstrap+LOWO`阶段的每一次独立优化运行中，即针对每一个($b,f$)组合，需在优化过程中按固定代数间隔（例如，每10代）保存一次当前的全局最优参数向量 $\hat{\theta}_{b,f}^{(t)}$，并记录其对应的代数 $t$。

###### **参数预处理**

为确保t-SNE降维的有效性和结果的准确性，所有采集到的参数向量必须经过严格的预处理。

* **对数变换 (Log10域统一)**：对于物理上呈乘幂或跨数量级变化的参数（如$KSDR\_A$、$KMACRO\_A$、$T2cutoff$等），需先应用以10为底的对数变换，将其映射到线性可比较的空间。
* **全局标准化 (Z-score Standardization)**：必须对所有10个参数维度（包括已完成对数变换的维度）执行全局Z-score标准化。
    1.  **数据汇集**：将`Bootstrap+LOWO`阶段保存的**所有**最优参数向量 $\{\hat{\theta}_{b,f}^{(t)}\}$ 汇集起来，形成一个完整的、用于可视化的数据集。
    2.  **计算全局统计量**：针对该完整数据集，计算出每一个参数维度 $k$ 的**全局均值** $\mu_k$ 和**全局标准差** $\sigma_k$。
    3.  **应用全局变换**：使用上一步计算出的**同一套全局统计量**，对数据集中的**每一个**参数向量进行Z-score变换，确保所有点都被缩放到同一基准上。
        $$\theta'_{k} = \frac{\theta_{k}-\mu_k}{\sigma_k}, \quad k=1\ldots10 \quad \text{}$$

##### **t-SNE降维与轨迹可视化**

###### **降维计算**

将预处理后的高维参数向量集合输入t-SNE算法，将其投影到二维空间，为每个向量生成坐标 $(z_1, z_2)$。算法的关键超参数设定建议如下：
* `perplexity`: 设为 5~10，经验值约为$\sqrt{N}$，其中$N$为样本总数。
* `learning_rate`: 设为 'auto' 或 200~400。
* `init`: 设为 'pca'，此初始化方式有助于减少陷入局部极值的风险。
* `n_iter`: 迭代次数不应低于1000，建议取2000以保证算法充分收敛。
* `random_state`: 设定为固定值（如2025），以保证结果的可复现性。

###### **收敛流线图绘制**

* 对于同一个($b,f$)组合，按优化代数 $t$ 的时间顺序，将其对应的二维坐标点依次连接，形成一条**收敛流线**。
* 使用视觉编码进行区分：以**颜色**区分Bootstrap编号 $b$，以**标记形状**或色调深浅区分LOWO折号 $f$。
* 在流线的起点绘制“×”，终点绘制“○”，以直观判断收敛过程。
* **图形解读**：通过观察流线图，可以判断多条流线是否最终汇聚到少数几个簇，寻找可能提示异常信息的离群流线，并根据流线的弯折情况分析优化策略对搜索路径的影响。

##### **聚类分析与量化评估**

为定量评估最终参数解的稳定性，可对`Bootstrap+LOWO`阶段最终得到的参数点集进行自动分簇和指标度量。

###### **分析策略**

* 聚类的**度量**（如Silhouette等定量指标）应在**10维Z-score特征空间**($\Theta'$)上进行，以保证度量的准确性。
* 聚类的**展示**（如着色、画凸包）则在**2维t-SNE投影平面**($Z$)上进行，以求直观。

###### **聚类执行与评估**

* **算法选择**：可同时尝试**K-means**和**DBSCAN**算法，其中DBSCAN能有效识别离群点。
* **超参设定**：对K-means，可扫描$K$=2–10；对DBSCAN，可通过 _k-distance_ 曲线选择`eps`，`min_samples`可设为15。
* **质量评估**：计算**Silhouette**（越高越好，>0.5为佳）、**Davies–Bouldin**（越低越好，<1为佳）和**Calinski–Harabasz**（越高越好）等指标。
* **稳定性检验**：通过**Adjusted Rand Index (ARI)** 或 **Normalized Mutual Info (NMI)** 检验簇结构的稳健性，若ARI/NMI > 0.8则表示结构稳健。
* **物理一致性检验**：将各簇的参数逆变换回原始物理量纲，统计其均值和方差，并进行显著性检验（如t-test）。
* **交叉验证**：可在2维的$Z$点集上再次运行DBSCAN，并与在10维$\Theta'$上聚类的结果计算ARI；若一致性高（≥ 0.8），则增加结果的可信度。

###### **结果报告与可视化**

* 以列表形式报告簇数、各项质量评估指标、离群点数和稳定性指标。
* 在t-SNE图中，根据簇标签对点进行着色、绘制凸包，并连接收敛轨迹。
* 以表格形式展示每个簇的参数在原始物理量纲下的统计特征。

### 5.5 PLT盲井检验

以下称“核磁共振渗透率计算模型+θ_final参数”在PLT盲井上计算得到的渗透率为模型预测渗透率K_pred（$k_{pred}$），此步共有7个操作，说明如下：

#### 5.5.1 计算层段预测强度与代表性渗透率

此步骤的目的是将模型输出的逐点渗透率预测曲线 $k_{pred}(d)$，转换为每个PLT（生产剖面测井）解释层段的两个关键指标：
1.  **层段预测强度 ($S_{pred,i}$)**：代表该层段的总流动能力，本质上是模型预测的地层系数 ($kh$ 乘积)，用于与分层绝对产量 $Q_i$ 进行能量级对比。
2.  **层段代表性渗透率 ($\bar{k}_{pred,i}$)**：代表该层段的平均渗透率水平，作为一个标准化的“产能效率”指标，将用于后续的排序、Top-K分析和捕获曲线评估。

根据输入数据定义，测井曲线的深度采样间隔 $\Delta d$ 是一个固定值，默认为0.1524m。计算步骤如下：

1.  **遍历所有PLT层段**
    对于PLT解释出的 $PLT_N$ 个产层中的第 $i$ 个层段 ($Zone_i$)，其顶界为 $Top_i$，底界为 $Bottom_i$。

2.  **判断层段内数据点并分类计算**
    根据深度区间 $[Top_i, Bottom_i]$ 内是否存在模型预测的渗透率数据点 $k_{pred}$，分两种情况进行计算：

    * **情况A：层段内有一个或多个数据点**
        这是最常见的情况。我们分别计算 $S_{pred,i}$ 和 $\bar{k}_{pred,i}$：

        * **计算层段预测强度 ($S_{pred,i}$)**：采用**离散积分**的方法，将层段内所有数据点的渗透率贡献进行累加，该方法能精确反映层段内渗透率的变化对总产能的贡献。
            $$
            S_{pred,i} = \Delta d \cdot \sum_{d \in [Top_i, Bottom_i]} k_{pred}(d)
            $$

        * **计算层段代表性渗透率 ($\bar{k}_{pred,i}$)**：采用**算术平均值**，以获得表征该层段整体渗透率水平的单一值。
            $$
            \bar{k}_{pred,i} = \frac{1}{N_i} \sum_{d \in [Top_i, Bottom_i]} k_{pred}(d)
            $$
            式中，$N_i$ 是落在该层段内的测井数据点总数。

    * **情况B：层段内无任何数据点**
        这种情况通常发生在厚度极薄的层段，其整个区间恰好落在了两个测井采样点之间。此时，我们采用**最近邻插值法**来估算其指标。

        1.  计算该薄层的中点深度 $d_{mid} = (Top_i + Bottom_i) / 2$。
        2.  在整个井的渗透率预测曲线 $k_{pred}$ 上，寻找与 $d_{mid}$ 深度**最接近**的一个有效数据点，将其渗透率值定义为 $k_{closest}$。
        3.  基于 $k_{closest}$ 计算两个指标：
            * **定义层段代表性渗透率 ($\bar{k}_{pred,i}$)**：将最近邻点的渗透率作为该薄层的代表性渗透率。
                $$
                \bar{k}_{pred,i} = k_{closest}
                $$
            * **计算层段预测强度 ($S_{pred,i}$)**：使用该代表性渗透率乘以该薄层的实际厚度 $h_i$。
                $$
                S_{pred,i} = \bar{k}_{pred,i} \cdot (Bottom_i - Top_i)
                $$

3.  **获得最终结果**
    完成上述步骤后，将得到两个与 $PLT_N$ 个产层一一对应的序列：
    * 预测强度序列：{$S_{pred,1}, S_{pred,2}, ..., S_{pred,PLT_N}$}
    * 代表性渗透率序列：{$\bar{k}_{pred,1}, \bar{k}_{pred,2}, ..., \bar{k}_{pred,PLT_N}$}

#### 5.5.2 计算相对贡献

计算模型预测相对贡献R_pred（$R_{pred}$）和PLT相对贡献R_PLT（$R_{PLT}$），目的是消除绝对量纲差异，把评估聚焦在相对高低，公式如下（公式19和公式20）：
$$
R_{pred,i} = \frac{S_{pred,i}}{\sum_{j=1}^{PLT_N} S_{pred,j}}
$$
$$
R_{PLT,i} = \frac{Q_i}{\sum_{j=1}^{PLT_N} Q_j}
$$
式中：
- R_pred_i为模型预测第i层在全井产层中的占比；
- R_PLT_i为PLT第i层产量在全井中的占比；
- Q_i为PLT第i层产量，单位b/d；
- PLT_N为PLT解释出的产层总层数。

#### 5.5.3 相关性检验

判断模型预测R_pred和R_PLT单调一致性。

- 计算**Pearson R**（绝对强度）；
- 计算**Spearman ρ**（排序相关）；
- 做R_pred和R_PLT带1:1线的交会图(Cross-plot)。

R, ρ ≥ 0.70说明较好。

#### 5.5.4 误差计算

基于R_pred和R_PLT的做误差计算。

- MAE、MAPE、RMSE等。

#### 5.5.5 Top-K 法 + Capture 曲线

对于模型泛化能力的检验，尤其是评估其在工程决策中优选高产层段的能力，采用Top-K排序优选法（结合捕获曲线）是一种更稳健、更贴合实际的数据驱动评估方式。此方法的核心在于衡量“投入一定比例的资源（如总厚度），能捕获多少比例的目标（如高产储层）”。以下是以厚度作为权重资源的详细计算步骤：

**1. 数据准备**
* **模型预测指标**：采用在**章节5.5.1**中为每个层段计算的**代表性渗透率** $\bar{k}_{pred,i}$ 作为模型对该层段产能潜力的预测排序依据。
* **真值衡量标准**：采用PLT层段的**真实产量密度** $q_i = \frac{Q_i}{h_i}$ 作为衡量各层段实际产能效率的“真值”。

**2. 确定“真高产层”标签**
* **确定高产阈值**：计算所有PLT层段产量密度 $q_i$ 的一个高分位数，例如P70（第70百分位），作为判定高产层的阈值 $\tau_{true}=P_{70}(\{q_i\})$。
* **生成真值标签 ($I_{true,i}$)**：为每个层段生成一个二进制标签，1代表高产层，0代表非高产层。
    $$I_{true,i}=\begin{cases}1, & \text{if } q_i \ge \tau_{true} \\0, & \text{otherwise}\end{cases}$$

**3. 按模型预测排序并计算累积坐标点**
* **排序**：将所有 $PLT_N$ 个产层，按照模型预测的**代表性渗透率 $\bar{k}_{pred,i}$ 进行降序排列**，得到排序后的层段序列 $i_{(1)}, i_{(2)}, \dots, i_{(PLT_N)}$。
* **计算累积值**：从 $n=1$ 到 $PLT_N$ 遍历排序后的序列，计算累积厚度 $H_n$ 和累积高产厚度 $H^{true}_n$。同时，计算总厚度 $H_{total}$ 和总高产厚度 $H^{true}_{total}$。
    * $H_n = \sum_{k=1}^{n} h_{i_{(k)}}$
    * $H^{true}_n = \sum_{k=1}^{n} h_{i_{(k)}} \cdot I_{true,i_{(k)}}$
    * $H_{total} = \sum_{j=1}^{PLT_N} h_j$
    * $H^{true}_{total} = \sum_{j=1}^{PLT_N} h_j \cdot I_{true,j}$
* **归一化生成曲线节点**：将累积值归一化，得到从 $(x_0, y_0)=(0,0)$ 到 $(x_{PLT_N}, y_{PLT_N})=(1,1)$ 的一系列离散点 $(x_n, y_n)$。
    * $x_n = H_n / H_{total}$
    * $y_n = H^{true}_n / H^{true}_{total}$
    将这些节点依次连接，便构成了捕获曲线。曲线越向左上方凸起，说明模型的排序效果越好。

**4. 计算关键评估指标 (包含插值修正)**
基于生成的捕获曲线点集 $\{(x_n, y_n)\}$，计算以下核心指标：

* **Top-$\alpha$ Capture ($CR^{thk}_{\alpha}$)**：衡量当投入 $\alpha$ 比例的总厚度资源时，模型能成功捕获的高产厚度的百分比。由于 $\alpha$（如0.3）通常不会恰好等于某个 $x_n$，采用**线性插值法**精确计算：
    1.  找到使得 $x_{n-1} \le \alpha < x_n$ 的两个相邻节点 $(x_{n-1}, y_{n-1})$ 和 $(x_n, y_n)$。
    2.  应用线性插值公式计算 $CR^{thk}_{\alpha}$：
        $$
        CR^{thk}_{\alpha} = y_{n-1} + (y_n - y_{n-1}) \cdot \frac{\alpha - x_{n-1}}{x_n - x_{n-1}}
        $$
    常用的评估点为 $\alpha=0.30$ 和 $\alpha=0.50$。

* **捕获曲线下面积 ($AUC_{Capture}$)**：计算捕获曲线下方的总面积，反映模型在所有可能资源预算下的整体排序性能。取值范围为0.5（随机）到1.0（理想）。
    $$
    AUC_{Capture} = \sum_{n=1}^{PLT_N} \frac{(y_n + y_{n-1})}{2} \cdot (x_n - x_{n-1})
    $$

* **捕获曲线基尼系数 (Capture Gini)**：将AUC归一化到[0, 1]区间的指标，更直观地表示排序的优越性。Gini=0代表随机，Gini=1代表理想。
    $$
    G = 2 \cdot AUC_{Capture} - 1
    $$

* **Lift@$\alpha$**：衡量在投入 $\alpha$ 资源时，模型的命中效率相对于随机选择的提升倍数。
    $$
    \text{Lift}_{\alpha} = \frac{CR^{thk}_{\alpha}}{\alpha}
    $$
    例如，若 $\text{Lift}_{30\%} = 2.5$，意味着模型优选的前30%厚度的储层，其捕获高产层的效率是随机选择的2.5倍。
#### 5.5.6 层序累积贡献评估（洛伦兹曲线法）

##### **目标**

层序累积贡献评估旨在量化评价模型预测的各产层贡献度排序与真实产液量排序之间的一致性。此方法通过绘制洛伦兹曲线（Lorenz Curve）并计算其基尼系数（Gini Coefficient），从全局视角衡量模型对所有产层产能分配的准确度。

该评估与章节5.5.5中的Top-K捕获曲线分析形成互补：捕获曲线聚焦于模型以最少资源（如厚度）捕获高产目标的能力，而洛伦兹曲线则评估模型在所有层段上的整体排序质量与贡献错配程度。

##### **计算步骤**

###### **数据准备**

对于生产测井（PLT）解释出的全部 $PLT_N$ 个产层（$Zone_i$），需要准备以下三组数据：
* 层段代表性渗透率（$\bar{k}_{pred,i}$）：由模型计算得出的各产层代表性渗透率，用作排序依据。
* 层段预测强度（$S_{pred,i}$）：由模型预测的各产层流动能力，代表预测贡献。
* 层段产量（$Q_i$）：由PLT解释得到的各产层真实产量，代表真实贡献。

###### ** 层段排序**

将所有 $PLT_N$ 个产层，依据其代表性渗透率 $\bar{k}_{pred,i}$ 进行**降序**排列。排序后的层段索引序列记为 $i_{(1)}, i_{(2)}, \dots, i_{(PLT_N)}$，其中 $i_{(1)}$ 是模型预测渗透率最高的层段。

###### **计算累积贡献**

沿排序后的层段序列，从 $n=1$ 到 $PLT_N$ 逐一计算预测强度和真实产量的累积值。

* **累积预测强度 ($P_n$)**:
    $$P_n = \sum_{k=1}^{n} S_{pred, i_{(k)}}$$
* **累积真实产量 ($T_n$)**:
    $$T_n = \sum_{k=1}^{n} Q_{i_{(k)}}$$

同时，计算用于归一化的总和：
* **总预测强度 ($P_{total}$)**: $P_{total} = \sum_{j=1}^{PLT_N} S_{pred, j}$。
* **总真实产量 ($T_{total}$)**: $T_{total} = \sum_{j=1}^{PLT_N} Q_{j}$。

###### **归一化生成坐标点**

为洛伦兹曲线生成一系列离散坐标点 $(x_n, y_n)$。定义曲线的起点为 $(x_0, y_0) = (0, 0)$。对于 $n=1, \dots, PLT_N$，坐标计算如下：

* **横坐标（$x_n$）**：累积预测贡献分数。
    $$x_n = \frac{P_n}{P_{total}}$$
* **纵坐标（$y_n$）**：累积真实贡献分数。
    $$y_n = \frac{T_n}{T_{total}}$$

最终得到包含起点在内的 $PLT_N+1$ 个坐标点，构成洛伦兹曲线。

##### **评估指标：洛伦兹基尼系数**

洛伦兹基尼系数（$G_{Lorenz}$）用于量化洛伦兹曲线偏离理想对角线的程度，是衡量排序一致性的核心指标。

* **计算方法**：
    首先，使用梯形法则计算洛伦兹曲线下方的面积（$AUC_{Lorenz}$）。
    $$AUC_{Lorenz} = \sum_{n=1}^{PLT_N} \frac{(y_n + y_{n-1})}{2} \cdot (x_n - x_{n-1})$$
    随后，根据曲线下面积计算基尼系数。
    $$G_{Lorenz} = 1 - 2 \cdot AUC_{Lorenz}$$

* **指标解读**：
    * $G_{Lorenz} = 0$：表示完美一致。洛伦兹曲线与$y=x$对角线完全重合，模型预测的贡献度排序与真实贡献度排序完全相同。
    * $G_{Lorenz} > 0$：表示存在排序错配。系数值越大，表明模型预测排序与真实情况的偏差越大，最差情况时该值趋近于1。

##### **可视化与解读**

* **绘图**：
    在二维坐标系中，将坐标点 $(x_n, y_n)$ 依次连接，形成洛伦兹曲线。同时，必须绘制一条从 $(0, 0)$ 到 $(1, 1)$ 的对角线（$y=x$）作为理想参考线。

* **图形解读**：
    * **理想对角线（$y=x$）**：代表理想情况，即累积预测贡献的比例始终等于累积真实贡献的比例。
    * **曲线在对角线下方**：表明模型**高估**了顶部层段的贡献。即模型预测的贡献度累积速度快于真实贡献度的累积速度。
    * **曲线在对角线上方**：表明模型**低估**了顶部层段的贡献。即少量预测贡献就带来了大量真实贡献，说明部分高产层被模型排在了较低的位置。

#### 5.5.7 伪代码

代码被组织成几个独立的函数，分别对应于计算层段属性、生成捕获曲线坐标和生成洛伦兹曲线坐标等核心任务。

```python
import numpy as np
import pandas as pd

def calculate_plt_layer_properties(k_pred_curve, plt_layers):
    """
    从连续的渗透率预测曲线上计算每个PLT层段的离散属性。

    Args:
        k_pred_curve (pd.DataFrame): 包含 'MD' 和 'k_pred' 列的预测结果。
        plt_layers (pd.DataFrame): 包含 'Top_i', 'Bottom_i', 'Q_i' 的PLT分层。

    Returns:
        pd.DataFrame: 增加了 'S_pred' 和 'k_bar_pred' 列的plt_layers。
    """
    delta_d = 0.1524  # 深度采样间隔, m
    s_preds = []
    k_bar_preds = []

    for index, layer in plt_layers.iterrows():
        top, bottom = layer['Top_i'], layer['Bottom_i']

        # 筛选出落在当前层段内的预测点
        layer_k_data = k_pred_curve[
            (k_pred_curve['MD'] >= top) & (k_pred_curve['MD'] <= bottom)
        ]['k_pred']

        if not layer_k_data.empty:
            # 情况A: 层段内有数据点
            s_pred = delta_d * layer_k_data.sum()
            k_bar_pred = layer_k_data.mean()
        else:
            # 情况B: 层段内无数据点 (薄层)
            d_mid = (top + bottom) / 2
            # 找到全井中距离中点最近的预测点
            closest_point_idx = (k_pred_curve['MD'] - d_mid).abs().idxmin()
            k_closest = k_pred_curve.loc[closest_point_idx, 'k_pred']

            k_bar_pred = k_closest
            s_pred = k_bar_pred * (bottom - top)

        s_preds.append(s_pred)
        k_bar_preds.append(k_bar_pred)

    plt_layers['S_pred'] = s_preds
    plt_layers['k_bar_pred'] = k_bar_preds
    return plt_layers


def calculate_capture_curve_coords(analyzed_layers):
    """
    计算用于绘制捕获曲线的坐标点。

    Args:
        analyzed_layers (pd.DataFrame): 已包含预测属性和产量数据的分层表。

    Returns:
        tuple: (x_coords, y_coords) 用于绘制捕获曲线。
    """
    # 准备计算所需数据
    layers = analyzed_layers.copy()
    layers['h'] = layers['Bottom_i'] - layers['Top_i']
    layers['q_density'] = layers['Q_i'] / layers['h']

    # 定义高产层
    high_prod_threshold = layers['q_density'].quantile(0.70)
    layers['is_high_prod'] = (layers['q_density'] >= high_prod_threshold).astype(int)
    layers['high_prod_h'] = layers['h'] * layers['is_high_prod']

    # 按模型预测的渗透率降序排列
    sorted_layers = layers.sort_values(by='k_bar_pred', ascending=False)

    # 计算累积值并归一化
    h_total = sorted_layers['h'].sum()
    h_high_prod_total = sorted_layers['high_prod_h'].sum()

    cum_h = sorted_layers['h'].cumsum()
    cum_h_high_prod = sorted_layers['high_prod_h'].cumsum()

    x_coords = cum_h / h_total
    y_coords = cum_h_high_prod / h_high_prod_total if h_high_prod_total > 0 else np.zeros_like(x_coords)

    # 添加原点 (0,0)
    return np.insert(x_coords.values, 0, 0), np.insert(y_coords.values, 0, 0)


def calculate_lorenz_curve_coords(analyzed_layers):
    """
    计算用于绘制洛伦兹曲线的坐标点。

    Args:
        analyzed_layers (pd.DataFrame): 已包含预测属性和产量数据的分层表。

    Returns:
        tuple: (x_coords, y_coords) 用于绘制洛伦兹曲线。
    """
    layers = analyzed_layers.copy()

    # 按模型预测的渗透率降序排列
    sorted_layers = layers.sort_values(by='k_bar_pred', ascending=False)

    # 计算总和
    p_total = sorted_layers['S_pred'].sum()
    t_total = sorted_layers['Q_i'].sum()

    # 计算累积贡献并归一化
    cum_p = sorted_layers['S_pred'].cumsum()
    cum_t = sorted_layers['Q_i'].cumsum()

    x_coords = cum_p / p_total if p_total > 0 else np.zeros_like(cum_p)
    y_coords = cum_t / t_total if t_total > 0 else np.zeros_like(cum_t)

    # 添加原点 (0,0)
    return np.insert(x_coords.values, 0, 0), np.insert(y_coords.values, 0, 0)


def analyze_plt_well(k_pred_curve, plt_layers):
    """
    对PLT盲井进行完整的检验计算。
    """
    # 1. 计算各层段的基础预测属性
    analyzed_layers = calculate_plt_layer_properties(k_pred_curve, plt_layers)

    # 2. 计算相对贡献
    analyzed_layers['R_pred'] = analyzed_layers['S_pred'] / analyzed_layers['S_pred'].sum()
    analyzed_layers['R_PLT'] = analyzed_layers['Q_i'] / analyzed_layers['Q_i'].sum()

    # 3. 计算相关性等统计指标 (此处置空，实际应调用统计函数)
    pearson_r = analyzed_layers['R_pred'].corr(analyzed_layers['R_PLT'], method='pearson')
    spearman_rho = analyzed_layers['R_pred'].corr(analyzed_layers['R_PLT'], method='spearman')

    # 4. 生成绘图所需坐标
    capture_x, capture_y = calculate_capture_curve_coords(analyzed_layers)
    lorenz_x, lorenz_y = calculate_lorenz_curve_coords(analyzed_layers)

    # 5. 返回所有结果
    results = {
        'analyzed_layers': analyzed_layers,
        'correlations': {'pearson_r': pearson_r, 'spearman_rho': spearman_rho},
        'capture_curve': {'x': capture_x, 'y': capture_y},
        'lorenz_curve': {'x': lorenz_x, 'y': lorenz_y}
    }

    return results

```

### 5.6 岩心渗透率盲井检验

以下称“核磁共振渗透率计算模型+θ_final参数”在岩心渗透率盲井上计算得到的渗透率为模型预测渗透率$k_{pred}$，岩心渗透率为$k_{core\_SEM}$。需要完成的操作如下：
##### 5.6.1 通用检验流程

1.  **绘制交会图**：制作 $k_{pred}$ 和 $k_{core\_SEM}$ 的交会图，横纵坐标轴均采用对数刻度。
2.  **绘制边界线**：在图上绘制以下参考线：
    * 1:1 主对角线 ($k_{core\_SEM} = k_{pred}$)。
    * 3倍上/下边界线 ($k_{core\_SEM} = 3 \cdot k_{pred}$ 及 $k_{core\_SEM} = k_{pred} / 3$)。
    * 5倍上/下边界线 ($k_{core\_SEM} = 5 \cdot k_{pred}$ 及 $k_{core\_SEM} = k_{pred} / 5$)。
    * 10倍上/下边界线 ($k_{core\_SEM} = 10 \cdot k_{pred}$ 及 $k_{core\_SEM} = k_{pred} / 10$)。
    * 对线条设置颜色，1:1线为红色，3×线为黑色，5×线为蓝色，10×线为绿色。
3.  **计算符合率**：统计落在不同边界线（如3倍、5倍、10倍）内的数据点数量占总数据点数量的比例。
##### 5.6.2 检验标准

检验标准分为通用标准和针对特定井的放宽标准。

* **通用标准 (General Standard)**:
    对于采用常规方式（如RCA）获取岩心渗透率的井，通常采用较为严格的工程标准来衡量模型性能。经验门槛为：“**超过70%的数据点落在3倍边界线内，且超过90%的数据点落在10倍边界线内**”，则认为模型达到“工程可用”水平。

* **针对X-2井的放宽标准 (Relaxed Standard for Well X-2)**:
    * **理由**: `X-2`井的岩心渗透率是由SEM（扫描电子显微镜）数据间接得到的，其数据精度和误差特性与常规岩心分析不同，不确定性通常更高。因此，对其采用通用标准可能过于严苛。
    * **放宽标准**: 根据项目背景设定，对`X-2`井的检验标准进行放宽，重点评估其在5倍边界内的符合率。如果“**超过50%的数据点落在5倍边界线内**”，即可认为模型在该井上达到了可接受的预测效果。这一标准更切合该井“真值”数据的实际精度。

##### 5.6.3 伪代码

该伪代码的核心是计算模型预测值落在“真值”的特定误差倍数（如3倍、5倍、10倍）边界内的数据点百分比，并根据预设标准给出检验结论。

```python
import numpy as np
import pandas as pd

def calculate_hit_rates(k_pred, k_core, error_multiples=[3, 5, 10]):
    """
    计算落在指定误差倍数边界内的数据点符合率。

    Args:
        k_pred (np.array): 模型预测的渗透率值。
        k_core (np.array): 岩心渗透率真值。
        error_multiples (list): 需要计算的误差倍数列表。

    Returns:
        dict: 包含各误差倍数符合率的字典, e.g., {'3x': 75.0, '5x': 85.5, ...}。
    """
    # 确保输入数据长度一致且有效
    if len(k_pred) != len(k_core) or len(k_pred) == 0:
        return {f'{m}x': 0.0 for m in error_multiples}

    hit_rates = {}
    n_total = len(k_pred)

    for factor in error_multiples:
        # 定义误差边界
        lower_bound = k_core / factor
        upper_bound = k_core * factor

        # 统计落在边界内的点的数量
        n_hits = np.sum((k_pred >= lower_bound) & (k_pred <= upper_bound))

        # 计算符合率 (百分比)
        rate = (n_hits / n_total) * 100
        hit_rates[f'{factor}x'] = rate

    return hit_rates


def analyze_core_well_test(k_pred, k_core_sem):
    """
    对岩心渗透率盲井进行完整的检验分析。

    Args:
        k_pred (np.array): 模型在该井的渗透率预测值。
        k_core_sem (np.array): 该井的SEM岩心渗透率真值。

    Returns:
        dict: 包含符合率和检验结论的字典。
    """
    # 1. 计算符合率
    hit_rates_results = calculate_hit_rates(k_pred, k_core_sem)

    # 2. 根据X-2井的放宽标准进行判断
    # 标准: 超过50%的数据点落在5倍边界线内
    acceptance_criteria_met = False
    if '5x' in hit_rates_results and hit_rates_results['5x'] > 50.0:
        acceptance_criteria_met = True

    # 3. 生成结论
    conclusion = "Pass: 模型满足X-2井的放宽检验标准。" if acceptance_criteria_met \
            else "Fail: 模型未能满足X-2井的放宽检验标准。"

    # 4. 汇总并返回结果
    results = {
        'hit_rates': hit_rates_results,
        'conclusion': conclusion
    }

    return results

```

### 5.7 输出完整结果

在完成FOSTER-NMR模型的参数优化与盲井验证后，应整理并输出以下一系列完整的分析结果，以全面评估模型的性能、稳健性与不确定性。

1.  **最终模型参数与不确定性 (Final Model Parameters and Uncertainty)**
    * **最终参数向量 $\theta_{final}$**：以表格形式清晰列出经过Fine-Tuning阶段优化后得到的最终10维参数向量$\theta_{final}$中每个参数的取值。这些参数将用于后续的生产部署。
    * **参数置信区间**：展示基于Bootstrap+LOWO阶段产出的$B \times W_N$组参数向量，采用百分位法计算出的各参数的95%置信区间（95% CI）。这反映了由数据采样和井间差异所带来的参数不确定性范围。

2.  **模型训练与收敛过程 (Model Training and Convergence Process)**
    * **Fine-Tuning阶段收敛曲线**：绘制Fine-Tuning阶段的损失函数$Loss_{FT}(\theta)$（或其滑动平均值）随迭代次数变化的曲线图。在图上应明确标注出触发早停（Early-Stopping）条件的迭代点，直观展示模型的收敛效率与最终状态。
    * **训练/验证性能汇总表**：提供一个综合性能表格，量化模型在不同阶段的表现。
        * **交叉验证性能**：报告在Bootstrap+LOWO阶段，$B$轮自助采样的平均验证误差序列 $\{ {\mu\_RMSE_b} \}$的均值与标准差，用以评估模型的整体泛化能力与稳定性。
        * **最终训练误差**：报告在Fine-Tuning阶段，使用全量数据训练得到的最终损失函数值$Loss_{FT}(\theta_{final})$。
        * **过拟合评估**：计算并报告一个过拟合比率（Overfitting Ratio），例如`(平均验证误差 - 平均训练误差) / 平均训练误差`，以评估模型的过拟合程度。

3.  **参数解的稳定性与分布 (Stability and Distribution of Parameter Solutions)**
    * **t-SNE收敛轨迹与聚类分析图**：展示Bootstrap+LOWO阶段所有最优参数向量（$\hat{\theta}_{b,f}$）在经过Z-score标准化后，通过t-SNE降维到二维空间的散点图。图中应包含：
        * 按优化代数连接的**收敛流线**，以展示优化路径。
        * 对最终参数点集进行聚类分析后的结果，例如使用不同颜色或凸包（convex hull）区分不同的参数簇。
    * **聚类分析量化报告**：以表格形式报告在10维原始特征空间上进行聚类分析的定量结果，包括但不限于最优簇数、各项聚类质量指标（如Silhouette、Davies–Bouldin等）以及DBSCAN识别出的离群点数量。

4.  **PLT盲井检验报告 (X-1井) (PLT Blind Well Test Report - Well X-1)**
    * **贡献率相关性分析**：
        * 报告模型预测相对贡献（$R_{pred,i}$）与PLT解释相对贡献（$R_{PLT,i}$）之间的**Pearson R**和**Spearman ρ**相关系数值。
        * 绘制$R_{pred,i}$ vs. $R_{PLT,i}$的交会图，并附上1:1参考线。
        * 报告基于二者计算的误差指标（如MAE, MAPE, RMSE）。
    * **优选高产层能力评估**：
        * 展示以厚度为加权资源的**捕获曲线（Capture Curve）**，并与随机选择和理想情况进行对比。
        * 报告关键指标：$CR^{thk}_{0.3}$（投入30%厚度捕获的高产厚度比例）、曲线下面积（$AUC_{Capture}$）、捕获基尼系数（Capture Gini）以及Lift@30%。
    * **层序累积贡献评估**：
        * 展示模型预测贡献 vs. 真实产液贡献的**洛伦兹曲线（Lorenz Curve）**，并附上代表完美一致性的$y=x$对角线。
        * 报告**洛伦兹基尼系数（$G_{Lorenz}$）**，用于量化模型预测排序与真实排序的整体偏差。

5.  **岩心渗透率盲井检验报告 (X-2井) (Core Permeability Blind Well Test Report - Well X-2)**
    * **渗透率交会图**：提供模型预测渗透率（$k_{pred}$）与SEM岩心渗透率（$k_{core\_SEM}$）的对数-对数交会图。图中必须按规定颜色（1:1为红色，3倍为黑色，5倍为蓝色，10倍为绿色）清晰绘制1:1、3倍、5倍和10倍误差边界线。
    * **符合率统计**：以文字或表格形式，明确报告落在3倍、5倍和10倍误差边界内的数据点所占的百分比。
    * **检验结论**：根据符合率统计结果，明确判断模型性能是否达到了针对X-2井设定的放宽标准（“**超过50%的数据点落在5倍边界线内**”）。


# 6 结果与文件保存规则

为确保SCAPE框架（包含Stage-1 OBMIQ和Stage-2 SWIFT-PSO）每次运行结果的完整性、可追溯性和可复现性，所有输出文件必须遵循以下组织原则和细则。

## I. 核心原则

1.  **分级目录结构 (Hierarchical Directory Structure)**：为每一次完整的运行创建一个独立的、以时间戳命名的根目录。所有与该次运行相关的文件都应保存在此目录下，并根据阶段和类型分门别类存入子目录，避免文件混乱。

2.  **标准化文件格式 (Standardized File Formats)**：

      * **表格数据**: 使用`.csv`或`.parquet`格式保存（如损失历史、分层属性表）。它们是通用格式，便于多种工具（Python Pandas, R, Excel等）读取。
      * **参数与配置**: 使用`.json`或`.yaml`格式保存（如模型参数、运行配置）。它们是人类可读的，且能轻松被程序解析。
      * **图表**: 使用`.png`（用于报告）和`.svg`（用于高质量出版或编辑）两种格式保存。
      * **模型对象**: 推荐使用`joblib`保存模型对象（如最终的OBMIQ融合模型），并附带一个包含模型元数据和超参数的`.json`文件。尽量避免单独使用`.pkl` (pickle)，因为它存在版本依赖问题。

3.  **描述性命名约定 (Descriptive Naming Convention)**：文件名应清晰、一致，并包含关键信息。推荐使用 `[阶段]_[内容]_[标识符].ext` 的格式，例如 `lowo_loss_history_b01_f03.csv` 表示第一轮Bootstrap第三折的损失历史。

4.  **单一清单文件 (Centralized Manifest File)**：在每次运行的根目录下，创建一个核心的`manifest.json`或`run_summary.json`文件。该文件作为本次运行的“索引”，记录了关键的配置信息、两个阶段最终的核心结果以及指向其他详细结果文件的相对路径。

5.  **代码与配置快照 (Code & Config Snapshot)**：在每次运行的根目录下，保存一份当次运行所使用的**完整配置文件**。如果使用Git进行版本控制，强烈建议将当次运行的\*\*Git提交哈希（commit hash）\*\*记录在清单文件中，以实现完全的可复现性。

## II. 目录结构建议

建议为每次完整运行（从OBMIQ建模到SWIFT-PSO验证）创建如下的目录结构：

```
/output/
└── /run_20250625_103000/                <-- 每次运行的独立根目录
    ├── manifest.json                     <-- 核心清单与摘要文件
    ├── config.json                       <-- 本次运行的完整配置文件（含两阶段）
    │
    ├── stage1_OBMIQ/                     <-- 阶段一：OBMIQ建模
    │   ├── model_dt2_p50.joblib
    │   ├── model_dt2_p50_config.json
    │   ├── model_dphit_nmr.joblib
    │   ├── model_dphit_nmr_config.json
    │   ├── nested_cv_performance.csv
    │   └── uncertainty_statement.md
    │
    └── stage2_swift_pso/                 <-- 阶段二：SWIFT-PSO渗透率优化
        ├── 1_bootstrap_lowo/
        │   ├── run_b01_f01/
        │   │   ├── params_optimal.json   <-- f01折的最优参数
        │   │   └── loss_history.csv      <-- f01折的损失历史
        │   ├── run_b01_f02/
        │   │   └── ...
        │   └── summary_bootstrap_mu_rmse.csv <-- B轮的平均验证误差汇总
        │
        ├── 2_fine_tuning/
        │   ├── params_warm_start.json    <-- 热启动参数
        │   ├── params_final.json         <-- 最终优化的参数
        │   └── loss_history_finetune.csv <-- 精调阶段的损失历史
        │
        ├── 3_analysis/
        │   ├── parameter_statistics.csv  <-- 所有参数的统计信息（均值, std, 95% CI）
        │   ├── tsne_and_clusters.csv     <-- t-SNE降维坐标及聚类标签
        │   └── plots/
        │       ├── convergence_trajectory_tsne.png
        │       └── finetune_loss_curve.png
        │
        └── 4_validation/
            ├── x1_plt_well/
            │   ├── results_table.csv     <-- PLT井的详细分层计算结果
            │   ├── capture_curve_coords.csv
            │   ├── lorenz_curve_coords.csv
            │   └── plots/
            │       ├── contribution_crossplot.png
            │       ├── capture_curve.png
            │       └── lorenz_curve.png
            └── x2_core_well/
                ├── test_summary.json     <-- 岩心井的符合率和结论
                └── plots/
                    └── permeability_crossplot.png
```

## III. 各阶段结果保存细则

1.  **运行前**

      * **`config.json`**: 保存本次运行的所有配置，应包含`OBMIQ`和`swift_pso`两个独立的配置块，分别存放各自的超参数、迭代次数、文件路径等信息。

2.  **Stage-1: OBMIQ 阶段** (`stage1_OBMIQ/`)

      * **`model_dt2_p50.joblib` / `model_dphit_nmr.joblib`**: 分别保存为`DT2_P50`和`DPHIT_NMR`两个目标训练好的最终加权融合模型对象。
      * **`model_dt2_p50_config.json` / `model_dphit_nmr_config.json`**: 分别保存两个融合模型所包含的子模型类型、融合权重，以及每个子模型最终训练时使用的全部超参数。
      * **`nested_cv_performance.csv`**: 保存嵌套交叉验证（LOWO-CV）阶段，各候选算法（XGBoost, RandomForest, SVM）在所有折上的RMSE、平均RMSE及标准差，用于评估和选择模型。
      * **`uncertainty_statement.md`**: 保存文档中要求的关于“基于两口井建模存在高不确定性”的强制性声明。

3.  **Stage-2: SWIFT-PSO 阶段** (`stage2_swift_pso/`)

      * **Bootstrap+LOWO** (`1_bootstrap_lowo/`)

          * 为每一折（`b`轮, `f`折）创建一个子目录 `run_b{b:02d}_f{f:02d}/`：
              * **`params_optimal.json`**: 保存该折优化得到的最优参数向量 $\\hat{\\theta}\_{b,f}$。
              * **`loss_history.csv`**: 保存该折PSO运行过程中的损失值历史。
          * **`summary_bootstrap_mu_rmse.csv`**: 汇总文件，包含`B`轮循环，每轮的平均验证误差 $\\mu\_RMSE\_b$ 及其对应的标准差。

      * **Fine-Tuning** (`2_fine_tuning/`)

          * **`params_warm_start.json`**: 保存根据最佳Bootstrap轮次 `b*` 计算出的、用于精调的热启动参数向量 $\\hat{\\theta}^{\\ast}$。
          * **`params_final.json`**: 保存精调结束后得到的最终参数向量 $\\theta\_{final}$。
          * **`loss_history_finetune.csv`**: 保存精调阶段PSO的损失历史，并应在元数据中记录早停点。

      * **参数分析** (`3_analysis/`)

          * **`parameter_statistics.csv`**: 保存对第一阶段产出的所有$\\hat{\\theta}\_{b,f}$进行统计分析的结果，包含每个参数的均值、标准差、95%置信区间等。
          * **`tsne_and_clusters.csv`**: 保存t-SNE降维后的二维坐标，以及每个参数点对应的Bootstrap编号、LOWO折号、聚类分析标签等。
          * **`plots/`**: 保存该阶段生成的图表，如`convergence_trajectory_tsne.png`（t-SNE收敛轨迹与聚类图）和`finetune_loss_curve.png`（精调收敛曲线图）。

      * **盲井检验** (`4_validation/`)

          * 为每个盲井（如`x1_plt_well`, `x2_core_well`）创建子目录。
          * **PLT井 (`x1_plt_well/`)**:
              * `results_table.csv`: 保存PLT井的详细分析表，包含所有分层信息以及计算出的$S\_{pred}$, $\\bar{k}*{pred}$, $R*{pred}$, $R\_{PLT}$等列。
              * `capture_curve_coords.csv`, `lorenz_curve_coords.csv`: 分别保存用于绘制捕获曲线和洛伦兹曲线的(x, y)坐标。
              * `plots/`: 保存所有相关的图表，如贡献率交会图、捕获曲线和洛伦兹曲线图。
          * **岩心井 (`x2_core_well/`)**:
              * `test_summary.json`: 保存岩心井的检验结果，包含各误差倍数（3x, 5x, 10x）的符合率以及最终的“Pass/Fail”检验结论。
              * `plots/`: 保存渗透率对数-对数交会图 `permeability_crossplot.png`。

4.  **顶层清单文件**

      * **`manifest.json`**: 在所有计算和分析完成后生成或更新此文件。它应包含：
          * 运行元数据：运行时间、Git提交哈希。
          * **Stage-1 摘要**：最终选择的OBMIQ模型（如'WeightedEnsemble'）、在嵌套CV上的平均RMSE、指向最终模型文件的路径。
          * **Stage-2 摘要**：最终模型参数 $\\theta\_{final}$ 的一个副本、Bootstrap阶段的平均泛化误差、PLT井的关键指标（如Spearman ρ, Capture Gini, $G\_{Lorenz}$）、岩心井的5倍符合率和最终结论。
          * 指向关键结果文件（如`params_final.json`, `x1_plt_well/results_table.csv`等）的相对路径。
