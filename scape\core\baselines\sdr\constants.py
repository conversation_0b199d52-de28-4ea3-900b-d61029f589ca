"""scape.core.baselines.sdr.constants - SDR基准模型常量"""

from enum import Enum


class SdrArtifacts(str, Enum):
    """定义SDR基准模型步骤的产物逻辑名称。

    遵循框架的 `step_name.<category>.<specific_name>` 命名规范。
    """
    # 训练步骤 (sdr_baseline_training) 的产物
    MODEL_ASSETS = "sdr_baseline_training.models.sdr_assets"
    TRAINING_CONFIG_SNAPSHOT = "sdr_baseline_training.configs.training_config_snapshot"

    # 预测步骤 (sdr_baseline_prediction) 的产物
    PREDICTED_DATA = "sdr_baseline_prediction.datasets.sdr_predictions"
    PREDICTION_CONFIG_SNAPSHOT = "sdr_baseline_prediction.configs.prediction_config_snapshot"
