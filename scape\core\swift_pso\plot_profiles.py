"""scape.core.swift_pso.plot_profiles - SWIFT-PSO绘图配置模板

定义并注册SWIFT-PSO模块所有图表的PlotProfile模板。
此模块在 `scape.core.swift_pso` 包被导入时自动执行，
将配置注册到全局 `logwp.extras.plotting.registry` 中。

Architecture
------------
层次/依赖: scape/core层，SWIFT-PSO绘图配置层
设计原则: 配置即服务、两级继承、中心化注册
性能特征: 延迟加载、配置缓存

References
----------
- 《logwp/extras/plotting/README.md》- 绘图配置系统设计文档
"""

from __future__ import annotations

from logwp.extras.plotting import PlotProfile, SaveConfig, registry
from .constants import TsnePlotProfiles

# 1. 定义SWIFT-PSO模块的【模块级基础模板】
#    它会隐式继承全局的 "base" 模板

swift_pso_base_profile = PlotProfile(
        name=TsnePlotProfiles.SWIFT_PSO_BASE.value,
        rc_params={
            "font.family": "Arial",
            "font.size": 12,
            "axes.grid": True,
            "grid.alpha": 0.3,
            "grid.linestyle": "-",
            "axes.edgecolor": "black",
            "axes.linewidth": 1.0,
        },
        figure_props={
            "figsize": (16, 12),
            "dpi": 150,
            "layout": "constrained"
        },
        save_config=SaveConfig(
            format=["png", "svg"],
            dpi=300,
            transparent=False,
            bbox_inches="tight"
        )
    )

# 2. 定义t-SNE收敛轨迹的【具体图表模板】
#    它会继承自上面定义的 "swift_pso.base" 模板
tsne_convergence_profile = PlotProfile(
        name=TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value,
        title_props={
            "label": "SWIFT-PSO Parameter Convergence Trajectory (t-SNE)",
            "fontsize": 16,
            "fontweight": "bold",
            "pad": 20
        },
        label_props={
            "xlabel": "t-SNE Dimension 1",
            "ylabel": "t-SNE Dimension 2",
            "fontsize": 14
        },
        artist_props={
            # 轨迹线样式
            "trajectory": {
                "color": "grey",
                "alpha": 0.4,
                "linewidth": 0.8,
                "zorder": 1
            },
            # 散点样式（聚类着色）
            "scatter": {
                "s": 80,  # 对应原来的marker_size
                "edgecolor": "white",
                "linewidth": 0.5,
                "zorder": 2
            },
            # 起点标记样式
            "start_point": {
                "marker": "x",
                "s": 50,
                "color": "black",
                "linewidth": 1.0,
                "zorder": 3,
                "label": "Start Point"
            },
            # 终点标记样式
            "end_point": {
                "s": 100,
                "facecolors": "none",
                "edgecolors": "red",
                "linewidth": 1.2,
                "zorder": 4,
                "label": "End Point"
            },
            # 调色板配置
            "palette": {
                "name": "viridis",
                "n_colors": None  # 动态确定
            },
            # 图例配置
            "legend": {
                "title": "Legend",
                "bbox_to_anchor": (1.05, 1),
                "loc": "upper left"
            }
        }
    )

# 3. 定义t-SNE聚类分析的【具体图表模板】
#    它同样继承自 "swift_pso.base"
tsne_cluster_analysis_profile = PlotProfile(
        name=TsnePlotProfiles.CLUSTER_ANALYSIS.value,
        title_props={
            "label": "SWIFT-PSO Final Parameters Cluster Analysis (t-SNE)",
            "fontsize": 16,
            "fontweight": "bold",
            "pad": 20
        },
        label_props={
            "xlabel": "t-SNE Dimension 1",
            "ylabel": "t-SNE Dimension 2",
            "fontsize": 14
        },
        artist_props={
            # 聚类散点样式
            "scatter": {
                "s": 100,
                "edgecolor": "black",
                "linewidth": 0.8,
                "zorder": 2
            },
            # 新增：凸包样式
            "convex_hull": {
                "alpha": 0.15,
                "edgecolor": "black",
                "linewidth": 1.0,
                "linestyle": "--",
                "zorder": 1
            },
            # 调色板和图例配置与收敛图保持一致
            "palette": tsne_convergence_profile.artist_props["palette"],
            "legend": tsne_convergence_profile.artist_props["legend"]
        }
    )

# 4. 向全局注册表注册所有模板
#    注册表会根据名称自动处理继承关系
registry.register_base(swift_pso_base_profile)
registry.register(tsne_convergence_profile)
registry.register(tsne_cluster_analysis_profile)
