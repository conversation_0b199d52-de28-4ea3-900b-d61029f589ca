"""渗透率相关性分析步骤的门面(Facade)模块。

此模块提供了 `run_perm_correlation_step` 函数，作为渗透率相关性分析步骤
的唯一公共入口。它遵循《可追踪机器学习组件开发框架》的规范，负责
流程编排、与RunContext交互、以及调用内部计算逻辑。
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict

from logwp.infra import get_logger

from .artifact_handler import ValidationArtifactHandler
from .config import PermCorrelationConfig
from .constants import PermCorrelationArtifacts
from .internal import perm_corr_computer
from . import plotting

if TYPE_CHECKING:
    from logwp.extras.plotting import PlotProfile
    from logwp.extras.tracking import RunContext
    from logwp.models.datasets.bundle import WpDataFrameBundle

logger = get_logger(__name__)


def run_perm_correlation_step(
    config: PermCorrelationConfig,
    ctx: RunContext,
    left_bundle: WpDataFrameBundle,
    right_bundle: WpDataFrameBundle,
    *,
    left_curve: str,
    right_curve: str,
    plot_profile: PlotProfile,
    prefix: str = "",
) -> Dict[str, Any]:
    """执行渗透率相关性分析步骤。

    此函数是渗透率相关性分析的门面(facade)，负责编排整个分析流程，
    包括调用内部计算、记录产物和指标、以及生成图表。

    Args:
        config (PermCorrelationConfig): 步骤的配置对象。
        ctx (RunContext): 当前运行的上下文，用于追踪。
        left_bundle (WpDataFrameBundle): 左侧数据集（通常是预测值来源）。
        right_bundle (WpDataFrameBundle): 右侧数据集（通常是真值来源）。
        left_curve (str): 左侧数据集中要对比的曲线名。
        right_curve (str): 右侧数据集中要对比的曲线名。
        plot_profile (PlotProfile): 用于交会图的PlotProfile对象。
        prefix (str, optional): 产物输出目录和指标名称的前缀，用于在同一
            Workflow中多次运行时区分产物。默认为 ""。

    Returns:
        Dict[str, Any]: 一个包含分析摘要的轻量级字典。
    """
    base_step_name = "perm_corr_analysis"
    step_name = f"{prefix}_{base_step_name}" if prefix else base_step_name

    logger.info("开始执行渗透率相关性分析步骤...", step_name=step_name)
    step_dir = ctx.get_step_dir(step_name)
    handler = ValidationArtifactHandler()

    # 1. 找出共同井进行分析
    left_wells = set(left_bundle.safe_get_well_names())
    right_wells = set(right_bundle.safe_get_well_names())
    common_wells_to_analyze = sorted(list(left_wells & right_wells))

    if not common_wells_to_analyze:
        logger.warning("左右数据包中没有共同的井可供分析，步骤终止。")
        return {}

    logger.info(f"找到 {len(common_wells_to_analyze)} 口共同井进行分析: {common_wells_to_analyze}")

    # 2. 循环处理每口井
    summary_results = {}
    for well_name in common_wells_to_analyze:
        logger.info(f"正在处理井: {well_name}", well_name=well_name)

        # 2.1. 调用内部计算模块
        well_results = perm_corr_computer.compute_all_metrics_for_well(
            well_name=well_name,
            left_bundle=left_bundle,
            left_curve=left_curve,
            right_bundle=right_bundle,
            right_curve=right_curve,
            relaxed_wells=config.relaxed_wells,
        )

        aligned_df = well_results.get("aligned_df")
        if aligned_df is None or aligned_df.empty:
            logger.warning(f"井 {well_name} 对齐后的数据为空，跳过此井。", well_name=well_name)
            continue

        # 2.2. 记录数值型指标 (Metrics)
        step_name_with_well = f"{step_name}.{well_name}"
        ctx.log_metrics(well_results.get("hit_rates", {}), step_name=step_name_with_well)
        ctx.log_metrics(well_results.get("correlations", {}), step_name=step_name_with_well)

        # 2.3. 保存并注册表格产物 (Artifact)
        table_artifact_name = f"{PermCorrelationArtifacts.ALIGNED_DATA_PREFIX.value}{well_name}"
        table_path = step_dir / f"{table_artifact_name}.csv"
        handler.save_dataframe(aligned_df, table_path)
        ctx.register_artifact(
            artifact_path=table_path.relative_to(ctx.run_dir),
            artifact_name=table_artifact_name,
            description=f"渗透率相关性分析后 {well_name} 井的对齐数据表。",
        )

        # 2.4. 处理绘图产物
        # 保存配置快照
        profile_artifact_name = f"{PermCorrelationArtifacts.CROSSPLOT_PROFILE_PREFIX.value}{well_name}"
        profile_path = step_dir / f"{profile_artifact_name}.json"
        handler.save_plot_profile(plot_profile, profile_path)
        ctx.register_artifact(profile_path.relative_to(ctx.run_dir), profile_artifact_name)

        # 保存数据快照 (与对齐数据表内容相同，但逻辑名不同，以符合框架)
        data_artifact_name = f"{PermCorrelationArtifacts.CROSSPLOT_DATA_PREFIX.value}{well_name}"
        data_path = step_dir / f"{data_artifact_name}.csv"
        handler.save_dataframe(aligned_df, data_path)
        ctx.register_artifact(data_path.relative_to(ctx.run_dir), data_artifact_name)

        # 生成并注册图表
        plot_artifact_name = f"{PermCorrelationArtifacts.CROSSPLOT_PREFIX.value}{well_name}"
        plot_path = step_dir / f"{plot_artifact_name}{plot_profile.save_config.get_preferred_extension()}"
        plotting.replot_perm_corr_crossplot_from_snapshot(
            snapshot_path=data_path,
            plot_profile=plot_profile,
            output_path=plot_path,
            metrics=well_results,
        )
        ctx.register_artifact(plot_path.relative_to(ctx.run_dir), plot_artifact_name)

        # 2.5. 汇总关键结果
        summary_results[well_name] = {
            "spearman_rho": well_results.get("correlations", {}).get("spearman_rho"),
            "conclusion": well_results.get("conclusion"),
        }

    logger.info("渗透率相关性分析步骤执行完毕。")
    return summary_results
