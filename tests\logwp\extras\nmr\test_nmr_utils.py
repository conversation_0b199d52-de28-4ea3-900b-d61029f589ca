"""tests.logwp.extras.nmr.test_nmr_utils

测试 `logwp.extras.nmr.nmr_utils` 模块的功能。

测试目标:
- 验证单点和向量化孔隙度组分计算在CPU和GPU上的正确性。
- 确保CPU和GPU后端计算结果的一致性。
- 覆盖各种边界条件、数据异常和参数错误。

Architecture
------------
测试层次: 单元测试
测试原则: 参数化测试、后端一致性验证、GPU条件执行
"""

from __future__ import annotations

import pytest
import numpy as np

from logwp.extras.nmr.nmr_utils import (
    calculate_t2_porosity_components,
    calculate_t2_porosity_components_vectorized,
)
from logwp.extras.nmr.exceptions import WpNmrParameterError, WpNmrDataError
from logwp.extras.backend import BackendService, create_backend_service_by_name

# 检查cupy是否可用，并相应地设置测试标记
try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False

skip_if_no_gpu = pytest.mark.skipif(not CUPY_AVAILABLE, reason="CuPy is not installed, skipping GPU tests.")

# 定义测试后端
backends = ["cpu", pytest.param("gpu", marks=skip_if_no_gpu)]


@pytest.fixture
def t2_time() -> np.ndarray:
    """提供一个标准的T2时间轴。"""
    return np.logspace(-1, 4, 64)


@pytest.fixture
def t2_spectrum() -> np.ndarray:
    """提供一个简单的T2谱。"""
    return np.full(64, 0.01)


@pytest.fixture
def t2_spectrum_matrix(t2_spectrum: np.ndarray) -> np.ndarray:
    """提供一个T2谱矩阵。"""
    return np.tile(t2_spectrum, (10, 1))


@pytest.mark.unit
class TestCalculateT2PorosityComponents:
    """测试单点孔隙度组分计算。"""

    @pytest.mark.parametrize("backend", backends)
    def test_basic_calculation(self, t2_time, t2_spectrum, backend):
        """测试在CPU和GPU上的基本计算。"""
        service = create_backend_service_by_name(backend)
        t2_spectrum_arr = service.to_backend(t2_spectrum)
        t2_time_arr = service.to_backend(t2_time)

        v_micro, v_meso, v_macro = calculate_t2_porosity_components(
            t2_spectrum=t2_spectrum_arr,
            t2_time=t2_time_arr,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=backend
        )

        assert isinstance(v_micro, float)
        assert v_micro >= 0
        assert v_meso >= 0
        assert v_macro >= 0

        total_porosity = v_micro + v_meso + v_macro
        expected_total = np.sum(t2_spectrum)
        assert abs(total_porosity - expected_total) < 1e-9

    def test_invalid_parameters(self, t2_time, t2_spectrum):
        """测试无效参数是否引发异常。"""
        with pytest.raises(WpNmrParameterError, match="T2截止值顺序错误"):
            calculate_t2_porosity_components(t2_spectrum, t2_time, 33.0, 3.0)

        with pytest.raises(WpNmrParameterError, match="T2截止值必须为正数"):
            calculate_t2_porosity_components(t2_spectrum, t2_time, -3.0, 33.0)

    def test_data_mismatch(self, t2_spectrum):
        """测试数据长度不匹配是否引发异常。"""
        t2_time_short = np.logspace(-1, 4, 32)
        with pytest.raises(WpNmrDataError, match="T2轴与T2谱数据长度不匹配"):
            calculate_t2_porosity_components(t2_spectrum, t2_time_short, 3.0, 33.0)

    @pytest.mark.parametrize("backend", backends)
    def test_custom_t2_range(self, t2_time, t2_spectrum, backend):
        """测试自定义T2范围参数是否生效。"""
        service = create_backend_service_by_name(backend)
        t2_spectrum_arr = service.to_backend(t2_spectrum)
        t2_time_arr = service.to_backend(t2_time)

        # 全范围计算
        v_micro_full, v_meso_full, v_macro_full = calculate_t2_porosity_components(
            t2_spectrum=t2_spectrum_arr,
            t2_time=t2_time_arr,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=backend
        )
        total_full = v_micro_full + v_meso_full + v_macro_full

        # 限制范围计算 (1ms to 1000ms)
        v_micro_lim, v_meso_lim, v_macro_lim = calculate_t2_porosity_components(
            t2_spectrum=t2_spectrum_arr,
            t2_time=t2_time_arr,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            t2_range_min=1.0,
            t2_range_max=1000.0,
            backend_service=backend
        )
        total_limited = v_micro_lim + v_meso_lim + v_macro_lim

        # 限制范围的结果应该小于全范围的结果
        assert total_limited < total_full

    def test_invalid_t2_range(self, t2_time, t2_spectrum):
        """测试无效的T2范围参数是否引发异常。"""
        with pytest.raises(WpNmrParameterError, match="T2范围无效"):
            calculate_t2_porosity_components(
                t2_spectrum, t2_time, 3.0, 33.0, t2_range_min=1000.0, t2_range_max=100.0
            )

    def test_passing_backend_service_instance(self, t2_time, t2_spectrum):
        """测试直接传递BackendService实例作为参数。"""
        # 预先创建一个服务实例
        cpu_service = create_backend_service_by_name('cpu')

        # 将实例传递给函数
        v_micro, v_meso, v_macro = calculate_t2_porosity_components(
            t2_spectrum=t2_spectrum,
            t2_time=t2_time,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=cpu_service
        )

        # 验证计算结果是否正确
        assert np.isclose(v_micro + v_meso + v_macro, np.sum(t2_spectrum))

    def test_invalid_backend_type(self, t2_time, t2_spectrum):
        """测试传递无效类型的backend参数时应抛出TypeError。"""
        with pytest.raises(TypeError, match="不支持的backend类型"):
            calculate_t2_porosity_components(
                t2_spectrum, t2_time, 3.0, 33.0, backend_service=123
            )

    @pytest.mark.gpu
    @skip_if_no_gpu
    def test_consistency_cpu_vs_gpu(self, t2_time, t2_spectrum):
        """测试CPU和GPU计算结果的一致性。"""
        # CPU计算
        v_micro_cpu, v_meso_cpu, v_macro_cpu = calculate_t2_porosity_components(
            t2_spectrum=t2_spectrum,
            t2_time=t2_time,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service='cpu'
        )

        # GPU计算
        service_gpu = create_backend_service_by_name('gpu')
        t2_spectrum_gpu = service_gpu.to_gpu(t2_spectrum)
        t2_time_gpu = service_gpu.to_gpu(t2_time)
        v_micro_gpu, v_meso_gpu, v_macro_gpu = calculate_t2_porosity_components(
            t2_spectrum=t2_spectrum_gpu,
            t2_time=t2_time_gpu,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service='gpu'
        )

        assert np.isclose(v_micro_cpu, v_micro_gpu)
        assert np.isclose(v_meso_cpu, v_meso_gpu)
        assert np.isclose(v_macro_cpu, v_macro_gpu)


@pytest.mark.unit
class TestCalculateT2PorosityComponentsVectorized:
    """测试向量化孔隙度组分计算。"""

    @pytest.mark.parametrize("backend", backends)
    def test_vectorized_calculation(self, t2_time, t2_spectrum_matrix, backend):
        """测试在CPU和GPU上的向量化计算。"""
        service = create_backend_service_by_name(backend)
        matrix_arr = service.to_backend(t2_spectrum_matrix)
        time_arr = service.to_backend(t2_time)

        v_micro, v_meso, v_macro = calculate_t2_porosity_components_vectorized(
            t2_spectrum_matrix=matrix_arr,
            t2_time=time_arr,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=service
        )

        n_depths = t2_spectrum_matrix.shape[0]
        assert v_micro.shape == (n_depths,)
        assert v_meso.shape == (n_depths,)
        assert v_macro.shape == (n_depths,)

        # 转换为CPU数组进行验证
        v_micro_cpu = service.to_cpu(v_micro)
        v_meso_cpu = service.to_cpu(v_meso)
        v_macro_cpu = service.to_cpu(v_macro)

        total_porosity = v_micro_cpu + v_meso_cpu + v_macro_cpu
        expected_total = np.sum(t2_spectrum_matrix, axis=1)
        np.testing.assert_allclose(total_porosity, expected_total, rtol=1e-6)

    @pytest.mark.parametrize("backend", backends)
    def test_vectorized_custom_t2_range(self, t2_time, t2_spectrum_matrix, backend):
        """测试向量化版本中的自定义T2范围参数。"""
        service = create_backend_service_by_name(backend)
        matrix_arr = service.to_backend(t2_spectrum_matrix)
        time_arr = service.to_backend(t2_time)

        # 全范围计算
        v_micro_full, v_meso_full, v_macro_full = calculate_t2_porosity_components_vectorized(
            t2_spectrum_matrix=matrix_arr,
            t2_time=time_arr,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=service
        )
        total_full = service.to_cpu(v_micro_full + v_meso_full + v_macro_full)

        # 限制范围计算
        v_micro_lim, v_meso_lim, v_macro_lim = calculate_t2_porosity_components_vectorized(
            t2_spectrum_matrix=matrix_arr,
            t2_time=time_arr,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            t2_range_min=1.0,
            t2_range_max=1000.0,
            backend_service=service
        )
        total_limited = service.to_cpu(v_micro_lim + v_meso_lim + v_macro_lim)

        # 限制范围的结果应该小于全范围的结果
        assert np.all(total_limited < total_full)

    def test_vectorized_invalid_t2_range(self, t2_time, t2_spectrum_matrix):
        """测试向量化版本中无效的T2范围参数。"""
        cpu_service = create_backend_service_by_name('cpu')
        with pytest.raises(WpNmrParameterError, match="T2范围无效"):
            calculate_t2_porosity_components_vectorized(
                t2_spectrum_matrix, t2_time, 3.0, 33.0,
                t2_range_min=1000.0, t2_range_max=100.0,
                backend_service=cpu_service
            )

    def test_vectorized_passing_backend_service_instance(self, t2_time, t2_spectrum_matrix):
        """测试向量化版本直接传递BackendService实例。"""
        cpu_service = create_backend_service_by_name('cpu')

        v_micro, v_meso, v_macro = calculate_t2_porosity_components_vectorized(
            t2_spectrum_matrix=t2_spectrum_matrix,
            t2_time=t2_time,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=cpu_service
        )

        assert v_micro.shape == (t2_spectrum_matrix.shape[0],)

    def test_vectorized_invalid_backend_type(self, t2_time, t2_spectrum_matrix):
        """测试向量化版本传递无效类型的backend参数。"""
        with pytest.raises(TypeError, match="不支持的backend类型"):
            calculate_t2_porosity_components_vectorized(
                t2_spectrum_matrix, t2_time, 3.0, 33.0, backend_service=None
            )

    @pytest.mark.gpu
    @skip_if_no_gpu
    def test_vectorized_consistency_cpu_vs_gpu(self, t2_time, t2_spectrum_matrix):
        """测试向量化版本在CPU和GPU上的一致性。"""
        # CPU计算
        service_cpu = create_backend_service_by_name('cpu')
        v_micro_cpu, v_meso_cpu, v_macro_cpu = calculate_t2_porosity_components_vectorized(
            t2_spectrum_matrix=t2_spectrum_matrix,
            t2_time=t2_time,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=service_cpu
        )

        # GPU计算
        service_gpu = create_backend_service_by_name('gpu')
        matrix_gpu = service_gpu.to_gpu(t2_spectrum_matrix)
        time_gpu = service_gpu.to_gpu(t2_time)
        v_micro_gpu, v_meso_gpu, v_macro_gpu = calculate_t2_porosity_components_vectorized(
            t2_spectrum_matrix=matrix_gpu,
            t2_time=time_gpu,
            t2cutoff_short=3.0,
            t2cutoff_long=33.0,
            backend_service=service_gpu
        )

        # 将GPU结果转换回CPU进行比较
        v_micro_gpu_cpu = service_gpu.to_cpu(v_micro_gpu)
        v_meso_gpu_cpu = service_gpu.to_cpu(v_meso_gpu)
        v_macro_gpu_cpu = service_gpu.to_cpu(v_macro_gpu)

        np.testing.assert_allclose(v_micro_cpu, v_micro_gpu_cpu, rtol=1e-6)
        np.testing.assert_allclose(v_meso_cpu, v_meso_gpu_cpu, rtol=1e-6)
        np.testing.assert_allclose(v_macro_cpu, v_macro_gpu_cpu, rtol=1e-6)
