"""scape.core.obmiq.training_facade - OBMIQ训练步骤门面

本模块提供了OBMIQ训练步骤的公共入口函数 `run_obmiq_training_step`。

Architecture
------------
层次/依赖: scape/core/obmiq层，是外部工作流调用OBMIQ训练功能的唯一入口
设计原则: 遵循《可追踪机器学习组件开发框架》的门面模式，负责编排内部规程

Functions:
    run_obmiq_training_step: 执行完整的训练工作流

References:
    - 《scape_core_obmiq_pytorch版开发计划.md》§2.2, §4
"""
from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, List, Tuple

import numpy as np
import pandas as pd
from logwp.infra import get_logger
from logwp.models.curve import CurveExpansionMode
from logwp.models.datasets.bundle import WpDataFrameBundle
import torch
from logwp.extras.tracking import RunContext

from .config import ObmiqTrainingConfig
from .constants import ObmiqPlotProfiles, ObmiqTrainingArtifacts
from logwp.extras.plotting import registry
from .internal.plotting_utils import generate_and_register_plots
from .artifact_handler import ObmiqArtifactHandler
from .internal import final_training_procedure, interpretability, tuning_procedure
from .internal.model_builder import OBMIQPyTorchModel
from .plotting import (
    replot_crossplot,
    replot_residuals_hist,
    replot_residuals_plot,
    replot_training_history,
    replot_captum_ig_summary,
    replot_grad_cam
)

logger = get_logger()


def _export_model_to_onnx(
    model_assets: Dict[str, Any],
    ctx: RunContext,
    step_dir: Path,
):
    """将训练好的PyTorch模型导出为ONNX格式。"""
    logger.info("--- Stage 5: Exporting Model to ONNX ---")
    try:
        # 1. 定义一个包装器，使模型forward签名与ONNX导出期望的多个输入参数一致
        class OnnxWrapper(torch.nn.Module):
            def __init__(self, model_to_wrap: OBMIQPyTorchModel):
                super().__init__()
                self.model = model_to_wrap

            def forward(self, sequence_input: torch.Tensor, tabular_input: torch.Tensor) -> torch.Tensor:
                """接收独立的张量，在内部打包成字典喂给原模型。"""
                return self.model({"sequence_input": sequence_input, "tabular_input": tabular_input})

        # 2. 重建并包装模型
        best_hps = model_assets["model_hyperparameters"]
        data_shapes = model_assets["data_shapes"]
        model = OBMIQPyTorchModel(best_hps, data_shapes).cpu()
        model.load_state_dict(model_assets["model_state_dict"])
        model.eval()
        wrapped_model = OnnxWrapper(model)

        # 3. 创建符合包装后模型输入的虚拟张量元组
        # ONNX导出需要一个示例输入来追踪模型的计算图
        sequence_length = model_assets["metadata"]["standard_t2_time_axis"].shape[0]
        dummy_seq_input = torch.randn(1, 1, sequence_length, requires_grad=True)
        dummy_tab_input = torch.randn(
            1, data_shapes["num_tabular_features"], requires_grad=True
        )
        # 将输入打包为元组，以匹配包装器的forward方法签名
        dummy_input_tuple = (dummy_seq_input, dummy_tab_input)

        # 4. 导出模型
        onnx_path = step_dir / "obmiq_model.onnx"
        torch.onnx.export(
            wrapped_model,
            dummy_input_tuple,
            str(onnx_path),
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=["sequence_input", "tabular_input"],
            output_names=["predictions"],
            dynamic_axes={
                "sequence_input": {0: "batch_size"},
                "tabular_input": {0: "batch_size"},
                "predictions": {0: "batch_size"},
            },
        )
        logger.info(f"Model successfully exported to ONNX format at: {onnx_path}")

        # 5. 注册ONNX模型产物
        ctx.register_artifact(
            onnx_path.relative_to(ctx.run_dir),
            ObmiqTrainingArtifacts.ONNX_MODEL.value,
            description="可用于跨平台部署的ONNX格式模型。",
        )

    except ImportError:
        logger.warning("`onnx`或`onnxruntime`库未安装，跳过ONNX导出。")
    except Exception as e:
        logger.error(f"ONNX导出失败: {e}", exc_info=True)


def _find_saliency_sample_indices(
    saliency_samples: List[Tuple[str, float]],
    final_eval_df: pd.DataFrame,
    well_col: str,
    depth_col: str,
) -> List[int]:
    """根据井名和目标深度，从DataFrame中找到最接近的样本索引。

    该函数实现了之前在 `_run_and_save_interpretability_artifacts` 中的循环逻辑，
    并采用了更高效的“先分组、后查找”策略。

    Args:
        saliency_samples: 一个包含 (井名, 深度) 元组的列表。
        final_eval_df: 包含所有井评估数据的DataFrame。
        well_col: DataFrame中井名列的名称。
        depth_col: DataFrame中深度列的名称。

    Returns:
        一个包含找到的样本索引的列表。
    """
    sample_indices = []
    # 根据项目规范 (SCAPE_方法说明书_V5.md)，井名应不区分大小写和首尾空格。
    # 因此，我们基于一个标准化的井名Series进行分组，以确保匹配的健壮性。
    try:
        # 创建一个标准化的Series用于分组，但不修改原始DataFrame
        normalized_well_series = final_eval_df[well_col].strip().upper()
        grouped_df = final_eval_df.groupby(normalized_well_series)
    except AttributeError:
        # 如果井名列不是字符串类型，则按原样分组，并记录警告。
        logger.warning(
            f"井名列 '{well_col}' 不是字符串类型，将进行区分大小写的精确匹配。"
            "这可能导致因大小写或空格问题而找不到井。"
        )
        grouped_df = final_eval_df.groupby(well_col)

    for well_name, depth in saliency_samples:
        # 对输入的井名也进行标准化，以匹配分组的键
        if isinstance(well_name, str):
            normalized_well_name = well_name.strip().upper()
        else:
            normalized_well_name = well_name  # 如果不是字符串，按原样使用

        if normalized_well_name not in grouped_df.groups:
            logger.warning(f"在数据中未找到井 '{well_name}' (标准化后为 '{normalized_well_name}')，跳过此Saliency样本。")
            continue
        well_df = grouped_df.get_group(normalized_well_name)
        closest_idx = (well_df[depth_col] - depth).abs().idxmin()
        sample_indices.append(closest_idx)
        logger.info(f"找到样本: 井='{well_name}', 目标深度={depth}, 实际深度={final_eval_df.loc[closest_idx, depth_col]:.2f} (索引={closest_idx})")
    return sample_indices


def _generate_and_save_cv_evaluation_artifacts(
    cv_evaluation_results: Dict[str, pd.DataFrame],
    ctx: RunContext,
    step_dir: Path,
):
    """根据交叉验证的盲测结果，生成泛化能力评估产物。

    此函数负责生成符合文档要求的“模型性能评估表 (表一)”和
    “预测值-真实值交叉图 (图二)”。

    Args:
        cv_evaluation_results: 从调优规程中收集到的交叉验证评估结果。
        ctx: 当前运行的上下文。
        step_dir: 当前步骤的输出目录。
    """
    logger.info("--- Stage 2 Artifacts: Generating Generalization Evaluation Artifacts ---")
    handler = ObmiqArtifactHandler()

    # 1. 生成性能评估表 (表一)
    if "per_fold_metrics_df" in cv_evaluation_results:
        per_fold_metrics_df = cv_evaluation_results["per_fold_metrics_df"]

        # 筛选出需要计算统计量的指标列
        metric_cols = [col for col in per_fold_metrics_df.columns if col.startswith(('r2_', 'rmse_'))]

        if metric_cols:
            # 计算均值和标准差
            summary_stats = per_fold_metrics_df[metric_cols].agg(['mean', 'std'])

            # 保存为CSV产物
            summary_path = step_dir / "lowo_cv_performance_summary.csv"
            handler.save_dataframe(summary_stats, summary_path)

            # 注册产物
            ctx.register_artifact(
                summary_path.relative_to(ctx.run_dir),
                ObmiqTrainingArtifacts.LOWO_CV_PERFORMANCE_SUMMARY.value,
                description="LOWO-CV中模型在所有留出井（测试集）上性能指标的均值和标准差。"
            )
            logger.info(f"成功生成并保存了泛化能力性能评估表: {summary_path}")

    # 2. 生成预测值-真实值交叉图 (图二)
    if "all_predictions_df" in cv_evaluation_results:
        all_predictions_df = cv_evaluation_results["all_predictions_df"]

        # 2.1 保存数据快照
        snapshot_path = step_dir / "lowo_cv_predictions.csv"
        handler.save_dataframe(all_predictions_df, snapshot_path)
        ctx.register_artifact(
            snapshot_path.relative_to(ctx.run_dir),
            ObmiqTrainingArtifacts.LOWO_CV_PREDICTIONS_DATA.value,
            description="LOWO-CV中所有井的盲测预测结果，用于生成泛化能力交叉图。"
        )
        logger.info(f"成功保存了泛化能力评估的数据快照: {snapshot_path}")

        # 2.2 绘制交叉图
        # 动态识别目标列和预测列
        pred_cols = [col for col in all_predictions_df.columns if col.endswith('_pred')]
        actual_cols = [col.replace('_pred', '') for col in pred_cols]

        if len(actual_cols) == 2 and all(col in all_predictions_df.columns for col in actual_cols):
            target_col_1, target_col_2 = actual_cols[0], actual_cols[1]
            pred_col_1, pred_col_2 = pred_cols[0], pred_cols[1]

            plot_map = {
                ObmiqTrainingArtifacts.LOWO_CV_CROSSPLOT_DT2_P50: (
                    replot_crossplot,
                    {"actual_col": target_col_1, "predicted_col": pred_col_1},
                    ObmiqPlotProfiles.CROSSPLOT,
                ),
                ObmiqTrainingArtifacts.LOWO_CV_CROSSPLOT_DPHIT_NMR: (
                    replot_crossplot,
                    {"actual_col": target_col_2, "predicted_col": pred_col_2},
                    ObmiqPlotProfiles.CROSSPLOT,
                ),
            }
            generate_and_register_plots(
                ctx, step_dir, plot_map, snapshot_path, "LOWO-CV Generalization Plot"
            )
            logger.info("成功生成并保存了泛化能力交叉图。")
        else:
            logger.warning("在 all_predictions_df 中未能识别出两个目标列及其预测列，跳过交叉图生成。")

def _run_and_save_interpretability_artifacts(
    model_assets: Dict[str, Any],
    final_eval_df: pd.DataFrame,
    config: ObmiqTrainingConfig,
    ctx: RunContext,
    step_dir: Path,
    saliency_samples: List[Tuple[str, float]] | None = None,
):
    """执行可解释性计算，并保存所有相关的数据快照和图表产物。"""
    logger.info("--- Stage 3: Model Interpretability Analysis (Captum) ---")
    results = interpretability.compute_captum_attributions(
        model_assets=model_assets,
        final_eval_df=final_eval_df,
    )

    if results is None:
        logger.warning("可解释性计算返回None，跳过产物生成。")
        return

    # 解包计算结果
    tabular_attributions = results["tabular_attributions"]
    sequence_attributions = results["sequence_attributions"]
    scaled_tabular_features_df = results["scaled_tabular_features_df"]
    original_sequences_arr = results["original_sequences_arr"]

    metadata = model_assets["metadata"]
    target_features = metadata["target_features"]
    t2_time_axis = metadata["standard_t2_time_axis"]
    handler = ObmiqArtifactHandler()

    # a. 处理表格特征归因 (类似SHAP)
    for target_name in target_features:
        # 保存数据快照
        tabular_attr_df = pd.DataFrame(
            tabular_attributions[target_name],
            columns=scaled_tabular_features_df.columns,
        )
        profile = registry.get(ObmiqPlotProfiles.CAPTUM_IG_SUMMARY.value
        ).with_updates(title_props={"label": f"Feature Attribution for {target_name}"})
        # 生成并保存图表
        plot_path = step_dir / f"captum_ig_summary_{target_name}.png"
        artifact_name_plot = f"{ObmiqTrainingArtifacts.CAPTUM_IG_SUMMARY_PLOT.value}_{target_name}"
        ctx.register_artifact(
            plot_path.relative_to(ctx.run_dir),
            artifact_name_plot,
            description=f"Captum Integrated Gradients归因分析图，展示表格特征对 {target_name} 的贡献度。",
        )
        snapshot_path = step_dir / f"captum_ig_summary_{target_name}_data.csv"
        handler.save_dataframe(tabular_attr_df, snapshot_path)
        artifact_name_data = f"{ObmiqTrainingArtifacts.CAPTUM_TABULAR_ATTRIBUTIONS_DATA.value}_{target_name}"
        ctx.register_artifact(
            snapshot_path.relative_to(ctx.run_dir),
            artifact_name_data,
            description=f"Captum Integrated Gradients归因数据 (表格特征, 目标 {target_name})",
        )
        replot_captum_ig_summary(
            snapshot_path=snapshot_path, profile=profile, output_path=plot_path
        )

    # b. 处理序列特征归因 (类似Grad-CAM)
    saliency_data_dir = step_dir / ObmiqTrainingArtifacts.CAPTUM_SEQUENCE_ATTRIBUTIONS_DIR.value.split(".")[-1]
    saliency_plot_dir = step_dir / ObmiqTrainingArtifacts.CAPTUM_SALIENCY_EXAMPLES_DIR.value.split(".")[-1]
    saliency_data_dir.mkdir(exist_ok=True)
    saliency_plot_dir.mkdir(exist_ok=True)

    sample_indices = []
    # 始终获取列名，因为无论如何文件名都需要它们
    well_col = model_assets["metadata"]["grouping_col"]
    depth_col = model_assets["metadata"]["depth_col"]

    if saliency_samples:
        sample_indices = _find_saliency_sample_indices(
            saliency_samples=saliency_samples,
            final_eval_df=final_eval_df,
            well_col=well_col,
            depth_col=depth_col,
        )

    # 如果用户未指定或未找到任何有效样本，则使用默认的回退逻辑
    if not sample_indices:
        logger.info("未指定或未找到有效Saliency样本，将使用默认的头、中、尾三个样本。")
        if len(final_eval_df) > 0:
            sample_indices = [
                final_eval_df.index[0],
                final_eval_df.index[len(final_eval_df) // 2],
                final_eval_df.index[-1],
            ]
        # 去重，以防DataFrame太短导致索引重复
        sample_indices = sorted(list(set(sample_indices)))

    for sample_idx in sample_indices:
        # 对于每个样本，获取其元数据用于文件名和标题
        current_well = final_eval_df.loc[sample_idx, well_col]
        current_depth = final_eval_df.loc[sample_idx, depth_col]

        for target_name in target_features:
            heatmap = sequence_attributions[target_name][sample_idx].flatten()
            original_image = original_sequences_arr[sample_idx].flatten()

            # --- 统一的文件名和标题逻辑 ---
            safe_well_name = str(current_well).replace('/', '_').replace('\\', '_')
            filename_stem = f"saliency_well_{safe_well_name}_depth_{current_depth:.2f}_target_{target_name}"
            title = f"Saliency Map for {target_name}\n(Well: {current_well}, Depth: {current_depth:.2f}m)"

            # 保存数据快照
            # 创建一个包含绘图所需全部信息的DataFrame
            snapshot_df = pd.DataFrame({
                "t2_axis": t2_time_axis,
                "original_image": original_image,
                "heatmap": heatmap,
            })
            snapshot_path = saliency_data_dir / f"{filename_stem}.csv"
            handler.save_dataframe(snapshot_df, snapshot_path)

            # 生成并保存图表
            plot_path = saliency_plot_dir / f"{filename_stem}.png"
            profile = registry.get(
                ObmiqPlotProfiles.GRAD_CAM.value
            ).with_updates(title_props={"label": title})
            replot_grad_cam(snapshot_path=snapshot_path, profile=profile, output_path=plot_path)

    ctx.register_artifact(saliency_data_dir.relative_to(ctx.run_dir), ObmiqTrainingArtifacts.CAPTUM_SEQUENCE_ATTRIBUTIONS_DIR.value)
    ctx.register_artifact(saliency_plot_dir.relative_to(ctx.run_dir), ObmiqTrainingArtifacts.CAPTUM_SALIENCY_EXAMPLES_DIR.value)


def _save_and_plot_final_model_evaluation(
    model_assets: Dict[str, Any],
    final_eval_df: pd.DataFrame,
    resolved_selectors: Dict[str, Any],
    ctx: RunContext,
    step_dir: Path,
):
    """保存并绘制最终模型评估图表."""
    logger.info("Saving final model evaluation data and generating plots...")
    handler = ObmiqArtifactHandler()
    eval_snapshot_path = step_dir / "final_model_evaluation.csv"
    handler.save_dataframe(final_eval_df, eval_snapshot_path)
    ctx.register_artifact(
        eval_snapshot_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.FINAL_MODEL_EVALUATION_DATA.value,
        description="最终模型在全部训练数据上的预测和残差结果。",
    )

    logger.info("Generating evaluation plots...")
    target_cols = resolved_selectors["target_cols"]
    target_col_1, target_col_2 = target_cols[0], target_cols[1]
    pred_col_1, pred_col_2 = f"{target_col_1}_pred", f"{target_col_2}_pred"
    residual_col_1, residual_col_2 = f"{target_col_1}_residual", f"{target_col_2}_residual"

    plot_map = {
        ObmiqTrainingArtifacts.EVAL_CROSSPLOT_DT2_P50: (
            replot_crossplot,
            {"actual_col": target_col_1, "predicted_col": pred_col_1},
            ObmiqPlotProfiles.CROSSPLOT,
        ),
        ObmiqTrainingArtifacts.EVAL_CROSSPLOT_DPHIT_NMR: (
            replot_crossplot,
            {"actual_col": target_col_2, "predicted_col": pred_col_2},
            ObmiqPlotProfiles.CROSSPLOT,
        ),
        ObmiqTrainingArtifacts.EVAL_RESIDUALS_PLOT_DT2_P50: (
            replot_residuals_plot,
            {"predicted_col": pred_col_1, "residual_col": residual_col_1},
            ObmiqPlotProfiles.RESIDUALS_PLOT,
        ),
        ObmiqTrainingArtifacts.EVAL_RESIDUALS_PLOT_DPHIT_NMR: (
            replot_residuals_plot,
            {"predicted_col": pred_col_2, "residual_col": residual_col_2},
            ObmiqPlotProfiles.RESIDUALS_PLOT,
        ),
        ObmiqTrainingArtifacts.EVAL_RESIDUALS_HIST_DT2_P50: (
            replot_residuals_hist,
            {"residual_col": residual_col_1},
            ObmiqPlotProfiles.RESIDUALS_HIST,
        ),
        ObmiqTrainingArtifacts.EVAL_RESIDUALS_HIST_DPHIT_NMR: (
            replot_residuals_hist,
            {"residual_col": residual_col_2},
            ObmiqPlotProfiles.RESIDUALS_HIST,
        ),
    }

    generate_and_register_plots(
        ctx, step_dir, plot_map, eval_snapshot_path, "Final model evaluation plot"
    )


def _validate_training_inputs(
    bundle: WpDataFrameBundle,
    resolved_selectors: Dict[str, Any],
    t2_time_axis: np.ndarray,
):
    """对训练步骤的输入参数进行基本验证。"""
    if bundle.data.empty:
        raise ValueError("输入数据 'train_bundle.data' 不能为空。")

    if len(resolved_selectors["target_cols"]) != 2:
        raise ValueError(f"参数 'target_features' 解析后必须包含两个目标列，但收到了 {len(resolved_selectors['target_cols'])} 个。")

    if not isinstance(t2_time_axis, np.ndarray):
        raise TypeError(f"参数 't2_time_axis' 必须是 NumPy 数组，但收到了 {type(t2_time_axis)}。")

    # 检查所有必需的列是否存在于DataFrame中
    all_resolved_cols = (
        set(resolved_selectors["sequence_cols"])
        | {resolved_selectors["normalization_col"]}
        | {resolved_selectors["depth_col"]}
        | {resolved_selectors["grouping_col"]}
        | set(resolved_selectors["tabular_cols"])
        | set(resolved_selectors["target_cols"])
    )
    missing_cols = all_resolved_cols - set(bundle.data.columns)

    if missing_cols:
        raise ValueError(
            "输入数据 'train_bundle.data' 中缺少以下必需的列: "
            f"{', '.join(sorted(list(missing_cols)))}"
        )
    logger.debug("所有训练输入参数验证通过。")


def _resolve_feature_selectors(
    bundle: WpDataFrameBundle,
    sequence_feature: str,
    normalization_feature: str,
    depth_feature: str,
    tabular_features: List[str],
    target_features: List[str],
    grouping_feature: str,
) -> Dict[str, Any]:
    """
    将用户提供的逻辑曲线名解析为DataFrame的物理列名。

    这是连接用户输入和内部处理逻辑的关键桥梁。它利用WpDataFrameBundle
    的元数据，将单个曲线名（如'T2_DIST'）展开为一个或多个DataFrame
    列名（如['T2_DIST_1', 'T2_DIST_2', ...]）。

    Args:
        bundle: 包含数据和元数据的WpDataFrameBundle。
        **kwargs: 所有以曲线名形式传入的特征选择器。

    Returns:
        一个字典，其中包含了解析后的DataFrame列名列表。

    Raises:
        ValueError: 如果解析后的列名在DataFrame中不存在。
    """
    # 序列特征预计为二维曲线，会展开为多个列
    sequence_cols = bundle.curve_metadata.expand_curve_names(
        [sequence_feature], mode=CurveExpansionMode.DATAFRAME
    )

    # 其他特征预计为一维曲线，展开后是只包含一个元素的列表
    normalization_col = bundle.curve_metadata.expand_curve_names(
        [normalization_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    depth_col = bundle.curve_metadata.expand_curve_names(
        [depth_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    grouping_col = bundle.curve_metadata.expand_curve_names(
        [grouping_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]
    target_cols = bundle.curve_metadata.expand_curve_names(
        target_features, mode=CurveExpansionMode.DATAFRAME
    )
    tabular_cols = bundle.curve_metadata.expand_curve_names(
        tabular_features, mode=CurveExpansionMode.DATAFRAME
    )

    logger.info("曲线名已成功解析为DataFrame列名。")
    return {
        "sequence_cols": sequence_cols,
        "normalization_col": normalization_col,
        "depth_col": depth_col,
        "tabular_cols": tabular_cols,
        "target_cols": target_cols,
        "grouping_col": grouping_col,
    }


def run_obmiq_training_step(
    config: ObmiqTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    *,
    sequence_feature: str,
    normalization_feature: str,
    depth_feature: str,
    tabular_features: List[str],
    target_features: List[str],
    grouping_feature: str,
    t2_time_axis: np.ndarray,
    saliency_samples: List[Tuple[str, float]] | None = None,
) -> Dict[str, Any]:
    """执行OBMIQ模型训练、超参数寻优和最终模型交付 (PyTorch版)。

    该函数是OBMIQ训练步骤的唯一入口，它编排了内部的两个核心规程：
    1. 超参数寻优规程 (tuning_procedure)
    2. 最终模型训练规程 (final_training_procedure)

    Args:
        config: 训练步骤的Pydantic配置对象。
        ctx: 当前运行的上下文，用于追踪。
        train_bundle: 包含所有训练数据的WpDataFrameBundle。
        sequence_feature: 作为1D-CNN输入的二维序列特征名称。
        normalization_feature: 用于对序列进行逐样本归一化的特征。
        tabular_features: 所有最终入选的一维表格特征列表。
        target_features: 两个目标特征的名称列表。
        grouping_feature: 用于交叉验证分组的特征/列名 (例如井号)。
        t2_time_axis: 标准T2时间轴，将随模型一同保存。
        saliency_samples: (可选) 用于生成Saliency Map(Grad-CAM)的样本列表，每个样本为(井名, 深度)元组。

    Returns:
        一个包含最终优化参数和状态的字典。
    """
    logger.info("===== OBMIQ Training Step (PyTorch) Started =====")
    step_dir = ctx.get_step_dir("obmiq_training_pytorch")

    # 0. 将用户提供的曲线名解析为DataFrame列名
    resolved_selectors = _resolve_feature_selectors(
        bundle=train_bundle,
        sequence_feature=sequence_feature,
        normalization_feature=normalization_feature,
        depth_feature=depth_feature,
        tabular_features=tabular_features,
        target_features=target_features,
        grouping_feature=grouping_feature,
    )

    # 将用户提供的原始逻辑曲线名也收集起来，用于后续的“逻辑契约”
    logical_selectors = {
        "sequence_feature": sequence_feature,
        "normalization_feature": normalization_feature,
        "depth_feature": depth_feature,
        "tabular_features": tabular_features,
        "target_features": target_features,
        "grouping_feature": grouping_feature,
    }

    _validate_training_inputs(
        bundle=train_bundle,
        resolved_selectors=resolved_selectors,
        t2_time_axis=t2_time_axis,
    )

    # Stage 0: 保存配置快照
    logger.info("--- Stage 0: Saving Configuration Snapshot ---")
    handler = ObmiqArtifactHandler()
    config_path = step_dir / "training_config.json"
    # Pydantic模型需要先转换为字典再保存
    handler.save_parameters(config.model_dump(), config_path)
    ctx.register_artifact(
        config_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.TRAINING_CONFIG.value,
        description="Snapshot of the training configuration used for this run.",
    )

    # 1. 内部规程一：超参数寻优
    logger.info("--- Stage 2: Hyperparameter Tuning using LOWO-CV ---")
    # 修改调用以接收交叉验证的详细评估结果
    best_hps, cv_report_df, cv_evaluation_results = tuning_procedure.run_hyperparameter_tuning_cv(
        train_df=train_bundle.data, config=config, feature_selectors=resolved_selectors
    )
    if cv_evaluation_results:
        logger.info("成功从交叉验证流程中收集到泛化能力评估数据。")
    logger.info(f"Best hyperparameters found: {best_hps}")

    # 2. 保存交叉验证和超参数寻优报告
    logger.info("--- Stage 2 Artifacts: Saving CV and Tuning Reports ---")
    handler = ObmiqArtifactHandler()

    # 保存CV性能报告
    cv_report_path = step_dir / "cv_performance_report.csv"
    handler.save_dataframe(cv_report_df, cv_report_path)
    ctx.register_artifact(
        cv_report_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.CV_PERFORMANCE_REPORT.value,
        description="LOWO-CV中每一折的最佳验证损失和对应的超参数。",
    )

    # 保存最佳超参数报告
    tuning_report_path = step_dir / "hyperparameter_tuning_report.json"
    handler.save_parameters(best_hps, tuning_report_path)
    ctx.register_artifact(
        tuning_report_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.TUNING_REPORT.value,
        description="在所有CV折中聚合得到的全局最佳超参数组合。",
    )

    # 新增：基于CV盲测结果，生成泛化能力评估产物
    if cv_evaluation_results:
        _generate_and_save_cv_evaluation_artifacts(
            cv_evaluation_results=cv_evaluation_results, ctx=ctx, step_dir=step_dir
        )

    # 3. 为TensorBoard创建日志目录
    tb_log_dir = step_dir / "tensorboard_logs"
    tb_log_dir.mkdir(exist_ok=True)
    ctx.register_artifact(
        tb_log_dir.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.TENSORBOARD_LOGS.value,
        description="用于TensorBoard可视化的日志文件目录。",
    )

    # 5. 内部规程二：最终模型训练
    logger.info("--- Stage 3: Final Model Training ---")
    # 确定数据维度信息，用于模型实例化
    data_shapes = {
        "num_tabular_features": len(resolved_selectors["tabular_cols"]),
        "num_targets": len(resolved_selectors["target_cols"]),
    }
    # 准备模型元数据，将与模型一同保存
    # 同时保存逻辑名(logical_selectors)和物理名(resolved_selectors)，
    # 以支持更灵活的预测流程。
    metadata = (
        logical_selectors | resolved_selectors | {"standard_t2_time_axis": t2_time_axis}
    )
    model_assets, history_df, final_eval_df = final_training_procedure.train_final_model(
        train_df=train_bundle.data,
        best_hps=best_hps,
        config=config,
        feature_selectors=resolved_selectors,
        data_shapes=data_shapes,
        metadata=metadata,
        tb_log_dir=str(tb_log_dir),
    )
    logger.info("Final model training completed.")

    # 6. 保存核心产物：模型资产包
    logger.info("--- Stage 3 Artifacts: Saving Final Model Assets ---")

    assets_path = step_dir / "model_assets_pytorch.pkl"
    handler.save_model_assets(model_assets, assets_path)
    ctx.register_artifact(
        assets_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.MODEL_ASSETS.value,
        description="包含模型权重、超参数和预处理器的PyTorch模型资产包。",
    )

    # 7. 保存并绘制训练历史
    history_snapshot_path = step_dir / "final_training_history.csv"
    handler.save_dataframe(history_df, history_snapshot_path)
    ctx.register_artifact(
        history_snapshot_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.FINAL_TRAINING_HISTORY_DATA.value,
        description="最终模型训练过程中的损失变化历史。",
    )

    logger.info("Plotting final training history...")
    history_plot_path = step_dir / "final_training_history.png"
    history_profile = registry.get(
        ObmiqPlotProfiles.TRAINING_HISTORY.value
    )
    replot_training_history(
        snapshot_path=history_snapshot_path,
        profile=history_profile,
        output_path=history_plot_path,
    )
    ctx.register_artifact(
        history_plot_path.relative_to(ctx.run_dir),
        ObmiqTrainingArtifacts.FINAL_TRAINING_HISTORY_PLOT.value,
        description="最终模型训练的损失曲线图。",
    )

    # 8. 保存并绘制最终模型评估图表
    _save_and_plot_final_model_evaluation(
        model_assets=model_assets,
        final_eval_df=final_eval_df,
        resolved_selectors=resolved_selectors,
        ctx=ctx,
        step_dir=step_dir,
        )

    # 9. 模型可解释性分析 (Captum)
    _run_and_save_interpretability_artifacts(
        model_assets=model_assets,
        final_eval_df=final_eval_df,
        config=config,
        ctx=ctx,
        step_dir=step_dir,
        saliency_samples=saliency_samples,
    )

    # 10. 导出模型为ONNX格式
    _export_model_to_onnx(model_assets, ctx, step_dir)

    logger.info("===== OBMIQ Training Step (PyTorch) Finished =====")
    return {"status": "completed", "best_hyperparameters": best_hps}
