#!/usr/bin/env python3
"""曲线元数据机器学习友好名称重命名服务。

提供CurveMetadata的曲线重命名功能，专为适配机器学习算法的输入要求。

Architecture
------------
层次/依赖: curve/internal服务层，曲线元数据重命名业务逻辑
设计原则: Service Layer模式、无状态设计、类型安全
性能特征: 内存操作、原地修改、高效重命名

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_DDS_logwp_数据集合并.md》§3.3 - 曲线重命名服务设计
- 《SCAPE_SAD_软件架构设计.md》§4.12 - 内部服务层设计
"""

from __future__ import annotations

from datetime import datetime
from typing import TYPE_CHECKING, Dict

from logwp.models.exceptions import WpCurveMetadataError
from logwp.infra.exceptions import ErrorContext
from logwp.models.utils import CaseInsensitiveDict
from logwp.infra import get_logger

if TYPE_CHECKING:
    from logwp.models.curve.metadata import CurveMetadata, CurveBasicAttributes

logger = get_logger(__name__)


def rename_curves_for_ml_service(
    metadata: CurveMetadata,
    name_mapping: CaseInsensitiveDict[str, str]
) -> CaseInsensitiveDict[str, str]:
    """
    根据提供的映射表，重命名曲线以适配机器学习算法的输入要求。

    此服务函数会就地修改传入的CurveMetadata对象，并返回一个用于
    同步更新DataFrame列名的映射字典。

    Args:
        metadata (CurveMetadata): 要就地修改的曲线元数据对象。
        name_mapping (Dict[str, str]): 映射字典，格式为 {'旧曲线名': '新曲线名'}。

    Returns:
        CaseInsensitiveDict[str, str]: DataFrame列名映射，格式为 {'旧列名': '新列名'}。

    Raises:
        WpCurveMetadataError: 如果映射中指定的旧曲线名在元数据中不存在。
    """
    from logwp.models.curve.metadata import CurveBasicAttributes

    logger.info(
        "开始为机器学习重命名曲线",
        operation="rename_curves_for_ml_service",
        mapping_count=len(name_mapping)
    )

    # --- 1. 验证阶段 ---
    all_old_names = set(name_mapping.keys())
    all_new_names = set(name_mapping.values())

    # 检查旧名称是否存在
    for old_name in all_old_names:
        if old_name not in metadata.curves:
            raise WpCurveMetadataError(
                f"无法重命名：曲线 '{old_name}' 在元数据中不存在。",
                context=ErrorContext(
                    operation="rename_curves_for_ml_service",
                    additional_info={
                        "requested_old_name": old_name,
                        "available_curves": list(metadata.curves.keys())
                    }
                )
            )

    # 检查新名称是否与不参与重命名的现有曲线冲突
    for new_name in all_new_names:
        if new_name in metadata.curves and new_name not in all_old_names:
            raise WpCurveMetadataError(
                f"无法重命名：目标名称 '{new_name}' 已被一个不参与本次重命名的现有曲线使用。",
                context=ErrorContext(
                    operation="rename_curves_for_ml_service",
                    additional_info={
                        "conflicting_new_name": new_name,
                        "name_mapping": name_mapping
                    }
                )
            )

    # --- 2. 准备阶段 ---
    column_rename_map: Dict[str, str] = {}
    renamed_attrs: Dict[str, 'CurveBasicAttributes'] = {}  # {new_name: new_attrs}

    for old_name, new_name in name_mapping.items():
        original_attrs = metadata.get_curve(old_name)

        new_attrs_dict = original_attrs.to_dict()
        new_attrs_dict['name'] = new_name

        if original_attrs.is_2d_composite_curve() and original_attrs.element_names:
            new_element_names = [
                elem.replace(old_name, new_name, 1) for elem in original_attrs.element_names
            ]
            new_attrs_dict['element_names'] = new_element_names

        new_attrs = CurveBasicAttributes.from_dict(new_attrs_dict)
        renamed_attrs[new_name] = new_attrs

        # 构建列名重命名映射
        if new_attrs.is_2d_composite_curve():
            if original_attrs.dataframe_element_names and new_attrs.dataframe_element_names:
                for old_col, new_col in zip(original_attrs.dataframe_element_names, new_attrs.dataframe_element_names):
                    column_rename_map[old_col] = new_col
        else:
            column_rename_map[original_attrs.dataframe_column_name] = new_attrs.dataframe_column_name

    # --- 3. 原子更新阶段 ---
    new_curves_dict = CaseInsensitiveDict()

    # 添加未被重命名的曲线
    for name, attrs in metadata.curves.items():
        if name not in all_old_names:
            new_curves_dict[name] = attrs

    # 添加重命名后的新曲线
    for new_name, new_attrs in renamed_attrs.items():
        new_curves_dict[new_name] = new_attrs

    # 原子性地替换整个曲线字典
    metadata.curves = new_curves_dict
    metadata.modified_at = datetime.now()

    logger.info(
        "曲线重命名完成",
        operation="rename_curves_for_ml_service",
        renamed_count=len(name_mapping),
        column_map_size=len(column_rename_map)
    )

    return CaseInsensitiveDict(column_rename_map)
