from __future__ import annotations

from logwp.models.exceptions import ErrorContext, WpCurveMetadataError, WpDataError, WpValidationError

"""logwp.models.datasets.bundle - 数据集Bundle封装

提供轻量级的数据集数据封装，支持DataFrame和NumPy数组两种格式。
Bundle对象包含数据本身以及便捷的元数据访问接口。

Architecture
------------
层次/依赖: datasets模块数据封装层
设计原则: DTO模式、类型安全、便捷访问
性能特征: 轻量级封装、自动化属性计算、大小写不敏感

Bundle类型：
- WpDatasetBundle: 抽象基类，定义通用接口
- WpDataFrameBundle: DataFrame数据封装
- WpArrayBundle: NumPy数组数据封装

遵循CCG规范：
- CS-2: 使用Wp前缀命名
- TS-1: 完整类型注解覆盖
- EH-4: 异常链保持
- LG-2: 结构化日志信息

References
----------
- 《SCAPE_SAD_软件架构设计.md》§6.2 - 内部服务层设计
- 《SCAPE_API_logwp_models.md》§4 - 曲线元数据管理
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import TYPE_CHECKING, Any, Optional, Dict, List
import pandas as pd
import numpy as np
from logwp.models.constants import WpCurveCategory, WpDepthRole, WpDataType
from logwp.models.curve import CurveBasicAttributes
from logwp.models.utils import CaseInsensitiveDict
from logwp.models.datasets.internal.statistic_calculator import calculate_series_statistic
from logwp.models.datasets.internal.ml_converter import convert_bundle_to_structured_ml_inputs
from logwp.infra import get_logger
try:
    from scipy.interpolate import interp1d
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    interp1d = None  # To satisfy linters

if TYPE_CHECKING:
    from logwp.models.curve.metadata import CurveMetadata

logger = get_logger(__name__)


@dataclass
class WpDatasetBundle(ABC):
    """数据集Bundle抽象基类。

    提供轻量级的数据集数据封装，包含数据本身以及便捷的元数据访问接口。
    Bundle对象是简单的DTO（Data Transfer Object），专注于数据传输和访问。

    Architecture Notes:
        - DTO模式：纯数据传输对象，不包含业务逻辑
        - 自动化：基于curve_metadata自动计算便捷属性
        - 类型安全：明确区分单深度和区间深度情况
        - 大小写不敏感：所有映射都支持大小写不敏感访问

    Attributes:
        name: 数据集名称
        curve_metadata: 完整的曲线元数据
        well_curve_map: 井名曲线名到Bundle名的映射（大小写不敏感）
        is_interval_bundle: 是否为区间数据集（双深度）
        depth_curve_map: 深度曲线名到Bundle名的映射（单深度情况）
        depth_top_curve_map: 顶深曲线名到Bundle名的映射（区间情况）
        depth_bottom_curve_map: 底深曲线名到Bundle名的映射（区间情况）

    Examples:
        >>> # 创建Bundle后自动计算便捷属性
        >>> bundle = WpDataFrameBundle(name="test", curve_metadata=metadata, data=df)
        >>>
        >>> # 访问井名曲线
        >>> well_curve = bundle.well_curve_map.get("WELL")
        >>>
        >>> # 检查数据集类型
        >>> if bundle.is_interval_bundle:
        ...     top_curve = bundle.depth_top_curve_map.get("MD_TOP")
        ...     bottom_curve = bundle.depth_bottom_curve_map.get("MD_BOTTOM")
        ... else:
        ...     depth_curve = bundle.depth_curve_map.get("MD")
    """

    # 基础属性
    name: str
    curve_metadata: 'CurveMetadata'

    # 便捷属性（自动计算）
    well_curve_map: CaseInsensitiveDict[str, str] = field(init=False)
    is_interval_bundle: bool = field(init=False)
    depth_curve_map: Optional[CaseInsensitiveDict[str, str]] = field(init=False)
    depth_top_curve_map: Optional[CaseInsensitiveDict[str, str]] = field(init=False)
    depth_bottom_curve_map: Optional[CaseInsensitiveDict[str, str]] = field(init=False)

    def __post_init__(self):
        """初始化后自动计算便捷属性。"""
        self._initialize_convenience_properties()

        logger.debug(
            "Bundle便捷属性初始化完成",
            operation="__post_init__",
            bundle_name=self.name,
            is_interval=self.is_interval_bundle,
            well_curves_count=len(self.well_curve_map),
            depth_curves_count=len(self.depth_curve_map) if self.depth_curve_map else 0,
            top_curves_count=len(self.depth_top_curve_map) if self.depth_top_curve_map else 0,
            bottom_curves_count=len(self.depth_bottom_curve_map) if self.depth_bottom_curve_map else 0
        )

    def _initialize_convenience_properties(self):
        """基于curve_metadata初始化便捷属性。

        使用CurveMetadata的标准API自动计算所有便捷属性：
        1. 井名曲线映射
        2. 判断是否为区间Bundle
        3. 深度曲线映射（根据数据集类型）

        严格遵循API文档中的元数据处理逻辑，避免硬编码。
        """
        # 1. 井名曲线映射（使用标准API）
        well_curves = self.curve_metadata.get_well_identifier_curves()
        self.well_curve_map = CaseInsensitiveDict({
            curve_name: curve_name for curve_name in well_curves
        })

        # 2. 判断是否为区间Bundle（使用标准API）
        top_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.TOP)
        bottom_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.BOTTOM)
        self.is_interval_bundle = bool(top_curves and bottom_curves)

        # 3. 深度曲线映射（使用标准API，正确处理单深度和区间深度）
        if self.is_interval_bundle:
            # 区间数据集：分别映射顶深和底深
            self.depth_top_curve_map = CaseInsensitiveDict({
                curve_name: curve_name for curve_name in top_curves
            })
            self.depth_bottom_curve_map = CaseInsensitiveDict({
                curve_name: curve_name for curve_name in bottom_curves
            })
            self.depth_curve_map = None

            logger.debug(
                "区间Bundle深度映射初始化",
                operation="_initialize_convenience_properties",
                top_curves=top_curves,
                bottom_curves=bottom_curves,
                top_count=len(top_curves),
                bottom_count=len(bottom_curves)
            )
        else:
            # 单深度数据集：映射单一深度曲线
            single_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
            self.depth_curve_map = CaseInsensitiveDict({
                curve_name: curve_name for curve_name in single_curves
            })
            self.depth_top_curve_map = None
            self.depth_bottom_curve_map = None

            logger.debug(
                "单深度Bundle深度映射初始化",
                operation="_initialize_convenience_properties",
                single_curves=single_curves,
                single_count=len(single_curves)
            )

    # data属性由子类的dataclass字段定义，不需要抽象属性

    def get_well_names(self) -> Optional[Any]:
        """获取井名数据。

        使用curve_metadata API获取井名曲线，避免硬编码假设。

        Returns:
            Optional[Any]: 井名数据，具体类型由子类决定
            - WpDataFrameBundle: pd.Series或None
            - WpArrayBundle: np.ndarray或None
        """
        if not self.well_curve_map:
            return None

        # 使用API获取第一个井名曲线（而非硬编码假设）
        well_curve_names = self.curve_metadata.get_well_identifier_curves()
        if not well_curve_names:
            return None

        # 获取第一个井名曲线的数据
        well_curve_name = well_curve_names[0]
        return self._get_curve_data(well_curve_name)

    def get_depths(self) -> Optional[Any]:
        """获取深度数据（单深度情况）。

        使用curve_metadata API获取单深度曲线，仅在非区间Bundle时有效。

        Returns:
            Optional[Any]: 深度数据，仅在非区间Bundle时有效
            - WpDataFrameBundle: pd.Series或None
            - WpArrayBundle: np.ndarray或None
        """
        if self.is_interval_bundle:
            return None

        # 使用API获取单深度曲线
        single_depth_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
        if not single_depth_curves:
            return None

        # 获取第一个单深度曲线的数据
        depth_curve_name = single_depth_curves[0]
        return self._get_curve_data(depth_curve_name)

    def get_top_depths(self) -> Optional[Any]:
        """获取顶深数据（区间情况）。

        使用curve_metadata API获取顶深曲线，仅在区间Bundle时有效。

        Returns:
            Optional[Any]: 顶深数据，仅在区间Bundle时有效
        """
        if not self.is_interval_bundle:
            return None

        # 使用API获取顶深曲线
        top_depth_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.TOP)
        if not top_depth_curves:
            return None

        # 获取第一个顶深曲线的数据
        top_curve_name = top_depth_curves[0]
        return self._get_curve_data(top_curve_name)

    def get_bottom_depths(self) -> Optional[Any]:
        """获取底深数据（区间情况）。

        使用curve_metadata API获取底深曲线，仅在区间Bundle时有效。

        Returns:
            Optional[Any]: 底深数据，仅在区间Bundle时有效
        """
        if not self.is_interval_bundle:
            return None

        # 使用API获取底深曲线
        bottom_depth_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.BOTTOM)
        if not bottom_depth_curves:
            return None

        # 获取第一个底深曲线的数据
        bottom_curve_name = bottom_depth_curves[0]
        return self._get_curve_data(bottom_curve_name)

    @abstractmethod
    def _get_curve_data(self, curve_name: str) -> Optional[Any]:
        """获取指定曲线的数据，子类实现具体逻辑。

        Args:
            curve_name: 曲线名称

        Returns:
            Optional[Any]: 曲线数据
        """
        pass


@dataclass
class WpDataFrameBundle(WpDatasetBundle):
    """DataFrame数据包封装。

    封装pandas DataFrame数据，提供便捷的元数据访问接口。
    适用于需要进一步处理DataFrame的场景。

    Attributes:
        data: pandas DataFrame数据
        curve_to_columns_map: 曲线名到DataFrame列名的映射

    Examples:
        >>> # 创建DataFrame Bundle
        >>> bundle = WpDataFrameBundle(
        ...     name="test_logs",
        ...     curve_metadata=metadata,
        ...     data=df,
        ...     curve_to_columns_map=mapping
        ... )
        >>>
        >>> # 访问DataFrame数据
        >>> df = bundle.data
        >>> assert isinstance(df, pd.DataFrame)
        >>>
        >>> # 获取曲线列名映射
        >>> gr_columns = bundle.curve_to_columns_map.get("GR")  # ["GR"]
        >>> t2_columns = bundle.curve_to_columns_map.get("T2_VALUE")  # ["T2_VALUE_1", "T2_VALUE_2", ...]
        >>>
        >>> # 获取井名数据
        >>> well_names = bundle.get_well_names()  # pd.Series
    """

    # 必需字段（无默认值）
    data: pd.DataFrame = field()
    curve_to_columns_map: Dict[str, list[str]] = field()

    @classmethod
    def from_dataframe(cls, name: str, df: pd.DataFrame) -> 'WpDataFrameBundle':
        """
        从一个pandas DataFrame便捷地创建WpDataFrameBundle。

        此方法会自动推断元数据，并构建一个完整的Bundle对象，
        极大地简化了测试代码的编写。

        Args:
            name: Bundle的名称。
            df: 包含测井数据的pandas DataFrame。

        Returns:
            一个配置完整的WpDataFrameBundle实例。
        """
        # 1. 利用CurveMetadata的静态方法推断元数据
        # is_interval 标志在这里没有直接使用，因为 __post_init__ 会根据元数据自动设置 is_interval_bundle
        from logwp.models.curve.metadata import CurveMetadata
        metadata, _ = CurveMetadata.from_dataframe(df)

        # 2. 利用CurveMetadata的便捷方法创建列映射
        curve_to_columns_map = metadata.create_curve_to_columns_map()

        # 3. 创建并返回WpDataFrameBundle实例
        return cls(
            name=name,
            data=df,
            curve_metadata=metadata,
            curve_to_columns_map=curve_to_columns_map,
        )

    def safe_get_well_names(self) -> list[str]:
        """
        安全地从Bundle中提取所有唯一的井名。

        此方法旨在提供一种健壮的方式来获取井列表，即使在元数据不完整
        或数据存在问题的情况下也能避免抛出异常。

        Returns:
            list[str]: 一个包含所有唯一井名的列表。如果无法提取井名
                       （例如，缺少井标识曲线），则返回一个空列表。

        Note:
            - 此方法内部会处理所有异常，并记录警告日志。
            - 结果将始终是一个字符串列表。
            - 空值(NaN)会被自动忽略。
        """
        try:
            # 如果DataFrame为空，直接返回空列表
            if self.data.empty:
                return []

            # 获取井名标识曲线
            well_curve_names = self.curve_metadata.get_well_identifier_curves()
            if not well_curve_names:
                logger.warning(
                    "无法安全获取井列表：Bundle的元数据中缺少井名标识曲线。",
                    operation="safe_get_well_names",
                    bundle_name=self.name,
                )
                return []

            # 通常只使用第一个井名标识
            well_curve = self.curve_metadata.get_curve(well_curve_names[0])
            well_column = well_curve.dataframe_column_name

            if well_column not in self.data.columns:
                logger.warning(
                    f"无法安全获取井列表：DataFrame中缺少井名列 '{well_column}'。",
                    operation="safe_get_well_names",
                    bundle_name=self.name,
                    additional_info={"available_columns": list(self.data.columns)}
                )
                return []

            # 提取唯一井名，丢弃空值，转换为字符串列表
            wells = self.data[well_column].dropna().unique().tolist()
            return [str(w) for w in wells]

        except Exception as e:
            logger.warning(
                "安全获取井列表时发生未知错误，返回空列表。",
                operation="safe_get_well_names",
                bundle_name=self.name,
                error=str(e),
            )
            return []

    def get_well_column_name(self) -> str:
        """
        安全地获取井名标识符在DataFrame中的列名。

        此方法提供了一种健壮的方式来获取井名列，避免了直接访问
        内部元数据结构的复杂性。

        Returns:
            str: 井名标识符在DataFrame中的列名。

        Raises:
            WpDataError: 如果Bundle中未找到井名标识曲线，或无法确定其列名。
        """
        # 1. 使用标准API获取井名标识曲线
        well_curve_names = self.curve_metadata.get_well_identifier_curves()
        if not well_curve_names:
            raise WpDataError(
                "无法获取井名列：Bundle的元数据中缺少井名标识曲线。",
                context=ErrorContext(
                    operation="get_well_column_name",
                    bundle_name=self.name,
                    reason="no_well_identifier_found"
                )
            )

        # 2. 通常只使用第一个井名标识
        well_curve_attrs = self.curve_metadata.get_curve(well_curve_names[0])
        if not well_curve_attrs or not well_curve_attrs.dataframe_column_name:
            raise WpDataError(
                "无法确定井名标识符在DataFrame中的列名。",
                context=ErrorContext(
                    operation="get_well_column_name",
                    bundle_name=self.name,
                    reason="well_column_name_undetermined",
                    additional_info={"well_curve_name": well_curve_names[0]}
                )
            )

        well_col = well_curve_attrs.dataframe_column_name

        # 3. 验证列是否存在于DataFrame中
        if well_col not in self.data.columns:
            raise WpDataError(
                f"井名列 '{well_col}' 不存在于DataFrame中。",
                context=ErrorContext(
                    operation="get_well_column_name",
                    bundle_name=self.name,
                    reason="well_column_missing_in_dataframe",
                    additional_info={"available_columns": list(self.data.columns)}
                )
            )

        return well_col

    def _get_curve_data(self, curve_name: str) -> Optional[pd.Series]:
        """获取指定曲线的DataFrame数据。

        Args:
            curve_name: 曲线名称

        Returns:
            Optional[pd.Series]: 曲线数据Series，如果是多列则返回第一列
        """
        # 直接通过curve_metadata获取曲线信息
        try:
            curve_attrs = self.curve_metadata.get_curve(curve_name)
            if not curve_attrs:
                return None

            # 获取DataFrame列名
            if curve_attrs.is_1d_curve():
                column_name = curve_attrs.dataframe_column_name
                if column_name in self.data.columns:
                    return self.data[column_name]
            elif curve_attrs.is_2d_composite_curve():
                # 对于二维曲线，返回第一个元素列
                element_names = curve_attrs.dataframe_element_names
                if element_names and element_names[0] in self.data.columns:
                    return self.data[element_names[0]]

            return None
        except Exception:
            # 回退到curve_to_columns_map
            columns = self.curve_to_columns_map.get(curve_name)
            if not columns:
                return None

            column_name = columns[0]
            if column_name in self.data.columns:
                return self.data[column_name]

            return None

    def get_identifier_dataframe(self) -> pd.DataFrame:
        """
        获取一个包含所有标识符列（井名和深度）的DataFrame。

        此方法是一个便捷的辅助函数，它会自动识别数据集是单深度还是
        区间类型，并提取相应的井名和深度列，将它们合并成一个
        新的DataFrame。这些列共同构成了数据集中每个数据点的唯一标识。

        Returns:
            pd.DataFrame: 一个包含井名和深度列的DataFrame。如果找不到
                          任何标识符列，则返回一个与原始数据索引对齐的空DataFrame。
        """
        identifier_parts = []

        # 1. 获取井名数据
        well_series = self.get_well_names()
        if well_series is not None:
            identifier_parts.append(well_series)

        # 2. 获取深度数据（根据Bundle类型）
        if self.is_interval_bundle:
            top_series = self.get_top_depths()
            if top_series is not None:
                identifier_parts.append(top_series)

            bottom_series = self.get_bottom_depths()
            if bottom_series is not None:
                identifier_parts.append(bottom_series)
        else:
            depth_series = self.get_depths()
            if depth_series is not None:
                identifier_parts.append(depth_series)

        if not identifier_parts:
            return pd.DataFrame(index=self.data.index)

        return pd.concat(identifier_parts, axis=1)

    def to_all_wells_data(self) -> Dict[str, pd.DataFrame]:
        """
        将Bundle中的多井DataFrame拆分为按井名组织的字典。

        此方法是为 `run_swift_pso` 工作流准备 `all_wells_data` 输入的
        便捷辅助函数。

        Returns:
            Dict[str, pd.DataFrame]: 一个字典，键是井名，值是对应井的DataFrame。
                每个DataFrame都拥有独立的、从0开始的整数索引 (RangeIndex)。

        Raises:
            WpDataError: 如果Bundle中缺少井名标识曲线，则无法进行拆分。

        Examples:
            >>> # 假设 bundle.data 包含 'C-1' 和 'C-2' 两口井的数据
            >>> all_wells_data = bundle.to_all_wells_data()
            >>>
            >>> assert isinstance(all_wells_data, dict)
            >>> assert "C-1" in all_wells_data
            >>> assert "C-2" in all_wells_data
            >>>
            >>> c1_df = all_wells_data["C-1"]
            >>> assert isinstance(c1_df.index, pd.RangeIndex)
            >>> assert c1_df['WELL'].nunique() == 1
        """
        logger.info(
            "开始按井拆分DataFrame Bundle",
            operation="to_all_wells_data",
            bundle_name=self.name
        )

        # 1. 获取井名列
        well_curve_names = self.curve_metadata.get_well_identifier_curves()
        if not well_curve_names:
            raise WpDataError(
                "无法按井拆分：Bundle中缺少井名标识曲线。",
                context=ErrorContext(
                    operation="to_all_wells_data",
                    bundle_name=self.name,
                    reason="no_well_identifier_found"
                )
            )

        # 通常只使用第一个井名标识
        well_column = self.curve_metadata.get_curve(well_curve_names[0]).dataframe_column_name

        if well_column not in self.data.columns:
            raise WpDataError(
                f"无法按井拆分：DataFrame中缺少井名列 '{well_column}'。",
                context=ErrorContext(
                    operation="to_all_wells_data",
                    bundle_name=self.name,
                    reason="well_column_missing_in_dataframe"
                )
            )

        # 2. 使用groupby进行拆分，并使用字典推导式高效构建结果
        # 关键：对每个子DataFrame调用 .reset_index(drop=True) 以确保独立的RangeIndex
        all_wells_data = {
            str(well_name): well_df.reset_index(drop=True)
            for well_name, well_df in self.data.groupby(well_column)
        }

        logger.info(
            "DataFrame Bundle按井拆分完成",
            operation="to_all_wells_data",
            bundle_name=self.name,
            wells_found=list(all_wells_data.keys())
        )

        return all_wells_data

    def get_wells(self) -> list[str]:
        """
        获取Bundle中包含的所有唯一井名。

        此方法从DataFrame中提取唯一的井名，并按其在数据中的首次出现
        顺序返回。

        Returns:
            list[str]: 一个包含所有唯一井名的列表。如果数据集为空，返回空列表。

        Raises:
            WpDataError: 如果Bundle的元数据中缺少井名标识曲线，或DataFrame中
                         缺少对应的列。

        Examples:
            >>> # 假设 bundle.data 包含 'C-1' 和 'C-2' 两口井的数据
            >>> well_list = bundle.get_wells()
            >>> print(well_list)
            ['C-1', 'C-2']
        """
        try:
            # 如果DataFrame为空，直接返回空列表
            if self.data.empty:
                return []

            # 获取井名标识曲线
            well_curve_names = self.curve_metadata.get_well_identifier_curves()
            if not well_curve_names:
                raise WpDataError(
                    "无法获取井列表：Bundle的元数据中缺少井名标识曲线。",
                    context=ErrorContext(
                        operation="get_wells",
                        dataset_name=self.name,
                        reason="no_well_identifier_found"
                    )
                )

            # 通常只使用第一个井名标识
            well_curve = self.curve_metadata.get_curve(well_curve_names[0])
            well_column = well_curve.dataframe_column_name

            if well_column not in self.data.columns:
                raise WpDataError(
                    f"无法获取井列表：DataFrame中缺少井名列 '{well_column}'。",
                    context=ErrorContext(
                        operation="get_wells",
                        dataset_name=self.name,
                        additional_info={"available_columns": list(self.data.columns)}
                    )
                )

            # 提取唯一井名，保持出现顺序，并转换为字符串
            wells = self.data[well_column].dropna().unique().tolist()
            return [str(w) for w in wells]

        except WpDataError:
            # 直接重新抛出已正确封装的WpDataError
            raise
        except Exception as e:
            # 捕获其他意外错误并封装
            raise WpDataError(f"获取井列表时发生未知错误: {e}", context=ErrorContext(operation="get_wells", dataset_name=self.name)) from e

    def get_interval_curve_values(
        self, well_name: str, curve_names: list[str]
    ) -> list[list[Any]]:
        """
        获取指定井的区间型曲线值，以轻量化列表形式返回。

        此方法专门用于处理`interval`类型的Bundle，将每个层段的数据
        提取为 `[top, bottom, v1, v2, ...]` 的格式，便于后续的
        循环处理和算法计算。

        Args:
            well_name (str): 要提取数据的井名。
            curve_names (list[str]): 要提取值的曲线名称列表。

        Returns:
            list[list[Any]]: 一个列表，其中每个子列表代表一个层段，
            格式为 `[顶深, 底深, 曲线1的值, 曲线2的值, ...]`。
            返回的曲线值顺序与 `curve_names` 列表的顺序一致。

        Raises:
            WpValidationError: 如果此Bundle不是`interval`类型。
            WpDataError: 如果缺少井名或深度曲线，或指定的井不存在。
            WpCurveMetadataError: 如果指定的曲线名称在元数据中不存在。

        Examples:
            >>> # 假设plt_bundle是一个PLT解释结果的interval bundle
            >>> # 它包含 'Top_i', 'Bottom_i', 'QOZI' 等曲线
            >>> try:
            ...     plt_data = plt_bundle.get_interval_curve_values(
            ...         well_name='C-1',
            ...         curve_names=['QOZI']
            ...     )
            ...     # 返回结果示例:
            ...     # [
            ...     #   [2500.0, 2505.0, 150.5],
            ...     #   [2505.0, 2510.0, 230.0],
            ...     #   ...
            ...     # ]
            ... except (WpValidationError, WpDataError) as e:
            ...     print(f"提取失败: {e}")

        References:
            - 《SCAPE_MS_方法说明书.md》§4.5 - PLT盲井检验
        """
        # 1. 验证是否为Interval类型
        if not self.is_interval_bundle:
            raise WpValidationError(
                "此方法仅适用于Interval类型的Bundle。",
                context=ErrorContext(
                    operation="get_interval_curve_values",
                    bundle_name=self.name,
                    reason="bundle_not_interval_type",
                ),
            )

        # 2. 获取井名、顶深、底深列名
        well_id_curves = self.curve_metadata.get_well_identifier_curves()
        if not well_id_curves:
            raise WpDataError("Bundle中缺少井名标识曲线。", context=ErrorContext(operation="get_interval_curve_values", dataset_name=self.name))
        well_col = self.curve_metadata.get_curve(well_id_curves[0]).dataframe_column_name

        top_depth_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.TOP)
        if not top_depth_curves:
            raise WpDataError("Bundle中缺少顶深(TOP)曲线。", context=ErrorContext(operation="get_interval_curve_values", dataset_name=self.name))
        top_col = self.curve_metadata.get_curve(top_depth_curves[0]).dataframe_column_name

        bottom_depth_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.BOTTOM)
        if not bottom_depth_curves:
            raise WpDataError("Bundle中缺少底深(BOTTOM)曲线。", context=ErrorContext(operation="get_interval_curve_values", dataset_name=self.name))
        bottom_col = self.curve_metadata.get_curve(bottom_depth_curves[0]).dataframe_column_name

        # 3. 筛选指定井的数据
        if well_name not in self.data[well_col].unique():
            raise WpDataError(
                f"井 '{well_name}' 在Bundle中不存在。",
                context=ErrorContext(
                    operation="get_interval_curve_values",
                    bundle_name=self.name,
                    additional_info={"available_wells": self.data[well_col].unique().tolist()}
                )
            )
        well_df = self.data[self.data[well_col] == well_name]

        # 4. 获取请求的数据曲线列名
        data_cols = []
        for curve_name in curve_names:
            curve_attrs = self.curve_metadata.get_curve(curve_name)
            if not curve_attrs:
                raise WpCurveMetadataError(f"曲线 '{curve_name}' 在元数据中不存在。")
            # 假设为一维曲线，直接获取其DataFrame列名
            data_cols.append(curve_attrs.dataframe_column_name)

        # 5. 组装所有需要提取的列，并验证它们是否存在
        all_required_cols = [top_col, bottom_col] + data_cols
        missing_cols = [col for col in all_required_cols if col not in well_df.columns]
        if missing_cols:
            raise WpDataError(
                f"DataFrame中缺少必要的列: {missing_cols}",
                context=ErrorContext(operation="get_interval_curve_values", dataset_name=self.name)
            )

        # 6. 提取数据并转换为目标格式
        # .values.tolist() 是一个高效地将DataFrame转换为list[list]的方法
        result_list = well_df[all_required_cols].values.tolist()

        return result_list

    def get_curve_values_at_depths(
        self,
        well_name: str,
        depths: list[float],
        curve_name: str,
        interpolation_method: str = 'nearest'
    ) -> list[Any]:
        """
        根据一个离散的深度点列表，从一条连续曲线上查询（或插值）对应的值。

        此方法是连接离散点数据（如岩心）和连续曲线（如模型预测）的关键工具，
        常用于岩心渗透率盲井检验等场景。

        Args:
            well_name (str): 要查询的井名。
            depths (list[float]): 一个包含所有目标深度的Python列表。
            curve_name (str): 要查询的曲线的逻辑名称。
            interpolation_method (str): 插值方法。支持 'nearest' (最近邻) 和
                'linear' (线性)。默认为 'nearest'。

        Returns:
            list[Any]: 一个与输入`depths`列表长度相同、顺序一致的结果列表。
                对于无法插值的点（如超出范围），将返回 np.nan。

        Raises:
            WpValidationError: 如果此Bundle是`interval`类型或插值方法不受支持。
            WpDataError: 如果缺少井名/深度曲线，或指定的井不存在，或曲线无有效数据。
            WpCurveMetadataError: 如果指定的曲线名称在元数据中不存在。
            ImportError: 如果需要'scipy'库但未安装。

        Examples:
            >>> # 假设 k_pred_bundle 是一个包含预测渗透率的连续型Bundle
            >>> core_depths = [2501.2, 2503.5, 2508.9]
            >>> pred_at_core_depths = k_pred_bundle.get_curve_values_at_depths(
            ...     well_name='T-1',
            ...     depths=core_depths,
            ...     curve_name='K_PRED',
            ...     interpolation_method='linear'
            ... )
            >>> # 返回: [150.3, 182.1, 210.5] (示例值)

        References:
            - 《SCAPE_MS_方法说明书.md》§4.6 - 岩心渗透率盲井检验
        """
        # 1. 验证
        if not SCIPY_AVAILABLE:
            raise ImportError(
                "此功能需要 'scipy' 库。请运行 'pip install scipy' 进行安装。"
            )

        if self.is_interval_bundle:
            raise WpValidationError(
                "此方法仅适用于非Interval类型的Bundle（如Continuous或Point）。",
                context=ErrorContext(operation="get_curve_values_at_depths", dataset_name=self.name)
            )

        supported_methods = {'nearest', 'linear'}
        if interpolation_method not in supported_methods:
            raise WpValidationError(
                f"不支持的插值方法: '{interpolation_method}'. 支持的方法: {supported_methods}",
                context=ErrorContext(operation="get_curve_values_at_depths", dataset_name=self.name)
            )

        # 2. 获取列名
        well_id_curves = self.curve_metadata.get_well_identifier_curves()
        if not well_id_curves:
            raise WpDataError("Bundle中缺少井名标识曲线，无法执行按井插值。",
                              context=ErrorContext(operation="get_curve_values_at_depths", dataset_name=self.name))
        well_col = self.curve_metadata.get_curve(well_id_curves[0]).dataframe_column_name

        depth_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
        if not depth_curves:
            raise WpDataError("Bundle中缺少单一深度(SINGLE)曲线，无法执行插值。",
                              context=ErrorContext(operation="get_curve_values_at_depths", dataset_name=self.name))
        depth_col = self.curve_metadata.get_curve(depth_curves[0]).dataframe_column_name

        target_attrs = self.curve_metadata.get_curve(curve_name)
        if not target_attrs:
            raise WpCurveMetadataError(
                f"曲线 '{curve_name}' 在元数据中不存在。",
                context=ErrorContext(operation="get_curve_values_at_depths", dataset_name=self.name)
            )
        target_col = target_attrs.dataframe_column_name

        # 3. 准备数据
        well_df = self.data[self.data[well_col] == well_name]
        if well_df.empty:
            raise WpDataError(
                f"在Bundle中未找到井 '{well_name}' 的数据。",
                context=ErrorContext(operation="get_curve_values_at_depths", dataset_name=self.name)
            )

        # 提取源深度和值，并去除NaN值以进行插值
        source_data = well_df[[depth_col, target_col]].dropna()
        if source_data.empty or len(source_data) < 2:
            logger.warning(f"井 '{well_name}' 的曲线 '{curve_name}' 有效数据点不足(少于2个)用于插值。", operation="get_curve_values_at_depths", dataset_name=self.name)
            return [np.nan] * len(depths)

        source_depths, source_values = source_data[depth_col].values, source_data[target_col].values

        # 4. 创建插值函数
        interp_func = interp1d(source_depths, source_values, kind=interpolation_method, bounds_error=False, fill_value=np.nan)

        # 5. 执行插值并返回结果
        return interp_func(depths).tolist()

    def get_interval_curve_values_for_wells(
        self, curve_names: list[str]
    ) -> CaseInsensitiveDict[str, list[list[Any]]]:
        """
        获取所有井的区间型曲线值，以轻量化字典形式返回。

        此方法是 `get_interval_curve_values` 的多井版本，一次性提取
        Bundle中所有井的指定区间数据。

        Args:
            curve_names (list[str]): 要提取值的曲线名称列表。

        Returns:
            CaseInsensitiveDict[str, list[list[Any]]]: 一个大小写不敏感的字典，
            键是井名，值是对应井的区间值列表。每个区间值列表的格式与
            `get_interval_curve_values` 方法的返回格式相同。

        Raises:
            WpValidationError: 如果此Bundle不是`interval`类型。
            WpDataError: 如果缺少井名或深度曲线。
            WpCurveMetadataError: 如果指定的曲线名称在元数据中不存在。

        Examples:
            >>> # 假设plt_bundle包含C-1和C-2两口井的PLT数据
            >>> all_plt_data = plt_bundle.get_interval_curve_values_for_wells(['QOZI'])
            >>> # 返回结果示例:
            >>> # {
            >>> #   'C-1': [[2500.0, 2505.0, 150.5], ...],
            >>> #   'C-2': [[3100.0, 3102.0, 89.0], ...]
            >>> # }

        References:
            - 《SCAPE_MS_方法说明书.md》§4.5 - PLT盲井检验
        """
        # 1. 验证是否为Interval类型
        if not self.is_interval_bundle:
            raise WpValidationError(
                "此方法仅适用于Interval类型的Bundle。",
                context=ErrorContext(
                    operation="get_interval_curve_values_for_wells",
                    dataset_name=self.name,
                    reason="bundle_not_interval_type",
                ),
            )

        # 2. 获取所有井名
        well_names = self.get_well_names()
        if well_names is None or well_names.empty:
            return CaseInsensitiveDict()

        # 3. 循环调用单井方法并组装结果
        all_wells_data = CaseInsensitiveDict()
        for well_name in well_names.unique():
            all_wells_data[well_name] = self.get_interval_curve_values(well_name, curve_names)
        return all_wells_data

    def get_curve_values_with_zone(
        self, well_name: str, top_depth: float, bottom_depth: float, curve_name: str
    ) -> list[Any]:
        """
        为指定井提取某条曲线在给定深度层段内的所有数据点。

        此方法专门用于处理非`interval`类型的Bundle（如连续型或离散型），
        是连接PLT分层信息和连续预测曲线的关键桥梁。

        Args:
            well_name (str): 要查询的井名。
            top_depth (float): 层段的顶界深度。
            bottom_depth (float): 层段的底界深度。
            curve_name (str): 要提取值的曲线名称。

        Returns:
            list[Any]: 一个包含该层段内所有曲线值的列表。

        Raises:
            WpValidationError: 如果此Bundle是`interval`类型。
            WpDataError: 如果缺少井名或深度曲线，或指定的井/曲线不存在。

        Examples:
            >>> # 假设k_pred_bundle是一个包含模型预测渗透率的连续型bundle
            >>> # 曲线包括 'k_pred'
            >>> try:
            ...     k_values_in_zone = k_pred_bundle.get_curve_values_with_zone(
            ...         well_name='C-1',
            ...         top_depth=2500.0,
            ...         bottom_depth=2505.0,
            ...         curve_name='k_pred'
            ...     )
            ...     # 返回结果示例: [120.5, 122.1, 123.0, ...]
            ... except (WpValidationError, WpDataError) as e:
            ...     print(f"提取失败: {e}")

        References:
            - 《SCAPE_MS_方法说明书.md》§4.5.1 - 计算层段预测强度
        """
        # 1. 验证是否为非Interval类型
        if self.is_interval_bundle:
            raise WpValidationError(
                "此方法仅适用于非Interval类型的Bundle（如Continuous或Point）。",
                context=ErrorContext(
                    operation="get_curve_values_with_zone",
                    dataset_name=self.name,
                    reason="bundle_is_interval_type",
                ),
            )

        # 2. 验证深度范围
        if top_depth >= bottom_depth:
            raise WpValidationError(f"顶深({top_depth})必须小于底深({bottom_depth})。")

        # 3. 获取井名和深度列名
        well_id_curves = self.curve_metadata.get_well_identifier_curves()
        if not well_id_curves:
            raise WpDataError("Bundle中缺少井名标识曲线。", context=ErrorContext(operation="get_curve_values_with_zone", dataset_name=self.name))
        well_col = self.curve_metadata.get_curve(well_id_curves[0]).dataframe_column_name

        depth_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
        if not depth_curves:
            raise WpDataError("Bundle中缺少单一深度(SINGLE)曲线。", context=ErrorContext(operation="get_curve_values_with_zone", dataset_name=self.name))
        depth_col = self.curve_metadata.get_curve(depth_curves[0]).dataframe_column_name

        # 4. 获取请求的数据曲线列名
        curve_attrs = self.curve_metadata.get_curve(curve_name)
        if not curve_attrs:
            raise WpCurveMetadataError(f"曲线 '{curve_name}' 在元数据中不存在。")

        # 此方法主要针对一维曲线，如果是二维，则提取其所有元素列
        if curve_attrs.is_1d_curve():
            data_cols = [curve_attrs.dataframe_column_name]
        else: # is_2d_composite_curve
            # 对于二维曲线，此方法可能不适用，但为保持健壮性，我们返回第一个元素的值
            logger.warning(
                f"曲线 '{curve_name}' 是二维曲线，此方法将只返回其第一个元素的值。",
                operation="get_curve_values_with_zone",
                bundle_name=self.name
            )
            data_cols = [curve_attrs.dataframe_element_names[0]]

        # 5. 筛选指定井的数据
        if well_name not in self.data[well_col].unique():
            raise WpDataError(
                f"井 '{well_name}' 在Bundle中不存在。",
                context=ErrorContext(
                    operation="get_curve_values_with_zone",
                    dataset_name=self.name,
                    additional_info={"available_wells": self.data[well_col].unique().tolist()}
                )
            )
        well_df = self.data[self.data[well_col] == well_name]

        # 6. 验证所有必需的列是否存在
        all_required_cols = [depth_col] + data_cols
        missing_cols = [col for col in all_required_cols if col not in well_df.columns]
        if missing_cols:
            raise WpDataError(f"DataFrame中缺少必要的列: {missing_cols}", context=ErrorContext(operation="get_curve_values_with_zone", dataset_name=self.name))

        # 7. 按深度范围筛选并提取数据
        zone_mask = (well_df[depth_col] >= top_depth) & (well_df[depth_col] <= bottom_depth)
        zone_df = well_df.loc[zone_mask]

        if zone_df.empty:
            return []

        # 返回第一个数据列的值列表
        return zone_df[data_cols[0]].tolist()

    def get_curve_statistic_with_zone(
        self,
        well_name: str,
        top_depth: float,
        bottom_depth: float,
        curve_name: str,
        statistic: str,
    ) -> float:
        """
        为指定井计算某条曲线在给定深度层段内的统计值。

        此方法是对 `get_curve_values_with_zone` 的进一步封装，直接计算
        常用统计量，避免了先提取数据再计算的开销。

        Args:
            well_name (str): 要查询的井名。
            top_depth (float): 层段的顶界深度。
            bottom_depth (float): 层段的底界深度。
            curve_name (str): 要计算的曲线名称。
            statistic (str): 要计算的统计量。支持的值与
                `WpDepthIndexedDatasetBase.get_curve_statistic` 类似，
                例如: 'mean', 'median', 'std', 'min', 'max', 'p25', 'p75'。

        Returns:
            float: 计算出的统计值。

        Raises:
            WpValidationError: 如果此Bundle是`interval`类型或参数无效。
            WpDataError: 如果缺少井名/深度曲线，或指定的井/曲线不存在，
                         或在指定层段内没有有效数据。
            ValueError: 如果指定的统计量不受支持。

        Examples:
            >>> # 计算 'k_pred' 在某个层段的平均值
            >>> try:
            ...     mean_k = k_pred_bundle.get_curve_statistic_with_zone(
            ...         well_name='C-1',
            ...         top_depth=2500.0,
            ...         bottom_depth=2505.0,
            ...         curve_name='k_pred',
            ...         statistic='mean'
            ...     )
            ...     # 返回结果示例: 125.3
            ... except (WpValidationError, WpDataError, ValueError) as e:
            ...     print(f"计算失败: {e}")

        References:
            - 《SCAPE_MS_方法说明书.md》§4.5.1 - 计算层段代表性渗透率
        """
        # 1. 验证是否为非Interval类型
        if self.is_interval_bundle:
            raise WpValidationError(
                "此方法仅适用于非Interval类型的Bundle（如Continuous或Point）。",
                context=ErrorContext(
                    operation="get_curve_statistic_with_zone",
                    dataset_name=self.name,
                    reason="bundle_is_interval_type",
                ),
            )

        # 2. 验证深度范围
        if top_depth >= bottom_depth:
            raise WpValidationError(f"顶深({top_depth})必须小于底深({bottom_depth})。")

        # 3. 验证统计量名称
        supported_statistics = {'mean', 'median', 'std', 'min', 'max', 'p25', 'p50', 'p75', 'p90'}
        if statistic not in supported_statistics:
            raise ValueError(f"不支持的统计量: '{statistic}'. 支持的包括: {supported_statistics}")

        # 4. 获取层段内的曲线值 (复用已有方法)
        zone_values = self.get_curve_values_with_zone(well_name, top_depth, bottom_depth, curve_name)

        if not zone_values:
            raise WpDataError(
                f"在井 '{well_name}' 的深度范围 [{top_depth}, {bottom_depth}] 内，"
                f"曲线 '{curve_name}' 没有找到任何数据点。",
                context=ErrorContext(operation="get_curve_statistic_with_zone", dataset_name=self.name)
            )

        # 5. 转换为Series并计算统计值
        series = pd.Series(zone_values)

        # 检查是否为数值型
        if not pd.api.types.is_numeric_dtype(series):
            raise WpDataError(f"曲线 '{curve_name}' 不是数值类型，无法计算统计值。")

        series_numeric = series.dropna()
        if series_numeric.empty:
            raise WpDataError(
                f"在井 '{well_name}' 的深度范围 [{top_depth}, {bottom_depth}] 内，"
                f"曲线 '{curve_name}' 没有有效的数值数据用于计算统计值。",
                context=ErrorContext(operation="get_curve_statistic_with_zone", dataset_name=self.name)
            )

        # 6. 计算并返回统计值
        if statistic == 'mean':
            return float(series_numeric.mean())
        elif statistic == 'median' or statistic == 'p50':
            return float(series_numeric.median())
        elif statistic == 'std':
            return float(series_numeric.std())
        elif statistic == 'min':
            return float(series_numeric.min())
        elif statistic == 'max':
            return float(series_numeric.max())
        elif statistic.startswith('p'):
            try:
                quantile = float(statistic[1:]) / 100.0
                return float(series_numeric.quantile(quantile))
            except (ValueError, IndexError):
                raise ValueError(f"无效的分位数格式: '{statistic}'")

    def add_1d_curve(
        self,
        curve_name: str,
        curve_data: np.ndarray,
        *,
        unit: str = "",
        data_type: WpDataType = WpDataType.FLOAT,
        description: str = "",
        category: WpCurveCategory = WpCurveCategory.COMPUTED,
        overwrite: bool = False,
    ) -> None:
        """
        动态地将一条新的一维曲线添加到Bundle中。

        此方法会同步更新DataFrame数据、曲线元数据和列映射，确保Bundle的
        内部一致性。

        Args:
            curve_name: 新曲线的逻辑名称。
            curve_data: 包含曲线数据的NumPy数组。
            unit: 曲线单位。
            description: 曲线描述。
            category: 曲线类别，默认为 COMPUTED。
            overwrite: 如果曲线已存在，是否覆盖。默认为False。

        Raises:
            WpDataError: 如果曲线数据长度与Bundle不匹配，或曲线已存在且overwrite=False。
        """
        # 1. 验证输入
        if len(curve_data) != len(self.data):
            raise WpDataError(
                f"无法添加曲线 '{curve_name}'：数据长度 ({len(curve_data)}) "
                f"与Bundle的DataFrame长度 ({len(self.data)}) 不匹配。",
                context=ErrorContext(operation="add_1d_curve", dataset_name=self.name),
            )

        if self.curve_metadata.has_curve(curve_name) and not overwrite:
            raise WpDataError(
                f"无法添加曲线 '{curve_name}'：该曲线已存在。请使用 overwrite=True 进行覆盖。",
                context=ErrorContext(operation="add_1d_curve", dataset_name=self.name),
            )

        # 2. 如果覆盖，先移除旧的曲线数据和元数据
        if self.curve_metadata.has_curve(curve_name):
            # 从映射中移除，并获取旧的列名
            old_columns = self.curve_to_columns_map.pop(curve_name, [])
            # 从DataFrame中删除旧列
            self.data.drop(columns=old_columns, inplace=True, errors='ignore')
            # 从元数据中移除旧的曲线属性
            self.curve_metadata.remove_curve(curve_name)

        # 3. 创建新的元数据属性对象
        new_curve_attrs = CurveBasicAttributes.create_1d_curve(
            name=curve_name, unit=unit, data_type=data_type, description=description, category=category
        )

        # 4. 从元数据对象获取DataFrame列名
        # 对于一维曲线，其DataFrame列名由其自身属性决定
        column_name = new_curve_attrs.dataframe_column_name

        # 5. 更新DataFrame
        self.data[column_name] = curve_data

        # 6. 更新元数据集合
        self.curve_metadata.add_curve(new_curve_attrs)

        # 7. 更新列映射
        self.curve_to_columns_map[curve_name] = [column_name]

        logger.info(
            "成功添加1D曲线到Bundle",
            operation="add_1d_curve",
            bundle_name=self.name,
            curve_name=curve_name,
            curve_shape=curve_data.shape,
        )

    def replace_curve(
        self,
        curve_name: str,
        new_data: np.ndarray,
        new_data_type: WpDataType,
        *,
        new_unit: Optional[str] = None,
        new_description: Optional[str] = None,
    ) -> None:
        """
        使用新的数据和类型安全地替换一条现有的一维曲线。

        此方法会同步更新DataFrame数据、曲线元数据和内部列映射，
        确保Bundle的内部状态保持一致。

        Args:
            curve_name: 要替换的曲线的逻辑名称。
            new_data: 包含新曲线数据的NumPy数组。
            new_data_type: 新曲线的元数据类型 (WpDataType)。
            new_unit: (可选) 新的曲线单位。如果为None，则保留旧单位。
            new_description: (可选) 新的曲线描述。如果为None，则保留旧描述。

        Raises:
            WpCurveMetadataError: 如果指定的曲线在元数据中不存在。
            WpDataError: 如果新数据长度与Bundle不匹配，或尝试替换非一维曲线。
        """
        # 1. 验证输入
        if not self.curve_metadata.has_curve(curve_name):
            raise WpCurveMetadataError(
                f"无法替换曲线 '{curve_name}'：该曲线在元数据中不存在。",
                context=ErrorContext(operation="replace_curve", dataset_name=self.name)
            )

        if len(new_data) != len(self.data):
            raise WpDataError(
                f"无法替换曲线 '{curve_name}'：新数据长度 ({len(new_data)}) "
                f"与Bundle的DataFrame长度 ({len(self.data)}) 不匹配。",
                context=ErrorContext(operation="replace_curve", dataset_name=self.name),
            )

        old_attrs = self.curve_metadata.get_curve(curve_name)
        if not old_attrs.is_1d_curve():
            raise WpDataError(
                f"无法替换曲线 '{curve_name}'：此方法目前仅支持替换一维曲线。",
                context=ErrorContext(operation="replace_curve", dataset_name=self.name)
            )

        # 2. 移除旧的曲线数据和元数据
        old_columns = self.curve_to_columns_map.pop(curve_name, [])
        self.data.drop(columns=old_columns, inplace=True, errors='ignore')
        self.curve_metadata.remove_curve(curve_name)

        # 3. 创建新的元数据属性对象，保留旧属性
        new_curve_attrs = CurveBasicAttributes.create_1d_curve(
            name=curve_name,
            unit=new_unit if new_unit is not None else old_attrs.unit,
            description=new_description if new_description is not None else old_attrs.description,
            data_type=new_data_type,
            category=old_attrs.category,
            curve_class=old_attrs.curve_class,
            is_well_identifier=old_attrs.is_well_identifier,
            depth_role=old_attrs.depth_role,
        )

        # 4. 更新DataFrame、元数据和映射
        column_name = new_curve_attrs.dataframe_column_name
        self.data[column_name] = new_data
        self.curve_metadata.add_curve(new_curve_attrs)
        self.curve_to_columns_map[curve_name] = [column_name]

        logger.info(
            "成功替换Bundle中的曲线",
            operation="replace_curve",
            bundle_name=self.name,
            curve_name=curve_name,
            new_data_type=new_data_type.value,
            new_shape=new_data.shape,
        )

    def validate_required_curves(
        self, required_curves: list[str] | dict[str, WpDataType]
    ) -> bool:
        """
        全面验证Bundle中是否存在所有必需的曲线，并同时检查元数据和物理数据类型。

        此方法支持两种模式：
        1. 列表模式: `required_curves` 是一个字符串列表，验证所有这些曲线都存在，
           且其元数据类型和物理数据类型均为数值型。
        2. 字典模式: `required_curves` 是一个字典 `{'curve_name': WpDataType}`，
           验证每个曲线都存在且其元数据和物理数据类型都与指定类型匹配。

        Args:
            required_curves: 必需的曲线列表或曲线到期望类型的字典。

        Returns:
            bool: 如果所有必需曲线都存在且满足条件，则返回True。

        Raises:
            WpValidationError: 如果有任何曲线缺失或不满足类型要求。
        """
        missing_curves = []
        type_mismatch_curves = []

        if isinstance(required_curves, dict):
            curves_to_check = required_curves.keys()
        else:
            curves_to_check = required_curves

        for curve_name in curves_to_check:
            # 步骤1: 检查元数据中是否存在曲线
            curve_attrs = self.curve_metadata.get_curve(curve_name)
            if not curve_attrs:
                missing_curves.append(curve_name)
                continue

            # 步骤2: 检查DataFrame中是否存在对应的物理列
            columns = self.curve_to_columns_map.get(curve_name)
            if not columns or not all(col in self.data.columns for col in columns):
                missing_curves.append(f"{curve_name} (数据列缺失)")
                continue

            # 步骤3: 根据模式检查元数据类型和物理数据类型
            if isinstance(required_curves, dict):
                # --- 字典模式：精确匹配 ---
                expected_type = required_curves[curve_name]
                # 3a. 检查元数据类型
                if curve_attrs.data_type != expected_type:
                    type_mismatch_curves.append(
                        f"'{curve_name}' (元数据类型不匹配: 期望 {expected_type.value}, "
                        f"实际 {curve_attrs.data_type.value})"
                    )
                    continue

                # 3b. 检查物理数据类型
                if expected_type in [WpDataType.FLOAT, WpDataType.INT]:
                    for col in columns:
                        if not pd.api.types.is_numeric_dtype(self.data[col]):
                            type_mismatch_curves.append(
                                f"'{curve_name}' (物理类型不匹配: 列 '{col}' 不是数值型, "
                                f"实际为 {self.data[col].dtype})"
                            )
                            break  # 一个列不匹配就够了
            else:
                # --- 列表模式：检查是否为数值型 ---
                # 3a. 检查元数据类型
                if not curve_attrs.is_numeric():
                    type_mismatch_curves.append(
                        f"'{curve_name}' (元数据类型不匹配: 期望为数值型, "
                        f"实际 {curve_attrs.data_type.value})"
                    )
                    continue

                # 3b. 检查物理数据类型
                for col in columns:
                    if not pd.api.types.is_numeric_dtype(self.data[col]):
                        type_mismatch_curves.append(
                            f"'{curve_name}' (物理类型不匹配: 列 '{col}' 不是数值型, "
                            f"实际为 {self.data[col].dtype})"
                        )
                        break  # 一个列不匹配就够了

        if missing_curves or type_mismatch_curves:
            error_message = "必需曲线验证失败。"
            details = {}
            if missing_curves:
                details["missing_curves"] = missing_curves
                error_message += f" 缺失的曲线: {missing_curves}."
            if type_mismatch_curves:
                details["type_mismatch_curves"] = type_mismatch_curves
                error_message += f" 类型不匹配的曲线: {type_mismatch_curves}."

            raise WpValidationError(
                error_message,
                context=ErrorContext(
                    operation="validate_required_curves",
                    dataset_name=str(self.name),
                    additional_info=details,
                ),
            )

        logger.info(
            "必需曲线验证通过",
            operation="validate_required_curves",
            bundle_name=self.name,
            checked_curves_count=len(curves_to_check),
        )
        return True

    def check_uniform_depth_sampling(
        self,
        *,
        algorithm: str = "mad",
        tolerance: float = 1e-6
    ) -> tuple[bool, float | None]:
        """检查深度采样是否为等间隔。

        与WpDepthIndexedDatasetBase中的方法类似，此方法检查Bundle中的深度数据
        是否为等间隔采样。

        Args:
            algorithm: 检查算法，支持"std_dev"（标准差）和"mad"（中位数绝对偏差），默认为"mad"
            tolerance: 容差阈值，用于判断是否为等间隔，默认1e-6

        Returns:
            tuple[bool, float | None]: (是否等间隔, 采样间隔)
                - 如果是等间隔，返回(True, 采样间隔值)
                - 如果不是等间隔，返回(False, None)

        Raises:
            WpDataError: 当数据集不支持单一深度参考或检查失败时抛出
            ValueError: 参数无效
        """
        # 1. 检查是否支持单一深度参考
        if self.is_interval_bundle:
            raise WpDataError(
                "深度采样间隔检查仅适用于有单一深度参考曲线的数据集",
                context=ErrorContext(
                    operation="check_uniform_depth_sampling",
                    dataset_name=str(self.name),
                    additional_info={
                        "is_interval_bundle": self.is_interval_bundle,
                        "requirement": "single_depth_reference_only"
                    }
                )
            )

        # 2. 获取深度参考曲线
        if not self.depth_curve_map:
             raise WpDataError(
                "Bundle中缺少深度参考曲线",
                context=ErrorContext(operation="check_uniform_depth_sampling", dataset_name=str(self.name))
            )

        depth_curve_name = next(iter(self.depth_curve_map))
        depth_curve_attrs = self.curve_metadata.get_curve(depth_curve_name)
        if not depth_curve_attrs:
             raise WpDataError(f"在元数据中找不到深度曲线 '{depth_curve_name}'", context=ErrorContext(operation="check_uniform_depth_sampling", dataset_name=str(self.name)))

        depth_column_name = depth_curve_attrs.dataframe_column_name

        # 3. 获取深度数据列
        if depth_column_name not in self.data.columns:
            raise WpDataError(f"深度列 '{depth_column_name}' 在DataFrame中不存在", context=ErrorContext(operation="check_uniform_depth_sampling", dataset_name=str(self.name), additional_info={"depth_column_name": depth_column_name, "available_columns": list(self.data.columns)}))

        depth_column = self.data[depth_column_name]

        # 4. 转发到服务层
        from logwp.models.datasets.internal.depth_sampling_check import check_uniform_depth_sampling, DepthSamplingAlgorithm

        try:
            algo_enum = DepthSamplingAlgorithm(algorithm)
        except ValueError:
            raise ValueError(f"不支持的算法: '{algorithm}'。支持的算法: 'std_dev', 'mad'") from None

        return check_uniform_depth_sampling(
            depth_column=depth_column,
            depth_curve=depth_curve_attrs,
            algorithm=algo_enum,
            tolerance=tolerance
        )

    def check_na(
        self,
        curve_names: Optional[list[str]] = None,
        *,
        check_how: str = "any"
    ) -> pd.Series:
        """
        检查指定曲线中是否存在NaN（空值）。

        此方法返回一个布尔型的Series，其索引与Bundle的DataFrame一致，
        标记出满足NaN条件的行。

        Args:
            curve_names (Optional[list[str]], optional): 要检查的曲线名称列表。
                如果为None或空列表，则检查所有数据曲线（不包括井名、深度等系统曲线）。
                默认为None。
            check_how (str, optional): 检查NaN的策略。默认为 "any"。
                - "any": 如果某行在指定曲线列表中任意一条曲线上包含NaN，则该行标记为True。
                - "all": 只有当某行在指定曲线列表中的所有曲线上都为NaN时，该行才标记为True。

        Returns:
            pd.Series: 一个布尔型的Series，True表示该行满足NaN检查条件。

        Raises:
            ValueError: 如果 `check_how` 参数不是 "any" 或 "all"。
            WpCurveMetadataError: 如果 `curve_names` 中包含不存在的曲线。

        Examples:
            >>> # 检查GR或PHIT中任意一个是否存在NaN
            >>> nan_mask_any = bundle.check_na(curve_names=['GR', 'PHIT'], check_how='any')
            >>> rows_with_any_nan = bundle.data[nan_mask_any]

            >>> # 检查所有数据曲线中是否存在NaN
            >>> nan_mask_all_curves = bundle.check_na()
        """
        # 1. 验证参数
        if check_how not in ("any", "all"):
            raise ValueError(f"参数 'check_how' 必须是 'any' 或 'all'，但收到了 '{check_how}'。")

        if self.data.empty:
            return pd.Series(dtype=bool)

        # 2. 确定要检查的曲线
        curves_to_check = curve_names or self.curve_metadata.get_data_curves()

        if not curves_to_check:
            return pd.Series([False] * len(self.data), index=self.data.index)

        # 3. 获取DataFrame列名并执行检查
        from logwp.models.curve import CurveExpansionMode
        df_columns = self.curve_metadata.expand_curve_names(curves_to_check, CurveExpansionMode.DATAFRAME)

        existing_columns = [col for col in df_columns if col in self.data.columns]
        if not existing_columns:
            return pd.Series([False] * len(self.data), index=self.data.index)

        if check_how == "any":
            return self.data[existing_columns].isna().any(axis=1)
        else:  # check_how == "all"
            return self.data[existing_columns].isna().all(axis=1)

    def get_curve_statistic(self, curve_name: str, statistic: str) -> float:
        """
        计算指定一维曲线的统计值。

        此方法提供了一个快速获取曲线描述性统计量的接口，例如均值、中位数、
        标准差或特定分位数。

        Args:
            curve_name (str): 要计算统计值的曲线名称。
            statistic (str): 要计算的统计量的名称。支持的值包括：
                'mean', 'median', 'std', 'min', 'max', 'p25', 'p50', 'p75', 'p90' 等。

        Returns:
            float: 计算出的统计值。

        Raises:
            WpCurveMetadataError: 如果曲线不存在或不是一维曲线。
            WpValidationError: 如果统计量名称无效或不受支持。
            WpDataError: 如果曲线不是数值类型或没有有效的数值数据。

        Examples:
            >>> median_gr = bundle.get_curve_statistic('GR', 'median')
            >>> p90_phit = bundle.get_curve_statistic('PHIT', 'p90')
        """
        # 1. 验证曲线存在性并获取属性
        curve_attrs = self.curve_metadata.get_curve(curve_name)
        if not curve_attrs:
            raise WpCurveMetadataError(
                f"曲线 '{curve_name}' 在元数据中不存在。",
                context=ErrorContext(operation="get_curve_statistic", dataset_name=self.name)
            )

        # 确保这是一维曲线
        if not curve_attrs.is_1d_curve():
            raise WpCurveMetadataError(
                f"曲线 '{curve_name}' 不是一维曲线，无法计算统计值。",
                context=ErrorContext(operation="get_curve_statistic", dataset_name=self.name)
            )

        # 2. 获取数据列
        column_name = self.curve_to_columns_map.get(curve_name, [None])[0]
        if column_name is None or column_name not in self.data.columns:
             raise WpDataError(
                f"在DataFrame中找不到曲线 '{curve_name}' 对应的列 '{column_name}'。",
                context=ErrorContext(operation="get_curve_statistic", dataset_name=self.name)
            )
        series = self.data[column_name]

        # 3. 调用核心服务函数进行计算
        return calculate_series_statistic(
            series=series,
            statistic=statistic,
            curve_name=curve_name,
            dataset_name=self.name
        )

    def to_structured_ml_inputs(
        self,
        input_map: dict[str, list[str]],
        target_features: list[str]
    ) -> tuple[dict[str, np.ndarray], np.ndarray]:
        """
        将Bundle数据转换为结构化的、用于多输入ML模型的Numpy数组。

        此方法为机器学习工作流（如OBMIQ）提供了一个便捷的数据准备接口。
        它能自动处理一维和二维（序列）特征，并将它们打包成一个结构化的
        字典，可以直接作为TensorFlow/Keras等多输入模型的输入。

        Args:
            input_map: 一个字典，键是ML模型的输入名称(如 'sequence_input'),
                       值是对应输入的曲线名列表。
            target_features: 目标特征的曲线名列表。

        Returns:
            一个元组 (X, y):
            - X: 一个字典，键是输入名，值是对应的特征Numpy数组。
            - y: 目标的Numpy数组。

        Raises:
            WpCurveMetadataError: 如果指定的曲线在元数据或列映射中不存在。
            WpDataError: 如果元数据中定义的列在DataFrame中不存在。

        Examples:
            >>> X_structured, y_true = train_bundle.to_structured_ml_inputs(
            ...     input_map={'sequence': ['T2_DIST'], 'tabular': ['GR', 'DEN']},
            ...     target_features=['PERM']
            ... )
            >>> # X_structured['sequence'] 是序列数据数组
            >>> # X_structured['tabular'] 是表格数据数组
        """
        return convert_bundle_to_structured_ml_inputs(
            bundle=self, input_map=input_map, target_features=target_features
        )


@dataclass
class WpArrayBundle(WpDatasetBundle):
    """NumPy数组数据包封装。

    封装numpy数组数据，提供便捷的元数据访问接口和机器学习辅助方法。
    适用于算法计算和机器学习场景。

    Attributes:
        data: 曲线名到numpy数组的映射（大小写不敏感）

    Examples:
        >>> # 创建Array Bundle
        >>> bundle = WpArrayBundle(
        ...     name="test_arrays",
        ...     curve_metadata=metadata,
        ...     data=CaseInsensitiveDict({"GR": gr_array, "PHIT": phit_array})
        ... )
        >>>
        >>> # 访问数组数据
        >>> arrays = bundle.data
        >>> gr_values = arrays["GR"]  # np.ndarray
        >>>
        >>> # 大小写不敏感访问
        >>> gr_values = arrays["gr"]  # 同样有效
        >>>
        >>> # 获取井名数据
        >>> well_names = bundle.get_well_names()  # np.ndarray
        >>>
        >>> # 机器学习辅助方法
        >>> X, y = bundle.to_sklearn_format(target_curve="PERM")
    """

    # 必需字段（无默认值）
    data: CaseInsensitiveDict = field()

    def _get_curve_data(self, curve_name: str) -> Optional[np.ndarray]:
        """获取指定曲线的数组数据。

        Args:
            curve_name: 曲线名称

        Returns:
            Optional[np.ndarray]: 曲线数据数组
        """
        # 直接从data字典获取（CaseInsensitiveDict支持大小写不敏感）
        return self.data.get(curve_name)

    def to_sklearn_format(
        self,
        target_curve: Optional[str] = None,
        exclude_system_curves: bool = True
    ) -> tuple[np.ndarray, Optional[np.ndarray]]:
        """转换为scikit-learn格式 (X, y)。

        Args:
            target_curve: 目标曲线名称（y），如果为None则只返回特征矩阵
            exclude_system_curves: 是否排除系统曲线（井名、深度），默认True

        Returns:
            tuple[np.ndarray, Optional[np.ndarray]]: (特征矩阵X, 目标向量y)
            - X: 形状为(n_samples, n_features)的特征矩阵
            - y: 形状为(n_samples,)的目标向量，如果target_curve为None则为None

        Examples:
            >>> # 获取特征矩阵（排除系统曲线）
            >>> X, _ = bundle.to_sklearn_format()
            >>> assert X.shape[1] == len(bundle.get_feature_curves())

            >>> # 获取特征和目标
            >>> X, y = bundle.to_sklearn_format(target_curve="PERM")
            >>> assert X.shape[0] == y.shape[0]  # 样本数一致
        """
        # 获取特征曲线
        feature_curves = self.get_feature_curves(exclude_system_curves=exclude_system_curves)
        if target_curve and target_curve in feature_curves:
            feature_curves.remove(target_curve)

        # 构建特征矩阵
        feature_arrays = []
        for curve_name in feature_curves:
            curve_data = self.data.get(curve_name)
            if curve_data is not None:
                if curve_data.ndim == 1:
                    feature_arrays.append(curve_data.reshape(-1, 1))
                else:
                    feature_arrays.append(curve_data)

        if not feature_arrays:
            # 没有特征数据
            n_samples = len(next(iter(self.data.values()))) if self.data else 0
            X = np.empty((n_samples, 0))
        else:
            X = np.hstack(feature_arrays)

        # 获取目标向量
        y = None
        if target_curve:
            target_data = self.data.get(target_curve)
            if target_data is not None:
                y = target_data.flatten() if target_data.ndim > 1 else target_data

        logger.debug(
            "转换为sklearn格式完成",
            operation="to_sklearn_format",
            feature_shape=X.shape,
            target_shape=y.shape if y is not None else None,
            feature_curves=feature_curves,
            target_curve=target_curve
        )

        return X, y

    def get_feature_curves(self, exclude_system_curves: bool = True) -> list[str]:
        """获取特征曲线名称列表。

        使用curve_metadata API正确识别系统曲线，避免硬编码假设。

        Args:
            exclude_system_curves: 是否排除系统曲线（井名、深度），默认True

        Returns:
            list[str]: 特征曲线名称列表
        """
        all_curves = list(self.data.keys())

        if not exclude_system_curves:
            return all_curves

        # 使用API正确识别系统曲线
        system_curves = set()

        # 添加井名曲线
        well_curves = self.curve_metadata.get_well_identifier_curves()
        system_curves.update(well_curves)

        # 添加深度曲线（正确处理单深度和区间深度）
        if self.is_interval_bundle:
            # 区间数据集：添加顶深和底深曲线
            top_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.TOP)
            bottom_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.BOTTOM)
            system_curves.update(top_curves)
            system_curves.update(bottom_curves)
        else:
            # 单深度数据集：添加单深度曲线
            single_curves = self.curve_metadata.get_curves_by_depth_role(WpDepthRole.SINGLE)
            system_curves.update(single_curves)

        # 排除系统曲线，返回特征曲线
        feature_curves = [curve for curve in all_curves if curve not in system_curves]

        logger.debug(
            "获取特征曲线列表",
            operation="get_feature_curves",
            total_curves=len(all_curves),
            system_curves_count=len(system_curves),
            feature_curves_count=len(feature_curves),
            exclude_system=exclude_system_curves
        )

        return feature_curves

    def get_data_summary(self) -> Dict[str, Dict[str, Any]]:
        """获取数据摘要统计。

        Returns:
            Dict[str, Dict[str, Any]]: 每个曲线的统计信息

        Examples:
            >>> summary = bundle.get_data_summary()
            >>> print(summary["GR"])  # {"shape": (100,), "dtype": "float64", "min": 20.5, "max": 150.2, ...}
        """
        summary = {}

        for curve_name, curve_data in self.data.items():
            curve_summary = {
                "shape": curve_data.shape,
                "dtype": str(curve_data.dtype),
                "size": curve_data.size,
                "memory_mb": curve_data.nbytes / 1024 / 1024
            }

            # 数值统计（仅对数值类型）
            if np.issubdtype(curve_data.dtype, np.number):
                curve_summary.update({
                    "min": float(np.nanmin(curve_data)),
                    "max": float(np.nanmax(curve_data)),
                    "mean": float(np.nanmean(curve_data)),
                    "std": float(np.nanstd(curve_data)),
                    "nan_count": int(np.isnan(curve_data).sum()),
                    "finite_count": int(np.isfinite(curve_data).sum())
                })

            summary[curve_name] = curve_summary

        return summary
