from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, List

import numpy as np
import pandas as pd
from logwp.infra import get_logger
from logwp.models.curve import CurveExpansionMode
from logwp.models.datasets.bundle import WpDataFrameBundle
from sklearn.metrics import mean_squared_error, r2_score
from logwp.extras.plotting import registry
from logwp.extras.tracking import RunContext

from .artifact_handler import ObmiqBaselinesArtifactHandler
from .config import ObmiqBaselinesTrainingConfig
from .constants import ObmiqBaselinesPlotProfiles, ObmiqBaselinesTrainingArtifacts
from .internal import final_training_procedure, nested_cv_procedure
from .plotting import (
    replot_crossplot,
    replot_feature_importance,
    replot_residuals_hist,
    replot_residuals_plot,
)

logger = get_logger()


def _resolve_feature_selectors(
    bundle: WpDataFrameBundle,
    tabular_features: list[str],
    target_features: list[str],
    grouping_feature: str,
) -> dict[str, Any]:
    """将用户提供的逻辑曲线名解析为DataFrame的物理列名。"""
    tabular_cols = bundle.curve_metadata.expand_curve_names(
        tabular_features, mode=CurveExpansionMode.DATAFRAME
    )
    target_cols = bundle.curve_metadata.expand_curve_names(
        target_features, mode=CurveExpansionMode.DATAFRAME
    )
    grouping_col = bundle.curve_metadata.expand_curve_names(
        [grouping_feature], mode=CurveExpansionMode.DATAFRAME
    )[0]

    logger.info("曲线名已成功解析为DataFrame列名。")
    return {
        "tabular_cols": tabular_cols,
        "target_cols": target_cols,
        "grouping_col": grouping_col,
    }


def _validate_training_inputs(
    bundle: WpDataFrameBundle, resolved_selectors: dict[str, Any]
):
    """对训练步骤的输入参数进行基本验证。"""
    if bundle.data.empty:
        raise ValueError("输入数据 'train_bundle.data' 不能为空。")

    all_resolved_cols = (
        set(resolved_selectors["tabular_cols"])
        | set(resolved_selectors["target_cols"])
        | {resolved_selectors["grouping_col"]}
    )
    missing_cols = all_resolved_cols - set(bundle.data.columns)

    if missing_cols:
        raise ValueError(
            "输入数据 'train_bundle.data' 中缺少以下必需的列: "
            f"{', '.join(sorted(list(missing_cols)))}"
        )
    logger.debug("所有训练输入参数验证通过。")


def _save_and_register_artifacts(
    ctx: RunContext,
    step_dir: Path,
    handler: ObmiqBaselinesArtifactHandler,
    model_assets: dict[str, Any],
    nested_cv_report: dict[str, Any],
    lowo_cv_predictions_df: pd.DataFrame,
    target_name: str,
):
    """保存并注册单个目标的所有产物。"""
    # 1. 保存模型资产
    assets_path = step_dir / f"model_assets_{target_name}.joblib"
    handler.save_model_assets(model_assets, assets_path)
    ctx.register_artifact(
        assets_path.relative_to(ctx.run_dir),
        f"{ObmiqBaselinesTrainingArtifacts.MODEL_ASSETS.value}_{target_name}",
        description=f"包含最终融合模型和元数据的资产包 (目标: {target_name})",
    )

    # 2. 创建并保存详细的嵌套CV报告
    # 从嵌套CV结果中提取RMSE和R2分数，并转置使模型名为索引
    rmse_scores_df = pd.DataFrame(nested_cv_report["all_rmse_scores"]).T
    r2_scores_df = pd.DataFrame(nested_cv_report["all_r2_scores"]).T

    # 重命名列以清晰地标识每一折的指标
    rmse_scores_df.columns = [f"fold_{i+1}_rmse" for i in rmse_scores_df.columns]
    r2_scores_df.columns = [f"fold_{i+1}_r2" for i in r2_scores_df.columns]

    # 计算每个模型的平均性能
    rmse_scores_df["avg_rmse"] = rmse_scores_df.mean(axis=1)
    r2_scores_df["avg_r2"] = r2_scores_df.mean(axis=1)

    # 合并成一个最终的报告DataFrame
    report_df = pd.concat([rmse_scores_df, r2_scores_df], axis=1)
    report_df.index.name = "model_name"
    report_path = step_dir / f"nested_cv_report_{target_name}.csv"

    # --- 新增: 计算并附加总体泛化性能指标 ---
    if not lowo_cv_predictions_df.empty:
        y_true_overall = lowo_cv_predictions_df["actual"]
        y_pred_overall = lowo_cv_predictions_df["predicted"]

        overall_rmse = np.sqrt(mean_squared_error(y_true_overall, y_pred_overall))
        overall_r2 = r2_score(y_true_overall, y_pred_overall)

        # 创建一个包含总体指标的Series，其索引与report_df的列对齐
        overall_metrics = pd.Series(
            {"avg_rmse": overall_rmse, "avg_r2": overall_r2}, name="_OVERALL_FUSED_MODEL_"
        )
        # 使用 pd.concat 附加新行
        report_df = pd.concat([report_df, pd.DataFrame([overall_metrics])])

    handler.save_dataframe(report_df, report_path)
    ctx.register_artifact(
        report_path.relative_to(ctx.run_dir),
        f"{ObmiqBaselinesTrainingArtifacts.NESTED_CV_REPORT.value}_{target_name}",
        description=f"嵌套交叉验证中各候选模型的性能报告 (目标: {target_name})",
    )


def _save_and_plot_generalization_artifacts(
    ctx: RunContext,
    step_dir: Path,
    handler: ObmiqBaselinesArtifactHandler,
    predictions_df: pd.DataFrame,
    target_name: str,
):
    """保存并绘制基于LOWO-CV的泛化能力评估产物。"""
    # --- 新增: 计算总体泛化指标并记录到追踪系统 ---
    if not predictions_df.empty:
        overall_rmse = np.sqrt(mean_squared_error(predictions_df["actual"], predictions_df["predicted"]))
        overall_r2 = r2_score(predictions_df["actual"], predictions_df["predicted"])
        ctx.log_metrics(
            {f"lowo_cv_overall_rmse": overall_rmse, f"lowo_cv_overall_r2": overall_r2},
            step_name=f"obmiq_baselines_training.{target_name}",
        )
        logger.info(f"  总体泛化性能 (LOWO-CV): RMSE={overall_rmse:.4f}, R2={overall_r2:.4f}")

    logger.info(f"  正在为目标 '{target_name}' 生成泛化能力评估产物...")

    # 1. 保存LOWO-CV的预测结果数据快照
    snapshot_path = step_dir / f"lowo_cv_predictions_{target_name}.csv"
    handler.save_dataframe(predictions_df, snapshot_path)
    ctx.register_artifact(
        snapshot_path.relative_to(ctx.run_dir),
        f"{ObmiqBaselinesTrainingArtifacts.LOWO_CV_PREDICTIONS_DATA.value}_{target_name}",
        description=f"留一井交叉验证(LOWO-CV)的合并预测结果 (目标: {target_name})",
    )

    # 2. 绘制LOWO-CV的交会图
    plot_path = step_dir / f"lowo_cv_crossplot_{target_name}.png"
    profile = registry.get(ObmiqBaselinesPlotProfiles.LOWO_CV_CROSSPLOT.value).with_updates(
        title_props={"label": f"LOWO-CV Crossplot for {target_name}"},
        label_props={"actual_col": "actual", "predicted_col": "predicted"},
    )
    replot_crossplot(snapshot_path=snapshot_path, profile=profile, output_path=plot_path)
    ctx.register_artifact(
        plot_path.relative_to(ctx.run_dir),
        f"{ObmiqBaselinesTrainingArtifacts.LOWO_CV_CROSSPLOT.value}_{target_name}",
        description=f"基于LOWO-CV结果的泛化能力交会图 (目标: {target_name})",
    )

def _evaluate_and_plot_final_model(
    ctx: RunContext,
    step_dir: Path,
    handler: ObmiqBaselinesArtifactHandler,
    model_assets: dict[str, Any],
    X: pd.DataFrame,
    y: pd.Series,
    target_name: str,
):
    """对最终模型进行评估，并生成所有相关的图表产物。"""
    logger.info(f"  正在为目标 '{target_name}' 评估最终模型并生成图表...")
    ensemble_model = model_assets["ensemble_model"]

    # 1. 在全量数据上进行预测
    y_pred = ensemble_model.predict(X)
    eval_df = pd.DataFrame({"actual": y, "predicted": y_pred})
    eval_df["residual"] = eval_df["actual"] - eval_df["predicted"]

    # 2. 保存评估数据快照
    eval_snapshot_path = step_dir / f"final_model_evaluation_{target_name}.csv"
    handler.save_dataframe(eval_df, eval_snapshot_path)
    ctx.register_artifact(
        eval_snapshot_path.relative_to(ctx.run_dir),
        f"{ObmiqBaselinesTrainingArtifacts.FINAL_MODEL_EVALUATION_DATA.value}_{target_name}",
        description=f"最终模型在全部训练数据上的预测和残差结果 (目标: {target_name})",
    )

    # 3. 绘制评估图表 (交会图, 残差图)
    plot_map = {
        f"{ObmiqBaselinesTrainingArtifacts.EVAL_CROSSPLOT.value}_{target_name}": (
            replot_crossplot,
            {"actual_col": "actual", "predicted_col": "predicted"},
            ObmiqBaselinesPlotProfiles.CROSSPLOT,
        ),
        f"{ObmiqBaselinesTrainingArtifacts.EVAL_RESIDUALS_PLOT.value}_{target_name}": (
            replot_residuals_plot,
            {"predicted_col": "predicted", "residual_col": "residual"},
            ObmiqBaselinesPlotProfiles.RESIDUALS_PLOT,
        ),
        f"{ObmiqBaselinesTrainingArtifacts.EVAL_RESIDUALS_HIST.value}_{target_name}": (
            replot_residuals_hist,
            {"residual_col": "residual"},
            ObmiqBaselinesPlotProfiles.RESIDUALS_HIST,
        ),
    }
    for artifact_name, (plot_func, props, profile_name) in plot_map.items():
        plot_path = step_dir / f"{artifact_name.split('.')[-1]}.png"
        profile = registry.get(profile_name.value).with_updates(
            title_props={"label": f"{artifact_name.split('.')[-1]} for {target_name}"},
            label_props=props,
        )
        plot_func(snapshot_path=eval_snapshot_path, profile=profile, output_path=plot_path)
        ctx.register_artifact(
            plot_path.relative_to(ctx.run_dir),
            artifact_name,
            description=f"最终模型评估图 (目标: {target_name})",
        )

    # 4. 提取并保存特征重要性
    # 注意：特征重要性来自两个子模型，这里我们简单地取平均
    importances = []
    feature_names = model_assets["metadata"]["tabular_features_ordered"]
    for sub_model_pipeline in [ensemble_model.model_1, ensemble_model.model_2]:
        # 特征重要性可能来自XGBoost的 .feature_importances_ 或 RandomForest的 .feature_importances_
        model_step = sub_model_pipeline.named_steps["model"]
        if hasattr(model_step, "feature_importances_"):
            # RFECV可能会改变特征顺序，我们需要从selector获取最终的特征名
            selector_mask = sub_model_pipeline.named_steps["selector"].support_
            selected_features = np.array(feature_names)[selector_mask]

            imp_series = pd.Series(model_step.feature_importances_, index=selected_features)
            importances.append(imp_series)

    if importances:
        # 合并两个模型的重要性（如果都存在）
        fi_df = pd.concat(importances, axis=1).mean(axis=1).reset_index()
        fi_df.columns = ["feature", "importance"]
        fi_df = fi_df.sort_values(by="importance", ascending=False)

        fi_snapshot_path = step_dir / f"feature_importance_{target_name}.csv"
        handler.save_dataframe(fi_df, fi_snapshot_path)
        ctx.register_artifact(
            fi_snapshot_path.relative_to(ctx.run_dir),
            f"{ObmiqBaselinesTrainingArtifacts.FEATURE_IMPORTANCE_DATA.value}_{target_name}",
        )

        fi_plot_path = step_dir / f"feature_importance_{target_name}.png"
        fi_profile = registry.get(ObmiqBaselinesPlotProfiles.FEATURE_IMPORTANCE_PLOT.value).with_updates(
            title_props={"label": f"Feature Importance for {target_name}"}
        )
        replot_feature_importance(snapshot_path=fi_snapshot_path, profile=fi_profile, output_path=fi_plot_path)
        ctx.register_artifact(
            fi_plot_path.relative_to(ctx.run_dir),
            f"{ObmiqBaselinesTrainingArtifacts.FEATURE_IMPORTANCE_PLOT.value}_{target_name}",
        )


def run_obmiq_baselines_training_step(
    config: ObmiqBaselinesTrainingConfig,
    ctx: RunContext,
    train_bundle: WpDataFrameBundle,
    *,
    tabular_features: list[str],
    target_features: list[str],
    grouping_feature: str,
) -> dict[str, Any]:
    """
    执行OBMIQ Baselines模型训练、评估和交付。

    该函数是训练步骤的唯一入口，它编排了内部的两个核心规程：
    1. 嵌套交叉验证规程 (nested_cv_procedure)
    2. 最终模型训练规程 (final_training_procedure)
    """
    logger.info("===== OBMIQ Baselines Training Step Started =====")
    step_dir = ctx.get_step_dir("obmiq_baselines_training")
    handler = ObmiqBaselinesArtifactHandler()

    # 步骤 0: 解析与验证
    resolved_selectors = _resolve_feature_selectors(
        bundle=train_bundle,
        tabular_features=tabular_features,
        target_features=target_features,
        grouping_feature=grouping_feature,
    )
    _validate_training_inputs(bundle=train_bundle, resolved_selectors=resolved_selectors)

    # 步骤 1: 保存配置快照
    config_path = step_dir / "training_config.json"
    handler.save_parameters(config.model_dump(), config_path)
    ctx.register_artifact(
        config_path.relative_to(ctx.run_dir),
        ObmiqBaselinesTrainingArtifacts.TRAINING_CONFIG.value,
        description="本次训练运行的配置快照。",
    )

    # 步骤 2: 为每个目标独立建模
    all_results = {}
    X = train_bundle.data[resolved_selectors["tabular_cols"]]
    groups = train_bundle.data[resolved_selectors["grouping_col"]]

    for target_name in resolved_selectors["target_cols"]:
        logger.info(f"--- 开始为目标 '{target_name}' 进行建模 ---")
        y = train_bundle.data[target_name]

        # 2a. 内部规程一：嵌套交叉验证
        # 假设: nested_cv_procedure.run_nested_cv 现在返回一个包含 (真实值, 预测值) 的DataFrame
        nested_cv_results, lowo_cv_predictions_df = nested_cv_procedure.run_nested_cv(
            X=X, y=y, groups=groups, config=config, target_name=target_name
        )

        # 2b. 生成并保存泛化能力评估产物
        _save_and_plot_generalization_artifacts(
            ctx=ctx,
            step_dir=step_dir,
            handler=handler,
            predictions_df=lowo_cv_predictions_df,
            target_name=target_name,
        )

        # 2c. 内部规程二：最终模型训练
        model_assets = final_training_procedure.train_final_ensemble_model(
            X=X,
            y=y,
            groups=groups,
            nested_cv_results=nested_cv_results,
            config=config,
            target_name=target_name,
        )

        # 2d. 保存并注册该目标的所有产物
        _save_and_register_artifacts(
            ctx=ctx,
            step_dir=step_dir,
            handler=handler,
            model_assets=model_assets,
            nested_cv_report=nested_cv_results,
            lowo_cv_predictions_df=lowo_cv_predictions_df,
            target_name=target_name,
        )

        # 2e. 评估最终模型并生成图表
        _evaluate_and_plot_final_model(
            ctx=ctx,
            step_dir=step_dir,
            handler=handler,
            model_assets=model_assets,
            X=X,
            y=y,
            target_name=target_name,
        )

        all_results[target_name] = {"model_assets": model_assets}
        logger.info(f"--- 目标 '{target_name}' 建模完成 ---")

    logger.info("===== OBMIQ Baselines Training Step Finished =====")
    return {"status": "completed", "results_per_target": all_results}
