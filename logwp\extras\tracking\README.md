# 可追踪机器学习组件开发框架

**版本: 1.0**

## 0. 引言

### 0.1. 目标

本框架旨在为数据科学与机器学习项目提供一套**高度规范、模块化、可维护且可复现**的组件开发标准。它指导开发者如何将一个机器学习任务（如模型训练、数据处理、评估）封装成一个独立的、可追踪的、可重用的组件。

### 0.2. 核心原则

本框架遵循现代软件工程的最佳实践，强调以下核心原则：

*   **关注点分离 (Separation of Concerns)**: 将组件的配置、业务逻辑、数据接口、公共API等不同职责严格分离到不同的模块中。
*   **清晰的接口 (Clear API)**: 每个组件通过一个明确的“门面”（Facade）对外提供服务，隐藏所有内部实现细节。
*   **信息隐藏 (Information Hiding)**: 组件的内部实现可以自由演进，只要其公共接口保持稳定，就不会影响到其他组件或工作流。
*   **约定优于配置 (Convention over Configuration)**: 提供标准的目录结构和命名约定，减少重复性决策，使代码结构高度一致，易于理解和维护。

---

## 1. 核心概念

为了确保沟通的精确性，我们定义以下核心概念：

| 概念 | 标准名称 | 描述 |
| --- | --- | --- |
| **工作流** | `Workflow` | 由一个或多个按序执行的`Step`组成的端到端完整实验流程。 |
| **步骤** | `Step` | `Workflow`中的一个独立、可复现的任务单元。它有明确的输入、处理逻辑和输出产物。 |
| **运行上下文** | `RunContext` | 由`logwp.extras.tracking`提供，是连接`Workflow`和`Step`的桥梁。由`Workflow`创建并传递给`Step`，负责追踪参数、指标和产物。 |
| **内部规程** | `Internal Procedure` | 当一个`Step`内部实现非常复杂时，可将其分解为多个内部子任务流程。此概念仅存于`Step`内部，以避免与全局的`Workflow`混淆。 |
| **产物** | `Artifact` | `Step`执行过程中生成的任何持久化文件，如模型、数据集、报告、图表等。 |
| **配置** | `Configuration` | 控制`Workflow`或`Step`行为的参数集合，必须使用`Pydantic`模型定义，以实现类型安全和验证。 |
| **门面** | `Facade` | `Step`对外暴露的唯一公共接口模块，提供`run_step`函数、配置模型、产物处理器等。 |
| **产物处理器** | `Artifact Handler` | 一个**无状态**的工具类，专门负责特定`Step`产物的序列化/反序列化和读/写操作。 |
| **产物常量** | `Artifact Constants` | 定义`Artifact`逻辑名称的常量，通常使用`Enum`实现，以保证全局唯一性和代码健壮性。 |
| **数据快照** | `Data Snapshot` | 在生成图表类产物时，一同保存的、用于生成该图表的原始输入数据文件（如CSV），以保证绘图逻辑的可复现性。 |

---

## 2. 组件架构与目录结构

所有`Step`都必须遵循标准的包结构，以实现“约定优于配置”。

### 2.1. 单一步骤包 (Single-Step Package)

当一个功能模块只包含一个`Step`时，采用此结构。

```
my_step_package/
├── __init__.py               # 【必须】从各个模块导出公共API，如facade.run_my_step, config.MyStepConfig等
├── facade.py                 # 【必须】Step的公共门面，定义并实现run_my_step函数
├── config.py                 # 【必须】定义MyStepConfig Pydantic模型
├── constants.py              # 【推荐】使用Enum定义产物名称常量
├── artifact_handler.py       # 【推荐】定义无状态的MyStepArtifactHandler
├── plotting.py               # (可选) 定义与数据快照相关的绘图复现功能
├── exceptions.py             # (可选) 定义Step特有的异常
└── internal/                 # 【必须】存放所有内部实现细节的目录
    ├── __init__.py
    ├── algorithm.py          # 核心算法逻辑
    └── data_utils.py         # 内部数据处理工具
```

### 2.2. 多步骤包 (Multi-Step Package)

当多个功能紧密相关（如训练和预测）并能共享大量内部逻辑时，将它们组织在同一个包中。

```
my_multi_step_package/
├── __init__.py                     # 【必须】导出所有公共API
├── training_facade.py              # 【必须】训练Step的门面 (run_training_step)
├── prediction_facade.py            # 【必须】预测Step的门面 (run_prediction_step)
├── config.py                       # 【必须】包含TrainingConfig和PredictionConfig
├── constants.py                    # 【推荐】包含两个Step的所有Artifact常量
├── artifact_handler.py             # 【推荐】包含两个Step的ArtifactHandler
└── internal/                       # 【必须】共享的内部实现
    ├── __init__.py
    ├── shared_model_core.py        # 【共享】核心模型/算法实现
    ├── training_procedure.py       # 训练Step的内部规程
    └── prediction_procedure.py     # 预测Step的内部规程
```

---

## 3. Step 开发规范

### 3.1. 配置 (`config.py`)

**原则：参数的分类与处理**

为了实现真正的关注点分离，必须对传入`Step`的各类参数进行严格分类，并采用不同的处理策略：

| 参数类别 | 定义 | 示例 | 处理方式 |
| :--- | :--- | :--- | :--- |
| **模型/算法参数** | 直接影响计算结果和科学逻辑的参数。 | `learning_rate`, `max_iterations`, `relaxed_wells` | **必须**在Pydantic `Config`模型中定义。 |
| **数据选择器参数** | 指定从输入数据对象中选择哪个数据列或子集的参数。 | `permeability_curve`, `flow_rate_curve` | **必须**作为`facade`函数的直接、关键字参数，与它所描述的数据对象一同传入。 |
| **执行/表现层参数** | 控制运行环境或视觉样式，不影响计算数值。 | `backend` ('cpu'/'gpu'), `plot_profile_name` | **必须**作为`facade`函数的直接、关键字参数传入。 |
| **数据依赖性参数** | 其值在运行时从输入数据中动态计算或提取的参数。 | `t2_p50_ref` (参考值), `t2_time` (T2轴数组) | 见下文“数据依赖性参数注入”原则。 |

**原则：数据依赖性参数注入**

对于依赖于具体输入数据的参数，应由**工作流驱动脚本**在调用步骤函数之前，计算或提取出来，然后根据其性质进行注入：
-   **小型配置值** (如`t2_p50_ref`): 应由工作流脚本**注入**到Pydantic `Config`对象中。
-   **大型数据数组** (如`t2_time`): 应作为`facade`函数的直接参数传入。

这种分离确保了核心算法逻辑的配置是可移植和独立的，而数据选择、执行环境和动态参数的注入则由更高层的工作流灵活决定。

*   **强制使用Pydantic**: 所有`Step`的配置必须定义为一个继承自`pydantic.BaseModel`的类。
*   **提供描述**: 使用`Field`为每个参数提供清晰的`description`。
*   **灵活性**: `Step`的内部实现可以直接使用Pydantic配置对象，或通过`.model_dump()`方法将其转换为字典。

**示例 (`config.py`)**:
```python
from pydantic import BaseModel, Field

class MyStepConfig(BaseModel):
    learning_rate: float = Field(0.01, description="模型的学习率")
    max_iterations: int = Field(100, description="最大迭代次数", gt=0)
```

### 3.2. 产物与常量 (`constants.py`)

*   **明确产出**: 必须在`Step`的文档字符串中明确其生成的所有`Artifact`。
*   **常量化命名**: `Artifact`的逻辑名称必须在`constants.py`中通过`enum.Enum`定义。
*   **分层命名规则**: 为保证全局唯一性，`Artifact`名称采用 **点分层级命名法**:
    `step_name.<category>.<specific_name>`
    *   `step_name`: 步骤的唯一标识符 (例如 `swift_pso_training`)。
    *   `category`: 产物类别 (例如 `models`, `datasets`, `plots`, `reports`, `data_snapshots`)。
    *   `specific_name`: 具体产物名 (例如 `final_model`, `convergence_curve`)。

**示例 (`constants.py`)**:
```python
from enum import Enum

class MyStepArtifacts(str, Enum):
    FINAL_MODEL = "my_step.models.final_model"
    VALIDATION_REPORT = "my_step.reports.validation"
    CONVERGENCE_PLOT = "my_step.plots.convergence"
    CONVERGENCE_PLOT_DATA = "my_step.data_snapshots.convergence" # 绘图数据快照
```

### 3.3. 产物处理器 (`artifact_handler.py`)

*   **无状态**: 处理器类本身不存储任何实例变量。其所有方法都应是静态方法，或在一个无状态的实例上调用。
*   **职责单一**: 只负责特定格式产物的“序列化/反序列化”和“读/写”，不包含任何业务逻辑。
*   **标准签名**: 方法签名应清晰地反映其I/O操作，例如 `load_model(path: Path)` 和 `save_report(data: dict, path: Path)`。
*   **用户可访问**: 用户可以导入并直接使用`Artifact Handler`，以便在`Workflow`之外独立访问产物。

**示例 (`artifact_handler.py`)**:
```python
from pathlib import Path
import json
import joblib

class MyStepArtifactHandler:
    @staticmethod
    def save_model(model: object, path: Path):
        joblib.dump(model, path)

    @staticmethod
    def load_model(path: Path) -> object:
        return joblib.load(path)

    @staticmethod
    def save_report(report_data: dict, path: Path):
        path.write_text(json.dumps(report_data, indent=2))
```

### 3.4. 门面 (`facade.py`)

*   **公共入口**: 定义`Step`的主执行函数，例如 `run_my_step()`。
*   **标准函数签名**: 主函数必须接收`config`对象和`RunContext`实例作为参数。
    `def run_my_step(config: MyStepConfig, ctx: RunContext, ...)`
*   **流程编排**: `facade`模块负责编排对`internal`模块中`Internal Procedures`的调用，是业务逻辑和内部实现的“胶水层”。

**示例 (`facade.py`)**:
```python
from .config import MyStepConfig
from .constants import MyStepArtifacts
from .artifact_handler import MyStepArtifactHandler
from .internal import algorithm
from logwp.extras.tracking import RunContext

def run_my_step(config: MyStepConfig, ctx: RunContext, input_data):
    """My Step 的主执行函数。"""
    # 1. 从上下文中获取此步骤的专用输出目录
    step_dir = ctx.get_step_dir("my_step")

    # 2. 调用内部核心算法
    model, report = algorithm.train_model(config, input_data)

    # 3. 使用Artifact Handler保存产物
    model_path = step_dir / "model.joblib"
    MyStepArtifactHandler.save_model(model, model_path)

    report_path = step_dir / "report.json"
    MyStepArtifactHandler.save_report(report, report_path)

    # 4. 使用`register_artifact`向RunContext注册已保存在运行目录中的产物
    ctx.register_artifact(
        artifact_path=model_path.relative_to(ctx.run_dir),
        artifact_name=MyStepArtifacts.FINAL_MODEL
    )
    ctx.register_artifact(
        artifact_path=report_path.relative_to(ctx.run_dir),
        artifact_name=MyStepArtifacts.VALIDATION_REPORT
    )

    # 5. 返回关键结果供工作流使用
    return {"model_path": model_path, "report": report}
```

---

## 4. 可复现性与可视化

### 4.1. 绘图数据快照

**强制要求**: 为保证图表的可复现性，在生成图表产物（如`.png`）时，必须将其依赖的输入数据也保存为一个独立的、易于读取的**数据快照**产物（如`.csv`或`.parquet`）。

*   **目的**: 将耗时的上游计算与轻量的绘图逻辑解耦。这使得调试图表样式、修改标签或重新生成图表变得极其简单，无需重新运行整个`Workflow`。
*   **命名**: 数据快照的`Artifact`名称应与对应图表的名称相关联，如 `my_step.plots.convergence` (图) 和 `my_step.data_snapshots.convergence` (数据)。

### 4.2. 绘图复现与配置

*   **复现功能位置**: 用于从数据快照重新生成图表的函数，应放置在`Step`包下的一个专用模块中，如`plotting.py`或`visuals.py`。此函数接收数据快照路径和`PlotProfile`作为输入。
*   **统一绘图配置**: 所有绘图**必须**使用`logwp.extras.plotting`包提供的`PlotProfile`系统。这确保了整个项目图表风格的统一性和可维护性。请参考`logwp/extras/plotting/README.md`获取详细信息。

**示例 (`plotting.py`)**:
```python
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
from logwp.extras.plotting import PlotProfile, apply_profile, save_figure

def replot_convergence_from_snapshot(
    snapshot_path: Path,
    plot_profile: PlotProfile,
    output_path: Path
):
    """从数据快照重新生成收敛曲线图。"""
    # 1. 加载数据快照
    data = pd.read_csv(snapshot_path)

    # 2. 创建图表并应用绘图配置
    fig, ax = plt.subplots()
    apply_profile(ax, plot_profile)
    ax.plot(data['iteration'], data['loss'], **plot_profile.artist_props.get("line", {}))
    ax.set_title("Convergence Curve")

    # 3. 保存图像
    save_figure(fig, plot_profile.save_config, output_path.parent, output_path.stem)
```

---

## 5. 使用模式

`Step`的设计应支持以下两种使用方式：

### 5.1. 在 `Workflow` 中运行 (标准模式)

这是`Step`的主要使用场景。`Step`的`run_*_step`函数由一个更高层级的`Workflow`编排脚本调用，并接收由该脚本创建和管理的`RunContext`实例。

### 5.2. 独立运行 (便捷模式)

为了便于调试、快速实验或执行单个任务，可以为`Step`提供一个便捷的独立运行脚本。

*   **命名**: 脚本应以`standalone_`为前缀，例如 `standalone_my_step_execution.py`。
*   **两种实现**:
    1.  **带追踪的单机模式**: 脚本内部创建一个临时的`RunContext`，将所有输出保存在一个指定的（或临时的）目录中。这对于需要详细追踪的独立运行非常有用。
    2.  **不带追踪的单机模式**: 如果仅为快速执行核心算法，脚本可以直接调用`internal`中的算法函数，并将输入/输出路径作为简单参数传递。此模式不产生任何追踪记录。

---

## 6. 测试策略

一个健壮的`Step`需要一个分层的测试策略来保证其质量。

### 6.1. 单元测试 (Unit Testing)

*   **目标**: 测试`Step`内部的单个函数或类的逻辑正确性。
*   **实践**:
    *   **模拟`RunContext`**: 对`facade`层的`run_*_step`函数进行测试时，必须使用`pytest.mock.MagicMock`来模拟`RunContext`。测试应断言`Step`是否以正确的参数调用了`ctx.get_step_dir`、`ctx.register_artifact`等方法。
    *   **独立测试`ArtifactHandler`**: `ArtifactHandler`的I/O逻辑应有自己的单元测试，使用`pytest`的`tmp_path` fixture来读写真实文件。

### 6.2. 集成测试 (Integration Testing)

*   **目标**: 测试多个`Step`在模拟`Workflow`中协同工作的正确性，特别是下游`Step`能否正确消费上游`Step`的产物。
*   **实践**:
    *   编写一个测试用例，该用例创建一个真实的`RunContext`（指向一个临时目录），并按顺序调用2-3个`Step`的`facade`函数。
    *   断言下游`Step`可以使用其`ArtifactHandler`成功加载上游`Step`通过`RunContext`注册的`Artifact`。

**示例 (`tests/test_integration.py`)**:
```python
def test_training_and_prediction_flow(tmp_path):
    # 1. 设置
    run_dir = tmp_path / "integration_run"
    with RunContext(run_dir) as ctx:
        # 2. 运行训练Step
        run_training_step(training_config, ctx, train_data)

        # 3. 运行预测Step
        # 它将在内部通过ctx.get_artifact_path()获取模型
        predictions = run_prediction_step(prediction_config, ctx, predict_data)

    # 4. 断言
    assert "predictions" in predictions
    # 验证预测结果的正确性...
```

### 6.3. 契约测试 (Contract Testing)

*   **目标**: 验证`Artifact`的“生产者”和“消费者”之间的数据接口契约是否得到遵守。
*   **实践**:
    *   为关键的、结构化的`Artifact`（如报告JSON、数据快照CSV）定义一个`Pydantic`模型作为其数据模式（Schema）。
    *   生产者`Step`的测试应验证其输出的`Artifact`文件内容符合此Schema。
    *   消费者`Step`的测试可以使用此Schema来生成模拟的输入`Artifact`，从而解耦对生产者`Step`的依赖。

### 6.4. 回归测试 (Regression Testing)

*   **目标**: 验证`Step`或`Workflow`的关键性能指标没有因为代码变更而意外下降。
*   **实践**: 在集成测试中，可以检查由`ctx.log_metrics()`记录的关键指标（如模型准确率、损失函数值）是否在一个预期的合理范围内。
    ```python
    # 在集成测试的最后
    loaded_ctx = RunContext.load(run_dir)
    accuracy = loaded_ctx.metrics["validation"]["accuracy"]
    assert 0.85 < accuracy < 0.95, "模型准确率超出预期范围！"
    ```

---

## 7. 附录：完整示例骨架

以下是一个遵循本框架规范的`swift_pso`步骤的完整代码骨架。

#### `scape/core/swift_pso/config.py`
```python
from pydantic import BaseModel, Field

class SwiftPsoConfig(BaseModel):
    n_particles: int = Field(100, description="粒子数量", gt=10)
    max_iterations: int = Field(300, description="最大迭代次数", gt=10)
    # ... 其他配置 ...
```

#### `scape/core/swift_pso/constants.py`
```python
from enum import Enum

class SwiftPsoArtifacts(str, Enum):
    FINAL_MODEL_PARAMS = "swift_pso.models.final_parameters"
    CONVERGENCE_REPORT = "swift_pso.reports.convergence_stats"
    CONVERGENCE_PLOT = "swift_pso.plots.convergence"
    CONVERGENCE_PLOT_DATA = "swift_pso.data_snapshots.convergence"
```

#### `scape/core/swift_pso/artifact_handler.py`
```python
import json
from pathlib import Path

class SwiftPsoArtifactHandler:
    @staticmethod
    def save_parameters(params: dict, path: Path):
        path.write_text(json.dumps(params, indent=2))

    @staticmethod
    def load_parameters(path: Path) -> dict:
        return json.loads(path.read_text())
```

#### `scape/core/swift_pso/facade.py`
```python
from .config import SwiftPsoConfig
from .constants import SwiftPsoArtifacts
from .artifact_handler import SwiftPsoArtifactHandler
from .internal import pso_optimizer
from logwp.extras.tracking import RunContext

def run_swift_pso_step(config: SwiftPsoConfig, ctx: RunContext, initial_data):
    """执行SWIFT-PSO优化步骤。

    该步骤使用粒子群优化算法来寻找FOSTER-NMR模型的最佳参数。

    Args:
        config: SWIFT-PSO步骤的配置对象。
        ctx: 当前运行的上下文，用于追踪。
        initial_data: 用于优化的初始数据集。

    Returns:
        一个包含最终优化参数的字典。

    Artifacts:
        - swift_pso.models.final_parameters: 最终优化后的模型参数（JSON）。
    """
    step_dir = ctx.get_step_dir("swift_pso")

    # 调用内部优化器
    final_params, stats = pso_optimizer.run(config, initial_data)

    # 保存并注册产物
    params_path = step_dir / "final_params.json"
    SwiftPsoArtifactHandler.save_parameters(final_params, params_path)
    ctx.register_artifact(
        artifact_path=params_path.relative_to(ctx.run_dir),
        artifact_name=SwiftPsoArtifacts.FINAL_MODEL_PARAMS
    )

    # 记录指标
    ctx.log_metrics(stats, step_name="swift_pso")

    return {"final_parameters": final_params}
```

#### `scape/core/swift_pso/__init__.py`
```python
# 导出公共API
from .facade import run_swift_pso_step
from .config import SwiftPsoConfig
from .constants import SwiftPsoArtifacts
from .artifact_handler import SwiftPsoArtifactHandler

__all__ = [
    "run_swift_pso_step",
    "SwiftPsoConfig",
    "SwiftPsoArtifacts",
    "SwiftPsoArtifactHandler",
]
```

## 8. 架构与流程可视化
为了更直观地理解本框架的设计，本节提供组件的对象结构图和一次典型运行的关键流程图。

### 8.1. 对象结构与关系图
此图展示了框架中核心概念（Workflow, RunContext, Step等）之间的静态关系和职责划分。它强调了“关注点分离”和“清晰接口”的设计原则。

```mermaid
graph TD
    subgraph "上层编排 (Workflow Layer)"
        A[Workflow 驱动脚本]
    end

    subgraph "追踪核心 (Tracking Core)"
        B(RunContext)
    end

    subgraph "组件/步骤 (Step Component)"
        style C fill:#f9f,stroke:#333,stroke-width:2px
        C(MyStep 包)
        subgraph C
            D[facade.py<br>run_my_step()]
            E[config.py<br>MyStepConfig]
            F[artifact_handler.py<br>MyStepArtifactHandler]
            G[internal/*]
        end
    end

    subgraph "物理产物 (Physical Output)"
        H((Artifact<br>模型/数据文件))
    end

    A -- "1. 创建" --> B
    A -- "2. 创建" --> E
    A -- "3. 调用" --> D

    D -- "4. 接收" --> B
    D -- "4. 接收" --> E
    D -- "5. 调用" --> G
    D -- "6. 使用" --> F

    B -- "7. 管理" --> H
    F -- "8. 读/写" --> H

    linkStyle 0,1,2,3,4,5,6,7,8 stroke-width:2px,fill:none,stroke:gray;
```
图解说明:

- Workflow驱动脚本: 位于最上层，负责整个流程的编排。它创建 RunContext 和 Step 所需的配置 MyStepConfig。
- 调用入口: Workflow 脚本通过调用 facade 模块中的 run_my_step() 函数来启动一个步骤。
- 参数传递: run_my_step() 函数接收 RunContext 和 MyStepConfig 作为核心输入，这是它与外界交互的“契约”。
- 内部实现: run_my_step() 内部调用 internal 目录下的核心算法逻辑，并将产物的读写操作委托给 MyStepArtifactHandler。
- 产物交互: Artifact Handler 负责具体的I/O操作，将模型或数据（Artifact）写入文件系统。
- 追踪记录: run_my_step() 通过 RunContext 提供的接口（如 register_artifact）来记录产物元数据，RunContext 最终将所有信息汇总到清单文件中。

### 8.2. 关键工作流程图

此序列图展示了一次包含两个步骤（一个训练，一个预测）的典型工作流中，各个组件之间随时间发生的动态交互。

```mermaid
sequenceDiagram
    participant U as User/Workflow
    participant R as RunContext
    participant S1 as Step 1 (Training)
    participant H1 as ArtifactHandler 1
    participant S2 as Step 2 (Prediction)

    U->>R: 创建 RunContext(run_dir, config)
    activate R

    U->>S1: run_training_step(config1, ctx)
    activate S1
    S1->>R: get_step_dir("training")
    R-->>S1: 返回 "training" 目录路径
    S1->>H1: save_model(model_data, path)
    activate H1
    H1-->>S1: 模型保存成功
    deactivate H1
    S1->>R: register_artifact(name="final_model", ...)
    R-->>S1: 注册成功
    S1-->>U: 返回训练结果
    deactivate S1

    U->>S2: run_prediction_step(config2, ctx)
    activate S2
    S2->>R: get_artifact_path("final_model")
    R-->>S2: 返回模型文件绝对路径
    S2->>H1: load_model(path)
    activate H1
    H1-->>S2: 返回加载的模型对象
    deactivate H1
    S2-->>U: 返回预测结果
    deactivate S2

    deactivate R
    Note right of U: `with` 块结束, RunContext 自动<br>将所有记录写入 manifest.json
```
图解说明:

- 初始化: 用户（或 Workflow 脚本）首先初始化 RunContext。
- Step 1 (生产者):
  - Step 1 从 RunContext 获取其专属的输出目录。
  - 它使用自己的 ArtifactHandler 将训练好的模型保存到该目录。
  - 然后，它调用 RunContext 的 register_artifact 方法，将刚才保存的文件的元数据（路径、哈希等）注册到运行清单中。
- Step 2 (消费者):
  - Step 2 需要使用 Step 1 产出的模型。它通过向 RunContext 查询逻辑名称 "final_model" 来获取该产物的物理路径。
  - 然后，它使用 Step 1 提供的 ArtifactHandler 来加载模型文件。
- 完成: 当所有步骤执行完毕，RunContext 的上下文管理器退出时，它会自动将所有记录的参数、指标和产物信息持久化到 manifest.json 文件中，完成一次可复现的运行记录。

## 9. 重构迁移示例：SWIFT-PSO 多步骤工作流

本节以 `SWIFT-PSO` 功能为例，详细展示如何将一个复杂的、包含多个计算阶段的模块，按照本框架的“多步骤包”规范进行重构。此示例将原有的单一训练步骤拆分为一个**核心训练步骤**和一个独立的**t-SNE可视化步骤**，以应对t-SNE部分计算量大的问题。

### 9.1 重构映射关系

下表清晰地展示了 `SWIFT-PSO` 模块在重构为多步骤包前后的组件对应关系。

| 现有组件 (Old Component) | 迁移后组件 (New Component) | 重构说明 (Refactoring Notes) |
| :--- | :--- | :--- |
| `pso_experiment.py` 中的单一实验函数 | `training_facade.py` (`run_training_step`) <br> `visualization_facade.py` (`run_visualization_step`) | 单一的实验函数被拆分为两个独立的、可追踪的`Step`：一个用于核心训练，一个用于计算密集型的t-SNE可视化。 |
| `pso_experiment.py` 内的PSO算法逻辑 | `internal/pso_optimizer.py` (共享) | 核心PSO优化器逻辑保持在`internal`中，由训练步骤`training_facade`调用。 |
| `visual_t_sne.py` 内的t-SNE计算逻辑 | `internal/tsne_computer.py` (共享) | t-SNE降维计算和绘图逻辑被移入`internal`，由可视化步骤`visualization_facade`调用。 |
| 单一的`SwiftPsoConfig`模型 | `config.py` 中的 `SwiftPsoTrainingConfig` 和 `TsneVisualConfig` | 为每个`Step`创建了独立的Pydantic配置模型，职责更清晰。训练配置不再包含可视化开关。 |
| 单一的`Artifact Handler` | `artifact_handler.py` 中的 `SwiftPsoArtifactHandler` | 统一的`Artifact Handler`服务于两个步骤，处理共享的产物格式（如参数JSON和数据CSV）。 |
| 单一的`constants.py` | `constants.py` 中的 `SwiftPsoTrainingArtifacts` 和 `TsneVisualArtifacts` | 为每个`Step`定义了独立的产物常量枚举，并严格遵循`step_name.<category>.<specific_name>`命名规范。 |

### 9.2 重构后代码示例

以下是重构后 `scape/core/swift_pso` 多步骤包的核心文件结构和代码。

#### `scape/core/swift_pso/config.py`

```python
from typing import Dict, Any
from pydantic import BaseModel, Field

class SwiftPsoTrainingConfig(BaseModel):
    """SWIFT-PSO 训练步骤的配置模型。"""
    bootstrap_iterations: int = Field(20, description="Bootstrap 迭代次数", gt=0)
    narrow_window_factor: float = Field(0.3, description="Fine-Tuning阶段的窄窗口缩放因子", gt=0, le=1)
    # ... 其他训练相关参数 ...
    pso_config_lowo: Dict[str, Any] = Field(..., description="Bootstrap+LOWO阶段的PSO配置")
    pso_config_finetune: Dict[str, Any] = Field(..., description="Fine-Tuning阶段的PSO配置")

class TsneVisualConfig(BaseModel):
    """t-SNE 可视化步骤的配置模型。"""
    perplexity: int = Field(30, description="t-SNE算法的Perplexity参数", gt=0)
    n_iter: int = Field(1000, description="t-SNE的优化迭代次数", gt=250)
    plot_profile_name: str = Field(
        "swift_pso.tsne_convergence",
        description="用于t-SNE绘图的Plotting Profile名称"
    )

```

#### `scape/core/swift_pso/constants.py`

```python
from enum import Enum

class SwiftPsoTrainingArtifacts(str, Enum):
    """定义SWIFT-PSO训练步骤的所有产物名称。"""
    # 关键输出，作为可视化步骤的输入
    ALL_OPTIMIZED_PARAMETERS = "swift_pso_training.datasets.all_parameters_from_lowo"

    # 其他训练产物
    FINAL_PARAMETERS = "swift_pso_training.models.final_parameters"
    CONVERGENCE_HISTORY_FINETUNE = "swift_pso_training.reports.convergence_history_finetune"

class TsneVisualArtifacts(str, Enum):
    """定义t-SNE可视化步骤的所有产物名称。"""
    # 遵循 "数据快照" 原则
    TSNE_PLOT = "swift_pso_visualization.plots.tsne_convergence"
    TSNE_PLOT_DATA = "swift_pso_visualization.data_snapshots.tsne_convergence"

```

#### `scape/core/swift_pso/artifact_handler.py`

```python
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any

class SwiftPsoArtifactHandler:
    """
    服务于SWIFT-PSO所有步骤的无状态产物处理器。
    """
    @staticmethod
    def save_parameters(params: Dict[str, Any], path: Path):
        path.write_text(json.dumps(params, indent=2))

    @staticmethod
    def load_parameters(path: Path) -> Dict[str, Any]:
        return json.loads(path.read_text())

    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path):
        """将DataFrame保存为CSV文件。"""
        df.to_csv(path, index=False)

    @staticmethod
    def load_dataframe(path: Path) -> pd.DataFrame:
        """从CSV文件加载DataFrame。"""
        return pd.read_csv(path)

```

#### `scape/core/swift_pso/training_facade.py`

```python
from .config import SwiftPsoTrainingConfig
from .constants import SwiftPsoTrainingArtifacts
from .artifact_handler import SwiftPsoArtifactHandler
from .internal import pso_optimizer
from logwp.extras.tracking import RunContext

if TYPE_CHECKING:
    from logwp.models.datasets.bundle import WpDataFrameBundle

def run_swift_pso_training_step(
    config: SwiftPsoTrainingConfig,
    ctx: RunContext,
    train_bundle: "WpDataFrameBundle",
    *,
    backend: str = "cpu"
) -> dict:
    """执行 SWIFT-PSO 训练与优化步骤（生产者）。

    此步骤执行核心优化，并产出所有优化后的参数集，
    供下游的可视化步骤消费。
    """
    step_dir = ctx.get_step_dir("swift_pso_training")
    ctx.log_parameter("bootstrap_iterations", config.bootstrap_iterations, step_name="swift_pso_training")
    ctx.log_parameter("backend", backend, step_name="swift_pso_training")

    # 实际调用时，backend参数会被传递给内部函数
    results = pso_optimizer.run(config, train_bundle, backend=backend)
    all_lowo_params_df = results["all_lowo_parameters_df"]

    # 使用Artifact Handler保存关键产物
    handler = SwiftPsoArtifactHandler()
    params_path = step_dir / "all_parameters_from_lowo.csv"
    handler.save_dataframe(all_lowo_params_df, params_path)

    # 注册产物，供下游步骤使用
    ctx.register_artifact(
        artifact_path=params_path.relative_to(ctx.run_dir),
        artifact_name=SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS,
        description="LOWO阶段所有Bootstrap迭代产生的优化参数，用于t-SNE可视化。"
    )

    # ... 保存和注册其他训练产物 ...

    return {"final_parameters": results["final_parameters"]}

```

#### `scape/core/swift_pso/visualization_facade.py`

```python
from .config import TsneVisualConfig
from .constants import SwiftPsoTrainingArtifacts, TsneVisualArtifacts
from .artifact_handler import SwiftPsoArtifactHandler
from .internal import tsne_computer
from logwp.extras.tracking import RunContext
from logwp.extras.plotting import registry as plot_registry
import pandas as pd

def run_tsne_visualization_step(
    config: TsneVisualConfig,
    ctx: RunContext,
    *,
    tsne_source_data: pd.DataFrame | None = None
):
    """执行t-SNE降维与可视化步骤（消费者）。

    此步骤消费训练步骤产出的参数集，执行t-SNE计算并生成图表。
    它优先使用直接传入的`tsne_source_data`，如果未提供，则从`RunContext`中加载。
    """
    step_dir = ctx.get_step_dir("swift_pso_visualization")
    handler = SwiftPsoArtifactHandler()

    # 1. 确定输入数据
    if tsne_source_data is not None:
        params_df = tsne_source_data
    else:
        # 从RunContext获取上游产物的路径
        params_artifact_path = ctx.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)
        params_df = handler.load_dataframe(params_artifact_path)

    # 2. 调用内部核心算法
    # 假设 tsne_computer.run 返回降维后的数据
    tsne_results_df = tsne_computer.run(config, params_df)

    # 3. 保存数据快照产物
    snapshot_path = step_dir / "tsne_convergence_data.csv"
    handler.save_dataframe(tsne_results_df, snapshot_path)
    ctx.register_artifact(
        artifact_path=snapshot_path.relative_to(ctx.run_dir),
        artifact_name=TsneVisualArtifacts.TSNE_PLOT_DATA
    )

    # 4. 使用plotting模块生成图表
    from . import plotting
    plot_path = step_dir / "tsne_convergence.png"
    plot_profile = plot_registry.get(config.plot_profile_name)

    plotting.replot_tsne_from_snapshot(
        snapshot_path=snapshot_path,
        plot_profile=plot_profile,
        output_path=plot_path
    )
    ctx.register_artifact(
        artifact_path=plot_path.relative_to(ctx.run_dir),
        artifact_name=TsneVisualArtifacts.TSNE_PLOT
    )

    print("t-SNE 可视化步骤完成。")

```

### 3. Workflow 驱动脚本中的调用

在一个更高层级的`Workflow`脚本中，可以按顺序编排这两个步骤。

```python
# In a high-level workflow script (e.g., run_my_experiment.py)

# ... (setup RunContext, load data etc.) ...

with RunContext(...) as ctx:

    # --- Step 1: Training ---
    print("Starting SWIFT-PSO training step...")
    training_config = SwiftPsoTrainingConfig(**workflow_config["training"])
    training_results = run_swift_pso_training_step(
        config=training_config,
        ctx=ctx,
        train_bundle=data_bundle,
        backend=workflow_config.get("backend", "cpu")
    )
    print("Training step completed.")

    # --- Step 2: Visualization (conditional) ---
    if workflow_config.get("enable_visualization", False):
        print("Starting t-SNE visualization step...")
        # 直接加载产物并传递，而不是让步骤内部加载，这使得流程更清晰
        all_params_df = ctx.load_artifact(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)

        vis_config = TsneVisualConfig(**workflow_config["visualization"])
        run_tsne_visualization_step(
            config=vis_config,
            ctx=ctx,
            tsne_source_data=all_params_df
        )
        print("Visualization step completed.")

    # ... (other steps) ...

```
