# Petroleum Engineering Units and Quantities (`logwp.extras.units`)

A package for handling physical quantities and unit conversions with
dimensional analysis, tailored for petroleum engineering.

This package prevents errors by ensuring dimensional correctness in all calculations
and provides a clear, readable syntax for unit-aware arithmetic.

## Quickstart

The package exposes a global unit registry `ureg` and a convenient quantity
constructor `Q_`.

```python
from logwp.extras import units

# 1. Creating Quantities
formation_pressure = units.Q_(3500, 'psi')
mud_density = units.Q_(1.2, 'g_cm3')
# The unit registry `ureg` automatically loads 'custom_units.yaml' if it exists.

print(formation_pressure)
# > 3500.0 psi
```

## Core Features

### Unit Conversion

The `to()` method converts a quantity to a compatible unit. The system will raise a `DimensionalityError` if the conversion is not physically valid (e.g., meters to kilograms).

```python
pressure_in_mpa = formation_pressure.to('MPa')
print(f"{pressure_in_mpa:.2f}")
# > 24.13 megapascal
```

### Dimensional Analysis & Arithmetic

Quantities can be used in mathematical expressions. The resulting unit is calculated automatically.

```python
tvd = units.Q_(2500, 'm')
g = units.Q_(9.81, 'm/s**2') # A unit can be constructed on the fly

hydrostatic_pressure = mud_density * g * tvd
print(f"Hydrostatic Pressure: {hydrostatic_pressure.to('MPa'):.2f}")
# > Hydrostatic Pressure: 29.43 megapascal
```

### Contextual Conversions (e.g., Temperature)

The package correctly handles non-linear conversions for units like temperature.

```python
boiling_point = units.Q_(100, 'celsius')
print(boiling_point.to('fahrenheit'))
# > 212.0 fahrenheit
```

Arithmetic operations (+, -) are disabled for temperature to prevent physically ambiguous calculations.

## Extending the Registry

You can add your own custom units or override existing ones in two ways:

### 1. Configuration File (Recommended)

Create a `custom_units.yaml` file in your project's root directory. The package will automatically detect and load it.

**Example `custom_units.yaml`:**
```yaml
# custom_units.yaml

units:
  # Add a new unit
  - name: foot
    symbol: ft
    dimension: length      # Must match a dimension attribute in the registry
    scale: 0.3048          # Conversion factor to the base SI unit (meter)
    aliases: ['feet']

  # Override an existing unit (e.g., a company-specific barrel)
  - name: barrel
    symbol: bbl_co
    dimension: volume
    scale: 0.1591          # A slightly different scale
```

### 2. Programmatic Registration

For dynamic or complex cases, you can register units directly in your code.

```python
from logwp.extras import units
from logwp.extras.units import Unit

my_unit = Unit(name="knot", symbol="kn", dimension=units.ureg.length / units.ureg.time, scale=0.514444)
units.ureg.register_unit(my_unit)

speed = units.Q_(10, 'kn')
print(speed.to('m/s'))
```

## Available Units

The registry includes a wide range of SI and oilfield units, including:
- **Pressure**: `psi`, `bar`, `MPa`, `kPa`, `Pa`
- **Permeability**: `darcy` (D), `millidarcy` (mD)
- **Density**: `g_cm3`
- **Volume**: `bbl`
- **Temperature**: `celsius`, `fahrenheit`, `kelvin`
- And all SI base units.
