{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a9e0a2e7", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "1e86095f", "metadata": {}, "source": ["# SWIFT-PSO 案例\n", "- 固定随机种子\n", "- 新增`收敛轨迹与聚类分析图`以及各Cluster参数统计\n", "- 12个优化参数,t2lm_exp、vmacro_b、vmacro_min为固定\n", "- random_seed = 2000\n", "- t-SNE可视化最终收敛点聚类分析 (Cluster Analysis)聚类方法：kmeans"]}, {"cell_type": "markdown", "id": "4266395e", "metadata": {}, "source": ["## 1. 初始化与导入"]}, {"cell_type": "code", "execution_count": 2, "id": "3e9b170c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-29T03:45:38.021391Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 128.09, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:40.167073Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.41, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-29T03:45:40.192137Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.42, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-29T03:45:40.216233Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.43, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-29T03:45:40.244346Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.89, 'cpu_percent': 0.0} operation=register_base_profile profile_name=swift_pso.base\n", "2025-07-29T03:45:40.260229Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.93, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_convergence\n", "2025-07-29T03:45:40.274214Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.96, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T03:45:41.702564Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 402.98, 'cpu_percent': 0.0} operation=register_base_profile profile_name=validation.plt.base\n", "2025-07-29T03:45:41.736897Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.01, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:45:41.750785Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.03, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.capture_curve\n", "2025-07-29T03:45:41.784096Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.05, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:45:41.802374Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.07, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.perm_corr.permeability_crossplot\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "# 导入 swift_pso 组件\n", "from scape.core.swift_pso import (\n", "    run_swift_pso_training_step,\n", "    run_swift_pso_prediction_step,\n", "    run_tsne_visualization_step,\n", "    SwiftPsoTrainingConfig,\n", "    SwiftPsoPredictionConfig,\n", "    TsneVisualConfig,\n", "    SwiftPsoTrainingArtifacts,\n", "    SwiftPsoPredictionArtifacts,\n", "    TsneVisualArtifacts,\n", "    TsnePlotProfiles\n", ")\n", "\n", "# 导入 validation 组件\n", "from scape.core.validation import (\n", "    run_plt_analysis_step,\n", "    run_perm_correlation_step,\n", "    PltAnalysisConfig,\n", "    PermCorrelationConfig,\n", "    PltAnalysisArtifacts,\n", "    PermCorrelationArtifacts,\n", "    PltPlotTypes,\n", "    PltAnalysisPlotProfiles,\n", "    PermCorrelationPlotProfiles\n", ")\n", "\n", "import scape.core.swift_pso.plot_profiles"]}, {"cell_type": "markdown", "id": "f519543e", "metadata": {}, "source": ["## 2. 实验设置与数据加载\n", "\n", "使用 `RunContext` 初始化一个实验，所有后续操作都将在此上下文中进行，确保所有产物和日志都保存在一个独立的运行目录中。"]}, {"cell_type": "code", "execution_count": 3, "id": "e446002f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:45:41.936008Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.83, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\swift_pso_run_20250729_114541 run_id=20250729-034541-3727a80a\n", "实验运行已初始化，所有产物将保存至: F:\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\20_swift_pso\\case12\\output01\\swift_pso_run_20250729_114541\n", "2025-07-29T03:45:41.975988Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.83, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T03:45:42.003454Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.07, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx file_size_mb=0.17 sheet_count=1\n", "2025-07-29T03:45:42.015316Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.08, 'cpu_percent': 0.0} project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T03:45:42.028989Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.1, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T03:45:42.055444Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.49, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:45:42.082130Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.7, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=11 well_curves=1\n", "2025-07-29T03:45:42.495604Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.89, 'cpu_percent': 0.0} shape=(408, 74) sheet_name=swift_pso_train_cleaned\n", "2025-07-29T03:45:42.527474Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.91, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned')\n", "2025-07-29T03:45:42.543199Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.91, 'cpu_percent': 0.0} curve_count=11 dataset_name=swift_pso_train_cleaned dataset_type=Point df_shape=(408, 74) processing_time=0.501\n", "2025-07-29T03:45:42.582697Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.91, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:45:42.607184Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.91, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:45:42.622936Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.91, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=0.647 project_name=WpIdentifier('scape_swift_pso_train_cleaned')\n", "✅ 成功读取训练数据: ./scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T03:45:42.654646Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.18, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=11 input_curves=['PHI_T2_DIST', 'PZI', 'PHIT_NMR', 'MD', 'T2LM', 'DPHIT_NMR', 'T2_P50', 'DT2_P50', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL'] operation=extract_metadata output_curve_count=11 output_curves=['PHI_T2_DIST', 'PZI', 'PHIT_NMR', 'MD', 'T2LM', 'DPHIT_NMR', 'T2_P50', 'DT2_P50', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL']\n", "2025-07-29T03:45:42.690378Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.18, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T03:45:42.701755Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.18, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T03:45:42.717446Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.18, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T03:45:42.733154Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.18, 'cpu_percent': 0.0}\n", "===train_bundle:   WELL_NO       MD  PHIT_NMR      T2LM   DT2_P50    T2_P50  DPHIT_NMR  \\\n", "0     C-1  6319.40  0.105551   785.428  0.316622   997.666   0.079668   \n", "1     C-1  6335.20  0.138267   746.054  0.111100   871.799   0.023800   \n", "2     C-1  6334.52  0.137828  3960.371  1.190176  6131.592   0.060766   \n", "3     C-1  6337.00  0.100931   527.925  0.102427   663.328   0.004202   \n", "4     C-1  6349.70  0.047791   111.798 -0.399350   203.148  -0.029715   \n", "\n", "     T2_VALUE_1    T2_VALUE_2    T2_VALUE_3  ...  T2_VALUE_58  T2_VALUE_59  \\\n", "0  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003882     0.003707   \n", "1  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.004239     0.003638   \n", "2  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.010259     0.012969   \n", "3  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003190     0.003105   \n", "4  1.506000e-05  3.392000e-05  6.404000e-05  ...     0.000000     0.000000   \n", "\n", "   T2_VALUE_60  T2_VALUE_61  T2_VALUE_62  T2_VALUE_63  T2_VALUE_64  K_LABEL  \\\n", "0     0.003506     0.003268     0.002990     0.002676     0.002339    0.888   \n", "1     0.003084     0.002577     0.002114     0.001696     0.001326    0.205   \n", "2     0.015308     0.017210     0.018582     0.019324     0.019364  153.000   \n", "3     0.003001     0.002867     0.002698     0.002490     0.002247    6.130   \n", "4     0.000000     0.000000     0.000000     0.000000     0.000000    0.263   \n", "\n", "   K_LABEL_TYPE  PZI  \n", "0          CORE    1  \n", "1           MDT    1  \n", "2          CORE    1  \n", "3          CORE    1  \n", "4          CORE    1  \n", "\n", "[5 rows x 74 columns]\n", "2025-07-29T03:45:42.767373Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.23, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL']\n", "===train_label_all_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T03:45:42.843193Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.25, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL']\n", "===train_label_pz_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T03:45:42.908903Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.31, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL']\n", "===train_label_core_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "2025-07-29T03:45:42.983391Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.31, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'K_LABEL']\n", "===train_label_core_pz_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "  - T2轴信息: T2AxisLog10(t2_start=0.1, t2_end=10000, n=64, unit='ms')\n"]}], "source": ["# --- 实验设置 ---\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"swift_pso_run\")\n", "run_context = RunContext(output_dir / run_dir_name,overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")\n", "\n", "# --- 加载训练数据 ---\n", "data_file_path = \"./scape_swift_pso_train_cleaned.wp.xlsx\"\n", "reader = WpExcelReader()\n", "train_project = reader.read(data_file_path)\n", "print(f\"✅ 成功读取训练数据: {data_file_path}\")\n", "\n", "train_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',\n", "        'PHI_T2_DIST', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_bundle: {train_bundle.data.head()}\")\n", "\n", "# 训练集岩心检验数据准备\n", "train_label_all_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_all_bundle: {train_label_all_bundle.data.head()}\")\n", "\n", "train_label_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_pz_bundle: {train_label_pz_bundle.data.head()}\")\n", "\n", "train_label_core_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE'\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_bundle: {train_label_core_bundle.data.head()}\")\n", "\n", "train_label_core_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE' and PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_pz_bundle: {train_label_core_pz_bundle.data.head()}\")\n", "\n", "# --- 准备数据依赖参数 ---\n", "t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "t2_time_array = t2_axis_info.calculate_values()\n", "print(f\"  - T2轴信息: {t2_axis_info}\")"]}, {"cell_type": "markdown", "id": "e5b87190", "metadata": {}, "source": ["## 3. 步骤一：SWIFT-PSO 训练\n", "\n", "调用 `run_swift_pso_training_step` 执行训练。我们首先使用 `SwiftPsoTrainingConfig.create_default()` 创建一个默认配置对象，然后将数据依赖的参数（如参考值、T2轴）注入其中。"]}, {"cell_type": "code", "execution_count": 4, "id": "76495147", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\n", "2025-07-29T03:45:43.125017Z [info     ] 开始SWIFT-PSO训练步骤                [scape.core.swift_pso.training_facade] backend=gpu bootstrap_iterations=20 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.55, 'cpu_percent': 0.0} operation=swift_pso_training_step run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:43.156485Z [info     ] 成功替换Bundle中的曲线                 [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.66, 'cpu_percent': 0.0} curve_name=K_LABEL_TYPE new_data_type=INT new_shape=(408,) operation=replace_curve\n", "2025-07-29T03:45:43.191846Z [info     ] 必需曲线验证通过                       [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') checked_curves_count=9 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.66, 'cpu_percent': 0.0} operation=validate_required_curves\n", "2025-07-29T03:45:43.208644Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=gpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.66, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T03:45:43.232206Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.configs.training_config artifact_path=swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.77, 'cpu_percent': 0.0} description=本次训练步骤的完整Pydantic配置快照，确保可复现性。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:43.255666Z [info     ] 训练配置已保存为产物                     [scape.core.swift_pso.training_facade] config_path=output01\\swift_pso_run_20250729_114541\\swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.77, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:43.260682Z [info     ] 调用内部PSO优化器                     [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.77, 'cpu_percent': 0.0} operation=swift_pso_training_step\n", "2025-07-29T03:45:43.284979Z [info     ] 开始按井拆分DataFrame Bundle         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.77, 'cpu_percent': 0.0} operation=to_all_wells_data\n", "2025-07-29T03:45:43.308604Z [info     ] DataFrame Bundle按井拆分完成         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 408.11, 'cpu_percent': 0.0} operation=to_all_wells_data wells_found=['C-1', 'C-2', 'T-1']\n", "--- Bootstrap-<PERSON>de 1/20 ---\n", "2025-07-29T03:45:43.342206Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 409.32, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 151 代触发。\n", "2025-07-29T03:45:45.627050Z [info     ] PSO 优化完成。最终迭代次数: 151, 最优损失: 6.725111 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.47, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:45.642794Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.47, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:45.690642Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.5, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:45.706601Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(8.999999999999999e-08, 0.16021067999999997) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T03:45:45.738193Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:45:47.256434Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 6.788366 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:47.287964Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:47.319735Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.51, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:47.351682Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(6.999999999999999e-08, 0.07633129999999999) v_micro_range=(0.00457161, 0.10102376999999998)\n", "2025-07-29T03:45:47.390892Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 108 代触发。\n", "2025-07-29T03:45:48.893606Z [info     ] PSO 优化完成。最终迭代次数: 108, 最优损失: 6.285369 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:48.925415Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:48.957483Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:48.988964Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.07809129999999999) v_meso_range=(0.0009777800000000001, 0.09862211) v_micro_range=(0.00422468, 0.05513255000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 2/20 ---\n", "2025-07-29T03:45:49.020694Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 79 代触发。\n", "2025-07-29T03:45:50.111346Z [info     ] PSO 优化完成。最终迭代次数: 79, 最优损失: 5.841797 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:50.116706Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:50.148382Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:50.180053Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.0857914) v_meso_range=(0.00455389, 0.18791093999999997) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T03:45:50.220769Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 188 代触发。\n", "2025-07-29T03:45:52.695880Z [info     ] PSO 优化完成。最终迭代次数: 188, 最优损失: 5.136477 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:52.711575Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:52.743052Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:52.774795Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.3e-07, 0.08431650999999998) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T03:45:52.806368Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.7, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:45:54.407201Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 7.998781 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:54.433871Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:54.466149Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:54.487291Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 3/20 ---\n", "2025-07-29T03:45:54.513797Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 204 代触发。\n", "2025-07-29T03:45:57.168259Z [info     ] PSO 优化完成。最终迭代次数: 204, 最优损失: 5.472105 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:57.194893Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:57.227232Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:57.248342Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285228, 0.17181711999999996) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T03:45:57.274935Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 159 代触发。\n", "2025-07-29T03:45:59.435751Z [info     ] PSO 优化完成。最终迭代次数: 159, 最优损失: 6.510588 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:45:59.448963Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:59.489097Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:45:59.515709Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.00393, 0.10102375999999999)\n", "2025-07-29T03:45:59.546963Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 157 代触发。\n", "2025-07-29T03:46:01.796662Z [info     ] PSO 优化完成。最终迭代次数: 157, 最优损失: 5.267429 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:01.822172Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:01.849935Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:01.879272Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.00102866, 0.09212977000000001) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 4/20 ---\n", "2025-07-29T03:46:01.918093Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 132 代触发。\n", "2025-07-29T03:46:03.703999Z [info     ] PSO 优化完成。最终迭代次数: 132, 最优损失: 7.094233 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:03.726900Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:03.756421Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:03.784004Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.0028523, 0.17507141999999995) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T03:46:03.810602Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 74 代触发。\n", "2025-07-29T03:46:04.864500Z [info     ] PSO 优化完成。最终迭代次数: 74, 最优损失: 4.966953 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:04.884304Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:04.921397Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:04.944424Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(1.4e-07, 0.09115818999999999) v_micro_range=(3.800000000000002e-07, 0.09835937)\n", "2025-07-29T03:46:04.984421Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 90 代触发。\n", "2025-07-29T03:46:06.291672Z [info     ] PSO 优化完成。最终迭代次数: 90, 最优损失: 6.076406 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:06.319874Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:06.358448Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:06.373375Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.06876098) v_meso_range=(0.008276929999999998, 0.11841274) v_micro_range=(0.0003839299999999999, 0.044701840000000014)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 5/20 ---\n", "2025-07-29T03:46:06.411702Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 96 代触发。\n", "2025-07-29T03:46:07.625615Z [info     ] PSO 优化完成。最终迭代次数: 96, 最优损失: 4.977387 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:07.652173Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.75, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:07.678158Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:07.705582Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.07432702) v_meso_range=(0.01783688, 0.18938359999999996) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T03:46:07.745624Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 150 代触发。\n", "2025-07-29T03:46:09.786321Z [info     ] PSO 优化完成。最终迭代次数: 150, 最优损失: 3.897100 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:09.799599Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:09.848800Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:09.880358Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.00393, 0.10102375999999999)\n", "2025-07-29T03:46:09.906292Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 195 代触发。\n", "2025-07-29T03:46:12.687349Z [info     ] PSO 优化完成。最终迭代次数: 195, 最优损失: 6.038473 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:12.697484Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:12.734271Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:12.760162Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583869999999999, 0.09813032) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 6/20 ---\n", "2025-07-29T03:46:12.787545Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 99 代触发。\n", "2025-07-29T03:46:14.107951Z [info     ] PSO 优化完成。最终迭代次数: 99, 最优损失: 6.221747 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:14.129587Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:14.147846Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:14.178456Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.08579139000000001) v_meso_range=(9.999999999999998e-08, 0.15713684999999997) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T03:46:14.214560Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 193 代触发。\n", "2025-07-29T03:46:16.748860Z [info     ] PSO 优化完成。最终迭代次数: 193, 最优损失: 7.152123 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:16.769885Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:16.802240Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:16.828908Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.7000000000000004e-07, 0.08604465) v_micro_range=(3.400000000000002e-07, 0.09647831000000001)\n", "2025-07-29T03:46:16.864327Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:46:18.562940Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 7.828927 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:18.583649Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:18.615635Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:18.641892Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 7/20 ---\n", "2025-07-29T03:46:18.669670Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 191 代触发。\n", "2025-07-29T03:46:21.257271Z [info     ] PSO 优化完成。最终迭代次数: 191, 最优损失: 4.862065 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:21.284044Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:21.310674Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:21.350577Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(8.999999999999999e-08, 0.16021067999999997) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T03:46:21.376608Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 92 代触发。\n", "2025-07-29T03:46:22.737858Z [info     ] PSO 优化完成。最终迭代次数: 92, 最优损失: 5.392507 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:22.751234Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:22.788452Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:22.823270Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.4e-07, 0.08560667999999999) v_micro_range=(3.700000000000002e-07, 0.09835936)\n", "2025-07-29T03:46:22.856810Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 110 代触发。\n", "2025-07-29T03:46:24.405131Z [info     ] PSO 优化完成。最终迭代次数: 110, 最优损失: 6.110406 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:24.430054Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:24.458476Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:24.485124Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007509629999999999, 0.09788414000000001) v_micro_range=(0.0005744599999999999, 0.044776070000000015)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 8/20 ---\n", "2025-07-29T03:46:24.525203Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 122 代触发。\n", "2025-07-29T03:46:26.125688Z [info     ] PSO 优化完成。最终迭代次数: 122, 最优损失: 6.530291 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:26.139062Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:26.165798Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:26.205668Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.0014767699999999994, 0.16761701999999998) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T03:46:26.232494Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 98 代触发。\n", "2025-07-29T03:46:27.566299Z [info     ] PSO 优化完成。最终迭代次数: 98, 最优损失: 4.806744 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:27.586272Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:27.621346Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:27.646269Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.05339436000000001) v_meso_range=(9.999999999999998e-08, 0.08808925) v_micro_range=(0.00370749, 0.09978174999999999)\n", "2025-07-29T03:46:27.673054Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 133 代触发。\n", "2025-07-29T03:46:29.633834Z [info     ] PSO 优化完成。最终迭代次数: 133, 最优损失: 5.449725 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.76, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:29.661836Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.77, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:29.687069Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.78, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:29.713752Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.78, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.07809129999999999) v_meso_range=(0.007206029999999999, 0.10613274) v_micro_range=(0.0019181899999999989, 0.045421560000000014)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 9/20 ---\n", "2025-07-29T03:46:29.754187Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.88, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 139 代触发。\n", "2025-07-29T03:46:31.587601Z [info     ] PSO 优化完成。最终迭代次数: 139, 最优损失: 7.280151 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.96, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:31.605284Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.96, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:31.634625Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.96, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:31.666644Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.98, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285234, 0.18108265999999998) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T03:46:31.687903Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.98, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 97 代触发。\n", "2025-07-29T03:46:33.008344Z [info     ] PSO 优化完成。最终迭代次数: 97, 最优损失: 4.659664 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.98, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:33.030435Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.98, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:33.066145Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.98, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:33.088238Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.99, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.5000000000000002e-07, 0.08604462999999998) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:46:33.114999Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.04, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 186 代触发。\n", "2025-07-29T03:46:35.732668Z [info     ] PSO 优化完成。最终迭代次数: 186, 最优损失: 8.233968 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:35.758284Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:35.793119Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:35.812779Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007509629999999999, 0.09788414000000001) v_micro_range=(0.0005744599999999999, 0.044776070000000015)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 10/20 ---\n", "2025-07-29T03:46:35.855331Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 108 代触发。\n", "2025-07-29T03:46:37.343312Z [info     ] PSO 优化完成。最终迭代次数: 108, 最优损失: 6.534539 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:37.369940Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:37.398234Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:37.431906Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.0857914) v_meso_range=(0.00455383, 0.17864539999999995) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T03:46:37.469918Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 190 代触发。\n", "2025-07-29T03:46:40.086633Z [info     ] PSO 优化完成。最终迭代次数: 190, 最优损失: 7.230206 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:40.102642Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:40.141233Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:40.167572Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.5000000000000002e-07, 0.08604462999999998) v_micro_range=(3.600000000000002e-07, 0.09835935000000001)\n", "2025-07-29T03:46:40.212323Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 104 代触发。\n", "2025-07-29T03:46:41.717933Z [info     ] PSO 优化完成。最终迭代次数: 104, 最优损失: 6.064808 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:41.733930Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:41.765625Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:41.781467Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(1e-08, 0.0596632) v_meso_range=(0.008608919999999999, 0.12925048) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11/20 ---\n", "2025-07-29T03:46:41.813157Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 131 代触发。\n", "2025-07-29T03:46:43.575807Z [info     ] PSO 优化完成。最终迭代次数: 131, 最优损失: 6.208149 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:43.593627Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:43.624707Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:43.651586Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285235, 0.18265773) v_micro_range=(3.300000000000002e-07, 0.042200610000000006)\n", "2025-07-29T03:46:43.682837Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 117 代触发。\n", "2025-07-29T03:46:45.274199Z [info     ] PSO 优化完成。最终迭代次数: 117, 最优损失: 5.883167 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:45.290071Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:45.321386Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:45.358295Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.2e-07, 0.08241936999999999) v_micro_range=(3.9000000000000024e-07, 0.09835938)\n", "2025-07-29T03:46:45.392089Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:46:47.011546Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 8.472033 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:47.029651Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:47.067362Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:47.090931Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 12/20 ---\n", "2025-07-29T03:46:47.125053Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 149 代触发。\n", "2025-07-29T03:46:49.074940Z [info     ] PSO 优化完成。最终迭代次数: 149, 最优损失: 5.455302 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:49.090681Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:49.133218Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:49.162501Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(7.999999999999999e-08, 0.14883592999999998) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T03:46:49.202045Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 151 代触发。\n", "2025-07-29T03:46:51.241673Z [info     ] PSO 优化完成。最终迭代次数: 151, 最优损失: 6.541027 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:51.266467Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:51.297895Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:51.313638Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(8.999999999999999e-08, 0.0780848) v_micro_range=(0.00370749, 0.09978174999999999)\n", "2025-07-29T03:46:51.345273Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:46:52.956194Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 7.933418 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:52.971863Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:53.013155Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:53.034830Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 13/20 ---\n", "2025-07-29T03:46:53.066354Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 97 代触发。\n", "2025-07-29T03:46:54.323043Z [info     ] PSO 优化完成。最终迭代次数: 97, 最优损失: 7.339596 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:54.336367Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:54.376400Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:54.403087Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.0014767699999999994, 0.16761701999999998) v_micro_range=(0.00038958999999999993, 0.06871368000000001)\n", "2025-07-29T03:46:54.439096Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.14, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 116 代触发。\n", "2025-07-29T03:46:56.163679Z [info     ] PSO 优化完成。最终迭代次数: 116, 最优损失: 7.756560 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:56.176963Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:56.216998Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:56.243740Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.8000000000000005e-07, 0.08969831) v_micro_range=(3.300000000000002e-07, 0.09200621000000002)\n", "2025-07-29T03:46:56.274064Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 193 代触发。\n", "2025-07-29T03:46:58.978157Z [info     ] PSO 优化完成。最终迭代次数: 193, 最优损失: 5.269834 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:46:59.004874Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:59.041110Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:46:59.057963Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0031885800000000003, 0.09788410000000002) v_micro_range=(0.00268107, 0.049097120000000015)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 14/20 ---\n", "2025-07-29T03:46:59.084890Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 99 代触发。\n", "2025-07-29T03:47:00.384524Z [info     ] PSO 优化完成。最终迭代次数: 99, 最优损失: 7.666782 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:00.402387Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:00.435521Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:00.458561Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285231, 0.17610280999999997) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T03:47:00.498577Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 166 代触发。\n", "2025-07-29T03:47:02.752824Z [info     ] PSO 优化完成。最终迭代次数: 166, 最优损失: 7.035452 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:02.779530Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:02.819620Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:02.845075Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.00393, 0.10102375999999999)\n", "2025-07-29T03:47:02.872934Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 148 代触发。\n", "2025-07-29T03:47:05.060418Z [info     ] PSO 优化完成。最终迭代次数: 148, 最优损失: 5.383756 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:05.092549Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:05.134213Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:05.168692Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.06876098) v_meso_range=(0.0038816500000000004, 0.11627095) v_micro_range=(0.00268107, 0.049097120000000015)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 15/20 ---\n", "2025-07-29T03:47:05.206644Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 85 代触发。\n", "2025-07-29T03:47:06.335117Z [info     ] PSO 优化完成。最终迭代次数: 85, 最优损失: 6.883102 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:06.354304Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:06.394380Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:06.420915Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.0028523, 0.17507141999999995) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T03:47:06.459899Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 118 代触发。\n", "2025-07-29T03:47:08.008429Z [info     ] PSO 优化完成。最终迭代次数: 118, 最优损失: 4.963103 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:08.021418Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:08.061468Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:08.089612Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(1e-08, 0.04824077000000001) v_meso_range=(8.999999999999999e-08, 0.09421518999999999) v_micro_range=(0.00457161, 0.10102376999999998)\n", "2025-07-29T03:47:08.114694Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 108 代触发。\n", "2025-07-29T03:47:09.635531Z [info     ] PSO 优化完成。最终迭代次数: 108, 最优损失: 6.419850 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:09.662184Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:09.698703Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:09.715472Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.07809129999999999) v_meso_range=(0.0009777800000000001, 0.09862211) v_micro_range=(0.00422468, 0.05513255000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 16/20 ---\n", "2025-07-29T03:47:09.770421Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.16, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 97 代触发。\n", "2025-07-29T03:47:10.995900Z [info     ] PSO 优化完成。最终迭代次数: 97, 最优损失: 5.500352 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.17, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:11.009220Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.17, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:11.049271Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.17, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:11.075994Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.17, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.0857914) v_meso_range=(0.00455385, 0.18189969999999994) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T03:47:11.116060Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.17, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 164 代触发。\n", "2025-07-29T03:47:13.291684Z [info     ] PSO 优化完成。最终迭代次数: 164, 最优损失: 5.899917 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.17, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:13.303554Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.17, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:13.335980Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:13.369612Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.6000000000000003e-07, 0.08604464) v_micro_range=(3.500000000000002e-07, 0.09835934000000002)\n", "2025-07-29T03:47:13.410270Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:47:14.979457Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 8.424812 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:14.997429Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:15.024065Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:15.050694Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 17/20 ---\n", "2025-07-29T03:47:15.090268Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.18, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 118 代触发。\n", "2025-07-29T03:47:16.611366Z [info     ] PSO 优化完成。最终迭代次数: 118, 最优损失: 7.265567 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:16.624598Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:16.664649Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:16.699948Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285229, 0.17392412) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T03:47:16.737145Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 94 代触发。\n", "2025-07-29T03:47:18.024213Z [info     ] PSO 优化完成。最终迭代次数: 94, 最优损失: 6.412029 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:18.038592Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:18.065306Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:18.103412Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(1.0999999999999998e-07, 0.08015219999999999) v_micro_range=(4.0000000000000025e-07, 0.09835938999999999)\n", "2025-07-29T03:47:18.139401Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.19, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:47:19.752885Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 7.692414 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:19.772697Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:19.812595Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:19.839307Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 18/20 ---\n", "2025-07-29T03:47:19.879433Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T03:47:21.546618Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 6.295204 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:21.572261Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:21.610965Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:21.637071Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.0857914) v_meso_range=(8.999999999999999e-08, 0.15566420999999997) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T03:47:21.677332Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 166 代触发。\n", "2025-07-29T03:47:23.878626Z [info     ] PSO 优化完成。最终迭代次数: 166, 最优损失: 6.528109 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:23.894034Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:23.933057Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:23.959818Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.00393, 0.10102375999999999)\n", "2025-07-29T03:47:23.993858Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T03:47:25.614848Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 7.888118 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:25.640520Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:25.671736Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:25.694696Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 19/20 ---\n", "2025-07-29T03:47:25.734949Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.2, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 70 代触发。\n", "2025-07-29T03:47:26.681889Z [info     ] PSO 优化完成。最终迭代次数: 70, 最优损失: 5.375747 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:26.695283Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:26.748634Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:26.789678Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.08579139000000001) v_meso_range=(0.00635756, 0.18938357999999997) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T03:47:26.827929Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 161 代触发。\n", "2025-07-29T03:47:29.016078Z [info     ] PSO 优化完成。最终迭代次数: 161, 最优损失: 7.310304 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:29.037961Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:29.075144Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:29.096259Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(9.999999999999998e-08, 0.08015218999999998) v_micro_range=(0.0008690600000000001, 0.09878561)\n", "2025-07-29T03:47:29.135037Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 165 代触发。\n", "2025-07-29T03:47:31.510262Z [info     ] PSO 优化完成。最终迭代次数: 165, 最优损失: 8.409941 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:31.523608Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:31.554140Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:31.577071Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0031885800000000003, 0.09788410000000002) v_micro_range=(0.00268107, 0.049097120000000015)\n", "--- Boots<PERSON><PERSON>-<PERSON><PERSON> 20/20 ---\n", "2025-07-29T03:47:31.603822Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 153 代触发。\n", "2025-07-29T03:47:33.644440Z [info     ] PSO 优化完成。最终迭代次数: 153, 最优损失: 7.598500 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:33.657768Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:33.704493Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:33.736812Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285232, 0.17749091999999997) v_micro_range=(3.600000000000002e-07, 0.04908202000000001)\n", "2025-07-29T03:47:33.770697Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T03:47:35.458466Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 7.074361 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:35.473331Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:35.498580Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:35.525171Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} n_bins=64 n_depths=202 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.05721957000000001) v_meso_range=(9.999999999999998e-08, 0.08015218999999998) v_micro_range=(0.0008690600000000001, 0.09878561)\n", "2025-07-29T03:47:35.565150Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 205 代触发。\n", "2025-07-29T03:47:38.446289Z [info     ] PSO 优化完成。最终迭代次数: 205, 最优损失: 5.408407 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:38.467235Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:38.499726Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:38.523060Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.24, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007509629999999999, 0.09788414000000001) v_micro_range=(0.0005744599999999999, 0.044776070000000015)\n", "2025-07-29T03:47:38.552996Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.8, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 28 代触发。\n", "2025-07-29T03:47:38.913242Z [info     ] PSO 优化完成。最终迭代次数: 28, 最优损失: 5.327661 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.06, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:38.929837Z [info     ] Fine-Tuning阶段完成，最终损失: 5.327661 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.06, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:38.939818Z [info     ] SWIFT-PSO 优化完成。                [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.06, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:38.953143Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.models.final_parameters artifact_path=swift_pso_training\\final_parameters.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.06, 'cpu_percent': 0.0} description=最终优化后的模型参数及上下文，可直接用于预测步骤。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:39.006532Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.datasets.all_parameters_from_lowo artifact_path=swift_pso_training\\all_parameters_from_lowo.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} description=所有Bootstrap+LOWO迭代产生的优化参数集，用于t-SNE可视化分析。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:39.033191Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.convergence_history_finetune artifact_path=swift_pso_training\\convergence_history_finetune.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} description=Fine-Tuning阶段的损失函数收敛历史。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:39.059347Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.bootstrap_summary artifact_path=swift_pso_training\\summary_bootstrap_mu_rmse.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} description=每轮Bootstrap的平均验证误差（mu_rmse），用于确定最佳轮次b*。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:39.093984Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.params_warm_start artifact_path=swift_pso_training\\params_warm_start.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} description=用于启动Fine-Tuning阶段的热启动参数（theta_hat_star）。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:39.113079Z [info     ] SWIFT-PSO训练步骤完成                [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} final_loss=5.327661108498567 operation=swift_pso_training_step\n", "✅ [Step 1/5] 训练完成！ 结果: {'status': 'completed', 'final_loss': 5.327661108498567}\n", "   - 最终模型参数已保存为产物: swift_pso_training.models.final_parameters\n", "   - t-SNE源数据已保存为产物: swift_pso_training.datasets.all_parameters_from_lowo\n"]}], "source": ["# 1. 使用 Pydantic 模型创建配置\n", "training_config = SwiftPsoTrainingConfig.create_default()\n", "\n", "training_config.enable_fold_diagnostics = True\n", "training_config.optimization_params=[\n", "            'log10_KSDR_A', 'PHIT_EXP', 'RHO_NMR', 'log10_KMACRO_A',\n", "            'log10_T2cutoff_short', 'log10_T2cutoff_long',\n", "            'beta_1', 'beta_2', 'delta_MDT'\n", "        ]\n", "\n", "training_config.fixed_params['T2LM_EXP'] = 2.0\n", "training_config.fixed_params['KMACRO_B'] = 2.0\n", "training_config.fixed_params['Vmacro_min'] = 0.01\n", "\n", "# 2. 注入数据依赖参数\n", "t2_p50_ref = train_bundle.data['T2_P50'].median()\n", "phit_nmr_ref = train_bundle.data['PHIT_NMR'].median()\n", "\n", "training_config.random_seed = 2000\n", "training_config.pso_config_lowo['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_lowo['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_lowo['t2_time'] = t2_time_array\n", "training_config.pso_config_lowo['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_lowo['t2_range_min'] = 0.1\n", "training_config.pso_config_lowo['t2_range_max'] = 8000\n", "\n", "training_config.pso_config_finetune['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_finetune['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_finetune['t2_time'] = t2_time_array\n", "training_config.pso_config_finetune['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_finetune['t2_range_min'] = 0.1\n", "training_config.pso_config_finetune['t2_range_max'] = 8000\n", "\n", "# 3. 调整执行参数\n", "training_config.bootstrap_iterations = 20\n", "training_config.pso_config_lowo[\"max_iterations\"] = 400\n", "training_config.pso_config_lowo[\"n_particles\"] = 150\n", "training_config.pso_config_finetune[\"max_iterations\"] = 200\n", "training_config.pso_config_finetune[\"n_particles\"] = 100\n", "\n", "# 4. 执行训练步骤\n", "print(\"🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\")\n", "training_result = run_swift_pso_training_step(\n", "    config=training_config,\n", "    ctx=run_context, # 直接传递上下文对象\n", "    train_bundle=train_bundle,\n", "    backend='gpu'  # 执行层参数直接传入\n", ")\n", "\n", "print(f\"✅ [Step 1/5] 训练完成！ 结果: {training_result}\")\n", "print(f\"   - 最终模型参数已保存为产物: {SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value}\")\n", "print(f\"   - t-SNE源数据已保存为产物: {SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value}\")"]}, {"cell_type": "markdown", "id": "b1c2d3e4", "metadata": {}, "source": ["## 4. 步骤二：模型预测\n", "\n", "使用 `run_swift_pso_prediction_step` 对训练集和应用集进行预测。我们首先从 `RunContext` 中显式加载上一步训练产出的模型，然后将其作为参数传入预测函数。"]}, {"cell_type": "code", "execution_count": 5, "id": "f5g6h7i8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:47:39.233152Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T03:47:39.259496Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx file_size_mb=1.88 sheet_count=1\n", "2025-07-29T03:47:39.285463Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T03:47:39.299398Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T03:47:39.320756Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:47:39.352174Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.83, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=8 well_curves=1\n", "2025-07-29T03:47:43.154961Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.61, 'cpu_percent': 0.0} shape=(4689, 71) sheet_name=swift_pso_apply_cleaned\n", "2025-07-29T03:47:43.208115Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.61, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned')\n", "2025-07-29T03:47:43.234771Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.61, 'cpu_percent': 0.0} curve_count=8 dataset_name=swift_pso_apply_cleaned dataset_type=Continuous df_shape=(4689, 71) processing_time=3.922\n", "2025-07-29T03:47:43.261472Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.61, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:47:43.288215Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.61, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:47:43.310641Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 460.61, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=4.077 project_name=WpIdentifier('scape_swift_pso_apply_cleaned')\n", "✅ 成功读取应用数据: ./scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T03:47:43.354466Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.62, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=8 input_curves=['PHI_T2_DIST', 'PHIT_NMR', 'MD', 'T2LM', 'DPHIT_NMR', 'T2_P50', 'DT2_P50', 'WELL_NO'] operation=extract_metadata output_curve_count=8 output_curves=['PHI_T2_DIST', 'PHIT_NMR', 'MD', 'T2LM', 'DPHIT_NMR', 'T2_P50', 'DT2_P50', 'WELL_NO']\n", "2025-07-29T03:47:43.393834Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.62, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T03:47:43.408157Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.62, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T03:47:43.434871Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.62, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T03:47:43.448171Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.62, 'cpu_percent': 0.0}\n"]}], "source": ["# --- 加载应用数据 ---\n", "apply_data_path = \"./scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "apply_project = reader.read(apply_data_path)\n", "print(f\"✅ 成功读取应用数据: {apply_data_path}\")\n", "\n", "apply_bundle = apply_project.get_dataset(\"swift_pso_apply_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR', 'PHI_T2_DIST'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "\n", "# --- 准备预测配置 (输出曲线名将作为facade函数的直接参数传入) ---\n", "prediction_config = SwiftPsoPredictionConfig.create_default()"]}, {"cell_type": "code", "execution_count": 6, "id": "j9k0l1m2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 2.1/5] 开始对训练集进行预测...\n", "\n", "================================================================================\n", "                            SWIFT-PSO 模型产物摘要\n", "================================================================================\n", "\n", "--- 优化参数 (Optimized Parameters) ---\n", "                 参数名      数值     线性域数值\n", "        log10_KSDR_A -1.9309    0.0117\n", "            PHIT_EXP  4.8500       N/A\n", "             RHO_NMR  4.9720       N/A\n", "      log10_KMACRO_A -0.7594    0.1740\n", "log10_T2cutoff_short  2.4471  279.9896\n", " log10_T2cutoff_long  3.0311 1074.3269\n", "              beta_1  0.4348       N/A\n", "              beta_2  0.5089       N/A\n", "           delta_MDT -0.4872       N/A\n", "\n", "--- 固定参数 (Fixed Parameters) ---\n", "       参数名     数值 线性域数值\n", "  T2LM_EXP 2.0000   N/A\n", "  KMACRO_B 2.0000   N/A\n", "Vmacro_min 0.0100   N/A\n", "\n", "--- 上下文 (Context) ---\n", "  - t2_p50_ref: 309.2900\n", "  - phit_nmr_ref: 0.0683\n", "\n", "================================================================================\n", "2025-07-29T03:47:43.581454Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} data_rows=408 operation=swift_pso_prediction_step run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:43.608160Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T03:47:43.621540Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:47:43.634860Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} data_rows=408 operation=swift_pso_prediction\n", "2025-07-29T03:47:43.648202Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} n_bins=64 n_depths=408 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.12447955000000001) v_meso_range=(6.999999999999999e-08, 0.13332976) v_micro_range=(0.00457161, 0.10102376999999998)\n", "2025-07-29T03:47:43.688164Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(1.760176903953391e-08, 127.25875518705665) result_rows=408\n", "2025-07-29T03:47:43.701619Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:47:43.729992Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:47:43.754552Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:47:43.767738Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(408,) operation=add_1d_curve\n", "2025-07-29T03:47:43.795418Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:47:43.808063Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:43.848412Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=408\n", "✅ 训练集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n", "🚀 [Step 2.2/5] 开始对应用集进行预测...\n", "2025-07-29T03:47:43.861607Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction_step run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:43.888421Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T03:47:43.915021Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:47:43.928356Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction\n", "2025-07-29T03:47:43.949764Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 467.96, 'cpu_percent': 0.0} n_bins=64 n_depths=4689 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.12447955000000001) v_meso_range=(6.999999999999999e-08, 0.13380256) v_micro_range=(0.0018771999999999999, 0.28068542999999996)\n", "2025-07-29T03:47:43.981189Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(2.8206304341590035e-12, 14.976837630081366) result_rows=4689\n", "2025-07-29T03:47:44.002637Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:47:44.021689Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:47:44.048311Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:47:44.075099Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T03:47:44.101815Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.66, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T03:47:44.155382Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.7, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:47:44.181278Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.7, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=4689\n", "2025-07-29T03:47:44.209724Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.7, 'cpu_percent': 0.0} copy_data=False new_dataset_name=train_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:47:44.221775Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T03:47:44.248448Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=train_apply dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:47:44.261835Z [info     ] 成功添加 'train_apply' (WpDiscreteDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:47:44.285406Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} copy_data=False new_dataset_name=pred_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:47:44.315270Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:47:44.341796Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=pred_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T03:47:44.366631Z [info     ] 成功添加 'pred_apply' (WpContinuousDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T03:47:44.381800Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} dataset_count=2 file_path=output01\\swift_pso_run_20250729_114541\\swift_pso_apply_result.wp.xlsx project_name=WpIdentifier('swift_pso_apply_result') save_head_info=True save_well_map=True\n", "2025-07-29T03:47:44.408480Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.73, 'cpu_percent': 0.0} curve_count=15 dataset_name=WpIdentifier('train_apply') dataset_type=Point df_shape=(408, 78)\n", "2025-07-29T03:47:44.648694Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.99, 'cpu_percent': 0.0} dataset_name=WpIdentifier('train_apply') processing_time=0.24\n", "2025-07-29T03:47:44.675396Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.99, 'cpu_percent': 0.0} curve_count=12 dataset_name=WpIdentifier('pred_apply') dataset_type=Continuous df_shape=(4689, 75)\n", "2025-07-29T03:47:46.222706Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 557.02, 'cpu_percent': 0.0} dataset_name=WpIdentifier('pred_apply') processing_time=1.547\n", "2025-07-29T03:47:46.249315Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.38, 'cpu_percent': 0.0} dataset_count=2 head_info=False total_sheets=2 well_map=False\n", "2025-07-29T03:47:46.275860Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.39, 'cpu_percent': 0.0}\n", "2025-07-29T03:47:46.289441Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 554.39, 'cpu_percent': 0.0} workbook_sheets=2\n", "2025-07-29T03:48:19.808294Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.09, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:19.821459Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.11, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:26.077548Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.48, 'cpu_percent': 0.0} file_path=output01\\swift_pso_run_20250729_114541\\swift_pso_apply_result.wp.xlsx processing_time=41.696 project_name=WpIdentifier('swift_pso_apply_result')\n", "✅ 应用集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n"]}], "source": ["# --- 对训练集进行预测 ---\n", "print(\"🚀 [Step 2.1/5] 开始对训练集进行预测...\")\n", "# 1. 实例化产物处理器\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "# 2. 从RunContext加载上一步训练产出的模型参数\n", "model_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.FINAL_PARAMETERS)\n", "model_assets = handler.load_parameters(model_params_path)\n", "handler.print_model_assets_human_readable(model_assets)\n", "\n", "# 3. 调用预测步骤，并传入加载的模型\n", "train_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets,  # 传入加载的模型\n", "    prediction_bundle=train_bundle, # 使用训练数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu' # 预测通常在CPU上进行\n", ")\n", "\n", "# 获取带预测结果的bundle，以备后续步骤使用\n", "train_bundle_with_pred = train_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 训练集预测完成！新增曲线: {train_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")\n", "\n", "# --- 对应用集进行预测 ---\n", "print(\"🚀 [Step 2.2/5] 开始对应用集进行预测...\")\n", "# 模型参数只需加载一次，可复用\n", "apply_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets, # 传入加载的模型\n", "    prediction_bundle=apply_bundle, # 使用应用数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu'\n", ")\n", "\n", "temp_project = WpWellProject(name=\"swift_pso_apply_result\")\n", "temp_project.add_dataframe_bundle(\"train_apply\",train_bundle)\n", "temp_project.add_dataframe_bundle(\"pred_apply\",apply_bundle)\n", "\n", "writer = WpExcelWriter()\n", "apply_result_path = output_dir / run_dir_name / \"swift_pso_apply_result.wp.xlsx\"\n", "writer.write(temp_project, apply_result_path, apply_formatting=True)\n", "\n", "apply_bundle_with_pred = apply_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 应用集预测完成！新增曲线: {apply_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")"]}, {"cell_type": "markdown", "id": "1af960c4", "metadata": {}, "source": ["## 训练集上渗透率交会图"]}, {"cell_type": "code", "execution_count": 7, "id": "e07ee165", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:48:26.183878Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.48, 'cpu_percent': 0.0} step_name=train_all_perm_corr_analysis\n", "2025-07-29T03:48:26.199523Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.49, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:26.212160Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.49, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:48:26.250559Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.69, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:26.277202Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.69, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:26.299855Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.69, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:26.344359Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 606.69, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:26.397315Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.07, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:26.422006Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_114541\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 607.12, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:28.038337Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.63, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:48:28.083623Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.63, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:28.112866Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.63, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:48:28.131597Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.66, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:28.144985Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.66, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:28.171362Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:28.198291Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.67, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:28.237324Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.73, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:28.251673Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_114541\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 613.8, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:29.759056Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.06, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:48:29.785585Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:29.815342Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.06, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T03:48:29.837816Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.06, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:29.868007Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.06, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:29.887473Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:29.913256Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:29.945621Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:29.972272Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_114541\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 622.18, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:31.399632Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.72, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T03:48:31.439538Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.73, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:31.465804Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.73, 'cpu_percent': 0.0}\n", "  - TRAIN_ALL 模型验证完成。\n", "\n", "2025-07-29T03:48:31.492950Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.74, 'cpu_percent': 0.0} step_name=train_pz_perm_corr_analysis\n", "2025-07-29T03:48:31.506249Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.75, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:31.511255Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.75, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:48:31.533101Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.92, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:31.559705Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.93, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:31.572904Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.94, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:31.611999Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 631.96, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:31.640234Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 632.31, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:32.026531Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_114541\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 492.13, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:33.471484Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.09, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:48:33.503648Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.09, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:33.526494Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.09, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:48:33.546886Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.11, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:33.573657Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.11, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:33.597499Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:33.613728Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.11, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:33.654812Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.11, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:33.680435Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_114541\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 498.11, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:35.054468Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:48:35.094424Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:35.121084Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T03:48:35.147663Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:35.173720Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:35.187046Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:35.233288Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:35.267625Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:35.294333Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_114541\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 504.61, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:36.748385Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.29, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T03:48:36.788216Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:36.830579Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.29, 'cpu_percent': 0.0}\n", "  - TRAIN_PZ 模型验证完成。\n", "\n", "2025-07-29T03:48:36.840677Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.29, 'cpu_percent': 0.0} step_name=train_core_perm_corr_analysis\n", "2025-07-29T03:48:36.854283Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.29, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:36.872547Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.29, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:48:36.894834Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.28, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:36.921129Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.28, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:36.939221Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:36.961640Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:37.001599Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.47, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:37.031761Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_114541\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 511.47, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:38.589067Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:48:38.630604Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:38.655582Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:48:38.682379Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:38.708888Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:38.722207Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:38.748485Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:38.789108Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:38.815697Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_114541\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.86, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:40.256378Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:48:40.296288Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:40.323290Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE 模型验证完成。\n", "\n", "2025-07-29T03:48:40.335878Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} step_name=train_core_pz_perm_corr_analysis\n", "2025-07-29T03:48:40.349066Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:40.351475Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:48:40.375766Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:40.410874Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:40.429580Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:40.456299Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:40.508616Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:40.522972Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_114541\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 516.25, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:41.936816Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.66, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T03:48:41.963522Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:41.989689Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.66, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:48:42.010022Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.66, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:42.042448Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.66, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:42.058352Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:42.083188Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.66, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:42.136496Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.68, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:48:42.170327Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_114541\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 522.67, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:43.684445Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.74, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_114541\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T03:48:43.724262Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.74, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:43.750859Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 527.74, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE_PZ 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"train_all\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.4231290700186872,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 22.3% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 45.1% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6097684008545466,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 14.4% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 27.2% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6941033039469707,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 25.8% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 58.1% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.40927675518472584,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 37.8% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 72.5% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.651111881589031,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 22.2% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 45.7% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.5905693867473459,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 38.1% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 71.4% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.3852259283643704,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 24.1% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 46.2% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6222081901236391,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 11.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 27.9% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.3852259283643704,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 24.1% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 46.2% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.6222081901236391,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 11.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 27.9% <= 90%\\u3002\"\n", "    }\n", "  }\n", "}\n"]}], "source": ["models_to_perm_corr = {\n", "    \"train_all\": train_label_all_bundle,\n", "    \"train_pz\": train_label_pz_bundle,\n", "    \"train_core\": train_label_core_bundle,\n", "    \"train_core_pz\": train_label_core_bundle,\n", "}\n", "\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_results = {}\n", "for model_prefix, label_bundle in models_to_perm_corr.items():\n", "    perm_corr_config = PermCorrelationConfig()\n", "    perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": f\"Core Perm vs. Predicted Perm ({model_prefix.upper()})\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", "    )\n", "    perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=train_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=label_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=model_prefix\n", "    )\n", "    perm_corr_results[model_prefix] = perm_corr_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(perm_corr_results, indent=2))"]}, {"cell_type": "markdown", "id": "n3o4p5q6", "metadata": {}, "source": ["## 5. 步骤三：t-SNE 可视化\n", "\n", "调用 `run_tsne_visualization_step` 对训练过程中的参数演化进行可视化。此步骤会自动消费训练步骤产出的 `ALL_OPTIMIZED_PARAMETERS` 产物。\n", "\n", "我们采用 **Get -> Modify -> Pass** 模式来定制图表：\n", "1.  **Get**: 从全局注册表 `plot_registry` 获取一个默认的 `PlotProfile` 模板。\n", "2.  **Modify**: 在运行时动态修改模板的属性（如标题）。\n", "3.  **Pass**: 将修改后的 `PlotProfile` 对象传入 `facade` 函数。"]}, {"cell_type": "code", "execution_count": 8, "id": "r7s8t9u0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 3/5] 开始执行 t-SNE 可视化...\n", "2025-07-29T03:48:44.471164Z [info     ] 开始t-SNE可视化步骤                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 496.05, 'cpu_percent': 0.0} custom_profiles=['swift_pso.tsne_convergence', 'swift_pso.tsne_cluster_analysis'] operation=tsne_visualization_step run_id=20250729-034541-3727a80a source_data_rows=814\n", "2025-07-29T03:48:44.511145Z [info     ] 开始执行收敛轨迹分析                     [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 496.05, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T03:48:44.535214Z [info     ] 开始t-SNE降维计算                    [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 496.05, 'cpu_percent': 0.0} data_rows=814 operation=tsne_computation perplexity=15\n", "2025-07-29T03:48:44.551225Z [info     ] 执行t-SNE降维                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 496.07, 'cpu_percent': 0.0} init=pca learning_rate=200.0 max_iter=2000 n_components=2 operation=tsne_computation perplexity=15 random_state=42 verbose=0\n", "2025-07-29T03:48:54.741551Z [info     ] 在原始高维空间上执行K-means聚类            [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 500.84, 'cpu_percent': 0.0} n_clusters=4 operation=tsne_computation\n", "2025-07-29T03:48:54.955095Z [info     ] 聚类轮廓系数 (Silhouette Score): 0.2129 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 501.24, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:54.980355Z [info     ] t-SNE降维和聚类计算完成                 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 501.24, 'cpu_percent': 0.0} operation=tsne_computation result_rows=814 tsne_x_range=(-785.0850219726562, 745.7468872070312) tsne_y_range=(-729.0791015625, 782.95263671875)\n", "2025-07-29T03:48:55.021674Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.14, 'cpu_percent': 0.0} description=t-SNE收敛轨迹的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:55.048361Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.14, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization\\tsne_convergence_trajectory_profile.json profile_name=swift_pso.tsne_convergence\n", "2025-07-29T03:48:55.060586Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.14, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization\\tsne_convergence_trajectory_plot.png profile_name=swift_pso.tsne_convergence snapshot_path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization\\tsne_convergence_trajectory_data.csv\n", "2025-07-29T03:48:55.101761Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.18, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_convergence\n", "2025-07-29T03:48:55.181837Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_convergence_trajectory_plot base_path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 502.42, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:56.955880Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 569.62, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T03:48:56.982461Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 569.62, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T03:48:57.037604Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 569.62, 'cpu_percent': 0.0} description=SWIFT-PSO参数演化轨迹的t-SNE可视化图表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:57.062699Z [info     ] 开始执行最终收敛点聚类分析                  [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 569.62, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T03:48:57.075869Z [info     ] 开始最终收敛点的聚类分析                   [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 569.62, 'cpu_percent': 0.0} operation=cluster_analysis_computation total_points=814\n", "2025-07-29T03:48:57.089310Z [info     ] 筛选出 60 个最终收敛点进行分析。             [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 569.62, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:57.596200Z [info     ] 执行聚类分析，方法: kmeans              [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 569.98, 'cpu_percent': 0.0} operation=cluster_analysis_computation\n", "2025-07-29T03:48:57.662114Z [info     ] 聚类分析计算完成。                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.36, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:57.689472Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.43, 'cpu_percent': 0.0} description=最终收敛点聚类分析的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:57.734179Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.43, 'cpu_percent': 0.0} description=聚类分析的总体量化指标报告，包括轮廓系数、簇心等。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:57.755932Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_statistics artifact_path=swift_pso_visualization\\tsne_cluster_statistics_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.43, 'cpu_percent': 0.0} description=每个簇内部所有参数的详细统计信息（均值、标准差等）。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:57.790098Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_summary_table artifact_path=swift_pso_visualization\\tsne_cluster_summary_table.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.46, 'cpu_percent': 0.0} description=各簇参数均值和标准差的对比摘要表，便于物理意义解释。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:57.818781Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.46, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization\\tsne_cluster_analysis_profile.json profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T03:48:57.822379Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.46, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization\\tsne_cluster_analysis_plot.png profile_name=swift_pso.tsne_cluster_analysis snapshot_path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization\\tsne_cluster_analysis_data.csv\n", "2025-07-29T03:48:57.876005Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 570.65, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T03:48:57.942918Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_cluster_analysis_plot base_path=output01\\swift_pso_run_20250729_114541\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 571.03, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:48:59.396863Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T03:48:59.435604Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_114541\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T03:48:59.450074Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} description=SWIFT-PSO最终收敛点的t-SNE聚类分析图表（带凸包）。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:48:59.476633Z [info     ] t-SNE可视化步骤完成                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "✅ [Step 3/5] t-SNE 可视化完成！结果: {'status': 'completed', 'clusters_found': 4}\n"]}], "source": ["# 1. 创建可视化配置 (保持不变)\n", "tsne_config = TsneVisualConfig(\n", "    perplexity=15,\n", "    n_iter=2000,\n", "    random_state=42,\n", "    cluster_method=\"kmeans\",\n", "    n_clusters=4,\n", "    dbscan_eps=1,\n", "    dbscan_min_samples=5\n", ")\n", "\n", "# 2. 获取并修改两个绘图配置，然后打包成字典\n", "# 2.1 收敛轨迹图的配置\n", "trajectory_profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Parameter Evolution (Trajectory)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.2 聚类分析图的配置\n", "cluster_profile = plot_registry.get(TsnePlotProfiles.CLUSTER_ANALYSIS).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Final Parameters (Cluster Analysis)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.3 打包成字典\n", "custom_plot_profiles = {\n", "    TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value: trajectory_profile,\n", "    TsnePlotProfiles.CLUSTER_ANALYSIS.value: cluster_profile\n", "}\n", "\n", "# 3. 执行可视化步骤 (加载数据的部分保持不变)\n", "print(\"🚀 [Step 3/5] 开始执行 t-SNE 可视化...\")\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "tsne_source_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)\n", "tsne_source_data = handler.load_dataframe(tsne_source_path)\n", "\n", "# 3.1 更新函数调用\n", "tsne_result = run_tsne_visualization_step(\n", "    config=tsne_config,\n", "    ctx=run_context,\n", "    tsne_source_data=tsne_source_data,\n", "    plot_profiles=custom_plot_profiles # <--- 修改此处\n", ")\n", "\n", "# 4. 更新打印的产物信息\n", "print(f\"✅ [Step 3/5] t-SNE 可视化完成！结果: {tsne_result}\")"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["## 6. 步骤四：PLT 盲井检验\n", "\n", "调用 `run_plt_analysis_step` 对模型的预测结果进行PLT盲井检验。此步骤会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物（包含在 `apply_bundle_with_pred` 中）。\n", "\n", "我们为不同的图表（贡献率交会图、捕获率曲线、洛伦兹曲线）分别获取并定制 `PlotProfile`，然后将它们打包成一个字典传入。"]}, {"cell_type": "code", "execution_count": 9, "id": "w5x6y7z8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:48:59.570105Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx\n", "2025-07-29T03:48:59.610158Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T03:48:59.650111Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} project_name=scape_plt_val\n", "2025-07-29T03:48:59.663421Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_plt_val\n", "2025-07-29T03:48:59.676813Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T03:48:59.711559Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:48:59.728034Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:48:59.746444Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T03:48:59.770174Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T03:48:59.802283Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} shape=(14, 4) sheet_name=PLT\n", "2025-07-29T03:48:59.809669Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T03:48:59.828393Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(14, 4) processing_time=0.082\n", "2025-07-29T03:48:59.851144Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:48:59.876844Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:48:59.890017Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx processing_time=0.32 project_name=WpIdentifier('scape_plt_val')\n", "2025-07-29T03:48:59.909909Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} extracted_curve_count=1 operation=extract_curve_dataframe_bundle\n", "2025-07-29T03:48:59.930176Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['WELL_NO', 'MD_Bottom', 'MD_Top', 'QOZI'] operation=extract_metadata output_curve_count=4 output_curves=['WELL_NO', 'MD_Bottom', 'MD_Top', 'QOZI']\n", "✅ 成功读取PLT验证数据: ./scape_plt_val.wp.xlsx\n", "🚀 [Step 4/5] 开始执行 PLT 盲井检验...\n", "2025-07-29T03:48:59.957408Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.59, 'cpu_percent': 0.0} step_name=plt_analysis\n", "2025-07-29T03:48:59.983466Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.6, 'cpu_percent': 0.0}\n", "2025-07-29T03:48:59.983466Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.6, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T03:49:00.013416Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.79, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:00.049946Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.79, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:00.085546Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.79, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:00.117156Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.79, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:00.143575Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.79, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:00.179378Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.79, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:00.209298Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.79, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:00.253556Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.8, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:00.276451Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.8, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:49:00.290296Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.8, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:00.316570Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.8, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:00.357109Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.8, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:49:00.377680Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\swift_pso_run_20250729_114541\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.8, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:49:00.646398Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.57, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T03:49:00.677286Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.57, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:00.703474Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.57, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T03:49:00.719114Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.57, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:00.756901Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.57, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:00.797230Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.57, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T03:49:00.814275Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\swift_pso_run_20250729_114541\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 641.57, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:49:01.062956Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.32, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T03:49:01.092566Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.32, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:01.117376Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.32, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:49:01.133114Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:01.143486Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.32, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:01.171750Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.32, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:49:01.194287Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\swift_pso_run_20250729_114541\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.32, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:49:01.473963Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T03:49:01.514472Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:01.548371Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T03:49:01.575486Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:01.608575Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:01.641962Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:01.672355Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:01.704354Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:01.735910Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:01.770915Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T03:49:01.811058Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:01.848305Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:49:01.872012Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:01.899513Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:01.938864Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T03:49:01.962185Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\swift_pso_run_20250729_114541\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 647.07, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:49:02.232764Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 649.88, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T03:49:02.254541Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 649.88, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:02.285383Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 649.88, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T03:49:02.304447Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 649.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:02.343479Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 649.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:02.384546Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 649.88, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T03:49:02.418714Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\swift_pso_run_20250729_114541\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 649.88, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:49:02.699791Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.04, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T03:49:02.736933Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.04, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:02.766231Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.04, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:49:02.783517Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:02.804800Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:02.840978Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.04, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T03:49:02.850887Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\swift_pso_run_20250729_114541\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.04, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T03:49:03.129988Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 655.98, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T03:49:03.158210Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 655.98, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:03.198137Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 655.98, 'cpu_percent': 0.0}\n", "✅ [Step 4/5] PLT 检验完成！结果: {'C-1': {'spearman_rho': 0.5714285714285715, 'gini_capture': 0.6364089205739811, 'gini_lorenz': -0.045037113489582836}, 'C-2': {'spearman_rho': 0.3571428571428572, 'gini_capture': -0.050207321282104145, 'gini_lorenz': 0.32061302075332354}}\n", "   - 分析报告已保存为产物: plt_analysis.reports.analyzed_layers_* \n"]}], "source": ["# --- 加载PLT验证数据 ---\n", "plt_val_path = \"./scape_plt_val.wp.xlsx\"\n", "plt_val_project = reader.read(plt_val_path)\n", "plt_val_bundle = plt_val_project.get_dataset(\"PLT\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取PLT验证数据: {plt_val_path}\")\n", "\n", "# 1. 创建PLT分析配置 (曲线名将作为facade函数的直接参数传入)\n", "plt_config = PltAnalysisConfig()\n", "\n", "# 2. 获取并修改多个绘图配置 (Get -> Modify -> Pass)\n", "contribution_profile = plot_registry.get(PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT).with_updates(\n", "    title_props={\"label\": \"Flow Contribution Crossplot\"}\n", ")\n", "\n", "capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE).with_updates(\n", "    title_props={\"label\": \"Permeability Capture Curve\"}\n", ")\n", "\n", "lorenz_profile = plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE).with_updates(\n", "    title_props={\"label\": \"Lorenz Curve Analysis\"}\n", ")\n", "\n", "plt_plot_profiles = {\n", "    PltPlotTypes.CONTRIBUTION_CROSSPLOT: contribution_profile,\n", "    PltPlotTypes.CAPTURE_CURVE: capture_profile,\n", "    PltPlotTypes.LORENZ_CURVE: lorenz_profile\n", "}\n", "\n", "# 3. 执行PLT分析步骤\n", "print(\"🚀 [Step 4/5] 开始执行 PLT 盲井检验...\")\n", "plt_result = run_plt_analysis_step(\n", "    config=plt_config,\n", "    ctx=run_context,\n", "    prediction_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    plt_bundle=plt_val_bundle,\n", "    permeability_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    flow_rate_curve=\"QOZI\",           # 数据选择器参数\n", "    plot_profiles=plt_plot_profiles\n", ")\n", "\n", "print(f\"✅ [Step 4/5] PLT 检验完成！结果: {plt_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PltAnalysisArtifacts.ANALYZED_LAYERS_TABLE_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["## 7. 步骤五：岩心井渗透率(CT)相关性分析\n", "\n", "调用 `run_perm_correlation_step` 对模型的预测结果进行岩心井检验。此步骤同样会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物。"]}, {"cell_type": "code", "execution_count": 10, "id": "e5f6g7h8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:49:03.291610Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 656.0, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx\n", "2025-07-29T03:49:03.331615Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T03:49:03.364590Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} project_name=scape_core_k_val\n", "2025-07-29T03:49:03.371527Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_core_k_val\n", "2025-07-29T03:49:03.394573Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T03:49:03.411462Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:49:03.424889Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T03:49:03.464885Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-29T03:49:03.491494Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.26, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T03:49:03.517785Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.29, 'cpu_percent': 0.0} shape=(44, 9) sheet_name=K_Val\n", "2025-07-29T03:49:03.531122Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.29, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-29T03:49:03.560640Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.29, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(44, 9) processing_time=0.108\n", "2025-07-29T03:49:03.583911Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.29, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T03:49:03.598214Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.29, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T03:49:03.611594Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.29, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val.wp.xlsx processing_time=0.32 project_name=WpIdentifier('scape_core_k_val')\n", "2025-07-29T03:49:03.638267Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.31, 'cpu_percent': 0.0} extracted_curve_count=7 operation=extract_curve_dataframe_bundle\n", "2025-07-29T03:49:03.651616Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.31, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['Lithology', 'PERM', 'PERM_LT_001_FLAG', 'SAMPLE_TYPE', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'POR', 'K_LABEL'] operation=extract_metadata output_curve_count=9 output_curves=['Lithology', 'PERM', 'PERM_LT_001_FLAG', 'SAMPLE_TYPE', 'MD', 'K_LABEL_TYPE', 'WELL_NO', 'POR', 'K_LABEL']\n", "✅ 成功读取岩心验证数据: ./scape_core_k_val.wp.xlsx\n", "🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\n", "2025-07-29T03:49:03.691619Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.32, 'cpu_percent': 0.0} step_name=ct_perm_corr_analysis\n", "2025-07-29T03:49:03.704887Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.35, 'cpu_percent': 0.0}\n", "2025-07-29T03:49:03.718242Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.35, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T03:49:03.744563Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.25, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:03.771208Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.25, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_114541\\ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:49:03.785085Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:03.821443Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:03.851734Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T03:49:03.882348Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_114541\\ct_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.25, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T03:49:05.550226Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.35, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_114541\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_114541\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T03:49:05.588878Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.35, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-034541-3727a80a\n", "2025-07-29T03:49:05.624666Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.35, 'cpu_percent': 0.0}\n", "✅ [Step 5/5] 岩心井检验完成！结果: {'T-1': {'spearman_rho': 0.5327351139784332, 'conclusion': \"Fail: 5x符合率 12.2% <= 50% (未满足井 'T-1' 的放宽标准)。\"}}\n", "   - 分析报告已保存为产物: perm_corr_analysis.plots.crossplot_* \n"]}], "source": ["# --- 加载岩心验证数据 ---\n", "core_val_path = \"./scape_core_k_val.wp.xlsx\"\n", "core_val_project = reader.read(core_val_path)\n", "core_val_bundle = core_val_project.get_dataset(\"K_Val\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取岩心验证数据: {core_val_path}\")\n", "\n", "# 1. 创建渗透率相关性分析配置\n", "perm_corr_config = PermCorrelationConfig(\n", "    relaxed_wells=[\"T-1\"]     # 对T-1井使用放宽的深度对齐标准\n", ")\n", "\n", "# 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": \"Core Perm vs. Predicted Perm\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "\n", "# 3. 执行渗透率相关性分析步骤\n", "print(\"🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\")\n", "perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=core_val_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"PERM\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=\"ct\"\n", ")\n", "\n", "print(f\"✅ [Step 5/5] 岩心井检验完成！结果: {perm_corr_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PermCorrelationArtifacts.CROSSPLOT_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "i9j0k1l2", "metadata": {}, "source": ["## 8. 总结\n", "\n", "🎉 **SWIFT-PSO Case 02 (重构版) 工作流执行完毕！**\n", "\n", "本次重构展示了如何使用新的组件化框架来构建一个清晰、可维护、可追踪的机器学习工作流。所有步骤的输入、输出和配置都得到了规范化管理。\n", "\n", "**关键亮点:**\n", "1.  **统一的运行上下文 (`RunContext`)**: 所有的步骤都在同一个 `RunContext` 中执行，确保了所有产物（模型、数据集、图表、报告）和日志都被集中管理在一个独立的运行目录中：\n", "    - **输出目录**: `./output02/swift_pso_workflow_run`\n", "2.  **清晰的步骤划分**: 每个核心任务（训练、预测、可视化、验证）都被封装成一个独立的 `run_*_step` 函数，职责明确。\n", "3.  **类型安全的配置 (Pydantic)**: 使用 `SwiftPsoTrainingConfig`, `TsneVisualConfig`, `PltAnalysisConfig` 等Pydantic模型替代了易出错的字典，提供了自动验证和清晰的文档。\n", "4.  **自动化的产物管理**: `facade` 函数内部处理了产物的保存和注册，使得工作流代码更简洁。下游步骤可以通过 `RunContext` 自动加载上游产物，无需手动传递文件路径。\n", "5.  **灵活的绘图系统 (`PlotProfile`)**: 通过 **Get -> Modify -> Pass** 模式，我们可以在不修改组件源码的情况下，轻松地定制图表的每一个细节，同时享受高质量的默认模板。"]}, {"cell_type": "markdown", "id": "j8k9l0m1", "metadata": {}, "source": ["## 9. 最终化运行\n", "\n", "在所有步骤完成后，手动调用 `finalize()` 来结束本次运行。这将确保所有日志被刷新，并且运行清单 `manifest.json` 被正确写入。"]}, {"cell_type": "code", "execution_count": 11, "id": "n2o3p4q5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T03:49:05.718870Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 509.35, 'cpu_percent': 0.0} duration_seconds=203.769 manifest_path=output01\\swift_pso_run_20250729_114541\\manifest.json operation=finalize run_id=20250729-034541-3727a80a status=RUNNING\n", "✅ 运行已最终化，所有记录已保存。\n"]}], "source": ["run_context.finalize()\n", "print(\"✅ 运行已最终化，所有记录已保存。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}