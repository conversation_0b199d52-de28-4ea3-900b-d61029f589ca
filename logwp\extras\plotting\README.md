# logwp.extras.plotting - 现代化绘图配置服务

## 概述

`logwp.extras.plotting` 是SCAPE项目的现代化绘图配置系统，提供基于配置模板的绘图解决方案。该系统采用"配置即服务"的设计理念，实现了绘图逻辑与样式配置的完全解耦。

## 🎯 设计理念

### 重构背景与动机

传统的绘图系统存在以下问题：

1. **配置僵化**：`PlotStyleConfig` 类只能定义固定的绘图属性，无法灵活适应不同图表的特殊需求
2. **职责不清**：绘图美学风格与保存格式耦合，上游用户需要处理复杂的参数组合
3. **需求错配**：项目核心需求是按"绘图类型"配置，而非传递通用参数
4. **可维护性差**：新增图表类型或修改全局风格需要多处侵入式修改
5. **用户不友好**：最终用户无法在不修改代码的情况下定制图表外观

### 核心设计原则

#### 1. 配置即服务 (Configuration as a Service)

将绘图系统从简单的"参数传递"模式升级为现代化的"配置即服务"模式：

- **模板化管理**：每种绘图类型（如`capture_curve`）封装为独立、可复用的配置模板
- **中心化注册**：`PlotProfileRegistry`统一管理所有绘图模板，提供默认最佳实践
- **用户可定制**：支持从外部JSON文件加载和保存配置，无需接触代码
- **架构解耦**：上游用户与具体绘图实现完全解耦，只需关心"需要什么类型的图"

#### 2. 两级继承体系 (Two-Level Inheritance System)

为了同时满足"项目内所有图表风格统一"和"特定模块内图表风格统一"的需求：

```
全局根模板 (base)
└── 模块级基础模板 (module.base)
    └── 具体图表模板 (module.chart)
```

**全局根模板 (`base`)**：
- 定义项目级视觉识别标准（官方字体、品牌色板等）
- 是所有样式配置的"最终回退点"
- 在`logwp.extras.plotting`包中定义并注册

**模块级基础模板 (`module.base`)**：
- 定义特定功能模块内部所有图表的通用风格
- 继承自全局根模板，可覆盖全局设置
- 在具体模块中定义（如`validation.base`、`swift_pso.base`）

**具体图表模板 (`module.chart`)**：
- 定义特定图表的专有样式和配置
- 继承自对应的模块级基础模板
- 包含图表特有的绘图元素配置

#### 3. 层叠合并逻辑 (Cascade Merging)

当用户请求具体模板时（如`registry.get("validation.contribution_crossplot")`），系统执行三级层叠合并：

1. **起始点**：创建全局根模板的深拷贝
2. **第一层合并**：查找并合并模块级基础模板（`validation.base`）
3. **第二层合并**：合并用户请求的特定模板
4. **返回结果**：经过三级配置合并的完整`PlotProfile`对象

**深度合并策略**：对于字典类型属性（如`rc_params`、`artist_props`），采用深度合并而非浅合并，确保配置正确组合。

### 软件设计模式

#### 注册表模式 (Registry Pattern)
- `PlotProfileRegistry`作为单例，自动收集和分发绘图配置
- 支持运行时注册、查询和持久化操作
- 提供配置的生命周期管理

#### 数据驱动配置 (Data-Driven Configuration)
- 绘图风格由可序列化的数据对象（`PlotProfile`）驱动
- 配置与代码逻辑完全分离
- 支持JSON格式的外部配置文件

#### 关注点分离 (Separation of Concerns)
- **开发者职责**：定义和注册效果良好的默认`PlotProfile`
- **使用者职责**：通过外部JSON文件定制图表外观
- **系统职责**：提供配置管理、继承合并、样式应用等服务

### 架构优势

- **高内聚低耦合**：绘图配置逻辑高度内聚到`logwp.extras.plotting`包中
- **最大化代码复用**：全局配置只需定义一次，模块级通用配置也只需定义一次
- **分层级一致性**：既保证项目视觉风格统一，也保证特定模块内部图表风格统一
- **极高可维护性**：
  - 修改全局字体只需编辑全局`base`模板
  - 修改模块图表尺寸只需编辑模块级`base`模板
  - 修改单个图表只需编辑其特定模板
- **清晰职责边界**：配置职责清晰划分到全局、模块、具体图表三个层级

## 📁 包结构

```
logwp/extras/plotting/
├── __init__.py          # 公共API导出
├── profiles.py          # 核心数据模型 (PlotProfile, SaveConfig)
├── registry.py          # 配置注册表和继承体系
├── styler.py           # 样式应用工具函数
├── saver.py            # 图像保存工具函数
├── exceptions.py       # 专属异常类
└── README.md           # 本文档
```

## 🚀 快速开始

### 基本使用

```python
import matplotlib.pyplot as plt
from logwp.extras.plotting import registry, apply_profile, save_figure

# 1. 获取预定义配置
profile = registry.get("validation.contribution_crossplot")

# 2. 创建图表并应用配置
fig, ax = plt.subplots()
apply_profile(ax, profile)

# 3. 绘制数据
ax.scatter(x_data, y_data, **profile.artist_props.get("scatter", {}))
ax.plot([0, 1], [0, 1], **profile.artist_props.get("reference_line", {}))

# 4. 设置标题和标签
ax.set_title("Contribution Cross-plot")
ax.set_xlabel(profile.label_props.get("xlabel", "X"))
ax.set_ylabel(profile.label_props.get("ylabel", "Y"))

# 5. 保存图表（如果配置了保存设置）
if profile.save_config:
    save_figure(fig, profile.save_config, "output", "crossplot")
```

### 自定义配置

#### 方法1: 从零创建 (适用于完全独立的图表)
```python
from logwp.extras.plotting import PlotProfile, SaveConfig

# 创建自定义配置
custom_profile = PlotProfile(
    name="my_custom_plot",
    figure_props={"figsize": (10, 8)},
    title_props={"fontsize": 16, "fontweight": "bold"},
    artist_props={"scatter": {"s": 80, "alpha": 0.8, "color": "blue"}},
    save_config=SaveConfig(format=["png", "svg"], dpi=600)
)

# 注册自定义配置
registry.register(custom_profile)
```

#### 方法2: 基于模板修改 (推荐)
```python
from logwp.extras.plotting import registry

# 1. 获取一个基础模板
original = registry.get("validation.contribution_crossplot")

# 2. 使用 .with_updates() 创建一个修改后的副本
updated_profile = original.with_updates(
   title_props={"label": "My Custom Title", "fontsize": 14},
   artist_props={"scatter": {"s": 60, "alpha": 0.7}},
   save_config={"format": "pdf"}
)

# 3. 现在可以使用 updated_profile 进行绘图
# apply_profile(ax, updated_profile)

```

## 🏗️ 核心组件

### PlotProfile - 绘图配置模板

`PlotProfile` 是绘图配置的核心数据模型，采用分层设计理念，将绘图配置分为不同的职责层次：

```python
@dataclass
class PlotProfile:
    name: str                           # 配置名称
    rc_params: Dict[str, Any]           # matplotlib全局参数
    figure_props: Dict[str, Any]        # 图表属性（尺寸、DPI等）
    title_props: Dict[str, Any]         # 标题属性
    label_props: Dict[str, Any]         # 标签属性
    artist_props: Dict[str, Any]        # 绘图元素属性（线条、散点等）
    save_config: Optional[SaveConfig]   # 保存配置
```

#### 属性详细说明

**`rc_params`** - 全局样式主题
- 存放直接传递给`matplotlib.rcParams`的配置参数
- 负责设定全局样式和默认值，就像图表的"皮肤"或"主题"
- 在绘图开始时应用一次，为所有后续元素设定基调
- 包含：`font.*`（字体属性）、`axes.*`（坐标轴样式）、`lines.*`（线条默认样式）等

**`figure_props`** - 画布规格设置
- 存放直接传递给`matplotlib.pyplot.figure()`的关键字参数
- 控制整个图表画布（Figure）级别的属性
- 如果说`rc_params`是"全局主题"，那么`figure_props`就是"画纸"的规格
- 包含：`figsize`（图表尺寸）、`dpi`（分辨率）、`layout`（布局方式）等

**`title_props`** - 标题精细控制
- 存放传递给`matplotlib.axes.Axes.set_title()`的关键字参数
- 用来精细雕琢图上最显眼的元素——图表标题
- 包含：`label`（标题文本）、`fontsize`、`fontweight`、`color`、`pad`等

**`label_props`** - 坐标轴标签配置
- 存放传递给`set_xlabel()`和`set_ylabel()`的关键字参数
- 专注于图表中最基础的说明性文字：坐标轴标签
- 包含：`xlabel`、`ylabel`、`fontsize`、`color`、`labelpad`等

**`artist_props`** - 数据绘制核心
- 存放传递给具体绘图函数（`ax.plot()`、`ax.scatter()`等）的关键字参数
- 名字来源于Matplotlib的核心概念——Artist（图上所有可见元素）
- 直接控制数据如何被画出来的核心画笔
- 内容完全取决于图表类型：散点图、折线图、柱状图等各有不同配置

**`save_config`** - 保存配置
- 可选的图像保存配置，控制输出格式和质量
- 支持多格式同时保存、自定义分辨率等

### PlotProfileRegistry - 配置注册表

`PlotProfileRegistry` 管理所有绘图配置模板，支持两级继承体系：

```
全局根模板 (base)
└── 模块级基础模板 (module.base)
    └── 具体图表模板 (module.chart)
```

#### 主要方法

```python
# 注册基础模板
registry.register_base(base_profile)

# 注册具体配置
registry.register(chart_profile)

# 获取配置（自动继承合并）
profile = registry.get("validation.contribution_crossplot")

# 列出所有配置
all_profiles = registry.list_profiles()
base_profiles = registry.list_base_profiles()

# 从目录加载配置
registry.load_from_dir("config/plot_profiles")

# 保存配置到目录
registry.save_to_dir("config/plot_profiles")
```

### SaveConfig - 保存配置

```python
@dataclass
class SaveConfig:
    format: Union[str, List[str]]       # 保存格式
    dpi: Optional[int] = None           # 分辨率
    width: Optional[float] = None       # 宽度（英寸）
    height: Optional[float] = None      # 高度（英寸）
    transparent: bool = False           # 透明背景
    bbox_inches: str = "tight"          # 边界框设置
    save_kwargs: Dict[str, Any] = None  # 其他保存参数
```

## 📋 配置模板示例

### 基础模板

```json
{
  "name": "validation.base",
  "rc_params": {
    "font.family": "Arial",
    "font.size": 11,
    "axes.grid": true,
    "grid.alpha": 0.3
  },
  "figure_props": {
    "figsize": [7, 7],
    "dpi": 150,
    "layout": "constrained"
  },
  "save_config": {
    "format": ["png", "svg"],
    "dpi": 300,
    "transparent": false,
    "bbox_inches": "tight"
  }
}
```

### 具体图表模板

```json
{
  "name": "validation.contribution_crossplot",
  "title_props": {
    "fontsize": 14,
    "fontweight": "bold",
    "pad": 15
  },
  "label_props": {
    "xlabel": "Predicted Relative Contribution (R_pred)",
    "ylabel": "PLT Relative Contribution (R_PLT)",
    "fontsize": 12
  },
  "artist_props": {
    "scatter": {
      "s": 60,
      "alpha": 0.7,
      "edgecolor": "white",
      "linewidth": 0.5
    },
    "reference_line": {
      "color": "red",
      "linestyle": "--",
      "linewidth": 1.5,
      "label": "1:1 Line"
    }
  }
}
```

## 🔧 工具函数

### apply_profile - 样式应用

```python
def apply_profile(ax: plt.Axes, profile: PlotProfile) -> None:
    """将PlotProfile配置应用到matplotlib Axes对象"""
```

### save_figure - 图像保存

```python
def save_figure(
    figure: plt.Figure,
    save_config: SaveConfig,
    base_path: Union[str, Path],
    base_name: str
) -> List[Path]:
    """根据SaveConfig配置保存图像"""
```

## 📊 已注册的配置模板

### PLT检验模块 (validation.*)

- `validation.base` - PLT检验基础模板
- `validation.contribution_crossplot` - 贡献率交会图
- `validation.capture_curve` - 捕获曲线图
- `validation.lorenz_curve` - 洛伦兹曲线图

### 渗透率相关性模块 (perm_corr.*)

- `perm_corr.base` - 渗透率相关性基础模板
- `perm_corr.permeability_crossplot` - 渗透率交会图

### SWIFT-PSO模块 (swift_pso.*)

- `swift_pso.base` - SWIFT-PSO基础模板
- `swift_pso.tsne_convergence` - t-SNE收敛轨迹图

## 🛠️ 高级用法

### 从JSON文件加载配置

```python
# 加载单个配置文件
profile = PlotProfile.from_json("my_config.json")
registry.register(profile)

# 加载整个目录的配置
registry.load_from_dir("config/plot_profiles")
```

### 导出配置到文件

```python
# 导出所有配置到目录
registry.save_to_dir("config/plot_profiles")

# 导出单个配置
profile = registry.get("validation.contribution_crossplot")
profile.to_json("my_crossplot_config.json")
```

### 配置继承和合并

#### 继承链可视化

```plaintext
[全局根模板: "base"]
  rc_params: {"font.family": "Arial", "font.size": 11}
  figure_props: {"dpi": 150}
  │
  └─> [模块级基础模板: "validation.base"] (继承自 "base")
        rc_params: {"font.family": "Arial", "font.size": 11, "axes.grid": True}
        figure_props: {"figsize": (7, 7), "dpi": 150}  ← figsize被添加
        │
        └─> [具体模板: "validation.contribution_crossplot"] (继承自 "validation.base")
              rc_params: {"font.family": "Arial", "font.size": 11, "axes.grid": True}
              figure_props: {"figsize": (7, 7), "dpi": 150}  ← 继承自模块级
              title_props: {"fontsize": 14, "fontweight": "bold"}  ← 新增属性
              artist_props: {"scatter": {...}, "reference_line": {...}}  ← 新增属性
```

#### 代码示例

```python
# 1. 注册全局根模板
global_base = PlotProfile(
    name="base",
    rc_params={"font.family": "Arial", "font.size": 11},
    figure_props={"dpi": 150}
)
registry.register_base(global_base)

# 2. 注册模块级基础模板
module_base = PlotProfile(
    name="validation.base",
    rc_params={"axes.grid": True},  # 会与全局base深度合并
    figure_props={"figsize": (7, 7)}  # 会与全局base深度合并
)
registry.register_base(module_base)

# 3. 注册具体图表模板
chart_profile = PlotProfile(
    name="validation.contribution_crossplot",
    title_props={"fontsize": 14, "fontweight": "bold"},
    artist_props={"scatter": {"s": 60, "alpha": 0.7}}
)
registry.register(chart_profile)

# 4. 获取时自动执行三级合并
merged = registry.get("validation.contribution_crossplot")
# merged 包含了所有三级的配置，经过深度合并
print(merged.rc_params)  # {"font.family": "Arial", "font.size": 11, "axes.grid": True}
print(merged.figure_props)  # {"figsize": (7, 7), "dpi": 150}
```

#### 深度合并机制

对于字典类型属性，系统采用深度合并而非浅合并：

```python
# 浅合并（错误方式）
base_rc = {"font.size": 10}
module_rc = {"axes.grid": True}
result = base_rc.copy()
result.update(module_rc)  # 只会得到 {"axes.grid": True}

# 深度合并（正确方式）
result = deep_merge(base_rc, module_rc)  # 得到 {"font.size": 10, "axes.grid": True}
```

## 🚨 异常处理

```python
from logwp.extras.plotting.exceptions import (
    WpPlottingError,
    ProfileNotFoundError,
    ProfileRegistrationError,
    StyleApplicationError,
    ProfileIOError
)

try:
    profile = registry.get("nonexistent_profile")
except ProfileNotFoundError as e:
    print(f"配置未找到: {e}")

try:
    apply_profile(ax, profile)
except StyleApplicationError as e:
    print(f"样式应用失败: {e}")
```

## 📚 最佳实践

1. **使用常量定义配置名称**：避免硬编码字符串
2. **优先使用预定义配置**：减少重复配置
3. **合理使用继承**：基础模板定义通用样式，具体配置定义特殊样式
4. **外部化配置文件**：将自定义配置保存为JSON文件
5. **测试配置有效性**：确保配置能正确应用到matplotlib

## 🔗 相关文档

- [绘图系统全新重构设计文档](../../docs/DDS/logwp_extras_plotting及相关全新重构.md)
- [SCAPE编码与通用规范](../../docs/coding/SCAPE_CCG_编码与通用规范.md)
- [配置导出脚本](../../scripts/plotting/export_profiles.py)

## 💡 实际应用示例

### 在SCAPE模块中的使用

#### PLT盲井检验

```python
from scape.core.validation.plt_analyzer import (
    run_plt_blind_well_analysis,
    PltPlotProfiles,
    get_plt_profile
)

# 使用默认配置
results = run_plt_blind_well_analysis(
    prediction_bundle, "K_PRED",
    plt_bundle, "QOZI"
)

# 使用自定义配置
profiles = {
    "contribution": get_plt_profile(PltPlotProfiles.CONTRIBUTION_CROSSPLOT),
    "capture": get_plt_profile(PltPlotProfiles.CAPTURE_CURVE),
    "lorenz": get_plt_profile(PltPlotProfiles.LORENZ_CURVE)
}
results = run_plt_blind_well_analysis(
    prediction_bundle, "K_PRED",
    plt_bundle, "QOZI",
    plot_profiles=profiles
)
```

#### 渗透率相关性分析

```python
from scape.core.validation.perm_correlation_analyzer import (
    run_perm_correlation_analysis,
    PermCorrPlotProfiles,
    get_perm_corr_profile
)

# 使用高质量配置
profiles = {
    "crossplot": get_perm_corr_profile(PermCorrPlotProfiles.PERMEABILITY_CROSSPLOT)
}
results = run_perm_correlation_analysis(
    left_bundle, "K_PRED",
    right_bundle, "K_CORE",
    plot_profiles=profiles
)
```

#### t-SNE可视化

```python
from scape.core.swift_pso.visual_t_sne import (
    generate_tsne_visualization,
    TsnePlotProfiles,
    get_tsne_profile
)

# 使用收敛轨迹配置
profile = get_tsne_profile(TsnePlotProfiles.CONVERGENCE_TRAJECTORY)
generate_tsne_visualization(
    data_bundle,
    plot_profile=profile,
    save_path="output/tsne_analysis"
)
```

### 配置文件管理

#### 导出所有配置

```bash
# 使用导出脚本
python scripts/plotting/export_profiles.py

# 查看导出的配置
ls config/plot_profiles/
# base.json
# validation.contribution_crossplot.json
# perm_corr.permeability_crossplot.json
# swift_pso.tsne_convergence.json
# ...
```

#### 自定义配置工作流

```python
# 1. 复制现有配置
import shutil
shutil.copy(
    "config/plot_profiles/validation.contribution_crossplot.json",
    "config/plot_profiles/my_custom_crossplot.json"
)

# 2. 编辑JSON文件（修改颜色、尺寸等）
# 3. 在代码中加载自定义配置
from logwp.extras.plotting import PlotProfile, registry

custom_profile = PlotProfile.from_json(
    "config/plot_profiles/my_custom_crossplot.json"
)
registry.register(custom_profile)

# 4. 使用自定义配置
profile = registry.get("my_custom_crossplot")
```

## 🔍 故障排除

### 常见问题

#### Q: 配置未找到错误
```python
# 错误：ProfileNotFoundError: Profile 'my_profile' not found
# 解决：检查配置是否已注册
print(registry.list_profiles())  # 查看所有可用配置
```

#### Q: 样式应用失败
```python
# 错误：StyleApplicationError: Failed to apply style
# 解决：检查matplotlib版本兼容性和配置参数有效性
import matplotlib
print(matplotlib.__version__)  # 确保版本 >= 3.5
```

#### Q: JSON序列化错误
```python
# 错误：JSON serialization failed
# 解决：确保配置中的所有值都是JSON可序列化的
# 避免使用numpy数组、复杂对象等
```

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger("logwp.extras.plotting").setLevel(logging.DEBUG)

# 检查配置合并结果
profile = registry.get("validation.contribution_crossplot")
print(f"合并后的配置: {profile}")

# 验证配置有效性
try:
    fig, ax = plt.subplots()
    apply_profile(ax, profile)
    print("✅ 配置有效")
except Exception as e:
    print(f"❌ 配置无效: {e}")
```

## 🚀 性能优化

### 配置缓存

```python
# 配置会自动缓存，重复获取不会重新计算继承
profile1 = registry.get("validation.contribution_crossplot")  # 计算继承
profile2 = registry.get("validation.contribution_crossplot")  # 使用缓存
assert profile1 is profile2  # 同一个对象
```

### 批量样式应用

```python
# 对于多个子图，可以复用配置
fig, axes = plt.subplots(2, 2)
profile = registry.get("validation.contribution_crossplot")

for ax in axes.flat:
    apply_profile(ax, profile)  # 高效的批量应用
```

## 🔮 未来规划

### 计划中的功能

1. **主题系统**：支持深色主题、高对比度主题等
2. **动态配置**：支持运行时动态修改配置
3. **配置验证器**：更强的配置有效性检查
4. **可视化编辑器**：图形化配置编辑工具
5. **模板市场**：共享和下载配置模板

### 版本兼容性

- **当前版本**: v1.0 (稳定版)
- **向后兼容**: 保证配置文件格式向后兼容
- **升级路径**: 提供自动迁移工具

## 🤝 贡献指南

### 新增图表类型

1. 在对应模块创建`*_plot_profiles.py`文件
2. 定义配置常量类和注册函数
3. 遵循命名约定：`模块名.图表类型`
4. 提供完整的配置示例和使用文档
5. 确保配置的JSON序列化兼容性

### 代码贡献

1. 遵循[SCAPE编码规范](../../docs/coding/SCAPE_CCG_编码与通用规范.md)
2. 添加完整的类型注解和文档字符串
3. 编写相应的单元测试
4. 更新相关文档

### 配置模板贡献

1. 提供高质量的默认配置
2. 包含详细的使用说明
3. 考虑不同使用场景（科学出版、演示文稿等）
4. 确保配置的可访问性（色盲友好等）
