"""Plotting reproducibility functions for the LogScout step.

This module provides functions to regenerate plots from their corresponding
data snapshots. This allows for easy restyling or modification of plots
without re-running the entire analysis workflow.
"""

from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING

import matplotlib.pyplot as plt
import pandas as pd

from .internal import plotter

if TYPE_CHECKING:
    from logwp.extras.plotting import PlotProfile


def replot_heatmap_from_snapshot(
    snapshot_path: Path, profile: PlotProfile, title: str
) -> plt.Figure:
    """Regenerates a correlation heatmap from a data snapshot.

    Args:
        snapshot_path: Path to the correlation matrix CSV file.
        profile: The PlotProfile to apply.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object.
    """
    corr_matrix = pd.read_csv(snapshot_path, index_col=0)
    return plotter.plot_heatmap(corr_matrix, profile, title)


def replot_clustermap_from_snapshot(
    snapshot_path: Path, profile: PlotProfile, title: str
) -> plt.Figure:
    """Regenerates a correlation clustermap from a data snapshot.

    Args:
        snapshot_path: Path to the correlation matrix CSV file.
        profile: The PlotProfile to apply.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object.
    """
    corr_matrix = pd.read_csv(snapshot_path, index_col=0)
    return plotter.plot_clustermap(corr_matrix, profile, title)


def replot_pairplot_from_snapshot(
    snapshot_path: Path, features: list[str], profile: PlotProfile, title: str
) -> plt.Figure:
    """Regenerates a pairplot from a data snapshot.

    Args:
        snapshot_path: Path to the feature data CSV file.
        features: A list of feature names to include in the plot.
        profile: The PlotProfile to apply.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object.
    """
    data = pd.read_csv(snapshot_path)
    return plotter.plot_pairplot(data, features, profile, title)


def replot_target_relationship_from_snapshot(
    snapshot_path: Path, task_type: str, profile: PlotProfile, title: str
) -> plt.Figure:
    """Regenerates a feature vs. target plot from a data snapshot.

    Args:
        snapshot_path: Path to the data CSV file (containing one feature and the target).
        task_type: The task type, 'regression' or 'classification'.
        profile: The PlotProfile to apply.
        title: The title for the plot.

    Returns:
        A matplotlib Figure object.
    """
    data = pd.read_csv(snapshot_path)
    feature_col, target_col = data.columns[0], data.columns[1]

    if task_type == "regression":
        return plotter.plot_regression_target(
            data, feature_col, target_col, profile, title
        )
    elif task_type == "classification":
        return plotter.plot_classification_target(
            data, feature_col, target_col, profile, title
        )
    else:
        raise ValueError(f"Unsupported task_type for replotting: {task_type}")
