{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a9e0a2e7", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "1e86095f", "metadata": {}, "source": ["# SWIFT-PSO 案例\n", "- 固定随机种子\n", "- 新增`收敛轨迹与聚类分析图`以及各Cluster参数统计\n", "- 12个优化参数,t2lm_exp、vmacro_b、vmacro_min为固定\n", "- random_seed = 2000\n", "- t-SNE可视化最终收敛点聚类分析 (Cluster Analysis)聚类方法：kmeans"]}, {"cell_type": "markdown", "id": "4266395e", "metadata": {}, "source": ["## 1. 初始化与导入"]}, {"cell_type": "code", "execution_count": 2, "id": "3e9b170c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["init                          \n", "2025-07-29T04:18:44.855927Z [info     ] 日志系统配置完成                       [logwp.infra.logging_config] config={'level': 'INFO', 'json_format': False, 'log_to_file': False, 'log_file_path': 'logs/scape_project.log', 'include_performance_context': True, 'include_gpu_context': True} context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 128.02, 'cpu_percent': 0.0}\n", "2025-07-29T04:18:47.132551Z [info     ] 绘图配置注册表已初始化                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.45, 'cpu_percent': 0.0} operation=registry_init\n", "2025-07-29T04:18:47.164544Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.46, 'cpu_percent': 0.0} operation=register_base_profile profile_name=base\n", "2025-07-29T04:18:47.189983Z [info     ] 默认全局基础模板已注册                    [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.46, 'cpu_percent': 0.0} profile_name=base\n", "2025-07-29T04:18:47.222904Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.93, 'cpu_percent': 0.0} operation=register_base_profile profile_name=swift_pso.base\n", "2025-07-29T04:18:47.233772Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.95, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_convergence\n", "2025-07-29T04:18:47.254654Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 355.98, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T04:18:48.630540Z [info     ] 基础模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 402.87, 'cpu_percent': 0.0} operation=register_base_profile profile_name=validation.plt.base\n", "2025-07-29T04:18:48.650743Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 402.9, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T04:18:48.673629Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 402.92, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.capture_curve\n", "2025-07-29T04:18:48.697328Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 402.94, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.plt.lorenz_curve\n", "2025-07-29T04:18:48.710585Z [info     ] 配置模板已注册                        [logwp.extras.plotting.registry] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 402.96, 'cpu_percent': 0.0} operation=register_profile overwrite=False profile_name=validation.perm_corr.permeability_crossplot\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# 导入 logwp 核心库\n", "from logwp.io import WpExcelReader, WpExcelWriter\n", "from logwp.models.well_project import WpWellProject\n", "from logwp.models.datasets.bundle import WpDataFrameBundle\n", "from logwp.models.ext_attr.predefined.t2_axis_domain import T2AxisLog10\n", "from logwp.extras.tracking import RunContext\n", "from logwp.extras.plotting import registry as plot_registry\n", "\n", "# 导入 swift_pso 组件\n", "from scape.core.swift_pso import (\n", "    run_swift_pso_training_step,\n", "    run_swift_pso_prediction_step,\n", "    run_tsne_visualization_step,\n", "    SwiftPsoTrainingConfig,\n", "    SwiftPsoPredictionConfig,\n", "    TsneVisualConfig,\n", "    SwiftPsoTrainingArtifacts,\n", "    SwiftPsoPredictionArtifacts,\n", "    TsneVisualArtifacts,\n", "    TsnePlotProfiles\n", ")\n", "\n", "# 导入 validation 组件\n", "from scape.core.validation import (\n", "    run_plt_analysis_step,\n", "    run_perm_correlation_step,\n", "    PltAnalysisConfig,\n", "    PermCorrelationConfig,\n", "    PltAnalysisArtifacts,\n", "    PermCorrelationArtifacts,\n", "    PltPlotTypes,\n", "    PltAnalysisPlotProfiles,\n", "    PermCorrelationPlotProfiles\n", ")\n", "\n", "import scape.core.swift_pso.plot_profiles"]}, {"cell_type": "markdown", "id": "f519543e", "metadata": {}, "source": ["## 2. 实验设置与数据加载\n", "\n", "使用 `RunContext` 初始化一个实验，所有后续操作都将在此上下文中进行，确保所有产物和日志都保存在一个独立的运行目录中。"]}, {"cell_type": "code", "execution_count": 3, "id": "e446002f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:18:48.835297Z [info     ] RunContext initialized         [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.73, 'cpu_percent': 0.0} operation=init_context run_dir=output01\\swift_pso_run_20250729_121848 run_id=20250729-041848-e6c9a655\n", "实验运行已初始化，所有产物将保存至: F:\\X\\20250924-26.IFEDC202519960\\30.dev\\V2\\scape_project\\scape_case\\jupyter\\20_swift_pso\\case22\\output01\\swift_pso_run_20250729_121848\n", "2025-07-29T04:18:48.859318Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 403.73, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T04:18:48.883972Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.06, 'cpu_percent': 0.0} file_path=scape_swift_pso_train_cleaned.wp.xlsx file_size_mb=0.13 sheet_count=1\n", "2025-07-29T04:18:48.899747Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.07, 'cpu_percent': 0.0} project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T04:18:48.915715Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.08, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_train_cleaned\n", "2025-07-29T04:18:48.935814Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.48, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_train_cleaned dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:18:48.967264Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 404.67, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=11 well_curves=1\n", "2025-07-29T04:18:49.294877Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.23, 'cpu_percent': 0.0} shape=(297, 74) sheet_name=swift_pso_train_cleaned\n", "2025-07-29T04:18:49.326620Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.24, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_train_cleaned')\n", "2025-07-29T04:18:49.342297Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.24, 'cpu_percent': 0.0} curve_count=11 dataset_name=swift_pso_train_cleaned dataset_type=Point df_shape=(297, 74) processing_time=0.411\n", "2025-07-29T04:18:49.373781Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.24, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T04:18:49.373781Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.24, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T04:18:49.389593Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.24, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_train_cleaned.wp.xlsx processing_time=0.53 project_name=WpIdentifier('scape_swift_pso_train_cleaned')\n", "✅ 成功读取训练数据: ./scape_swift_pso_train_cleaned.wp.xlsx\n", "2025-07-29T04:18:49.421407Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.64, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=11 input_curves=['PZI', 'PHIT_NMR', 'WELL_NO', 'K_LABEL_TYPE', 'DPHIT_NMR', 'T2_P50', 'PHI_T2_DIST', 'DT2_P50', 'K_LABEL', 'MD', 'T2LM'] operation=extract_metadata output_curve_count=11 output_curves=['PZI', 'PHIT_NMR', 'WELL_NO', 'K_LABEL_TYPE', 'DPHIT_NMR', 'T2_P50', 'PHI_T2_DIST', 'DT2_P50', 'K_LABEL', 'MD', 'T2LM']\n", "2025-07-29T04:18:49.453096Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.64, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T04:18:49.468927Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.64, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T04:18:49.484623Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.64, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T04:18:49.507946Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.64, 'cpu_percent': 0.0}\n", "===train_bundle:   WELL_NO       MD  PHIT_NMR      T2LM   DT2_P50    T2_P50  DPHIT_NMR  \\\n", "0     C-1  6319.40  0.105551   785.428  0.316622   997.666   0.079668   \n", "1     C-1  6335.20  0.138267   746.054  0.111100   871.799   0.023800   \n", "2     C-1  6334.52  0.137828  3960.371  1.190176  6131.592   0.060766   \n", "3     C-1  6337.00  0.100931   527.925  0.102427   663.328   0.004202   \n", "4     C-1  6349.70  0.047791   111.798 -0.399350   203.148  -0.029715   \n", "\n", "     T2_VALUE_1    T2_VALUE_2    T2_VALUE_3  ...  T2_VALUE_58  T2_VALUE_59  \\\n", "0  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003882     0.003707   \n", "1  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.004239     0.003638   \n", "2  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.010259     0.012969   \n", "3  1.000000e-08  1.000000e-08  1.000000e-08  ...     0.003190     0.003105   \n", "4  1.506000e-05  3.392000e-05  6.404000e-05  ...     0.000000     0.000000   \n", "\n", "   T2_VALUE_60  T2_VALUE_61  T2_VALUE_62  T2_VALUE_63  T2_VALUE_64  K_LABEL  \\\n", "0     0.003506     0.003268     0.002990     0.002676     0.002339    0.888   \n", "1     0.003084     0.002577     0.002114     0.001696     0.001326    0.205   \n", "2     0.015308     0.017210     0.018582     0.019324     0.019364  153.000   \n", "3     0.003001     0.002867     0.002698     0.002490     0.002247    6.130   \n", "4     0.000000     0.000000     0.000000     0.000000     0.000000    0.263   \n", "\n", "   K_LABEL_TYPE  PZI  \n", "0          CORE    1  \n", "1           MDT    1  \n", "2          CORE    1  \n", "3          CORE    1  \n", "4          CORE    1  \n", "\n", "[5 rows x 74 columns]\n", "2025-07-29T04:18:49.543563Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.69, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL']\n", "===train_label_all_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T04:18:49.586921Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.73, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL']\n", "===train_label_pz_bundle:   WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0     C-1  6319.40    0.888         CORE    1\n", "1     C-1  6335.20    0.205          MDT    1\n", "2     C-1  6334.52  153.000         CORE    1\n", "3     C-1  6337.00    6.130         CORE    1\n", "4     C-1  6349.70    0.263         CORE    1\n", "2025-07-29T04:18:49.650230Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.78, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL']\n", "===train_label_core_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "2025-07-29T04:18:49.713592Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.78, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=5 input_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL'] operation=extract_metadata output_curve_count=5 output_curves=['PZI', 'WELL_NO', 'K_LABEL_TYPE', 'MD', 'K_LABEL']\n", "===train_label_core_pz_bundle:    WELL_NO       MD  K_LABEL K_LABEL_TYPE  PZI\n", "0      C-1  6319.40    0.888         CORE    1\n", "2      C-1  6334.52  153.000         CORE    1\n", "3      C-1  6337.00    6.130         CORE    1\n", "4      C-1  6349.70    0.263         CORE    1\n", "10     C-1  6364.25    0.018         CORE    1\n", "  - T2轴信息: T2AxisLog10(t2_start=0.1, t2_end=10000, n=64, unit='ms')\n"]}], "source": ["# --- 实验设置 ---\n", "output_dir = Path(\"./output01\")\n", "run_dir_name = RunContext.generate_timestamped_run_name(prefix=\"swift_pso_run\")\n", "run_context = RunContext(output_dir / run_dir_name,overwrite=True)\n", "print(f\"实验运行已初始化，所有产物将保存至: {run_context.run_dir.resolve()}\")\n", "\n", "# --- 加载训练数据 ---\n", "data_file_path = \"./scape_swift_pso_train_cleaned.wp.xlsx\"\n", "reader = WpExcelReader()\n", "train_project = reader.read(data_file_path)\n", "print(f\"✅ 成功读取训练数据: {data_file_path}\")\n", "\n", "train_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',\n", "        'PHI_T2_DIST', 'K_LABEL', 'K_LABEL_TYPE', 'PZI'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_bundle: {train_bundle.data.head()}\")\n", "\n", "# 训练集岩心检验数据准备\n", "train_label_all_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_all_bundle: {train_label_all_bundle.data.head()}\")\n", "\n", "train_label_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_pz_bundle: {train_label_pz_bundle.data.head()}\")\n", "\n", "train_label_core_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE'\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_bundle: {train_label_core_bundle.data.head()}\")\n", "\n", "train_label_core_pz_bundle = train_project.get_dataset(\"swift_pso_train_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=['K_LABEL', 'K_LABEL_TYPE', 'PZI' ],\n", "    query_condition = \"K_LABEL_TYPE=='CORE' and PZI==1\",\n", "    include_system_columns=True\n", ")\n", "print (f\"===train_label_core_pz_bundle: {train_label_core_pz_bundle.data.head()}\")\n", "\n", "# --- 准备数据依赖参数 ---\n", "t2_axis_info = T2AxisLog10(t2_start=0.1, t2_end=10000, n=64)\n", "t2_time_array = t2_axis_info.calculate_values()\n", "print(f\"  - T2轴信息: {t2_axis_info}\")"]}, {"cell_type": "markdown", "id": "e5b87190", "metadata": {}, "source": ["## 3. 步骤一：SWIFT-PSO 训练\n", "\n", "调用 `run_swift_pso_training_step` 执行训练。我们首先使用 `SwiftPsoTrainingConfig.create_default()` 创建一个默认配置对象，然后将数据依赖的参数（如参考值、T2轴）注入其中。"]}, {"cell_type": "code", "execution_count": 4, "id": "76495147", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\n", "2025-07-29T04:18:49.844300Z [info     ] 开始SWIFT-PSO训练步骤                [scape.core.swift_pso.training_facade] backend=gpu bootstrap_iterations=20 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.89, 'cpu_percent': 0.0} operation=swift_pso_training_step run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:49.871509Z [info     ] 成功替换Bundle中的曲线                 [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} curve_name=K_LABEL_TYPE new_data_type=INT new_shape=(297,) operation=replace_curve\n", "2025-07-29T04:18:49.887530Z [info     ] 必需曲线验证通过                       [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') checked_curves_count=9 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} operation=validate_required_curves\n", "2025-07-29T04:18:49.903550Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=gpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.9, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T04:18:49.935071Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.configs.training_config artifact_path=swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.95, 'cpu_percent': 0.0} description=本次训练步骤的完整Pydantic配置快照，确保可复现性。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:49.950877Z [info     ] 训练配置已保存为产物                     [scape.core.swift_pso.training_facade] config_path=output01\\swift_pso_run_20250729_121848\\swift_pso_training\\training_config.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.95, 'cpu_percent': 0.0}\n", "2025-07-29T04:18:49.966634Z [info     ] 调用内部PSO优化器                     [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.95, 'cpu_percent': 0.0} operation=swift_pso_training_step\n", "2025-07-29T04:18:49.982580Z [info     ] 开始按井拆分DataFrame Bundle         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 406.95, 'cpu_percent': 0.0} operation=to_all_wells_data\n", "2025-07-29T04:18:49.989453Z [info     ] DataFrame Bundle按井拆分完成         [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 407.29, 'cpu_percent': 0.0} operation=to_all_wells_data wells_found=['C-1', 'C-2', 'T-1']\n", "--- Bootstrap-<PERSON>de 1/20 ---\n", "2025-07-29T04:18:50.035269Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 408.88, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T04:18:51.829443Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 7.087854 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.59, 'cpu_percent': 0.0}\n", "2025-07-29T04:18:51.860586Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.59, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:51.892342Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.63, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:51.908115Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.66, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285231, 0.17610280999999997) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T04:18:51.947838Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.79, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 84 代触发。\n", "2025-07-29T04:18:53.103093Z [info     ] PSO 优化完成。最终迭代次数: 84, 最优损失: 8.975764 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.81, 'cpu_percent': 0.0}\n", "2025-07-29T04:18:53.118914Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.81, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:53.152943Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.84, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:53.182748Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.93, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.02664115) v_meso_range=(2.1000000000000008e-07, 0.11335440000000001) v_micro_range=(3.300000000000002e-07, 0.04953357000000002)\n", "2025-07-29T04:18:53.214452Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 440.93, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 90 代触发。\n", "2025-07-29T04:18:54.483511Z [info     ] PSO 优化完成。最终迭代次数: 90, 最优损失: 6.002062 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0}\n", "2025-07-29T04:18:54.515034Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:54.537694Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b00_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b00_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:54.569085Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.05082942) v_meso_range=(0.005932699999999999, 0.13381922000000002) v_micro_range=(0.00268106, 0.047672050000000014)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 2/20 ---\n", "2025-07-29T04:18:54.603539Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 127 代触发。\n", "2025-07-29T04:18:56.316109Z [info     ] PSO 优化完成。最终迭代次数: 127, 最优损失: 7.537962 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0}\n", "2025-07-29T04:18:56.331840Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:56.379738Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.02, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:56.395858Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285231, 0.17610280999999997) v_micro_range=(3.700000000000002e-07, 0.05358706000000001)\n", "2025-07-29T04:18:56.427429Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 159 代触发。\n", "2025-07-29T04:18:58.772429Z [info     ] PSO 优化完成。最终迭代次数: 159, 最优损失: 6.402909 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0}\n", "2025-07-29T04:18:58.793673Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:58.825551Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:18:58.841378Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.05, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T04:18:58.885152Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.06, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 99 代触发。\n", "2025-07-29T04:19:00.366222Z [info     ] PSO 优化完成。最终迭代次数: 99, 最优损失: 6.225702 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.07, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:00.379609Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.07, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:00.406215Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b01_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b01_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.09, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:00.432775Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.09, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583869999999999, 0.09813032) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 3/20 ---\n", "2025-07-29T04:19:00.459510Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T04:19:02.113616Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 6.108709 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:02.133380Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:02.165987Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:02.186269Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.0857914) v_meso_range=(0.00455384, 0.18075239999999998) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T04:19:02.220129Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 125 代触发。\n", "2025-07-29T04:19:04.020926Z [info     ] PSO 优化完成。最终迭代次数: 125, 最优损失: 6.921834 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:04.047653Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:04.074214Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:04.100869Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.2e-07, 0.08241936999999999) v_micro_range=(3.9000000000000024e-07, 0.05727889000000002)\n", "2025-07-29T04:19:04.137242Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 73 代触发。\n", "2025-07-29T04:19:05.181483Z [info     ] PSO 优化完成。最终迭代次数: 73, 最优损失: 6.014360 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:05.197771Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:05.221393Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b02_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b02_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:05.261438Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0031885800000000003, 0.09788410000000002) v_micro_range=(0.00268107, 0.049097120000000015)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 4/20 ---\n", "2025-07-29T04:19:05.300905Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.12, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T04:19:07.048860Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 6.565652 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:07.061998Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:07.101017Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.13, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:07.115281Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.14, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(8.999999999999999e-08, 0.16021067999999997) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T04:19:07.155407Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.14, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 150 代触发。\n", "2025-07-29T04:19:09.147267Z [info     ] PSO 优化完成。最终迭代次数: 150, 最优损失: 6.715571 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:09.178077Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:09.214538Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.15, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:09.236181Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(9.999999999999998e-08, 0.08015218999999998) v_micro_range=(0.0008690600000000001, 0.06493764)\n", "2025-07-29T04:19:09.262839Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.16, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 110 代触发。\n", "2025-07-29T04:19:10.835856Z [info     ] PSO 优化完成。最终迭代次数: 110, 最优损失: 6.957785 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:10.850167Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:10.876749Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b03_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b03_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:10.890255Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0075838899999999985, 0.10196435999999999) v_micro_range=(0.0003838999999999999, 0.04470181000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 5/20 ---\n", "2025-07-29T04:19:10.940049Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 217 代触发。\n", "2025-07-29T04:19:13.918037Z [info     ] PSO 优化完成。最终迭代次数: 217, 最优损失: 5.159000 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:13.931245Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:13.971953Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:13.998029Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285229, 0.17392412) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T04:19:14.024729Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 114 代触发。\n", "2025-07-29T04:19:15.558617Z [info     ] PSO 优化完成。最终迭代次数: 114, 最优损失: 5.489939 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:15.579478Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:15.598537Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:15.625496Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(8.999999999999999e-08, 0.0780848) v_micro_range=(0.005783170000000001, 0.07943931)\n", "2025-07-29T04:19:15.638558Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 107 代触发。\n", "2025-07-29T04:19:17.119201Z [info     ] PSO 优化完成。最终迭代次数: 107, 最优损失: 6.291923 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:17.150147Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:17.185965Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b04_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b04_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:17.216129Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.005826999999999999, 0.09788412) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 6/20 ---\n", "2025-07-29T04:19:17.251534Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 136 代触发。\n", "2025-07-29T04:19:19.093306Z [info     ] PSO 优化完成。最终迭代次数: 136, 最优损失: 8.634242 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:19.106612Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:19.133383Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:19.159838Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.0028523, 0.17507141999999995) v_micro_range=(0.00038955999999999995, 0.05752800000000001)\n", "2025-07-29T04:19:19.186771Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 114 代触发。\n", "2025-07-29T04:19:20.693867Z [info     ] PSO 优化完成。最终迭代次数: 114, 最优损失: 5.710851 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:20.720630Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:20.747123Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:20.773335Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(8.999999999999999e-08, 0.0780848) v_micro_range=(0.005783170000000001, 0.07943931)\n", "2025-07-29T04:19:20.814051Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 98 代触发。\n", "2025-07-29T04:19:22.174463Z [info     ] PSO 优化完成。最终迭代次数: 98, 最优损失: 7.220544 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:22.187622Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:22.213876Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b05_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b05_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:22.253818Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583869999999999, 0.09813032) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 7/20 ---\n", "2025-07-29T04:19:22.281020Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 88 代触发。\n", "2025-07-29T04:19:23.401614Z [info     ] PSO 优化完成。最终迭代次数: 88, 最优损失: 6.216392 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:23.427198Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:23.454508Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:23.493972Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.08579139000000001) v_meso_range=(0.00527699, 0.18011803999999995) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T04:19:23.533858Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 160 代触发。\n", "2025-07-29T04:19:25.642455Z [info     ] PSO 优化完成。最终迭代次数: 160, 最优损失: 4.917216 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:25.655566Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:25.695612Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:25.721309Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.4e-07, 0.08560667999999999) v_micro_range=(3.700000000000002e-07, 0.05703878000000002)\n", "2025-07-29T04:19:25.748583Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 109 代触发。\n", "2025-07-29T04:19:27.256300Z [info     ] PSO 优化完成。最终迭代次数: 109, 最优损失: 6.539459 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:27.278348Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:27.317145Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b06_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b06_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:27.341175Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.07809129999999999) v_meso_range=(0.0009777800000000001, 0.09862211) v_micro_range=(0.00422468, 0.05513255000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 8/20 ---\n", "2025-07-29T04:19:27.375892Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 127 代触发。\n", "2025-07-29T04:19:29.057040Z [info     ] PSO 优化完成。最终迭代次数: 127, 最优损失: 6.916555 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:29.077865Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:29.111903Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:29.136916Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285234, 0.18108265999999998) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T04:19:29.176806Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 84 代触发。\n", "2025-07-29T04:19:30.324127Z [info     ] PSO 优化完成。最终迭代次数: 84, 最优损失: 9.417906 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:30.337307Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:30.364043Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:30.396817Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(0.0, 0.02664115) v_meso_range=(2.1000000000000008e-07, 0.11335440000000001) v_micro_range=(3.300000000000002e-07, 0.04953357000000002)\n", "2025-07-29T04:19:30.432333Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T04:19:32.138176Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 7.634344 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:32.151517Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:32.191468Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b07_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b07_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:32.218255Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583869999999999, 0.09813032) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 9/20 ---\n", "2025-07-29T04:19:32.258211Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 99 代触发。\n", "2025-07-29T04:19:33.658837Z [info     ] PSO 优化完成。最终迭代次数: 99, 最优损失: 6.273927 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:33.680704Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:33.708124Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:33.725468Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285229, 0.17392412) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T04:19:33.765135Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 142 代触发。\n", "2025-07-29T04:19:35.684985Z [info     ] PSO 优化完成。最终迭代次数: 142, 最优损失: 5.354298 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:35.699422Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:35.726241Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:35.763222Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.0999999999999998e-07, 0.08015219999999999) v_micro_range=(4.0000000000000025e-07, 0.05743926000000002)\n", "2025-07-29T04:19:35.779468Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 136 代触发。\n", "2025-07-29T04:19:37.740251Z [info     ] PSO 优化完成。最终迭代次数: 136, 最优损失: 6.263673 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:37.753458Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:37.780315Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b08_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b08_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:37.812409Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583869999999999, 0.09813032) v_micro_range=(0.0003839199999999999, 0.04470183000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 10/20 ---\n", "2025-07-29T04:19:37.846959Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.21, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 94 代触发。\n", "2025-07-29T04:19:39.104560Z [info     ] PSO 优化完成。最终迭代次数: 94, 最优损失: 6.867219 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:39.123985Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:39.153409Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:39.185190Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285234, 0.18108265999999998) v_micro_range=(3.400000000000002e-07, 0.042917819999999995)\n", "2025-07-29T04:19:39.214244Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.22, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 125 代触发。\n", "2025-07-29T04:19:40.914787Z [info     ] PSO 优化完成。最终迭代次数: 125, 最优损失: 7.055628 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:40.936125Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:40.969726Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:40.994724Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(2e-08, 0.03983903) v_meso_range=(1.6000000000000003e-07, 0.09288630999999999) v_micro_range=(3.600000000000002e-07, 0.05600393000000002)\n", "2025-07-29T04:19:41.022145Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 89 代触发。\n", "2025-07-29T04:19:42.355329Z [info     ] PSO 优化完成。最终迭代次数: 89, 最优损失: 9.232751 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:42.378120Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:42.395172Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b09_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b09_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:42.422094Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583879999999999, 0.09988977) v_micro_range=(0.0003839099999999999, 0.04470182000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11/20 ---\n", "2025-07-29T04:19:42.448682Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 164 代触发。\n", "2025-07-29T04:19:44.556128Z [info     ] PSO 优化完成。最终迭代次数: 164, 最优损失: 7.730893 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:44.574429Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:44.596062Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.23, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:44.622826Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(7.999999999999999e-08, 0.14883592999999998) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T04:19:44.650070Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.24, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 94 代触发。\n", "2025-07-29T04:19:45.983358Z [info     ] PSO 优化完成。最终迭代次数: 94, 最优损失: 5.598857 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:45.996618Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:46.023414Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:46.063331Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.0999999999999998e-07, 0.08015219999999999) v_micro_range=(4.0000000000000025e-07, 0.05743926000000002)\n", "2025-07-29T04:19:46.089971Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 112 代触发。\n", "2025-07-29T04:19:47.623960Z [info     ] PSO 优化完成。最终迭代次数: 112, 最优损失: 7.575903 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:47.637294Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:47.676864Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b10_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b10_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:47.716935Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006186099999999999, 0.080837) v_micro_range=(0.00865324, 0.06007731000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 12/20 ---\n", "2025-07-29T04:19:47.756826Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 118 代触发。\n", "2025-07-29T04:19:49.451389Z [info     ] PSO 优化完成。最终迭代次数: 118, 最优损失: 7.250685 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:49.470902Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:49.504683Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:49.518005Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.0857914) v_meso_range=(0.00455388, 0.18606648999999997) v_micro_range=(3.500000000000002e-07, 0.04422076000000001)\n", "2025-07-29T04:19:49.574405Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 116 代触发。\n", "2025-07-29T04:19:51.132008Z [info     ] PSO 优化完成。最终迭代次数: 116, 最优损失: 7.201065 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:51.151955Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:51.185430Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:51.217627Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.8000000000000005e-07, 0.08969831) v_micro_range=(3.300000000000002e-07, 0.04953357000000002)\n", "2025-07-29T04:19:51.249642Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 187 代触发。\n", "2025-07-29T04:19:53.857570Z [info     ] PSO 优化完成。最终迭代次数: 187, 最优损失: 8.448497 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:53.879551Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:53.906329Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b11_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b11_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:53.933000Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.26, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.00102866, 0.09212977000000001) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 13/20 ---\n", "2025-07-29T04:19:53.976550Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 155 代触发。\n", "2025-07-29T04:19:56.120502Z [info     ] PSO 优化完成。最终迭代次数: 155, 最优损失: 5.356430 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:56.133780Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:56.173850Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:56.200409Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(7.999999999999999e-08, 0.14883592999999998) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T04:19:56.240218Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.27, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 148 代触发。\n", "2025-07-29T04:19:58.214665Z [info     ] PSO 优化完成。最终迭代次数: 148, 最优损失: 5.214788 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0}\n", "2025-07-29T04:19:58.232555Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:58.267379Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:19:58.293654Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.0999999999999998e-07, 0.08015219999999999) v_micro_range=(4.0000000000000025e-07, 0.05743926000000002)\n", "2025-07-29T04:19:58.334647Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 116 代触发。\n", "2025-07-29T04:20:00.015392Z [info     ] PSO 优化完成。最终迭代次数: 116, 最优损失: 8.141652 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:00.035131Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:00.058715Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b12_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b12_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:00.082038Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583879999999999, 0.09988977) v_micro_range=(0.0003839099999999999, 0.04470182000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 14/20 ---\n", "2025-07-29T04:20:00.122064Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 130 代触发。\n", "2025-07-29T04:20:01.831870Z [info     ] PSO 优化完成。最终迭代次数: 130, 最优损失: 8.343265 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:01.842605Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:01.882745Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.28, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:01.912548Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285232, 0.17749091999999997) v_micro_range=(3.600000000000002e-07, 0.04908202000000001)\n", "2025-07-29T04:20:01.948297Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 123 代触发。\n", "2025-07-29T04:20:03.631714Z [info     ] PSO 优化完成。最终迭代次数: 123, 最优损失: 5.326702 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:03.643369Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:03.683457Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:03.723596Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.2e-07, 0.08241936999999999) v_micro_range=(3.9000000000000024e-07, 0.05727889000000002)\n", "2025-07-29T04:20:03.768035Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.29, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 166 代触发。\n", "2025-07-29T04:20:06.039024Z [info     ] PSO 优化完成。最终迭代次数: 166, 最优损失: 7.623223 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.37, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:06.044316Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.37, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:06.079491Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b13_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b13_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:06.097077Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006358900000000001, 0.08723549) v_micro_range=(0.00422468, 0.05513255000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 15/20 ---\n", "2025-07-29T04:20:06.133616Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 166 代触发。\n", "2025-07-29T04:20:08.245135Z [info     ] PSO 优化完成。最终迭代次数: 166, 最优损失: 7.354783 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:08.258360Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:08.285173Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.39, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:08.317966Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285228, 0.17181711999999996) v_micro_range=(0.00038957999999999994, 0.06439876000000001)\n", "2025-07-29T04:20:08.340992Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 159 代触发。\n", "2025-07-29T04:20:10.539170Z [info     ] PSO 优化完成。最终迭代次数: 159, 最优损失: 6.326881 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:10.562196Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:10.592561Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:10.619297Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T04:20:10.658972Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 166 代触发。\n", "2025-07-29T04:20:12.928454Z [info     ] PSO 优化完成。最终迭代次数: 166, 最优损失: 6.538745 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:12.940191Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.4, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:12.976015Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b14_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b14_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:12.992971Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.00102866, 0.09212977000000001) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 16/20 ---\n", "2025-07-29T04:20:13.034918Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 181 代触发。\n", "2025-07-29T04:20:15.447791Z [info     ] PSO 优化完成。最终迭代次数: 181, 最优损失: 6.419682 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:15.465473Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:15.501250Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:15.527170Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(7.999999999999999e-08, 0.14883592999999998) v_micro_range=(0.0034293399999999995, 0.08437213999999998)\n", "2025-07-29T04:20:15.569235Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 80 代触发。\n", "2025-07-29T04:20:16.688230Z [info     ] PSO 优化完成。最终迭代次数: 80, 最优损失: 5.304779 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:16.701580Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:16.731754Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:16.765198Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.6000000000000003e-07, 0.08604464) v_micro_range=(3.500000000000002e-07, 0.05418589000000002)\n", "2025-07-29T04:20:16.794368Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.41, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 80 代触发。\n", "2025-07-29T04:20:17.955351Z [info     ] PSO 优化完成。最终迭代次数: 80, 最优损失: 5.562743 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:17.976418Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:18.008167Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b15_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b15_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:18.034228Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.007583879999999999, 0.09988977) v_micro_range=(0.0003839099999999999, 0.04470182000000001)\n", "--- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 17/20 ---\n", "2025-07-29T04:20:18.075527Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 109 代触发。\n", "2025-07-29T04:20:19.689416Z [info     ] PSO 优化完成。最终迭代次数: 109, 最优损失: 6.563553 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:19.702731Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:19.742864Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:19.782559Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.42, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285233, 0.17923820999999998) v_micro_range=(3.500000000000002e-07, 0.04422076000000001)\n", "2025-07-29T04:20:19.811874Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 170 代触发。\n", "2025-07-29T04:20:22.036857Z [info     ] PSO 优化完成。最终迭代次数: 170, 最优损失: 5.055282 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:22.064118Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:22.103544Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:22.132777Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(1.6000000000000003e-07, 0.08604464) v_micro_range=(3.500000000000002e-07, 0.05418589000000002)\n", "2025-07-29T04:20:22.156450Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 146 代触发。\n", "2025-07-29T04:20:24.132658Z [info     ] PSO 优化完成。最终迭代次数: 146, 最优损失: 6.477007 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:24.144431Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:24.184052Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b16_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b16_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:24.198680Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.0006358900000000001, 0.08723549) v_micro_range=(0.00422468, 0.05513255000000001)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 18/20 ---\n", "2025-07-29T04:20:24.237286Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 111 代触发。\n", "2025-07-29T04:20:25.718315Z [info     ] PSO 优化完成。最终迭代次数: 111, 最优损失: 8.587862 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:25.731581Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:25.777259Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.43, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:25.797841Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(8.999999999999999e-08, 0.16021067999999997) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T04:20:25.832433Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 124 代触发。\n", "2025-07-29T04:20:27.558988Z [info     ] PSO 优化完成。最终迭代次数: 124, 最优损失: 4.024220 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:27.581797Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:27.617866Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:27.638981Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T04:20:27.678749Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 155 代触发。\n", "2025-07-29T04:20:29.879863Z [info     ] PSO 优化完成。最终迭代次数: 155, 最优损失: 5.966227 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:29.892576Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.44, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:29.933225Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b17_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b17_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.45, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:29.958872Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.45, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.00102866, 0.09212977000000001) v_micro_range=(0.0026810899999999997, 0.05125704000000002)\n", "--- <PERSON><PERSON><PERSON>-<PERSON><PERSON> 19/20 ---\n", "2025-07-29T04:20:29.999373Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.45, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 178 代触发。\n", "2025-07-29T04:20:32.454180Z [info     ] PSO 优化完成。最终迭代次数: 178, 最优损失: 7.662412 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.46, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:32.478039Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.46, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:32.510145Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.46, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:32.534054Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.46, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(8.999999999999999e-08, 0.16021067999999997) v_micro_range=(0.0013387099999999992, 0.07467034000000002)\n", "2025-07-29T04:20:32.560393Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.46, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 205 代触发。\n", "2025-07-29T04:20:35.341351Z [info     ] PSO 优化完成。最终迭代次数: 205, 最优损失: 6.884103 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.46, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:35.361904Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.46, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:35.388618Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.47, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:35.434635Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.48, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(9.999999999999998e-08, 0.08015218999999998) v_micro_range=(0.0008690600000000001, 0.06493764)\n", "2025-07-29T04:20:35.454958Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.52, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 195 代触发。\n", "2025-07-29T04:20:38.136240Z [info     ] PSO 优化完成。最终迭代次数: 195, 最优损失: 5.471930 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:38.162848Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.62, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:38.189600Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b18_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b18_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:38.231266Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08760391) v_meso_range=(0.005826999999999999, 0.09788412) v_micro_range=(0.00268105, 0.04645870000000001)\n", "--- Boots<PERSON><PERSON>-<PERSON><PERSON> 20/20 ---\n", "2025-07-29T04:20:38.271010Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 168 代触发。\n", "2025-07-29T04:20:40.563913Z [info     ] PSO 优化完成。最终迭代次数: 168, 最优损失: 7.157133 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:40.577081Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:40.603863Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f00 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f00\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:40.635174Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0} n_bins=64 n_depths=175 operation=nmr_compute stage=complete v_macro_range=(4e-08, 0.08579141) v_meso_range=(0.00285229, 0.17392412) v_micro_range=(0.00038956999999999994, 0.06102307000000001)\n", "2025-07-29T04:20:40.660424Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.64, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 161 代触发。\n", "2025-07-29T04:20:42.831376Z [info     ] PSO 优化完成。最终迭代次数: 161, 最优损失: 6.788212 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:42.857994Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:42.897343Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f01 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f01\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:42.922863Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} n_bins=64 n_depths=91 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.04773146999999999) v_meso_range=(7.999999999999999e-08, 0.07808478999999999) v_micro_range=(0.013267750000000002, 0.09139636)\n", "2025-07-29T04:20:42.964319Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 125 代触发。\n", "2025-07-29T04:20:44.714849Z [info     ] PSO 优化完成。最终迭代次数: 125, 最优损失: 6.013594 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:44.725526Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_parameters.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\params_optimal.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:44.765135Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.fold_loss_history.b19_f02 artifact_path=swift_pso_training\\1_bootstrap_lowo\\run_b19_f02\\loss_history.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:44.791641Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 441.65, 'cpu_percent': 0.0} n_bins=64 n_depths=31 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.07809129999999999) v_meso_range=(0.0035304700000000004, 0.10613271000000002) v_micro_range=(0.00268107, 0.049097120000000015)\n", "2025-07-29T04:20:44.831670Z [info     ] SWIFT-PSO optimizer using gpu backend. [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 442.23, 'cpu_percent': 0.0}\n", "INFO: 早停条件在第 29 代触发。\n", "2025-07-29T04:20:45.115853Z [info     ] PSO 优化完成。最终迭代次数: 29, 最优损失: 5.200639 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.64, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:45.125606Z [info     ] Fine-Tuning阶段完成，最终损失: 5.200639 [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.64, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:45.138848Z [info     ] SWIFT-PSO 优化完成。                [scape.core.swift_pso.internal.pso_optimizer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.64, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:45.165776Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.models.final_parameters artifact_path=swift_pso_training\\final_parameters.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 445.64, 'cpu_percent': 0.0} description=最终优化后的模型参数及上下文，可直接用于预测步骤。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:45.205663Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.datasets.all_parameters_from_lowo artifact_path=swift_pso_training\\all_parameters_from_lowo.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.45, 'cpu_percent': 0.0} description=所有Bootstrap+LOWO迭代产生的优化参数集，用于t-SNE可视化分析。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:45.232421Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.convergence_history_finetune artifact_path=swift_pso_training\\convergence_history_finetune.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.45, 'cpu_percent': 0.0} description=Fine-Tuning阶段的损失函数收敛历史。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:45.259031Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.reports.bootstrap_summary artifact_path=swift_pso_training\\summary_bootstrap_mu_rmse.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.45, 'cpu_percent': 0.0} description=每轮Bootstrap的平均验证误差（mu_rmse），用于确定最佳轮次b*。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:45.285325Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_training.diagnostics.params_warm_start artifact_path=swift_pso_training\\params_warm_start.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.45, 'cpu_percent': 0.0} description=用于启动Fine-Tuning阶段的热启动参数（theta_hat_star）。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:45.298577Z [info     ] SWIFT-PSO训练步骤完成                [scape.core.swift_pso.training_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.45, 'cpu_percent': 0.0} final_loss=5.2006394898060915 operation=swift_pso_training_step\n", "✅ [Step 1/5] 训练完成！ 结果: {'status': 'completed', 'final_loss': 5.2006394898060915}\n", "   - 最终模型参数已保存为产物: swift_pso_training.models.final_parameters\n", "   - t-SNE源数据已保存为产物: swift_pso_training.datasets.all_parameters_from_lowo\n"]}], "source": ["# 1. 使用 Pydantic 模型创建配置\n", "training_config = SwiftPsoTrainingConfig.create_default()\n", "\n", "training_config.enable_fold_diagnostics = True\n", "training_config.optimization_params=[\n", "            'log10_KSDR_A', 'PHIT_EXP', 'RHO_NMR', 'log10_KMACRO_A',\n", "            'log10_T2cutoff_short', 'log10_T2cutoff_long',\n", "            'beta_1', 'beta_2', 'delta_MDT'\n", "        ]\n", "\n", "training_config.fixed_params['T2LM_EXP'] = 2.0\n", "training_config.fixed_params['KMACRO_B'] = 2.0\n", "training_config.fixed_params['Vmacro_min'] = 0.01\n", "\n", "# 2. 注入数据依赖参数\n", "t2_p50_ref = train_bundle.data['T2_P50'].median()\n", "phit_nmr_ref = train_bundle.data['PHIT_NMR'].median()\n", "\n", "training_config.random_seed = 2000\n", "training_config.pso_config_lowo['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_lowo['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_lowo['t2_time'] = t2_time_array\n", "training_config.pso_config_lowo['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_lowo['t2_range_min'] = 0.1\n", "training_config.pso_config_lowo['t2_range_max'] = 8000\n", "\n", "training_config.pso_config_finetune['t2_p50_ref'] = t2_p50_ref\n", "training_config.pso_config_finetune['phit_nmr_ref'] = phit_nmr_ref\n", "training_config.pso_config_finetune['t2_time'] = t2_time_array\n", "training_config.pso_config_finetune['curve_to_columns_map'] = train_bundle.curve_to_columns_map\n", "training_config.pso_config_finetune['t2_range_min'] = 0.1\n", "training_config.pso_config_finetune['t2_range_max'] = 8000\n", "\n", "# 3. 调整执行参数\n", "training_config.bootstrap_iterations = 20\n", "training_config.pso_config_lowo[\"max_iterations\"] = 400\n", "training_config.pso_config_lowo[\"n_particles\"] = 150\n", "training_config.pso_config_finetune[\"max_iterations\"] = 200\n", "training_config.pso_config_finetune[\"n_particles\"] = 100\n", "\n", "# 4. 执行训练步骤\n", "print(\"🚀 [Step 1/5] 开始执行 SWIFT-PSO 训练...\")\n", "training_result = run_swift_pso_training_step(\n", "    config=training_config,\n", "    ctx=run_context, # 直接传递上下文对象\n", "    train_bundle=train_bundle,\n", "    backend='gpu'  # 执行层参数直接传入\n", ")\n", "\n", "print(f\"✅ [Step 1/5] 训练完成！ 结果: {training_result}\")\n", "print(f\"   - 最终模型参数已保存为产物: {SwiftPsoTrainingArtifacts.FINAL_PARAMETERS.value}\")\n", "print(f\"   - t-SNE源数据已保存为产物: {SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS.value}\")"]}, {"cell_type": "markdown", "id": "b1c2d3e4", "metadata": {}, "source": ["## 4. 步骤二：模型预测\n", "\n", "使用 `run_swift_pso_prediction_step` 对训练集和应用集进行预测。我们首先从 `RunContext` 中显式加载上一步训练产出的模型，然后将其作为参数传入预测函数。"]}, {"cell_type": "code", "execution_count": 5, "id": "f5g6h7i8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:20:45.392092Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.5, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T04:20:45.423123Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.51, 'cpu_percent': 0.0} file_path=scape_swift_pso_apply_cleaned.wp.xlsx file_size_mb=1.88 sheet_count=1\n", "2025-07-29T04:20:45.439459Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.51, 'cpu_percent': 0.0} project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T04:20:45.445410Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.51, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_swift_pso_apply_cleaned\n", "2025-07-29T04:20:45.481447Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.51, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=swift_pso_apply_cleaned dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:20:45.498515Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 446.52, 'cpu_percent': 0.0} curve_2d_groups=1 depth_curves=1 total_curves=8 well_curves=1\n", "2025-07-29T04:20:49.380807Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} shape=(4689, 71) sheet_name=swift_pso_apply_cleaned\n", "2025-07-29T04:20:49.407298Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} dataset_name=WpIdentifier('swift_pso_apply_cleaned')\n", "2025-07-29T04:20:49.432784Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} curve_count=8 dataset_name=swift_pso_apply_cleaned dataset_type=Continuous df_shape=(4689, 71) processing_time=3.961\n", "2025-07-29T04:20:49.447108Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T04:20:49.472833Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T04:20:49.486195Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 459.48, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_swift_pso_apply_cleaned.wp.xlsx processing_time=4.094 project_name=WpIdentifier('scape_swift_pso_apply_cleaned')\n", "✅ 成功读取应用数据: ./scape_swift_pso_apply_cleaned.wp.xlsx\n", "2025-07-29T04:20:49.526706Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.5, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=8 input_curves=['PHIT_NMR', 'WELL_NO', 'DPHIT_NMR', 'T2_P50', 'PHI_T2_DIST', 'DT2_P50', 'MD', 'T2LM'] operation=extract_metadata output_curve_count=8 output_curves=['PHIT_NMR', 'WELL_NO', 'DPHIT_NMR', 'T2_P50', 'PHI_T2_DIST', 'DT2_P50', 'MD', 'T2LM']\n", "2025-07-29T04:20:49.553403Z [info     ] 应用曲线重命名映射...                   [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.5, 'cpu_percent': 0.0} rename_map={'PHI_T2_DIST': 'T2_VALUE'}\n", "2025-07-29T04:20:49.553403Z [info     ] 开始为机器学习重命名曲线                   [logwp.models.curve.internal.metadata_rename_ml] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.5, 'cpu_percent': 0.0} mapping_count=1 operation=rename_curves_for_ml_service\n", "2025-07-29T04:20:49.580256Z [info     ] 曲线重命名完成                        [logwp.models.curve.internal.metadata_rename_ml] column_map_size=64 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.5, 'cpu_percent': 0.0} operation=rename_curves_for_ml_service renamed_count=1\n", "2025-07-29T04:20:49.597320Z [info     ] 曲线重命名完成。                       [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.5, 'cpu_percent': 0.0}\n"]}], "source": ["# --- 加载应用数据 ---\n", "apply_data_path = \"./scape_swift_pso_apply_cleaned.wp.xlsx\"\n", "apply_project = reader.read(apply_data_path)\n", "print(f\"✅ 成功读取应用数据: {apply_data_path}\")\n", "\n", "apply_bundle = apply_project.get_dataset(\"swift_pso_apply_cleaned\").extract_curve_dataframe_bundle(\n", "    curve_names=[\n", "        'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR', 'PHI_T2_DIST'\n", "    ],\n", "    rename_map={'PHI_T2_DIST': 'T2_VALUE'},\n", "    include_system_columns=True\n", ")\n", "\n", "# --- 准备预测配置 (输出曲线名将作为facade函数的直接参数传入) ---\n", "prediction_config = SwiftPsoPredictionConfig.create_default()"]}, {"cell_type": "code", "execution_count": 6, "id": "j9k0l1m2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 2.1/5] 开始对训练集进行预测...\n", "\n", "================================================================================\n", "                            SWIFT-PSO 模型产物摘要\n", "================================================================================\n", "\n", "--- 优化参数 (Optimized Parameters) ---\n", "                 参数名      数值     线性域数值\n", "        log10_KSDR_A -1.9357    0.0116\n", "            PHIT_EXP  4.8843       N/A\n", "             RHO_NMR  5.0069       N/A\n", "      log10_KMACRO_A -0.5254    0.2983\n", "log10_T2cutoff_short  1.9496   89.0495\n", " log10_T2cutoff_long  3.0270 1064.0693\n", "              beta_1  0.3507       N/A\n", "              beta_2  0.5862       N/A\n", "           delta_MDT -0.4758       N/A\n", "\n", "--- 固定参数 (Fixed Parameters) ---\n", "       参数名     数值 线性域数值\n", "  T2LM_EXP 2.0000   N/A\n", "  KMACRO_B 2.0000   N/A\n", "Vmacro_min 0.0100   N/A\n", "\n", "--- 上下文 (Context) ---\n", "  - t2_p50_ref: 331.6580\n", "  - phit_nmr_ref: 0.0685\n", "\n", "================================================================================\n", "2025-07-29T04:20:49.727328Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.51, 'cpu_percent': 0.0} data_rows=297 operation=swift_pso_prediction_step run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:49.753983Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.51, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T04:20:49.780587Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.51, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T04:20:49.798098Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.51, 'cpu_percent': 0.0} data_rows=297 operation=swift_pso_prediction\n", "2025-07-29T04:20:49.807224Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.52, 'cpu_percent': 0.0} n_bins=64 n_depths=297 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.12447955000000001) v_meso_range=(1.3e-07, 0.17507141999999995) v_micro_range=(3.800000000000002e-07, 0.05752800000000001)\n", "2025-07-29T04:20:49.847293Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.52, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(1.575531032270637e-08, 15.011304277327207) result_rows=297\n", "2025-07-29T04:20:49.860633Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T04:20:49.887079Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T04:20:49.912949Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T04:20:49.927330Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_train_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(297,) operation=add_1d_curve\n", "2025-07-29T04:20:49.962384Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T04:20:49.980339Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:50.007110Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=297\n", "✅ 训练集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n", "🚀 [Step 2.2/5] 开始对应用集进行预测...\n", "2025-07-29T04:20:50.020263Z [info     ] 开始SWIFT-PSO预测步骤                [scape.core.swift_pso.prediction_facade] backend=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction_step run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:50.048236Z [info     ] 计算后端服务已成功创建                    [scape.core.swift_pso.internal.backend_utils] backend_type=cpu context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} operation=backend_creation\n", "2025-07-29T04:20:50.060250Z [info     ] 调用内部预测逻辑                       [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T04:20:50.066138Z [info     ] 开始FOSTER-NMR渗透率预测              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} data_rows=4689 operation=swift_pso_prediction\n", "2025-07-29T04:20:50.100612Z [info     ] 向量化T2谱孔隙度组分计算完成                [logwp.extras.nmr] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 466.84, 'cpu_percent': 0.0} n_bins=64 n_depths=4689 operation=nmr_compute stage=complete v_macro_range=(3.0000000000000004e-08, 0.12447955000000001) v_meso_range=(1.3e-07, 0.17675339) v_micro_range=(3.800000000000002e-07, 0.2788430599999999)\n", "2025-07-29T04:20:50.127193Z [info     ] FOSTER-NMR渗透率预测完成              [scape.core.swift_pso.internal.prediction_logic] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} operation=swift_pso_prediction permeability_range=(2.3352276215375873e-12, 15.011304277327207) result_rows=4689\n", "2025-07-29T04:20:50.154098Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=K_PRED_SWIFT curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T04:20:50.167399Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=VMICRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T04:20:50.195752Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=VMESO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T04:20:50.207462Z [info     ] 成功添加1D曲线到Bundle                [logwp.models.datasets.bundle] bundle_name=WpIdentifier('swift_pso_apply_cleaned') context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} curve_name=VMACRO curve_shape=(4689,) operation=add_1d_curve\n", "2025-07-29T04:20:50.226072Z [info     ] 预测曲线已添加至Bundle                 [scape.core.swift_pso.prediction_facade] added_curves=['K_PRED_SWIFT', 'VMICRO', 'VMESO', 'VMACRO'] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.54, 'cpu_percent': 0.0} operation=swift_pso_prediction_step\n", "2025-07-29T04:20:50.287490Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_prediction.datasets.predicted_permeability artifact_path=swift_pso_prediction\\predicted_permeability.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.55, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:20:50.315706Z [info     ] SWIFT-PSO预测步骤完成                [scape.core.swift_pso.prediction_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.55, 'cpu_percent': 0.0} operation=swift_pso_prediction_step prediction_rows=4689\n", "2025-07-29T04:20:50.335650Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.55, 'cpu_percent': 0.0} copy_data=False new_dataset_name=train_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T04:20:50.355646Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} curve_name=MD is_uniform=False operation=check_uniform_depth_sampling sampling_interval=None\n", "2025-07-29T04:20:50.385774Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=train_apply dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:20:50.407478Z [info     ] 成功添加 'train_apply' (WpDiscreteDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T04:20:50.425854Z [info     ] 开始从DataFrame Bundle添加新数据集      [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} copy_data=False new_dataset_name=pred_apply operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T04:20:50.460425Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:20:50.476431Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=pred_apply dataset_type=Continuous operation=dataset_initialization\n", "2025-07-29T04:20:50.495698Z [info     ] 成功添加 'pred_apply' (WpContinuousDataset) 数据集到项目中。 [logwp.models.well_project] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} operation=add_dataframe_bundle project_name=WpIdentifier('swift_pso_apply_result')\n", "2025-07-29T04:20:50.505957Z [info     ] 开始写入WP Excel文件                 [logwp.io.wp_excel.wp_excel_writer] apply_formatting=True context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} dataset_count=2 file_path=output01\\swift_pso_run_20250729_121848\\swift_pso_apply_result.wp.xlsx project_name=WpIdentifier('swift_pso_apply_result') save_head_info=True save_well_map=True\n", "2025-07-29T04:20:50.520800Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 464.59, 'cpu_percent': 0.0} curve_count=15 dataset_name=WpIdentifier('train_apply') dataset_type=Point df_shape=(297, 78)\n", "2025-07-29T04:20:50.751184Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.85, 'cpu_percent': 0.0} dataset_name=WpIdentifier('train_apply') processing_time=0.23\n", "2025-07-29T04:20:50.780550Z [info     ] 开始写入数据集表单                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 465.85, 'cpu_percent': 0.0} curve_count=12 dataset_name=WpIdentifier('pred_apply') dataset_type=Continuous df_shape=(4689, 75)\n", "2025-07-29T04:20:52.395199Z [info     ] 数据集表单写入完成                      [logwp.io.wp_excel.internal.dataset_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 555.14, 'cpu_percent': 0.0} dataset_name=WpIdentifier('pred_apply') processing_time=1.615\n", "2025-07-29T04:20:52.421710Z [info     ] 工作簿创建完成                        [logwp.io.wp_excel.internal.excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 552.51, 'cpu_percent': 0.0} dataset_count=2 head_info=False total_sheets=2 well_map=False\n", "2025-07-29T04:20:52.448258Z [info     ] 开始应用Excel格式化...                [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 552.52, 'cpu_percent': 0.0}\n", "2025-07-29T04:20:52.461635Z [info     ] 开始对工作簿应用格式化                    [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 552.52, 'cpu_percent': 0.0} workbook_sheets=2\n", "2025-07-29T04:21:23.700273Z [info     ] 工作簿格式化完成                       [logwp.io.wp_excel.internal.wp_excel_formatter_service] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.17, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:23.736556Z [info     ] Excel格式化应用完成。                  [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 599.18, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:29.974899Z [info     ] WP Excel文件写入完成                 [logwp.io.wp_excel.wp_excel_writer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.39, 'cpu_percent': 0.0} file_path=output01\\swift_pso_run_20250729_121848\\swift_pso_apply_result.wp.xlsx processing_time=39.469 project_name=WpIdentifier('swift_pso_apply_result')\n", "✅ 应用集预测完成！新增曲线: [CurveBasicAttributes(name='K_PRED_SWIFT', unit='mD', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Permeability calculated by FOSTER-NMR model', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='K_PRED_SWIFT', dataframe_element_names=None), CurveBasicAttributes(name='VMICRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Micro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMICRO', dataframe_element_names=None), CurveBasicAttributes(name='VMESO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Meso-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMESO', dataframe_element_names=None), CurveBasicAttributes(name='VMACRO', unit='fraction', data_type=<WpDataType.FLOAT: 'FLOAT'>, category=<WpCurveCategory.COMPUTED: 'COMPUTED'>, description='Macro-porosity component from T2 spectrum', dimension=<WpCurveDimension.ONE_D: '1D'>, curve_class=None, is_well_identifier=False, depth_role=None, element_names=None, dataframe_column_name='VMACRO', dataframe_element_names=None)]\n"]}], "source": ["# --- 对训练集进行预测 ---\n", "print(\"🚀 [Step 2.1/5] 开始对训练集进行预测...\")\n", "# 1. 实例化产物处理器\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "# 2. 从RunContext加载上一步训练产出的模型参数\n", "model_params_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.FINAL_PARAMETERS)\n", "model_assets = handler.load_parameters(model_params_path)\n", "handler.print_model_assets_human_readable(model_assets)\n", "\n", "# 3. 调用预测步骤，并传入加载的模型\n", "train_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets,  # 传入加载的模型\n", "    prediction_bundle=train_bundle, # 使用训练数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu' # 预测通常在CPU上进行\n", ")\n", "\n", "# 获取带预测结果的bundle，以备后续步骤使用\n", "train_bundle_with_pred = train_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 训练集预测完成！新增曲线: {train_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")\n", "\n", "# --- 对应用集进行预测 ---\n", "print(\"🚀 [Step 2.2/5] 开始对应用集进行预测...\")\n", "# 模型参数只需加载一次，可复用\n", "apply_pred_result = run_swift_pso_prediction_step(\n", "    config=prediction_config,\n", "    ctx=run_context,\n", "    model_assets=model_assets, # 传入加载的模型\n", "    prediction_bundle=apply_bundle, # 使用应用数据\n", "    t2_time=t2_time_array,\n", "    output_curve_names=(\"K_PRED_SWIFT\", \"VMICRO\", \"VMESO\", \"VMACRO\"),\n", "    backend='cpu'\n", ")\n", "\n", "temp_project = WpWellProject(name=\"swift_pso_apply_result\")\n", "temp_project.add_dataframe_bundle(\"train_apply\",train_bundle)\n", "temp_project.add_dataframe_bundle(\"pred_apply\",apply_bundle)\n", "\n", "writer = WpExcelWriter()\n", "apply_result_path = output_dir / run_dir_name / \"swift_pso_apply_result.wp.xlsx\"\n", "writer.write(temp_project, apply_result_path, apply_formatting=True)\n", "\n", "apply_bundle_with_pred = apply_pred_result[\"predicted_bundle\"]\n", "print(f\"✅ 应用集预测完成！新增曲线: {apply_bundle_with_pred.curve_metadata.list_curves()[-4:]}\")"]}, {"cell_type": "markdown", "id": "1af960c4", "metadata": {}, "source": ["## 训练集上渗透率交会图"]}, {"cell_type": "code", "execution_count": 7, "id": "e07ee165", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:21:30.074891Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.41, 'cpu_percent': 0.0} step_name=train_all_perm_corr_analysis\n", "2025-07-29T04:21:30.089165Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.41, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:30.105038Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.41, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T04:21:30.129205Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.63, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:30.176876Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.63, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:30.195338Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.63, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:30.221996Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 602.63, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:30.262041Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.02, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:30.289060Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_121848\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 603.06, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:31.863303Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.53, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T04:21:31.894701Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.53, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:31.916444Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.53, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T04:21:31.943316Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.54, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:31.969331Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.55, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:31.996000Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.55, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:32.009308Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.55, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:32.049961Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.65, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:32.063186Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_121848\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 610.71, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:33.517270Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.88, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T04:21:33.543819Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:33.575239Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.88, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T04:21:33.610037Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.88, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:33.636123Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.88, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:33.650043Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:33.676113Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:33.703828Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 618.94, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:33.730430Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_121848\\train_all_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 619.0, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:35.171456Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.35, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_all_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T04:21:35.211161Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_all_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.35, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:35.237648Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.36, 'cpu_percent': 0.0}\n", "  - TRAIN_ALL 模型验证完成。\n", "\n", "2025-07-29T04:21:35.251077Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.36, 'cpu_percent': 0.0} step_name=train_pz_perm_corr_analysis\n", "2025-07-29T04:21:35.264430Z [info     ] 找到 3 口共同井进行分析: ['C-1', 'C-2', 'T-1'] [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.38, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:35.277284Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.38, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T04:21:35.303487Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.49, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:35.317209Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.51, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:35.331179Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.51, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:35.367614Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.54, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:35.410598Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.89, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:35.437318Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_121848\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 628.92, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:36.891775Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.84, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T04:21:36.918330Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.84, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:36.944936Z [info     ] 正在处理井: C-2                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.84, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T04:21:36.971691Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.84, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-2 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:36.998413Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.84, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:37.015304Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.85, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:37.038432Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.86, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:37.078442Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 637.98, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:37.105161Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-2 base_path=output01\\swift_pso_run_20250729_121848\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 638.04, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:38.505867Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.48, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-2.svg']\n", "2025-07-29T04:21:38.532352Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-2 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:38.558936Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.48, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T04:21:38.585583Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.48, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:38.624667Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.48, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:38.638525Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:38.665229Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.48, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:38.691922Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.55, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:38.705195Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_121848\\train_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 644.61, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:40.199838Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.98, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T04:21:40.239661Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=train_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.98, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:40.279133Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 653.99, 'cpu_percent': 0.0}\n", "  - TRAIN_PZ 模型验证完成。\n", "\n", "2025-07-29T04:21:40.302459Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.0, 'cpu_percent': 0.0} step_name=train_core_perm_corr_analysis\n", "2025-07-29T04:21:40.305875Z [info     ] 找到 1 口共同井进行分析: ['C-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.01, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:40.327077Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.02, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T04:21:40.345824Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.05, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:40.378478Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.06, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:40.395882Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:40.424491Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.06, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:40.461168Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.25, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:40.489095Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_121848\\train_core_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 654.27, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:41.973817Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.29, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_core_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T04:21:42.013605Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.29, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:42.040352Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.29, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE 模型验证完成。\n", "\n", "2025-07-29T04:21:42.053515Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.29, 'cpu_percent': 0.0} step_name=train_core_pz_perm_corr_analysis\n", "2025-07-29T04:21:42.079175Z [info     ] 找到 1 口共同井进行分析: ['C-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.3, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:42.080578Z [info     ] 正在处理井: C-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.3, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T04:21:42.109400Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.34, 'cpu_percent': 0.0} description=渗透率相关性分析后 C-1 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:42.133606Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.35, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:42.146469Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.35, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:42.159739Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.36, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:42.200427Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.63, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:21:42.230769Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_C-1 base_path=output01\\swift_pso_run_20250729_121848\\train_core_pz_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 663.7, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:43.681227Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 670.25, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.png', 'output01\\\\swift_pso_run_20250729_121848\\\\train_core_pz_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_C-1.svg']\n", "2025-07-29T04:21:43.718218Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_C-1 artifact_path=train_core_pz_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 670.25, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:43.746579Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 670.25, 'cpu_percent': 0.0}\n", "  - TRAIN_CORE_PZ 模型验证完成。\n", "\n", "✅ 所有模型的 PLT 检验已完成！\n", "--- 验证结果摘要 ---\n", "{\n", "  \"train_all\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.4229257156314524,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 22.3% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 46.9% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.7012657937954461,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 18.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 27.5% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6974283497143813,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 29.0% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 58.1% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.4091807078846262,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 36.7% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 75.5% <= 90%\\u3002\"\n", "    },\n", "    \"C-2\": {\n", "      \"spearman_rho\": 0.1705641228430261,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 38.5% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 46.1% <= 90%\\u3002\"\n", "    },\n", "    \"T-1\": {\n", "      \"spearman_rho\": 0.6009758957649203,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 42.9% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 71.4% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.38426052339777267,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 23.4% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 48.3% <= 90%\\u3002\"\n", "    }\n", "  },\n", "  \"train_core_pz\": {\n", "    \"C-1\": {\n", "      \"spearman_rho\": 0.38426052339777267,\n", "      \"conclusion\": \"Fail: 3x\\u7b26\\u5408\\u7387 23.4% <= 70% \\u4e14 10x\\u7b26\\u5408\\u7387 48.3% <= 90%\\u3002\"\n", "    }\n", "  }\n", "}\n"]}], "source": ["models_to_perm_corr = {\n", "    \"train_all\": train_label_all_bundle,\n", "    \"train_pz\": train_label_pz_bundle,\n", "    \"train_core\": train_label_core_bundle,\n", "    \"train_core_pz\": train_label_core_bundle,\n", "}\n", "\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_results = {}\n", "for model_prefix, label_bundle in models_to_perm_corr.items():\n", "    perm_corr_config = PermCorrelationConfig()\n", "    perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": f\"Core Perm vs. Predicted Perm ({model_prefix.upper()})\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", "    )\n", "    perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=train_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=label_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"K_LABEL\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=model_prefix\n", "    )\n", "    perm_corr_results[model_prefix] = perm_corr_result\n", "    print(f\"  - {model_prefix.upper()} 模型验证完成。\\n\")\n", "print(\"✅ 所有模型的 PLT 检验已完成！\")\n", "print(\"--- 验证结果摘要 ---\")\n", "import json\n", "print(json.dumps(perm_corr_results, indent=2))"]}, {"cell_type": "markdown", "id": "n3o4p5q6", "metadata": {}, "source": ["## 5. 步骤三：t-SNE 可视化\n", "\n", "调用 `run_tsne_visualization_step` 对训练过程中的参数演化进行可视化。此步骤会自动消费训练步骤产出的 `ALL_OPTIMIZED_PARAMETERS` 产物。\n", "\n", "我们采用 **Get -> Modify -> Pass** 模式来定制图表：\n", "1.  **Get**: 从全局注册表 `plot_registry` 获取一个默认的 `PlotProfile` 模板。\n", "2.  **Modify**: 在运行时动态修改模板的属性（如标题）。\n", "3.  **Pass**: 将修改后的 `PlotProfile` 对象传入 `facade` 函数。"]}, {"cell_type": "code", "execution_count": 8, "id": "r7s8t9u0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 [Step 3/5] 开始执行 t-SNE 可视化...\n", "2025-07-29T04:21:44.174489Z [info     ] 开始t-SNE可视化步骤                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 685.41, 'cpu_percent': 0.0} custom_profiles=['swift_pso.tsne_convergence', 'swift_pso.tsne_cluster_analysis'] operation=tsne_visualization_step run_id=20250729-041848-e6c9a655 source_data_rows=809\n", "2025-07-29T04:21:44.201146Z [info     ] 开始执行收敛轨迹分析                     [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 685.44, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T04:21:44.214403Z [info     ] 开始t-SNE降维计算                    [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 685.45, 'cpu_percent': 0.0} data_rows=809 operation=tsne_computation perplexity=15\n", "2025-07-29T04:21:44.241181Z [info     ] 执行t-SNE降维                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 685.57, 'cpu_percent': 0.0} init=pca learning_rate=200.0 max_iter=2000 n_components=2 operation=tsne_computation perplexity=15 random_state=42 verbose=0\n", "2025-07-29T04:21:54.537461Z [info     ] 在原始高维空间上执行K-means聚类            [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 691.95, 'cpu_percent': 0.0} n_clusters=4 operation=tsne_computation\n", "2025-07-29T04:21:54.760893Z [info     ] 聚类轮廓系数 (Silhouette Score): 0.1873 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 692.48, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:54.780088Z [info     ] t-SNE降维和聚类计算完成                 [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 692.5, 'cpu_percent': 0.0} operation=tsne_computation result_rows=809 tsne_x_range=(-778.9645385742188, 772.2908935546875) tsne_y_range=(-811.2766723632812, 832.9977416992188)\n", "2025-07-29T04:21:54.817783Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 693.25, 'cpu_percent': 0.0} description=t-SNE收敛轨迹的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:54.851737Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 693.25, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization\\tsne_convergence_trajectory_profile.json profile_name=swift_pso.tsne_convergence\n", "2025-07-29T04:21:54.857966Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 693.25, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization\\tsne_convergence_trajectory_plot.png profile_name=swift_pso.tsne_convergence snapshot_path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization\\tsne_convergence_trajectory_data.csv\n", "2025-07-29T04:21:54.912201Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 693.87, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_convergence\n", "2025-07-29T04:21:55.005322Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_convergence_trajectory_plot base_path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 694.59, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:56.851757Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 763.21, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T04:21:56.878564Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 763.21, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.png', 'output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_convergence_trajectory_plot.svg']\n", "2025-07-29T04:21:56.923465Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_convergence_trajectory artifact_path=swift_pso_visualization\\tsne_convergence_trajectory_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 763.22, 'cpu_percent': 0.0} description=SWIFT-PSO参数演化轨迹的t-SNE可视化图表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:56.938954Z [info     ] 开始执行最终收敛点聚类分析                  [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 763.23, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "2025-07-29T04:21:56.965467Z [info     ] 开始最终收敛点的聚类分析                   [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 763.23, 'cpu_percent': 0.0} operation=cluster_analysis_computation total_points=809\n", "2025-07-29T04:21:56.984358Z [info     ] 筛选出 60 个最终收敛点进行分析。             [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 763.23, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:57.501462Z [info     ] 执行聚类分析，方法: kmeans              [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 763.69, 'cpu_percent': 0.0} operation=cluster_analysis_computation\n", "2025-07-29T04:21:57.579007Z [info     ] 聚类分析计算完成。                      [scape.core.swift_pso.internal.tsne_computer] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 764.73, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:57.592707Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.data_snapshots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_data.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 764.82, 'cpu_percent': 0.0} description=最终收敛点聚类分析的坐标数据，用于绘图复现。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:57.619024Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 764.82, 'cpu_percent': 0.0} description=聚类分析的总体量化指标报告，包括轮廓系数、簇心等。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:57.654399Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_statistics artifact_path=swift_pso_visualization\\tsne_cluster_statistics_report.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 764.82, 'cpu_percent': 0.0} description=每个簇内部所有参数的详细统计信息（均值、标准差等）。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:57.685759Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.reports.tsne_cluster_summary_table artifact_path=swift_pso_visualization\\tsne_cluster_summary_table.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 764.89, 'cpu_percent': 0.0} description=各簇参数均值和标准差的对比摘要表，便于物理意义解释。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:57.705448Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 764.89, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization\\tsne_cluster_analysis_profile.json profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T04:21:57.721987Z [info     ] 开始从数据快照重新生成t-SNE图表             [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 764.89, 'cpu_percent': 0.0} operation=tsne_replot output_path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization\\tsne_cluster_analysis_plot.png profile_name=swift_pso.tsne_cluster_analysis snapshot_path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization\\tsne_cluster_analysis_data.csv\n", "2025-07-29T04:21:57.759985Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 765.41, 'cpu_percent': 0.0} operation=apply_profile profile_name=swift_pso.tsne_cluster_analysis\n", "2025-07-29T04:21:57.819605Z [warning  ] 为聚类 0 计算凸包失败: QH6154 Qhull precision error: Initial simplex is flat (facet 1 is coplanar with the interior point)\n", "\n", "While executing:  | qhull i Qt\n", "Options selected for Qhull 2020.2.r 2020/08/31:\n", "  run-id 1280290944  incidence  Qtriangulate  _pre-merge  _zero-centrum\n", "  _max-width 1.4e+03  Error-roundoff 7e-13  _one-merge 3.5e-12\n", "  _near-inside 1.8e-11  Visible-distance 1.4e-12  U-max-coplanar 1.4e-12\n", "  Width-outside 2.8e-12  _wide-facet 8.4e-12  _maxoutside 4.2e-12\n", "\n", "The input to qhull appears to be less than 2 dimensional, or a\n", "computation has overflowed.\n", "\n", "<PERSON><PERSON><PERSON> could not construct a clearly convex simplex from points:\n", "- p2(v3): -7.5e+02 8.2e+02\n", "- p1(v2): 3.6e+02 -5.9e+02\n", "- p0(v1): -7.5e+02 8.2e+02\n", "\n", "The center point is coplanar with a facet, or a vertex is coplanar\n", "with a neighboring facet.  The maximum round off error for\n", "computing distances is 7e-13.  The center point, facets and distances\n", "to the center point are as follows:\n", "\n", "center point   -378.7    350.5\n", "\n", "facet p1 p0 distance= -5.7e-14\n", "facet p2 p0 distance=  -71\n", "facet p2 p1 distance=    0\n", "\n", "These points either have a maximum or minimum x-coordinate, or\n", "they maximize the determinant for k coordinates.  Trial points\n", "are first selected from points that maximize a coordinate.\n", "\n", "The min and max coordinates for each dimension are:\n", "  0:    -750.3     364.5  difference= 1115\n", "  1:    -592.4     821.9  difference= 1414\n", "\n", "If the input should be full dimensional, you have several options that\n", "may determine an initial simplex:\n", "  - use 'QJ'  to joggle the input and make it full dimensional\n", "  - use 'QbB' to scale the points to the unit cube\n", "  - use 'QR0' to randomly rotate the input for different maximum points\n", "  - use 'Qs'  to search all points for the initial simplex\n", "  - use 'En'  to specify a maximum roundoff error less than 7e-13.\n", "  - trace execution with 'T3' to see the determinant for each point.\n", "\n", "If the input is lower dimensional:\n", "  - use 'QJ' to joggle the input and make it full dimensional\n", "  - use 'Qbk:0Bk:0' to delete coordinate k from the input.  You should\n", "    pick the coordinate with the least range.  The hull will have the\n", "    correct topology.\n", "  - determine the flat containing the points, rotate the points\n", "    into a coordinate plane, and delete the other coordinates.\n", "  - add one or more points to make the input full dimensional.\n", " [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 765.89, 'cpu_percent': 0.0} operation=tsne_replot\n", "2025-07-29T04:21:57.859674Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=tsne_cluster_analysis_plot base_path=output01\\swift_pso_run_20250729_121848\\swift_pso_visualization context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 766.12, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:21:59.397444Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.8, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T04:21:59.420135Z [info     ] t-SNE图表保存成功                    [scape.core.swift_pso.plotting] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.8, 'cpu_percent': 0.0} operation=tsne_replot saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.png', 'output01\\\\swift_pso_run_20250729_121848\\\\swift_pso_visualization\\\\tsne_cluster_analysis_plot.svg']\n", "2025-07-29T04:21:59.446725Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=swift_pso_visualization.plots.tsne_cluster_analysis artifact_path=swift_pso_visualization\\tsne_cluster_analysis_plot.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.8, 'cpu_percent': 0.0} description=SWIFT-PSO最终收敛点的t-SNE聚类分析图表（带凸包）。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:21:59.472919Z [info     ] t-SNE可视化步骤完成                   [scape.core.swift_pso.visualization_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.8, 'cpu_percent': 0.0} operation=tsne_visualization_step\n", "✅ [Step 3/5] t-SNE 可视化完成！结果: {'status': 'completed', 'clusters_found': 4}\n"]}], "source": ["# 1. 创建可视化配置 (保持不变)\n", "tsne_config = TsneVisualConfig(\n", "    perplexity=15,\n", "    n_iter=2000,\n", "    random_state=42,\n", "    cluster_method=\"kmeans\",\n", "    n_clusters=4,\n", "    dbscan_eps=1,\n", "    dbscan_min_samples=5\n", ")\n", "\n", "# 2. 获取并修改两个绘图配置，然后打包成字典\n", "# 2.1 收敛轨迹图的配置\n", "trajectory_profile = plot_registry.get(TsnePlotProfiles.CONVERGENCE_TRAJECTORY).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Parameter Evolution (Trajectory)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.2 聚类分析图的配置\n", "cluster_profile = plot_registry.get(TsnePlotProfiles.CLUSTER_ANALYSIS).with_updates(\n", "    title_props={\"label\": \"SWIFT-PSO Final Parameters (Cluster Analysis)\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "# 2.3 打包成字典\n", "custom_plot_profiles = {\n", "    TsnePlotProfiles.CONVERGENCE_TRAJECTORY.value: trajectory_profile,\n", "    TsnePlotProfiles.CLUSTER_ANALYSIS.value: cluster_profile\n", "}\n", "\n", "# 3. 执行可视化步骤 (加载数据的部分保持不变)\n", "print(\"🚀 [Step 3/5] 开始执行 t-SNE 可视化...\")\n", "from scape.core.swift_pso import SwiftPsoArtifactHandler\n", "handler = SwiftPsoArtifactHandler()\n", "\n", "tsne_source_path = run_context.get_artifact_path(SwiftPsoTrainingArtifacts.ALL_OPTIMIZED_PARAMETERS)\n", "tsne_source_data = handler.load_dataframe(tsne_source_path)\n", "\n", "# 3.1 更新函数调用\n", "tsne_result = run_tsne_visualization_step(\n", "    config=tsne_config,\n", "    ctx=run_context,\n", "    tsne_source_data=tsne_source_data,\n", "    plot_profiles=custom_plot_profiles # <--- 修改此处\n", ")\n", "\n", "# 4. 更新打印的产物信息\n", "print(f\"✅ [Step 3/5] t-SNE 可视化完成！结果: {tsne_result}\")"]}, {"cell_type": "markdown", "id": "s1t2u3v4", "metadata": {}, "source": ["## 6. 步骤四：PLT 盲井检验\n", "\n", "调用 `run_plt_analysis_step` 对模型的预测结果进行PLT盲井检验。此步骤会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物（包含在 `apply_bundle_with_pred` 中）。\n", "\n", "我们为不同的图表（贡献率交会图、捕获率曲线、洛伦兹曲线）分别获取并定制 `PlotProfile`，然后将它们打包成一个字典传入。"]}, {"cell_type": "code", "execution_count": 9, "id": "w5x6y7z8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:21:59.567027Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.89, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx\n", "2025-07-29T04:21:59.598004Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.91, 'cpu_percent': 0.0} file_path=scape_plt_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T04:21:59.620319Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.91, 'cpu_percent': 0.0} project_name=scape_plt_val\n", "2025-07-29T04:21:59.633574Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.91, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_plt_val\n", "2025-07-29T04:21:59.659784Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.92, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T04:21:59.685879Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.92, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T04:21:59.699782Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.92, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T04:21:59.713090Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 834.98, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=PLT dataset_type=Interval operation=dataset_initialization\n", "2025-07-29T04:21:59.726708Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.0, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=2 total_curves=4 well_curves=1\n", "2025-07-29T04:21:59.753025Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.01, 'cpu_percent': 0.0} shape=(14, 4) sheet_name=PLT\n", "2025-07-29T04:21:59.766590Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.01, 'cpu_percent': 0.0} dataset_name=WpIdentifier('PLT')\n", "2025-07-29T04:21:59.779820Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.01, 'cpu_percent': 0.0} curve_count=4 dataset_name=PLT dataset_type=Interval df_shape=(14, 4) processing_time=0.067\n", "2025-07-29T04:21:59.806424Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.01, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T04:21:59.819777Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.01, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T04:21:59.840149Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.01, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_plt_val.wp.xlsx processing_time=0.273 project_name=WpIdentifier('scape_plt_val')\n", "2025-07-29T04:21:59.859819Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.01, 'cpu_percent': 0.0} extracted_curve_count=1 operation=extract_curve_dataframe_bundle\n", "2025-07-29T04:21:59.873220Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.02, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=4 input_curves=['MD_Bottom', 'MD_Top', 'WELL_NO', 'QOZI'] operation=extract_metadata output_curve_count=4 output_curves=['MD_Bottom', 'MD_Top', 'WELL_NO', 'QOZI']\n", "✅ 成功读取PLT验证数据: ./scape_plt_val.wp.xlsx\n", "🚀 [Step 4/5] 开始执行 PLT 盲井检验...\n", "2025-07-29T04:21:59.899883Z [info     ] 开始执行PLT盲井检验步骤...               [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.02, 'cpu_percent': 0.0} step_name=plt_analysis\n", "2025-07-29T04:21:59.926510Z [info     ] 找到 2 口共同井进行分析: ['C-1', 'C-2']  [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.02, 'cpu_percent': 0.0}\n", "2025-07-29T04:21:59.940402Z [info     ] 正在处理井: C-1                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.02, 'cpu_percent': 0.0} well_name=C-1\n", "2025-07-29T04:21:59.971523Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:00.003859Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:00.033363Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:00.060123Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:00.086784Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:00.113327Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:00.140038Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.65, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:00.179886Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-1 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.67, 'cpu_percent': 0.0} description=PLT分析后 C-1 井的分层属性表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:00.206581Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.67, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T04:22:00.220317Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.67, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:00.246798Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.67, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:00.286765Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.78, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T04:22:00.313370Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-1 base_path=output01\\swift_pso_run_20250729_121848\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 835.79, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T04:22:00.601436Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 838.82, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-1.png']\n", "2025-07-29T04:22:00.620569Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-1 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 838.82, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:00.647337Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 838.82, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json profile_name=validation.plt.capture_curve\n", "2025-07-29T04:22:00.673966Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 838.82, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:00.701695Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 838.82, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:00.740226Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 838.93, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T04:22:00.753657Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-1 base_path=output01\\swift_pso_run_20250729_121848\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 838.94, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T04:22:01.003138Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 841.88, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-1.png']\n", "2025-07-29T04:22:01.034042Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 841.88, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:01.060756Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 841.88, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T04:22:01.083954Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-1 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 841.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:01.103283Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 841.88, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:01.129993Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 842.0, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T04:22:01.148873Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-1 base_path=output01\\swift_pso_run_20250729_121848\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 842.04, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T04:22:01.460051Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.3, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-1.png']\n", "2025-07-29T04:22:01.487108Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-1 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.3, 'cpu_percent': 0.0} description=C-1 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:01.514239Z [info     ] 正在处理井: C-2                     [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.3, 'cpu_percent': 0.0} well_name=C-2\n", "2025-07-29T04:22:01.540670Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.31, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:01.567100Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.31, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:01.607373Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.31, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:01.634012Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.32, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:01.661073Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.34, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:01.687644Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.34, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:01.727269Z [info     ] 深度采样间隔检查完成                     [logwp.models.datasets.internal.depth_sampling_check] algorithm=mad context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.34, 'cpu_percent': 0.0} curve_name=MD is_uniform=True operation=check_uniform_depth_sampling sampling_interval=0.15239999999994325\n", "2025-07-29T04:22:01.767427Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.reports.analyzed_layers_C-2 artifact_path=plt_analysis\\plt_analysis.reports.analyzed_layers_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.35, 'cpu_percent': 0.0} description=PLT分析后 C-2 井的分层属性表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:01.780571Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.35, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T04:22:01.807220Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.contribution_crossplot_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.contribution_crossplot_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.35, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:01.833376Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.contribution_crossplot_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.35, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:01.860739Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.48, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.contribution_crossplot\n", "2025-07-29T04:22:01.887388Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.contribution_crossplot_C-2 base_path=output01\\swift_pso_run_20250729_121848\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 845.5, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T04:22:02.161419Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 848.85, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\plt_analysis\\\\plt_analysis.plots.contribution_crossplot_C-2.png']\n", "2025-07-29T04:22:02.181117Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.contribution_crossplot_C-2 artifact_path=plt_analysis\\plt_analysis.plots.contribution_crossplot_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 848.85, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.contribution_crossplot 图表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:02.220798Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 848.85, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json profile_name=validation.plt.capture_curve\n", "2025-07-29T04:22:02.234016Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.capture_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.capture_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 848.85, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:02.260991Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.capture_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 848.85, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:02.287429Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 848.99, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.capture_curve\n", "2025-07-29T04:22:02.316610Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.capture_curve_C-2 base_path=output01\\swift_pso_run_20250729_121848\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 849.01, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T04:22:02.581563Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 852.2, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\plt_analysis\\\\plt_analysis.plots.capture_curve_C-2.png']\n", "2025-07-29T04:22:02.621363Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.capture_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.capture_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 852.2, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.capture_curve 图表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:02.648098Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 852.21, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json profile_name=validation.plt.lorenz_curve\n", "2025-07-29T04:22:02.660992Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.configs.lorenz_curve_profile_C-2 artifact_path=plt_analysis\\plt_analysis.configs.lorenz_curve_profile_C-2.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 852.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:02.687627Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.data_snapshots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.data_snapshots.lorenz_curve_C-2.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 852.21, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:02.741161Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 852.34, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.plt.lorenz_curve\n", "2025-07-29T04:22:02.754704Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=plt_analysis.plots.lorenz_curve_C-2 base_path=output01\\swift_pso_run_20250729_121848\\plt_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 852.37, 'cpu_percent': 0.0} formats=['png'] operation=save_figure\n", "2025-07-29T04:22:03.011247Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 855.79, 'cpu_percent': 0.0} operation=save_figure saved_count=1 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\plt_analysis\\\\plt_analysis.plots.lorenz_curve_C-2.png']\n", "2025-07-29T04:22:03.047177Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=plt_analysis.plots.lorenz_curve_C-2 artifact_path=plt_analysis\\plt_analysis.plots.lorenz_curve_C-2.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 855.79, 'cpu_percent': 0.0} description=C-2 井的 plt_analysis.plots.lorenz_curve 图表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:03.061561Z [info     ] PLT盲井检验步骤执行完毕。                 [scape.core.validation.plt_analysis_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 855.79, 'cpu_percent': 0.0}\n", "✅ [Step 4/5] PLT 检验完成！结果: {'C-1': {'spearman_rho': 0.5714285714285715, 'gini_capture': 0.6364089205739811, 'gini_lorenz': -0.04340850319052669}, 'C-2': {'spearman_rho': 0.3571428571428572, 'gini_capture': -0.050207321282104145, 'gini_lorenz': 0.31942995641122507}}\n", "   - 分析报告已保存为产物: plt_analysis.reports.analyzed_layers_* \n"]}], "source": ["# --- 加载PLT验证数据 ---\n", "plt_val_path = \"./scape_plt_val.wp.xlsx\"\n", "plt_val_project = reader.read(plt_val_path)\n", "plt_val_bundle = plt_val_project.get_dataset(\"PLT\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取PLT验证数据: {plt_val_path}\")\n", "\n", "# 1. 创建PLT分析配置 (曲线名将作为facade函数的直接参数传入)\n", "plt_config = PltAnalysisConfig()\n", "\n", "# 2. 获取并修改多个绘图配置 (Get -> Modify -> Pass)\n", "contribution_profile = plot_registry.get(PltAnalysisPlotProfiles.CONTRIBUTION_CROSSPLOT).with_updates(\n", "    title_props={\"label\": \"Flow Contribution Crossplot\"}\n", ")\n", "\n", "capture_profile = plot_registry.get(PltAnalysisPlotProfiles.CAPTURE_CURVE).with_updates(\n", "    title_props={\"label\": \"Permeability Capture Curve\"}\n", ")\n", "\n", "lorenz_profile = plot_registry.get(PltAnalysisPlotProfiles.LORENZ_CURVE).with_updates(\n", "    title_props={\"label\": \"Lorenz Curve Analysis\"}\n", ")\n", "\n", "plt_plot_profiles = {\n", "    PltPlotTypes.CONTRIBUTION_CROSSPLOT: contribution_profile,\n", "    PltPlotTypes.CAPTURE_CURVE: capture_profile,\n", "    PltPlotTypes.LORENZ_CURVE: lorenz_profile\n", "}\n", "\n", "# 3. 执行PLT分析步骤\n", "print(\"🚀 [Step 4/5] 开始执行 PLT 盲井检验...\")\n", "plt_result = run_plt_analysis_step(\n", "    config=plt_config,\n", "    ctx=run_context,\n", "    prediction_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    plt_bundle=plt_val_bundle,\n", "    permeability_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    flow_rate_curve=\"QOZI\",           # 数据选择器参数\n", "    plot_profiles=plt_plot_profiles\n", ")\n", "\n", "print(f\"✅ [Step 4/5] PLT 检验完成！结果: {plt_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PltAnalysisArtifacts.ANALYZED_LAYERS_TABLE_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["## 7. 步骤五：岩心井渗透率(CT)相关性分析\n", "\n", "调用 `run_perm_correlation_step` 对模型的预测结果进行岩心井检验。此步骤同样会消费预测步骤产出的 `PREDICTED_PERMEABILITY` 产物。"]}, {"cell_type": "code", "execution_count": 10, "id": "e5f6g7h8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:22:03.141644Z [info     ] 开始读取WP Excel文件（流模式）            [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 855.8, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx\n", "2025-07-29T04:22:03.194669Z [info     ] Excel工作簿以只读流模式加载成功             [logwp.io.wp_excel.internal.excel_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 855.99, 'cpu_percent': 0.0} file_path=scape_core_k_val.wp.xlsx file_size_mb=0.01 sheet_count=3\n", "2025-07-29T04:22:03.229544Z [info     ] 从文件名提取项目名称                     [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 856.0, 'cpu_percent': 0.0} project_name=scape_core_k_val\n", "2025-07-29T04:22:03.242426Z [info     ] 创建WpWellProject                [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 856.01, 'cpu_percent': 0.0} default_depth_unit=m project_name=scape_core_k_val\n", "2025-07-29T04:22:03.261118Z [info     ] 井头信息构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] attribute_count=4 context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 856.04, 'cpu_percent': 0.0} sheet_name=_Head_Info\n", "2025-07-29T04:22:03.274690Z [info     ] _Well_Map表单解析完成                [logwp.io.wp_excel.internal.well_map_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 856.04, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T04:22:03.287771Z [info     ] 井名映射构造完成（流模式）                  [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 856.05, 'cpu_percent': 0.0} mapping_count=4 sheet_name=_Well_Map\n", "2025-07-29T04:22:03.301069Z [warning  ] 创建了空数据集                        [logwp.models.datasets.base] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.4, 'cpu_percent': 0.0} dataframe_shape=(0, 0) dataset_name=K_Val dataset_type=Point operation=dataset_initialization\n", "2025-07-29T04:22:03.315793Z [info     ] 曲线元数据创建完成                      [logwp.io.wp_excel.internal.curve_parser] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.41, 'cpu_percent': 0.0} curve_2d_groups=0 depth_curves=1 total_curves=9 well_curves=1\n", "2025-07-29T04:22:03.354515Z [info     ] DataFrame从行迭代器构造完成             [logwp.io.wp_excel.internal.data_converter] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} shape=(44, 9) sheet_name=K_Val\n", "2025-07-29T04:22:03.367756Z [info     ] 数据集构造合规性验证通过                   [logwp.io.wp_excel.internal.validator] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} dataset_name=WpIdentifier('K_Val')\n", "2025-07-29T04:22:03.381599Z [info     ] 数据集构造完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} curve_count=9 dataset_name=K_Val dataset_type=Point df_shape=(44, 9) processing_time=0.094\n", "2025-07-29T04:22:03.409606Z [info     ] 工作表遍历完成（流模式）                   [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} dataset_count=1 has_head_info=True has_well_map=True\n", "2025-07-29T04:22:03.424749Z [info     ] 项目完整性验证通过                      [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} dataset_count=1\n", "2025-07-29T04:22:03.447301Z [info     ] WP Excel文件读取完成                 [logwp.io.wp_excel.wp_excel_reader] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} dataset_count=1 file_path=scape_core_k_val.wp.xlsx processing_time=0.306 project_name=WpIdentifier('scape_core_k_val')\n", "2025-07-29T04:22:03.461194Z [info     ] curve_names为None，将提取所有数据曲线。    [logwp.models.datasets.internal.curve_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} extracted_curve_count=7 operation=extract_curve_dataframe_bundle\n", "2025-07-29T04:22:03.487807Z [info     ] 曲线元数据提取完成                      [logwp.models.curve.internal.metadata_extraction] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.44, 'cpu_percent': 0.0} downgraded_curves=[] input_curve_count=9 input_curves=['PERM', 'SAMPLE_TYPE', 'POR', 'K_LABEL_TYPE', 'WELL_NO', 'PERM_LT_001_FLAG', 'MD', 'K_LABEL', 'Lithology'] operation=extract_metadata output_curve_count=9 output_curves=['PERM', 'SAMPLE_TYPE', 'POR', 'K_LABEL_TYPE', 'WELL_NO', 'PERM_LT_001_FLAG', 'MD', 'K_LABEL', 'Lithology']\n", "✅ 成功读取岩心验证数据: ./scape_core_k_val.wp.xlsx\n", "🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\n", "2025-07-29T04:22:03.512674Z [info     ] 开始执行渗透率相关性分析步骤...              [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.45, 'cpu_percent': 0.0} step_name=ct_perm_corr_analysis\n", "2025-07-29T04:22:03.527953Z [info     ] 找到 1 口共同井进行分析: ['T-1']         [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.45, 'cpu_percent': 0.0}\n", "2025-07-29T04:22:03.527953Z [info     ] 正在处理井: T-1                     [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.45, 'cpu_percent': 0.0} well_name=T-1\n", "2025-07-29T04:22:03.554484Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.datasets.aligned_data_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.datasets.aligned_data_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.87, 'cpu_percent': 0.0} description=渗透率相关性分析后 T-1 井的对齐数据表。 operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:03.581409Z [info     ] 配置模板已保存                        [logwp.extras.plotting.profiles] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.87, 'cpu_percent': 0.0} path=output01\\swift_pso_run_20250729_121848\\ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:22:03.594851Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.configs.crossplot_profile_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.configs.crossplot_profile_T-1.json context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.87, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:03.621228Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.data_snapshots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.data_snapshots.crossplot_T-1.csv context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 853.87, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:03.674527Z [info     ] 绘图配置应用成功                       [logwp.extras.plotting.styler] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 854.12, 'cpu_percent': 0.0} operation=apply_profile profile_name=validation.perm_corr.permeability_crossplot\n", "2025-07-29T04:22:03.687887Z [info     ] 开始保存图像                         [logwp.extras.plotting.saver] base_name=perm_corr_analysis.plots.crossplot_T-1 base_path=output01\\swift_pso_run_20250729_121848\\ct_perm_corr_analysis context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 854.12, 'cpu_percent': 0.0} formats=['png', 'svg'] operation=save_figure\n", "2025-07-29T04:22:05.355875Z [info     ] 图像保存成功                         [logwp.extras.plotting.saver] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 862.04, 'cpu_percent': 0.0} operation=save_figure saved_count=2 saved_paths=['output01\\\\swift_pso_run_20250729_121848\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.png', 'output01\\\\swift_pso_run_20250729_121848\\\\ct_perm_corr_analysis\\\\perm_corr_analysis.plots.crossplot_T-1.svg']\n", "2025-07-29T04:22:05.395815Z [info     ] Artifact registered            [logwp.extras.tracking.context] artifact_name=perm_corr_analysis.plots.crossplot_T-1 artifact_path=ct_perm_corr_analysis\\perm_corr_analysis.plots.crossplot_T-1.png context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 862.04, 'cpu_percent': 0.0} description=None operation=register_artifact run_id=20250729-041848-e6c9a655\n", "2025-07-29T04:22:05.435809Z [info     ] 渗透率相关性分析步骤执行完毕。                [scape.core.validation.perm_corr_facade] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 862.04, 'cpu_percent': 0.0}\n", "✅ [Step 5/5] 岩心井检验完成！结果: {'T-1': {'spearman_rho': 0.5327351139784332, 'conclusion': \"Fail: 5x符合率 9.8% <= 50% (未满足井 'T-1' 的放宽标准)。\"}}\n", "   - 分析报告已保存为产物: perm_corr_analysis.plots.crossplot_* \n"]}], "source": ["# --- 加载岩心验证数据 ---\n", "core_val_path = \"./scape_core_k_val.wp.xlsx\"\n", "core_val_project = reader.read(core_val_path)\n", "core_val_bundle = core_val_project.get_dataset(\"K_Val\").extract_curve_dataframe_bundle(include_system_columns=True)\n", "print(f\"✅ 成功读取岩心验证数据: {core_val_path}\")\n", "\n", "# 1. 创建渗透率相关性分析配置\n", "perm_corr_config = PermCorrelationConfig(\n", "    relaxed_wells=[\"T-1\"]     # 对T-1井使用放宽的深度对齐标准\n", ")\n", "\n", "# 2. 获取并修改绘图配置 (Get -> Modify -> Pass)\n", "original_perm_corr_profile = plot_registry.get(PermCorrelationPlotProfiles.PERMEABILITY_CROSSPLOT, clone=False)\n", "perm_corr_profile = original_perm_corr_profile.with_updates(\n", "    title_props={\"label\": \"Core Perm vs. Predicted Perm\"},\n", "    save_config={\"format\": [\"png\", \"svg\"]}\n", ")\n", "\n", "# 3. 执行渗透率相关性分析步骤\n", "print(\"🚀 [Step 5/5] 开始执行岩心井渗透率相关性分析...\")\n", "perm_corr_result = run_perm_correlation_step(\n", "    config=perm_corr_config,\n", "    ctx=run_context,\n", "    left_bundle=apply_bundle_with_pred, # 使用带预测结果的应用集\n", "    right_bundle=core_val_bundle,\n", "    left_curve=\"K_PRED_SWIFT\", # 数据选择器参数\n", "    right_curve=\"PERM\",    # 数据选择器参数\n", "    plot_profile=perm_corr_profile,\n", "    prefix=\"ct\"\n", ")\n", "\n", "print(f\"✅ [Step 5/5] 岩心井检验完成！结果: {perm_corr_result}\")\n", "print(f\"   - 分析报告已保存为产物: {PermCorrelationArtifacts.CROSSPLOT_PREFIX.value}* \")"]}, {"cell_type": "markdown", "id": "i9j0k1l2", "metadata": {}, "source": ["## 8. 总结\n", "\n", "🎉 **SWIFT-PSO Case 02 (重构版) 工作流执行完毕！**\n", "\n", "本次重构展示了如何使用新的组件化框架来构建一个清晰、可维护、可追踪的机器学习工作流。所有步骤的输入、输出和配置都得到了规范化管理。\n", "\n", "**关键亮点:**\n", "1.  **统一的运行上下文 (`RunContext`)**: 所有的步骤都在同一个 `RunContext` 中执行，确保了所有产物（模型、数据集、图表、报告）和日志都被集中管理在一个独立的运行目录中：\n", "    - **输出目录**: `./output02/swift_pso_workflow_run`\n", "2.  **清晰的步骤划分**: 每个核心任务（训练、预测、可视化、验证）都被封装成一个独立的 `run_*_step` 函数，职责明确。\n", "3.  **类型安全的配置 (Pydantic)**: 使用 `SwiftPsoTrainingConfig`, `TsneVisualConfig`, `PltAnalysisConfig` 等Pydantic模型替代了易出错的字典，提供了自动验证和清晰的文档。\n", "4.  **自动化的产物管理**: `facade` 函数内部处理了产物的保存和注册，使得工作流代码更简洁。下游步骤可以通过 `RunContext` 自动加载上游产物，无需手动传递文件路径。\n", "5.  **灵活的绘图系统 (`PlotProfile`)**: 通过 **Get -> Modify -> Pass** 模式，我们可以在不修改组件源码的情况下，轻松地定制图表的每一个细节，同时享受高质量的默认模板。"]}, {"cell_type": "markdown", "id": "j8k9l0m1", "metadata": {}, "source": ["## 9. 最终化运行\n", "\n", "在所有步骤完成后，手动调用 `finalize()` 来结束本次运行。这将确保所有日志被刷新，并且运行清单 `manifest.json` 被正确写入。"]}, {"cell_type": "code", "execution_count": 11, "id": "n2o3p4q5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-29T04:22:05.530373Z [info     ] Run finalized                  [logwp.extras.tracking.context] context_gpu={'device_name': 'Quadro RTX 4000', 'memory_used_mb': 1060.69, 'memory_total_mb': 8191.69} context_performance={'memory_rss_mb': 862.09, 'cpu_percent': 0.0} duration_seconds=196.687 manifest_path=output01\\swift_pso_run_20250729_121848\\manifest.json operation=finalize run_id=20250729-041848-e6c9a655 status=RUNNING\n", "✅ 运行已最终化，所有记录已保存。\n"]}], "source": ["run_context.finalize()\n", "print(\"✅ 运行已最终化，所有记录已保存。\")"]}], "metadata": {"kernelspec": {"display_name": "scape_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}