from __future__ import annotations


"""logwp.exceptions
~~~~~~~~~~~~~~~~~~~~
现代化异常层次树（EXH）实现。

遵循现代化技术栈要求和 CursorRules·EXH 及 SAD §4.5：
1. 根异常类 ``WpError`` 继承 ``Exception``，所有包内异常派生于此。
2. 支持 Exception Groups (Python 3.11+) 批量异常处理。
3. 结构化异常信息，包含丰富的诊断上下文。
4. GPU计算异常支持，处理CUDA相关错误。
5. **严禁** 在此文件之外重新定义与此处重复的异常类。

注意：基础异常类型（WpError、ErrorContext、CompContext）已移动到 logwp.infra.exceptions
以避免循环导入问题，符合架构分层原则。

异常定义规则（MANDATORY）
========================
1. **唯一定义原则**：所有logwp包异常必须且只能在此文件中定义
2. **继承层次规范**：所有异常必须直接或间接继承自WpError
3. **命名约定**：使用Wp前缀 + 功能描述 + Error后缀（如WpDataError）
4. **分组组织**：按功能域分组（文件/I/O、数据层、校验、Registry等）

异常使用规则（MANDATORY）
========================
1. **结构化上下文**：所有异常抛出必须使用context=ErrorContext(...)
   ✅ raise WpDataError("错误信息", context=ErrorContext(operation="操作名"))
   ❌ raise WpDataError("错误信息")

2. **异常链保持**：捕获并重新抛出时必须使用raise ... from e
   ✅ raise WpDataError("处理失败", context=ctx) from e
   ❌ raise WpDataError("处理失败", context=ctx)

3. **专用异常类型**：根据错误类型选择最具体的异常类
   ✅ raise WpValidationError() # 验证错误
   ❌ raise WpError() # 过于通用

4. **GPU异常特殊处理**：GPU相关异常需要同时提供ErrorContext和GpuContext
   ✅ raise WpGpuError("GPU错误", context=ctx, gpu_context=gpu_ctx)

5. **COMP异常特殊处理**：COMP类型异常需要同时提供ErrorContext和CompContext
   ✅ raise WpCompError("COMP错误", context=ctx, comp_context=comp_ctx)

若需新增异常：
   • 先在本文件按树形结构添加；
   • 更新《SCAPE_DDS_详细设计_logwp.md》附录 B Mermaid 图；
   • 添加对应单元测试（EXT-1/2）。

logwp测井数据模型异常层次指南（Architecture Guide）
===========================================
针对logwp包的测井数据模型，异常层次按功能域组织：

**数据层异常族**：
```
WpError (根异常)
├── WpDataError (数据集内容或一致性错误基类)
│   ├── WpDatasetNotFoundError (数据集不存在)
│   ├── WpConsistencyError (数据一致性错误)
│   └── WpDataIntegrityError (数据完整性错误)
├── WpValidationError (数据验证错误)
│   └── WpSchemaValidationError (模式验证错误)
├── WpUnknownDatasetError (未知数据集类型)
└── WpDuplicateColumnError (列名冲突)
```

**属性和映射异常族**：
```
WpError (根异常)
├── WpAttributeError (属性操作错误基类)
│   ├── WpAttributeNotFoundError (属性不存在)
│   └── WpAttributeScopeError (属性作用域错误)
└── WpWellMappingError (井名映射错误)
    └── WpCircularMappingError (循环映射错误)
```

**使用场景映射**：
- **WpDataError**: 数据集操作、深度过滤、数据处理失败
- **WpDatasetNotFoundError**: WpWellProject.get_dataset()查找失败
- **WpValidationError**: 数据集验证、物理约束检查失败
- **WpUnknownDatasetError**: DatasetFactory创建未知类型数据集
- **WpAttributeError**: WpHead属性操作失败
- **WpAttributeNotFoundError**: WpHead.get_attribute()查找失败
- **WpWellMappingError**: WpWellMap井名映射操作失败

**常见错误修正**：
❌ WpDatasetError (不存在) → ✅ WpDataError
❌ WpFactoryError (不存在) → ✅ WpDataError
❌ raise WpDataError("msg", ErrorContext(...)) → ✅ raise WpDataError("msg", context=ErrorContext(...))

**代码示例**：
```python
# 数据集操作失败 (WpDepthIndexedDatabaseBase, WpContinuousDataset等)
raise WpDataError(
    "深度过滤失败",
    context=ErrorContext(
        operation="filter_depth_range",
        dataset_name=str(self.name),
        details={"min_depth": 100, "max_depth": 200}
    )
) from e

# 数据集不存在 (WpWellProject.get_dataset)
raise WpDatasetNotFoundError(
    "数据集不存在",
    context=ErrorContext(
        operation="get_dataset",
        dataset_name="MISSING_LOGS",
        file_path="project.xlsx"
    )
)

# 属性操作失败 (WpHead.get_attribute)
raise WpAttributeNotFoundError(
    "属性不存在",
    context=ErrorContext(
        operation="get_attribute",
        attribute_name="T2_AXIS",
        scope_info={"dataset": "NMR_logs"}
    )
)

# 数据集工厂创建失败 (DatasetFactory.create)
raise WpDataError(
    "创建数据集失败",
    context=ErrorContext(
        operation="create_dataset",
        dataset_name="OBMIQ_logs",
        additional_info={"dataset_type": "CONTINUOUS"}
    )
) from e
```
"""

# 从基础设施层导入基础异常类型，避免循环导入
from logwp.infra.exceptions import ErrorContext, WpError, CompContext, GpuContext

__all__ = [
    # 基础异常类型（从 logwp.infra.exceptions 重新导出）
    "ErrorContext",  # 异常上下文信息
    "CompContext",   # COMP类型上下文信息
    "WpError",       # 根异常类
    "GpuContext",    # GPU计算上下文信息

    # 文件 / I/O 异常族
    "WpFileFormatError",
    "WpIOError",
    "WpMemoryLimitError",
    "WpAsyncIOError",

    # 数据层异常族
    "WpDataError",
    "WpDatasetNotFoundError",
    "WpConsistencyError",
    "WpDataIntegrityError",
    "WpCurveMetadataError",
    "WpDatasetMergeError",
    "WpDatasetTypeError",

    # 校验异常族
    "WpValidationError",
    "WpSchemaValidationError",
    "WpDepthUnitMismatchError",

    # Dataset异常族
    "WpUnknownDatasetError",
    "WpDuplicateColumnError",

    # NMR异常族
    "WpNmrError",
    "WpNmrInputError",
    "WpNmrComputationError",

    # GPU计算异常族
    "WpGpuError",
    "WpGpuMemoryError",
    "WpGpuDeviceError",
    "WpGpuComputationError",

    # WFS v1.0 业务异常族
    "WpCompError",
    "WpCompValidationError",
    "WpT2AxisError",
    "WpCurve2DError",
    "WpAttributeError",
    "WpAttributeNotFoundError",
    "WpAttributeScopeError",
    "WpWellMappingError",
    "WpCircularMappingError",

    # 批量处理工具
    "create_exception_group",
]


# ------------------------------------------------------------
# 注意：基础异常类型已移动到 logwp.infra.exceptions
# ErrorContext、CompContext、WpError 现在从 logwp.infra.exceptions 导入
# ------------------------------------------------------------


# ------------------------------------------------------------
# 文件 / I/O 异常族（现代化设计）
# ------------------------------------------------------------


# ------------------------------------------------------------
# 数据层异常族（现代化设计）
# ------------------------------------------------------------


class WpDataError(WpError):
    """数据集内容或一致性错误基类。

    支持GPU和CPU数据处理的错误诊断。
    """


class WpDatasetNotFoundError(WpDataError):
    """请求的数据集在项目中不存在。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="get_dataset",
        ...     dataset_name="MISSING_LOGS",
        ...     file_path="project.xlsx"
        ... )
        >>> raise WpDatasetNotFoundError("数据集不存在", context=ctx)
    """


class WpConsistencyError(WpDataError):
    """数据集间索引或形状不一致。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="validate_dataset_consistency",
        ...     dataset_name="OBMIQ_logs",
        ...     additional_info={
        ...         "expected_rows": 1000,
        ...         "actual_rows": 950,
        ...         "missing_indices": [100, 200, 300]
        ...     }
        ... )
        >>> raise WpConsistencyError("数据集行数不一致", context=ctx)
    """


class WpDataIntegrityError(WpDataError):
    """单数据集内数据完整性检查失败。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="validate_data_integrity",
        ...     dataset_name="K_Label",
        ...     column_name="DEPTH",
        ...     additional_info={
        ...         "null_count": 50,
        ...         "duplicate_count": 10,
        ...         "out_of_range_count": 5
        ...     }
        ... )
        >>> raise WpDataIntegrityError("数据完整性检查失败", context=ctx)
    """


class WpCurveMetadataError(WpDataError):
    """曲线元数据处理错误。

    用于曲线基本属性管理过程中的错误处理。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="add_curve",
        ...     curve_name="GR",
        ...     additional_info={"reason": "duplicate_curve"}
        ... )
        >>> raise WpCurveMetadataError("曲线已存在", context=ctx)
        >>>
        >>> ctx = ErrorContext(
        ...     operation="validate_curve_attributes",
        ...     curve_name="T2_VALUE",
        ...     additional_info={
        ...         "dimension": "2D",
        ...         "element_names": None,
        ...         "validation_rule": "2d_curve_must_have_elements"
        ...     }
        ... )
        >>> raise WpCurveMetadataError("二维组合曲线必须提供元素名称列表", context=ctx)
    """


class WpDatasetMergeError(WpDataError):
    """数据集合并操作错误。

    用于数据集合并过程中的错误处理，包括类型不兼容、数据冲突等。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="merge_datasets_via_continuous",
        ...     additional_info={
        ...         "left_dataset": "logs",
        ...         "right_dataset": "core",
        ...         "error_reason": "incompatible_data_types"
        ...     }
        ... )
        >>> raise WpDatasetMergeError("数据集合并失败", context=ctx)
        >>>
        >>> ctx = ErrorContext(
        ...     operation="convert_dataset_to_continuous",
        ...     dataset_name="interval_data",
        ...     additional_info={
        ...         "dataset_type": "WpIntervalDataset",
        ...         "conversion_error": "insufficient_data_points"
        ...     }
        ... )
        >>> raise WpDatasetMergeError("数据集转换为连续型失败", context=ctx)
    """





class WpDatasetTypeError(WpDataError):
    """数据集类型错误。

    用于不支持的数据集类型或类型转换错误的处理。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="convert_dataset_to_continuous",
        ...     dataset_name="unknown_dataset",
        ...     additional_info={
        ...         "actual_type": "UnknownDatasetType",
        ...         "supported_types": ["WpContinuousDataset", "WpDiscreteDataset", "WpIntervalDataset"]
        ...     }
        ... )
        >>> raise WpDatasetTypeError("不支持的数据集类型", context=ctx)
    """


# ------------------------------------------------------------
# 校验异常族（现代化设计）
# ------------------------------------------------------------


class WpValidationError(WpError):
    """通用校验失败基类（Schema / 值域等）。

    支持pydantic v2和传统pandera校验。
    """


class WpDepthUnitMismatchError(WpValidationError):
    """深度单位不匹配错误。

    用于数据集合并或比较时深度单位不一致的错误处理。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="validate_depth_unit_consistency",
        ...     additional_info={
        ...         "left_dataset": "logs",
        ...         "right_dataset": "core",
        ...         "left_unit": "m",
        ...         "right_unit": "ft"
        ...     }
        ... )
        >>> raise WpDepthUnitMismatchError("数据集深度单位不一致", context=ctx)
        >>>
        >>> ctx = ErrorContext(
        ...     operation="merge_datasets_via_continuous",
        ...     additional_info={
        ...         "left_dataset_unit": "meter",
        ...         "right_dataset_unit": "feet",
        ...         "requirement": "consistent_depth_units"
        ...     }
        ... )
        >>> raise WpDepthUnitMismatchError("合并数据集要求相同的深度单位", context=ctx)
    """


# ------------------------------------------------------------
# 数据集相关异常（简化设计）
# ------------------------------------------------------------


class WpUnknownDatasetError(WpError):
    """请求的数据集类型未知。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="create_dataset",
        ...     dataset_type="UNKNOWN_TYPE",
        ...     additional_info={
        ...         "available_types": ["Continuous", "Point", "Interval"]
        ...     }
        ... )
        >>> raise WpUnknownDatasetError("未知数据集类型", context=ctx)
    """


class WpDuplicateColumnError(WpError):
    """同名列冲突（Writer 或 merge 操作）。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="merge_datasets",
        ...     column_name="DEPTH",
        ...     additional_info={
        ...         "conflicting_datasets": ["OBMIQ_logs", "K_Label"],
        ...         "column_types": ["float64", "int64"]
        ...     }
        ... )
        >>> raise WpDuplicateColumnError("列名冲突", context=ctx)
    """


# ------------------------------------------------------------
# Exception Groups 支持（Python 3.11+）
# ------------------------------------------------------------


def create_exception_group(
    message: str,
    exceptions: list[Exception]
) -> ExceptionGroup[Exception]:
    """创建异常组，支持批量异常处理。

    Architecture
    ------------
    层次/依赖: 工具函数，支持Exception Groups (PEP 654)
    设计原则: 批量错误处理、错误聚合、诊断友好
    性能特征: 轻量级包装，保持原始异常信息

    Args:
        message: 异常组描述信息
        exceptions: 异常列表

    Returns:
        ExceptionGroup: Python 3.11+ 异常组

    Examples:
        >>> # 批量文件处理错误
        >>> errors = []
        >>> for file_path in file_paths:
        ...     try:
        ...         process_file(file_path)
        ...     except WpError as e:
        ...         errors.append(e)
        >>> if errors:
        ...     raise create_exception_group("批量文件处理失败", errors)

        >>> # GPU计算批量错误
        >>> gpu_errors = []
        >>> for dataset in datasets:
        ...     try:
        ...         process_on_gpu(dataset)
        ...     except WpGpuError as e:
        ...         gpu_errors.append(e)
        >>> if gpu_errors:
        ...     raise create_exception_group("GPU批量计算失败", gpu_errors)

    Notes:
        - 要求 Python 3.11+
        - 保持原始异常的类型和上下文信息
        - 支持嵌套异常组
        - 便于错误分析和UI展示
    """
    # 项目要求 Python 3.11+，直接使用 ExceptionGroup
    return ExceptionGroup(message, exceptions)


# ------------------------------------------------------------
# WFS v1.0 新增异常族（基于WFS规范）
# ------------------------------------------------------------


class WpCompError(WpError):
    """COMP类型数据处理错误基类。

    处理COMP类型复合数据结构的解析、验证和处理错误。

    Architecture
    ------------
    层次/依赖: COMP类型处理层专用异常
    设计原则: 结构化诊断、JSON错误定位、类型安全
    性能特征: 支持复杂数据结构错误诊断
    """

    def __init__(
        self,
        message: str,
        *,
        context: ErrorContext | None = None,
        comp_context: CompContext | None = None
    ) -> None:
        super().__init__(message, context=context)
        self.comp_context = comp_context

    def __str__(self) -> str:
        """返回包含COMP上下文信息的异常描述。"""
        base_msg = super().__str__()
        if not self.comp_context:
            return base_msg

        comp_parts = []
        if self.comp_context.attribute_name:
            comp_parts.append(f"属性: {self.comp_context.attribute_name}")
        if self.comp_context.field_path:
            comp_parts.append(f"字段路径: {self.comp_context.field_path}")
        if self.comp_context.validation_error:
            comp_parts.append(f"验证错误: {self.comp_context.validation_error}")

        if comp_parts:
            return f"{base_msg} [COMP: {', '.join(comp_parts)}]"
        return base_msg


class WpCompValidationError(WpCompError):
    """COMP类型数据验证失败。

    Examples:
        >>> comp_ctx = CompContext(
        ...     attribute_name="T2_AXIS",
        ...     field_path="T2_Start.v",
        ...     validation_error="值必须为正数",
        ...     json_content='{"v": -0.1, "u": "ms", "t": "FLOAT"}'
        ... )
        >>> ctx = ErrorContext(
        ...     operation="validate_comp_structure",
        ...     dataset_name="OBMIQ_logs",
        ...     additional_info={"validator": "pydantic"}
        ... )
        >>> raise WpCompValidationError("COMP类型验证失败", context=ctx, comp_context=comp_ctx)
    """


class WpT2AxisError(WpCompError):
    """T2_AXIS预定义属性处理错误。

    Examples:
        >>> comp_ctx = CompContext(
        ...     attribute_name="T2_AXIS",
        ...     validation_error="T2_End必须大于T2_Start",
        ...     expected_structure={"T2_Start": 0.1, "T2_End": 3000},
        ...     actual_structure={"T2_Start": 3000, "T2_End": 0.1}
        ... )
        >>> ctx = ErrorContext(
        ...     operation="process_t2_axis",
        ...     dataset_name="NMR_logs",
        ...     curve_name="T2_SPECTRUM"
        ... )
        >>> raise WpT2AxisError("T2轴定义无效", context=ctx, comp_context=comp_ctx)
    """


class WpCurve2DError(WpError):
    """二维组合曲线处理错误。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="reconstruct_2d_curve",
        ...     dataset_name="NMR_logs",
        ...     curve_name="T2_VALUE",
        ...     additional_info={
        ...         "expected_elements": 64,
        ...         "found_elements": 32,
        ...         "missing_indices": [33, 34, 35]
        ...     }
        ... )
        >>> raise WpCurve2DError("二维组合曲线元素不完整", context=ctx)
    """


class WpAttributeError(WpError):
    """属性查找和管理错误基类。

    处理属性查找、作用域管理和继承机制相关错误。
    """


class WpAttributeNotFoundError(WpAttributeError):
    """属性不存在错误。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="get_attribute",
        ...     dataset_name="OBMIQ_logs",
        ...     additional_info={
        ...         "attribute_name": "MISSING_ATTR",
        ...         "search_scope": "curve",
        ...         "available_attributes": ["T2_AXIS", "Version"]
        ...     }
        ... )
        >>> raise WpAttributeNotFoundError("属性不存在", context=ctx)
    """


class WpAttributeScopeError(WpAttributeError):
    """属性作用域冲突或无效。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="resolve_attribute_scope",
        ...     dataset_name="OBMIQ_logs",
        ...     additional_info={
        ...         "conflicting_scopes": ["well", "curve"],
        ...         "attribute_name": "T2_AXIS",
        ...         "resolution_strategy": "curve_priority"
        ...     }
        ... )
        >>> raise WpAttributeScopeError("属性作用域冲突", context=ctx)
    """


class WpWellMappingError(WpError):
    """井名映射处理错误基类。

    处理井名映射、重映射和验证相关错误。
    """


class WpCircularMappingError(WpWellMappingError):
    """循环井名映射错误。

    Examples:
        >>> ctx = ErrorContext(
        ...     operation="validate_well_mapping",
        ...     sheet_name="_Well_Map",
        ...     additional_info={
        ...         "mapping_chain": ["WELL_A", "WELL_B", "WELL_C", "WELL_A"],
        ...         "cycle_start": "WELL_A",
        ...         "cycle_length": 3
        ...     }
        ... )
        >>> raise WpCircularMappingError("检测到循环井名映射", context=ctx)
    """
