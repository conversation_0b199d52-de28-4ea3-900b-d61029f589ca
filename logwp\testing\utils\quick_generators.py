"""快速数据生成器 - 测试专用

提供快速生成测试用DataFrame和元数据的工具函数。

Examples
--------
>>> # 生成连续型DataFrame
>>> df = quick_continuous_df(
...     well_name="W-1",
...     start_depth=2500,
...     end_depth=2510,
...     interval=0.5,
...     GR=lambda d: 50 + d*0.01,  # 函数形式
...     PHIT=0.15,                 # 常数形式
...     FACIES=["砂岩", "泥岩"] * 10  # 列表形式
... )
>>>
>>> # 生成快速元数据
>>> metadata = quick_metadata("WELL", "MD", "GR", "PHIT", "FACIES")
"""

from __future__ import annotations

import pandas as pd
import numpy as np
from typing import Any, Callable

from logwp.models.curve import CurveMetadata, CurveBasicAttributes
from logwp.models.constants import WpDataType, WpCurveCategory, WpCurveClass, WpDepthUnit, WpDepthR<PERSON>


def quick_continuous_df(
    well_name: str = "W-1",
    start_depth: float = 2500.0,
    end_depth: float = 2510.0,
    interval: float = 0.5,
    **curves: Any
) -> pd.DataFrame:
    """快速生成连续型DataFrame。

    Args:
        well_name: 井名
        start_depth: 起始深度
        end_depth: 结束深度
        interval: 深度间隔
        **curves: 曲线数据，支持常数、函数、列表

    Returns:
        pd.DataFrame: 生成的DataFrame

    Examples:
        >>> df = quick_continuous_df(
        ...     well_name="W-1",
        ...     start_depth=2500,
        ...     end_depth=2505,
        ...     interval=0.5,
        ...     GR=lambda d: 50 + d*0.01,
        ...     PHIT=0.15,
        ...     FACIES=["砂岩", "泥岩"] * 5
        ... )
    """
    depths = np.arange(start_depth, end_depth + interval, interval)
    n_points = len(depths)

    data = {
        'WELL': [well_name] * n_points,
        'MD': depths
    }

    for curve_name, curve_value in curves.items():
        if callable(curve_value):
            # 函数形式：curve_value(depth) -> value
            data[curve_name] = [curve_value(d) for d in depths]
        elif isinstance(curve_value, (list, np.ndarray)):
            # 列表形式：重复或截取到合适长度
            if len(curve_value) >= n_points:
                data[curve_name] = curve_value[:n_points]
            else:
                # 重复列表直到足够长度
                repeats = (n_points // len(curve_value)) + 1
                extended = (curve_value * repeats)[:n_points]
                data[curve_name] = extended
        else:
            # 常数形式：所有点使用相同值
            data[curve_name] = [curve_value] * n_points

    return pd.DataFrame(data)


def quick_discrete_df(
    well_name: str = "W-1",
    depths: list[float] | None = None,
    **curves: Any
) -> pd.DataFrame:
    """快速生成离散型DataFrame。

    Args:
        well_name: 井名
        depths: 深度列表
        **curves: 曲线数据

    Returns:
        pd.DataFrame: 生成的DataFrame
    """
    if depths is None:
        depths = [2500.5, 2502.3, 2505.1]

    n_points = len(depths)
    data = {
        'WELL': [well_name] * n_points,
        'MD': depths
    }

    for curve_name, curve_value in curves.items():
        if isinstance(curve_value, (list, np.ndarray)):
            if len(curve_value) >= n_points:
                data[curve_name] = curve_value[:n_points]
            else:
                # 重复列表
                repeats = (n_points // len(curve_value)) + 1
                extended = (curve_value * repeats)[:n_points]
                data[curve_name] = extended
        else:
            data[curve_name] = [curve_value] * n_points

    return pd.DataFrame(data)


def quick_interval_df(
    well_name: str = "W-1",
    intervals: list[tuple[float, float]] | None = None,
    **curves: Any
) -> pd.DataFrame:
    """快速生成区间型DataFrame。

    Args:
        well_name: 井名
        intervals: 区间列表 [(top, bottom), ...]
        **curves: 曲线数据

    Returns:
        pd.DataFrame: 生成的DataFrame
    """
    if intervals is None:
        intervals = [(2500.0, 2505.0), (2505.0, 2510.0), (2510.0, 2515.0)]

    n_intervals = len(intervals)
    data = {
        'WELL': [well_name] * n_intervals,
        'MD_Top': [i[0] for i in intervals],
        'MD_Bottom': [i[1] for i in intervals]
    }

    for curve_name, curve_value in curves.items():
        if isinstance(curve_value, (list, np.ndarray)):
            if len(curve_value) >= n_intervals:
                data[curve_name] = curve_value[:n_intervals]
            else:
                # 重复列表
                repeats = (n_intervals // len(curve_value)) + 1
                extended = (curve_value * repeats)[:n_intervals]
                data[curve_name] = extended
        else:
            data[curve_name] = [curve_value] * n_intervals

    return pd.DataFrame(data)


def quick_metadata(*curve_names: str) -> CurveMetadata:
    """快速生成曲线元数据。

    根据曲线名称自动推断数据类型和类别。

    Args:
        *curve_names: 曲线名称列表

    Returns:
        CurveMetadata: 生成的曲线元数据

    Examples:
        >>> metadata = quick_metadata("WELL", "MD", "GR", "PHIT", "FACIES")
        >>> assert metadata.has_curve("WELL")
        >>> assert metadata.has_curve("MD")
    """
    metadata = CurveMetadata()

    for curve_name in curve_names:
        # 根据名称推断属性
        if curve_name.upper() in ["WELL", "WELL_NAME", "WELL_NO"]:
            # 井名曲线
            attrs = CurveBasicAttributes.create_well_identifier_curve(name=curve_name)
        elif curve_name.upper() in ["MD", "DEPTH", "TVD", "TVDSS"]:
            # 深度曲线
            attrs = CurveBasicAttributes.create_depth_reference_curve(name=curve_name)
        elif curve_name.upper() in ["MD_TOP", "DEPTH_TOP", "TOP_MD"]:
            # 顶深曲线
            attrs = CurveBasicAttributes.create_1d_curve(
                name=curve_name,
                unit=WpDepthUnit.METER,
                category=WpCurveCategory.IDENTIFIER,
                depth_role=WpDepthRole.TOP
            )
        elif curve_name.upper() in ["MD_BOTTOM", "DEPTH_BOTTOM", "BOT_MD", "BOTTOM_MD"]:
            # 底深曲线
            attrs = CurveBasicAttributes.create_1d_curve(
                name=curve_name,
                unit=WpDepthUnit.METER,
                category=WpCurveCategory.IDENTIFIER,
                depth_role=WpDepthRole.BOTTOM
            )
        elif curve_name.upper() in ["FACIES", "LITHOLOGY", "ZONE", "FORMATION"]:
            # 类别型曲线
            attrs = CurveBasicAttributes.create_1d_curve(
                name=curve_name,
                data_type=WpDataType.STR,
                category=WpCurveCategory.COMPUTED,
                curve_class=WpCurveClass.CATEGORICAL
            )
        else:
            # 默认为数值型测井曲线
            attrs = CurveBasicAttributes.create_1d_curve(
                name=curve_name,
                category=WpCurveCategory.LOGGING
            )

        metadata.add_curve(attrs)

    return metadata
