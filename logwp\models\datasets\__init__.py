from __future__ import annotations

"""logwp.models.datasets - 数据集层

现代化数据集实现，提供抽象基类和三类数据集。

Architecture
------------
层次/依赖: logwp包数据集层，依赖types、models、constants、exceptions
设计原则: 抽象基类、类型安全、WFS规范兼容、简化设计
性能特征: 内存优化、延迟加载、GPU支持、异步I/O

Core Features
-------------
- **WpDepthIndexedDatabaseBase**: 抽象基类，定义数据集核心接口
- **WpContinuousDataset**: 连续型数据集，支持测井曲线数据
- **WpDiscreteDataset**: 离散型数据集，支持不等间距采样的点数据（对应WFS Point类型）
- **WpIntervalDataset**: 区间型数据集，支持层状数据的双深度索引

Package Structure
-----------------
- base: WpDepthIndexedDatabaseBase抽象基类
- continuous: WpContinuousDataset连续型数据集
- discrete: WpDiscreteDataset离散型数据集
- interval: WpIntervalDataset区间型数据集
- service: 数据集服务层，提供复杂操作的stateless函数

Examples
--------
>>> from logwp.models.datasets import WpContinuousDataset
>>>
>>> # 创建连续型数据集
>>> dataset = WpContinuousDataset(
...     name="OBMIQ_logs",
...     df=logs_df
... )
>>>
>>> # 直接使用具体类型，避免工厂模式复杂性
>>> discrete_dataset = WpDiscreteDataset(
...     name="K_Label",
...     df=label_df
... )

References
----------
- 《SCAPE_DDS_详细设计_logwp.md》§3.2 - datasets层设计
- 《SCAPE_WFS_WP文件规范.md》A.3 - 数据集类型定义
"""

# 抽象基类
from .base import WpIndexedDatasetBase, WpDepthIndexedDatasetBase

# 具体数据集实现
from .continuous import WpContinuousDataset
from .discrete import WpDiscreteDataset
from .interval import WpIntervalDataset

__all__ = [
    # 抽象基类
    "WpIndexedDatasetBase",
    "WpDepthIndexedDatasetBase",

    # 具体数据集
    "WpContinuousDataset",
    "WpDiscreteDataset",
    "WpIntervalDataset",
]
