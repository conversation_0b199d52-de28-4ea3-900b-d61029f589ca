"""PCA可视化服务。

实现PCA结果的可视化功能，包括方差解释图、散点图、热图等。
支持多种图像格式和自定义配置。

Architecture
------------
层次/依赖: PCA内部服务层，可视化
设计原则: 科学可视化、多格式支持、用户友好
性能特征: 高分辨率、矢量格式、快速渲染
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import TYPE_CHECKING, Any

import numpy as np
import structlog

# 可视化库的条件导入
try:
    import matplotlib.pyplot as plt
    import matplotlib.colors as mcolors
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None
    mcolors = None
    Figure = None

try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False
    sns = None

from ..constants import (
    DEFAULT_DPI,
    DEFAULT_FIGURE_SIZE,
    DEFAULT_IMAGE_FORMAT,
    DEFAULT_BBOX_INCHES,
    SUPPORTED_IMAGE_FORMATS,
    DEFAULT_COLORMAP,
    VARIANCE_COLOR,
    CUMULATIVE_COLOR,
)
from ..exceptions import (
    WpPcaVisualizationError,
    WpPcaErrorContext,
)

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase
    from .model import PCAParameters

logger = structlog.get_logger(__name__)

# =============================================================================
# 可视化配置类
# =============================================================================

@dataclass
class SaveConfig:
    """图像保存配置类。

    Architecture
    ------------
    层次/依赖: PCA可视化配置，数据容器模式
    设计原则: 不可变配置、类型安全、默认值友好
    性能特征: 轻量级配置、快速访问

    Attributes:
        filename: 图像文件保存路径
        format: 图像格式（png、jpg、pdf、svg等）
        dpi: 图像分辨率，默认300
        bbox_inches: 边界框设置，默认'tight'
        quality: 图像质量（仅jpg格式），可选
        transparent: 是否透明背景，默认False
    """
    filename: str | Path
    format: str = "png"
    dpi: int = 300
    bbox_inches: str = "tight"
    quality: int | None = None
    transparent: bool = False

    def __post_init__(self) -> None:
        """验证配置参数有效性。"""
        if self.dpi <= 0:
            raise ValueError(f"DPI必须大于0，当前值: {self.dpi}")

        valid_formats = {"png", "jpg", "jpeg", "pdf", "svg", "eps"}
        if self.format.lower() not in valid_formats:
            raise ValueError(f"不支持的图像格式: {self.format}")

        if self.quality is not None and not (0 <= self.quality <= 100):
            raise ValueError(f"图像质量必须在0-100之间，当前值: {self.quality}")


# =============================================================================
# 可视化函数
# =============================================================================

# TODO: 在任务3.2中实现以下功能

def plot_variance_explained(
    pca_model: PCAParameters,
    save_config: SaveConfig | None = None
) -> None:
    """绘制主成分方差贡献图。

    Architecture
    ------------
    层次/依赖: PCA可视化器，方差分析图表
    设计原则: 科学可视化、信息丰富、美观实用
    性能特征: 高分辨率、快速渲染、多格式支持

    Args:
        pca_model: PCA模型，包含每个主成分的方差贡献
        save_config: 保存图表的配置，None表示不保存

    Raises:
        WpPcaVisualizationError: 可视化异常
    """
    if not MATPLOTLIB_AVAILABLE:
        raise WpPcaVisualizationError(
            "matplotlib未安装，无法进行可视化。请安装: pip install matplotlib"
        )

    try:
        logger.info(
            "开始绘制方差解释图",
            n_components=pca_model["n_components"],
            pca_curve_name=pca_model["pca_curve_name"]
        )

        # 1. 数据准备
        explained_variance_ratio = pca_model["explained_variance_ratio"]
        cumulative_variance = np.cumsum(explained_variance_ratio)
        n_components = len(explained_variance_ratio)
        component_labels = [f"PC{i+1}" for i in range(n_components)]

        # 2. 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=DEFAULT_FIGURE_SIZE,
                                       gridspec_kw={'height_ratios': [2, 1]})

        # 2.1 个体方差解释比例（柱状图）
        bars = ax1.bar(component_labels, explained_variance_ratio * 100,
                       color=VARIANCE_COLOR, alpha=0.7, edgecolor='black', linewidth=0.5)

        ax1.set_title(f'PCA方差解释分析 - {pca_model["pca_curve_name"]}',
                      fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('方差解释比例 (%)', fontsize=12)
        ax1.grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, ratio in zip(bars, explained_variance_ratio):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{ratio*100:.1f}%', ha='center', va='bottom', fontsize=10)

        # 2.2 累积方差解释比例（折线图）
        line = ax2.plot(component_labels, cumulative_variance * 100,
                        color=CUMULATIVE_COLOR, marker='o', linewidth=2, markersize=6)

        ax2.set_xlabel('主成分', fontsize=12)
        ax2.set_ylabel('累积方差解释比例 (%)', fontsize=12)
        ax2.grid(True, alpha=0.3)

        # 添加重要阈值线
        ax2.axhline(y=95, color='red', linestyle='--', alpha=0.7, label='95%阈值')
        ax2.axhline(y=90, color='orange', linestyle='--', alpha=0.7, label='90%阈值')
        ax2.legend(loc='lower right')

        # 添加累积值标签
        for i, cum_var in enumerate(cumulative_variance):
            if i % max(1, n_components // 10) == 0 or i == n_components - 1:  # 避免标签过密
                ax2.text(i, cum_var * 100 + 1, f'{cum_var*100:.1f}%',
                        ha='center', va='bottom', fontsize=9)

        # 3. 图表美化
        plt.tight_layout()

        # 添加统计信息文本
        stats_text = (
            f"总主成分数: {n_components}\n"
            f"前5个主成分累积方差: {cumulative_variance[min(4, n_components-1)]*100:.1f}%\n"
            f"总累积方差: {cumulative_variance[-1]*100:.1f}%"
        )
        fig.text(0.02, 0.02, stats_text, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))

        # 4. 保存图像
        if save_config:
            save_figure_with_config(fig, save_config)
        else:
            plt.show()

        logger.info(
            "方差解释图绘制完成",
            n_components=n_components,
            total_variance=float(cumulative_variance[-1]),
            saved=save_config is not None
        )

    except Exception as e:
        error_context = WpPcaErrorContext(
            operation="plot_variance_explained",
            stage="visualization",
            n_components=pca_model["n_components"]
        )

        if isinstance(e, WpPcaVisualizationError):
            e.context = error_context
            raise
        else:
            raise WpPcaVisualizationError(
                f"绘制方差解释图时发生错误: {str(e)}",
                context=error_context
            ) from e
    finally:
        if MATPLOTLIB_AVAILABLE:
            plt.close('all')  # 清理图形资源


def plot_pca_scatter(
    pca_dataset: WpDepthIndexedDatasetBase,
    n_components: int = 2,
    save_config: SaveConfig | None = None
) -> None:
    """绘制PCA降维结果的散点图。

    Architecture
    ------------
    层次/依赖: PCA可视化器，降维结果展示
    设计原则: 直观展示、交互友好、信息丰富
    性能特征: 大数据支持、快速渲染、美观布局

    Args:
        pca_dataset: 降维后的数据集
        n_components: 要展示的主成分数量（2或3）
        save_config: 保存图表的配置，None表示不保存

    Raises:
        WpPcaVisualizationError: 可视化异常
    """
    if not MATPLOTLIB_AVAILABLE:
        raise WpPcaVisualizationError(
            "matplotlib未安装，无法进行可视化。请安装: pip install matplotlib"
        )

    try:
        logger.info(
            "开始绘制PCA散点图",
            dataset_name=pca_dataset.name,
            n_components=n_components
        )

        # 1. 数据提取和验证
        validate_visualization_input(pca_dataset, "scatter", n_components)

        # 获取PCA曲线数据
        pca_curves = []
        for curve_name, curve in pca_dataset.curve_metadata.curves.items():
            if curve.category.value == "computed":  # PCA曲线通常是computed类型
                pca_curves.append(curve_name)

        if not pca_curves:
            raise WpPcaVisualizationError("数据集中未找到PCA曲线")

        pca_curve_name = pca_curves[0]  # 使用第一个PCA曲线
        pca_columns = pca_dataset.curve_metadata.get_dataframe_columns_for_curves([pca_curve_name])

        if len(pca_columns) < n_components:
            raise WpPcaVisualizationError(
                f"PCA数据维度({len(pca_columns)})小于要求的展示维度({n_components})"
            )

        # 提取前n_components个主成分数据
        pca_data = pca_dataset.df[pca_columns[:n_components]].values

        # 2. 绘制散点图
        if n_components == 2:
            # 2D散点图
            fig, ax = plt.subplots(figsize=DEFAULT_FIGURE_SIZE)

            scatter = ax.scatter(pca_data[:, 0], pca_data[:, 1],
                               c=range(len(pca_data)), cmap=DEFAULT_COLORMAP,
                               alpha=0.6, s=20, edgecolors='black', linewidth=0.1)

            ax.set_xlabel(f'第1主成分 ({pca_columns[0]})', fontsize=12)
            ax.set_ylabel(f'第2主成分 ({pca_columns[1]})', fontsize=12)
            ax.set_title(f'PCA散点图 (2D) - {pca_dataset.name}', fontsize=14, fontweight='bold')
            ax.grid(True, alpha=0.3)

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label('样本索引', fontsize=10)

        elif n_components == 3:
            # 3D散点图
            fig = plt.figure(figsize=DEFAULT_FIGURE_SIZE)
            ax = fig.add_subplot(111, projection='3d')

            scatter = ax.scatter(pca_data[:, 0], pca_data[:, 1], pca_data[:, 2],
                               c=range(len(pca_data)), cmap=DEFAULT_COLORMAP,
                               alpha=0.6, s=20, edgecolors='black', linewidth=0.1)

            ax.set_xlabel(f'第1主成分 ({pca_columns[0]})', fontsize=10)
            ax.set_ylabel(f'第2主成分 ({pca_columns[1]})', fontsize=10)
            ax.set_zlabel(f'第3主成分 ({pca_columns[2]})', fontsize=10)
            ax.set_title(f'PCA散点图 (3D) - {pca_dataset.name}', fontsize=14, fontweight='bold')

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20)
            cbar.set_label('样本索引', fontsize=10)

        else:
            raise WpPcaVisualizationError(f"不支持的维度数量: {n_components}，只支持2D或3D")

        # 3. 图表美化和统计信息
        stats_text = (
            f"样本数量: {len(pca_data)}\n"
            f"展示维度: {n_components}D\n"
            f"PCA曲线: {pca_curve_name}"
        )
        fig.text(0.02, 0.02, stats_text, fontsize=10,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))

        plt.tight_layout()

        # 4. 保存图像
        if save_config:
            save_figure_with_config(fig, save_config)
        else:
            plt.show()

        logger.info(
            "PCA散点图绘制完成",
            dataset_name=pca_dataset.name,
            n_components=n_components,
            n_samples=len(pca_data),
            saved=save_config is not None
        )

    except Exception as e:
        error_context = WpPcaErrorContext(
            operation="plot_pca_scatter",
            stage="visualization",
            dataset_name=pca_dataset.name,
            n_components=n_components
        )

        if isinstance(e, WpPcaVisualizationError):
            e.context = error_context
            raise
        else:
            raise WpPcaVisualizationError(
                f"绘制PCA散点图时发生错误: {str(e)}",
                context=error_context
            ) from e
    finally:
        if MATPLOTLIB_AVAILABLE:
            plt.close('all')  # 清理图形资源


def plot_pca_heatmap(
    pca_model: PCAParameters,
    save_config: SaveConfig | None = None
) -> None:
    """绘制PCA主成分的热图。

    Architecture
    ------------
    层次/依赖: PCA可视化器，主成分贡献分析
    设计原则: 信息密集、颜色映射、特征解释
    性能特征: 高分辨率、清晰标注、美观配色

    Args:
        pca_model: PCA模型，包含主成分矩阵
        save_config: 保存图表的配置，None表示不保存

    Raises:
        WpPcaVisualizationError: 可视化异常
    """
    if not MATPLOTLIB_AVAILABLE:
        raise WpPcaVisualizationError(
            "matplotlib未安装，无法进行可视化。请安装: pip install matplotlib"
        )

    try:
        logger.info(
            "开始绘制PCA热图",
            n_components=pca_model["n_components"],
            n_features=pca_model["n_features"]
        )

        # 1. 主成分矩阵准备
        components = pca_model["components"]
        feature_names = pca_model["feature_names"]
        n_components = pca_model["n_components"]

        # 限制显示的主成分数量（避免图表过大）
        max_components_display = min(20, n_components)
        components_display = components[:max_components_display]

        # 限制显示的特征数量
        max_features_display = min(30, len(feature_names))
        if len(feature_names) > max_features_display:
            # 选择贡献最大的特征
            feature_importance = np.sum(np.abs(components_display), axis=0)
            top_features_idx = np.argsort(feature_importance)[-max_features_display:]
            components_display = components_display[:, top_features_idx]
            feature_names_display = [feature_names[i] for i in top_features_idx]
        else:
            feature_names_display = feature_names

        # 2. 创建热图
        fig, ax = plt.subplots(figsize=(max(8, len(feature_names_display) * 0.4),
                                       max(6, max_components_display * 0.3)))

        # 使用seaborn绘制热图（如果可用）
        if SEABORN_AVAILABLE:
            sns.heatmap(components_display,
                       xticklabels=feature_names_display,
                       yticklabels=[f'PC{i+1}' for i in range(max_components_display)],
                       cmap='RdBu_r', center=0,
                       annot=True if len(feature_names_display) <= 15 else False,
                       fmt='.2f', cbar_kws={'label': '主成分载荷'},
                       ax=ax)
        else:
            # 使用matplotlib绘制热图
            im = ax.imshow(components_display, cmap='RdBu_r', aspect='auto')

            # 设置坐标轴标签
            ax.set_xticks(range(len(feature_names_display)))
            ax.set_xticklabels(feature_names_display, rotation=45, ha='right')
            ax.set_yticks(range(max_components_display))
            ax.set_yticklabels([f'PC{i+1}' for i in range(max_components_display)])

            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('主成分载荷', fontsize=12)

            # 添加数值标注（如果特征数量不太多）
            if len(feature_names_display) <= 15:
                for i in range(max_components_display):
                    for j in range(len(feature_names_display)):
                        text = ax.text(j, i, f'{components_display[i, j]:.2f}',
                                     ha="center", va="center", color="black", fontsize=8)

        # 3. 图表美化
        ax.set_title(f'PCA主成分载荷热图 - {pca_model["pca_curve_name"]}',
                    fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('特征', fontsize=12)
        ax.set_ylabel('主成分', fontsize=12)

        # 添加统计信息
        stats_text = (
            f"显示主成分: {max_components_display}/{n_components}\n"
            f"显示特征: {len(feature_names_display)}/{len(feature_names)}\n"
            f"载荷范围: [{np.min(components_display):.3f}, {np.max(components_display):.3f}]"
        )
        fig.text(0.02, 0.98, stats_text, fontsize=10, va='top',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))

        plt.tight_layout()

        # 4. 保存图像
        if save_config:
            save_figure_with_config(fig, save_config)
        else:
            plt.show()

        logger.info(
            "PCA热图绘制完成",
            components_displayed=max_components_display,
            features_displayed=len(feature_names_display),
            saved=save_config is not None
        )

    except Exception as e:
        error_context = WpPcaErrorContext(
            operation="plot_pca_heatmap",
            stage="visualization",
            n_components=pca_model["n_components"],
            n_features=pca_model["n_features"]
        )

        if isinstance(e, WpPcaVisualizationError):
            e.context = error_context
            raise
        else:
            raise WpPcaVisualizationError(
                f"绘制PCA热图时发生错误: {str(e)}",
                context=error_context
            ) from e
    finally:
        if MATPLOTLIB_AVAILABLE:
            plt.close('all')  # 清理图形资源


def create_biplot(
    pca_dataset: WpDepthIndexedDatasetBase,
    pca_model: PCAParameters,
    save_config: SaveConfig | None = None
) -> None:
    """创建PCA双标图。

    同时显示样本点和特征向量的图表。

    Args:
        pca_dataset: PCA数据集
        pca_model: PCA模型
        save_config: 保存配置
    """
    # 双标图实现较复杂，暂时不实现
    # 用户可以使用scatter图和heatmap的组合来分析
    raise NotImplementedError("双标图功能将在后续版本中实现")


def validate_visualization_input(
    data: Any,
    plot_type: str,
    n_components: int | None = None
) -> None:
    """验证可视化输入参数。

    Args:
        data: 输入数据
        plot_type: 图表类型
        n_components: 主成分数量

    Raises:
        WpPcaVisualizationError: 输入参数无效
    """
    # 基本数据验证
    if data is None:
        raise WpPcaVisualizationError("输入数据不能为None")

    # 图表类型验证
    valid_plot_types = {"variance_explained", "scatter", "heatmap", "biplot"}
    if plot_type not in valid_plot_types:
        raise WpPcaVisualizationError(f"不支持的图表类型: {plot_type}")

    # 主成分数量验证
    if n_components is not None:
        if n_components <= 0:
            raise WpPcaVisualizationError(f"主成分数量必须大于0，当前值: {n_components}")

        if plot_type == "scatter" and n_components not in [2, 3]:
            raise WpPcaVisualizationError(f"散点图只支持2D或3D，当前值: {n_components}")

    # 数据集特定验证
    if hasattr(data, 'df') and hasattr(data, 'curve_metadata'):
        if data.df.empty:
            raise WpPcaVisualizationError("数据集为空，无法进行可视化")


def save_figure_with_config(
    figure: Any,  # matplotlib.figure.Figure
    save_config: SaveConfig
) -> None:
    """使用配置保存图像。

    Args:
        figure: matplotlib图像对象
        save_config: 保存配置

    Raises:
        WpPcaVisualizationError: 图像保存失败
    """
    try:
        # 确保目录存在
        filepath = Path(save_config.filename)
        filepath.parent.mkdir(parents=True, exist_ok=True)

        # 准备保存参数
        save_kwargs = {
            'dpi': save_config.dpi,
            'bbox_inches': save_config.bbox_inches,
            'transparent': save_config.transparent,
        }

        # 添加格式特定参数
        if save_config.format.lower() in ['jpg', 'jpeg'] and save_config.quality:
            save_kwargs['quality'] = save_config.quality

        # 保存图像
        figure.savefig(filepath, format=save_config.format, **save_kwargs)

        logger.info(
            "图像保存成功",
            filepath=str(filepath),
            format=save_config.format,
            dpi=save_config.dpi,
            file_size=filepath.stat().st_size if filepath.exists() else 0
        )

    except Exception as e:
        raise WpPcaVisualizationError(f"保存图像时发生错误: {str(e)}") from e
