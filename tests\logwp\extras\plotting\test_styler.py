"""测试样式应用工具 (styler.py)。

测试apply_profile函数的功能，包括：
- matplotlib样式应用
- 分阶段样式设置
- 错误处理和容错性
- 各种配置组合
"""

import pytest

from logwp.extras.plotting import PlotProfile, apply_profile
from logwp.extras.plotting.exceptions import StyleApplicationError


class TestApplyProfile:
    """测试apply_profile函数。"""
    
    def test_apply_empty_profile(self, matplotlib_figure):
        """测试应用空配置。"""
        fig, ax = matplotlib_figure
        
        # 空配置应该不报错
        empty_profile = PlotProfile(name="empty")
        apply_profile(ax, empty_profile)
        
        # 基本验证：函数执行成功
        assert ax.get_title() == ""  # 没有设置标题
    
    def test_apply_rc_params(self, matplotlib_figure):
        """测试应用rcParams。"""
        pytest.importorskip("matplotlib")
        import matplotlib.pyplot as plt
        
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_rc",
            rc_params={
                "font.size": 14,
                "axes.grid": True,
                "grid.alpha": 0.7,
                "grid.linestyle": "--"
            }
        )
        
        # 记录原始值
        original_font_size = plt.rcParams["font.size"]
        original_grid = plt.rcParams["axes.grid"]
        
        try:
            apply_profile(ax, profile)
            
            # 验证rcParams已更新
            assert plt.rcParams["font.size"] == 14
            assert plt.rcParams["axes.grid"] is True
            assert plt.rcParams["grid.alpha"] == 0.7
            assert plt.rcParams["grid.linestyle"] == "--"
            
        finally:
            # 恢复原始值
            plt.rcParams["font.size"] = original_font_size
            plt.rcParams["axes.grid"] = original_grid
    
    def test_apply_figure_props(self, matplotlib_figure):
        """测试应用Figure属性。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_figure",
            figure_props={
                "figsize": (12, 8),
                "dpi": 200,
                "facecolor": "lightgray",
                "edgecolor": "black"
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证Figure属性
        assert fig.get_size_inches().tolist() == [12.0, 8.0]
        assert fig.get_dpi() == 200
        assert fig.patch.get_facecolor() == (0.8274509803921568, 0.8274509803921568, 0.8274509803921568, 1.0)  # lightgray的RGBA
    
    def test_apply_figure_layout(self, matplotlib_figure):
        """测试应用Figure布局。"""
        fig, ax = matplotlib_figure
        
        # 测试constrained布局
        profile_constrained = PlotProfile(
            name="test_constrained",
            figure_props={"layout": "constrained"}
        )
        
        apply_profile(ax, profile_constrained)
        
        # 验证布局引擎（matplotlib版本差异可能导致不同的表示）
        layout_engine = fig.get_layout_engine()
        assert layout_engine is not None
    
    def test_apply_title_props(self, matplotlib_figure):
        """测试应用标题属性。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_title",
            title_props={
                "label": "Test Plot Title",
                "fontsize": 18,
                "fontweight": "bold",
                "color": "red",
                "pad": 25
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证标题设置
        assert ax.get_title() == "Test Plot Title"
        title_obj = ax.title
        assert title_obj.get_fontsize() == 18
        assert title_obj.get_fontweight() == "bold"
        assert title_obj.get_color() == "red"
    
    def test_apply_title_props_no_label(self, matplotlib_figure):
        """测试应用标题样式但无标题文本。"""
        fig, ax = matplotlib_figure
        
        # 先设置一个标题
        ax.set_title("Existing Title")
        
        profile = PlotProfile(
            name="test_title_style",
            title_props={
                "fontsize": 20,
                "color": "blue"
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证样式应用到现有标题
        assert ax.get_title() == "Existing Title"
        title_obj = ax.title
        assert title_obj.get_fontsize() == 20
        assert title_obj.get_color() == "blue"
    
    def test_apply_label_props(self, matplotlib_figure):
        """测试应用坐标轴标签属性。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_labels",
            label_props={
                "xlabel": "X Axis Label",
                "ylabel": "Y Axis Label",
                "fontsize": 14,
                "color": "green",
                "fontweight": "bold"
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证标签设置
        assert ax.get_xlabel() == "X Axis Label"
        assert ax.get_ylabel() == "Y Axis Label"
        
        xlabel_obj = ax.xaxis.label
        ylabel_obj = ax.yaxis.label
        assert xlabel_obj.get_fontsize() == 14
        assert xlabel_obj.get_color() == "green"
        assert ylabel_obj.get_fontsize() == 14
        assert ylabel_obj.get_color() == "green"
    
    def test_apply_label_props_style_only(self, matplotlib_figure):
        """测试仅应用标签样式。"""
        fig, ax = matplotlib_figure
        
        # 先设置标签
        ax.set_xlabel("Existing X")
        ax.set_ylabel("Existing Y")
        
        profile = PlotProfile(
            name="test_label_style",
            label_props={
                "fontsize": 16,
                "color": "purple"
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证样式应用到现有标签
        assert ax.get_xlabel() == "Existing X"
        assert ax.get_ylabel() == "Existing Y"
        
        xlabel_obj = ax.xaxis.label
        ylabel_obj = ax.yaxis.label
        assert xlabel_obj.get_fontsize() == 16
        assert xlabel_obj.get_color() == "purple"
        assert ylabel_obj.get_fontsize() == 16
        assert ylabel_obj.get_color() == "purple"
    
    def test_apply_axes_styling_grid(self, matplotlib_figure):
        """测试应用坐标轴和网格样式。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_axes",
            rc_params={
                "axes.grid": True,
                "grid.linestyle": ":",
                "grid.alpha": 0.6,
                "grid.color": "gray",
                "grid.linewidth": 0.8
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证网格设置
        # 注意：matplotlib的网格属性检查比较复杂，这里做基本验证
        assert ax.xaxis.get_gridlines()[0].get_visible() or ax.yaxis.get_gridlines()[0].get_visible()
    
    def test_apply_axes_styling_ticks(self, matplotlib_figure):
        """测试应用刻度样式。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_ticks",
            rc_params={
                "xtick.minor.visible": True,
                "ytick.minor.visible": True
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证次要刻度可见性
        # 这个测试可能因matplotlib版本而异，做基本验证
        assert True  # 函数执行成功即可
    
    def test_apply_axes_styling_spines(self, matplotlib_figure):
        """测试应用边框样式。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_spines",
            rc_params={
                "axes.spines.top": False,
                "axes.spines.right": False,
                "axes.spines.bottom": True,
                "axes.spines.left": True
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证边框可见性
        assert not ax.spines['top'].get_visible()
        assert not ax.spines['right'].get_visible()
        assert ax.spines['bottom'].get_visible()
        assert ax.spines['left'].get_visible()
    
    def test_apply_complete_profile(self, matplotlib_figure):
        """测试应用完整配置。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="test_complete",
            rc_params={
                "font.size": 12,
                "axes.grid": True,
                "grid.alpha": 0.5
            },
            figure_props={
                "figsize": (10, 6),
                "dpi": 120
            },
            title_props={
                "label": "Complete Test Plot",
                "fontsize": 16,
                "fontweight": "bold"
            },
            label_props={
                "xlabel": "X Data",
                "ylabel": "Y Data",
                "fontsize": 12
            }
        )
        
        apply_profile(ax, profile)
        
        # 验证所有设置
        assert fig.get_size_inches().tolist() == [10.0, 6.0]
        assert fig.get_dpi() == 120
        assert ax.get_title() == "Complete Test Plot"
        assert ax.get_xlabel() == "X Data"
        assert ax.get_ylabel() == "Y Data"
    
    def test_apply_profile_error_handling(self, matplotlib_figure):
        """测试错误处理。"""
        fig, ax = matplotlib_figure
        
        # 创建可能导致错误的配置
        problematic_profile = PlotProfile(
            name="problematic",
            rc_params={
                "font.family": "NonexistentFont",  # 不存在的字体
                "invalid.param": "invalid_value"   # 无效参数
            },
            figure_props={
                "figsize": (-1, -1),  # 无效尺寸
            }
        )
        
        # 应该能够处理错误而不崩溃
        try:
            apply_profile(ax, problematic_profile)
            # 如果没有抛出异常，说明错误处理正常
        except StyleApplicationError:
            # 如果抛出StyleApplicationError，也是可接受的
            pass
    
    def test_apply_profile_partial_failure(self, matplotlib_figure):
        """测试部分失败的容错性。"""
        fig, ax = matplotlib_figure
        
        profile = PlotProfile(
            name="partial_fail",
            rc_params={
                "font.size": 14,           # 有效
                "invalid.param": "value"   # 无效
            },
            title_props={
                "label": "Valid Title",    # 有效
                "fontsize": 16             # 有效
            }
        )
        
        # 应该能够应用有效部分
        apply_profile(ax, profile)
        
        # 验证有效部分已应用
        assert ax.get_title() == "Valid Title"
        title_obj = ax.title
        assert title_obj.get_fontsize() == 16
