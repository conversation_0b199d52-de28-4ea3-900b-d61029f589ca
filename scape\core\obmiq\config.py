from __future__ import annotations

from pydantic import BaseModel, Field, PositiveInt, conint, confloat, conlist


class ObmiqTrainingConfig(BaseModel):
    """
    OBMIQ (PyTorch版) 训练步骤的流程控制配置。

    该配置模型不定义超参数的搜索空间（由Optuna在代码中动态定义），
    而是控制整个训练和寻优流程的行为。
    """

    # Reproducibility
    random_seed: int = Field(2025, description="用于所有随机操作的全局种子，确保可复现性")

    # Optuna Hyperparameter Tuning
    n_trials: PositiveInt = Field(
        100, description="Optuna超参数搜索的总试验次数"
    )

    # Training Loop Control
    max_epochs_per_trial: PositiveInt = Field(
        50, description="在超参数寻优的单次试验中，模型训练的最大轮次"
    )
    final_train_epochs: PositiveInt = Field(
        150, description="在找到最佳超参数后，最终模型训练的最大轮次"
    )
    patience: PositiveInt = Field(
        10,
        description="早停机制的耐心轮次数。如果在patience个轮次内验证集性能没有提升，则停止训练。",
    )
    batch_size: conint(gt=0) = Field(
        64, description="训练和验证时使用的批处理大小"
    )

    # Cross-Validation Data Split
    val_split_ratio: confloat(gt=0, lt=1) = Field(
        0.2, description="在每个LOWO-CV折内部，用于从训练集中划分出验证集的比例"
    )

    # --- Optional Hyperparameter Search Space ---
    # 用户可以通过修改这些值来覆盖代码中的默认搜索范围。
    # 2024-06-26: 增加更小的选项(8)，允许模型探索更简单的架构以对抗过拟合。
    hp_cnn_filters: conlist(int, min_length=1) = Field(
        default_factory=lambda: [8, 16, 32, 64], description="CNN卷积核数量的搜索范围"
    )
    hp_cnn_kernel_size: conlist(int, min_length=1) = Field(
        default_factory=lambda: [3, 5, 7], description="CNN卷积核尺寸的搜索范围"
    )
    # 2024-06-26: 增加更小的选项(8)，允许模型探索更简单的架构以对抗过拟合。
    # 2024-07-01: 调整为两层MLP，分别为两层定义搜索空间
    hp_mlp_units_1: conlist(int, min_length=1) = Field(
        default_factory=lambda: [16, 32, 64],
        description="MLP第一个隐藏层神经元数量的搜索范围",
    )
    hp_mlp_units_2: conlist(int, min_length=1) = Field(
        default_factory=lambda: [8, 16, 32],
        description="MLP第二个隐藏层神经元数量的搜索范围",
    )
    # 2024-06-26: 扩大Dropout搜索范围，探索更强的正则化效果以对抗过拟合。
    hp_dropout_rate: conlist(float, min_length=2, max_length=2) = Field(
        default_factory=lambda: [0.2, 0.6],
        description="Dropout比率的浮点数搜索范围 [min, max]",
    )
    hp_learning_rate: conlist(float, min_length=2, max_length=2) = Field(
        default_factory=lambda: [1e-4, 1e-2],
        description="学习率的浮点数搜索范围 [min, max]",
    )
    # 2024-06-26: 新增权重衰减(L2正则化)作为超参数，以对抗过拟合。
    hp_weight_decay: conlist(float, min_length=2, max_length=2) = Field(
        default_factory=lambda: [1e-5, 1e-3],
        description="权重衰减(L2正则化)的浮点数搜索范围 [min, max]",
    )


class ObmiqPredictionConfig(BaseModel):
    """
    OBMIQ (PyTorch版) 预测步骤的配置。

    目前为空，为未来可能增加的预测时参数（如推理批处理大小）保留扩展性。
    """

    pass
