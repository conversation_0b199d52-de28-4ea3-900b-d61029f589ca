# 文件路径: scape/core/foster_nmr/calculate_foster_nmr.py

from __future__ import annotations

from typing import TYPE_CHECKING, Optional, Tuple, Dict, Any

import numpy as np
import pandas as pd

from logwp.extras.nmr.nmr_utils import calculate_t2_porosity_components_vectorized
from logwp.extras.backend import BackendService

if TYPE_CHECKING:
    # 仅在类型检查时导入，以避免循环导入
    from ..swift_pso.model_assets import FosterNmrModelAssets

def calculate_foster_nmr_permeability_for_val(
        parameters: Dict[str, Any],
        well_data: pd.DataFrame,
        t2_time:  np.ndarray,
        config: Dict[str, Any],
        *,
        t2_p50_ref: float,
        phit_nmr_ref: float,
        t2_range_min: Optional[float] = None,
        t2_range_max: Optional[float] = None,
        backend_service: BackendService
) -> Any:
    """
    (PSO优化场景) 根据FOSTER-NMR模型计算最终的核磁共振渗透率 K_NMR。

    该函数专为PSO优化流程设计，严格遵循《SCAPE_MS_方法说明书》第3节定义的5个计算步骤。
    它接收包含K_LABEL_TYPE的训练数据，支持MDT修正计算。
    内部调用 `_calculate_foster_nmr_core` 执行核心计算逻辑。

    Args:
        parameters (dict): 包含全部10个待优化模型参数的字典。
            - 'log10_KSDR_A', 'PHIT_EXP', 'RHO_NMR', 'log10_KMACRO_A', 'Vmacro_min',
            - 'log10_T2cutoff_short', 'log10_T2cutoff_long',
            - 'beta_1', 'beta_2', 'delta_MDT'

        well_data (pd.DataFrame): 包含一口井所有深度点的测井曲线DataFrame。
            - 'PHIT_NMR', 'T2LM', 'DT2_P50', 'T2_P50', 'DPHIT_NMR',
            - 'T2_VALUE' (T2谱，以多列形式存储), 'K_LABEL_TYPE' (用于MDT修正)

        t2_time (np.array): T2时间轴数组，单位ms，必须与T2谱的列数一致且单调递增。

        config (dict): 包含上下文元数据的配置字典。
            - 必须包含 'curve_to_columns_map'，用于解释二维曲线。

        t2_p50_ref (float): T2_P50的中位数参考值。
        phit_nmr_ref (float): PHIT_NMR的中位数参考值。

        t2_range_min (float, optional): T2时间轴计算范围最小值，单位ms，默认为None（使用全部范围）。
        t2_range_max (float, optional): T2时间轴计算范围最大值，单位ms，默认为None（使用全部范围）。
        backend_service (BackendService): 用于计算的后端服务实例。

    Returns:
        Any: 在所有深度点上计算出的最终渗透率 K_NMR (mD)，数组类型取决于后端。
    """
    # --- 步骤 0: 准备输入数据 ---

    # 从well_data中提取一维测井曲线
    phit_nmr = well_data['PHIT_NMR'].to_numpy()
    t2lm = well_data['T2LM'].to_numpy()
    dt2_p50 = well_data['DT2_P50'].to_numpy()
    dphit_nmr = well_data['DPHIT_NMR'].to_numpy()
    sample_types = well_data['K_LABEL_TYPE'].to_numpy()

    # 根据config中的元数据，从DataFrame中组装出二维T2谱数组
    curve_map = config.get('curve_to_columns_map')
    if not curve_map:
        raise ValueError("配置字典 'config' 中缺少 'curve_to_columns_map' 元数据。")
    t2_value_columns = curve_map.get('T2_VALUE')
    if not t2_value_columns:
        raise ValueError("'curve_to_columns_map' 中缺少 'T2_VALUE' 曲线的列映射。")
    t2_value_array = well_data[t2_value_columns].to_numpy()

    # --- 步骤 1: 调用核心计算引擎 ---
    # 职责: 将准备好的数据和参数传递给纯计算函数，获取结果。
    k_nmr = _calculate_foster_nmr_core(
        # Input arrays
        phit_nmr=phit_nmr,
        t2lm=t2lm,
        t2_value_array=t2_value_array,
        dt2_p50=dt2_p50,
        dphit_nmr=dphit_nmr,
        sample_types=sample_types,
        # Model parameters
        log10_KSDR_A=parameters['log10_KSDR_A'],
        PHIT_EXP=parameters['PHIT_EXP'],
        T2LM_EXP=parameters['T2LM_EXP'],
        KMACRO_B=parameters['KMACRO_B'],
        RHO_NMR=parameters['RHO_NMR'],
        log10_KMACRO_A=parameters['log10_KMACRO_A'],
        Vmacro_min=parameters['Vmacro_min'],
        log10_T2cutoff_short=parameters['log10_T2cutoff_short'],
        log10_T2cutoff_long=parameters['log10_T2cutoff_long'],
        beta_1=parameters['beta_1'],
        beta_2=parameters['beta_2'],
        delta_MDT=parameters['delta_MDT'],
        # Contextual parameters
        t2_time=t2_time,
        t2_p50_ref=t2_p50_ref,
        phit_nmr_ref=phit_nmr_ref,
        t2_range_min=t2_range_min,
        t2_range_max=t2_range_max,
        backend_service=backend_service
    )

    return k_nmr

def calculate_foster_nmr_permeability_for_prediction(
    well_data: pd.DataFrame,
    model_assets: "FosterNmrModelAssets",
    t2_time: np.ndarray,
    config: Dict[str, Any],
    *,
    backend_service: BackendService,
    t2_range_min: Optional[float] = None,
    t2_range_max: Optional[float] = None,
    return_porosity_components: bool = False
) -> Any:
    """
    (应用场景) 使用优化好的参数计算渗透率。

    此函数是FOSTER-NMR模型的应用适配器，专为生产预测场景设计。
    它接收不含K_LABEL_TYPE标签的数据，内部调用 `_calculate_foster_nmr_core`
    执行核心计算逻辑，返回渗透率预测结果。被 `swift_pso.pso_predictor.apply_foster_nmr_permeability` 调用。

    Args:
        well_data (pd.DataFrame): 包含所有必需输入曲线的DataFrame。
            - 必需曲线: 'PHIT_NMR', 'T2LM', 'DT2_P50', 'DPHIT_NMR', 'T2_VALUE'
        model_assets (FosterNmrModelAssets): 包含最终参数和上下文参考值的模型资产对象。
        t2_time (np.ndarray): T2时间轴数组。
        config (Dict[str, Any]): 包含 'curve_to_columns_map' 的配置字典。
        backend_service (BackendService): 用于计算的后端服务实例。
        t2_range_min (Optional[float], optional): T2计算范围最小值。
        t2_range_max (Optional[float], optional): T2计算范围最大值。
        return_porosity_components (bool, optional): 是否返回孔隙度组分。默认为False。

    Returns:
        Any: 如果return_porosity_components=False，返回渗透率数组 (mD)。
             如果return_porosity_components=True，返回四元组 (渗透率, Vmicro, Vmeso, Vmacro)。
    """
    # --- 解包模型资产 ---
    parameters = model_assets['parameters']
    context = model_assets['context']
    t2_p50_ref = context['t2_p50_ref']
    phit_nmr_ref = context['phit_nmr_ref']

    # --- 步骤 0: 准备输入数据 (应用场景) ---
    # 将所有需要的数组移动到目标设备
    phit_nmr = backend_service.to_backend(well_data['PHIT_NMR'].to_numpy())
    t2lm = backend_service.to_backend(well_data['T2LM'].to_numpy())
    dt2_p50 = backend_service.to_backend(well_data['DT2_P50'].to_numpy())
    dphit_nmr = backend_service.to_backend(well_data['DPHIT_NMR'].to_numpy())
    t2_time_backend = backend_service.to_backend(t2_time)

    # 在应用场景中，没有K_LABEL_TYPE，因此创建一个默认数组，
    # 这将确保在_core函数中MDT修正常数始终为1.0。
    # 使用整数编码0代表'UNKNOWN'，以兼容GPU
    sample_types = backend_service.zeros(len(well_data), dtype=np.int32)

    curve_map = config.get('curve_to_columns_map')
    if not curve_map:
        raise ValueError("配置字典 'config' 中缺少 'curve_to_columns_map' 元数据。")
    t2_value_columns = curve_map.get('T2_VALUE')
    if not t2_value_columns:
        raise ValueError("'curve_to_columns_map' 中缺少 'T2_VALUE' 曲线的列映射。")
    t2_value_array = backend_service.to_backend(well_data[t2_value_columns].to_numpy())

    # --- 步骤 1: 调用核心计算引擎 ---
    result_backend = _calculate_foster_nmr_core(
        phit_nmr=phit_nmr,
        t2lm=t2lm,
        t2_value_array=t2_value_array,
        dt2_p50=dt2_p50,
        dphit_nmr=dphit_nmr,
        sample_types=sample_types,
        t2_time=t2_time_backend,
        t2_p50_ref=t2_p50_ref,
        phit_nmr_ref=phit_nmr_ref,
        t2_range_min=t2_range_min,
        t2_range_max=t2_range_max,
        backend_service=backend_service,
        return_porosity_components=return_porosity_components,
        **parameters  # 直接解包所有模型参数
    )

    # --- 步骤 2: 将结果转换回CPU，确保输出的通用性 ---
    if return_porosity_components:
        # 解包元组中的每个数组并转换
        return tuple(backend_service.to_cpu(arr) for arr in result_backend)
    else:
        return backend_service.to_cpu(result_backend)

def _calculate_foster_nmr_core(
    # Input arrays from data
    phit_nmr: Any,
    t2lm: Any,
    t2_value_array: Any,
    dt2_p50: Any,
    dphit_nmr: Any,
    sample_types: Any,
    # Model parameters
    log10_KSDR_A: float,
    PHIT_EXP: float,
    T2LM_EXP: float,
    RHO_NMR: float,
    log10_KMACRO_A: float,
    Vmacro_min: float,
    KMACRO_B: float,
    log10_T2cutoff_short: float,
    log10_T2cutoff_long: float,
    beta_1: float,
    beta_2: float,
    delta_MDT: float,
    # Contextual parameters
    t2_time: Any,
    t2_p50_ref: float,
    phit_nmr_ref: float,
    t2_range_min: Optional[float],
    t2_range_max: Optional[float],
    # Backend
    backend_service: BackendService,
    # Output control
    return_porosity_components: bool = False
) -> Any:
    """
    FOSTER-NMR模型的核心计算引擎。(已适配BackendService)

    这是一个纯计算函数，接收NumPy/CuPy数组和模型参数，不依赖DataFrame或config字典。
    它实现了《SCAPE_MS_方法说明书》第3节定义的FOSTER-NMR的5个核心计算步骤，
    并包含参数边界约束以确保计算的健壮性。被PSO优化和应用预测两个场景共同调用。

    Args:
        phit_nmr (Any): 总孔隙度数组。
        t2lm (Any): T2对数平均值数组。
        t2_value_array (Any): 二维T2谱数组。
        dt2_p50 (Any): DT2_P50数组。
        dphit_nmr (Any): DPHIT_NMR数组。
        sample_types (Any): 样本类型数组。
        log10_KSDR_A (float): 模型参数。
        PHIT_EXP (float): 模型参数。
        RHO_NMR (float): 模型参数。
        log10_KMACRO_A (float): 模型参数。
        Vmacro_min (float): 模型参数。
        log10_T2cutoff_short (float): 模型参数。
        log10_T2cutoff_long (float): 模型参数。
        beta_1 (float): 模型参数。
        beta_2 (float): 模型参数。
        delta_MDT (float): 模型参数。
        t2_time (Any): T2时间轴数组。
        t2_p50_ref (float): T2_P50参考值。
        phit_nmr_ref (float): PHIT_NMR参考值。
        t2_range_min (Optional[float]): T2计算范围最小值。
        t2_range_max (Optional[float]): T2计算范围最大值。
        backend_service (BackendService): 用于计算的后端服务实例。
        return_porosity_components (bool): 是否返回孔隙度组分。默认为False。

    Returns:
        Any: 如果return_porosity_components=False，返回渗透率数组 K_NMR (mD)。
             如果return_porosity_components=True，返回四元组 (K_NMR, Vmicro, Vmeso, Vmacro)。
    """
    # --- 0. 解包参数和数据 ---
    # 将对数域的参数转换回线性域
    KSDR_A = 10**log10_KSDR_A
    KMACRO_A = 10**log10_KMACRO_A
    T2cutoff_short = 10**log10_T2cutoff_short
    T2cutoff_long = 10**log10_T2cutoff_long

    s = 30.0      # Sigmoid平滑系数，固定值

    # --- 1. NMR T2谱孔隙度划分 (Section 3.1) ---
    # 根据优化出的T2截止值实时计算孔隙度组分
    # 调用已重构好的、支持CPU/GPU的nmr_utils函数。
    # 由于nmr_utils已更新，可以直接接收backend模块，无需再创建临时服务。
    Vmicro, Vmeso, Vmacro = calculate_t2_porosity_components_vectorized(
        t2_spectrum_matrix=t2_value_array,
        t2_time=t2_time,
        t2cutoff_short=T2cutoff_short,
        t2cutoff_long=T2cutoff_long,
        t2_range_min=t2_range_min,
        t2_range_max=t2_range_max,
        backend_service=backend_service  # 传递服务实例
    )

    # --- 2. SDR碳酸盐岩渗透率 与 Timur/Coates大孔隙渗透率 (Section 3.2 & 3.3) ---
    # 公式1: SDR Carbonate Equation
    k_sdr = KSDR_A * (phit_nmr ** PHIT_EXP) * ((RHO_NMR * t2lm) ** T2LM_EXP)

    # 公式2: Timur/Coates Macro-porosity Equation
    # 为避免分母为零或负数，增加数值稳定性处理
    movable_fluid_fraction = phit_nmr - Vmacro
    # 使用服务层的where方法以兼容不同后端
    movable_fluid_fraction = backend_service.where(movable_fluid_fraction <= 1e-6, 1e-6,movable_fluid_fraction)
    ratio_term = Vmacro / movable_fluid_fraction
    k_macro = KMACRO_A * (phit_nmr ** PHIT_EXP) * (ratio_term ** KMACRO_B)

    # --- 3. 融合SDR和Timur/Coates渗透率 (Section 3.4) ---
    # 公式3: Sigmoid 权重函数
    w_macro = 1 / (1 + backend_service.exp(-s * (Vmacro - Vmacro_min)))

    # 公式4: 融合后的视渗透率 K_APP
    k_app = w_macro * k_macro + (1 - w_macro) * k_sdr

    # --- 4. 核磁共振油基泥浆侵入校正 (Section 3.5) ---
    # 公式5: 油基泥浆侵入校正因子 OBMCF_K
    # 增加数值稳定性，防止参考值为零
    safe_t2_p50_ref = t2_p50_ref if t2_p50_ref > 1e-6 else 1e-6
    safe_phit_nmr_ref = phit_nmr_ref if phit_nmr_ref > 1e-6 else 1e-6

    exponent = (beta_1 * (dt2_p50 / safe_t2_p50_ref)) + \
               (beta_2 * (dphit_nmr / safe_phit_nmr_ref))
    obmcf_k = backend_service.exp(exponent)

    # 公式6: 校正后的渗透率 K_OBMC
    k_obmc = k_app * obmcf_k

    # --- 5. MDT渗透率修正 (Section 3.6) ---
    # 公式7: 根据样本类型应用MDT修正
    # sample_types已由上游统一为整数编码 (2=MDT)，无需区分后端
    mdt_correction_factor = backend_service.where(sample_types == 2, 1 + delta_MDT, 1.0)
    k_nmr = k_obmc * mdt_correction_factor

    # --- 6. 根据参数决定返回值 ---
    if return_porosity_components:
        return k_nmr, Vmicro, Vmeso, Vmacro
    else:
        return k_nmr

def _calculate_porosity_components_vectorized_params(
    t2_value_array: Any,
    t2_time: Any,
    t2cutoff_short_vec: Any,
    t2cutoff_long_vec: Any,
    backend_service: BackendService,
    t2_range_min: Optional[float] = None,
    t2_range_max: Optional[float] = None
) -> Tuple[Any, Any, Any]:
    """
    (PSO专用) 并行计算多个T2截止值下的孔隙度组分。(已适配BackendService)

    此函数专为粒子群优化设计，其中每个粒子都有一组自己的T2截止值。
    它利用NumPy/CuPy的广播机制，为整个粒子群一次性计算出所有孔隙度组分。
    支持T2时间轴范围限制，只在指定范围内计算孔隙度组分。
    被 `calculate_foster_nmr_permeability_vectorized_for_loss` 调用。

    在粒子群优化（PSO）过程中，每一代（iteration）都需要为 整个粒子群（几十上百个粒子） 同时计算损失函数。
    每个粒子都代表 一套不同的模型参数。因此，T2cutoff_short 和 T2cutoff_long 不再是标量，
    而是包含了每个粒子对应值的 向量（vector），形状为 (n_particles,)。

    Args:
        t2_value_array (Any): T2谱数组 (n_depths, n_bins)。
        t2_time (Any): T2时间轴 (n_bins,)。
        t2cutoff_short_vec (Any): 短T2截止值向量 (n_particles,)。
        t2cutoff_long_vec (Any): 长T2截止值向量 (n_particles,)。
        backend_service (BackendService): 用于计算的后端服务实例。
        t2_range_min (Optional[float]): T2计算范围最小值，单位ms。默认为None（使用全部范围）。
        t2_range_max (Optional[float]): T2计算范围最大值，单位ms。默认为None（使用全部范围）。

    Returns:
        Tuple[Any, Any, Any]: (Vmicro, Vmeso, Vmacro) 矩阵，形状均为 (n_depths, n_particles)。
    """
    # --- 步骤1：确定T2计算范围 ---
    if t2_range_min is None:
        t2_range_min = backend_service.min(t2_time)
    if t2_range_max is None:
        t2_range_max = backend_service.max(t2_time)

    # --- 步骤1.5：确保所有数组使用相同的后端 ---
    # 使用 to_backend 确保所有数组都在目标设备上
    t2_time, t2_value_array, t2cutoff_short_vec, t2cutoff_long_vec = [
        backend_service.to_backend(arr) for arr in
        [t2_time, t2_value_array, t2cutoff_short_vec, t2cutoff_long_vec]
    ]

    # --- Reshape for broadcasting ---
    # t2_time: (n_bins,) -> (1, 1, n_bins)
    # t2_value_array: (n_depths, n_bins) -> (1, n_depths, n_bins)
    # t2cutoff_short_vec: (n_particles,) -> (n_particles, 1, 1)
    t2_time_r = t2_time.reshape(1, 1, -1)
    t2_value_r = t2_value_array.reshape(1, t2_value_array.shape[0], -1)
    t2cutoff_short_r = t2cutoff_short_vec.reshape(-1, 1, 1)
    t2cutoff_long_r = t2cutoff_long_vec.reshape(-1, 1, 1)

    # --- 步骤2：创建T2范围掩码 ---
    range_mask = (t2_time_r >= t2_range_min) & (t2_time_r <= t2_range_max)

    # --- Create masks using broadcasting ---
    # The comparison happens between (1, 1, n_bins) and (n_particles, 1, 1)
    # Resulting mask shape: (n_particles, 1, n_bins)
    micro_mask = (t2_time_r < t2cutoff_short_r) & range_mask
    meso_mask = (t2_time_r >= t2cutoff_short_r) & (t2_time_r < t2cutoff_long_r) & range_mask
    macro_mask = (t2_time_r >= t2cutoff_long_r) & range_mask

    # --- Apply masks and sum along the bin axis (axis=2) ---
    # t2_value_r (1, n_depths, n_bins) * micro_mask (n_particles, 1, n_bins)
    # Resulting product shape: (n_particles, n_depths, n_bins)
    # Summing over axis=2 results in shape: (n_particles, n_depths)
    Vmicro = backend_service.sum(t2_value_r * micro_mask, axis=2)
    Vmeso = backend_service.sum(t2_value_r * meso_mask, axis=2)
    Vmacro = backend_service.sum(t2_value_r * macro_mask, axis=2)

    # Transpose to get the desired (n_depths, n_particles) shape
    return Vmicro.T, Vmeso.T, Vmacro.T


def calculate_foster_nmr_permeability_vectorized_for_loss(
    parameters_matrix: Any,
    well_data_arrays: Dict[str, Any],
    backend_service: BackendService
) -> Any:
    """
    (PSO专用) FOSTER-NMR模型的完全向量化核心计算引擎。(已适配BackendService)

    此函数专为粒子群优化设计，接收一个参数矩阵（每行是一个粒子的参数），
    并为所有粒子并行计算渗透率。这是实现GPU加速PSO的关键函数。
    被 `swift_pso.internal.pso_loss_function.compute_loss_vectorized` 调用。

    Args:
        parameters_matrix (Any): 参数矩阵 (n_particles, n_parameters)。
        well_data_arrays (Dict[str, Any]): 包含numpy/cupy数组的井数据字典。
        backend_service (BackendService): 用于计算的后端服务实例。

    Returns:
        Any: 计算出的渗透率矩阵 K_NMR (n_depths, n_particles)。
    """
    # --- 0. Unpack data and parameters ---
    # a. Unpack well data arrays
    phit_nmr = well_data_arrays['phit_nmr']
    t2lm = well_data_arrays['t2lm']
    dt2_p50 = well_data_arrays['dt2_p50']
    dphit_nmr = well_data_arrays['dphit_nmr']
    # 此函数专用于PSO优化，调用者(pso_optimizer)必须提供sample_types
    sample_types = well_data_arrays['sample_types']
    t2_value_array = well_data_arrays['t2_value_array'] # (n_depths, n_bins)
    t2_time_raw = well_data_arrays['t2_time'] # (n_bins,)

    # 使用 to_backend 确保 t2_time 与其他数据在同一设备上
    t2_time = backend_service.to_backend(t2_time_raw)
    param_keys = well_data_arrays['param_keys']

    # Contextual scalars
    t2_p50_ref = well_data_arrays['t2_p50_ref']
    phit_nmr_ref = well_data_arrays['phit_nmr_ref']
    t2_range_min = well_data_arrays.get('t2_range_min')
    t2_range_max = well_data_arrays.get('t2_range_max')

    # --- Reshape for broadcasting ---
    # Parameter vectors -> (1, n_particles)
    # Well data vectors -> (n_depths, 1)
    # Result of broadcasting will be (n_depths, n_particles)
    def to_row(vec): return vec.reshape(1, -1)
    def to_col(vec): return vec.reshape(-1, 1)

    # b. Unpack parameter vectors from matrix for better readability
    params = {key: parameters_matrix[:, i] for i, key in enumerate(param_keys)}
    log10_T2cutoff_short_vec = params['log10_T2cutoff_short']
    log10_T2cutoff_long_vec = params['log10_T2cutoff_long']

    # --- 1. Porosity Components ---
    T2cutoff_short_vec = 10**log10_T2cutoff_short_vec
    T2cutoff_long_vec = 10**log10_T2cutoff_long_vec
    _Vmicro, _Vmeso, Vmacro = _calculate_porosity_components_vectorized_params(
        t2_value_array, t2_time, T2cutoff_short_vec, T2cutoff_long_vec, backend_service,
        t2_range_min=t2_range_min, t2_range_max=t2_range_max
    ) # Result shape: (n_depths, n_particles)

    # --- 2. SDR and Timur/Coates Equations ---
    KSDR_A_vec = 10**params['log10_KSDR_A']
    k_sdr = to_row(KSDR_A_vec) * (to_col(phit_nmr) ** to_row(params['PHIT_EXP'])) * ((to_row(params['RHO_NMR']) * to_col(t2lm)) ** to_row(params['T2LM_EXP']))

    movable_fluid_fraction = to_col(phit_nmr) - Vmacro
    movable_fluid_fraction = backend_service.where(movable_fluid_fraction <= 1e-6, 1e-6, movable_fluid_fraction)
    ratio_term = Vmacro / movable_fluid_fraction
    KMACRO_A_vec = 10**params['log10_KMACRO_A']
    k_macro = to_row(KMACRO_A_vec) * (to_col(phit_nmr) ** to_row(params['PHIT_EXP'])) * (ratio_term ** to_row(params['KMACRO_B']))

    # --- 3. Fusion ---
    s = 30.0
    w_macro = 1 / (1 + backend_service.exp(-s * (Vmacro - to_row(params['Vmacro_min']))))
    k_app = w_macro * k_macro + (1 - w_macro) * k_sdr

    # --- 4. OBM Correction ---
    safe_t2_p50_ref = t2_p50_ref if t2_p50_ref > 1e-6 else 1e-6
    safe_phit_nmr_ref = phit_nmr_ref if phit_nmr_ref > 1e-6 else 1e-6

    exponent = (to_row(params['beta_1']) * (to_col(dt2_p50) / safe_t2_p50_ref)) + \
               (to_row(params['beta_2']) * (to_col(dphit_nmr) / safe_phit_nmr_ref))
    obmcf_k = backend_service.exp(exponent)
    k_obmc = k_app * obmcf_k

    # --- 5. MDT Correction ---
    # sample_types已由上游统一为整数编码 (2=MDT)，无需区分后端
    mdt_correction_factor = backend_service.where(to_col(sample_types) == 2, 1 + to_row(params['delta_MDT']), 1.0)
    k_nmr = k_obmc * mdt_correction_factor

    return k_nmr
