from __future__ import annotations

from typing import Any, NamedTuple

from logwp.infra.exceptions import WpError, ErrorContext


class WpExtrasError(WpError):
    """logwp.extras扩展功能异常基类。

    Architecture
    ------------
    层次/依赖: extras层异常基类，继承自WpError
    设计原则: 异常层次化、结构化上下文
    性能特征: 轻量级异常、快速诊断
    """

    def __init__(
        self,
        message: str,
        context: ErrorContext | None = None,
        **kwargs: Any
    ) -> None:
        """初始化扩展功能异常。

        Args:
            message: 异常消息
            context: 错误上下文
            **kwargs: 其他参数
        """
        super().__init__(message, context=context)


__all__ = [
    "WpExtrasError"
]
