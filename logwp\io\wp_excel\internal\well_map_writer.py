"""井名映射表单写入服务。

严格按照WFS 6.1节规范实现_Well_Map表单写入功能。
支持井名映射关系的完整写入和Excel格式化。

Architecture
------------
层次/依赖: I/O层内部服务，被excel_writer调用
设计原则: WFS规范严格遵循、无状态函数、格式化支持
性能特征: 批量写入、内存优化、Excel格式化

Functions
---------
- write_well_map_sheet: 写入_Well_Map表单数据和格式化
- format_well_map_sheet: 格式化_Well_Map表单

References
----------
- 《SCAPE_DDS_logwp_io_write_wp_excel.md》§3.2.2 - 井名映射写入设计
- 《SCAPE_WFS_WP文件规范.md》§6.1 - _Well_Map表单规范
"""

from __future__ import annotations

import time

import openpyxl
import structlog

from logwp.io.constants import WpXlsxKey
from logwp.io.exceptions import WpFileFormatError
from logwp.models.mapping import WpWellMap
from logwp.infra.exceptions import ErrorContext

logger = structlog.get_logger(__name__)

__all__ = ["write_well_map_sheet"]


def write_well_map_sheet(
    worksheet: openpyxl.Worksheet, well_map: WpWellMap
) -> None:
    """严格按照WFS 6.1节规范写入_Well_Map表单。

    WFS规范要求：
    1. 第一行：固定2列标题（WELL/MAP_WELL）
    2. 第二行起：井名映射数据，每行一个映射关系
    3. 严格遵循WFS列结构要求

    Args:
        worksheet: Excel工作表对象
        well_map: 井名映射对象

    Raises:
        WpFileFormatError: 井名映射写入失败
    """
    start_time = time.time()

    try:
        logger.info(
            "开始写入井名映射表单",
            total_mappings=len(well_map.mappings)
        )

        # 1. 写入表头行（第一行）
        _write_well_map_header(worksheet)

        # 2. 写入映射记录（第二行起）
        _write_mapping_records(worksheet, well_map.mappings)

        processing_time = time.time() - start_time
        logger.info(
            "井名映射表单写入完成",
            total_mappings=len(well_map.mappings),
            processing_time=round(processing_time, 3)
        )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(
            "井名映射表单写入失败",
            error_type=type(e).__name__,
            error_message=str(e),
            processing_time=round(processing_time, 3)
        )
        raise WpFileFormatError(
            f"井名映射表单写入失败: {e}",
            context=ErrorContext(
                operation="write_well_map_sheet",
                additional_info={"total_mappings": len(well_map.mappings)}
            )
        ) from e


def _write_well_map_header(worksheet: openpyxl.Worksheet) -> None:
    """写入_Well_Map表单的表头行。

    按照WFS 6.1节规范写入2列标题。使用worksheet.append()以兼容只写模式。

    Args:
        worksheet: Excel工作表对象
    """
    # WFS 6.1节规定的2列标题
    headers = [
        WpXlsxKey.WELLMAP_COL_WELL.value,      # WELL
        WpXlsxKey.WELLMAP_COL_MAP_WELL.value   # MAP_WELL
    ]

    worksheet.append(headers)

    logger.debug("井名映射表头写入完成", header_count=len(headers))


def _write_mapping_records(
    worksheet: openpyxl.Worksheet,
    mappings: dict[str, str]
) -> None:
    """写入井名映射记录数据。

    Args:
        worksheet: Excel工作表对象
        mappings: 井名映射字典 {原井名: 映射井名}
    """
    for source_well, target_well in mappings.items():
        # 使用append()方法批量追加行，性能远高于逐个单元格写入
        worksheet.append([str(source_well), str(target_well)])

    logger.debug(
        "井名映射记录写入完成",
        record_count=len(mappings)
    )
