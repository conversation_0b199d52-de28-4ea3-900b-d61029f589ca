"""scape.core.obmiq.plotting - OBMIQ绘图复现功能

提供从数据快照复现OBMIQ步骤所有图表的功能。

Architecture
------------
层次/依赖: scape/core层，OBMIQ组件的绘图实现模块
设计原则: 数据快照优先、样式与逻辑解耦

References
----------
- 《logwp/extras/plotting/README.md》- 绘图系统设计文档
"""
from __future__ import annotations

from pathlib import Path
from typing import TYPE_CHECKING

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns

from logwp.extras.plotting import apply_profile, save_figure

if TYPE_CHECKING:
    from logwp.extras.plotting import PlotProfile


def replot_training_history(snapshot_path: Path, profile: PlotProfile, output_path: Path) -> None:
    """从数据快照重新生成训练历史曲线图。"""
    data = pd.read_csv(snapshot_path)
    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    ax.plot(data["loss"], **profile.artist_props.get("train_loss", {}))
    ax.plot(data["val_loss"], **profile.artist_props.get("val_loss", {}))

    ax.legend()
    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_crossplot(
    snapshot_path: Path,
    profile: PlotProfile,
    output_path: Path,
    actual_col: str,
    predicted_col: str,
) -> None:
    """从数据快照重新生成交会图。"""
    data = pd.read_csv(snapshot_path)
    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    x_data = data[actual_col]
    y_data = data[predicted_col]

    # 1. 绘制散点图
    ax.scatter(x_data, y_data, **profile.artist_props.get("scatter", {}))

    # 2. 显式地从数据计算对称的坐标轴范围，而不是依赖matplotlib的自动范围。
    # 这种方法更健壮，因为它不依赖于绘图调用的顺序。
    if not x_data.empty and not y_data.empty:
        min_val = min(x_data.min(), y_data.min())
        max_val = max(x_data.max(), y_data.max())
        # 增加5%的边距
        padding = (max_val - min_val) * 0.05
        lims = [min_val - padding, max_val + padding]
    else:
        lims = [0, 1]  # 为空数据设置默认范围

    # 3. 绘制1:1参考线并设置对称的坐标轴
    ax.plot(lims, lims, **profile.artist_props.get("line_1_to_1", {}))
    ax.set_xlim(lims)
    ax.set_ylim(lims)

    ax.legend()
    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_residuals_plot(
    snapshot_path: Path,
    profile: PlotProfile,
    output_path: Path,
    predicted_col: str,
    residual_col: str,
) -> None:
    """从数据快照重新生成残差图。"""
    data = pd.read_csv(snapshot_path)
    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    ax.scatter(data[predicted_col], data[residual_col], **profile.artist_props.get("scatter", {}))
    ax.axhline(y=0, **profile.artist_props.get("zero_line", {}))

    ax.legend()
    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_residuals_hist(
    snapshot_path: Path,
    profile: PlotProfile,
    output_path: Path,
    residual_col: str,
) -> None:
    """从数据快照重新生成残差直方图。"""
    data = pd.read_csv(snapshot_path)
    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    sns.histplot(data[residual_col], ax=ax, **profile.artist_props.get("hist", {}))

    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_grad_cam(
    snapshot_path: Path,
    profile: PlotProfile,
    output_path: Path,
) -> None:
    """从数据快照重新生成Grad-CAM热力图。"""
    # 0. 从快照加载绘图所需的所有数据
    data = pd.read_csv(snapshot_path)
    t2_axis = data["t2_axis"].values
    image = data["original_image"].values
    heatmap = data["heatmap"].values

    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    # 1. 绘制原始序列曲线。这一步会根据数据自动设定y轴的范围。
    ax.plot(t2_axis, image, **profile.artist_props.get("line", {}))

    # 2. 显式捕获由plot()调用确定的y轴范围。
    # 这样做可以确保热力图的垂直范围与原始曲线完全对齐，
    # 使代码意图更清晰，并防止后续操作意外修改范围。
    y_lims = ax.get_ylim()

    # 3. 归一化热力图以便于可视化
    # 使用一个小的epsilon防止除以零
    heatmap_normalized = (heatmap - np.min(heatmap)) / (np.max(heatmap) - np.min(heatmap) + 1e-8)

    # 4. 使用imshow叠加热力图，并使用之前捕获的y_lims来精确控制其垂直范围。
    ax.imshow(
        heatmap_normalized[np.newaxis, :],
        extent=(t2_axis[0], t2_axis[-1], y_lims[0], y_lims[1]),
        **profile.artist_props.get("imshow", {}),
    )

    ax.set_xlabel("T2 (ms)")
    ax.set_ylabel("Normalized Cumulative Amplitude")
    ax.set_xscale("log")  # T2 axis is logarithmic
    ax.legend()

    # 5. 强制横坐标轴显示所有刻度值。
    ax.set_xticks(t2_axis)

    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)


def replot_captum_ig_summary(
    snapshot_path: Path,
    profile: PlotProfile,
    output_path: Path,
) -> None:
    """从数据快照重新生成Captum Integrated Gradients摘要图。"""
    data = pd.read_csv(snapshot_path)
    fig, ax = plt.subplots()
    apply_profile(ax, profile)

    # 从快照数据计算每个特征的平均归因值
    feature_means = data.mean(axis=0).values
    feature_names = data.columns

    # 创建柱状图
    ax.bar(feature_names, feature_means, **profile.artist_props.get("bar", {}))

    # 旋转x轴标签以提高可读性
    ax.tick_params(axis="x", rotation=45)

    save_figure(fig, profile.save_config, output_path.parent, output_path.stem)
    plt.close(fig)
