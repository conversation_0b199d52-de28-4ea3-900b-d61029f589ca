"""验证包的产物处理器。

此模块定义了一个无状态的工具类 `ValidationArtifactHandler`，用于处理
`validation`包中所有步骤的产物I/O操作。

遵循《可追踪机器学习组件开发框架》的设计原则，此类不包含任何业务逻辑，
仅负责数据的序列化/反序列化和文件读写。
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import TYPE_CHECKING, Any, Dict

import pandas as pd

if TYPE_CHECKING:
    from logwp.extras.plotting import PlotProfile


class ValidationArtifactHandler:
    """一个无状态的工具类，用于处理验证步骤的产物I/O。

    此类不应有 `__init__` 方法或任何实例属性，所有方法均为静态方法。
    """

    @staticmethod
    def save_dataframe(df: pd.DataFrame, path: Path) -> None:
        """将DataFrame保存为CSV文件。

        Args:
            df (pd.DataFrame): 要保存的DataFrame。
            path (Path): 目标文件路径。
        """
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=False)

    @staticmethod
    def load_dataframe(path: Path) -> pd.DataFrame:
        """从CSV文件加载DataFrame。"""
        return pd.read_csv(path)

    @staticmethod
    def save_dict_as_json(data: Dict[str, Any], path: Path) -> None:
        """将字典保存为格式化的JSON文件。"""
        path.parent.mkdir(parents=True, exist_ok=True)
        with path.open("w", encoding="utf-8") as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    @staticmethod
    def load_dict_from_json(path: Path) -> Dict[str, Any]:
        """从JSON文件加载字典。"""
        with path.open("r", encoding="utf-8") as f:
            return json.load(f)

    @staticmethod
    def save_plot_profile(profile: "PlotProfile", path: Path) -> None:
        """将PlotProfile对象序列化为JSON文件。"""
        profile.to_json(path)

    @staticmethod
    def load_plot_profile(path: Path) -> "PlotProfile":
        """从JSON文件反序列化PlotProfile对象。"""
        from logwp.extras.plotting import PlotProfile
        return PlotProfile.from_json(path)
