"""scape.core.baselines.sdr.artifact_handler - SDR基准模型产物处理器"""

from __future__ import annotations

import json
from typing import TYPE_CHECKING, Any, Dict

if TYPE_CHECKING:
    from pathlib import Path
    import pandas as pd


class SdrArtifactHandler:
    """
    一个无状态的工具类，负责处理SDR基准模型步骤的所有产物I/O。
    """

    @staticmethod
    def save_model_assets(assets: Dict[str, Any], path: Path) -> None:
        """将模型资产包（一个字典）保存为JSON文件。

        Args:
            assets: 包含模型参数和元数据的字典。
            path: 保存文件的路径。
        """
        path.parent.mkdir(parents=True, exist_ok=True)
        with path.open('w', encoding='utf-8') as f:
            json.dump(assets, f, indent=4)

    @staticmethod
    def load_model_assets(path: Path) -> Dict[str, Any]:
        """从JSON文件加载模型资产包。

        Args:
            path: JSON文件的路径。

        Returns:
            包含模型参数和元数据的字典。
        """
        with path.open('r', encoding='utf-8') as f:
            return json.load(f)

    @staticmethod
    def save_dataframe(df: "pd.DataFrame", path: Path) -> None:
        """将DataFrame保存为CSV文件。

        Args:
            df: 要保存的Pandas DataFrame。
            path: 保存文件的路径。
        """
        path.parent.mkdir(parents=True, exist_ok=True)
        df.to_csv(path, index=False)
