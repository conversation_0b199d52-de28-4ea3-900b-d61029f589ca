"""井名映射概况生成服务。

提供WpWellMap井名映射概况生成的服务实现。

Architecture
------------
层次/依赖: models/internal服务层，无状态服务函数
设计原则: Service Layer模式、无状态设计、类型安全
性能特征: 轻量级处理、内存优化、错误容错

Examples:
    >>> from logwp.models.internal.wellmap_summary import generate_wellmap_summary
    >>> summary = generate_wellmap_summary(well_map)
    >>> print(f"总映射数: {summary['total_mappings']}")

References:
    《SCAPE_DDS_logwp_generate_summary.md》§4.4 - 井名映射服务设计
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from logwp.models.mapping import WpWellMap

from logwp.infra import get_logger
from .summary_constants import SummaryKeys, LogMessages

logger = get_logger(__name__)


def generate_wellmap_summary(well_map: WpWellMap) -> dict[str, Any]:
    """生成井名映射概况（无状态服务函数）。

    Args:
        well_map: WpWellMap实例

    Returns:
        dict: 井名映射概况数据
            - total_mappings: 总映射数
            - mappings_list: 映射列表

    Examples:
        >>> summary = generate_wellmap_summary(well_map)
        >>> print(f"映射数: {summary['total_mappings']}")
        >>> print(f"映射详情: {summary['mappings_list']}")
    """
    logger.debug(LogMessages.MSG_WELLMAP_SUMMARY_START, total_mappings=len(well_map.mappings))

    # 收集映射列表
    mappings_list = []
    for source, target in well_map.mappings.items():
        mappings_list.append({
            SummaryKeys.SOURCE: str(source),
            SummaryKeys.TARGET: str(target)
        })

    # 统计唯一目标井名
    unique_targets = set(str(target) for target in well_map.mappings.values())


    summary = {
        SummaryKeys.TOTAL_MAPPINGS: len(well_map.mappings),
        SummaryKeys.MAPPINGS_LIST: mappings_list,
    }

    logger.debug(LogMessages.MSG_WELLMAP_SUMMARY_COMPLETE,
                total_mappings=summary[SummaryKeys.TOTAL_MAPPINGS])

    return summary

