"""验证服务。

提供WFS规范验证、文件格式验证、数据完整性验证等功能。
专注于结构性验证，不涉及业务逻辑验证。
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict, List

import numpy as np
import pandas as pd
import structlog

from logwp.io.exceptions import WpFileFormatError
from logwp.models.constants import WpDataType, WpDepthRole, WpDsType
from logwp.models.datasets.base import WpDepthIndexedDatasetBase
from logwp.models.exceptions import WpValidationError
from logwp.io.constants import WpXlsxKey
from logwp.models.curve.metadata import CurveMetadata
from logwp.models.well_project import WpWellProject

logger = structlog.get_logger(__name__)


def validate_file_format(file_path: Path) -> None:
    """验证文件格式（.wp.xlsx）。

    WFS 2.1节：文件名必须以.wp.xlsx结尾

    Args:
        file_path: 文件路径

    Raises:
        WpFileFormatError: 文件格式不符合要求

    Examples:
        >>> validate_file_format(Path("test.wp.xlsx"))  # 通过
        >>> validate_file_format(Path("test.xlsx"))     # 抛出异常

    References:
        《SCAPE_WFS_WP文件规范.md》2.1节 - 文件格式要求
    """
    if not str(file_path).lower().endswith(WpXlsxKey.WP_FILE_EXTENSION.lower()):
        raise WpFileFormatError(
            f"文件扩展名必须为{WpXlsxKey.WP_FILE_EXTENSION}，实际: {file_path.suffix}"
        )

    logger.debug("文件格式验证通过", file_path=str(file_path))


def validate_workbook_limits(sheet_count: int, sheet_names: List[str]) -> None:
    """验证工作簿是否符合WFS限制。

    WFS 2.2.1节：最大255个工作表，工作表名最大31字符

    Args:
        sheet_count: 工作表数量
        sheet_names: 工作表名称列表

    Raises:
        WpFileFormatError: 工作簿超出WFS限制

    References:
        《SCAPE_WFS_WP文件规范.md》2.2.1节 - 工作簿限制
    """
    # 验证工作表数量
    max_worksheets = int(WpXlsxKey.MAX_WORKSHEETS.value)
    if sheet_count > max_worksheets:
        raise WpFileFormatError(
            f"工作表数量超出限制: {sheet_count} > {max_worksheets}"
        )

    # 验证工作表名称长度
    max_name_length = int(WpXlsxKey.MAX_SHEET_NAME_LENGTH.value)
    for sheet_name in sheet_names:
        if len(sheet_name) > max_name_length:
            raise WpFileFormatError(
                f"工作表名称过长: '{sheet_name}' ({len(sheet_name)} > {max_name_length})"
            )

    logger.debug("工作簿限制验证通过", sheet_count=sheet_count)


def validate_dataset_type_consistency(dataset_type: WpDsType,
                                     depth_curve_count: int) -> None:
    """验证数据集类型与深度索引一致性。

    WFS 3章：Continuous/Point需1个深度索引，Interval需2个

    Args:
        dataset_type: 数据集类型
        depth_curve_count: 深度曲线数量

    Raises:
        WpValidationError: 类型与深度索引不一致

    References:
        《SCAPE_WFS_WP文件规范.md》3章 - 数据集类型规范
    """
    if dataset_type in {WpDsType.CONTINUOUS, WpDsType.POINT}:
        if depth_curve_count != 1:
            raise WpValidationError(
                f"{dataset_type.value}数据集必须恰好包含1个深度曲线，实际: {depth_curve_count}"
            )
    elif dataset_type == WpDsType.INTERVAL:
        if depth_curve_count != 2:
            raise WpValidationError(
                f"Interval数据集必须恰好包含2个深度曲线，实际: {depth_curve_count}"
            )

    logger.debug("数据集类型一致性验证通过", dataset_type=dataset_type.value)


def validate_curve_class_constraints(curve_class: str, data_type: WpDataType) -> None:
    """验证曲线类别约束。

    WFS 4.1.4节：CAT类别仅支持INT或STR数据类型

    Args:
        curve_class: 曲线类别
        data_type: 数据类型

    Raises:
        WpValidationError: 曲线类别约束违反

    References:
        《SCAPE_WFS_WP文件规范.md》4.1.4节 - 曲线类别约束
    """
    if curve_class.upper() == WpXlsxKey.CLASS_CAT.upper():
        if data_type not in {WpDataType.INT, WpDataType.STR}:
            raise WpValidationError(
                f"CAT类别曲线仅支持INT或STR数据类型，实际: {data_type.value}"
            )

    logger.debug("曲线类别约束验证通过", curve_class=curve_class)


def validate_depth_unit_consistency(depth_curves: List[Any]) -> None:
    """验证深度单位一致性。

    WFS 4.5.2节：同一数据集内所有深度曲线单位必须相同

    Args:
        depth_curves: 深度曲线列表

    Raises:
        WpValidationError: 深度单位不一致

    References:
        《SCAPE_WFS_WP文件规范.md》4.5.2节 - 深度单位一致性
    """
    if len(depth_curves) <= 1:
        return  # 单个或无深度曲线，无需检查

    units = {curve.unit for curve in depth_curves}
    if len(units) > 1:
        raise WpValidationError(f"深度曲线单位不一致: {units}")

    logger.debug("深度单位一致性验证通过", unit=next(iter(units)))


def validate_required_columns(dataset_type: WpDsType,
                             curve_metadata: CurveMetadata) -> None:
    """验证必需保留列存在。

    WFS 4.4节：每个数据集必须包含深度索引列和井名列
    - Continuous/Point：1个深度索引 + 1个井名
    - Interval：2个深度索引（顶界+底界）+ 1个井名

    Args:
        dataset_type: 数据集类型
        curve_metadata: 曲线元数据

    Raises:
        WpValidationError: 必需列缺失

    References:
        《SCAPE_WFS_WP文件规范.md》4.4节 - 必需保留列
    """
    # 统计深度曲线和井名曲线
    depth_curves = [
        curve for curve in curve_metadata.curves.values()
        if curve.depth_role is not None
    ]
    well_curves = [
        curve for curve in curve_metadata.curves.values()
        if curve.is_well_identifier
    ]

    # 验证井名曲线
    if len(well_curves) != 1:
        raise WpValidationError(f"每个数据集必须恰好包含1个井名曲线，实际: {len(well_curves)}")

    # 验证深度曲线
    validate_dataset_type_consistency(dataset_type, len(depth_curves))

    logger.debug("必需列验证通过", depth_count=len(depth_curves), well_count=len(well_curves))


def validate_comp_type_structure(comp_value: str, attribute_name: str) -> Dict[str, Any]:
    """验证COMP类型数据结构。

    WFS 5.2.6节：JSON格式验证，必需字段检查

    Args:
        comp_value: COMP类型值（JSON字符串）
        attribute_name: 属性名称（用于错误报告）

    Returns:
        Dict[str, Any]: 解析的JSON数据

    Raises:
        WpValidationError: COMP数据结构无效

    References:
        《SCAPE_WFS_WP文件规范.md》5.2.6节 - COMP类型规范
    """
    try:
        comp_data = json.loads(comp_value)
        if not isinstance(comp_data, dict):
            raise WpValidationError(f"COMP数据必须是JSON对象: {attribute_name}")

        logger.debug("COMP类型结构验证通过", attribute=attribute_name)
        return comp_data

    except json.JSONDecodeError as e:
        raise WpValidationError(f"COMP数据JSON格式错误: {attribute_name}, 错误: {e}") from e


def validate_cdp1_compliance(df: pd.DataFrame) -> None:
    """验证CDP-1合规性（DataFrame索引）。

    CDP-1原则：DataFrame必须使用默认整数索引

    Args:
        df: DataFrame对象

    Raises:
        WpValidationError: CDP-1合规性违反

    References:
        《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》CDP-1节
    """
    if not isinstance(df.index, pd.RangeIndex):
        raise WpValidationError(
            f"DataFrame必须使用默认整数索引（CDP-1原则），实际: {type(df.index).__name__}"
        )

    logger.debug("CDP-1合规性验证通过", index_type=type(df.index).__name__)


def validate_dataset_construction_compliance(dataset: WpDepthIndexedDatasetBase) -> None:
    """验证数据集构造合规性。

    完整验证清单：
    - CDP-1：DataFrame索引类型检查
    - 深度曲线：数量、类型、单位一致性
    - 井名曲线：存在性、类型检查
    - 友好名称：DataFrame列名与元数据一致性
    - 数据完整性：行数一致性、空值处理

    Args:
        dataset: 数据集对象

    Raises:
        WpValidationError: 构造合规性验证失败

    References:
        《SCAPE_DDS_logwp_io层构造测井数据模型规范.md》DV章节
    """
    logger.debug("开始数据集构造合规性验证", dataset_name=dataset.name)

    # CDP-1合规性检查
    # validate_cdp1_compliance(dataset.df)

    # 深度曲线验证
    validate_depth_curves_compliance(dataset)

    # 井名曲线验证
    validate_well_identifier_compliance(dataset)

    # 友好名称一致性验证
    validate_friendly_names_consistency(dataset)

    # 数据完整性验证
    validate_data_integrity(dataset)

    logger.info("数据集构造合规性验证通过", dataset_name=dataset.name)


def validate_depth_curves_compliance(dataset: WpDepthIndexedDatasetBase) -> None:
    """验证深度曲线合规性。

    Args:
        dataset: 数据集对象

    Raises:
        WpValidationError: 深度曲线验证失败
    """
    depth_curves = [
        curve for curve in dataset.curve_metadata.curves.values()
        if curve.depth_role is not None
    ]

    if not depth_curves:
        raise WpValidationError("数据集必须包含深度曲线")

    # 验证深度数据有效性
    for curve in depth_curves:
        if curve.is_2d_composite_curve():
            column_names = curve.dataframe_element_names
        else:
            column_names = [curve.dataframe_column_name]

        for col_name in column_names:
            if col_name in dataset.df.columns:
                depth_data = dataset.df[col_name]

                # 检查有限数值
                if not depth_data.apply(lambda x: pd.isna(x) or np.isfinite(x)).all():
                    raise WpValidationError(
                        f"深度曲线 {curve.name} 包含无效数值（无穷大或NaN）"
                    )


def validate_well_identifier_compliance(dataset: WpDepthIndexedDatasetBase) -> None:
    """验证井名曲线合规性。

    Args:
        dataset: 数据集对象

    Raises:
        WpValidationError: 井名曲线验证失败
    """
    well_curves = [
        curve for curve in dataset.curve_metadata.curves.values()
        if curve.is_well_identifier
    ]

    if len(well_curves) != 1:
        raise WpValidationError(f"数据集必须包含恰好1个井名曲线，实际: {len(well_curves)}")

    well_curve = well_curves[0]
    well_column = well_curve.dataframe_column_name

    if well_column not in dataset.df.columns:
        raise WpValidationError(f"井名曲线列不存在: {well_column}")

    well_data = dataset.df[well_column]

    # 检查数据类型
    if not (well_data.dtype == object or pd.api.types.is_string_dtype(well_data)):
        raise WpValidationError(f"井名曲线 {well_curve.name} 必须为字符串类型")

    # 检查非空性
    if well_data.isna().all():
        raise WpValidationError(f"井名曲线 {well_curve.name} 不能全为空值")


def validate_friendly_names_consistency(dataset: WpDepthIndexedDatasetBase) -> None:
    """验证友好名称一致性。

    Args:
        dataset: 数据集对象

    Raises:
        WpValidationError: 友好名称不一致
    """
    expected_columns = set()

    for curve in dataset.curve_metadata.curves.values():
        if curve.is_2d_composite_curve():
            expected_columns.update(curve.dataframe_element_names)
        else:
            expected_columns.add(curve.dataframe_column_name)

    actual_columns = set(dataset.df.columns)

    if expected_columns != actual_columns:
        missing = expected_columns - actual_columns
        extra = actual_columns - expected_columns
        raise WpValidationError(
            f"DataFrame列名与元数据不一致。缺失: {missing}, 多余: {extra}"
        )


def validate_data_integrity(dataset: WpDepthIndexedDatasetBase) -> None:
    """验证数据完整性。

    Args:
        dataset: 数据集对象

    Raises:
        WpValidationError: 数据完整性验证失败
    """
    if dataset.df.empty:
        logger.warning("数据集为空", dataset_name=dataset.name)
        return

    # 检查数据形状合理性
    if dataset.df.shape[0] == 0:
        raise WpValidationError("数据集不能没有数据行")

    if dataset.df.shape[1] == 0:
        raise WpValidationError("数据集不能没有数据列")

    logger.debug("数据完整性验证通过", shape=dataset.df.shape)
