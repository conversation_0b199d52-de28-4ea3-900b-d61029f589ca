#!/usr/bin/env python3
"""深度单位处理工具模块。

提供深度单位标准化、语义等价检查等功能，支持各种深度单位变体的统一处理。

Architecture
------------
层次/依赖: utils层，深度单位处理工具
设计原则: 纯函数设计、无状态、类型安全
性能特征: 轻量级、缓存友好、快速查找

遵循CCG规范：
- CS-1: 函数使用snake_case命名
- TS-1: 完整类型注解覆盖
- CT-1: 使用枚举管理常量
- PF-1: 高效的字符串处理

References
----------
- 《SCAPE_CCG_编码与通用规范.md》§CT - 常量管理规范
- 《SCAPE_SAD_软件架构设计.md》§4.11 - 工具层设计
"""

from __future__ import annotations


def standardize_depth_unit(unit: str) -> str:
    """标准化深度单位名称。

    Args:
        unit: 原始深度单位

    Returns:
        str: 标准化后的深度单位

    Note:
        - 将各种变体统一为标准名称
        - 支持米、英尺、厘米等常见深度单位
        - 大小写不敏感处理
        - 未知单位保持原样（转为大写）

    Examples:
        >>> standardize_depth_unit("METERS")
        'METER'
        >>> standardize_depth_unit("metres")
        'METER'
        >>> standardize_depth_unit("ft")
        'FOOT'
        >>> standardize_depth_unit("unknown")
        'UNKNOWN'
    """
    unit_upper = unit.upper().strip()

    # 米的各种变体
    if unit_upper in {'M', 'METER', 'METERS', 'METRE', 'METRES'}:
        return 'METER'
    # 英尺的各种变体
    elif unit_upper in {'FT', 'FOOT', 'FEET'}:
        return 'FOOT'
    # 厘米的各种变体
    elif unit_upper in {'CM', 'CENTIMETER', 'CENTIMETERS', 'CENTIMETRE', 'CENTIMETRES'}:
        return 'CENTIMETER'
    # 毫米的各种变体
    elif unit_upper in {'MM', 'MILLIMETER', 'MILLIMETERS', 'MILLIMETRE', 'MILLIMETRES'}:
        return 'MILLIMETER'
    # 千米的各种变体
    elif unit_upper in {'KM', 'KILOMETER', 'KILOMETERS', 'KILOMETRE', 'KILOMETRES'}:
        return 'KILOMETER'
    # 英寸的各种变体
    elif unit_upper in {'IN', 'INCH', 'INCHES'}:
        return 'INCH'
    else:
        # 未知单位保持原样（转为大写）
        return unit_upper


def are_depth_units_equivalent(unit1: str, unit2: str) -> bool:
    """检查两个深度单位是否语义等价。

    Args:
        unit1: 第一个深度单位
        unit2: 第二个深度单位

    Returns:
        bool: 是否等价

    Note:
        - 通过标准化后比较判断等价性
        - 支持常见深度单位的各种变体
        - 大小写不敏感

    Examples:
        >>> are_depth_units_equivalent("METERS", "METRES")
        True
        >>> are_depth_units_equivalent("M", "METER")
        True
        >>> are_depth_units_equivalent("FEET", "METER")
        False
    """
    return standardize_depth_unit(unit1) == standardize_depth_unit(unit2)


def get_supported_depth_units() -> dict[str, list[str]]:
    """获取支持的深度单位及其变体。

    Returns:
        dict[str, list[str]]: 标准单位名称到变体列表的映射

    Note:
        - 用于文档生成和用户提示
        - 按标准单位分组显示所有支持的变体

    Examples:
        >>> units = get_supported_depth_units()
        >>> units['METER']
        ['M', 'METER', 'METERS', 'METRE', 'METRES']
    """
    return {
        'METER': ['M', 'METER', 'METERS', 'METRE', 'METRES'],
        'FOOT': ['FT', 'FOOT', 'FEET'],
        'CENTIMETER': ['CM', 'CENTIMETER', 'CENTIMETERS', 'CENTIMETRE', 'CENTIMETRES'],
        'MILLIMETER': ['MM', 'MILLIMETER', 'MILLIMETERS', 'MILLIMETRE', 'MILLIMETRES'],
        'KILOMETER': ['KM', 'KILOMETER', 'KILOMETERS', 'KILOMETRE', 'KILOMETRES'],
        'INCH': ['IN', 'INCH', 'INCHES']
    }


def validate_depth_unit(unit: str) -> bool:
    """验证深度单位是否被支持。

    Args:
        unit: 要验证的深度单位

    Returns:
        bool: 是否为支持的深度单位

    Note:
        - 检查单位是否在支持的变体列表中
        - 大小写不敏感

    Examples:
        >>> validate_depth_unit("METERS")
        True
        >>> validate_depth_unit("unknown_unit")
        False
    """
    unit_upper = unit.upper().strip()
    supported_units = get_supported_depth_units()

    for variants in supported_units.values():
        if unit_upper in variants:
            return True

    return False


def get_unit_conversion_factor(from_unit: str, to_unit: str) -> float | None:
    """获取深度单位转换系数。

    Args:
        from_unit: 源单位
        to_unit: 目标单位

    Returns:
        float | None: 转换系数，如果单位不支持则返回None

    Note:
        - 返回从源单位到目标单位的乘法系数
        - 基于米作为基准单位进行转换
        - 如果单位相同（语义等价）则返回1.0

    Examples:
        >>> get_unit_conversion_factor("METER", "CENTIMETER")
        100.0
        >>> get_unit_conversion_factor("FEET", "METER")
        0.3048
        >>> get_unit_conversion_factor("METERS", "METRES")
        1.0
    """
    # 如果单位语义等价，直接返回1.0
    if are_depth_units_equivalent(from_unit, to_unit):
        return 1.0

    # 标准化单位名称
    from_std = standardize_depth_unit(from_unit)
    to_std = standardize_depth_unit(to_unit)

    # 转换系数表（以米为基准）
    unit_to_meter = {
        'METER': 1.0,
        'FOOT': 0.3048,
        'CENTIMETER': 0.01,
        'MILLIMETER': 0.001,
        'KILOMETER': 1000.0,
        'INCH': 0.0254
    }

    # 检查单位是否支持
    if from_std not in unit_to_meter or to_std not in unit_to_meter:
        return None

    # 计算转换系数：from -> meter -> to
    return unit_to_meter[from_std] / unit_to_meter[to_std]


def format_depth_value(value: float, unit: str, precision: int = 2) -> str:
    """格式化深度值显示。

    Args:
        value: 深度值
        unit: 深度单位
        precision: 小数位数

    Returns:
        str: 格式化后的深度值字符串

    Note:
        - 自动选择合适的数值格式
        - 标准化单位名称显示

    Examples:
        >>> format_depth_value(2500.5, "METERS")
        '2500.50 m'
        >>> format_depth_value(8202.1, "FEET", 1)
        '8202.1 ft'
    """
    std_unit = standardize_depth_unit(unit)

    # 单位显示映射
    unit_display = {
        'METER': 'm',
        'FOOT': 'ft',
        'CENTIMETER': 'cm',
        'MILLIMETER': 'mm',
        'KILOMETER': 'km',
        'INCH': 'in'
    }

    display_unit = unit_display.get(std_unit, unit.lower())

    return f"{value:.{precision}f} {display_unit}"
