"""数据集概况生成服务。

提供WpDepthIndexedDatasetBase数据集概况生成的服务实现。

Architecture
------------
层次/依赖: models/datasets/internal服务层，无状态服务函数
设计原则: Service Layer模式、无状态设计、类型安全
性能特征: 向量化计算、内存优化、错误容错

Examples:
    >>> from logwp.models.datasets.internal.dataset_summary import generate_dataset_summary
    >>> summary = generate_dataset_summary(dataset)
    >>> print(f"数据集类型: {summary['basic_info']['type']}")

References:
    《SCAPE_DDS_logwp_generate_summary.md》§4.5 - 数据集概况服务设计
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any

import pandas as pd

if TYPE_CHECKING:
    from logwp.models.datasets.base import WpDepthIndexedDatasetBase

from logwp.models.constants import WpStandardColumn
from logwp.infra import get_logger
from logwp.models.internal.summary_constants import SummaryKeys, LogMessages

logger = get_logger(__name__)


def generate_dataset_summary(dataset: WpDepthIndexedDatasetBase) -> dict[str, Any]:
    """生成数据集概况（无状态服务函数）。

    Args:
        dataset: WpDepthIndexedDatasetBase实例

    Returns:
        dict: 数据集概况数据
            - basic_info: 基本信息
            - metadata_info: 元数据信息
            - dataframe_info: DataFrame信息
            - well_statistics: 按井统计
            - curve_statistics: 曲线统计

    Examples:
        >>> summary = generate_dataset_summary(dataset)
        >>> print(f"行数: {summary['basic_info']['total_rows']}")
        >>> print(f"曲线数: {summary['basic_info']['curve_count']}")
    """
    logger.debug(LogMessages.MSG_DATASET_SUMMARY_START,
                dataset_name=str(dataset.name),
                dataset_type=dataset.dataset_type.value)

    try:
        summary = {
            SummaryKeys.BASIC_INFO: _get_basic_info(dataset),
            SummaryKeys.METADATA_INFO: _get_metadata_info(dataset),
            SummaryKeys.DATAFRAME_INFO: _get_dataframe_info(dataset),
            SummaryKeys.WELL_STATISTICS: _get_well_statistics(dataset),
            SummaryKeys.CURVE_STATISTICS: _get_curve_statistics(dataset)
        }

        logger.debug(LogMessages.MSG_DATASET_SUMMARY_COMPLETE,
                    dataset_name=str(dataset.name),
                    total_rows=summary[SummaryKeys.BASIC_INFO][SummaryKeys.TOTAL_ROWS])

        return summary

    except Exception as e:
        logger.error(LogMessages.MSG_DATASET_SUMMARY_FAILED,
                    dataset_name=str(dataset.name),
                    error=str(e))
        return {SummaryKeys.ERROR: f"{LogMessages.ERROR_SUMMARY_FAILED}: {str(e)}"}


def _get_basic_info(dataset: WpDepthIndexedDatasetBase) -> dict[str, Any]:
    """获取数据集基本信息。

    Args:
        dataset: 数据集实例

    Returns:
        dict: 基本信息
    """
    return {
        SummaryKeys.NAME: str(dataset.name),
        SummaryKeys.TYPE: dataset.dataset_type.value,
        SummaryKeys.SAMPLING_INTERVAL: dataset.depth_sampling_rate,
        SummaryKeys.TOTAL_ROWS: len(dataset.df),
        SummaryKeys.TOTAL_COLUMNS: len(dataset.df.columns),
        SummaryKeys.CURVE_COUNT: len(dataset.curve_metadata),
        SummaryKeys.CREATED_AT: dataset.created_at.strftime("%Y-%m-%d %H:%M:%S"),
        SummaryKeys.MODIFIED_AT: dataset.modified_at.strftime("%Y-%m-%d %H:%M:%S")
    }


def _get_metadata_info(dataset: WpDepthIndexedDatasetBase) -> dict[str, Any]:
    """获取曲线元数据信息。

    Args:
        dataset: 数据集实例

    Returns:
        dict: 元数据信息
    """
    return dataset.curve_metadata.generate_summary()


def _get_dataframe_info(dataset: WpDepthIndexedDatasetBase) -> dict[str, Any]:
    """获取DataFrame结构信息。

    Args:
        dataset: 数据集实例

    Returns:
        dict: DataFrame信息
    """
    df = dataset.df

    # 获取列信息
    column_info = []
    for col in df.columns:
        dtype_str = str(df[col].dtype)
        non_null_count = df[col].count()
        null_count = df[col].isnull().sum()

        column_info.append({
            SummaryKeys.NAME: col,
            SummaryKeys.DTYPE: dtype_str,
            SummaryKeys.NON_NULL_COUNT: int(non_null_count),
            SummaryKeys.NULL_COUNT: int(null_count),
            SummaryKeys.NULL_PERCENTAGE: float(null_count / len(df) * 100) if len(df) > 0 else 0.0
        })

    return {
        SummaryKeys.COLUMNS: column_info,
        SummaryKeys.INDEX_TYPE: type(df.index).__name__,
        SummaryKeys.MEMORY_USAGE: df.memory_usage(deep=True).sum(),
        SummaryKeys.SHAPE: df.shape
    }


def _get_well_statistics(dataset: WpDepthIndexedDatasetBase) -> dict[str, Any]:
    """获取按井统计信息。

    对于单深度数据集：使用该深度曲线统计深度范围
    对于双深度数据集（区间）：DEPTH_MIN为顶深曲线最小值，DEPTH_MAX为底深曲线最大值

    Args:
        dataset: 数据集实例

    Returns:
        dict: 按井统计信息
    """
    try:
        # 查找井名列
        well_curve_names = dataset.curve_metadata.get_well_identifier_curves()
        if not well_curve_names:
            return {SummaryKeys.ERROR: LogMessages.MSG_NO_WELL_CURVES}

        # 获取第一个井名曲线的属性
        well_curve = dataset.curve_metadata.get_curve(well_curve_names[0])
        if well_curve is None:
            return {SummaryKeys.ERROR: f"{LogMessages.MSG_WELL_CURVE_NOT_FOUND}: {well_curve_names[0]}"}

        well_column = well_curve.dataframe_column_name
        if well_column not in dataset.df.columns:
            return {SummaryKeys.ERROR: f"{LogMessages.MSG_WELL_COLUMN_NOT_FOUND}: {well_column}"}

        # 根据深度参考曲线数量确定统计策略
        depth_reference_count = dataset.get_depth_reference_count()

        well_stats = {}

        # 按井分组统计
        for well_name, group in dataset.df.groupby(well_column):
            if len(group) > 0:
                well_stat = {
                    SummaryKeys.ROW_COUNT: len(group)
                }

                # 根据深度曲线数量采用不同的统计策略
                if depth_reference_count == 1:
                    # 单深度数据集：使用单一深度曲线统计
                    try:
                        depth_curve = dataset.get_single_depth_reference_curve()
                        depth_column = depth_curve.dataframe_column_name

                        if depth_column in dataset.df.columns:
                            depth_series = group[depth_column].dropna()
                            if len(depth_series) > 0:
                                well_stat.update({
                                    SummaryKeys.DEPTH_MIN: float(depth_series.min()),
                                    SummaryKeys.DEPTH_MAX: float(depth_series.max()),
                                    SummaryKeys.DEPTH_RANGE: float(depth_series.max() - depth_series.min()),
                                    SummaryKeys.DEPTH_POINTS: len(depth_series)
                                })
                            else:
                                well_stat.update({
                                    SummaryKeys.DEPTH_MIN: None,
                                    SummaryKeys.DEPTH_MAX: None,
                                    SummaryKeys.DEPTH_RANGE: None,
                                    SummaryKeys.DEPTH_POINTS: 0
                                })
                    except Exception as e:
                        logger.warning(f"单深度统计失败: {e}")
                        well_stat["depth_statistics"] = f"统计失败: {str(e)}"
                        # 确保设置默认的深度字段
                        well_stat.update({
                            SummaryKeys.DEPTH_MIN: None,
                            SummaryKeys.DEPTH_MAX: None,
                            SummaryKeys.DEPTH_RANGE: None,
                            SummaryKeys.DEPTH_POINTS: 0
                        })

                elif depth_reference_count == 2:
                    # 双深度数据集（区间）：分别使用顶深和底深曲线
                    try:
                        # 获取顶深和底深曲线
                        from logwp.models.constants import WpDepthRole

                        top_curves = dataset.curve_metadata.get_curves_by_depth_role(WpDepthRole.TOP)
                        bottom_curves = dataset.curve_metadata.get_curves_by_depth_role(WpDepthRole.BOTTOM)

                        if top_curves and bottom_curves:
                            top_curve = dataset.curve_metadata.get_curve(top_curves[0])
                            bottom_curve = dataset.curve_metadata.get_curve(bottom_curves[0])

                            if (top_curve and bottom_curve and
                                top_curve.dataframe_column_name in dataset.df.columns and
                                bottom_curve.dataframe_column_name in dataset.df.columns):

                                top_series = group[top_curve.dataframe_column_name].dropna()
                                bottom_series = group[bottom_curve.dataframe_column_name].dropna()

                                if len(top_series) > 0 and len(bottom_series) > 0:
                                    depth_min = float(top_series.min())
                                    depth_max = float(bottom_series.max())
                                    well_stat.update({
                                        SummaryKeys.DEPTH_MIN: depth_min,
                                        SummaryKeys.DEPTH_MAX: depth_max,
                                        SummaryKeys.DEPTH_RANGE: depth_max - depth_min,
                                        SummaryKeys.DEPTH_POINTS: len(group)  # 区间数据集的总行数
                                    })
                                else:
                                    well_stat.update({
                                        SummaryKeys.DEPTH_MIN: None,
                                        SummaryKeys.DEPTH_MAX: None,
                                        SummaryKeys.DEPTH_RANGE: None,
                                        SummaryKeys.DEPTH_POINTS: 0
                                    })
                        else:
                            well_stat["depth_statistics"] = "区间数据集缺少顶深或底深曲线"
                            # 确保设置默认的深度字段
                            well_stat.update({
                                SummaryKeys.DEPTH_MIN: None,
                                SummaryKeys.DEPTH_MAX: None,
                                SummaryKeys.DEPTH_RANGE: None,
                                SummaryKeys.DEPTH_POINTS: 0
                            })
                    except Exception as e:
                        logger.warning(f"区间深度统计失败: {e}")
                        well_stat["depth_statistics"] = f"区间统计失败: {str(e)}"
                        # 确保设置默认的深度字段
                        well_stat.update({
                            SummaryKeys.DEPTH_MIN: None,
                            SummaryKeys.DEPTH_MAX: None,
                            SummaryKeys.DEPTH_RANGE: None,
                            SummaryKeys.DEPTH_POINTS: 0
                        })

                else:
                    # 异常情况：深度曲线数量不符合预期
                    well_stat["depth_statistics"] = f"不支持的深度曲线数量: {depth_reference_count}"
                    # 确保设置默认的深度字段
                    well_stat.update({
                        SummaryKeys.DEPTH_MIN: None,
                        SummaryKeys.DEPTH_MAX: None,
                        SummaryKeys.DEPTH_RANGE: None,
                        SummaryKeys.DEPTH_POINTS: 0
                    })

                well_stats[str(well_name)] = well_stat

        return well_stats

    except Exception as e:
        logger.warning(LogMessages.MSG_WELL_STATS_FAILED, error=str(e))
        return {SummaryKeys.ERROR: f"{LogMessages.MSG_WELL_STATS_FAILED}: {str(e)}"}


def _get_curve_statistics(dataset: WpDepthIndexedDatasetBase) -> dict[str, Any]:
    """获取曲线统计信息。

    根据井数量采用不同的统计策略：
    - 单井：直接整体统计
    - 多井：先按井统计，再整体统计

    Args:
        dataset: 数据集实例

    Returns:
        dict: 曲线统计信息
            - 单井时：包含整体统计
            - 多井时：包含按井统计和整体统计
    """
    try:
        from .curve_statistics import generate_curve_statistics, generate_multi_well_curve_statistics

        # 获取井名列表
        wells = dataset.get_wells()
        well_count = len(wells)

        logger.debug("开始曲线统计分析",
                    well_count=well_count,
                    wells=wells[:5] if len(wells) > 5 else wells)  # 只记录前5个井名

        if well_count <= 1:
            # 单井或无井：直接整体统计
            logger.debug("执行单井整体统计")
            return generate_curve_statistics(dataset.df, dataset.curve_metadata)
        else:
            # 多井：先按井统计，再整体统计
            logger.debug("执行多井分层统计")
            return generate_multi_well_curve_statistics(dataset.df, dataset.curve_metadata, wells)

    except Exception as e:
        logger.warning(LogMessages.MSG_CURVE_STATS_FAILED, error=str(e))
        return {SummaryKeys.ERROR: f"{LogMessages.MSG_CURVE_STATS_FAILED}: {str(e)}"}



