"""
提供偏依赖图 (Partial Dependence Plot, PDP) 功能。
"""
from __future__ import annotations

from typing import Any, List, Union, TYPE_CHECKING
import pandas as pd

try:
    from sklearn.inspection import PartialDependenceDisplay
    import matplotlib.pyplot as plt
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

if TYPE_CHECKING:
    from matplotlib.axes import Axes


def plot_pdp(
    model: Any,
    X: pd.DataFrame,
    features: Union[str, List[str]],
    *,
    ax: "Axes | None" = None,
    **kwargs: Any
) -> "Axes":
    """
    绘制一个或两个特征的偏依赖图 (Partial Dependence Plot, PDP)。

    PDP显示了一个或两个特征对模型预测结果的边际效应。它通过在所有其他特征
    的边际分布上平均模型预测来工作，可以帮助我们理解特征与预测目标之间的关系
    是线性的、单调的还是更复杂的。

    此函数是 scikit-learn 的 `PartialDependenceDisplay.from_estimator` 的一个封装器。

    Args:
        model: 一个已训练的、scikit-learn兼容的模型对象。
        X (pd.DataFrame): 用于计算偏依赖的特征矩阵。
        features (Union[str, List[str]]): 要绘制PDP的特征名称或特征名称列表。
                                          如果是一个字符串，则绘制一维PDP。
                                          如果是两个字符串的列表，则绘制二维PDP。
        ax (matplotlib.axes.Axes | None, optional): 要在其上绘图的现有matplotlib轴对象。
                                                    如果为None，将创建一个新的图和轴。
                                                    默认为 None。
        **kwargs: 其他传递给 `PartialDependenceDisplay.from_estimator` 的关键字参数。

    Returns:
        matplotlib.axes.Axes: 包含了PDP图的matplotlib轴对象。
    """
    if not SKLEARN_AVAILABLE:
        raise ImportError(
            "此功能需要 'scikit-learn' 和 'matplotlib' 库。请运行 'pip install scikit-learn matplotlib' 进行安装。"
        )

    if not isinstance(X, pd.DataFrame):
        raise TypeError("输入数据 X 必须是 pandas DataFrame。")

    if isinstance(features, str):
        features_to_plot = [features]
    elif isinstance(features, list) and 1 <= len(features) <= 2:
        features_to_plot = features
    else:
        raise ValueError("`features` 参数必须是一个字符串或包含一到两个特征名称的列表。")

    if ax is None:
        fig, ax = plt.subplots(figsize=kwargs.pop('figsize', (8, 6)))
    else:
        fig = ax.get_figure()

    PartialDependenceDisplay.from_estimator(
        estimator=model,
        X=X,
        features=features_to_plot,
        ax=ax,
        **kwargs
    )

    ax.set_title(f"Partial Dependence Plot for '{', '.join(features_to_plot)}'")
    fig.tight_layout()

    return ax
